# Mosaic: Data-Free Knowledge Distillation via Mixture-of-Experts for Heterogeneous Distributed Environments

Junming Liu $^{1,2}$ , Yanting Gao $^{1}$ , <PERSON><PERSON>g $^{2}$ , <PERSON><PERSON><PERSON> Sun $^{2}$ , <PERSON><PERSON><PERSON> $^{1}$ , <PERSON><PERSON><PERSON> $^{1}$ , <PERSON><PERSON> $^{2}$ , <PERSON><PERSON> $^{2,*}$ , <PERSON><PERSON>g $^{1,*}$ $^{1}$ School of Computer Science and Technology, Tongji University, Shanghai, China   $^{2}$ Shanghai Artificial Intelligence Laboratory, Shanghai, China  <EMAIL>, <EMAIL>, <EMAIL>

# Abstract

Federated Learning (FL) is a decentralized machine learning paradigm that enables clients to collaboratively train models while preserving data privacy. However, the coexistence of model and data heterogeneity gives rise to inconsistent representations and divergent optimization dynamics across clients, ultimately hindering robust global performance. To transcend these challenges, we propose Mosaic, a novel data- free knowledge distillation framework tailored for heterogeneous distributed environments. Mosaic first trains local generative models to approximate each client's personalized distribution, enabling synthetic data generation that safeguards privacy through strict separation from real data. Subsequently, Mosaic forms a Mixture- of- Experts (MoE) from client models based on their specialized knowledge, and distills it into a global model using the generated data. To further enhance the MoE architecture, Mosaic integrates expert predictions via a lightweight meta model trained on a few representative prototypes. Extensive experiments on standard image classification benchmarks demonstrate that Mosaic consistently outperforms state- of- the- art approaches under both model and data heterogeneity. The source code has been published at Github.

# 1 Introduction

Amid the surge of data, deep learning has made remarkable strides in both established and emerging fields [49, 25, 62, 82, 9, 79]. However, in real- world scenarios, data is inherently distributed across clients, as seen in domains like autonomous driving [8], IoT [92], fintech [13], and healthcare [74]. Due to the high cost of data acquisition and increasingly stringent privacy regulations, aggregating data for centralized training is often impractical [50, 119]. In light of these limitations, Federated Learning (FL) has emerged as a compelling paradigm [57, 127, 43], enabling decentralized clients to collaboratively train a shared global model while retaining their private data locally, thereby addressing critical concerns surrounding data privacy and data sovereignty.

However, the promise of FL comes with significant challenges, chief among them being the pervasive heterogeneity across real- world clients [56, 122]. Specifically, the fundamental assumption of IID (Independently and Identically Distributed) data across clients often breaks down in practice, creating substantial data heterogeneity [70, 100, 23, 78, 12, 104]. This statistical divergence across clients gives rise to client drift [44] in standard FL algorithms like FedAvg [69], where local models inherently reflect the disparities of private data rather than aligning toward a unified global objective, ultimately degrading generalization and performance consistency. While numerous methods have

been proposed to mitigate data heterogeneity [55, 88, 129, 106, 61], they often operate under the premise of model homogeneity, where all clients are required to use the same architecture as the global model. Yet in real- world deployments, clients often exhibit significant variation in hardware and resource availability [109, 38], precluding the viability of training architecturally identical models. This leads to model heterogeneity [15, 36, 99, 108, 130], rendering conventional aggregation methods infeasible and hindering effective knowledge exchange among clients.

Building upon the broader complexities posed by heterogeneity, a number of approaches have been explored to alleviate its impact. Some methods attempt to sidestep the issue by enforcing a unified, high- capacity model, but this comes at the cost of reducing the participation of resource- limited clients who fall behind due to incomplete training [75, 67, 21]. Others adopt partial training (PT) strategies [2, 135, 41], where each client trains only a subset of the global model, leading to fragmented updates and instability in global integration [64]. Among these, Knowledge Distillation (KD) [31, 26, 115] emerges as a particularly effective alternative, providing a flexible means of transferring knowledge across heterogeneous clients while accommodating disparities in data and model capacity. However, many KD- based methods rely on proxy datasets to facilitate knowledge transfer, either by aggregating small portions of client data which raises privacy concerns [53] or by employing external auxiliary datasets that closely resemble the original data distribution [87], limiting their applicability in real- world scenarios. This motivates the need for Data- Free Knowledge Distillation (DFKD), which eliminates the reliance on external data while retaining the core advantages of distillation.

To fully unlock the potential of DFKD, it is essential to achieve two pivotal objectives: constructing a synthetic dataset that enables effective knowledge transfer, and identifying a teacher model that encapsulates comprehensive knowledge. Most existing DFKD approaches concentrate heavily on synthesized pseudo data that either closely approximate the global data distribution to ensure reliability [139, 132, 63, 65], or generate hard samples near decision boundaries to increase diversity [24, 131, 133, 64, 137], thereby enhancing the robustness of the distilled model and addressing its knowledge deficiencies. Yet in practice, the widely adopted strategy in DFKD—uploading local generators to the server and naively blending their parameters—often suffers from substantial instability under Non- IID settings, where both distribution shifts and catastrophic forgetting accumulate over communication rounds, ultimately hindering consistent knowledge synthesis. While these concerns are significant, there looms a more fundamental challenge: how to construct a generalizable and powerful teacher model. Certain methods construct an ensemble of client models and weight them by the confidence each client exhibits in specific predictions to guide the distillation process [131, 133, 64, 63, 138, 97], but our theoretical analysis and empirical results reveal that such schemes can yield erratic teachers that even underperform vanilla global models under severe data heterogeneity. In summary, our goal is to obtain stable generators and a knowledgeable teacher model under heterogeneous conditions<sup>2</sup>.

To confront the mentioned challenges, we propose Mosaic, a novel DFKD framework utilizing Mixture- of- Experts with Advanced Integrated Collaboration for Heterogeneous Distributed Environments. Mosaic begins by training a lightweight, label- agnostic generator on each client. Compared to conditional generators, this design notably reduces model complexity, training overhead, and communication cost, making it well- suited for distributed deployment. To enhance the transferability of synthetic samples, we adopt an adversarial training scheme [25, 1] where the local model acts as both a discriminator to ensure fidelity and a classifier to promote diversity. To mitigate training difficulties on clients with limited data, we further regularize the generation process using the mean and variance of hidden representations from the initialized local model, thereby aligning the synthetic distribution with the global one. Rather than aggregating generators uploaded by clients, which can lead to instability, Mosaic strategically ensembles them to collaboratively synthesize a diverse batch by composing unique fragments from each. In the next stage, client models are organized into per- class experts based on label availability, thereby constituting a Mixture- of- Experts (MoE) architecture [40]. A gating network governs the allocation of inputs to the appropriate experts, while a meta model [113] aggregates their predictions, constructing a knowledgeable teacher. A minimal set of prototype features is extracted from each client to further refine and enhance this ensemble. Finally, this composite teacher is distilled into a global student model using the generator ensemble.

The main contributions of this work are summarized as follows: First, we introduce a lightweight generator tailored for deployment in distributed environments, enhancing the fidelity and diversity of synthetic samples and improving the generation capabilities of clients with limited data by

utilizing only local models. Second, by leveraging the MoE architecture, we effectively address the robustness issues in teacher models from previous methods, with our MoE- based teacher significantly outperforming prior models. Third, extensive experiments on seven real- world image classification datasets demonstrate the superiority of Mosaic, showing not only improved accuracy for the global model but also significant performance gains for local models on classification tasks.

# 2 Preliminaries

We consider a centralized FL scenario involving a central server and  $N$  clients, each possessing a private labeled dataset  $\{(X_i,Y_i)\}_{i = 1}^N$  where  $X_{i} = \{x_{b}^{i}\}_{b = 1}^{n_{i}}$  follows a local data distribution  $\mathcal{D}_i$  over feature space  $\mathcal{X}_i$  , i.e.,  $x_{i}^{b}\sim \mathcal{D}_{i}$  , and  $Y_{i} = \{y_{i}\}_{b = 1}^{n_{i}}\subseteq [O]_{i} = \{1,\ldots ,O\}$  denotes the corresponding ground- truth labels. Here,  $C$  is the total number of classes. The FL setting involves both data and model heterogeneity. For data heterogeneity, we assume that all clients share the same feature space, but their data distributions may differ typically due to label distribution skew, i.e.,  $\mathcal{X}_i = \mathcal{X}_j$  and  $\mathcal{D}_i\neq \mathcal{D}_j$ $y_{i}\neq j$ $i,j\in [N]$  . For model heterogeneity, each client  $i$  maintains a local model  $f_{i}$  with parameters  $\theta_{i}$  , and the model capacity may vary across clients, i.e.,  $|\theta_{i}|\neq |\theta_{j}|$ $\exists i\neq j$ $i,j\in [N]$

In PT- based methods, the capacity of client  $i$  is determined by a width ratio  $R_{i}\in (0,1]$  , indicating the proportion of neurons selected from each layer of a global model  $f$  with parameters  $\theta$  . During training, each client receives a sub- model with a reduced width and performs local updates. The server then aggregates updated parameters shared across clients to refine the global model. Specifically, for each parameter  $\theta_{[l,k]}^t$  at round  $t$  the aggregation follows [7, 33, 15, 2, 64]:

$$
\theta_{[l,k]}^t = \frac{1}{\sum_{j\in\mathcal{S}_t}p_j}\sum_{i\in \mathcal{S}_t}p_i\theta_{i,[l,k]}^t, \tag{1}
$$

where  $\mathcal{S}_t\subseteq [N]$  denotes the participating clients, and  $p_i$  is a weight typically proportional to the data size of client  $i$ $\theta_{[l,k]}^t$  denotes the  $k$  th parameter of layer  $l$  of the global model, and  $\theta_{i,[l,k]}^t$  denotes the parameter  $\theta_{[l,k]}^t$  updated by client  $i$  . Parameters not updated by any client remain unchanged. However, PT assumes a common backbone and is thus inapplicable when clients adopt fundamentally different architectures (e.g., ResNet [28] versus VGG [94]). In such cases, only clients with compatible structures are aggregated, while clients with differing architectures are grouped separately for training. This represents the most basic approach in the absence of KD. The aggregation can be expressed as:

$$
\Theta_{[l,k]}^t = \bigcup_{m\in \mathcal{M}}\frac{1}{\sum_{j\in\mathcal{S}_t^n}p_j}\sum_{i\in \mathcal{S}_t^n}p_i\theta_{i,[l,k]}^t, \tag{2}
$$

where  $\mathcal{M}$  denotes the set of all model architectures,  $\mathcal{S}_t^m$  is the set of clients with architecture  $m$  , and  $\Theta_{[l,k]}^t$  represents the set of global model parameters  $\theta_{[l,k]}^t$  aggregated across different architectures.

# 3 Proposed Method

In this section, we detail the proposed method Mosaic. Mosaic is essentially a fine- tuning approach built upon PT- based methods, designed to address both model and data heterogeneity in FL environments. It requires a preliminarily converged global model  $f$  obtained via a standard baseline method [69, 2], which is then distributed to initialize local models. As illustrated in Figure 1, the overall workflow comprises four stages: local model update, generator optimization, model aggregation and knowledge distillation. Notably, the process of local model update is consistent with that in PT- based methods and is hence not elaborated here. The pseudocode for Mosaic is provided in Appendix B.

# 3.1 Generator Optimization

A craftsman must first sharpen their tools before they can perfect their work. In this stage, our goal is to train well- behaved generators that enhance the transferability of synthetic data, thereby improving the efficacy of KD. Many DFKD methods adopt a conditional generator that participates in multiple rounds of communication [116, 95, 29, 35, 66, 96]. While effective, this paradigm imposes significant burdens on clients in terms of storage, computation, and communication. Specifically, each client must continuously train and maintain a relatively large conditional generator, and transmit both the

![](images/75b99f225f9803fc77e07550f398eaab533625fb1234a94fc41a909f79793946.jpg)  
Figure 1: The full workflow for Mosaic combined with a PT-based method. Mosaic consists of four stages: local model update, generator optimization, model aggregation, and knowledge distillation. Notably, during generator optimization, the local model is updated and produces  $\mathcal{L}_{\mathrm{adv}}$ , while other losses  $\mathcal{L}_{\mathrm{entropy}}$ ,  $\mathcal{L}_{\mathrm{diversity}}$ , and  $\mathcal{L}_{\mathrm{inversion}}$  are computed using a frozen local model fixed at initialization.

local model and the generator during every round. Moreover, under severe data heterogeneity, we observe that the aggregated generator on the server tends to deviate from the global distribution and suffer from catastrophic forgetting. Meanwhile, the generator deployed to clients, especially in Generative Adversarial Network (GAN)- based settings, often collapses due to mismatches with local data and discriminator, leading to training degradation [25, 71, 136].

To mitigate these issues, Mosaic employs a lightweight unconditional generator design operating under a one- shot communication protocol [27, 29], where the generator is uploaded only once, eliminating the need for iterative synchronization. As a result, Mosaic significantly reduces overall costs, with quantitative comparisons provided in Appendix C. This raises an interesting question: Why aggregate generators when they are uploaded only once? Aggregating the generators can introduce instability, as shown by the empirical observations in Appendix D. Additionally, the diversity of a single global generator is also limited. Motivated by these findings, we preserve all client generators as an ensemble without aggregation. In the subsequent KD stage, this ensemble collectively synthesizes data batches, thereby avoiding distributional shifts and catastrophic forgetting induced by parameter fusion while substantially enhancing sample diversity through parallel generation.

In the following, we detail the training of the generators. We adopt adversarial training under the GAN framework, as it offers significantly lower computational cost than Diffusion models [32] and generates higher- quality, more diverse samples than Variational Autoencoders (VAEs) [47], as reported in prior studies [46, 61]. Additionally, we leverage the local model  $f_{i}$  as the discriminator on each client, avoiding the extra overhead of training a dedicated one. To ensure fidelity, we utilize the basic adversarial loss  $\mathcal{L}_{\mathrm{adv}}$  to guide the generator  $G_{i}$  in producing samples that align with the local data distribution  $\mathcal{D}_{i}$ . The generator's adversarial loss is formulated as:

![](images/56bb599005c1150a64a7c63d0964fd83bdad5988a29620e2fe1b24d69b59acda.jpg)  
Figure 2: Visualization of synthetic data and decision boundaries of the global model  $d_{s}$  and teacher ensemble  $d_{t}$ . Left panel: red circles indicate data synthesized by the aggregated global generator, while gray circles reflect areas the generator fails to cover due to instability. Middle panel: synthetic data produced by the generator ensemble, with most samples successfully synthesized. Right panel: dashed line denotes the original teacher ensemble, while the solid line represents the MoE-based teacher ensemble with broader decision boundaries.

$$
\mathcal{L}_{\mathrm{adv,real}} = \mathbb{E}_{x\sim \mathcal{D}_i}[\log f_i(x)],\mathcal{L}_{\mathrm{adv,fake}} = \mathbb{E}_{z\sim \mathcal{Z}}[\log (1 - f_i(G_i(z)))], \tag{3}
$$

where  $x\sim D_i$  represents a sample drawn from  $D_{i}$ $z\sim z$  is a latent vector sampled from a standard Gaussian distribution. Inspired by DeGAN [1], we use  $f_{i}$  as an additional classifier to enhance sample confidence. By incorporating an entropy loss  $\mathcal{L}_{\mathrm{entropy}}$  , we guide  $G_{i}$  to produce high- confidence samples belonging to the correct class. This entropy loss is formulated as:

$$
\mathcal{L}_{\mathrm{entropy}} = \mathbb{E}_{z\sim \mathcal{Z}}\left[-\sum_{k = 0}^{C}y_{k}\log (y_{k})\right], \tag{4}
$$

where  $y = f_{i}(G_{i}(z))$  is the classifier output for a generated sample, and  $y_{k}$  denotes the predicted probability for class  $k$  , ensuring that each generated sample is confidently assigned to a class.

To further enhance the diversity of the generated samples, we incorporate a diversity loss  $\mathcal{L}_{\mathrm{diversity}}$  following the same formulation as in DeGAN, which encourages  $G_{i}$  to produce a more varied set of samples. This diversity loss is formulated as:

$$
\mathcal{L}_{\mathrm{diversity}} = -\sum_{k = 0}^{C}w_{k}\log (w_{k}), \tag{5}
$$

where  $w_{k} = \mathbb{E}_{z\sim \mathcal{Z}}[y_{k}]$  denotes the expected probability for class  $k$  by the classifier  $f_{i}$  over a batch of generated samples, ensuring that the overall class distribution is as uniform as possible.

However, we observe that the generators trained on clients with limited data perform poorly due to overfitting of the discriminator and mode collapse, as demonstrated in Appendix E. This is primarily because the small dataset causes an imbalance in the adversarial training, leading the discriminator to prematurely converge and lose its guiding capability, which forces the generator to produce only a limited range of samples to deceive the discriminator [45, 42]. To address this issue, we introduce the inversion loss:

$$
\mathcal{L}_{\mathrm{inversion}} = \sum_{l = 1}^{L}\left\| \mu_{l}(G_{i}(z)) - \hat{\mu}_{l}\right\|_{2} + \sum_{l = 1}^{L}\left\| \sigma_{l}^{2}(G_{i}(z)) - \hat{\sigma}_{l}^{2}\right\|_{2}, \tag{6}
$$

where  $\mu_{l}(G_{i}(z))$  and  $\sigma_l^2 (G_i(z))$  denote the batch- wise mean and variance of the feature maps at the  $l$  - th layer of the global model  $f$  given synthetic data generated by  $G_{i}$  , and  $\hat{\mu}_{l},\hat{\sigma}_{l}^{2}$  are the running mean and variance stored in the BatchNorm layers [39, 123] of  $f$  , which approximate the global feature statistics accumulated during global training.

To further refine the application of the inversion loss, we introduce an empirical threshold  $\tau$  , which defines the minimum sample count below which generators trained on clients with limited data are eligible to incorporate the inversion loss. While this might cause  $G_{i}$  to deviate from its own data distribution, the overall impact is minimal due to the limited sample size. By applying the inversion loss, we reduce the risk of overfitting and mode collapse, thereby enhancing both the quality and diversity of the generated samples. The total generator loss  $\mathcal{L}_G$  is now expressed as:

$$
\mathcal{L}_G = \mathcal{L}_{\mathrm{adv}} + \lambda_e\mathcal{L}_{\mathrm{entropy}} - \lambda_d\mathcal{L}_{\mathrm{diversity}} + \lambda_i\mathcal{L}_{\mathrm{inversion}}, \tag{7}
$$

where  $\lambda_{e},\lambda_{d},$  and  $\lambda_{i}$  are hyperparameters controlling the relative importance of the entropy, diversity, and inversion losses, respectively. By jointly optimizing these objectives, the generator ensemble effectively reduces the instability introduced by aggregation, which manifests as deviations from the global distribution and the catastrophic forgetting of certain hard samples. This leads to improved generation quality and significantly enhanced diversity, as quantitatively verified through various evaluation metrics [83, 85, 134] and visual comparisons presented in Appendix F. Notably, the synthetic data from well- trained generators should differ visually from real data for privacy, while still capturing common knowledge from local models to align with the real data distribution for utility [64]. We discuss privacy protection in detail in Appendix G, where we elaborate on how our method addresses privacy preservation and the potential limitations that may arise.

# 3.2 Model Aggregation

3.2 Model AggregationWe now describe how to construct a MoE architecture. Our objective is to ensure that each expert specializes in a distinct subset of knowledge, with minimal redundancy or overlap across experts. Such orthogonality not only enhances task coverage but also reduces the server- side overhead of

maintaining overlapping models. To this end, we aggregate client models in a class- specific manner. Specifically, for each class  $c\in [C]$  , we construct a corresponding expert  $f_{c}$  by selectively combining the local models  $\{f_i\}_{i = 1}^N$  according to the relative availability of class-  $\cdot c$  data:

$$
\theta_{c}^{(t + 1)} = \sum_{i = 1}^{N}\frac{|D_{i,c}|}{\sum_{j = 1}^{N}|D_{j,c}|}\theta_{i}^{(t)}, \tag{8}
$$

where  $\theta_{i}^{(t)}$  denotes the parameters of local model  $f_{i}$  at round  $t$  , and  $D_{i,c} = \{(x_i^b,y_i^b)\in (X_i,Y_i)\mid$ $y_{i}^{b} = c\}$  is the subset3 of client  $i$  's data labeled as class  $c$  . This aggregation ensures that each expert  $f_{c}$  is dominated by contributions from clients who are most knowledgeable about class  $c$  , thereby promoting specialization while avoiding errors introduced by uninformed contributors. By repeating this process for all classes  $c\in [C]$  , the server obtains a set of class- specific experts  $\mathcal{F} = \{f_c\}_{c = 1}^C$  which together form the expert ensemble for downstream distillation or inference.

However, this design introduces a potential scalability issue when the number of classes  $C$  becomes very large (e.g., in datasets like ImageNet [14]). Maintaining a full set of class- specific experts incur prohibitive storage and computational costs on the server, even with a gating network [40] that selectively activates relevant experts during inference. Fortunately, we observe that in the large-  $C$  regimes, the expert ensemble can be substituted without significant degradation in performance. When  $C > > N$  and  $N$  is bounded, extreme data heterogeneity implies that each client  $i$  's label set  $Y_{i}\subseteq [C]$  is nearly disjoint, i.e.,  $|(Y_{i}\setminus Y_{j})\cup (Y_{j}\setminus Y_{i})|\gg 0,\forall i\neq j\in [N]$  . This suggests that certain classes are exclusive to individual clients, and the corresponding experts  $f_{c}$  effectively approximate the local model  $f_{i}$  . This motivates the consideration of directly ensembling local models, similar to the DENSE [131] approach. In contrast, when  $N\gg C$  and  $N$  is bounded, class- wise aggregation remains more effective due to the increased overlap in clients' label distributions. We empirically demonstrate this phenomenon in Appendix H.

The gating network we used is a replica of the global model  $f$  , which dictates the selection of experts within the ensemble. Specifically, for any synthetic sample  $\hat{x}$  , we identify the top  $k$  classes with the highest predicted scores from  $f$  , denoted as  $\mathrm{Top}_k(f(\hat{x}))$  . The activated experts for  $\hat{x}$  are then:

$$
\mathcal{F}(\hat{x}) = \{f_c\in \mathcal{F}\mid c\in \mathrm{Top}_k(f(\hat{x}))\} . \tag{9}
$$

To enhance the robustness of the ensemble, we employ a simple Multi- Layer Perceptron (MLP) [84] as the meta model  $M$  to weight the outputs of  $\mathcal{F}(\hat{x})$  . Exactly,  $M$  assigns a weight  $\alpha_{c,k}$  to each expert's output at position  $k$  , and aggregates the scaled logits as:

$$
f_{\mathrm{meta},k}(\hat{x}) = \sum_{f_c\in \mathcal{F}(\hat{x})}\alpha_{c,k}\cdot f_c(\hat{x})_k. \tag{10}
$$

The final prediction is then computed as:

$$
f_{\mathrm{meta}}(\hat{x}) = M(f_c(\hat{x}))_{f_c\in \mathcal{F}(\hat{x})}), \tag{11}
$$

where  $[f_c(\hat{x})]_{f_c\in \mathcal{F}(\hat{x})}$  denotes the concatenation of the logits from  $f_{c}$  in  $\mathcal{F}(\hat{x})$

Following prior work [99, 37], we extract a minimal set of prototypes from the clients to train  $M$  For client  $i$  , the prototype for class  $c$  is computed as the mean of the feature vectors of all samples from class  $c$  in  $D_{i}$  , given by:

$$
p_c^{(i)} = \frac{1}{|D_{i,c}|}\sum_{x_{i,c}\in D_{i,c}}f_i(x_{i,c}), \tag{12}
$$

where  $f_{i}(x)$  denotes the feature representation extracted by  $f_{i}$  , and  $D_{i,c}$  denotes the subset of  $D_{i}$  containing samples from class  $c$  . These prototypes are based on feature averages, providing privacy protection by reducing exposure of raw data. Furthermore, since we only extract prototypes from a few classes, the communication cost remains minimal.

Once the prototypes are collected, they are passed through  $f$  and  $\mathcal{F}(\hat{x})$  , where all model parameters are kept fixed. We then train  $M$  using only a standard Cross- Entropy (CE) loss:

$$
\mathcal{L}_{\mathrm{meta}} = \mathrm{CE}(y_{\mathrm{meta}},y), \tag{13}
$$

where  $y$  is the ground- truth label associated with the prototype. Following these steps across several modules, we construct a teacher model that, while slightly more complex, proves to be highly robust. We illustrate the effectiveness of this MoE structure over a basic local model ensemble in Appendix I, and provide tricks to prevent overfitting during the training of this simple meta model in Appendix J.

# 3.3 Knowledge Distillation

Based on the previous sections, we deploy a generator ensemble  $\{G_i\}_{i = 1}^N$  and a MoE model  $\mathcal{F}$  on the server side. The synthetic data produced by  $\{G_i\}_{i = 1}^N$  are used to optimize the student  $f$ , guided by the teacher  $\mathcal{F}$ . The KD process is performed by combining soft Kullback- Leibler (KL) divergence with hard CE loss [31]. Therefore, the total distillation loss is formulated as:

$$
\mathcal{L}_{\mathrm{KD}} = \sum_{i = 1}^{N}\mathbb{E}_{z\sim \mathcal{Z}}\Big[\lambda_{\mathrm{soft}}\mathrm{KL}\left(f\big(G_i(z)\big),\mathcal{F}\big(G_i(z)\big)\right) + \lambda_{\mathrm{hard}}\mathrm{CE}\left(f\big(G_i(z)\big),Y_i\right)\Big], \tag{14}
$$

where  $Y_{i} = \arg \max_{c}\big[\mathcal{F}(G_{i}(z))\big]_{c}$  denotes the hard label predicted by  $\mathcal{F}$  for the synthetic sample  $G_{i}(z)$ , with  $z$  drawn from the latent space  $\mathcal{Z} = \mathcal{N}(0,1)$ .

# 4 Experiments

# 4.1 Experimental Settings

Datasets. We conduct experiments on seven image classification datasets to comprehensively evaluate the effectiveness of our method: MNIST [52], FMINIST [117], SVHN [73], CIFAR- 10, CIFAR- 100 [48], FOOD101 [4], and Tiny- ImageNet<sup>4</sup>. Detailed descriptions of these datasets are provided in Appendix K. To simulate data heterogeneity, following prior works [125, 107, 64], we partition the training data across clients using a Dirichlet distribution  $Dir(\omega)$ , where a smaller  $\omega$  indicates a higher degree of data heterogeneity. Specific partitioning examples are provided in Appendix I.

Baselines. We compare Mosaic against a comprehensive set of FL baselines, including FedAvg [69], FedRS [60], FedBN [59], FedProx [58], FedOpt [81], FedInit [98], FedAF [110], PA3Fed [34], pFedFDA [68], DENSE [131], FedFTG [133], DFRD [64] and FedKFD [63]. The first nine methods (FedAvg to pFedFDA) represent state- of- the- art (SOTA) solutions that address data heterogeneity in FL without leveraging DFKD. In contrast, the latter four methods (DENSE to FedKFD) are grounded in the DFKD paradigm and fully applicable in scenarios involving model heterogeneity. Notably, these DFKD methods are essentially fine- tuning methods designed to enhance the performance of a preliminary global model [64]. In our experiments, this preliminary model is obtained using FedAvg [69] in the case of homogeneous FL, and FedRox [2] in the case of heterogeneous FL where PT methods are required. Due to space limitations, we present only a subset of the baseline results in the main text, with the remaining results provided in Appendix M.

Configurations. Following the setup in DFRD, we perform experiments as follows. Unless otherwise specified, all experiments are conducted on a centralized network with  $N = 10$  active clients. The parameter  $\omega \in \{0.01,0.1,1.0\}$  is varied to simulate different levels of data heterogeneity. In model scenarios with heterogeneous models, we assign exponentially distributed budgets for each client, formulated as:  $R_{i} = \left[\frac{1}{2}\right]^{\min \{\sigma ,\{\rho \cdot \frac{i}{N}\} \}}$  ( $i\in [N]$ ), where both  $\sigma$  and  $\rho$  are positive integers. We fix  $\sigma = 4$  and consider the values  $\rho \in \{5,10,40\}$  for our experiments. For Mosaic, we set the loss weights as  $\lambda_{e} = 1$ ,  $\lambda_{d} = 5$ , and  $\lambda_{i} = 10$ , and fix  $\lambda_{\mathrm{soft}} = 0.8$  and  $\lambda_{\mathrm{hard}} = 0.2$  for soft and hard label guidance. All baseline methods are implemented strictly following their respective default configurations. Unless otherwise noted, all experiments use ResNet- 18 [28] as the backbone and are executed using PyTorch on a single NVIDIA A100 GPU. We further examine the effect of both intra- architecture scaling (ResNet- 8 to ResNet- 101) and cross- architecture heterogeneity (e.g., VGG vs. ResNet), with results reported in Appendix P.

Evaluation Metrics. We assess FL performance using both local and global test accuracy. Local accuracy (L.acc, in round brackets) is computed by evenly splitting the test set across clients and evaluating each local model individually. Global accuracy (G.acc) is obtained by testing the server- side global model on the full test set. All results are averaged over three random seeds for robustness.

Table 1: Top test accuracy  $(\%)$  of distinct methods across  $\omega \in \{0.01,0.1,1.0\}$  on different datasets.  

<table><tr><td rowspan="2">Alg.s</td><td colspan="3">FMNIST</td><td colspan="3">SVHN</td><td colspan="3">CIFAR-10</td><td colspan="3">CIFAR-10</td><td></td></tr><tr><td>ω = 1.0</td><td colspan="3">SVHN</td><td colspan="3">SVHN</td><td colspan="3">SVHN</td><td colspan="3">SVHN</td></tr><tr><td>FedAvg</td><td>89.89±0.22</td><td>82.00±0.18</td><td>57.74±0.09</td><td>89.01±0.17</td><td>75.37±0.15</td><td>37.77±0.01</td><td>78.34±1.0</td><td>56.32±0.1</td><td>36.64±0.01</td><td>65.55±0.5</td><td>59.30±0.1</td><td>48.72±0.92</td><td></td></tr><tr><td>FedRS</td><td>(82.80±1.0)</td><td>(55.62±1.66)</td><td>(35.01±1.44)</td><td>(79.73±1.87)</td><td>(36.70±1.41)</td><td>(14.88±1.62)</td><td>(61.23±1.78)</td><td>(26.51±1.96)</td><td>(17.17±1.92)</td><td>(58.72±0.98)</td><td>(41.62±0.87)</td><td>(19.07±0.64)</td><td></td></tr><tr><td>FedOpt</td><td>(85.87±0.1)</td><td>(57.21±0.99)</td><td>(38.63±2.09)</td><td>(53.84±0.69)</td><td>(77.39±1.33)</td><td>(38.59±1.99)</td><td>(78.58±1.98)</td><td>(57.48±2.09)</td><td>(41.75±2.07)</td><td>(63.32±0.62)</td><td>(58.29±0.66)</td><td>(50.16±1.00)</td><td></td></tr><tr><td>FedOpt</td><td>91.96±0.95</td><td>88.50±0.94</td><td>58.66±0.71</td><td>91.40±1.27</td><td>78.78±1.94</td><td>40.56±1.71</td><td>81.40±1.68</td><td>59.17±1.83</td><td>41.86±1.92</td><td>68.00±0.88</td><td>61.19±0.89</td><td>50.62±1.06</td><td></td></tr><tr><td>FedInit</td><td>(85.83±0.1)</td><td>(57.40±1.09)</td><td>(36.56±1.69)</td><td>(81.65±1.61)</td><td>(38.68±1.49)</td><td>(15.97±1.27)</td><td>(62.51±1.62)</td><td>(27.98±1.94)</td><td>(18.23±1.82)</td><td>(60.91±0.92)</td><td>(41.17±0.91)</td><td>(20.69±1.70)</td><td></td></tr><tr><td>FedInit</td><td>(81.75±0.9)</td><td>(63.85±1.43)</td><td>(60.86±1.70)</td><td>(91.55±1.72)</td><td>(79.80±1.67)</td><td>(43.67±1.69)</td><td>(81.18±1.72)</td><td>(28.78±1.88)</td><td>(42.99±1.92)</td><td>(69.44±0.88)</td><td>(61.53±0.53)</td><td>(51.27±1.04)</td><td></td></tr><tr><td rowspan="3">PA3Fed</td><td>(82.91±0.1)</td><td>(57.44±1.55)</td><td>(39.21±2.99)</td><td>(82.85±1.83)</td><td>(40.23±2.72)</td><td>(19.03±2.84)</td><td>(62.88±1.94)</td><td>(28.37±2.90)</td><td>(20.04±2.22)</td><td>(60.45±0.74)</td><td>(42.66±0.96)</td><td>(20.88±1.51)</td><td></td></tr><tr><td>(86.71±0.9)</td><td>(54.48±1.45)</td><td>(64.89±2.80)</td><td>(92.13±1.84)</td><td>(81.69±1.89)</td><td>(45.50±1.36)</td><td>(11.56±1.91)</td><td>(61.50±2.15)</td><td>(44.19±2.55)</td><td>(67.83±0.43)</td><td>(61.61±0.73)</td><td>(52.05±0.89)</td><td></td></tr><tr><td>(86.09±1.2)</td><td>(58.37±1.84)</td><td>(42.78±1.78)</td><td>(89.54±1.44)</td><td>(76.61±1.26)</td><td>(28.07±1.81)</td><td>(79.02±1.37)</td><td>(29.68±1.78)</td><td>(23.51±1.68)</td><td>(59.34±0.65)</td><td>(42.07±0.91)</td><td>(21.95±1.34)</td><td></td></tr><tr><td>DENSE</td><td>90.09±1.22</td><td>91.37±1.41</td><td>58.27±2.14</td><td>80.56±1.84</td><td>74.99±1.24</td><td>38.07±1.84</td><td>79.56±1.99</td><td>58.67±1.76</td><td>36.38±2.20</td><td>(66.52±0.66)</td><td>(60.39±0.81)</td><td>(48.79±1.77)</td><td></td></tr><tr><td rowspan="2">FedFTG</td><td>(83.92±1.2)</td><td>(83.24±1.46)</td><td>(58.20±1.96)</td><td>(80.55±1.64)</td><td>(79.65±2.09)</td><td>(14.24±1.47)</td><td>(63.01±1.47)</td><td>(26.80±1.79)</td><td>(17.81±1.41)</td><td>(58.97±0.94)</td><td>(41.58±1.47)</td><td>(18.95±1.70)</td><td></td></tr><tr><td>(80.36±0.4)</td><td>(83.63±1.46)</td><td>(58.77±1.95)</td><td>(89.66±1.45)</td><td>(76.64±1.38)</td><td>(38.95±1.06)</td><td>(79.01±1.26)</td><td>(58.24±1.66)</td><td>(36.05±2.40)</td><td>(65.71±0.63)</td><td>(59.91±0.75)</td><td>(49.37±0.99)</td><td></td></tr><tr><td>DFRD</td><td>(84.28±0.1)</td><td>(57.51±1.71)</td><td>(39.39±2.99)</td><td>(79.64±1.93)</td><td>(37.36±1.85)</td><td>(15.05±1.38)</td><td>(61.96±1.33)</td><td>(27.70±1.80)</td><td>(17.63±2.90)</td><td>(59.08±0.85)</td><td>(41.81±0.90)</td><td>(20.00±1.11)</td><td></td></tr><tr><td>FEDKF</td><td>(85.76±0.2)</td><td>(58.87±1.90)</td><td>(59.33±2.42)</td><td>(81.64±1.29)</td><td>(77.36±2.22)</td><td>(18.48±1.70)</td><td>(79.63±1.38)</td><td>(27.84±1.45)</td><td>(18.57±2.79)</td><td>(58.61±0.74)</td><td>(42.17±0.84)</td><td>(20.02±1.36)</td><td></td></tr><tr><td rowspan="2">Mosaic</td><td>90.20±0.00</td><td>90.20±0.00</td><td>90.20±0.00</td><td>90.20±0.00</td><td>90.20±0.00</td><td>90.20±0.00</td><td>90.20±0.00</td><td>90.20±0.00</td><td>90.20+0.00</td><td>90.20+0.00</td><td>90.20+0.00</td><td>90.20+0.00</td><td></td></tr><tr><td>(90.39±0.00)</td><td>(73.88±1.47)</td><td>(56.51±1.33)</td><td>(88.97±1.27)</td><td>(80.47±1.31)</td><td>(51.11±1.24)</td><td>(75.38±1.71)</td><td>(59.77±1.77)</td><td>(47.95±1.32)</td><td>(65.68±0.88)</td><td>(57.93±0.76)</td><td>(43.92±0.99)</td><td></td></tr></table>

Table 2: Top test accuracy  $(\%)$  of distinct methods across  $\rho \in \{5,10,40\}$  on different datasets.  

<table><tr><td rowspan="2">Alg.s</td><td colspan="3">SVHN</td><td colspan="3">CIFAR-10</td><td colspan="3">PyImageNet</td><td colspan="3">FOOD101</td></tr><tr><td>ρ = 5</td><td>p = 10</td><td>ρ = 40</td><td>ρ = 5</td><td>p = 10</td><td>ρ = 40</td><td>p = 5</td><td colspan="3">Py-ImageNet</td><td>p = 5</td><td>p = 10</td></tr><tr><td>FedRolex</td><td>34.71±1.68</td><td>23.48±13.09</td><td>22.39±15.28</td><td>21.11±1.76</td><td>16.57±1.40</td><td>14.37±1.29</td><td>9.29±0.32</td><td>5.55±0.40</td><td>2.50±0.33</td><td>10.27±1.33</td><td>5.14±3.44</td><td>3.22±0.52</td></tr><tr><td>+DENSE</td><td>(14.20±1.94)</td><td>(13.85±2.09)</td><td>(13.59±2.11)</td><td>(16.99±0.67)</td><td>(16.12±0.90)</td><td>(17.11±1.29)</td><td>(5.35±0.21)</td><td>(2.73±0.09)</td><td>(1.81±0.89)</td><td>(6.86±0.12)</td><td>(3.37±1.04)</td><td>(2.95±0.17)</td></tr><tr><td>+Dense</td><td>36.58±1.53</td><td>26.69±13.11</td><td>24.34±14.81</td><td>23.72±5.8</td><td>19.65±1.47</td><td>16.44±1.89</td><td>9.33±0.06</td><td>5.40±0.40</td><td>2.40±0.20</td><td>10.83±0.78</td><td>7.54±0.04</td><td>3.07±0.55</td></tr><tr><td>+FedFTG</td><td>(17.51±1.27)</td><td>(15.53±13.96)</td><td>(14.06±1.98)</td><td>(12.16±1.64)</td><td>(17.79±1.67)</td><td>(17.77±1.27)</td><td>(9.16±0.18)</td><td>5.68±0.04</td><td>2.42±0.32</td><td>10.66±0.79</td><td>8.13±0.04</td><td>3.09±0.11</td></tr><tr><td>+DFRD</td><td>38.07±1.52</td><td>25.49±13.84</td><td>24.04±14.96</td><td>22.60±0.20</td><td>15.81±0.62</td><td>14.26±1.14</td><td>5.36±0.23</td><td>(2.76±0.31)</td><td>(1.81±0.13)</td><td>(6.95±0.06)</td><td>(3.86±0.06)</td><td>(3.06±0.08)</td></tr><tr><td>+DFRD</td><td>(17.64±1.63)</td><td>(17.64±1.63)</td><td>(14.03±1.91)</td><td>(13.16±1.78)</td><td>(16.25±1.34)</td><td>(17.70±1.26)</td><td>(5.18±0.18)</td><td>(2.75±0.07)</td><td>(1.81±0.09)</td><td>(6.85±0.19)</td><td>(3.75±0.09)</td><td>(2.92±0.18)</td></tr><tr><td>+DFRD</td><td>46.30±1.42</td><td>34.78±19.19</td><td>32.86±15.54</td><td>26.68±1.21</td><td>25.57±1.37</td><td>19.86±1.76</td><td>10.93±0.05</td><td>6.80±0.11</td><td>2.68±0.19</td><td>12.70±0.79</td><td>10.58±0.09</td><td>3.59±0.05</td></tr><tr><td>+DFRD</td><td>(18.44±1.34)</td><td>(15.99±1.53)</td><td>(15.17±0.66)</td><td>(17.51±0.33)</td><td>(16.74±0.72)</td><td>(17.77±1.16)</td><td>(6.11±0.02)</td><td>(3.35±0.04)</td><td>(2.22±0.08)</td><td>(8.02±0.08)</td><td>(4.86±0.06)</td><td>(3.34±0.15)</td></tr><tr><td>+DFRD</td><td>36.06±1.41</td><td>23.71±1.22</td><td>23.61±1.44</td><td>23.72±1.20</td><td>14.75±1.34</td><td>18.51±1.24</td><td>9.18±0.00</td><td>5.61±0.00</td><td>5.53±0.00</td><td>12.57±0.09</td><td>6.57±0.00</td><td>3.25±0.53</td></tr><tr><td>+DMosaic</td><td>(17.34±1.97)</td><td>(14.99±2.63)</td><td>(14.73±1.31)</td><td>(11.16±1.68)</td><td>(17.28±1.89)</td><td>(15.27±1.30)</td><td>(5.33±0.28)</td><td>(2.76±0.07)</td><td>(15.16±0.82)</td><td>(7.97±0.04)</td><td>(4.96±0.05)</td><td>(2.98±0.18)</td></tr><tr><td>+DMosaic</td><td>44.74±1.93</td><td>40.61±2.62</td><td>37.09±2.71</td><td>31.60±2.46</td><td>28.74±3.91</td><td>30.14±2.37</td><td>20.05±0.90</td><td>17.78±0.81</td><td>14.83±0.08</td><td>17.36±0.69</td><td>18.06±0.00</td><td>19.09±0.05</td></tr><tr><td>+DMosaic</td><td>(40.94±1.74)</td><td>(36.10±2.36)</td><td>(32.80±2.22)</td><td>(24.85±3.91)</td><td>(24.13±3.66)</td><td>(26.17±2.89)</td><td>(11.74±0.48)</td><td>(6.86±0.63)</td><td>(4.93±0.29)</td><td>(17.88±0.16)</td><td>(18.78±0.08)</td><td>(18.04±0.14)</td></tr></table>

# 4.2 Results and Analysis

We conducted in- depth analysis of the performance of various methods under different degrees of data heterogeneity on FMNIST, SVHN, CIFAR- 10, and CIFAR- 100, as shown in Table 1. In the table, bold values indicate the highest accuracy, while underlined values denote the second- highest in each column. It is evident that as the value of  $\omega$  decreases, all methods experience a significant performance degradation. However, Mosaic exhibits remarkable robustness under severe data heterogeneity. This can be attributed to its stable generator ensemble and powerful MoE teacher, which together enable more effective knowledge transfer to the global model. Moreover, Mosaic achieves remarkably high local accuracy (L.acc), primarily because the distilled global model is further fine- tuned on each client after deployment. Conceptually, the well- trained student model is further adapted to the local data distribution of each client, resulting in highly accurate local predictions. On the other hand, when  $\omega$  is large and the client distributions are more uniform, the advantage of Mosaic becomes less pronounced. This is because the decision boundaries of the MoE teacher closely align with those of the global model, thereby limiting the additional knowledge that can be transferred during distillation.

We also evaluate the effects of different model heterogeneity distributions on various DFKD methods5 using SVHN, CIFAR- 10, Tiny- ImageNet, and FOOD101, as shown in Table 2. In this setting, we introduce a moderate degree of data heterogeneity by default, with  $\omega = 0.1$ . The model heterogeneity configuration introduces significant uncertainty, leading to highly variable results across datasets. Mosaic achieves SOTA performance on SVHN, CIFAR- 10, and FOOD101, with average improvements of  $13.95\%$ ,  $12.89\%$ , and  $12.47\%$  respectively. However, its performance on Tiny- ImageNet is unexpectedly irregular. As discussed in Section 3.2, we adopt a conventional ensemble as the teacher to reduce inference cost, which still achieves over  $50\%$  accuracy and is sufficiently informative for distillation. We suspect this anomaly is caused by the limited capacity of the simple generator, which fails to produce sufficiently diverse or high- quality samples. We leave this issue as a potential direction for future improvement.

# 4.3 Ablation Study

In this section, we perform an ablation study of Mosaic's core modules on SVHN, CIFAR- 10, and FOOD101 under consistent settings with  $w = 0.1$  and  $\rho = 10$ .

Impacts of Generator Loss Components. As shown in Table 3, each loss component of the generator is essential and contributes to performance improvements.  $\mathcal{L}_{\mathrm{adv}}$  forms the absolute foundation, with  $\mathcal{L}_{\mathrm{entropy}}$  and  $\mathcal{L}_{\mathrm{diversity}}$  offering slight enhancements. It is important to note that when  $\mathcal{L}_{\mathrm{inversion}}$  is introduced, the value of  $\mathcal{L}_{\mathrm{diversity}}$  nearly diminishes to zero during training, leading to an almost negligible performance gain. In this case, omitting  $\mathcal{L}_{\mathrm{diversity}}$  is also feasible. We also provide additional results on the effects of introducing  $\mathcal{L}_{\mathrm{inversion}}$  in low- data clients, with the corresponding learning curves presented in Appendix Q.

Impacts of MoE components. We select a representative case without model heterogeneity  $\rho = 0$  in CIFAR- 10 to highlight the effectiveness of our MoE design. We report the performance of the ensemble constructed using the meta model. As shown in Table 4, our MoE- based ensemble effectively integrates expert predictions and substantially outperforms the DENSE baseline, demonstrating the utility of these components. Additional results on teacher ensemble performance can be found in Appendix R.

Table 3: Test accuracy  $(\%)$  comparison among different transferability constraints over SVHN and CIFAR-10 and FOOD101.  

<table><tr><td>G. L.</td><td>SVHN</td><td>CIFAR-10</td><td>FOOD101</td></tr><tr><td rowspan="2">Ladv</td><td>36.77±3.22</td><td>23.95±2.37</td><td>17.45±0.07</td></tr><tr><td>(29.67±3.03)</td><td>(19.61±2.69)</td><td>(16.99±0.11)</td></tr><tr><td rowspan="2">Lentropy</td><td>37.46±3.54</td><td>24.80±3.11</td><td>7.85±0.06</td></tr><tr><td>(37.99±3.80)</td><td>(19.85±2.96)</td><td>(16.85±0.17)</td></tr><tr><td rowspan="2">Ldiversity</td><td>31.04±3.41</td><td>25.65±3.90</td><td>18.13±0.14</td></tr><tr><td>(31.04±3.21)</td><td>(19.94±2.91)</td><td>(17.03±0.22)</td></tr><tr><td rowspan="2">Linversion</td><td>40.61±3.21</td><td>28.74±3.89</td><td>18.98±0.10</td></tr><tr><td>(36.10±2.36)</td><td>(24.13±3.66)</td><td>(18.78±0.08)</td></tr></table>

Table 4: Comparison of different ensemble methods on CIFAR-10 dataset.  

<table><tr><td>Methods</td><td>ω = 1.0</td><td>ω = 0.1</td><td>ω = 0.01</td></tr><tr><td>DENSE</td><td>62.22 ± 2.69</td><td>50.15 ± 2.13</td><td>24.95 ± 3.32</td></tr><tr><td>F</td><td>77.64 ± 1.33</td><td>59.21 ± 1.89</td><td>40.41 ± 0.98</td></tr><tr><td>+meta</td><td>80.05 ± 0.54</td><td>64.66 ± 0.49</td><td>51.33 ± 1.10</td></tr></table>

# 4.4 Further Analysis

Discussion on the Gating Network. When  $C$  is small or  $N$  is set to a low value, the gating network is less necessary, meaning  $\mathrm{Top}_k = C$  or  $\mathrm{Top}_k = N$ . In more balanced cases such as CIFAR- 100 where  $C$  and  $N$  are comparable, setting  $k = C / 3$  already yields strong performance.

Model Heterogeneity of Generators. Since local models account for heterogeneity, generators can likewise vary in capacity due to resource constraints. The one- shot uploading strategy in Mosaic, which constructs a generator ensemble, remains effective under generator model heterogeneity. However, we acknowledge our limitation that, compared to methods like DFRD which train generators solely on the server, our approach requires more training on clients. Nonetheless, it achieves better generation quality.

Impact of Client Numbers on Mosaic. We discuss the impact of varying client numbers on Mosaic. This involves whether generators trained on clients with fewer samples are reliable, as well as the composition and performance of the MoE teacher. In this context, Mosaic maintains strong performance as  $N$  increases, with detailed results provided in Appendix S.

Reason for Not Using Synthetic Data in Meta Model Training. We experimented with training the meta model using synthetic data, but its limited transferability failed to enhance the simple MLP's capability and instead degraded performance, likely due to distributional bias that misguides decision boundaries. Detailed analyses and results supporting this decision are provided in Appendix T.

# 5 Conclusion

In this work, we presented Mosaic, a DFKD framework designed to address the fundamental challenges of model and data heterogeneity in FL. By empowering each client to train a personalized generative model, Mosaic captures the essence of local data distributions without exposing real data—ensuring strict privacy preservation. These client- specific generators enable the creation of synthetic samples that fuel a MoE architecture, distilling the collective intelligence of diverse client models into a unified global model. To enhance coordination among experts, a lightweight meta model is trained on a small set of representative prototypes, yielding more coherent and robust predictions. Our comprehensive evaluation across standard image classification benchmarks demonstrates that Mosaic significantly outperforms existing SOTA methods with varying degrees of heterogeneity. Our findings underscore the efficacy of combining DKFD with expert ensemble strategies, paving the way for scalable and privacy- aware FL in highly heterogeneous environments.

# References

[1] Sravanti Addepalli, Gaurav Nayak, Anirban Chakraborty, and R. Babu. Degan : Data- enriching gan for retrieving representative samples from a trained classifier. In Proceedings of the AAAI Conference on Artificial Intelligence, 12 2019. doi: 10.48550/arXiv.1912.11960.

[2] Samuil Alam, Luyang Liu, Ming Yan, and Mi Zhang. Fedrolex: Model- heterogeneous federated learning with rolling sub- model extraction. In S. Koyejo, S. Mohamed, A. Agarwal, D. Belgrave, K. Cho, and A. Oh, editors, Advances in Neural Information Processing Systems, volume 35, pages 29677- 29690. Curran Associates, Inc., 2022. URL https://proceedings.neurips.cc/paper_files/paper/2022/file/bf5311df07f3efce97471921e6c2f159- Paper- Conference.pdf.

[3] Kuluhan Binici, Shivam Agarwal, Nam Trung Pham, Karianto Leman, and Tulika Mitra. Robust and resource- efficient data- free knowledge distillation by generative pseudo replay. Proceedings of the AAAI Conference on Artificial Intelligence, 36(6):6089- 6096, Jun. 2022. doi: 10.1609/aaai.v36i6.20556. URL https://ojs.aaai.org/index.php/AAAI/article/view/20556.

[4] Lukas Bossard, Matthieu Guillaumin, and Luc Van Gool. Food- 101 - mining discriminative components with random forests. In David Fleet, Tomas Pajdla, Bernt Schiele, and Tinne Tuytelaars, editors, Computer Vision - ECCV 2014, pages 446- 461, Cham, 2014. Springer International Publishing. ISBN 978- 3- 319- 10599- 4.

[5] Leo Breiman. Bagging predictors. Machine learning, 24:123- 140, 1996.

[6] David Byrd and Antigoni Polychroniadou. Differentially private secure multi- party computation for federated learning in financial applications. In Proceedings of the First ACM International Conference on AI in Finance, ICAIF '20, New York, NY, USA, 2021. Association for Computing Machinery. ISBN 9781450375849. doi: 10.1145/3383455.3422562. URL https://doi.org/10.1145/3383455.3422562.

[7] Sebastian Caldas, Jakub Konecny, H Brendan McMahan, and Ameet Talwalkar. Expanding the reach of federated learning by reducing client resource requirements. arXiv preprint arXiv:1812.07210, 2018.

[8] Vishnu Pandi Chellapandi, Liangqi Yuan, Christopher G. Brinton, Stanislaw H. Zak, and Ziran Wang. Federated learning for connected and automated vehicles: A survey of existing approaches and challenges. IEEE Transactions on Intelligent Vehicles, 9(1):119- 137, 2024. doi: 10.1109/TIV.2023.3332675.

[9] Ting Chen, Simon Kornblith, Mohammad Norouzi, and Geoffrey Hinton. A simple framework for contrastive learning of visual representations. In Hal Daume III and Aarti Singh, editors, Proceedings of the 37th International Conference on Machine Learning, volume 119 of Proceedings of Machine Learning Research, pages 1597- 1607. PMLR, 13- 18 Jul 2020. URL https://proceedings.mlr.press/v119/chen20j.html.

[10] Patryk Chrabaszcz, Ilya Loshchilov, and Frank Hutter. A downsampled variant of imagenet as an alternative to the cifar datasets. arXiv preprint arXiv:1707.08819, 2017.

[11] Sen Cui, Abudukelimu Wuerkaixi, Weishen Pan, Jian Liang, Lei Fang, Changshui Zhang, and Fei Wang. CLAP: Collaborative adaptation for patchwork learning. In The Twelfth International Conference on Learning Representations, 2024. URL https://openreview.net/forum?id=8EyRkd3Qj5.

[12] Yutong Dai, Zeyuan Chen, Junnan Li, Shelby Heinecke, Lichao Sun, and Ran Xu. Tackling data heterogeneity in federated learning with class prototypes. Proceedings of the AAAI Conference on Artificial Intelligence, 37(6):7314- 7322, Jun. 2023. doi: 10.1609/aaai.v37i6.25891. URL https://ojs.aaai.org/index.php/AAAI/article/view/25891.

[13] Bibhu Dash, Pawankumar Sharma, and Azad Ali. Federated learning for privacy- preserving: A review of pii data analysis in fintech. International Journal of Software Engineering & Applications (IUSEA), 13(4), 2022.

[14] Jia Deng, Wei Dong, Richard Socher, Li- Jia Li, Kai Li, and Li Fei- Fei. Imagenet: A large- scale hierarchical image database. In 2009 IEEE Conference on Computer Vision and Pattern Recognition, pages 248- 255, 2009. doi: 10.1109/CVPR.2009.5206848.

[15] Enmano Diao, Jie Ding, and Vahid Tarokh. Hetero{fl}: Computation and communication efficient federated learning for heterogeneous clients. In International Conference on Learning Representations, 2021. URL https://openreview.net/forum?id=TNkPBBYFkXg.

[16] Moming Duan, Qinbin Li, Linshan Jiang, and Bingsheng He. Towards open federated learning platforms: Survey and vision from technical and legal perspectives. arXiv e- prints, art. arXiv:2307.02140, July 2023. doi: 10.48550/arXiv.2307.02140.

[17] Gokberk Elmas, Salman U. H. Dar, Yilmaz Korkmaz, Emir Ceyani, Burak Susam, Muzaffer Ozbey, Salman Avestimehr, and Tolga Cukur. Federated learning of generative image priors for mri reconstruction. IEEE Transactions on Medical Imaging, 42(7):1996- 2009, 2023. doi: 10.1109/TMI.2022.3220757.

[18] Haokun tang and Quan Qian. Privacy preserving machine learning with homomorphic encryption and federated learning. Future Internet, 13(4), 2021. ISSN 1999- 5903. doi: 10.3390/fi13040094. URL https://www.mdpi.com/1999- 5903/13/4/94.

[19] Xiuwen Fang and Mang Ye. Robust federated learning with noisy and heterogeneous clients. In Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition (CVPR), pages 10072- 10081, June 2022.

[20] Tiantian Feng, Dibbalay Bose, Tuo Zhang, Rajat Hebbar, Anil Ramakrishna, Rahul Gupta, Mi Zhang, Salman Avestimehr, and Shrikanth Narayanan. Fedmultimodal: A benchmark for multimodal federated learning. In Proceedings of the 29th ACM SIGKDD Conference on Knowledge Discovery and Data Mining, KDD '23, page 4035- 4045, New York, NY, USA, 2023. Association for Computing Machinery. ISBN 9798400701030. doi: 10.1145/3580305.3599825. URL https://doi.org/10.1145/3580305.3599825.

[21] Lei Fu, Huanle Zhang, Ge Gao, Mi Zhang, and Xin Liu. Client selection in federated learning: Principles, challenges, and opportunities. IEEE Internet of Things Journal, 10(24):21811- 21819, 2023. doi: 10.1109/IIOT.2023.3299573.

[22] Dashan Gao, Xin Yao, and Qiang Yang. A survey on heterogeneous federated learning. arXiv preprint arXiv:2210.04505, 2022. URL https://arxiv.org/abs/2210.04505.

[23] Liang Gao, Huazhu Fu, Li Li, Yingwen Chen, Ming Xu, and Cheng- Zhong Xu. Feddc: Federated learning with non- iid data via local drift decoupling and correction. In Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition (CVPR), pages 10112- 10121, June 2022.

[24] Lingzhi Gao, Zhenyuan Zhang, and Chao Wu. Feddgt: Federated data- free knowledge distillation via three- player generative adversarial networks. arXiv preprint arXiv:2201.03169, 2022.

[25] Ian J. Goodfellow, Jean Pouget- Abadie, Mehdi Mirza, Bing Xu, David Warde- Farley, Sherijl Ozair, Aaron Courville, and Yoshua Bengio. Generative adversarial nets. In Z. Ghahramani, M. Welling, C. Cortes, N. Lawrence, and K.Q. Weinberger, editors, Advances in Neural Information Processing Systems, volume 27. Curran Associates, Inc., 2014. URL https://proceedings.neurips.cc/paper_files/paper/2014/file/f033ed80deb0234979a61f95710dbe25- Paper.pdf.

[26] Jianping Gou, Baosheng Yu, Stephen J. Maybank, and Dacheng Tao. Knowledge distillation: A survey. International Journal of Computer Vision, 129(6):1789- 1819, 2021. doi: 10.1007/s11263- 021- 01453- z.

[27] Neel Guha, Ameet Talwalkar, and Virginia Smith. One- shot federated learning. arXiv preprint arXiv:1902.11175, 2019.

[28] Kaiming He, Xiangyu Zhang, Shaoqing Ren, and Jian Sun. Deep residual learning for image recognition. In Proceedings of the IEEE Conference on Computer Vision and Pattern Recognition (CVPR), June 2016.

[29] Clare Elizabeth Heinbaugh, Emilio Luz- Ricca, and Huajie Shao. Data- free one- shot federated learning under very high statistical heterogeneity. In The Eleventh International Conference on Learning Representations, 2023. URL https://openreview.net/forum?id=_hb4vM3jspB.

[30] Martin Heusel, Hubert Ramsauer, Thomas Unterthiner, Bernhard Nessler, and Sepp Hochreiter. Gans trained by a two time- scale update rule converge to a local nash equilibrium. In Advances in Neural Information Processing Systems, pages 6626- 6637, 2017. URL https://arxiv.org/abs/1706.08500.

[31] Geoffrey Hinton, Oriol Vinyals, and Jeffrey Dean. Distilling the knowledge in a neural network. In NIPS Deep Learning and Representation Learning Workshop, 2015. URL http://arxiv.org/abs/1503.02531.

[32] Jonathan Ho, Ajay Jain, and Pieter Abbeel. Denoising diffusion probabilistic models. In H. Larochelle, M. Ranzato, R. Hadsell, M.F. Balcan, and H. Lin, editors, Advances in Neural Information Processing Systems, volume 33, pages 6840- 6851. Curran Associates, Inc., 2020. URL https://proceedings.neurips.cc/paper_files/paper/2020/file/4c5bcfec8584af0d967f1ab10179ca4b- Paper.pdf.

[33] Samuel Horváth, Stefanos Laskaridis, Mario Almeida, Ilias Leontiadis, Stylianos Venieris, and Nicholas Lane. Fjord: Fair and accurate federated learning under heterogeneous targets with ordered dropout. In M. Ranzato, A. Beygelzimer, Y. Dauphin, P.S. Liang, and J. Wortman Vaughan, editors, Advances in Neural Information Processing Systems, volume 34, pages 12876- 12889. Curran Associates, Inc., 2021. URL https://proceedings.neurips.cc/paper_files/paper/2021/file/6aed000af86a084f9cb0264161e29dd3- Paper.pdf.

[34] Chengxiang Huang and Bingyan Liu. Pa3fed: Period- aware adaptive aggregation for improved federated learning. Proceedings of the AAAI Conference on Artificial Intelligence, 39(16):17395- 17403, Apr. 2025. doi: 10.1609/aaai.v39i16.33912. URL https://ojs.aaai.org/index.php/AAAI/article/view/33912.

[35] Jia Huang, Zhen Chen, Shengzheng Liu, and Haixia Long. A novel federated learning framework based on conditional generative adversarial networks for privacy preserving in 6g. Electronics, 13(4), 2024. ISSN 2079- 9292. doi: 10.3390/electronics13040783. URL https://www.mdpi.com/2079- 9292/13/4/783.

[36] Wenke Huang, Mang Ye, and Bo Du. Learn from others and be yourself in heterogeneous federated learning. In Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition (CVPR), pages 10143- 10153, June 2022.

[37] Wenke Huang, Mang Ye, Zekun Shi, He Li, and Bo Du. Rethinking federated learning with domain shift: A prototype view. In 2023 IEEE/CVF Conference on Computer Vision and Pattern Recognition (CVPR), pages 16312- 16322, 2023. doi: 10.1109/CVPR52729.2023.01565.

[38] Ahmed Imteaj, Urmish Thakker, Shiqiang Wang, Jian Li, and M. Hadi Amini. A survey on federated learning for resource- constrained iot devices. IEEE Internet of Things Journal, 9(1):1- 24, 2022. doi: 10.1109/IJOT.2021.3095077.

[39] Sergey Ioffe and Christian Szegedy. Batch normalization: Accelerating deep network training by reducing internal covariate shift. In Francis Bach and David Blei, editors, Proceedings of the 32nd International Conference on Machine Learning, volume 37 of Proceedings of Machine Learning Research, pages 448- 456, Lille, France, 07- 09- Jul 2015. PMLR. URL https://proceedings.rlan. press/vi27/iaffe15. html.

[40] Robert A Jacobs, Michael I Jordan, Steven J Nowlan, and Geoffrey E Hinton. Adaptive mixtures of local experts. Neural computation, 3(1):79- 87, 1991.

[41] Changkun Jiang, Jiahao Chen, Lin Gao, and Jianqiang Li. Fedpartial: Enabling model- heterogeneous federated learning via partial model transmission and aggregation. In 2024 IEEE International Conference on Web Services (ICWS), pages 1145- 1152, 2024. doi: 10.1109/ICWS62655.2024.00137.

[42] Liming Jiang, Bo Dai, Wayne Wu, and Chen Change Loy. Deceive d: Adaptive pseudo augmentation for gan training with limited data. In M. Ranzato, A. Beygelzimer, Y. Dauphin, P.S. Liang, and J. Wortman Vaughan, editors, Advances in Neural Information Processing Systems, volume 34, pages 21655- 21667. Curran Associates, Inc., 2021. URL https://proceedings.neurips.cc/paper_files/paper/2021/file/b534ba68236ba543ae44b22bd110a1d6- Paper.pdf.

[43] Peter Korouz, H. Brendan McMahan, Brendan Avon, Aurelien Bellet, Mehdi Bennis, Arjun Nitin Bhagoji, Kallista Bonawitz, Zachary Charles, Graham Cormode, Rachel Cummings, Rafael G. L. D'Oliveira, Hubert Eichner, Salim El Rouayheb, David Evans, Josh Gardner, Zachary Garrett, Adria Gascón, Badih Ghazi, Phillip B. Gibbons, Marco Gruteser, Zaid Harchaoui, Chaoyang He, Lie He, Zhouyuan Huo, Ben Hutchinson, Justin Hsu, Martin Jaggi, Tara Javidi, Gauri Joshi, Mikhail Khodak, Jakub Konecný, Aleksandra Korolova, Farinaz Koushanfar, Samni Koyejo, Tancrède Lepoint, Yang Liu, Prateek Mittal, Mehnyar Mohri, Richard Nock, Ayfer Özgür, Rasmus Pagh, Hang Qi, Daniel Ramage, Ramesh Raskar, Mariana Raykova, Dawn Song, Weikang Song, Sebastian U. Stich, Ziteng Sun, Ananda Theertha Suresh, Florian Tramèr, Praneeth Vepakomma, Jianyu Wang, Li Xiong, Zheng Xu, Qiang Yang, Felix X. Yu, Han Yu, and Sen Zhao. Advances and open problems in federated learning. Foundations and Trends in Machine Learning, 14(1- 2):1- 210, 2021. ISSN 1935- 8237. doi: 10.1561/2200000083. URL http://dx.doi.org/10.1561/2200000083.

[44] Sai Praneeth Karimireddy, Satyen Kale, Mehryar Mohri, Sashank Reddi, Sebastian Stich, and Ananda Theertha Suresh. SCAFFOLD: Stochastic controlled averaging for federated learning. In Hal Daume III and Aarti Singh, editors, Proceedings of the 37th International Conference on Machine Learning, volume 119 of Proceedings of Machine Learning Research, pages 5132- 5143. PMLR, 13- 18 Jul 2020. URL https://proceedings.mlr.press/v119/karimireddy20a.html.

[45] Tero Karras, Miika Aittala, Janne Hellsten, Samuli Laine, Jaakko Lehtinen, and Timo Alia. Training generative adversarial networks with limited data. In H. Larochelle, M. Ranzato, R. Hadsell, M.F. Balcan, and H. Lin, editors, Advances in Neural Information Processing Systems, volume 33, pages 12104- 12114. Curran Associates, Inc., 2020. URL https://proceedings.neurips.cc/paper_files/paper/2020/file/8d30aa96e72440759f74bd2306c1fa3d- Paper.pdf.

[46] Amirhossein Kazerouni, Ehsan Khodapanah Aghdam, Moein Heidari, Reza Azad, Mohsen Fayyaz, Ilker Hacihaliloglu, and Dorit Merhof. Diffusion models in medical imaging: A comprehensive survey. Medical Image Analysis, page 102846, 2023.

[47] Diederik P Kingma, Max Welling, et al. Auto- encoding variational bayes. In International Conference on Learning Representations (ICLR), 2014. URL https://arxiv.org/abs/1312.6114.

[48] Alex Krizhevsky, Geoffrey Hinton, et al. Learning multiple layers of features from tiny images. Technical report, University of Toronto, 2009.

[49] Alex Krizhevsky, Ilya Sutskever, and Geoffrey E Hinton. Imagenet classification with deep convolutional neural networks. In F. Pereira, C.J. Burges, L. Bottou, and K.Q. Weinberger, editors, Advances in Neural Information Processing Systems, volume 25. Curran Associates, Inc., 2012. URL https://proceedings.neurips.cc/paper_files/paper/2012/file/c399862d3b9d6b76c8436e924a68c45b- Paper.pdf.

[50] Hanane Lamaazi, Aysha Muaded Moha Alneyadi, and Mohamed Adel Serhani. Academic data privacy- preserving using centralized and distributed systems: A comparative study. In Proceedings of the 2024 6th International Conference on Big- Data Service and Intelligent Computation, BDSIC '24, page 8- 16. New York, NY, USA, 2024. Association for Computing Machinery. ISBN 9798400718069. doi: 10.1145/3686540.3686542. URL https://doi.org/10.1145/3686540.3686542.

[51] Y. Lecun, L. Bottou, Y. Bengio, and P. Haffner. Gradient- based learning applied to document recognition. Proceedings of the IEEE, 86(11):2278- 2324, 1998. doi: 10.1109/5.726791.

[52] Y. Lecun, L. Bottou, Y. Bengio, and P. Haffner. Gradient- based learning applied to document recognition. Proceedings of the IEEE, 86(11):2278- 2324, 1998. doi: 10.1109/5.726791.

[53] Daliang Li and Junpu Wang. Fedmd: Heterogeneous federated learning via model distillation. arXiv preprint arXiv:1910.03581, 2019. URL https://arxiv.org/abs/1910.03581.

[54] Lin Li, Jianping Gou, Baosheng Yu, Lan Du, and Zhang Yiand Dacheng Tao. Federated distillation: A survey. arXiv preprint arXiv:2404.08564, 2024.

[55] Qinbin Li, Bingsheng He, and Dawn Song. Model- contrastive federated learning. In Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition (CVPR), pages 10713- 10722, June 2021.

[56] Tian Li, Anit Kumar Sahu, Ameet Talwalkar, and Virginia Smith. Federated learning: Challenges, methods, and future directions. IEEE Signal Processing Magazine, 37(3):50- 60, 2020. doi: 10.1109/MSP.2020.2975749.

[57] Tian Li, Anit Kumar Sahu, Ameet Talwalkar, and Virginia Smith. Federated learning: Challenges, methods, and future directions. IEEE Signal Processing Magazine, 37(3):50- 60, 2020. doi: 10.1109/MSP.2020.2975749.

[58] Tian Li, Anit Kumar Sahu, Manzil Zaheer, Maziar Sanjabi, Ameet Talwalkar, and Virginia Smith. Federated optimization in heterogeneous networks. In I. Dhillon, D. Papailiopoulos, and V. Sze, editors, Proceedings of Machine Learning and Systems, volume 2, pages 429- 450, 2020. URL https://proceedings.mlsys.org/paper_files/paper/2020/file/1f5fe83998a09396ebe6477d9475ba0c- Paper.pdf.

[59] Xiaoxiao Li, Meirui Jiang, Xiaofei Zhang, Michael Kamp, and Qi Dou. Fedbn: Federated learning on non- iid features via local batch normalization. arXiv preprint arXiv:2102.07623, 2021.

[60] Xin- Chun Li and De- Chuan Zhan. Fedrs: Federated learning with restricted softmax for label distribution non- iid data. In Proceedings of the 27th ACM SIGKDD Conference on Knowledge Discovery & Data Mining, KDD '21, page 995- 1005, New York, NY, USA, 2021. Association for Computing Machinery. ISBN 9781450383325. doi: 10.1145/3447548.3467254. URL https://doi.org/10.1145/3447548.3467254.

[61] Junming Liu, Guosun Zeng, Ding Wang, Yanting Gao, and Yufei Jin. Fedrecon: Missing modality reconstruction in distributed heterogeneous environments. arXiv preprint arXiv:2504.09941, 2025.

[62] Jonathan Long, Evan Shelhamer, and Trevor Darrell. Fully convolutional networks for semantic segmentation. In Proceedings of the IEEE Conference on Computer Vision and Pattern Recognition (CVPR), June 2015.

[63] Zihao Lu, Junli Wang, and Changjun Jiang. Data- free knowledge filtering and distillation in federated learning. IEEE Transactions on Big Data, pages 1- 16, 2024. doi: 10.1109/TBDATA.2024.3442551.

[64] kangyang Luo, Shuai Wang, Yexuan Fu, Xiang Li, Yunshi Lan, and Ming Gao. Dfrd: Data- free robustness distillation for heterogeneous federated learning. In A. Oh, T. Naumann, A. Globerson, K. Saenko, M. Hardt, and S. Levine, editors, Advances in Neural Information Processing Systems, volume 36, pages 17854- 17866. Curran Associates, Inc., 2023. URL https://proceedings.neurips.cc/paper_files/paper/2023/file/39ca8893ea38905a9d2ffe786e85af0f- Paper- Conference.pdf.

[65] Kangyang Luo, Shuai Wang, Yexuan Fu, Renrong Shao, Xiang Li, Yunshi Lan, Ming Gao, and Jinlong Shu. Dfrdg: Data- free dual- generator adversarial distillation for one- shot federated learning. arXiv preprint arXiv:2409.07734, 2024.

[66] Kangyang Luo, Shuai Wang, Xiang Li, Yunshi Lan, Ming Gao, and Jinlong Shu. Privacy- preserving federated learning with consistency via knowledge distillation using conditional generator. arXiv preprint arXiv:2409.06955, 2024.

[67] Disha Makhija, Xing Han, Nhat Ho, and Joydeep Ghosh. Architecture agnostic federated learning for neural networks. In Kamalika Chaudhuri, Stefanie Jegelka, Le Song, Csaba Szepesvari, Gang Niu, and Sivan Sabato, editors, Proceedings of the 39th International Conference on Machine Learning, volume 162 of Proceedings of Machine Learning Research, pages 14860- 14870. PMLR, 17- 23 Jul 2022. URL https://proceedings.mlr.press/v162/makhija22a.html.

[68] Connor J. McLaughlin and Lili Su. Personalized federated learning via feature distribution adaptation. In A. Globerson, L. Mackey, D. Belgrave, A. Fan, U. Paquet, J. Tomczak, and C. Zhang, editors, Advances in Neural Information Processing Systems, volume 37, pages 77038- 77059. Curran Associates, Inc., 2024. URL https://proceedings.neurips.cc/paper_files/paper/2024/file/8ce6c5450ccddbe6adee4b3749893587- Paper- Conference.pdf.

[69] Brendan McMahan, Eider Moore, Daniel Ramage, Seth Hampson, and Blaise Aguera y Arcas. Communication- Efficient Learning of Deep Networks from Decentralized Data. In Aarti Singh and Jerry Zhu, editors, Proceedings of the 20th International Conference on Artificial Intelligence and Statistics, volume 54 of Proceedings of Machine Learning Research, pages 1273- 1282. PMLR, 20- 22 Apr 2017. URL https://proceedings.mlr.press/v54/mcmahan17a.html.

[70] Matias Mendieta, Taojiannan Yang, Pu Wang, Minwoo Lee, Zhengming Ding, and Chen Chen. Local learning matters: Rethinking data heterogeneity in federated learning. In Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition, pages 8397- 8406, 2022.

[71] Luke Metz, Ben Poole, David Pfau, and Jascha Sohl- Dickstein. Unrolled generative adversarial networks. arXiv preprint arXiv:1611.02163, 2016.

[72] Vaikkunth Mugunthan, Antigoni Polychroniadou, David Byrd, and Tucker Hybinette Balch. Smpai: Secure multi- party computation for federated learning. In Proceedings of the NeurIPS 2019 Workshop on Robust AI in Financial Services, volume 21. MIT Press Cambridge, MA, USA, 2019.

[73] Yuva Netzer, Tao Wang, Adam Coates, Alessandro Bissacco, Baolin Wu, and Andrew Y. Ng. Reading digits in natural images with unsupervised feature learning. In NIPS workshop on deep learning and unsupervised feature learning, Granada, Spain, 2011.

[74] Dinh C. Nguyen, Quoc- Viet Pham, Pubudu N. Pathirana, Ming Ding, Aruna Seneviratne, Zihuai Lin, Octavia Dobre, and Won- Joo Hwang. Federated learning for smart healthcare: A survey. ACM Comput. Surv., 55(3), February 2022. ISSN 0360- 0300. doi: 10.1145/3501296. URL https://doi.org/10.1145/3501296.

[75] Takayuki Nishio and Ryo Yonetani. Client selection for federated learning with heterogeneous resources in mobile edge. In ICC 2019 - 2019 IEEE International Conference on Communications (ICC), pages 1- 7, 2019. doi: 10.1109/ICC.2019.8761315.

[76] Ahmed El Ouadrhiri and Ahmed Abdelhadi. Differential privacy for deep and federated learning: A survey. IEEE Access, 10:22359- 22380, 2022. doi: 10.1109/ACCESS.2022.3151670.

[77] Chen Qiu, Xingyu Li, Chaithanya Kumar Mummadi, Madan Ravi Ganesh, Zhenzhen Li, Lu Peng, and Wan- Yi Lin. Federated text- driven prompt generation for vision- language models. In The Twelfth International Conference on Learning Representations, 2024. URL https://openreview.net/forum?id= NW31gAy1Im.

[78] Liangqiong Qu, Yuyin Zhou, Paul Pu Liang, Yingda Xia, Feifei Wang, Ehsan Adeli, Li Fei- Fei, and Daniel Rubin. Rethinking architecture design for tackling data heterogeneity in federated learning. In Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition (CVPR), pages 10061- 10071, June 2022.

[79] Alec Radford, Jong Wook Kim, Chris Hallacy, Aditya Ramesh, Gabriel Goh, Sandhini Agarwal, Girish Sastry, Amanda Askell, Pamela Mishkin, Jack Clark, Gretchen Krueger, and Ilya Sutskever. Learning transferable visual models from natural language supervision. In Marine Meijo and Tang Zhang, editors, Proceedings of the 38th International Conference on Machine Learning, volume 139 of Proceedings of Machine Learning Research, pages 8748- 8763. PMLR, 18- 24 Jul 2021. URL https://proceedings.mlr.press/v139/radford21a.html.

[80] Mohammad Rasouli, Tao Sun, and Ram Rajagopal. Fedgan: Federated generative adversarial networks for distributed data. arXiv preprint arXiv:2006.07228, 2020.

[81] Sashank Reddi, Zachary Charles, Manzil Zaheer, Zachary Garrett, Keith Rush, Jakub Konecny, Sanjiv Kumar, and H Brendan McMahan. Adaptive federated optimization. arXiv preprint arXiv:2003.00295, 2020.

[82] Joseph Redmon, Santosh Divvala, Ross Girshick, and Ali Farhadi. You only look once: Unified, real- time object detection. In Proceedings of the IEEE Conference on Computer Vision and Pattern Recognition (CVPR), June 2016.

[83] Peter J Reusseeuw. Silhouettes: a graphical aid to the interpretation and validation of cluster analysis. Journal of computational and applied mathematics, 20:53- 65, 1987.

[84] David E Rumelhart, Geoffrey E Hinton, Ronald J Williams, et al. Learning internal representations by error propagation, 1985.

[85] Tim Salimans, Ian Goodfellow, Wojciech Zaremba, Vicki Cheung, Alec Radford, and Xi Chen. Improved techniques for training gans. In Advances in Neural Information Processing Systems, pages 2234- 2242, 2016. URL https://arxiv.org/abs/1606.03498.

[86] Giovanni Seni and John Elder. Ensemble methods in data mining: improving accuracy through combining predictions. Morgan & Claypool Publishers, 2010.

[87] Xinyi Shang, Yang Lu, Yiu- Ming Cheung, and Hanzi Wang. Fedic: Federated learning on non- iid and long- tailed data via calibrated distillation. In 2022 IEEE International Conference on Multimedia and Expo (ICME), pages 1- 6, 2022. doi: 10.1109/ICME52920.2022.9860009.

[88] Xinyi Shang, Yang Lu, Gang Huang, and Hanzi Wang. Federated learning on heterogeneous and long- tailed data via classifier re- training with federated features. In Proceedings of the 31st International Joint Conference on Artificial Intelligence (IJCAI), pages 2193- 2199, 07 2022. doi: 10.24963/ijcai.2022/305.

[89] Renrong Shao, Wei Zhang, and Jun Wang. Conditional pseudo- supervised contrast for data- free knowledge distillation. Pattern Recognition, 143:109781, 2023. ISSN 0031- 3203. doi: https://doi.org/10.1016/j.patcog.2023.109781. URL https://www.sciencedirect.com/science/article/pii/S003132032300479X.

[90] Renrong Shao, Wei Zhang, Jianhua Yin, and Jun Wang. Data- free knowledge distillation for fine- grained visual categorization. In Proceedings of the IEEE/CVF International Conference on Computer Vision (ICCV), pages 1515- 1525, October 2023.

[91] Tao Shen, Jie Zhang, Xinkang Jia, Fengda Zhang, Gang Huang, Pan Zhou, Kun Kuang, Fei Wu, and Chao Wu. Federated mutual learning. arXiv preprint arXiv:2006.16765, 2020.

[92] Weisong Shi, Jie Cao, Quan Zhang, Youhuizi Li, and Lanyu Xu. Edge computing: Vision and challenges. IEEE Internet of Things Journal, 3(5):637- 646, 2016. doi: 10.1109/JIOT.2016.2579198.

[93] Hyunjune Shin and Dong- Wan Choi. Teacher as a lenient expert: Teacher- agnostic data- free knowledge distillation. Proceedings of the AAAI Conference on Artificial Intelligence, 38(13):14991- 14999, Mar. 2024. doi: 10.1609/aaai.v38i13.29420. URL https://ojs.aaai.org/index.php/AAAI/article/view/29420.

[94] Karen Simonyan and Andrew Zisserman. Very deep convolutional networks for large- scale image recognition. arXiv preprint arXiv:1409.1556, 2014.

[95] Chuaneng Sun, Tingcong Jiang, Saman Zonoux, and Dario Pompili. Fed2kd: Heterogeneous federated learning for pandemic risk assessment via two- way knowledge distillation. In 2022 17th Wireless On- Demand Network Systems and Services Conference (WONS), pages 1- 8, 2022. doi: 10.23919/ WONS54113.2022.9764443.

[96] Chuaneng Sun, Tingcong Jiang, and Dario Pompili. Heterogeneous federated learning via generative model- aided knowledge distillation in the edge. IEEE Internet of Things Journal, 12(5):5589- 5599, 2025. doi: 10.1109/JIOT.2024.3488565.

[97] Wenjun Sun, Ruqiang Yan, Ruibing Jin, Rui Zhao, and Zhenghua Chen. Fedalign: Federated model alignment via data- free knowledge distillation for machine fault diagnosis. IEEE Transactions on Instrumentation and Measurement, 73:1- 12, 2024. doi: 10.1109/TIM.2023.3345910.

[98] Yan Sun, Li Shen, and Dacheng Tao. Understanding how consistency works in federated learning via stage- wise relaxed initialization. In A. Oh, T. Naumann, A. Globerson, K. Saenko, M. Hardt, and S. Levine, editors, Advances in Neural Information Processing Systems, volume 36, pages 80543- 80574. Curran Associates, Inc., 2023. URL https://proceedings.neurips.cc/paper_files/paper/ 2023/file/fef126561bbf9d4467dbb8d27334b8fe- Paper- Conference.pdf.

[99] Yue Tan, Guodong Long, LU LIU, Tianyi Zhou, Qinghua Lu, Jing Jiang, and Chengqi Zhang. Fedproto: Federated prototype learning across heterogeneous clients. Proceedings of the AAAI Conference on Artificial Intelligence, 36(8):8432- 8440, Jun. 2022. doi: 10.1609/aaai.v36i8.20819. URL https://ojs.aaai.org/index.php/AAAI/article/view/20819.

[100] Zhenheng Tang, Yonggang Zhang, Shaohuai Shi, Xin He, Bo Han, and Xiaowen Chu. Virtual homogeneity learning: Defending against data heterogeneity in federated learning. In Kamalika Chaudhuri, Stefanie Jegelka, Le Song, Csaba Szepesvari, Gang Niu, and Sivan Sabato, editors, Proceedings of the 39th International Conference on Machine Learning, volume 162 of Proceedings of Machine Learning Research, pages 21111- 21132. PMLR, 17- 23 Jul 2022. URL https://proceedings.mlr.press/v162/tang22d.html.

[101] Zhenheng Tang, Yonggang Zhang, Peijie Dong, Yiu- ming Cheung, Amelie Chi Zhou, Bo Han, and Xiaowen Chu. Fusefi: One- shot federated learning through the lens of causality with progressive model fusion. In A. Globerson, L. Mackey, D. Belgrave, A. Fan, U. Paquet, J. Tomczak, and C. Zhang, editors, Advances in Neural Information Processing Systems, volume 37, pages 28393- 28429. Curran Associates, Inc., 2024. URL https://proceedings.neurips.cc/paper_files/paper/2024/file/31e6e0c09325a3be16d93f84e40e0c7e- Paper- Conference.pdf.

[102] Minh- Tuan Tran, Trung Le, Xuan- May Le, Mehntash Harandi, Quan Hung Tran, and Dinh Phung. Nayer: Noisy layer data generation for efficient and effective data- free knowledge distillation. 2024 IEEE/CVF Conference on Computer Vision and Pattern Recognition (CVPR), pages 23860- 23869, 2023. URL https://api.semanticscholar.org/CorpusID:263334159.

[103] Stacey Truex, Ling Liu, Ka- Ho Chow, Mehmet Emre Gursoy, and Wenqi Wei. Ldp- fed: federated learning with local differential privacy. In Proceedings of the Third ACM International Workshop on Edge Systems, Analytics and Networking, EdgeSys '20, page 61- 66, New York, NY, USA, 2020. Association for Computing Machinery. ISBN 9781450371322. doi: 10.1145/3378679.3394533. URL https://doi.org/10.1145/3378679.3394533.

[104] Saeed Vahidian, Mahdi Morafah, Chen Chen, Mubarak Shah, and Bill Lin. Rethinking data heterogeneity in federated learning: Introducing a new notion and standard benchmarks. IEEE Transactions on Artificial Intelligence, 5(3):1386- 1397, 2024. doi: 10.1109/TAI.2023.3293068.

[105] Ashish Vaswani, Noam Shazeer, Niki Parmar, Jakob Uszkoreit, Llion Jones, Aidan N Gomez, L ukasz Kaiser, and Illia Polosukhin. Attention is all you need. In I. Guyon, U. Von Luxburg, S. Bengio, H. Wallach, R. Fergus, S. Vishwanathan, and R. Garnett, editors, Advances in Neural Information Processing Systems, volume 30. Curran Associates, Inc., 2017. URL https://proceedings.neurips.cc/paper_files/paper/2017/file/3f5ee243547dee91fbd053c14a845aa- Paper- pdf.

[106] Haozhao Wang, Peirong Zheng, Xingshuo Han, Wenchao Xu, Ruixuan Li, and Tianwei Zhang. Fednlr: Federated learning with neuron- wise learning rates. In Proceedings of the 30th ACM SIGKDD Conference on Knowledge Discovery and Data Mining, KDD '24, page 3069- 3080, New York, NY, USA, 2024. Association for Computing Machinery. ISBN 9798400704901. doi: 10.1145/3637528.3672042. URL https://doi.org/10.1145/3637528.3672042.

[107] Hongyi Wang, Mikhail Yurochkin, Yuekai Sun, Dimitris Papailiopoulos, and Yasaman Khazaeni. Federated learning with matched averaging. arXiv preprint arXiv:2002.06440, 2020.

[108] Jiaqi Wang, Xingyi Yang, Suhan Cui, Livei Che, Lingjuan Lyu, Dongkuan (DK) Xu, and Fenglong Ma. Towards personalized federated learning via heterogeneous model reassembly. In A. Oh, T. Naumann, A. Globerson, K. Saenko, M. Hardt, and S. Levine, editors, Advances in Neural Information Processing Systems, volume 36, pages 29515- 29531. Curran Associates, Inc., 2023. URL https://proceedings.neurips.cc/paper_files/paper/2023/file/5e2217482fa75556f1970be809acd3f8- Paper- Conference.pdf.

[109] Shiqiang Wang, Tiffany Tuor, Theodoros Salonidis, Kin K. Leung, Christian Makaya, Ting He, and Kevin Chan. Adaptive federated learning in resource constrained edge computing systems. IEEE Journal on Selected Areas in Communications, 37(6):1205- 1221, 2019. doi: 10.1109/JSAc.2019.2904548.

[110] Yuan Wang, Huazhu Fu, Renuga Kanagavelu, Qingsong Wei, Yong Liu, and Rick Siow Mong Goh. An aggregation- free federated learning for tackling data heterogeneity. In Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition (CVPR), pages 26233- 26242, June 2024.

[111] Yuzheng Wang, Dingkang Yang, Zhaoyu Chen, Yang Liu, Siao Liu, Wenqiang Zhang, Lihua Zhang, and Lizhe Qi. De- confounded data- free knowledge distillation for handling distribution shifts. In Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition (CVPR), pages 12615- 12625, June 2024.

[112] Kang Wei, Jun Li, Ming Ding, Chuan Ma, Howard H. Yang, Farhad Farokhi, Shi Jin, Tony Q. S. Quek, and H. Vincent Poor. Federated learning with differential privacy: Algorithms and performance analysis. IEEE Transactions on Information Forensics and Security, 15:3454- 3469, 2020. doi: 10.1109/TIFS.2020.2988575.

[113] David H Wolpert. Stacked generalization. Neural networks, 5(2):241- 259, 1992.

[114] David H Wolpert. Stacked generalization. Neural networks, 5(2):241- 259, 1992.

[115] Chuchen Wu, Fangzhao Wu, Lingjuan Lyu, Yongfeng Huang, and Xing Xie. Communication- efficient federated learning via knowledge distillation. Nature Communications, 13, 2021. URL https://api.semanticscholar.org/CorpusID:237353469.

[116] Yuezhou Wu, Yan Kang, Jiahuan Luo, Yuanqin He, and Qiang Yang. Fedcg: Leverage conditional gan for protecting privacy and maintaining competitive performance in federated learning. arXiv preprint arXiv:2111.08211, 2021.

[117] Han Xiao, Kashif Rasul, and Roland Vollgraf. Fashion- mnist: a novel image dataset for benchmarking machine learning algorithms. arXiv preprint arXiv:1708.07747, 2017.

[118] Chenhao Xu, Youyang Qu, Yong Xiang, and Longxiang Gao. Asynchronous federated learning on heterogeneous devices: A survey. Computer Science Review, 50:100595, 2023. ISSN 1574- 0137. doi: https://doi.org/10.1016/j.cosrev.2023.100595. URL https://www.sciencedirect.com/science/article/pii/S157401372300062X.

[119] Fei Yang, Xu Zhang, Shangwei Guo, Daiyuan Chen, Yan Gan, Tao Xiang, and Yang Liu. Robust and privacy- preserving collaborative training: a comprehensive survey. Artificial Intelligence Review, 57(7): 180, 2024.

[120] Yuwen Yang, Chang Liu, Xun Cai, Suizhi Huang, Hongtao Lu, and Yue Ding. Unideal Curriculum knowledge distillation federated learning. In ICASSP 2024 - 2024 IEEE International Conference on Acoustics, Speech and Signal Processing (ICASSP), pages 7145- 7149, 2024. doi: 10.1109/ICASSP48485.2024.10447769.

[121] Fanfan Ye, Bingyi Lu, Liang Ma, Qiaoyong Zhong, and Di Xie. Up to thousands- fold storage saving: Towards efficient data- free distillation of large- scale visual classifiers. In Proceedings of the 31st ACM International Conference on Multimedia, MM '23, page 8376- 8386, New York, NY, USA, 2023. Association for Computing Machinery. ISBN 9798400701085. doi: 10.1145/3581783.3611748. URL https://doi.org/10.1145/3581783.3611748.

[122] Mang Ye, Xiuwen Fang, Bo Du, Pong C. Yuen, and Dacheng Tao. Heterogeneous federated learning: State- of- the- art and research challenges. ACM Comput. Surv., 56(3), October 2023. ISSN 0360- 0300. doi: 10.1145/3625558. URL https://doi.org/10.1145/3625558.

[123] Hongxu Yin, Pavlo Molchanov, Jose M. Alvarez, Zhizhong Li, Arun Mallya, Derek Hoiem, Niraj K. Jha, and Jan Kautz. Dreaming to distill: Data- free knowledge transfer via deepinversion. In 2020 IEEE/CVF Conference on Computer Vision and Pattern Recognition (CVPR), pages 8712- 8721, 2020. doi: 10.1109/CVPR42600.2020.00874.

[124] Shikang Yu, Jiachen Chen, Hu Han, and Shuqiang Jiang. Data- free knowledge distillation via feature exchange and activation region constraint. In 2023 IEEE/CVF Conference on Computer Vision and Pattern Recognition (CVPR), pages 24266- 24275, 2023. doi: 10.1109/CVPR52729.2023.02324.

[125] Mikhail Yurochkin, Mayank Agarwal, Soumya Ghosh, Kristjan Greenewald, Nghia Hoang, and Yasaman Khazaeni. Bayesian nonparametric federated learning of neural networks. In Kamalika Chaudhuri and Ruslan Salakhutdinov, editors, Proceedings of the 36th International Conference on Machine Learning, volume 97 of Proceedings of Machine Learning Research, pages 7252- 7261. PMLR, 09- 15 Jun 2019. URL https://proceedings.mlr.press/v97/yurochkin19a.html.

[126] Hui Zeng, Minrui Xu, Tongqing Zhou, Xinyi Wu, Jiawen Kang, Zhiping Cai, and Dusit Niyato. One- shot- out- not- degraded federated learning. In Proceedings of the 32nd ACM International Conference on Multimedia, MM '24, page 11070- 11079, New York, NY, USA, 2024. Association for Computing Machinery. ISBN 9798400706868. doi: 10.1145/3664647.3680715. URL https://doi.org/10.1145/3664647.3680715.

[127] Chen Zhang, Yu Xie, Hang Bai, Bin Yu, Weihong Li, and Yuan Gao. A survey on federated learning. Knowledge- Based Systems, 216:106775, 2021. doi: 10.1016/j.knosys.2021.106775.

[128] Chengliang Zhang, Suyi Li, Junzhe Xia, Wei Wang, Feng Yan, and Yang Liu. BatchCrypt: Efficient homomorphic encryption for Cross- Silo federated learning. In 2020 USENIX Annual Technical Conference (USENIX ATC 20), pages 493- 506. USENIX Association, July 2020. ISBN 978- 1- 939133- 14- 4. URL https://www.usenix.org/conference/atc20/presentation/zhang- chengliang

[129] Jianqing Zhang, Yang Hua, Hao Wang, Tao Song, Zhengui Xue, Ruhui Ma, and Haibing Guan. Fedala: Adaptive local aggregation for personalized federated learning. Proceedings of the AAAI Conference on Artificial Intelligence, 37(9):11237- 11244, Jun. 2023. doi: 10.1609/aaai.v37i9.26330. URL https://ojs.aaai.org/index.php/AAAI/article/view/26330.

[130] Jianqing Zhang, Yang Liu, Yang Hua, and Jian Cao. Fedtgp: Trainable global prototypes with adaptive- margin- enhanced contrastive learning for data and model heterogeneity in federated learning. Proceedings of the AAAI Conference on Artificial Intelligence, 38(15):16768- 16776, Mar. 2024. doi: 10.1609/aaai.v38i15.29617. URL https://ojs.aaai.org/index.php/AAAI/article/view/29617.

[131] Jie Zhang, Chen Chen, Bo Li, Lingjuan Lyu, Shuang Wu, Shouhong Ding, Chunhua Shen, and Chao Wu. Dense: Data- free one- shot federated learning. In S. Koyejo, S. Mohamed, A. Agarwal, D. Belgrave, K. Cho, and A. Oh, editors, Advances in Neural Information Processing Systems, volume 35, pages 21414- 21428. Curran Associates, Inc., 2022. URL https://proceedings.neurips.cc/paper_ files/paper/2022/file/868f2266086530b2c71006ea1908b14a- Paper- Conference.pdf.

[132] Jie Zhang, Song Guo, Jingcai Guo, Deze Zeng, Jingren Zhou, and Albert Y. Zomaya. Towards data- independent knowledge transfer in model- heterogeneous federated learning. IEEE Transactions on Computers, 72(10):2888- 2901, 2023. doi: 10.1109/TC.2023.3272801.

[133] Lin Zhang, Li Shen, Liang Ding, Dacheng Tao, and Ling- Yu Duan. Fine- tuning global model via data- free knowledge distillation for non- iid federated learning. In Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition (CVPR), pages 10174- 10183, June 2022.

[134] Richard Zhang, Phillip Isola, Alexei A. Efros, Eli Shechtman, and Oliver Wang. The unreasonable effectiveness of deep features as a perceptual metric. In Proceedings of the IEEE conference on computer vision and pattern recognition, pages 586- 595, 2018.

[135] Tuo Zhang, Lei Gao, Sunwoo Lee, Mi Zhang, and Salman Avestimehr. Timelyfl: Heterogeneity- aware asynchronous federated learning with adaptive partial training. In Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition (CVPR) Workshops, pages 5064- 5073, June 2023.

[136] Zhaoyu Zhang, Changwei Luo, and Jun Yu. Towards the gradient vanishing, divergence mismatching and model collapse of generative adversarial nets. In Proceedings of the 28th ACM International Conference on Information and Knowledge Management, CIKM '19, page 2377- 2380, New York, NY, USA, 2019. Association for Computing Machinery. ISBN 9781450369763. doi: 10.1145/3357384.3358081. URL https://doi.org/10.1145/3357384.3358081.

[137] Siran Zhao, Tianchi Liao, Lele Fu, Chuan Chen, Jing Bian, and Zibin Zheng. Data- free knowledge distillation via generator- free data generation for non- iid federated learning. Neural Networks, 179: 106627, 2024. ISSN 0893- 6080. doi: https://doi.org/10.1016/j.neunet.2024.106627. URL https://www.sciencedirect.com/science/article/pii/S0893608024005513. [138] Yinlin Zhu, Xunkai Li, Zhengyu Wu, Di Wu, Miao Hu, and Rong- Hua Li. Fedtad: topology- aware data- free knowledge distillation for subgraph federated learning. arXiv preprint arXiv:2404.14061, 2024. [139] Zhuangli Zhu, Junyuan Hong, and Jiayu Zhou. Data- free knowledge distillation for heterogeneous federated learning. In Marina Meila and Tong Zhang, editors, Proceedings of the 38th International Conference on Machine Learning, volume 139 of Proceedings of Machine Learning Research, pages 12878- 12889. PMLR, 18- 24 Jul 2021. URL https://proceedings.mlr.press/v139/zhu21b.html.

# A Related Work

# A.1 Heterogeneous Federated Learning

Heterogeneous FL has emerged as a crucial field of study, primarily due to the diverse and decentralized nature of client environments and data distributions [22, 118, 19, 122]. Three key challenges arise in this setting: data heterogeneity, where clients hold Non- IID data that impedes convergence and generalization [36]; model heterogeneity, where clients differ in model architectures or computational capabilities [2]; and modality heterogeneity, where clients operate on distinct input modalities, such as images, text, or audio [61]. These challenges jointly exacerbate the difficulty of collaborative training and hinder the deployment of unified global models.

Some recent approaches have specifically targeted data heterogeneity and demonstrated notable improvements in performance. Wang et al. proposed FedAF, an aggregation- free FL framework that enables clients to collaboratively distill knowledge into condensed data representations, effectively mitigating client drift and improving robustness under severe label- skew conditions [110]. McLaughlin and Su introduced pFedFDA, which models representation learning as a generative task and adapts global generative classifiers to local feature distributions, thereby achieving personalized models that handle complex distribution shifts [68]. Huang and Liu developed PA3Fed, a period- aware adaptive aggregation strategy that identifies high- entropy training periods to selectively emphasize more informative updates, improving training stability in heterogeneous environments [34].

While these methods address data heterogeneity from various perspectives, HFL also faces increasing challenges due to discrepancies in model architectures across clients. Several foundational works have proposed distinct strategies to tackle model heterogeneity, among which PT and KD stand out as two representative paradigms. For instance, FedRolex [2] adopts a PT- based approach that allows clients to train rolling sub- models of a larger server model, ensuring architectural compatibility and broad device participation. In contrast, FedGen [139] adopts a KD- based approach, where a server- side generator synthesizes data representations to guide local training, mitigating both data and model heterogeneity. While PT and KD have shown strong potential individually, recent studies highlight that combining the two can yield more robust performance [64]. Building on this insight, our proposed method, Mosaic, extends the PT framework by incorporating KD- based fine- tuning, thereby enhancing model generalization and achieving SOTA performance across multiple benchmarks.

Further extending the scope of heterogeneity, recent methods have explored modality heterogeneity, where clients possess different or incomplete input modalities. Cui et al. proposed CLAP [11], which addresses patchwork multimodal learning by collaboratively adapting across clients with different modality combinations. Liu et al. introduced FedRecon [61], leveraging a lightweight MVAE for missing modality reconstruction while handling Non- IID data through distribution alignment and generator freezing. Although Mosaic primarily focuses on data and model heterogeneity, it can be naturally extended to multimodal settings. We present preliminary results in Appendix V, demonstrating Mosaic's scalability to modality- heterogeneous benchmarks.

# A.2 Data-Free Knowledge Distillation

A.2 Data-Free Knowledge DistillationIn contrast to traditional distillation methods that require access to original training data, DFKD facilitates knowledge transfer from teacher to student models by generating synthetic data, ensuring the protection of sensitive information [123, 89, 90]. Recent advancements in this domain have introduced innovative techniques aimed at improving the quality and efficiency of synthetic data generation. For instance, Yu et al. [124] employ channel-wise feature exchange and spatial activation region constraints to enhance data diversity, resulting in more robust student models without relying on real data. Similarly, Tran et al. [102] propose NAYER, a method that shifts the source of randomness to a noisy layer, paired with label-text embeddings to produce high-quality samples. Despite these innovations, DFKD still faces challenges in generating diverse, high-fidelity samples. Methods often struggle to capture the full distribution of the original data, especially in imbalanced scenarios, which can lead to biased student models [121, 111]. Nonetheless, DFKD continues to evolve, driven by the increasing demand for privacy-preserving techniques in machine learning, establishing itself as a rapidly advancing field.

# A.3Data-Free Knowledge Transfer in Federated Learning

A.3 Data-Free Knowledge Transfer in Federated LearningDFKD in FL offers a privacy-preserving solution for knowledge transfer, eliminating the need for raw data exchanges between clients [65, 16, 54]. By generating synthetic data for distillation, DFKD ensures sensitive information remains protected while facilitating effective knowledge transfer from global teacher models to local student models. Notably, methods such as FedGen [139], DENSE [131] and FedFTG [133] maintain a generator exclusively on the server, where the ensemble of client models serves as the teacher to guide its training. This design eliminates the need for generator aggregation and has demonstrated both effectiveness and scalability, motivating several subsequent methods that further explore this paradigm, including FedGIMP [17], DFRD [64], DFDG [65] and FedKFD [63]. In contrast, several methods adopt a client-side generator training strategy with subsequent aggregation on the server, such as FedCG [116], Fed2KD [95], FedCVALE-KD [29] and FedMD-CG [66]. While this design often leads to higher-quality synthetic data by leveraging client-specific distributions, it may suffer from distributional shift and catastrophic forgetting due to unstable generator aggregation. Mosaic follows the latter paradigm but mitigates its drawbacks by introducing a generator ensemble in place of a single global generator, effectively reducing instability. Additionally, a one-shot upload strategy is employed to limit communication overhead, making the design both efficient and robust.

# A.4One-shot Federated Learning

A.4 One-shot Federated LearningOne-shot FL significantly reduces communication costs by requiring only a single round of client-to-server communication. Instead of repeatedly transmitting full model updates or gradients, clients upload their trained models only once, enabling efficient global model construction with minimal communication overhead. Several representative methods have adopted this paradigm, such as DENSE and FedCAVE-KD. More recently, Zeng et al. proposed IntactOFL [126], a one-shot FL framework using a MoE network to preserve local knowledge without lossy aggregation, improving performance and heterogeneity tolerance. Tang et al. proposed FuseFL [101] introduces a block-wise progressive fusion scheme based on a causal perspective, addressing the isolation problem caused by data heterogeneity and achieving strong performance in one-shot settings. Mosaic similarly adopts a one-shot generator upload strategy where clients train and upload local generators once; the server ensembles these generators for distillation, reducing communication, avoiding unstable aggregation, and improving robustness and generalization.

# B Pseudocode

In this section, we present the pseudocode of Mosaic, as shown in Algorithm 1. To provide a clear understanding of the initialization phase, we also include the pseudocode of two standard FL baselines: FedAvg and FedRolex, shown in Algorithm 2 and Algorithm 3, respectively. These algorithms are used to obtain the initial global model  $f$  , which serves as the foundation for subsequent generator optimization, mdoel aggregation, and knowledge distillation in Mosaic.

Remark. In practical deployment, the meta model training and knowledge distillation are performed only once. Specifically, the total communication rounds  $T$  are divided into two stages: first,  $T_{1}$  rounds of local model updates are conducted; then, meta model training and knowledge distillation are carried out once on the server side. After that, an additional  $T_{2}$  rounds of local updates are performed with a reduced learning rate to fine- tune the models. As the meta training and distillation are one- time operations, the computational cost on the server remains low.

# C Comparison of Generator Storage and Communication Overheads

C Comparison of Generator Storage and Communication OverheadsWe conducted a comprehensive survey of open- source DFKD methods in which generator models are trained on clients and subsequently uploaded to the server, comparing the size of the generator architectures they adopt. To provide a broader reference, we also included several methods that, while not categorized as DFKD, still involve client- side training and uploading of generator models. All the conditional generators we compared are designed for ten- class classification tasks. Notably, for larger- scale datasets, the generator size would proportionally increase, leading to significantly higher communication overhead. As shown in Table 5, Mosaic achieves competitive storage and

# Algorithm 1 The pseudocode of Mosaic

Input:  $T$  : communication rounds;  $N$  : the number of clients;  $\{D_{i}\}_{i = 1}^{N}$  : local datasets;  $f$  : pretrained global model with parameters  $\theta$ $\eta_{\theta}$  : learning rate for updating  $\theta$  .  $G_{i}$  : generator of client  $i$ $\eta_G$  .. generator learning rate;  $\mathcal{F}$  : MoE model;  $M$  : meta model;  $\eta_{M}$  : learning rate for updating  $M;L_{1}$  .. generator training epochs;  $L_{2}$  : meta model training epochs;  $L_{3}$  : knowledge distillation epochs;  $q$  number of prototypes per client, where  $q\ll C$ $\lambda_{e}$ $\lambda_d$ $\lambda_{i}$  : coefficients for entropy, diversity, and inversion losses;  $\lambda_{\mathrm{hard}}$ $\lambda_{\mathrm{soft}}$  : weights for distillation targets.

Output: Final distilled global model:  $f$

# 1:Generator Optimization:

2:for each client  $i\in [N]$  in parallel do

3: Initialize generator  $G_{i}$  and local model  $f_{i}\gets f$

4: Freeze a copy of the global model:  $f_{i}^{\mathrm{freeze}} - f$

5: for  $t = 1$  to  $L_{1}$  do

6: Compute  $L_{\mathrm{adv}}$  using Eq. (3) with  $f_{i}$  as discriminator

7: Compute  $L_{\mathrm{entropy}}$ $L_{\mathrm{diversity}}$  , and  $L$  inversion using Eqs. (4)- (6) with  $f_{i}^{\mathrm{freeze}}$  as classifier

8:  $L_{G_i}\leftarrow L_{\mathrm{adv}} + \lambda_e\bar{L}_{\mathrm{entropy}} + \lambda_dL_{\mathrm{diversity}} - \lambda_iL_{\mathrm{inversion}}$

9:  $G_{i}\leftarrow G_{i} - \eta_{G}\nabla_{G_{i}}L_{G_{i}}$

10: end for

11: Extract  $y$  representative prototypes  $\{p_{i}^{(j)}\}_{j = 1}^{q}$

12: Upload  $G_{i}$  and  $\{p_{i}^{(j)}\}$  to the server

13:end for

14:for  $t = 0$  to  $T - 1$  do

15: Local Model Update:

16: for each client  $i\in [N]$  in parallel do

17:  $\theta_{i}\leftarrow \theta_{i} - \eta_{\theta_{i}}\nabla_{\theta_{i}}\mathcal{L}_{\mathrm{task}}(f_{i}(X_{i}),Y_{i})$

18: Upload updated model  $f_{i}$  to the server

19: end for

20: Model Aggregation:

21:  $\begin{array}{r}f\leftarrow \sum_{i = 1}^{N}\frac{|D_{i}|}{\sum_{k = 1}^{N}|D_{k}|} f_{i} \end{array}$

22: Construct  $\mathcal{F}$  from  $\{f_{i}\}_{i = 1}^{N}$  using Eq. (8)

23: Knowledge Distillation:

24: for  $i = 1$  to  $L_{2}$  do

25: for each batch of prototypes  $p$  do 26: // Top-  $k$  experts selected by gating network  $f$  27:  $M\leftarrow M - \eta_{M}\nabla_{M}\mathcal{L}_{\mathrm{meta}}(M(\mathcal{F}[f(p)]),y)$

28: end for

29: end for

30: for  $t = 1$  to  $L_{3}$  do

31: Sample latent vectors  $\{z_{i}\}_{i = 1}^{N}\sim \mathcal{Z} = \mathcal{N}(0,1)$  32: Generate synthetic batch:  $\{\hat{x_{i}}\}_{i = 1}^{N}\leftarrow \{G_{i}(z_{i})\}_{i = 1}^{N}$

33: for each synthetic sample  $x_{i}$  do

34: Compute  $\mathcal{L}_{\mathrm{KD}}$  using Eq. (14) 35:  $\theta \leftarrow \theta - \eta_{\theta}\nabla_{\theta}\mathcal{L}_{\mathrm{KD}}$

36: end for

37: end for

38:end for

# Algorithm 2 FedAvg

Input:  $\{D_i\}_{i = 1}^N$  : local datasets,  $T$  : communication rounds,  $E$  : local epochs Output: Global model parameters  $\theta$

1: Initialize global model parameters  $\theta^{(0)}$  2:for  $t = 0$  to  $T - 1$  do 3: Server samples a subset  $\mathcal{M}\subseteq [N]$  4: for each client  $i\in \mathcal{M}$  in parallel do 5:  $\theta_{i}\leftarrow \theta^{(t)}$  6: for  $e = 1$  to  $E$  do 7: Sample mini- batch  $(X_{i},Y_{i})\sim D_{i}$  8:  $\theta_{i}\leftarrow \theta_{i} - \eta_{i}\nabla_{\theta_{i}}\mathcal{L}_{\mathrm{task}}(\theta_{i};(X_{i},Y_{i}))$  9: end for 10: end for 11:  $\begin{array}{r}\theta^{(t + 1)}\leftarrow \sum_{i\in \mathcal{M}}\frac{|D_i|}{\sum_{k\in\mathcal{M}}|D_k|}\theta_i \end{array}$

12: end for

# Algorithm 3 FedRolex

Algorithm 3 FedRolexInput:  $\{D_i\}_{i = 1}^N$  : local datasets,  $T$  : communication rounds,  $\{\beta_{i}\}_{i = 1}^{N}$  : client- specific training budgets,  $S_{i}$  : subnetwork configuration determined by  $\beta_{i}$ Output: Global model parameters  $\theta$ 1: Initialize global model parameters  $\theta^{(0)}$ 2: for  $t = 0$  to  $T - 1$  do3: Server samples a subset  $\mathcal{M} \subseteq [N]$ 4: for each client  $i \in \mathcal{M}$  in parallel do5: Server sends subnetwork parameters  $\theta_{i, S_i^{(t)}}^{(t)}$  determined by  $\beta_{i}$  to client  $i$ 6: ClientStep  $(\theta_{i}^{(t)}, D_{i})$ 7: end for8: Server aggregates the received subnetwork updates  $\{\theta_{i, S_{i}^{(t)}}\}_{i \in \mathcal{M}}$  to update  $\theta^{(t + 1)}$ 9: end for10: Subroutine ClientStep  $(\theta_{i}^{(t)}, D_{i})$ 11: for  $k = 1$  to  $|D_{i}|$  do12:  $\theta_{i} \leftarrow \theta_{i} - \eta_{\theta_{i}} \nabla_{\theta_{i}} \mathcal{L}_{\mathrm{task}}(\theta_{i}; (X_{i}^{(k)}, Y_{i}^{(k)}))$ 13: return  $\theta_{i}$

communication efficiency with its lightweight model architecture. Its generator size is only slightly larger than that of FedCAVE, which can be attributed to our use of a GAN- based design, while FedCAVE employs a more compact VAE- style decoder as the generator [61]. Additionally, we provide a comparison between the generator size and the parameters of the global model  $f$ , showing that our generator is significantly smaller than the ResNet18 architecture adopted in our work.

# D Instability Induced by Generator Aggregation

We conducted experiments using unconditional generators trained with the Mosaic framework on CIFAR- 10 and CIFAR- 100 [48], resulting in  $N = 10$  client- specific generators for each dataset. We then evaluated the generation quality using two approaches: (1) an ensemble of the  $N$  generators, where samples are drawn uniformly from all local models, and (2) a global generator obtained by aggregating the parameters of the local models using FedAvg. All metrics are computed based on 5,000 generated samples. The code for quality evaluation is included in the supplementary material.

To empirically demonstrate the instability of aggregation, we first compute the Fréchet Inception Distance (FID) [30] to quantify the distributional discrepancy between generated samples and the real data. As shown in Table 6, the aggregated generator exhibits a significantly larger deviation from the real distribution compared to the ensemble of local generators. We further evaluate the overall generation quality using the Inception Score (IS) [85], which captures both fidelity and diversity at a

Table 5: Comparison of Generator Model Sizes in Various Methods. In the Method Type column, methods that are not based on federated learning are denoted as Non-FL, and those that are not categorized as DFKD are marked as Non-DFKD.  

<table><tr><td>Method Type</td><td>Methods</td><td>Parameters (M)</td><td>Size (MB)</td></tr><tr><td>FL, DFKD</td><td>FedDKD¹</td><td>23.21</td><td>88.57</td></tr><tr><td>FL, DFKD</td><td>FedCG [116]</td><td>3.79</td><td>14.46</td></tr><tr><td>FL, DFKD</td><td>FedCAVE [29]</td><td>2.67</td><td>10.22</td></tr><tr><td>FL, DFKD</td><td>Global Model f</td><td>11.18</td><td>42.66</td></tr><tr><td>FL, DFKD</td><td>Mosaic</td><td>3.57</td><td>13.65</td></tr><tr><td>Non-FL, DFKD</td><td>PRE-DFKD [3]</td><td>8.42</td><td>32.16</td></tr><tr><td>Non-FL, DFKD</td><td>TA-DFKD [93]</td><td>8.42</td><td>32.13</td></tr><tr><td>Non-FL, DFKD</td><td>NAYER [102]</td><td>6.48</td><td>24.76</td></tr><tr><td>FL, Non-DFKD</td><td>FedRecon [61]</td><td>4.43</td><td>17.72</td></tr><tr><td>FL, Non-DFKD</td><td>FedTPG [77]</td><td>21.05</td><td>80.33</td></tr><tr><td>FL, Non-DFKD</td><td>FedGAN [80]</td><td>3.64</td><td>13.89</td></tr></table>

https://github.com/colinlaganier/FedDKD

Table 6: Comparison of Aggregated Generator and Ensemble Generators on CIFAR-10 and CIFAR $100(\omega = 0.01)$  . For the ensemble setting, each client-specific generator contributes an equal number of samples to the evaluation, without weighting by client data size. Lower FID indicates better distributional alignment; higher IS, SS, and PD indicate better quality and diversity.  

<table><tr><td>Dataset</td><td>Method</td><td>FID (↓)</td><td>IS (↑)</td><td>SS (↑)</td><td>PD (↑)</td></tr><tr><td rowspan="2">CIFAR-10</td><td>Aggregated Generator</td><td>1939.19</td><td>1.37</td><td>NaN</td><td>11.03</td></tr><tr><td>Ensemble Generators</td><td>700.83</td><td>2.61</td><td>0.14</td><td>38.89</td></tr><tr><td rowspan="2">CIFAR-100</td><td>Aggregated Generator</td><td>2246.53</td><td>1.28</td><td>-0.04</td><td>7.25</td></tr><tr><td>Ensemble Generators</td><td>535.83</td><td>2.75</td><td>-0.13</td><td>42.05</td></tr></table>

coarse level. As discussed in the main text, the aggregated generator often suffers from catastrophic forgetting, leading to a substantial drop in sample diversity. To capture this effect more precisely, we additionally employ the Silhouette Score (SS) [83] and Pairwise Diversity (PD) [134], which offer a more comprehensive assessment of the diversity among generated samples. For SS, we utilize a ResNet- 18 [28] trained on the CIFAR- 10 and CIFAR- 100 training sets to obtain softmax- based pseudo- labels for the generated samples. Notably, for the CIFAR- 10 dataset, the ResNet classifier assigns all generated samples (from the aggregated generator) to a single class, causing the silhouette score (SS) to be recorded as NaN.

# E Collapse of Generators Trained on Limited Data Samples

We compare the visual quality and training loss trends of generators trained on clients with varying sample sizes on CIFAR- 10. Specifically, we select three representative clients (Client 1, Client 3, and Client 7), whose sample sizes are 689, 9999, and 9860, respectively.

We analyze five key training metrics to evaluate potential mode collapse:

Generator adversarial loss  $\mathcal{L}_{\mathrm{adv}}$  : Measures how successfully the generator can fool the discriminator. Lower values generally indicate stronger generators. Generator diversity loss  $\mathcal{L}_{\mathrm{diversity}}$  : Encourages the generator to produce samples covering a wide range of classes. Higher values suggest better class coverage. Generator accuracy (fake): The proportion of generated images that are misclassified as real by the discriminator. Higher is better for  $G$  Discriminator loss (all): Average loss over both real and fake samples. Used to evaluate the stability of the discriminator.

![](images/aba8ae850e62608336283a2a40e4da26a453a9eb344babdc7992a1aeb42b97fd.jpg)  
Figure 3: Training dynamics of generators on clients with different data sample sizes.

![](images/b9bf3e3f03292b292fe515fe0ee8a605786e16cff8d23e8898a7b7db49cbd29b.jpg)  
Figure 4: Samples generated by generators trained on different clients.

- Discriminator accuracy (fake): The accuracy of correctly identifying generated (fake) samples. Lower values may indicate that the generator is successfully deceiving  $f$ .

As shown in Figure 3, the generator training curves for client 1 (with only 689 samples) exhibit clear differences compared to clients 3 and 7 (with 9,999 and 9,860 samples, respectively). For the generator adversarial loss in subfigure (a), the losses for clients 3 and 7 initially decrease and then rise. This behavior reflects the discriminator's early instability—initially too weak to reject generated samples, leading to low loss, followed by improved performance as training progresses, causing the generator's adversarial loss to increase. In contrast, client 1 exhibits a different pattern: the adversarial loss first rises and then steadily declines. This is likely due to rapid overfitting of the discriminator, which occurs because of the extremely limited training data. In this scenario, the generator quickly collapses into producing a small set of samples that consistently fool the overfit discriminator, reaching a local optimum that prevents further meaningful updates. This interpretation is supported by subfigures (b)–(e): client 1 exhibits significantly lower diversity loss, indicating reduced variation in the generated samples. While its generator accuracy (fake) remains high, indicating that the discriminator frequently misclassifies fake samples as real, this does not reflect genuine generation quality. Instead, it suggests that the generator has collapsed to producing a small set of similar samples that consistently fool the discriminator by exploiting its overfitted decision boundary. We further visualize the outputs of the three generators in Figure 4. It can be observed that the generator from client 1 produces slightly less diverse samples than clients 3 and 7.

# F Generation Quality and Diversity Comparison of DFKD Methods

We computed and visualized the performance of different DFKD methods on CIFAR- 10. Quantitative results based on multiple evaluation metrics are summarized in Table 7. Specifically, IS, SS, and

PD follow the definitions provided in Appendix D. In addition, we introduce a new KD metric, where we adopt a more direct approach: a pretrained ResNet34 model (with  $77.26\%$  accuracy) serves as the teacher to distill knowledge into an untrained ResNet18 model. Intuitively, the better the quality of the generated samples, the more effective this straightforward distillation process becomes. Corresponding visual comparisons are shown in Figures 5a to 7b, and additional generation samples of Mosaic across all datasets are provided in Figures 13a to 17. These visualizations further highlight the differences in generation quality and diversity among the compared methods.

Table 7: Evaluation of Synthetic Data Quality and Diversity on CIFAR-10 with  $\omega = 0.01$  

<table><tr><td>Method</td><td>IS (↑)</td><td>SS (↑)</td><td>PD (↑)</td><td>KD (↑)</td></tr><tr><td>DENSE [131]</td><td>1.02</td><td>0.28</td><td>14.38</td><td>10.07 ±0.13</td></tr><tr><td>FedFTG [133]</td><td>1.03</td><td>0.99</td><td>18.67</td><td>9.72 ±0.62</td></tr><tr><td>DFRD [64]</td><td>1.05</td><td>0.08</td><td>29.28</td><td>14.47 ±3.19</td></tr><tr><td>FedKFD [63]</td><td>1.00</td><td>0.34</td><td>15.63</td><td>12.01 ±2.28</td></tr><tr><td>Mosaic</td><td>2.61</td><td>0.14</td><td>38.89</td><td>45.16 ±1.68</td></tr></table>

Mosaic significantly outperforms other methods in terms of IS, PD, and KD, but performs relatively poorly on the SS metric. This is primarily because the ensemble- generated samples from multiple generators do not form well- separated clusters; instead, they are more evenly and diffusely distributed. This, in fact, is a desirable property. First, it indicates that the pretrained ResNet classifier used for labeling fails to confidently distinguish between the generated samples, resulting in a high- entropy class distribution. Such a disordered synthetic dataset implies greater information content and randomness, making it harder to infer any specific client's data—thereby enhancing privacy. Second, the unordered distribution suggests that the generated samples themselves exhibit complex diversity rather than collapsing into narrow clusters. This diversity includes many hard- to- classify samples, which can enhance the effectiveness of knowledge distillation. Third, this indirectly shows that even when synthetic data leads to incorrect classifications, knowledge can still be effectively transferred. In such cases, both the teacher and the student may make similar errors, and this form of transferability can partially substitute the alignment achieved in traditional distillation paradigms.

![](images/88a95a106f875573abad8e772eb2050dadf310c96872d7ea038305ee9bb2f4f5.jpg)  
(a) Synthetic images generated by DENSE on CIFAR-10 with  $w = 0.01$ . 10 with  $w = 0.01$ . 10 with  $w = 0.01$ .  Figure 5

# G Privacy Concerns

We acknowledge that without dedicated privacy- preserving techniques, Mosaic cannot strictly guarantee user privacy in FL. Mosaic generates synthetic data on the server that is visually similar to the training data on clients, which poses potential risks of privacy leakage under FL regulations.

![](images/697307078424501ab3707b1f105b17f10db518f0018c830f8d9f5ba30ab521f2.jpg)  
Figure 6

![](images/e621747a11743fb10128c86d5bb73797f9da5f268856026670869ecf0793bc9c.jpg)  
(a) Visualization of synthetic images generated by (b) Visualization of synthetic images generated by a sinMosiac on CIFAR-10 with  $w = 0.01$  gle generator following the Mosaic framework, trained on  $\begin{array}{r}D = \sum_{i = 1}^{N}D_{i} \end{array}$  in CIFAR-10.  Figure 7

However, we observe that the quality of the synthetic images produced by Mosaic (Figure 7a) differs significantly from both real data samples (Figure 12a) and those generated by a single generator trained on the entire global dataset (Figure 7b). The images generated by Mosaic exhibit severe visual distortion, where it is difficult, even for a human observer, to identify specific objects or classes. This implies that although Mosaic captures a degree of client- level diversity, the synthetic outputs do not retain recoverable personal features from the raw data.

Interestingly, although Mosaic's generator shows high diversity—potentially indicating the inclusion of more personalized characteristics—this diversity is misleading and ambiguous. As analyzed in Appendix F, Mosaic's synthetic data tends to confuse classifiers trained on the corresponding original datasets. For example, pre- trained classifiers perform inconsistently when labeling Mosaic- generated samples, often assigning different labels to visually similar images. The SS are notably low, indicating a lack of cluster structure in the synthetic data. This suggests that the synthetic data generated by Mosaic significantly deviates from the original data in terms of image feature, resulting in strong visual ambiguity that naturally enhances privacy protection. Nevertheless, such diverse and ambiguous synthetic samples can still effectively expand the decision boundaries of the global model on the real test set, thereby enhancing its generalization capability.

We further evaluated this phenomenon using the MoE teacher model to classify the synthetic samples. Surprisingly, its predictions also demonstrated low SS values and unstable label distributions. This presents an interesting dynamic: while our goal is to generate samples for which the teacher is correct

and the student is wrong—thus providing effective training signal—in practice, many samples fall into the case where both teacher and student make errors. Despite this, we find that learning from such erroneous samples still improves the student's generalization on the real test set. In essence, learning consistently from incorrect samples in one domain can enhance correct performance in another, a form of beneficial transfer. Furthermore, among the synthetic data, we also observe the intended "target" samples—where the teacher is correct and the student is wrong—supporting the core goal of our approach. We summarize three types of samples found in our synthetic dataset: (1) Teacher correct, Student correct: limited effect; (2) Teacher correct, Student wrong: ideal case, partially present; (3) Teacher wrong, Student wrong: counterintuitively effective, significantly present. These samples not only diverge from the original data distribution but also possess confounding properties that enhance privacy.

In summary, we draw the following conclusions:

- Mosaic generates synthetic data that is visually and feature-wise distinct from real data, providing a natural safeguard for user privacy.- The high diversity in Mosaic's synthetic samples leads to confusion even for strong classifiers, thereby weakening their ability to make confident predictions. This ambiguity ensures that the synthetic samples cannot be easily traced back to specific classes or individuals, offering a layer of anonymity.- Despite their distortion and ambiguity, Mosaic's samples align with our learning objectives, including both the ideal case where the teacher is correct and the student is wrong, and the unexpected but beneficial case where both are wrong. These scenarios enhance the global model's generalization by improving its robustness through diverse and challenging training signals.

Together, these findings suggest that Mosaic achieves a practical balance between privacy protection and generalization capability, making it a valuable framework in privacy- sensitive federated learning scenarios.

In addition, Mosaic requires clients to upload local label distributions to assist in constructing the MoE model. This raises additional privacy concerns. However, we find that introducing a small degree of noise  $(5\% - 10\%)$  to the label counts—inspired by the label perturbation strategy in FedFTG—causes negligible performance drop (within  $2\%$ ), suggesting a practical trade- off between utility and privacy.

Although our method does not explicitly address privacy preservation, we acknowledge that Mosaic still carries the risk of privacy leakage due to the generation of synthetic data on the server. To mitigate this, Mosaic can be integrated with established privacy- enhancing techniques such as differential privacy (DP) [103, 112, 76] and secure multi- party computation [72, 6] or encryption protocols [128, 18]. Notably, both techniques are compatible with the Mosaic framework, which distinguishes it from other methods like DFRD that involve model distillation or generator training based on local model structures—making encryption impractical in those settings. In future work, we plan to explore formal privacy quantification and develop provable mechanisms to better safeguard client data within generative FL systems.

# H Performance of Ensemble Strategies under Varying Class Cardinality

Table 8: Comparison of different ensemble methods on CIFAR-10 dataset with  $\omega = 0.01$  across varying number of clients  $N$  

<table><tr><td>Methods</td><td>N=5</td><td>N=10</td><td>N=20</td><td>N=50</td><td>N=100</td></tr><tr><td>DENSE</td><td>34.50±2.17</td><td>24.95±3.32</td><td>23.90±3.82</td><td>23.34±2.91</td><td>21.55±3.68</td></tr><tr><td>F</td><td>51.57±1.78</td><td>43.41±3.98</td><td>26.43±5.54</td><td>27.43±2.00</td><td>23.73±1.42</td></tr><tr><td>+meta</td><td>55.26±1.06</td><td>53.33±1.10</td><td>40.26±3.79</td><td>44.04±2.20</td><td>45.64±1.69</td></tr></table>

To empirically investigate the impact of class cardinality and client number on ensemble performance, we conduct experiments on two benchmark datasets: CIFAR- 10 and CIFAR- 100. These datasets differ significantly in the number of classes, with CIFAR- 10 containing 10 classes and CIFAR- 100

Table 9: Comparison of different ensemble methods on CIFAR-100 dataset with  $\omega = 0.01$  across varying number of clients  $N$  

<table><tr><td>Methods</td><td>N=5</td><td>N=10</td><td>N=20</td><td>N=50</td><td>N=100</td></tr><tr><td>DENSE</td><td>51.02±0.93</td><td>44.73±0.79</td><td>45.03±2.09</td><td>35.43±0.97</td><td>34.06±1.78</td></tr><tr><td>F</td><td>53.44±0.61</td><td>49.52±1.41</td><td>51.51±2.86</td><td>37.99±1.83</td><td>38.75±2.65</td></tr><tr><td>meta</td><td>55.87±1.03</td><td>55.45±1.26</td><td>52.65±2.92</td><td>45.65±1.45</td><td>48.37±1.52</td></tr></table>

containing 100 classes, thus providing a suitable testbed for analyzing ensemble behavior under varying class cardinalities.

For each dataset, we evaluate the performance of ensemble strategies as the number of clients  $N$  varies. This allows us to observe how ensemble effectiveness shifts between regimes of large  $C$  (number of classes) relative to  $N$  , and large  $N$  relative to  $C$  , which correspond to distinct patterns of label distribution overlap among clients.

As shown in Table 8 and Table 9, the performance of different ensemble methods varies with the number of clients  $N$  .When  $N\ll C$  , the label distributions across clients are nearly disjoint, and the performance gap between local ensembling methods (e.g., DENSE) and class- wise aggregation is minimal, making local ensembling a viable substitute. However, when  $N\approx C$  or  $N\gg C$  , the label overlap across clients increases significantly, and class- wise aggregation methods (represented by  $\mathcal{F}$  and its meta- enhanced variant) outperform local ensembling by better leveraging the shared label information.

# I Proof: Meta model ensemble is more robust than uniform ensemble

Let the input space be  $X$  and the label space be  $Y = [C] = \{1,\ldots ,C\}$  . Consider an ensemble of  $C$  experts  $\mathcal{F} = \{f_k\}_{k = 1}^C$  , where each expert  $f_{c}\cdot X\to \mathbb{R}^{C}$  outputs a logit vector.

Let  $\hat{x}\in X$  be a given input and  $\mathcal{F}(\hat{x}) = \{f_1,\ldots ,f_k\} \subseteq \mathcal{F}$  be the subset of active experts.

# Ensemble Methods

Vanilla Ensemble (VE): Uniform averaging of expert logits:

$$
f_{\mathrm{ve}}(\hat{x}) = \frac{1}{k}\sum_{c = 1}^{k}f_{c}(\hat{x}). \tag{15}
$$

Meta- Enhanced Ensemble (ME): Weighted combination via a meta model  $M$  ..

$$
f_{\mathrm{me}}(\hat{x}) = \sum_{c = 1}^{k}\alpha_{c}(\hat{x})\cdot f_{c}(\hat{x}), \tag{16}
$$

where weights  $\alpha_{c}(\hat{x})\geq 0$  satisfy  $\textstyle \sum_{c = 1}^{k}\alpha_{c}(\hat{x}) = 1$  , and are learned to minimize predictive loss (e.g., cross- entropy).

# Assumptions

Assumption 1 (Error Decomposition) Each expert prediction can be decomposed as

$$
f_{c}(\hat{x}) = f^{*}(\hat{x}) + \epsilon_{c}(\hat{x}) + \delta_{c}(\hat{x}), \tag{17}
$$

where  $f^{*}(\hat{x})$  is the ideal predictor,  $\epsilon_{c}(\hat{x})\sim \mathcal{N}(0,\sigma_{c}^{2}I)$  represents zero- mean independent noise, and  $\delta_c(\hat{x})$  is the systematic bias vector. Assume uncorrelated noise:  $\mathrm{Cov}(\epsilon_i,\epsilon_j) = 0$  for  $i\neq j$

Assumption 2 (Meta model Capacity) The meta model  $M$  can approximate the optimal conditional weighting:

$$
\alpha_{c}(\hat{x})\approx \mathbb{E}\left[\alpha_{c}^{*}(\hat{x})|\hat{x}\right],\quad \mathrm{where~}\alpha_{c}^{*}(\hat{x})\propto \frac{1}{\sigma_{c}^{2} + \|\delta_{c}(\hat{x})\|^{2}}. \tag{18}
$$

This weighting corresponds to the mean squared error (MSE) optimal combination, balancing both variance  $\sigma_c^2$  and bias  $\| \delta_c(\hat{x})\| ^2$ . In our theoretical analysis (Theorem 1), we focus on an idealized setting where weights are chosen to minimize prediction variance only:

$$
\alpha_c^*\propto \frac{1}{\sigma_c^2}.
$$

This variance- only objective simplifies the analysis and yields a lower bound on the ensemble's prediction variance. Although it differs from the MSE- optimal weighting—which accounts for both variance and bias—both approaches share the principle of assigning greater weight to more reliable experts (those with lower variance or bias). Nonetheless, variance and MSE are not equivalent metrics, and minimizing variance alone does not necessarily minimize MSE. We adopt this idealized assumption primarily for analytical tractability and to gain theoretical insight into the benefits of meta- enhanced weighting.

# Theorem: Robustness Superiority of Meta-Enhanced Ensemble

Theorem 1.1. Under Assumptions 1 and 2, the meta- enhanced ensemble achieves lower expected prediction variance than the vanilla ensemble:

$$
\mathbb{E}_{\hat{x}}\left[\mathrm{Var}(f_{me}(\hat{x}))\right]< \mathbb{E}_{\hat{x}}\left[\mathrm{Var}(f_{ve}(\hat{x}))\right], \tag{19}
$$

with equality if and only if all experts are homogeneous, i.e.,  $\sigma_1^2 = \dots = \sigma_k^2$  and  $\delta_1 = \dots = \delta_k$

Proof. Vanilla Ensemble (VE) variance:

$$
\begin{array}{rl} & {\mathrm{Var}(f_{\mathrm{ve}}(\hat{x})) = \mathrm{Var}\left(\frac{1}{k}\sum_{c = 1}^{k}f_{c}(\hat{x})\right)}\\ & {\qquad = \frac{1}{k^{2}}\sum_{c = 1}^{k}\mathrm{Var}(f_{c}(\hat{x})) = \frac{1}{k^{2}}\sum_{c = 1}^{k}\sigma_{c}^{2}.} \end{array} \tag{21}
$$

Meta- Enhanced Ensemble (ME) variance with optimal weights:

$$
\alpha_{c}^{*} = \frac{1 / \sigma_{c}^{2}}{\sum_{j = 1}^{k}1 / \sigma_{j}^{2}},\quad \Rightarrow \quad \mathrm{Var}(f_{\mathrm{me}}(\hat{x})) = \sum_{c = 1}^{k}(\alpha_{c}^{*})^{2}\sigma_{c}^{2} = \frac{1}{\sum_{c = 1}^{k}1 / \sigma_{c}^{2}}. \tag{22}
$$

By the inequality between the harmonic and arithmetic mean:

$$
\frac{1}{\sum_{c = 1}^{k}1 / \sigma_{c}^{2}}\leq \frac{1}{k}\left(\frac{1}{k}\sum_{c = 1}^{k}\sigma_{c}^{2}\right), \tag{23}
$$

with equality if and only if all  $\sigma_c^2$  are equal.

# Lemma: Bias Bound of Meta-Enhanced Ensemble

Lemma I.2. When weights  $\alpha_{c}(\hat{x})$  downweight high- bias experts, the bias of ME satisfies:

$$
\mathbb{E}\left[\| \mathbb{E}[f_{me}(\hat{x})] - f^{*}(\hat{x})\|^{2}\right]\leq \max_{c}\| \delta_{c}(\hat{x})\|^{2}. \tag{24}
$$

Proof. By Jensen's inequality:

$$
\left\| \sum_{c = 1}^{k}\alpha_{c}(\hat{x})\delta_{c}(\hat{x})\right\|^{2}\leq \sum_{c = 1}^{k}\alpha_{c}(\hat{x})\| \delta_{c}(\hat{x})\|^{2}\leq \max_{c}\| \delta_{c}(\hat{x})\|^{2}. \tag{25}
$$

# Corollary: MSE Comparison

Corollary I.3. If  $\begin{array}{r}I f\sum_{c = 1}^{k}\alpha_{c}(\hat{x})\| \delta_{c}(\hat{x})\|^{2}\leq \left\| \frac{1}{k}\sum_{c = 1}^{k}\delta_{c}(\hat{x})\right\|^{2},t h e n \end{array}$  MSEme < MSEve.

Proof. MSE is the sum of variance and squared bias:

$$
\mathrm{MSE}(f) = \| \mathbb{E}[f] - f^{*}\|^{2} + \mathrm{Var}(f). \tag{27}
$$

By Theorem and Lemma above, both components are smaller for ME.

# Discussion: Theoretical vs. Practical Objectives

While our theory establishes that variance- optimal weights  $\alpha_{c}^{*}\propto 1 / \sigma_{c}^{2}$  minimize the ensemble's prediction variance (Theorem 1), in practice we optimize the meta model end- to- end using the crossentropy loss. On held- out validation samples, we measure the average trace of the logit covariance and observe a  $15\%$  reduction in variance compared to the vanilla ensemble. This empirical variance suppression, in turn, drives down MSE- since lower variance under a fixed bias bound directly improves mean- squared error. We also experimented with adding an explicit variance- regularization term to the loss; the resulting weight allocations and variance reductions closely match those obtained under pure cross- entropy training, indicating that classification training alone is sufficient to penalize high- variance experts.

In practice, this theoretical insight has been extensively validated in ensemble learning [5, 113, 86] We adopt this principle in our approach and empirically demonstrate its effectiveness with strong results.

# J Tricks for Meta Model Training

To train the meta model  $M$  , we first extract  $q$  representative prototypes  $\{p_{i}^{(j)}\}_{j = 1}^{q}$  from each client  $i$  Each prototype is passed through a frozen ensemble of expert classifiers  $\mathcal{F} = \{f_1,\ldots ,f_K\}$  , where each  $f_{k}$  is a linear classifier producing logits:

$$
z_{i}^{(j,k)} = f_{k}(p_{i}^{(j)}),\quad z_{i}^{(j,k)}\in \mathbb{R}^{C}. \tag{28}
$$

The outputs from all  $K$  experts are concatenated into a matrix:

$$
Z_{i}^{(j)} = [z_{i}^{(j,1)};z_{i}^{(j,2)},\ldots ;z_{i}^{(j,K)}]\in \mathbb{R}^{K\times C}, \tag{29}
$$

which is then reshaped into a vector in  $\mathbb{R}^{KC}$  for each sample.

To improve representation capacity, we first map each vector  $Z_{i}^{(j)}\in \mathbb{R}^{KC}$  to a higher- dimensional space via a linear projection:

$$
h_i^{(j)} = W_{\mathrm{expand}}Z_i^{(j)} + b_{\mathrm{expand}},\quad h_i^{(j)}\in \mathbb{R}^{KC}. \tag{30}
$$

Next, we apply a lightweight Transformer [105] to model the interaction between expert logits across class dimensions. Specifically, we reshape the batch of logits into a sequence and apply self- attention over the expert dimension. The output of the Transformer is:

$$
T_{i}^{(j)} = \mathrm{Transformer}(h_{i}^{(j)}), \tag{31}
$$

where attention is performed independently for each class across experts.

Finally, the Transformer output is aggregated across experts and projected back to class logits using a final linear layer:

$$
\hat{y}_{i}^{(j)} = W_{\mathrm{final}}T_{i}^{(j)} + b_{\mathrm{final}},\quad \hat{y}_{i}^{(j)}\in \mathbb{R}^{C}. \tag{32}
$$

To reduce overfitting, we maintain an exponential moving average (EMA) of all meta model parameters  $\theta$  . The EMA update rule at step  $t$  is:

$$
\theta_{\mathrm{EMA}}^{(t + 1)} = \beta \theta_{\mathrm{EMA}}^{(t)} + (1 - \beta)\theta^{(t + 1)}, \tag{33}
$$

where  $\beta \in [0,1)$  is the decay rate. During evaluation, the EMA version of the model is used.

Remarks on the Meta Model Design. Our meta model resembles a soft- gating network, but it is more fine- grained. While conventional soft- gating mechanisms may apply a single weight to the entire output of each expert, our meta model operates at the level of individual logits, allowing for more precise control over expert contributions. In addition, we incorporate a hard- gating mechanism, where the global model  $f$  itself serves as the gating network to determine which experts are activated. This hard gating is not essential and does not always lead to significant improvements, but it has shown to be effective on certain datasets such as CIFAR- 100, making it a viable optional strategy.

# K Datasets

In Table 10, we summarize the statistics of the datasets used in our experiments, including image size, number of classes, and the number of training and test samples. To ensure consistent input dimensions across datasets, we follow DFRD and use PyTorch's Resize() function to adjust the image sizes of MNIST, FMNIST, and FOOD101 where necessary. These datasets span a wide range of visual domains and complexities, from handwritten digits to general object recognition and fine- grained food classification. We provide example images from three representative datasets in Figures 12a to 12c.

Table 10: Statistics of the datasets used in our experiments.  

<table><tr><td>Dataset</td><td>Image size</td><td>class</td><td>train</td><td>test</td></tr><tr><td>MNIST</td><td>1×32×32</td><td>10</td><td>60000</td><td>10000</td></tr><tr><td>FMNIST</td><td>1×32×32</td><td>10</td><td>60000</td><td>10000</td></tr><tr><td>SVHN</td><td></td><td>10</td><td>50000</td><td>10000</td></tr><tr><td>CIFAR-10</td><td>3×32×32</td><td>10</td><td>50000</td><td>10000</td></tr><tr><td>CIFAR-100</td><td></td><td>100</td><td>73257</td><td>26032</td></tr><tr><td>Tiny-ImageNet</td><td></td><td>200</td><td>100000</td><td>10000</td></tr><tr><td>FOOD101</td><td>3×64×64</td><td>101</td><td>75750</td><td>25250</td></tr></table>

# L Illustrative Data Distribution Cases

We present data partitioning examples in Fig. 10 and 11 for six primary datasets used in our experiments: FMNIST, SVHN, CIFAR- 10, CIFAR- 100, Tiny- ImageNet, and FooD101. Our partitioning setup follows the configuration used in DFRD, and the sample images are directly sourced from that work.

# M Additional Experimental Results

In this subsection, we provide additional results on the MNIST dataset and comparative evaluations against FedProx [58], FedBN [59], FedAF [110], and pFedFDA [68] on CIFAR- 10 and CIFAR- 100. The MNIST dataset is relatively simple, and all methods perform similarly under the settings of  $\omega = 1.0$  and  $\omega = 0.1$ , with improvements over FedAvg being less than  $1\%$ . However, under the more heterogeneous setting  $\omega = 0.01$ , several methods demonstrate noticeable improvements. Therefore, we select the two best- performing methods under  $\omega = 0.01$ , along with FedAvg and Mosaic, for comparison. The results are presented in Table 11. The results of the additional baseline methods on CIFAR- 10 and CIFAR- 100, which are not discussed in the main text, are provided in Table 12.

We acknowledge certain limitations in our comparative experiments. For open- source implementations, we directly utilized the official repositories and applied our experimental settings, including datasets, partitioning schemes, model architectures, training epochs, and other hyperparameters. For methods without released code, we re- implemented them according to the descriptions in their original papers as faithfully as possible. However, exact replication is not always feasible. For example, our defined ResNet architecture may not be directly compatible with the original implementations of some methods, requiring substantial modifications to their codebases. Such adjustments could

introduce rough deviations from the intended behavior. Despite these factors, we have strived to conduct all comparisons in a fair and objective manner. Future iterations of our work may further refine and consolidate the evaluations of these methods.

Table 11: Top test accuracy  $(\%)$  of distinct methods across  $\omega \in \{0.01,0.1,1.0\}$  on MNIST datasets.  

<table><tr><td>Method</td><td>ω = 1.0</td><td>ω = 0.1</td><td>ω = 0.01</td></tr><tr><td>FedAvg [69]</td><td>99.20 ±0.02 (98.25±0.57)</td><td>97.55 ±0.22 (73.65±2.98)</td><td>79.43 ±0.73 (43.48±2.65)</td></tr><tr><td>FedOpt [81]</td><td>99.62 ±0.03 (99.01±0.19)</td><td>98.31 ±0.65 (73.27±2.41)</td><td>83.58 ±0.86 (47.98±2.74)</td></tr><tr><td>pFedFDA [68]</td><td>99.25 ±0.05 (98.30±0.31)</td><td>98.04 ±0.26 (74.66±1.75)</td><td>84.31 ±1.10 (48.06±2.10)</td></tr><tr><td>Mosaic</td><td>99.25 ±0.09 (99.08 ±0.52)</td><td>97.63 ±0.17 (96.31 ±0.90)</td><td>92.28 ±0.39 (84.87±1.91)</td></tr></table>

Table 12: Top test accuracy  $(\%)$  of distinct methods across  $\omega \in \{0.01,0.1,1.0\}$  on CIFAR-10 and CIFAR-100 datasets.  

<table><tr><td rowspan="2">Method</td><td colspan="3">CIFAR-10</td><td colspan="3">CIFAR-100</td></tr><tr><td>ω = 1.0</td><td>ω = 0.1</td><td>ω = 0.01</td><td>ω = 1.0</td><td>ω = 0.1</td><td>ω = 0.01</td></tr><tr><td>FedProx</td><td>79.45 ±1.57
(61.25 ±1.56)</td><td>56.73 ±1.87
(26.71 ±1.96)</td><td>37.32 ±2.57
(18.04 ±2.21)</td><td>65.54 ±0.66
(58.82 ±0.85)</td><td>59.64 ±0.65
(42.47 ±1.27)</td><td>49.73 ±1.14
(19.52 ±1.88)</td></tr><tr><td>FedBN</td><td>79.52 ±1.33
(62.22 ±1.72)</td><td>57.68 ±1.80
(27.85 ±1.81)</td><td>38.35 ±1.98
(18.40 ±2.36)</td><td>66.92 ±0.52
(58.50 ±0.52)</td><td>59.16 ±0.74
(43.44 ±0.62)</td><td>50.70 ±1.07
(19.83 ±0.98)</td></tr><tr><td>FedAF</td><td>79.81 ±1.95
(61.31 ±1.68)</td><td>58.69 ±2.17
(26.44 ±1.71)</td><td>40.98 ±2.23
(18.84 ±2.10)</td><td>65.39 ±0.77
(58.06 ±1.05)</td><td>60.28 ±0.84
(42.94 ±1.39)</td><td>50.97 ±1.65
(20.09 ±1.83)</td></tr><tr><td>pFedFDA</td><td>82.14 ±2.12
(64.92 ±2.60)</td><td>60.12 ±2.24
(30.64 ±2.88)</td><td>41.84 ±2.42
(21.56 ±2.46)</td><td>68.04 ±0.77
(59.31 ±1.11)</td><td>61.23 ±0.97
(43.46 ±1.45)</td><td>51.72 ±1.37
(20.61 ±1.70)</td></tr><tr><td>Mosaic</td><td>78.26 ±1.37
(78.38 ±1.71)</td><td>62.54 ±1.46
(59.77 ±1.77)</td><td>50.43 ±1.76
(47.95 ±1.32)</td><td>65.27 ±0.36
(65.68 ±0.38)</td><td>62.76 ±0.70
(57.93 ±0.76)</td><td>53.59 ±0.90
(43.92 ±0.99)</td></tr></table>

# N An Illustrative Case of Model Heterogeneity

Following the partition strategy proposed in the appendix of DFRD, we present a representative case of model heterogeneity to analyze its impact under controlled settings. To simulate heterogeneous model capacities among clients, we adopt an exponentially decaying budget formulation. For a total of  $N$  clients, each client's model width budget is defined as:

$$
R_{i} = \left[\frac{1}{2}\right]^{\min \{\sigma ,\lfloor \rho \cdot \frac{i}{N}\rfloor \}}(i\in [N]), \tag{34}
$$

where  $\sigma$  and  $\rho$  are positive integers. The parameter  $\sigma$  specifies the maximum level of width reduction allowed (i.e., the smallest model has width  $\left[\frac{1}{2}\right]^{\sigma}$  relative to the full model), while  $\rho$  controls the skewness of the distribution—higher values of  $\rho$  produce a larger proportion of clients with smaller capacities.

We provide examples under different  $\rho$  values with  $\sigma = 4$  and  $N = 10$  ..

$$
\begin{array}{rl} & {\bullet \rho = 5\colon \{R_i\} = \{1,\frac{1}{2},\frac{1}{2},\frac{1}{4},\frac{1}{4},\frac{1}{8},\frac{1}{8},\frac{1}{16},\frac{1}{16},\frac{1}{16}\} ;}\\ & {\bullet \rho = 10\colon \{R_i\} = \{\frac{1}{2},\frac{1}{4},\frac{1}{8},\frac{1}{16},\frac{1}{16},\frac{1}{16},\frac{1}{16},\frac{1}{16},\frac{1}{16},\frac{1}{16}\} ;}\\ & {\bullet \rho = 40\colon \{R_i\} = \{\frac{1}{16},\frac{1}{16},\frac{1}{16},\frac{1}{16},\frac{1}{16},\frac{1}{16},\frac{1}{16},\frac{1}{16},\frac{1}{16},\frac{1}{16}\} .} \end{array}
$$

Notably, when  $\rho \geq 4N$  , all clients receive the minimum capacity of  $\left[\frac{1}{2}\right]^{\sigma}$  - width. More distribution patterns and variants can be found in related works [15, 2, 64].

# O Additional Experimental Settings

We reproduced the model heterogeneity comparison experiments using the official DFRD code repository<sup>6</sup>. The repository is well maintained and includes implementations of methods such as FedRolex [2] and HeteroFL [15]. However, we observed some unusual behaviors in the model heterogeneity experiments. For example, on the CIFAR- 10 dataset, the performance at  $\rho = 10$  sometimes surpasses that at  $\rho = 40$ . While this can be partly attributed to randomness from different seeds, we are unable to fully replicate the exact results reported in their paper. Therefore, for comparison, we select the better reported results from their work.

We additionally supplement our experimental configurations to ensure consistency across datasets of varying complexity. Specifically, for relatively simple datasets—MNIST, FMNIST, SVHN, and CIFAR- 10—we set the synchronization interval to  $I = 10$ , meaning that each client performs 10 local updates per communication round. For more complex and large- scale datasets such as CIFAR- 100, FOOD101, and Tiny- ImageNet, we adopt a larger interval of  $I = 20$  to facilitate faster convergence and reduce the total number of communication rounds. The choice of synchronization interval  $I$  is known to influence convergence dynamics. As shown in Figure 8, increasing  $I$  often yields moderate gains in final accuracy across a wide range of methods, including both baselines and more advanced techniques. Larger values of  $I$  reduce communication frequency, thereby improving efficiency, though at the expense of increased local computation. Our configuration reflects a practical trade- off between these factors and is applied consistently throughout all evaluated methods.

To ensure fairness in evaluation and account for dataset complexity, we also adjust the total number of communication rounds for each dataset accordingly. Specifically, we train for 100 rounds on MNIST, FMNIST, and SVHN; 300 rounds on CIFAR- 10; 250 rounds on Tiny- ImageNet; 150 rounds on FOOD101; and 500 rounds on CIFAR- 100. These settings provide sufficient training time for each dataset while maintaining consistency in convergence analysis across methods and tasks.

![](images/5c403799a1ecbd3e6ade6e4e5b53d5e9b4022ee932c8444650db2d99ca831edf.jpg)  
Figure 8: Global model accuracy trends of FedAvg algorithm under varying synchronization intervals  $I$  on the CIFAR-10 dataset with data heterogeneity parameter  $\omega = 0.01$

# P Extended Experiments on Model Architectures

This is a very interesting experiment. Most PT methods assume model homogeneity with different capacities, but what if the models are completely heterogeneous? As defined in the Preliminary section, clients holding the same model can be aggregated together, while clients with different models are essentially isolated. In such scenarios, KD offers clear advantages over PT by enabling

learning across completely heterogeneous models. In our experiment, we randomly assign half of the clients to use a CNN [51] model and the other half to use a ResNet model. We then aggregate their models into a MoE using Mosaic, and perform KD to obtain a global CNN model and a global ResNet model, which are then distributed back to clients for further training. We observe that this approach significantly improves performance for both CNN and ResNet clients, with gains ranging from  $5\%$  to  $30\%$ . Such cross- architecture knowledge distillation is a common strength shared by all DFKD- based methods, including DENSE, FedFTG, DFRD, and FedKFD. It enables the collaborative training of fundamentally different model architectures while maintaining consistency in global knowledge transfer. Although this is only a preliminary exploration, it demonstrates the effectiveness and applicability of Mosaic under model heterogeneity. We plan to further investigate and extend this direction in future work.

# Q Learning Curves for  $\mathcal{L}_{\mathrm{inversion}}$  in Low-Data Clients

To better understand the effect of incorporating inversion loss for low- data clients, we visualize the learning curves of GAN ensembles under different configurations in Figure 9. These curves are generated on the CIFAR- 10 dataset under the highly heterogeneous setting of  $\omega = 0.01$ , with all selected GAN clients having fewer samples than the empirical threshold  $\tau = 1000$ . Each subfigure represents the ensemble performance of 1 to 4 selected GANs. We compare three scenarios: using inversion loss derived from a CIFAR- 100 pretrained model (blue), using inversion loss from the global model via forward inversion (red), and training without any inversion loss (green).

![](images/5ec653ab63135b741a2684ac29b39c00bc67c46f591e2901f2f4e9b697a0be3a.jpg)  
Figure 9: Accuracy curves on CIFAR-10 with  $\omega = 0.01$  for ensembles of low-data GAN clients. Blue: CIFAR-100 pretrained inversion loss; Red: global model forward inversion; Green: no inversion loss.

# R Additional Teacher Ensemble Results

R Additional Teacher Ensemble ResultsTo highlight the effectiveness of our MoE design, we selected in the main text a representative case without model heterogeneity  $(\rho = 0)$  on CIFAR- 10, as the performance differences in this scenario are particularly notable. Here, we provide additional comparisons under CIFAR- 10 with  $\omega = 0.1$ , considering different levels of model heterogeneity by varying  $\rho \in \{5,10,40\}$ . As shown in Table 13, these results further demonstrate the robustness and adaptability of our approach in increasingly heterogeneous settings. For simplicity, the table reports only the global model accuracy (G.acc) as the evaluation metric.

Table 13: MoE ensemble accuracy on CIFAR-10 under  $\omega = 0.1$  with varying  $\rho$  values.  

<table><tr><td>Method</td><td>ρ = 5</td><td>ρ = 10</td><td>ρ = 40</td></tr><tr><td>DENSE</td><td>25.72 ±4.21</td><td>23.18 ±3.97</td><td>16.92 ±5.70</td></tr><tr><td>F</td><td>30.55 ±3.55</td><td>25.92 ±3.20</td><td>27.21 ±3.96</td></tr><tr><td>+meta</td><td>33.01 ±3.24</td><td>29.77 ±3.33</td><td>33.78 ±2.78</td></tr></table>

# S Performance of Mosaic under Varying Numbers of Clients

S Performance of Mosaic under Varying Numbers of ClientsTo examine the robustness and scalability of federated methods, we evaluate FedAvg, DFRD, and Mosaic under varying client configurations. First, we vary the total number of clients  $N \in \{10,20,50,100,200\}$  to simulate different scales. Second, we fix  $N = 100$  and vary the number of active clients per round  $S \in \{5,10,50,100\}$  to assess the impact of partial participation. For simplicity, all reported metrics correspond to the global model accuracy, denoted as G.acc.

Table 14: Test accuracy  $(\%)$  with different total numbers of clients  $N$  on CIFAR-10.  

<table><tr><td>N</td><td>FedAvg</td><td>DFRD</td><td>Mosaic</td></tr><tr><td>10</td><td>36.76 ±2.45</td><td>39.78 ±2.41</td><td>50.43 ±1.76</td></tr><tr><td>20</td><td>29.08 ±1.50</td><td>26.45 ±2.34</td><td>37.11 ±4.54</td></tr><tr><td>50</td><td>27.36 ±1.57</td><td>29.88 ±1.99</td><td>38.45 ±1.93</td></tr><tr><td>100</td><td>28.01 ±3.73</td><td>30.84 ±3.11</td><td>35.72 ±3.62</td></tr><tr><td>200</td><td>24.82 ±3.71</td><td>25.98 ±5.32</td><td>30.30 ±4.79</td></tr></table>

Table 15: Test accuracy  $(\%)$  with different numbers of active clients  $S$  per round on CIFAR-10 (with total clients fixed at  $N = 100$  -  

<table><tr><td>S</td><td>FedAvg</td><td>DFRD</td><td>Mosaic</td></tr><tr><td>5</td><td>13.19 ±4.94</td><td>13.98 ±7.40</td><td>15.09 ±6.17</td></tr><tr><td>10</td><td>13.87 ±6.78</td><td>18.65 ±4.76</td><td>19.87 ±5.31</td></tr><tr><td>50</td><td>26.90 ±4.58</td><td>27.96 ±3.17</td><td>29.90 ±4.03</td></tr><tr><td>100</td><td>28.01 ±3.73</td><td>30.84 ±3.11</td><td>35.72 ±3.62</td></tr></table>

As shown in Table 14, increasing the number of clients  $N$  reduces the amount of data per client, generally leading to performance degradation. Interestingly, at  $N = 20$ , Mosaic exhibits an unstable drop in accuracy. This can be attributed to a fluctuation in the MoE teacher's performance, as reflected in Table 8. Such instability may stem from the specific way the dataset was partitioned (see our code for details). After  $N = 50$ , the MoE teacher becomes more stable and maintains relatively high accuracy, which in turn contributes to the improvement of Mosaic's performance at  $N = 50$ . However, when  $N$  becomes too large, the generator quality deteriorates due to extremely limited data per client, leading to a subsequent decline in accuracy.

Table 15 demonstrates that lower active client counts  $S$  induce larger accuracy fluctuations, likely caused by omission of clients with significant data during aggregation. Accuracy steadily improves with higher  $S$ , underscoring the importance of sufficient client participation. Mosaic's design, which

ensembles models weighted by client contribution and preserves client- trained GANs, provides resilience to participation variance and stabilizes performance.

# T Limitations of Using Synthetic Data for Meta Model Training

Training a meta model requires synthesized data with reliable labels. However, data generated by the GAN generator  $G$  often suffers from inferior quality compared to real data, posing significant challenges for accurate labeling. Relying on the global model  $f$  to provide labels for the meta model training risks making the MoE model's performance solely dependent on the global model itself, thereby limiting the potential of learning a highly effective teacher model. This creates an unreasonable paradigm where the student guides the teacher's training, and then the teacher in turn supervises the student; while some methods may adopt such an approach [91, 115, 120], it is not applicable within the Mosaic framework. On the other hand, enabling the MoE model to label the synthesized data in a self- supervised manner leads to convergence to undesirable local optima, further hindering performance. This highlights the primary limitation of using GAN- synthesized data: its inability to provide sufficiently accurate labels.

Moreover, as demonstrated in Table 6, the FID scores reveal a substantial gap in quality between GAN- synthesized data and real data, reflecting their status as two approximately homologous but inherently different datasets. Our empirical observations indicate that training the meta model on synthesized data significantly improves accuracy on the synthesized domain itself, but causes overfitting and degrades performance on real data. In contrast, using a small set of carefully selected prototypes for meta model training proves far more effective, striking a better balance between generalization and accuracy.

# U Learning Curves Across Training Epochs

We present the learning curves of FedAvg and Mosaic on both CIFAR- 10 and CIFAR- 100, illustrating how the models are trained and fine- tuned over communication rounds. These curves provide insights into the convergence behavior and stability of each method during training. All curves correspond to the case without model heterogeneity  $(\rho = 0)$ , and vary across different local update budgets  $\omega$ .

After obtaining the global model via FedAvg, we first perform a warm- up phase for 40 rounds, during which the global model is distributed to clients and used for local training. At the end of this phase (round 40), we train the meta model and conduct knowledge distillation. Subsequently, the distilled model is distributed again for another 40 rounds of fine- tuning with a reduced learning rate. This staged training strategy explains the sharp increase in Mosaic's performance observed around round 40, which is attributed to the effect of knowledge distillation. The full set of learning curves—spanning from Figure 18a to Figure 23b—demonstrates these dynamics in detail across different datasets and data heterogeneity settings.

# V Extension to Multimodal Benchmarks

To demonstrate the generality of our approach, we extend the Mosaic framework to multimodal settings and conduct preliminary experiments on the FedMultimodal [20] benchmark. Specifically, we focus on datasets within the benchmark that contain image modalities and restrict our exploration to the image generation component of Mosaic, where the GAN is trained solely on visual data.

Despite this limited scope, our results show that Mosaic can effectively enhance baseline methods under severe data heterogeneity, even in the presence of multiple modalities. This suggests that synthetic visual data alone can help mitigate distribution shifts and improve overall performance when integrated into multimodal federated learning systems. A promising direction for future research is to explore how Mosaic's DFKD framework can be adapted to support knowledge distillation across multiple modalities in federated learning.

![](images/55d68883edd352090a948fdf01be6b4a74215c2a782147c9a155b124a4f233b3.jpg)  
Figure 10: Data heterogeneity among clients is visualized on three datasets (FMNIST, SVHN, CIFAR-10), where the  $x$ -axis represents the clients id, the  $y$ -axis represents the class labels on the training set, and the size of scattered points represents the number of training samples with available labels for that client.

![](images/d897ff388c99b3062ce33a74f61bdb9d04e3c0e806fc9f9f27e3f1b0e7818a81.jpg)  
Figure 11: Data heterogeneity among clients is visualized on three datasets (CIFAR-100, TinyImageNet and FOOD101), where the  $x$ -axis represents the clients id, the  $y$ -axis represents the class labels on the training set, and the size of scattered points represents the number of training samples with available labels for that client.

![](images/7d12188b9921ec27fe0fa5a33092afd72ab90f730e70f5a964971f8f810c48fa.jpg)  
Figure 12

![](images/66025ddcf61dec68ebe9d4454b010dad501acd813a8eadf31255a7635f8fd918.jpg)  
Figure 13

![](images/7f826d2c54ee7ddb339a5515d5ec24b4f6edeb21f007be9391b1b64b9eea4740.jpg)  
Figure 14

(a) Visualization of synthetic images generated by (b) Visualization of synthetic images generated by Mosiac on CIFAR-10 with  $w = 0.01$ . Mosiac on CIFAR-10 with  $w = 0.1$ .

![](images/512e5fbab6fb612e55253387f2ce0ce40a505e9e4d89f075cba81a342b26a70e.jpg)  
Figure 15

![](images/b1626b9e9be85238cbb735a50afc8a4a711f0c13451f48b6dd7d8c87f7d0a0e9.jpg)  
Figure 16

(a) Visualization of synthetic images generated by (b) Visualization of synthetic images generated by Mosiac on CIFAR-100 with  $w = 0.1$ . Mosiac on CIFAR-100 with  $w = 0.01$ .

![](images/3bc9622cf993dd59e8ed2381f8396ce45d3fa81a1457a72956989c1bdb30adbf.jpg)  
Figure 17: Visualization of synthetic images generated by Mosaic on Tiny-ImageNet with  $w = 0.01$ .

![](images/82aa310b7f7395ea63b4e32a96042547f725aede84962fcf15d69f119afa5447.jpg)  
(a) Learning curve of FedAvg on CIFAR-10 with  $\omega = 1.0$ .

![](images/73bcdb99c802cc82f5bc7373ff962551951a737cb9a061a73e3092cade08f82b.jpg)  
Figure 18

![](images/74abc0bfcb90cffda21cf4bce39f2287a09b8a820f60c79ba5439be6b3fbd312.jpg)  
(a) Learning curve of FedAvg on CIFAR-10 with  $\omega = 0.01$ .

![](images/419c464168af0ec85b181094708c1c152959c687dd2b20dffeccde88dbe72bf5.jpg)  
Figure 19

(b) Learning curve of FedAvg on CIFAR-100 with  $\omega = 1.0$ .

![](images/fcede04b2b3ad3ab2b6ec69003284b6e88d713cab8b8595517b58ed1c08ed629.jpg)  
Figure 20

![](images/f4056c3015daadb48159cce5f0ee03b45154d26360131fed54806b931127a02c.jpg)  
Figure 21

![](images/f3ac22e6d64a346c6be8e2fa53437e462b9e5366edf2805f8d7716cce9b17556.jpg)  
Figure 22

![](images/8e38da837f563cd481f28d61cffa47351896e584a26b61cc55d2829cb79f6276.jpg)  
Figure 23