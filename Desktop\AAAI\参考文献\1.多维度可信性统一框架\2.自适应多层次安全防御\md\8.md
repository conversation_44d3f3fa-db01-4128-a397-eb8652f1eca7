# Centroid Approximation for Byzantine-Tolerant Federated Learning

Melanie Cambus $^{1}$ , <PERSON><PERSON> $^{2}$ , <PERSON><PERSON><PERSON> $^{2}$ , <PERSON> $^{2}$

# Abstract

AbstractFederated learning allows each client to keep its data locally when training machine learning models in a distributed setting. Significant recent research established the requirements that the input must satisfy in order to guarantee convergence of the training loop. This line of work uses averaging as the aggregation rule for the training models. In particular, we are interested in whether federated learning is robust to Byzantine behavior, and observe and investigate a tradeoff between the average/centroid and the validity conditions from distributed computing. We show that the various validity conditions alone do not guarantee a good approximation of the average. Furthermore, we show that reaching good approximation does not give good results in experimental settings due to possible Byzantine outliers. Our main contribution is the first lower bound of  $\min \{\frac{n - t}{t}, \sqrt{d}\}$  on the centroid approximation under box validity that is often considered in the literature, where  $n$  is the number of clients,  $t$  the upper bound on the number of Byzantine faults, and  $d$  is the dimension of the machine learning model. We complement this lower bound by an upper bound of  $2 \min \{n, \sqrt{d}\}$ , by providing a new analysis for the case  $n < d$ . In addition, we present a new algorithm that achieves a  $\sqrt{2d}$ - approximation under convex validity, which also proves that the existing lower bound in the literature is tight. We show that all presented bounds can also be achieved in the distributed peer- to- peer setting. We complement our analytical results with empirical evaluations in federated stochastic gradient descent and federated averaging settings.

# 1 Introduction

Federated learning [36, 37] is a decentralized technique for training machine learning models based on sharing model parameters while keeping the training data locally. In this work, we are particularly interested in the setting where the clients share updates - namely either the gradients in case of federated stochastic gradient descent (FedSGD) or the model parameters in case of federated averaging (FedAvg) - with a trusted central server. After the server has received the updates, it aggregates the results, updates the model parameters, and then shares the new model parameters with the clients for the next training round. This technique is popular when data privacy requirements prevent clients from sharing their data directly with the server [1, 29, 55]. The most common aggregation rule used to select a representative vector (gradient or model parameters) is averaging [26, 27, 30, 34, 36, 37, 40, 42, 47, 57]. However, when averaging is used, training can fail if some clients do not behave as expected. In particular, a single faulty vector can arbitrarily shift the average in any direction, leading to erroneous updates of the model parameters. Especially in the context of federated learning, it is crucial to be robust to malicious behavior and Byzantine faults, which is also the focus of our paper. In the case of homogeneous training data, it is usually possible to use similarities between vectors to exclude such outliers [18, 20, 53]. If the data is heterogeneous, such similarities may not exist.

Previously proposed Byzantine- tolerant FL methods for heterogeneous datasets focus on showing convergence of the training process and apply statistical methods for vector aggregation [14, 24, 31]. To mitigate Byzantine behavior, their methods remove outliers from the data and make additional assumptions on the input vectors of the clients. An alternative approach proposed in the literature is to use the absolute distance to the average to evaluate

federated learning algorithms [19]. This absolute measure, however, only allows one to analyze the worst- case input setting. The authors showed that the aggregated average can be as bad as the distance between the two furthest input vectors. Recently, a new approximation measure was introduced to estimate the quality of an aggregated average in a Byzantine environment [9] for approximate agreement algorithms. This approximation measure allows one to not only analyze the worst- case input setting, but rather estimate the quality of an algorithm based on the given input distribution. .

In this work, we transfer the idea of approximating the average vector to the traditional FL setting with  $n$  clients and one trusted server. In distributed computing, validity conditions are used to restrict an algorithm from terminating on arbitrary inputs. We investigate the tradeoff between the validity conditions and the approximation of the average vector for federated learning. This allows us to present aggregation algorithms that perform well under different input distributions.

# 1.1 The benefits of average approximation

In this paper, we consider approximation of the average to evaluate the quality of our algorithms. As we motivate in the following, a low average approximation ratio implies that an algorithm performs well for a given input distribution. Formally, given  $n$  vectors, up to  $t$  of which can be Byzantine, an optimal choice of the average vector under Byzantine attacks is defined as the midpoint of the smallest ball, denoted  $B$ , that encloses each average obtained from every subset of  $n - t$  vectors. When  $t$  clients are Byzantine, exactly one of these averages was computed from only non- faulty vectors. Therefore, the midpoint minimizes the maximum distance to the non- faulty average vector in the worst case. The approximation ratio is then defined as the ratio between the distance from the aggregation vector to the non- faulty average, and the radius of  $B$ .

The main advantage of this approximation ratio is that it is defined relative to the input setting: In scenarios with heterogeneous training data, Byzantine vectors cannot be differentiated from non- faulty vectors. That is, a large radius of the minimum covering ball either represents "bad" Byzantine behavior, or a "bad" initial configuration where each client has vastly different input. In such a scenario, no aggregation algorithm can choose a representative average vector. The large ball radius prevents one from punishing an algorithm for a large absolute distance to the average vector. A small radius, on the other hand, represents "benign" Byzantine behavior and very similar inputs. In such a scenario, an aggregation algorithm should be able to choose an aggregation vector that is close to the original average. Figure 1 visualizes the continuous change in the ball radius depending on the input vectors of the clients.

# 1.2 Contributions

We first show that known validity conditions from the literature do not guarantee good approximation of the average. We then show that under weak and strong validity conditions, both of which only require the server to output the same vector as the non- faulty client if all non- faulty clients send the server the same vector, a constant approximation of the average can be achieved.

Our first main contribution is almost tight bounds for algorithms that satisfy box validity, where the aggregation vector lies in the coordinate- parallel hyperbox of non- faulty vectors. We present a lower bound of  $\min \{\sqrt{(n - t) / t}, \sqrt{d}\}$  for the centroid approximation and show that the existing Box algorithm can achieve an approximation of  $2\sqrt{\min \{n, d\}}$  by providing a new upper bound proof for the case  $n < d$ . Our second main contribution is a tight upper bound (a  $2d$ - approximation) for convex validity, where the aggregation vector lies in the convex hull of all vectors. Note that this setting is only of theoretical interest, as it requires the number of clients to be larger than the dimension of their input vectors ( $n > (d + 1) \cdot t$ ), while in FL the dimension of the data is usually much larger than the number of clients. We show that all

![](images/f6931c2cab00dbdc8bfc20cf9579cf9ea27de3d317a093766b14439cd7bb0843.jpg)  
Figure 1: Illustration of the benefits of the approximation definition that is relative to the input distribution. This figure shows how the radius of the smallest ball containing all averages depends on different distributions of the inputs. There are 6 clients, one of which is possibly Byzantine. On the left, three input scenarios are considered. The points represent input vectors that are fixed in all scenarios. The three stars represent three different inputs of the sixth client. The circles represent the three smallest balls containing all possible averages on subsets of five points, one for each input of the last client. On the right, the radius of the minimum covering ball is presented when the  $x$ -coordinate of the last client is moved from  $-4$  to  $0$ . Observe that the radius of the minimum covering ball cannot be zero, as any of the points in the figure are also potentially Byzantine. The yellow star represents a "bad" input setting where it is not clear whether there is one non-faulty client with very distinct data, or whether a Byzantine party tries to disrupt the training process. The dark red star shows a benign setting where an aggregation algorithm should be able to output a vector close to the actual centroid. The radius of the ball used to define the approximation ratio decreases with more benign input distributions.

presented bounds can also be achieved in the distributed peer- to- peer setting. The agreement algorithms presented differ from [9, 19], since only exact agreement is considered in this paper.

Finally, we extend our analytical results with simulations. In this evaluation, we differentiate between the settings where the gradients (FedSGD) and the model parameters (FedAvg) are aggregated, and show how selected algorithms perform under different failure scenarios.

# 1.3 Related Work

Dean et al. [15] proposed a first distributed solution to train a large machine learning model on tens of thousands of CPU cores. Their work initiated a study of asynchronous algorithms for distributed stochastic gradient descent (SGD) that focus on scalability and communication efficiency [32, 33, 43, 56]. The synchronous version of SGD has been proposed by Chen et al. [10]. We refer to this framework as FedSGD. FedSGD has also been considered under Byzantine adversaries, both in synchronous [3, 17] and asynchronous settings [13]. While the mentioned work assumes homogeneous data distributions, some efforts have also been made to incorporate data heterogeneity [14, 24, 31, 51]. To tackle Byzantine behavior of the clients, these approaches make use of homogeneity of the data, apply statistical methods, or try to detect Byzantine behavior.

Federated averaging was introduced by McMahan et al. [36, 37] to perform training where the data is private, unbalanced, non- IID, and distributed across mobile devices. Here, model parameters instead of gradients are exchanged with a server. We refer to this framework as FedAvg in this paper. Much of the follow- up work has focused on showing convergence of the models in this framework without failures [25- 27, 40, 42, 47]. Byzantine- tolerant approaches have been introduced also for this setting, where the goal is to remove Byzantine behavior via stochastic quantization and outlier detection mechanisms [46].

In contrast to previous work, we do not focus on removing Byzantine clients from the training process, as such a process may influence the accuracy when the data is heterogeneous and no malicious behavior is present in the system. Instead, we use the approximation definition for the average from Cambus and Melnyk [9] that naturally incorporates Byzantine clients. In contrast to [9], we consider a stronger model without agreement, which makes our lower bound results more powerful, and introduce new algorithms that achieve an optimal approximation.

# 2 Model and Definitions

We consider a client/server setting with one server and  $n$  clients. The goal is to train a global neural network on the server with data spread heterogeneously among clients. In order to train the global model without gathering data from clients, each client possesses its own copy of the model and then shares only vectors generated from their local data and model with the server. The server then needs to aggregate the received vectors to advance the training of the global model. The training process is performed in synchronous rounds.

On top of the training set- up, we consider that up to  $t< n / 3$  of the clients can be Byzantine, i.e., they can behave arbitrarily and are not bound to following the protocol. The aggregation algorithms used by the server hence need to account for this. Note that we use the standard assumption from distributed computing that Byzantine clients are not differentiable from nonfaulty clients as long as they follow the protocol and only lie about their input. We treat all clients equally, assuming no size difference in the local data, in order to restrict the power of the Byzantine clients.

The focus of this work is on the aggregation function. Consider a specific communication round, in which each client sends a vector to the server, and the server aggregates those vectors. To account for the potential presence of Byzantine clients in the system, the aggregation algorithm used by the server needs to compute an aggregation that is as little influenced by Byzantine vectors as possible. In this work, we focus on the most common aggregation rule in FL - the averaging aggregation rule. Since Byzantine clients can be present in the system and are undetectable, it is impossible for an aggregation algorithm to determine the centroid of vectors of non- faulty clients. We are therefore interested in the quality of the computed aggregated vector.

# 2.1 Centroid approximation

We assume that the server receives up to  $m$  vectors  $\{v_{i},i\in [m]\}$  , where  $n - t\leq m\leq n$  . Each vector  $v$  is in the normed vector space  $\left(\mathbb{R}^d,\| \cdot \| _2\right)$  , where  $\forall x = (x_{1},\ldots ,x_{d})\in \mathbb{R}^{d}$ $\begin{array}{r}\| x\| _2 = \sqrt{\sum_{k = 1}^{d}x_k^2} \end{array}$  and the distance between any two vectors  $\mathcal{U}$  and  $w$  is their Euclidean distance  $\mathrm{dist}(v,w) =$ $\| v - w\| _2$  . When not specified,  $\| \cdot \|$  refers to the 2- norm. We use the following definition of the average/centroid:

Definition 2.1 (Centroid). The centroid of a finite set of  $k$  vectors  $\{v_{i},i\in [k]\}$  is  $\begin{array}{r}\frac{1}{k}\sum_{i = 1}^{k}v_{i} \end{array}$

We define the centroid approximation as in [9]. Let Cent  $\star$  be the centroid computed from non- faulty vectors only. Note that there can be up to  $n$  non- faulty vectors as  $t$  is only an upper bound on the number of Byzantine clients. In the following, we define the set of candidate centroids, which are computed based on the worst case where exactly  $t$  vectors are Byzantine.

Definition 2.2 (Set of candidate centroids). The set  $\mathrm{S_{Cent}}$  containing all centroids of  $n - t$  input vectors is defined as

$$
\mathrm{S}_{\mathrm{Cent}}\coloneqq \left\{\frac{1}{n - t}\sum_{i\in I}v_{i}\big|\forall I\in [n].t.w.|I| = n - t\right\} .
$$

Due to the assumption that Byzantine clients are not differentiable from non- faulty clients as long as they follow the protocol, we can only define the centroid approximation based on the worst case where exactly  $t$  clients are Byzantine. We define the point minimizing the maximum distance to all vectors in the set of candidate centroids defined above is the center of the following ball:

Definition 2.3 (Minimum covering ball). The minimum covering ball  $\mathrm{Ball}_{\mathrm{cov}}(\mathrm{S}_{\mathrm{Cent}})$  is the smallest ball containing all vectors in  $\mathrm{S_{Cent}}$ . Its radius is denoted  $\mathrm{Rad}_{\mathrm{cov}}$ .Finally, the centroid approximation is defined as follows:

Finally, the centroid approximation is defined as follows:

Definition 2.4 (Centroid approximation). Given an input layout  $L = \{v_{i},i\in [n]\}$  , let  $O_{A}$  be the output of an algorithm  $\mathcal{A}$  computing an approximation of the centroid of non- faulty vectors. The approximation ratio of  $\mathcal{A}$  given  $L$  is the smallest  $\alpha$  s.t.

$$
\mathrm{dist}(O_A,\mathrm{Cent}^\star)\leq \alpha \cdot \mathrm{Rad}_{\mathrm{cov}}.
$$

The algorithm  $\mathcal{A}$  is said to compute an  $\alpha$  - approximation of the centroid if, for all input layout  $L$  the approximation ratio of  $\mathcal{A}$  given  $L$  is upper bounded by  $\alpha$

In order to compute the approximation ratio of a certain type of algorithms, we need to consider a less restrictive area than the minimum covering ball:

Definition 2.5 (Centroid hyperbox). The centroid hyperbox CH is the smallest coordinate- parallel hyperbox containing SCent.

# 2.2 Validity conditions

We noted above that a Byzantine client can shift the centroid of vectors of all clients arbitrarily, and thus it can also shift the midpoint of the minimum covering ball arbitrarily far away from Cent\*. Just choosing the center as the centroid approximation might not be sufficient to ensure that we can trust the output of a certain algorithm. We hence take inspiration from the distributed agreement algorithms and use validity conditions to get additional guarantees on the output of different algorithms, complementing the guarantees given by the centroid approximation ratio.

A validity condition is satisfied when the output of an algorithm is guaranteed to be in a specific area, depending only on the input layout. In this work, we focus on common validity conditions from the literature:

Definition 2.6 (Validity conditions). Given are n vectors, up to t of which are Byzantine. An algorithm  $\mathcal{A}$  satisfies

weak validity [11, 12, 54] if, when all clients are non- faulty and all input vectors  $v_{i}$  are equal to a single vector  $v$  , the output of  $\mathcal{A}$  is  $v$  .

strong validity [4, 7, 8] if, when all non- faulty input vectors  $v_{i}$  are equal to a single vector  $v$  , the output of  $\mathcal{A}$  is  $v$  .

box validity [9, 16, 38] if the output of  $\mathcal{A}$  is inside the smallest coordinate- parallel hyperbox containing all non- faulty input vectors (Notation 1);

convex validity [2, 39, 48] if the output of  $\mathcal{A}$  is inside the convex hull of all non- faulty input vectors.

Notation 1. The smallest coordinate- parallel hyperbox containing only non- faulty vectors is called the trusted hyperbox and denoted TH.

Note that the trusted hyperbox cannot be computed in practice. However, we prove in Section 3 (Lemma 3.10) that an algorithm satisfies the box validity condition if and only if it agrees inside a hyperbox called the trimmed trusted hyperbox (TTH):

Definition 2.7 (Trimmed trusted hyperbox). Let  $v_{1},\ldots ,v_{m}$  be the received input vectors, where  $m$  is the number of received messages. The number of Byzantine values for each coordinate is at most  $m - (n - t)$  . Denote  $\phi :[m]\to [m]$  a bijection s.t.  $v_{\phi (j_{1})}[k]\leq v_{\phi (j_{2})}[k],\forall j_{1},j_{2}\in [m]$  . The trimmed trusted hyperbox is the Cartesian product of  $TTH[k]\coloneqq [v_{\phi (n - (n - t) + 1)}[k],v_{\phi (n - t)}[k]]$  for all  $k\in [d]$  .

In a similar manner, it is proved in [9] that, in order to satisfy the convex validity condition, an algorithm must agree inside the following area:

Definition 2.8 (Safe area [39]). Consider n vectors  $\{v_{1},\ldots ,v_{n}\} = V$  ,  $t< n / (\max \{3,d + 1\})$  of which can be Byzantine. Let  $C_{1},\ldots ,C_{(n - t)}$  be the convex hulls of every subset of  $V$  of size  $n - t$  . The safe area is the intersection of these convex hulls:  $\cap_{i\in [(n_{- t})]}C_{i}$  .

# 3 Centroid approximation in Byzantine federated learning

3 Centroid approximation in Byzantine federated learningIn this section, we first consider approximation guarantees that are given by validity conditions only. We show that only the box validity condition guarantees a bounded approximation ratio of the Cent*. In the second part, we consider the best possible approximation that can be achieved under various validity conditions. We provide tight approximation bounds for each validity condition, apart from the box validity condition, where a gap remains for some specific values of  $n$  and  $d$ . We conclude this section with a discussion on how our results can be transferred to federated learning in a peer- to- peer network.

# 3.1 Approximation guarantees given by validity conditions

In this section, we show that weak, strong, and convex validity conditions are not sufficient to guarantee that an algorithm achieves a bounded approximation ratio of Cent\*.

Lemma 3.1. Satisfying weak validity is not a sufficient condition for an algorithm to achieve a bounded approximation ratio of Cent\*.

Proof. Without loss of generality, we can consider an algorithm that either agrees on the unique input vector, or outputs the origin. Now consider the case where all clients have input  $x\cdot (1,\ldots ,1)$  Then, the diameter of the minimum covering ball can be arbitrarily small, but the distance between the origin and  $x\cdot (1,\ldots ,1)$  is  $\sqrt{d}\cdot x$  . Hence, the ratio between this distance and the radius of the minimum covering ball is unbounded.

Lemma 3.2. Satisfying strong validity is not a sufficient condition for an algorithm to achieve a bounded approximation ratio of Cent\*.

Proof. As before, we can consider an algorithm that either agrees on the unique non- faulty input vector, or outputs the origin (we do not need to know how the algorithm achieves this, only that it is a general algorithm satisfying strong validity). Assume the case, where the  $n - t$  non- faulty input vectors are all  $\epsilon$  away from  $(1,\ldots ,1)$  , and the Byzantine clients do not send any vector. The distance between the origin and the average of the non- faulty vectors is  $\sqrt{d}\cdot x$  . The radius of the minimum covering ball is however 0. Hence, the approximation ratio is unbounded.

Lemma 3.3 (from ([9], Observation 4.1)). The worst- case approximation ratio that can be achieved by any algorithm satisfying convex validity is unbounded.

Next, we show that the box validity condition is the only validity condition that, by itself, guarantees that any algorithm satisfying it has a bounded approximation ratio. More precisely, we show that outputting a vector inside TH is sufficient to ensure that the output is a bounded approximation of Cent\*.

Lemma 3.4. The worst- case approximation ratio that can be achieved by any algorithm satisfying box validity is at most  $\begin{array}{r}\frac{t}{n - t}\cdot 2\sqrt{d} \end{array}$

Proof. Consider the coordinate  $k\in [d]$  in which TTH realizes its longest edge. We define a bijection  $\phi :[n]\to [n]$  such that,  $i< j\Rightarrow v_{\phi (i)}[k]< v_{\phi (j)}[k],\forall i,j\in [n]$  . Then,

$$
\begin{array}{l}{|\mathrm{CH}[k]| = \frac{1}{n - t}\sum_{i = t + 1}^{n}v_{\phi (i)} - \frac{1}{n - t}\sum_{i = 1}^{n - t}v_{i}[k]}\\ {= \frac{1}{n - t}\sum_{i = n - t + 1}^{n}v_{\phi (i)} + \frac{1}{n - t}\sum_{i = t + 1}^{n - t}v_{\phi (i)} - \frac{1}{n - t}\sum_{i = 1}^{t}v_{i}[k] - \frac{1}{n - t}\sum_{i = t + 1}^{n - t}v_{\phi (i)}} \end{array}
$$

$$
= \frac{1}{n - t}\sum_{i = n - t + 1}^{n}v_{\phi (i)} - \frac{1}{n - t}\sum_{i = 1}^{t}v_{i}[k]\geq \frac{t}{n - t} v_{\phi (n - t)} - \frac{t}{n - t} v_{\phi (t)} = \frac{t}{n - t} |\mathrm{TTH}[k]|.
$$

Since CH and TTH are necessarily intersecting [9], the furthest a vector satisfying box validity can be from Cent\* is if Cent\* is in CH and the vector is on the opposite vertex of TTH. We showed above that the diagonal of TTH is at most  $\frac{t}{n - t}$  times the diagonal of CH.

The diagonal of CH being upper bounded by  $2\sqrt{d}\cdot \mathrm{Rad}_{\mathrm{cov}}$  , the further we can be from Cent\* by satisfying box validity is

$$
\left(1 + \frac{t}{n - t}\right)\cdot 2\sqrt{d}\cdot \mathrm{Rad}_{\mathrm{cov}}.
$$

The centroid approximation ratio of any algorithm satisfying box validity will hence be upper bounded by  $\textstyle \left(1 + \frac{t}{n - t}\right)\cdot 2\sqrt{d}$

# 3.2 Upper and lower bounds for centroid approximation

In this section, we present upper and lower bounds for centroid approximation under different validity conditions. An overview of these results is presented in Table 1. Note that most bounds are tight. Only in the case  $n< d$  , there is a gap for approximation under box validity that remains to be investigated.

In the following, we present the upper bound for weak validity.

Lemma 3.5 (upper bound for weak validity). The best approximation ratio that can be achieved by an algorithm satisfying weak validity is 1 in the worst case.

Proof. We can achieve 1 with the optimum algorithm picking the center of the minimum covering ball (see [9]). This algorithm satisfies weak validity.

Note that this upper bound is tight, as the lower bound cannot be less than 1 by definition. We now present the algorithm that highlights the upper bound for strong validity.

Lemma 3.6 (Upper bound for strong validity). The MDA algorithm [19] outputs the average of the subset of  $n - t$  vectors that have the smallest diameter, this diameter is defined as the maximum distance between any two vectors. The MDA computes a 2- approximation of the centroid.

Proof. Observe that the output vector of the MDA algorithm is in  $\mathrm{S_{Cent}}$  and is thus inside  $\mathrm{Ball}_{\mathrm{cov}}(\mathrm{S}_{\mathrm{Cent}})$  . The largest distance between any two vectors in  $\mathrm{Ball}_{\mathrm{cov}}(\mathrm{S}_{\mathrm{Cent}})$  is upper bounded by the diameter of the ball. Thus, the algorithm computes at most a 2- approximation.

The following lemma gives a lower bound of 2 on the approximation ratio of the centroid in the context of strong validity, which matches the upper bound above. This shows that the approximation ratio of the MDA algorithm is tight.

Lemma 3.7 (Lower bound for strong validity [9]). The best approximation ratio that can be achieved by an algorithm satisfying strong validity is 2 in the worst case.

In [9], a lower bound of  $2d$  has been presented for convex validity for the worst case where  $n = (d + 1)t$  . We generalize this bound to hold for any  $n\geq (d + 1)t$

Lemma 3.8 (Lower bound for convex validity). The best approximation ratio that can be achieved by an algorithm satisfying convex validity is at least 2d.

Proof. In [9], a lower bound of  $2d$  has been shown for the worst case  $n = (d + 1)t$ . This proof can be easily extended to hold for the general case  $n > \max \{3,d + 1\} \cdot t$ . Assume that  $dt$  vectors are placed at coordinates  $x + \epsilon \cdot u_i, i\in \{1,\ldots ,d\}$ , where  $\epsilon$  is a small constant and  $t$  vectors placed at each coordinate. The remaining  $n - dt$  vectors are placed at  $(0,\ldots ,0)$ . Assume that these  $n - dt$  vectors include  $t$  Byzantine vectors. Observe that such a construction is always possible since  $n > (d + 1)t$ .

In [9], it was shown that the safe area of such a construction results in a single point  $(0,\ldots ,0)$ . Note that the non- faulty centroid is located in  $td / (n - t)$ , and the radius of the centroid ball is  $t / (2(n - t))$ . Thus, the approximation of the centroid is  $2d$  in this example.

Table 1: This is an overview of the results established in this section. Already known results are cited in the respective cells. The lower bound on weak validity follows from the definition of approximation.  

<table><tr><td>validity condition</td><td>LB for n &amp;gt; (d+1)t</td><td>LB for n &amp;lt; (d+1)t</td><td>upper bound</td></tr><tr><td>weak</td><td>1</td><td>1</td><td>1 (Lemma 3.5)</td></tr><tr><td>strong</td><td>2 [9]</td><td>2 [9]</td><td>2 (Lemma 3.6)</td></tr><tr><td>box</td><td>√d (Lemma 3.11)</td><td>min{ n-t
t , √d} (Lemma 3.11)</td><td>2√min{ n, d} (Lemma 3.9)</td></tr><tr><td>convex</td><td>2d (9), Lemma 3.8)</td><td>not possible [39]</td><td>2d (Lemma 3.12)</td></tr></table>

We next give an upper bound result for the box validity condition. Note that there are two algorithms in the literature that achieve the same approximation ratio.

Lemma 3.9 (Upper bound for box validity). One round of the Box algorithm [9] or the RB- TM algorithm [19] achieves an approximation ratio of  $2\sqrt{\min \{n,d\}}$ .

Proof. Note that both algorithms were presented to solve approximate agreement. We can however let the server run one round of these algorithms as if the server were one of the nodes in the distributed network. In [9], it was shown that the output vector of one node at the end of a round is inside the intersection of CH and TTH. This condition is sufficient to achieve a  $2\sqrt{d}$ - approximation [9]. This solves the case  $n > d$ .

We next consider the case  $n< d$ . Note that if CH has dimension  $n$ , the diagonal length argument from [9] implies a  $2\sqrt{n}$  bound on the approximation ratio. Suppose that CH has dimension  $d'$  where  $n< d' \leq d$ . Since there are  $n$  input vectors and all elements of  $\mathrm{S_{Cent}}$  are computed from those vectors,  $\mathrm{Conv}(\mathrm{S_{Cent}})$  has to be contained in a subspace  $U_{\mathrm{input}}$  of dimension  $n$ . The hyperbox CH of dimension  $d'$  is the smallest possible hyperbox containing the convex polytope  $\mathrm{Conv}(\mathrm{S_{Cent}})$ . Hence,  $\mathrm{Conv}(\mathrm{S_{Cent}})$  has to intersect all  $2d'$  faces of CH, otherwise there exists a hyperbox strictly contained in CH that contains  $\mathrm{Conv}(\mathrm{S_{Cent}})$ . For the sake of simplicity, assume that CH is the unit hypercube of dimension  $d'$  placed at the origin with non- negative coordinates only. Note that translation and rotation of all points do not influence the approximation ratio. Further, all following computations can be adjusted with the length of the longest edge of CH to achieve the same result in the general case.

Observe that  $\mathrm{Conv}(\mathrm{S_{Cent}})$  has to intersect all faces of CH that contain the origin. Consider the set of centroids in  $\mathrm{S_{Cent}}$  that lie on these  $d'$  faces. Any two such centroids that lie on different faces are linearly independent. Since  $\mathrm{Conv}(\mathrm{S_{Cent}})$  spans at most an  $n$ - dimensional subspace, at most  $n$  centroids in this set can be linearly independent. Note that the radius of  $\mathrm{Ball}_{\mathrm{cov}}(\mathrm{S_{Cent}})$  is maximized when the centroids lie on intersections of many faces. Consider the largest subset of linearly independent centroids that intersect the  $d'$  considered faces (this subset can be chosen greedily). On average, each centroid in this subset lies in the intersection of at least  $d' / n$  faces. Thus, at least one of these centroids must lie in the intersection of at least  $d' / n$  faces of the unit hypercube. This implies that the radius of the minimum covering ball is at least  $\sqrt{d' / n} /2$  (the intersection of  $k$  faces is at distance  $\sqrt{k} /2$  from the center of the hyperbox).

However, since the centroid of non- faulty vectors has to be contained inside  $\mathrm{Conv}(\mathrm{S_{Cent}}) \subseteq \mathrm{CH}$ , the distance between the output of an algorithm agreeing inside CH and  $\mathrm{Cent}^*$  centroid is at

most  $\sqrt{d^{\prime}}$  , hence the approximation ratio is at most  $2\cdot \sqrt{n}$

Hence, the approximation ratio of the hyperbox algorithm is at most  $2\cdot \sqrt{\min \{n,d\}}$

Before addressing the lower bound for algorithms satisfying box validity, we first need to prove the following lemma.

Lemma 3.10. An algorithm satisfying box validity has to agree inside the trimmed trusted hyperbox.

Proof. We assume that  $t$  Byzantine parties follow the algorithm with their own (worst- case) input vectors, thus being undetectable. Let us consider a consensus algorithm such that the output vector  $v$  always satisfies box validity. For the sake of contradiction, suppose this output vector is outside the trimmed trusted hyperbox. By definition of the trimmed trusted hyperbox, there exists a coordinate  $k$  for which  $v[k]$  is strictly larger than  $n - t$  of the input vectors at coordinate  $k$ . Since Byzantine clients are undetectable, these  $n - t$  input vectors could be the non- faulty ones. This implies that the output vector  $v$  is not in the trusted box, thus violating the box validity condition. This is a contradiction. Hence, the output vector of any algorithm satisfying the box validity condition must be in the trimmed trusted hyperbox.

Lemma 3.11 (Lower bound for box validity). The approximation ratio of any algorithm satisfying box validity is at least  $\sqrt{\frac{1}{2}}\cdot \min \left\{\left\lfloor \frac{n - t}{t}\right\rfloor ,d\right\}$

Proof. In order to prove the lower bound on the approximation ratio, we present a construction where the trimmed trusted hyperbox consists of just one vector. Consider a setting where  $n - t - \min \left\{\left\lfloor \frac{n - t}{t}\right\rfloor t,d t\right\}$  input vectors are at coordinate  $(0,\ldots ,0)$  . We further assume that  $t$  vectors are at coordinate  $e_{k} = x\cdot u_{k},\forall k\in [\min \{\lfloor \frac{n - t}{t}\rfloor ,d\} ]$  , where  $u_{k}$  is the  $k^{t h}$  unit vector. Suppose the  $t$  Byzantine vectors choose their input vectors to be  $(0,\ldots ,0)$  . Then, the trimmed trusted hyperbox is  $(0,\ldots ,0)$

The centroid of non- faulty vectors is  $\begin{array}{r}\frac{t}{n - t}\sum_{k = 1}^{\min \{[(n - t) / t],d\}}e_k \end{array}$  and the distance between the trimmed trusted hyperbox and Cent\* is

$$
\begin{array}{r l} & {\mathrm{dist}(\mathrm{Cent}^{\star},(0,\ldots ,0)) = \sqrt{\sum_{k = 1}^{\min \{[(n - t) / t],d\}}\left(\frac{t}{n - t}\cdot x\right)^{2}}}\\ & {\qquad = \sqrt{\min \left\{\left\lfloor \frac{n - t}{t}\right\rfloor,d\right\}\cdot\left(\frac{t}{n - t}\cdot x\right)^{2}} = \sqrt{\min \left\{\left\lfloor \frac{n - t}{t}\right\rfloor,d\right\}\cdot\frac{t x}{n - t}}.} \end{array}
$$

Now the radius of the minimum covering ball is at most the largest distance between two possible centroids:

$$
\mathrm{Rad}_{\mathrm{cov}}\leq \left\| \sum_{k = 2}^{\min \{[(n - t) / t],d\}}\left(\frac{t}{n - t}\cdot e_k\right) - \sum_{k = 1}^{\min \{[(n - t) / t],d\} -1}\left(\frac{t}{n - t}\cdot e_k\right)\right\| _2 = 2\cdot \sqrt{\left(\frac{t x}{n - t}\right)^2}.
$$

Hence, the approximation ratio is at least

$$
\frac{\mathrm{dist}(\mathrm{Cent}^{\star},(0,\ldots,0))}{\mathrm{Rad}_{\mathrm{cov}}}\geq \sqrt{\frac{1}{2}\cdot\min \left\{\left\lfloor\frac{n - t}{t}\right\rfloor,d\right\}}
$$

We finally consider convex validity. Note that no guarantees can be given for algorithms satisfying convex validity in the case  $n > \max \{3,d + 1\}$  since the safe area cannot be guaranteed to exist in such cases. The results presented here are therefore only of interest in applications where the number of clients surpasses the dimension of the training model.

Lemma 3.12 (Upper bound for convex validity). Consider the algorithm that outputs a vector contained in the safe area that minimizes the distance to the center of  $\mathrm{Ball}_{\mathrm{cov}}(\mathrm{S}_{\mathrm{Cent}})$ . This algorithm computes a 2d- approximation of the centroid.

Proof. Observe that the algorithm computes at most a 2- approximation of Cent\* if the safe area and  $\mathrm{Ball}_{\mathrm{cov}}(\mathrm{S}_{\mathrm{Cent}})$  intersect. This is because the algorithm then chooses a vector that is contained in  $\mathrm{Ball}_{\mathrm{cov}}(\mathrm{S}_{\mathrm{Cent}})$

Now we consider the remaining case, where the safe area and  $\mathrm{Ball}_{\mathrm{cov}}(\mathrm{S}_{\mathrm{Cent}})$  are disjoint. Let  $x$  denote the distance between the safe area and  $\mathrm{Ball}_{\mathrm{cov}}(\mathrm{S}_{\mathrm{Cent}})$  and let  $S$  denote the closest point of the safe area to  $\mathrm{Ball}_{\mathrm{cov}}(\mathrm{S}_{\mathrm{Cent}})$  and  $B$  the closest point of  $\mathrm{Ball}_{\mathrm{cov}}(\mathrm{S}_{\mathrm{Cent}})$  to the safe area, so that the distance between  $S$  and  $B$  is  $x$ . We start by projecting all input vectors orthogonally onto  $S, B$ . The approximation ratio of the algorithm is computed as  $(x + \mathrm{Rad}_{\mathrm{cov}}) / \mathrm{Rad}_{\mathrm{cov}}$ . Observe that the distance between any two centroids after their orthogonal projection onto  $S, B$  cannot increase due to the triangle inequality, while the distance between  $S$  and  $B$  remains unchanged. Therefore, the distance between any two projected centroids onto  $S, B$  is a lower bound on the diameter of  $\mathrm{Ball}_{\mathrm{cov}}(\mathrm{S}_{\mathrm{Cent}})$ . To simplify the discussion on distances, we assume that  $S$  is at coordinate 0 and  $B$  is at coordinate  $x$ .

In the following, we will lower bound the size of  $\mathrm{Rad}_{\mathrm{cov}}$  and upper bound the size of  $x$ . Let the projection of the vectors  $v_{1}, \ldots , v_{n}$  be denoted  $p_{1}, \ldots , p_{n}$  such that the vector projected on the smallest coordinate is denoted  $p_{1}$  and the one projected on the largest coordinate is  $p_{n}$ . Note that there must be at least  $t + 1$  vectors  $p_{i}$  having negative coordinates, otherwise there would exist a convex hull of  $n - t$  vectors that would project onto only strictly positive coordinates, which is a contradiction. There are also at least  $t + 1$  vectors  $p_{j}$  that have coordinate at least  $x$ . If this was not true, there would exist a centroid with a smaller coordinate than  $x$ , which is a contradiction. Further, there are at most  $td$  vectors with a positive coordinate (see proof of Lemma 3.13).

Let  $l$  denote the number of vectors  $p_{i}$  with negative coordinates. Let  $r$  denote the number of vectors  $p_{i}$  with a larger coordinate than  $x$ , and let  $y_{1}, \ldots , y_{r}$  denote the coordinates of these vectors in increasing order. Further, we say that the smallest  $r - t$  coordinates have an average value of  $\overline{y}_{min}$  while the largest  $t$  coordinates have an average of  $\overline{y}_{max}$ . The average of all vectors  $p_{i}$  with coordinates between 0 and  $x$  is defined to be  $a$ .

Observe that  $x$  is upper bounded by the coordinate of any possible centroid. We choose the following centroid to upper bound  $x$ : the average of some  $t + 1$  vectors with negative coordinates, all the vectors between 0 and  $x$ , and the remaining smallest  $r - t$  vectors with coordinates larger than  $x$ . This gives the following bound:

$$
x\leq \frac{1}{n - t}\left(\sum_{i = 1}^{r - t}y_i + a\cdot (n - r - l)\right)\leq \frac{1}{n - t} (n - t - l)\cdot \overline{y}_{min}
$$

note that we upper bounded all vectors with coordinates smaller than 0 by 0.

To lower bound the diameter of  $\mathrm{Ball}_{\mathrm{cov}}(\mathrm{S}_{\mathrm{Cent}})$ , we consider the difference between its largest and smallest coordinates:

$$
\mathrm{Rad}_{\mathrm{cov}}\geq \frac{1}{2(n - t)}\left(\sum_{i = t + 1}^{n}p_{i} - \sum_{i = 1}^{n - t}p_{i}\right)\geq \frac{1}{2(n - t)}\left(t\cdot \overline{y}_{max} - \sum_{i = 1}^{t}p_{i}\right)\geq \frac{t}{2(n - t)}\cdot \overline{y}_{max}
$$

where  $\frac{1}{n - t} \sum_{i = 1}^{t} p_{i} \leq 0$  since there are at least  $t + 1$  vectors  $p_{i}$  with negative coordinates.

The approximation ratio achieved by the algorithm can now be upper bounded by:

$$
\frac{x}{\mathrm{Rad}_{\mathrm{cov}}} +1\leq \frac{\frac{1}{n - t}(n - t - l)\cdot\overline{y}_{min}}{\frac{t}{2(n - t)}\cdot\overline{y}_{max}} +1\leq \frac{2(n - t - l)}{t} +1\leq \frac{2dt}{t} +1 = 2d + 1.
$$

The last inequality holds because there can be at most  $dt$  vectors with positive coordinates, i.e.,  $n - t - l \leq dt$ .

Lemma 3.13. Assume that the safe area is a  $q$ - dimensional convex polytope, where  $1 \leq q \leq d$ . Consider the  $q$ - dimensional subspace in which the safe area is defined. Let  $H$  be a hyperplane that touches the safe area and divides the  $q$ - dimensional space into two subspaces. Then, there can be at most  $qt$  points on the opposite side of  $H$  wrt. the safe area.

Proof. Consider a vertex  $s_v$  of the safe area that lies at the intersection of the safe area with the hyperplane  $H$ . Note that at least one such vertex must exist since the safe area is a convex polytope.

Observe that exactly  $q$ $(q - 1)$  - faces of safe area meet in  $s_{v}$  . Each of these faces are hyperplanes , denoted  $H_{1},\ldots ,H_{d}$  , and go through  $s_{d}$  , each of them defined by a face of the safe area. The safe area is defined such that, for each face  $F_{i}$  , at most  $t$  vectors can lie outside of safe area and thus on the opposite side of  $H$  w.r.t. safe area. In total, at most  $q t$  can lie on the opposite side of  $H$  . And at least  $n - q t > n - d t$  vectors must lie inside safe area.

# 3.3 Federated learning in peer-to-peer networks

The results presented in this paper also hold for federated learning in synchronous peer- to- peer networks. In the peer- to- peer setting, there is no trusted server. Instead, the clients communicate with each other in a fully- connected network by sending messages. The aggregation step by the server is replaced by an exact Byzantine agreement algorithm that makes sure that the clients agree on the same aggregation vector.

The lower bounds presented in Section 3.1 and 3.2 trivially extend to this distributed setting, as they are presented for a stronger setting in which the clients do not receive different sets of vectors as it is possible in a peer- to- peer setting. On the other hand, interactive consistency protocols [23, 41] from distributed computing allow the clients to agree on the same set of vectors. Thus, each client can apply the aggregation algorithms presented in this paper locally. Since these algorithms are deterministic, all clients would output identical vectors after Byzantine agreement.

# 4 Empirical evaluation

In the practical evaluation, we differentiate between the two FL variants where the model parameters or the gradients are exchanged. We consider  $n$  clients, where each client  $i \in [n]$  has access to its own data that follows an unknown distribution  $\mathcal{D}_i$ . Let  $F_i(x)$  be the local loss function of client  $i$  with respect to model parameter  $x$ . The objective is

$$
\arg \min_{x\in \mathbb{R}^d}F(x),\quad \mathrm{where}\quad F(x) = \frac{1}{n}\sum_{i = 1}^{n}F_i(x)
$$

We do not differentiate between the amounts of data points that a client holds, as a Byzantine client could lie about its data to have a larger influence. The training is executed in rounds. We differentiate between the following two settings:

FedSGD In each round  $r$  , a client locally computes the gradient  $g_{i}(x_{r}) = \nabla F_{i}(x_{r})$  on its dataset. It then sends  $g_{i}(x_{r})$  to the server. The server upgrades the global model by aggregating the gradients  $\begin{array}{r}{x_{r + 1}\leftarrow x_{r} - \eta_{n}^{1}\sum_{i = 1}^{n}g_{i}(x_{r})} \end{array}$  , where  $\eta$  is a fixed learning rate, and sends the new model to the clients for the next round.

FedAvg In each round  $r$  , a client locally updates its model parameters (possibly multiple times)  $x_{r + 1}^{i}\leftarrow x_{r}^{i} - \eta g_{i}(x_{r})$  . It then shares its model parameter  $x_{r + 1}^{i}$  with the server. The server aggregates the model parameters  $\begin{array}{r}x_{r + 1}\leftarrow \sum_{i = 1}^{n}x_{r + 1}^{i} \end{array}$  and shares the new model with the clients.

The aggregation algorithm in the definition of FedSGD and FedAvg is an unweighted average of the vectors. For the experiments, we replace this aggregation step with one of the aggregation algorithms presented in Section 3.2. These aggregation algorithms are summarized below.

Aggregation algorithms We implemented the following aggregation algorithms for comparison:

Center of  $\mathrm{Ball}_{\mathrm{cov}}(\mathrm{S}_{\mathrm{Cent}})$  : This algorithm computes all possible centroids on subsets of  $n - t$  vectors and outputs the center of  $\mathrm{Ball}_{\mathrm{cov}}(\mathrm{S}_{\mathrm{Cent}})$  . The algorithm achieves a 1- approximation of the centroid and satisfies weak validity. MDA[19]: This algorithm computes a subset of  $n - t$  vectors with the smallest diameter and outputs the centroid of this subset. The algorithm achieves a 2- approximation of the centroid and satisfies strong validity. Box Algorithm[9]: This algorithm computes the intersection of TTH and CH, and outputs the center of this intersection. In [9], it was shown that such an intersection is non- empty for  $n > 3t$  . The algorithm achieves a  $2\sqrt{d}$  - approximation of the centroid and satisfies box validity.

We do not implement the algorithm based on the safe area (see Lemma 3.12), since this algorithm only works in scenarios where  $n > (d + 1)t$  . Considering that our training models have 200 dimensions, such an experiment was not feasible in our experimental setup.

# 4.1 Experimental setup

We implement a client/server federated learning model for solving classification tasks in Python using the Tensorflow library. The models are evaluated on the MNIST dataset from Kaggle3. The dataset contains 42,000 images of handwritten digit in JPEG format which are labeled, and each class of the data is kept in a separate folder. We consider 10 clients with 3 different data distributions: homogeneous, mild heterogeneous and extreme heterogeneous. For the homogeneous data distribution, we shuffle the entire dataset and split it among 10 clients. The mild heterogeneity case splits each class into 10 parts, where 8 parts contain  $10\%$  of the class, one part  $5\%$  and one part  $15\%$  of the class. In the extreme heterogeneity case, the data is sorted and split into 20 partitions. Each client is randomly assigned two partitions, ensuring they contain data from two distinct classes. Note that the scenarios where clients have different local dataset sizes are not taken into account, as Byzantine clients could exploit this variation to their advantage.

The underlying neural network for solving the image classification task is a MultiLayer Perceptron (MLP) with 3 layers. The learning rate is set to  $\eta = 0.01$  and the decay is calculated with respect to the number of global communication rounds (epochs), i.e.  $dcay = \frac{\eta}{rounds}$

Byzantine behavior in federated learning has been extensively studied and the attacks are categorized into training- based and parameter- based attacks [45]. While training- based attacks, also known as data poisoning attacks, have been analyzed in [6, 22, 35], our work focuses on parameter- based attacks [21, 45]. Specifically, we implement the sign flip attack, inspired by the signSGD algorithm [5, 28], where gradients are replaced by their sign values during transmission. In the sign flip attack, the gradient of the faulty clients is multiplied by  $- 1$  and sent to the server. This attack has been widely used in practical simulations [21, 44, 49, 50, 52].

![](images/56a0f9f02ab69e7b5caec312721612b7aa9cdfd59e55f67c73158218f5d9bf4c.jpg)  
Figure 2: FedSGD setting on homogeneous data with MDA, Box and  $\mathrm{Ball}_{\mathrm{cov}}(\mathrm{S}_{\mathrm{Cent}})$  algorithm

![](images/5e09ceaab62aac5c6dcad85512ba838da3cdd7f9014c5d367756a2729f325731.jpg)  
Figure 3: FedSGD with mild heterogeneous and extreme heterogeneous data under sign flip attack.

Figure 2a illustrates the Center of  $\mathrm{Ball}_{\mathrm{cov}}(\mathrm{S}_{\mathrm{Cent}})$  algorithm in the FedSGD setting with no Byzantine behavior. It can be observed that after 40,000 rounds  $\mathrm{Ball}_{\mathrm{cov}}(\mathrm{S}_{\mathrm{Cent}})$  algorithm reaches over  $77\%$ . The Center of  $\mathrm{Ball}_{\mathrm{cov}}(\mathrm{S}_{\mathrm{Cent}})$  algorithm requires significantly more rounds than the MDA or the Box algorithm in Figure 2b and is therefore not evaluated under Byzantine behavior

![](images/4974d1b73f31acfb55afc463279e5dec152ef93ecc4b2bd66d221b21a7f89c8c.jpg)  
Figure 4: FedAvg setting with homogeneous data distributions with and without Byzantine behavior

and different heterogeneity distributions.

In Figure 2b we evaluate MDA and Box algorithm in the FedSGD setting with homogeneous data distribution under the sign flip attack. We set the number of Byzantine clients to be  $f = 1$ ,  $f = 2$  or  $f = 3$ . MDA and the Box algorithm converge and achieve over  $90\%$  accuracy, even with  $f > 1$ .

In Figure 3a, the mild heterogeneous case shows a slight decrease of  $2\%$  in accuracy with  $f > 1$ , compared to the homogeneous case. In Figure 2b and Figure 3a a drop in accuracy between the different number of adversaries can be noticed. With the mild heterogeneous data in Figure 3a and  $f = 3$ , MDA shows more instability between rounds 220 and 270 with sudden changes in accuracy by up to  $7\%$ . Box algorithm appears to be more stable in that case.

Figure 3b illustrates the extreme heterogeneous case, where each client has two classes of data. The Box algorithm with  $f = 1$  and  $f = 2$  converges and achieves  $76\%$  and  $57\%$  accuracy, respectively. However, with  $f = 3$  the box algorithm fails to converge. MDA with  $f = 1$  achieves  $82\%$  accuracy. For  $f > 1$ , MDA becomes unstable and does not converge. In this case, the MDA algorithm achieves a higher accuracy than the Box algorithm, but it does not seem to converge as robustly as the Box algorithm under one or two Byzantine failures. For  $f = 3$ , the MDA and the Box algorithms both fail to converge. These results suggest that there may be a trade- off between the centroid approximation and the different validity conditions also in practice, which we plan to investigate in more detail in future work.

Figures 3c depicts radius of the smallest enclosing ball and diameter of the cnon- faulty vectors in FedSGD setting with mild heterogeneity and  $f = 1$ . A relationship between Figure 3a with  $f = 1$  and 3c can be observed. MDA and Box algorithm converge, therefore diameter of non- faulty input vectors and radius of the ball remain constant throughout the training process. Figure 3d represents the radius of the smallest enclosing ball and diameter of the non- faulty vectors in FedSGD setting with extreme heterogeneity and  $f = 3$ , which seems to correlate with Figure 3b. It can be noticed that the Box algorithm fails to converge, causing both the radius of the ball and the input diameter to approach zero.

Figure 4 shows FedAvg setting with homogeneous data distribution. When there is no Byzantine behavior, as in Figure 4a, MDA and Box algorithm converge and achieve over  $97\%$  accuracy. However, if there is one Byzantine party in the system, as shown in Figure 4b, MDA fails to converge. Further, there is a distinct decrease in the accuracy of the Box algorithm, which implies that the sign flip attack has a big impact on the system and prevents the model from converging. In the future, we intend to continue the empirical evaluation and test out various Byzantine attacks in different settings.

# Acknowledgments

AcknowledgmentsWe thank anonymous reviewers for their helpful feedback on previous versions of this work. This work was supported in part by the Research Council of Finland, Grant 333837 and the German Research Foundation (DFG), Schwerpunktprogramm, SPP 2378 (project ReNO), 2023- 2027.

# References

References[1] Consumer data privacy in a networked world: A framework for protecting privacy and promoting innovation in the global digital economy. Journal of Privacy and Confidentiality, 4, 03 2013. doi: 10.29012/jpc.v4i2.623. [2] W. Abbas, M. Shabbir, J. Li, and X. Koutsoukos. Resilient distributed vector consensus using centerpoint. Automatica, 136:110046, 2022. ISSN 0005- 1098. [3] D. Alistarh, Z. Allen- Zhu, and J. Li. Byzantine stochastic gradient descent. In Advances in Neural Information Processing Systems, volume 31. Curran Associates, Inc., 2018. [4] A. Bar- Noy and D. Dolev. Families of consensus algorithms. In VLSI Algorithms and Architectures, 1988. ISBN 978- 0- 387- 34770- 7. [5] J. Bernstein, J. Zhao, K. Azizzadenesheli, and A. Anandkumar. signsgd with majority vote is communication efficient and fault tolerant. In 7th International Conference on Learning Representations, ICLR 2019, New Orleans, LA, USA, May 6- 9, 2019. OpenReview.net, 2019. [6] B. Biggio, B. Nelson, and P. Laskov. Poisoning attacks against support vector machines. In Proceedings of the 29th International Coference on International Conference on Machine Learning, ICML'12, Madison, WI, USA, 2012. [7] G. Bracha. Asynchronous Byzantine Agreement Protocols. Information and Computation, 75(2):130- 143, 1987. [8] G. Bracha and S. Toueg. Resilient consensus protocols. In Proceedings of the Second Annual ACM Symposium on Principles of Distributed Computing, PODC '83, 1983. doi: 10.1145/800221.806706. [9] M. Cambus and D. Melnyk. Improved solutions for multidimensional approximate agreement via centroid computation, 2023. URL https://arxiv.org/abs/2306.12741. [10] J. Chen, R. Monga, S. Bengio, and R. Jozefowicz. Revisiting distributed synchronous sgd. In International Conference on Learning Representations Workshop Track, 2016. URL https://arxiv.org/abs/1604.00981. [11] P. Civit, S. Gilbert, and V. Gramoli. Polygraph: Accountable byzantine agreement. In 2021 IEEE '41st International Conference on Distributed Computing Systems (ICDCS), pages 403- 413. IEEE, 2021. [12] P. Civit, M. A. Dzulfikar, S. Gilbert, V. Gramoli, R. Guerraoui, J. Komatovic, and M. Vidigueira. Byzantine consensus is  $\theta$  (n²): The dolev- reschuk bound is tight even in partial synchrony! In 36th International Symposium on Distributed Computing (DISC 2022). Schloss Dagstuhl- Leibniz- Zentrum für Informatik, 2022. [13] G. Damaskinos, E. M. El Mhamdi, R. Guerraoui, R. Patra, and M. Taziki. Asynchronous Byzantine machine learning (the case of SGD). In Proceedings of the 35th International Conference on Machine Learning, volume 80 of Proceedings of Machine Learning Research, pages 1145- 1154. PMLR, 10- 15 Jul 2018.

[14] D. Data and S. Diggavi. Byzantine- resilient high- dimensional sgd with local iterations on heterogeneous data. In International Conference on Machine Learning, pages 2478- 2488. PMLR, 2021. [15] J. Dean, G. Corrado, R. Monga, K. Chen, M. Devin, M. Mao, M. a. Ranzato, A. Senior, P. Tucker, K. Yang, Q. Le, and A. Ng. Large scale distributed deep networks. In Advances in Neural Information Processing Systems, volume 25. Curran Associates, Inc., 2012. URL https://proceedings.neurips.cc/paper_files/paper/2012/file/6aca97005c68f1206823815f66102863- Paper.pdf.[16] D. Doley, N. A. Lynch, S. S. Pinter, E. W. Stark, and W. E. Weihl. Reaching approximate agreement in the presence of faults. J. ACM, 33(3):499- 516, May 1986. doi: 10.1145/5925.5931. [17] E. M. El Mhamdi, R. Guerraoui, and S. Rouault. The hidden vulnerability of distributed learning in Byzantium. In Proceedings of the 35th International Conference on Machine Learning, volume 80 of Proceedings of Machine Learning Research, pages 3521- 3530. PMLR, 10- 15 Jul 2018. [18] E.- M. El- Mhamdi, R. Guerraoui, A. Guirguis, L. N. Hoang, and S. Rouault. Genuinely distributed byzantine machine learning. In Proceedings of the 39th Symposium on Principles of Distributed Computing, PODC '20, 2020. [19] E. M. El- Mhamdi, S. Farhadkhani, R. Guerraoui, A. Guirguis, L.- N. Hoang, and S. Rouault. Collaborative learning in the jungle (decentralized, byzantine, heterogeneous, asynchronous and nonconvex learning). Advances in neural information processing systems, 34:25044- 25057, 2021. [20] C. Fang, Z. Yang, and W. U. Bajwa. Bridge: Byzantine- resilient decentralized gradient descent. IEEE Transactions on Signal and Information Processing over Networks, 8:610- 626, 2022. [21] S. Farhadkhani, R. Guerraoui, N. Gupta, R. Pinot, and J. Stephan. Byzantine machine learning made easy by resilient averaging of momentums. In International Conference on Machine Learning, pages 6246- 6283. PMLR, 2022. [22] S. Farhadkhani, R. Guerraoui, N. Gupta, and R. Pinot. Brief announcement: A case for byzantine machine learning. In Proceedings of the 43rd ACM Symposium on Principles of Distributed Computing, PODC '24, pages 131- 134, 2024. [23] M. J. Fischer and N. A. Lynch. A lower bound for the time to assure interactive consistency. Information Processing Letters, 14(4):183- 186, 1982. ISSN 0020- 0190. [24] A. Ghosh, J. Hong, D. Yin, and K. Ramchandran. Robust federated learning in a heterogeneous environment, 2019. URL https://arxiv.org/abs/1906.06629. [25] Y. Jee Cho, J. Wang, and G. Joshi. Towards understanding biased client selection in federated learning. In Proceedings of The 25th International Conference on Artificial Intelligence and Statistics, volume 151 of Proceedings of Machine Learning Research. PMLR, 2022. [26] D. Jhunjhunwala, P. Sharma, A. Nagarkatti, and G. Joshi. Fedvarp: Tackling the variance due to partial client participation in federated learning. In Uncertainty in Artificial Intelligence, pages 906- 916. PMLR, 2022. [27] D. Jhunjhunwala, S. Wang, and G. Joshi. Fedexp: Speeding up federated averaging via extrapolation. In The Eleventh International Conference on Learning Representations, 2023.

[28] R. Jin, Y. Huang, X. He, H. Dai, and T. Wu. Stochastic- sign sgd for federated learning with theoretical guarantees. arXiv preprint arXiv:2002.10940, 2020. [29] P. Kairouz, H. B. McMahan, B. Avent, A. Bellet, M. Bennis, A. N. Bhagoji, K. Bonawitz, Z. Charles, G. Cormode, R. Cummings, et al. Advances and open problems in federated learning. Foundations and trends@ in machine learning, 14(1- 2):1- 210, 2021. [30] S. P. Karimireddy, S. Kale, M. Mohri, S. Reddi, S. Stich, and A. T. Suresh. SCAFFOLD: Stochastic controlled averaging for federated learning. In Proceedings of the 37th International Conference on Machine Learning, volume 119 of Proceedings of Machine Learning Research. PMLR, 2020. [31] L. Li, W. Xu, T. Chen, G. B. Giannakis, and Q. Ling. Rsa: Byzantine- robust stochastic aggregation methods for distributed learning from heterogeneous datasets. AAAI'19/IAAI'19/EEAI'19. AAAI Press, 2019. ISBN 978- 1- 57735- 809- 1. [32] M. Li, D. G. Andersen, J. W. Park, A. J. Smola, A. Ahmed, V. Josifovski, J. Long, E. J. Shekita, and B.- Y. Su. Scaling distributed machine learning with the parameter server. In Proceedings of the 11th USENIX Conference on Operating Systems Design and Implementation, OSDI'14, page 583- 598, USA, 2014. USENIX Association. ISBN 9781931971164. [33] M. Li, D. G. Andersen, A. J. Smola, and K. Yu. Communication efficient distributed machine learning with the parameter server. In Advances in Neural Information Processing Systems, volume 27. Curran Associates, Inc., 2014. [34] T. Li, A. K. Sahu, M. Zaheer, M. Sanjabi, A. Talwalkar, and V. Smith. Federated optimization in heterogeneous networks. Proceedings of Machine learning and systems, 2:429- 450, 2020. [35] S. Mahloujifar, M. Mahmody, and A. Mohammed. Data poisoning attacks in multi- party learning. In ICML, pages 4274- 4283, 2019. [36] B. McMahan, E. Moore, D. Ramage, S. Hampson, and B. A. y. Arcas. Communication- Efficient Learning of Deep Networks from Decentralized Data. In Proceedings of the 20th International Conference on Artificial Intelligence and Statistics, volume 54 of Proceedings of Machine Learning Research, pages 1273- 1282. PMLR, 20- 22 Apr 2017. URL https://proceedings.mlr.press/v54/mcmahan17a.html.[37] H. B. McMahan, E. Moore, D. Ramage, and B. A. y Arcas. Federated learning of deep networks using model averaging. arXiv preprint arXiv:1602.05629, 2(2), 2016. [38] D. Melnyk and R. Wattenhofer. Byzantine agreement with interval validity. In 2018 IEEE 37th Symposium on Reliable Distributed Systems (SRDS), pages 251- 260, 2018. doi: 10.1109/SRDS.2018.00036. [39] H. Mendes, M. Herlihy, N. Vaidya, and V. K. Garg. Multidimensional agreement in byzantine systems. Distrib. Comput., 28(6), 2015. ISSN 0178- 2770. [40] A. Mitra, R. Jaafar, G. J. Pappas, and H. Hassani. Linear convergence in federated learning: Tackling client heterogeneity and sparse gradients. In Advances in Neural Information Processing Systems, 2021. [41] M. Pease, R. Shostak, and L. Lamport. Reaching agreement in the presence of faults. J. ACM, 27(2), Apr. 1980.

[42] S. J. Reddi, Z. Charles, M. Zaheer, Z. Garrett, K. Rush, J. Konečný, S. Kumar, and H. B. McMahan. Adaptive federated optimization. In International Conference on Learning Representations, 2021. [43] O. Shamir, N. Srebro, and T. Zhang. Communication- efficient distributed optimization using an approximate newton- type method. In Proceedings of the 31st International Conference on Machine Learning, number 2 in Proceedings of Machine Learning Research, pages 1000- 1008, Beijing, China, 22- 24 Jun 2014. PMLR.[44] A. Sharma and N. Marchang. Probabilistic sign flipping attack in federated learning. In 2024 15th International Conference on Computing Communication and Networking Technologies (ICCCNT), 2024. [45] J. Shi, W. Wan, S. Hu, J. Lu, and L. Y. Zhang. Challenges and approaches for mitigating byzantine attacks in federated learning. In 2022 IEEE International Conference on Trust, Security and Privacy in Computing and Communications (TrustCom), pages 139- 146. IEEE, 2022. [46] J. So, B. Güler, and A. S. Avestimehr. Byzantine- resilient secure federated learning. IEEE Journal on Selected Areas in Communications, 39(7), 2021. [47] J. Wang, Q. Liu, H. Liang, G. Joshi, and H. V. Poor. Tackling the objective inconsistency problem in heterogeneous federated optimization. NIPS '20, Red Hook, NY, USA, 2020. Curran Associates Inc. ISBN 9781713829546. [48] X. Wang, S. Mou, and S. Sundaram. A resilient convex combination for consensus- based distributed algorithms. Numerical Algebra, Control and Optimization, 9(3):269- 281, 2019. ISSN 2155- 3289. [49] Y. Wang, Y. Xia, and Y. Zhan. Elite: Defending federated learning against byzantine attacks based on information entropy. In 2021 China Automation Congress (CAC), pages 6049- 6054, 2021. [50] Z. Wu, Q. Ling, T. Chen, and G. B. Giannakis. Federated variance- reduced stochastic gradient descent with robustness to byzantine attacks. IEEE Transactions on Signal Processing, 68:4583- 4596, 2020. [51] C. Xie, S. Koyejo, and I. Gupta. Zeno: Distributed stochastic gradient descent with suspicion- based fault tolerance. In Proceedings of the 36th International Conference on Machine Learning, volume 97 of Proceedings of Machine Learning Research, pages 6893- 6901. PMLR, 09- 15 Jun 2019. [52] J. Xu, S.- L. Huang, L. Song, and T. Lan. Byzantine- robust federated learning through collaborative malicious gradient filtering. In 2022 IEEE 42nd International Conference on Distributed Computing Systems (ICDCS), pages 1223- 1235, 2022. [53] Z. Yang and W. U. Bajwa. Byrdie: Byzantine- resilient distributed coordinate descent for decentralized learning. IEEE Transactions on Signal and Information Processing over Networks, 5(4):611- 627, Dec. 2019. ISSN 2373- 7778. [54] M. Yin, D. Malkhi, M. K. Reiter, G. G. Gueta, and I. Abraham. Hotstuff: Bft consensus with linearity and responsiveness. In Proceedings of the 2019 ACM Symposium on Principles of Distributed Computing, pages 347- 356, 2019. [55] C. Zhang, Y. Xie, H. Bai, B. Yu, W. Li, and Y. Gao. A survey on federated learning. Knowledge- Based Systems, 216:106775, 2021. ISSN 0950- 7051.

[56] Y. Zhang, J. Duchi, M. I. Jordan, and M. J. Wainwright. Information- theoretic lower bounds for distributed statistical estimation with communication constraints. In Advances in Neural Information Processing Systems, volume 26. Curran Associates, Inc., 2013. [57] Y. Zhao, M. Li, L. Lai, N. Suda, D. Civin, and V. Chandra. Federated learning with non- iid data. arXiv preprint arXiv:1806.00582, 2018.