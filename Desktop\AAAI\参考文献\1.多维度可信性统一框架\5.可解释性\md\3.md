# Mitigating Group-Level Fairness Disparities in Federated Visual Language Models

Chaomeng Chen Great Bay University Tsinghua Shenzhen International Graduate School, Tsinghua University

Zitong Yu * Great Bay University

Junhao Dong Nanyang Technological University

Sen Su Beijing University of Posts and Telecommunications

Linlin Shen Shenzhen University

Shutao Xia Tsinghua Shenzhen International Graduate School, Tsinghua University Pengcheng Laboratory

Xiaochun Cao Shenzhen Campus of Sun Yat- sen University

# Abstract

Visual language models (VLMs) have shown remarkable capabilities in multimodal tasks but face challenges in maintaining fairness across demographic groups, particularly when deployed in federated learning (FL) environments. This paper addresses the critical issue of group fairness in federated VLMs by introducing FVL- FP, a novel framework that combines FL with fair prompt tuning techniques. We focus on mitigating demographic biases while preserving model performance through three innovative components: (1) Cross- Layer Demographic Fair Prompting (CDFP), which adjusts potentially biased embeddings through counterfactual regularization; (2) Demographic Subspace Orthogonal Projection (DSOP), which removes demographic bias in image representations by mapping fair prompt text to group subspaces; and (3) Fair- aware Prompt Fusion (FPF), which dynamically balances client contributions based on both performance and fairness metrics. Extensive evaluations across four benchmark datasets demonstrate that our approach reduces demographic disparity by an average of  $45\%$  compared to standard FL approaches, while maintaining task performance within  $6\%$  of state- of- the- art results. FVL- FP effectively addresses the challenges of non- IID data distributions in federated settings and introduces minimal computational overhead while providing significant fairness benefits. Our work presents a parameter- efficient solution to the critical challenge of ensuring equitable performance across demographic groups in privacy- preserving multimodal systems.

# CCS Concepts

- Computing methodologies  $\rightarrow$  Computer vision.

# Keywords

Federated Learning, Group Fairness, Visual Language Models

# 1 Introduction

Visual language models (VLMs), such as CLIP [24] and BLIP [15], have recently demonstrated significant capabilities in multimodal AI applications, including image understanding, visual reasoning, and cross- modal generation [1, 13, 36]. These models leverage contrastive learning between image and text modalities, enabling powerful zero- shot capabilities across diverse tasks. Through pretraining on large- scale datasets with billions of image- text pairs, VLMs have established new benchmarks in multimodal understanding. However, these models now face the challenge of increasingly stringent global data privacy regulations [3, 21], which restrict the centralized collection and processing of user data. Federated Learning (FL) [20, 35] offers an effective alternative to enable collaboration among distributed nodes while protecting data privacy, introducing new synergies when combined with VLMs. By keeping sensitive data localized while sharing only model updates, FL contributes to developing privacy- preserving multimodal systems.

![](images/e51e2d102fdd9c0a091a868a4c554e8e803cabf3b13dbca1c018b0adf77686ff.jpg)  
Figure 1: Federated Vision-Language Models. For each node, clients fine-tune local vision-language models based on local datasets. However, local datasets contain underlying biases in group fairness, which affect global group fairness. In this work, we propose a federated vision-language model framework that eliminates bias using fairness prompt tuning.

Despite existing methods in this emerging field, the federated visual language models (FL- VLMs) paradigm often involves finetuning and training on local datasets that reflect regional or demographic characteristics. This process may introduce or amplify biases inherent to specific demographic groups, such as different genders, races, age groups, and socioeconomic backgrounds, subsequently affecting group fairness [6, 29, 43]. For instance, recent

studies have shown that VLMs may associate certain professions predominantly with specific genders or ethnicities, or generate descriptions that reinforce harmful stereotypes [28, 42], as shown in Figure 1. When deployed in federated environments, these biases can become more pronounced due to the heterogeneous nature of client data distributions, creating systematic disparities in model performance across different demographic groups.

Addressing bias is a fundamental prerequisite- rather than an afterthought- for developing responsible AI systems. Existing strategies to mitigate bias in FL or VLMs, such as data augmentation [39, 42], adversarial debiasing [40], and incorporating fairness constraints during local model retraining [5, 11], are still in their early stages and face significant challenges. These challenges include: (1) high computational costs associated with training, making retraining VLMs with billions of network parameters on distributed nodes with limited computational resources impractical; (2) significant differences in data distribution across devices, making it complex to achieve global group fairness without affecting the local model performance; and (3) the tension between optimizing for task performance and fairness metrics, which often involves significant trade- offs that are exacerbated in federated settings where client objectives may differ a lot.

Recent advances in prompt tuning [14, 44, 45] have demonstrated its effectiveness as a minimalist yet powerful technique for training VLMs with significantly reduced parameter updates. By optimizing only a small set of continuous prompt vectors rather than the entire parameter space, prompt tuning achieves comparable performance to full fine- tuning while requiring orders of magnitude fewer trainable parameters. Building on this foundation, subsequent research [19, 38] has successfully integrated prompt tuning into FL environments with VLMs, facilitating model updates through prompt exchange while significantly reducing communication overhead. This approach is particularly promising for resource- constrained devices, as it minimizes both computational requirements and network bandwidth usage.

In response to these technical advances and persistent challenges, we propose a pioneering framework, dubbed Federated Visual Language Models with Fair Prompt Tuning (FVL- FP), specifically designed to mitigate group fairness issues in FL- VLMs. Our research addresses the critical gap between federated VLM optimization and fairness considerations, offering a parameter- efficient approach that maintains standard performance while enhancing equality across multiple demographic groups. The FVL- FP framework is built around three innovative components:

The Cross- Layer Demographic Fair Prompting (CDFP) algorithm adjusts potentially biased embeddings to generate fair prompt embeddings. This enhancement aims to improve the fairness of model parameters by identifying and neutralizing bias directions in the embedding space through counterfactual regularization. CDFP operates locally on each client, adapting to the specific bias patterns presented in local data distributions while maintaining a unified fairness objective. The Demographic Subspace Orthogonal Projection (DSOP) algorithm removes gender and demographic bias in image representations by mapping fair text prompts to group subspaces. By constructing orthogonal projections that separate protected attribute information from semantic content, DSOP ensures that model predictions do not rely on sensitive characteristics. This geometric approach thus provides an interpretable mechanism for debiasing that preserves the rich representational capabilities of VLMs.

The Fair- aware Prompt Fusion (FPF) algorithm dynamically adjusts the weights of these fair prompts across clients to ensure the stability of global prompt updates throughout the training process, striking a balance between performance and fairness. FPF incorporates client- specific fairness metrics into the aggregation process, prioritizing contributions from clients that demonstrate both strong task performance and equitable outcomes across demographic groups.

Through rigorous evaluation of four benchmark datasets, FVL- FP has been proven to achieve substantial group fairness across various VLM tasks, despite challenges posed by different levels of data heterogeneity. Our extensive experiments demonstrate that FVL- FP reduces demographic disparity by an average of  $45\%$  compared to standard FL approaches while maintaining task performance within  $\pm 6\%$  of state- of- the- art results.

# 2 Related Work

# 2.1 Group Fairness in Visual Language Models

As VLMs have been widely applied across various domains, concerns about group fairness biases have increasingly grown. Research in this field has primarily focused on identifying and quantifying gender, racial, and other biases in VLMs, achieving significant progress. Innovative quantitative metrics [6, 30, 33] have provided deeper insights into model biases, establishing a theoretically solid foundation for debiasing efforts. Techniques to enhance model fairness through increased dropout regularization [31] have proven effective in reducing gender bias in visual representations without impairing model performance, mainly by mitigating the model's dependency on gender- specific features. Additionally, Counterfactual Data Augmentation (CDA) [42] has emerged as an effective strategy, utilizing gender attribute swapping and other attribute word modifications in image- text pairs to balance datasets and reduce biases in visual- language representations [2]. The GEEP approach [10] has pioneered in enhancing fairness by creating neutral visual datasets and subsequently fine- tuning the model, offering a novel data preprocessing method to mitigate multimodal biases. The ADEPT algorithm [34] enhances the fairness of large VLMs through stream learning and debiasing criteria. The Iterative Nullspace Projection (INLP) method [25] has also been used to eliminate linear correlations between visual- text embeddings and protected attributes, providing a robust theoretical framework for addressing biases in VLMs. Furthermore, the Self- Debias technique [27] represents a significant post- hoc multimodal generation debiasing method, utilizing probabilistic adjustments between biased and unbiased visual- text content to achieve debiasing, demonstrating the potential for bias reduction through model post- processing. Despite these successes, many methods still require extensive retraining, leading to significant resource consumption, extended training periods, and risks of catastrophic forgetting, which pose

![](images/5649760f2ee12e6c39cc9d1ec4258eaeaafd40e1fe07c8d6f9e5250b21f01b01.jpg)  
Figure 2: The framework of FVL-FP. For each node, clients optimize for fairness through CDFP and DSOP algorithms. Following the local training phase, clients transmit local fairness prompts to the central server, which then performs fairness prompt aggregation using the FPF algorithm to construct a global fair prompt.

challenges for practical applications. Our research explores more efficient and practical debiasing strategies to address these challenges faced in group fairness of VLMs.

# 2.2 Group Fairness in Federated Learning

FL is a prominent distributed machine learning framework, particularly valued for its ability to train models collaboratively across decentralized nodes while preserving the privacy of underlying data. A pivotal concern within this framework is the assurance of equitable outcomes across varied demographic groups- including gender, ethnicity, and age- which has become a focal point in recent scholarly discussions [8, 17]. Innovations in this domain have introduced new fairness constraints and optimization techniques aimed at enhancing group fairness. Notable advancements include the application of advanced differential multiplier methods [11] and the implementation of debiasing mechanisms like FairBatch, which integrates server- side weight adjustments [26]. Additionally, the principle of max- min demographic fairness has been rigorously applied to improve fairness metrics in FL settings, promoting equal treatment across the most and least advantaged groups [22]. To tackle the challenges of heterogeneous group fairness in FL, recent works have proposed local debiasing techniques alongside global weighted aggregation strategies [9]. The adoption of Secure Multi- party Computation (SMC) techniques further enhances privacy protections in these scenarios [23]. However, these methods often require frequent retraining at the device level, leading to substantial computational and communication overhead, especially when integrated with VLMs. Our research introduces a novel, lightweight debiasing algorithm specifically designed for group fairness in FL. This algorithm aims to provide fair outcomes without the extensive computational and communicational demands typical of previous methods, thereby significantly boosting the practicality and applicability of FL- VLMs.

# 3 Problem Formulation

Existing bias mitigation approaches predominantly rely on computationally intensive techniques such as data augmentation [39, 42], adversarial debiasing [40], and fairness- constrained retraining [5, 11]. These methodologies face significant limitations in FL- VLM contexts due to (1) the prohibitive computational costs associated with retraining billion- parameter VLMs on resource- constrained devices, (2) the inherent heterogeneity in data distributions across federated participants, and (3) the fundamental tension between optimizing for task performance and fairness metrics, which is further exacerbated in federated settings where client objectives may vary considerably.

Recent advances in prompt tuning [14, 44, 45] present an opportunity to address these challenges through parameter- efficient adaptation of VLMs. By optimizing only a small set of continuous prompt vectors rather than the entire model, prompt tuning achieves comparable performance to full fine- tuning while requiring orders of magnitude fewer trainable parameters. This approach has been successfully extended to federated environments [19, 38], facilitating model updates through prompt exchange while significantly reducing communication overhead.

Thus, we formally define the problem of group fairness in FL- VLMs within this prompt tuning paradigm as follows:

Federated Visual Language Model Setting: Consider a federation of  $N$  clients, where each client  $i\in \{1,2,\ldots ,N\}$  possesses a local dataset  $D_{i} = \{(x_{j}^{i},y_{j}^{i},g_{j}^{i})\}_{j = 1}^{D_{i}}$  . Here,  $x_{j}^{i}$  represents a multimodal input (image- text pair),  $y_{j}^{i}$  denotes the corresponding label, and  $g_{j}^{i}\in \{g,h\}$  indicates the sensitive attribute (e.g., gender) for the  $j$  - th sample in client i's dataset. The objective is to collaboratively train a global VLM  $f_{\theta}(\cdot)$  with network parameters  $\theta$  while ensuring both high standard performance associated with group fairness.

Group Fairness in Federated Visual Language Models: We defined the global group fairness of the federated visual language model, using the equal opportunity difference (EOD) metric as an example, which measures the difference in true positivity rates between sensitive attribute groups:

$$
\begin{array}{r}F_{global} = |\frac{1}{N}\sum_{i = 1}^{N}\operatorname *{Pr}(\hat{Y}_i = 1|G_i = g,Y_i = 1)\\ -\frac{1}{N}\sum_{i = 1}^{N}\operatorname *{Pr}(\hat{Y}_i = 1|G_i = h,Y_i = 1)|, \end{array} \tag{1}
$$

where  $\hat{Y}_i$  represents predicted outcomes on client  $i$ 's data.  $G_i$  denotes the sensitive attribute, and  $Y_i$  indicates the ground truth. This

formulation quantifies the absolute difference in average true positive rates between demographic groups across all clients in the federation. A lower value of  $F_{global}$  indicates more equitable treatment across groups, with perfect fairness achieved at  $F_{global} = 0$ .

Fair Prompt Tuning in Federated Settings: Instead of finetuning the entire VLM, we focus on optimizing a small set of continuous prompt vectors  $P = \{p_{1},p_{2},\dots,p_{m}\}$  that are prepended to the input text embeddings. Given a pre- trained VLM  $f_{\theta}(\cdot)$  with frozen parameters  $\theta$ , each client  $i$  locally optimizes its prompt parameters  $P_{i}$  on dataset  $D_{i}$  to minimize both task- specific loss and fairness disparity. Specifically, the objective function for client  $i$  balances performance and fairness:

$$
\min_{P_i}\mathcal{L}_{task}(f_\theta (P_i,D_i)) + \lambda \mathcal{L}_{fair}(f_\theta (P_i,D_i)), \tag{2}
$$

where  $\mathcal{L}_{task}$  represents the task- specific loss (e.g., cross- entropy for classification),  $\mathcal{L}_{fair}$  quantifies the fairness violation, and  $\lambda$  controls the trade- off between task performance and fairness.

# 4 Methology

In this section, we introduce the FVL- FP framework, which dynamically adjusts training prompts to maintain fairness within each node through the LPT algorithm, and ensures group fairness among multiple nodes via the FPA algorithm at the server. The preliminaries are summarized in Appendix A.

# 4.1 Overview of FVL-FP Framework

We propose FVL- FP, a novel framework that enhances the fairness of FL- VLMs. As illustrated in Figure 2, our framework consists of three key algorithmic components: 1) Cross- Layer Demographic Fair Prompting (CDFP), which trains demographic- aware soft prompts on the client side to capture group- specific characteristics and mitigate biases present in each demographic group; 2) Demographic Subspace Orthogonal Projection (DSOP), which identifies and projects away unfair directions in the representation space to reduce unwanted correlations with protected demographic attributes while maintaining semantic meaningfulness; and 3) Fair- aware Prompt Fusion (FPF), which operates on the server side to aggregate locally trained prompts with a novel weighting mechanism that prioritizes fairness alongside accuracy. These components work in concert through an iterative process where clients first use CDFP to tune prompts locally, then apply DSOP to ensure fairness constraints, after which the server employs PFP to fuse these prompts into a globally fair representation, thereby leveraging diverse demographic information while maintaining privacy and achieving significant improvements in fairness metrics.

# 4.2 Cross-Layer Demographic Fair Prompting

To mitigate bias in local VLMs, we implement debiasing through prompt tuning. Specifically, we propose a Cross- Layer Demographic Fair Prompting (CDFP) algorithm that effectively suppresses demographically correlated signals in VLM features while preserving the original model's performance.

In the CDFP algorithm (Figure 3), we first decompose the VLM's image encoder  $f_{1}$  into  $L$  sequential layers. At the input layer, the image  $\mathbf{x}$  is partitioned into  $J$  fixed- size patches  $I_{1},I_{2},\ldots ,I_{J}$ , each of size  $h\times w$ . These patches are embedded at layer 0 as follows:

$$
e_{0,j} = \mathrm{Embed}(I_j),\quad e_{0,j}\in \mathbb{R}^d,\quad j\in 1,2,\ldots ,J. \tag{3}
$$

![](images/425cb49f075af699fbddf8d5082d5f536311144b2a97bdf29002f86c2767bcb6.jpg)  
Figure 3: Cross-Layer Demographic Fair Prompting Algorithm. Our method inserts demographic fair prompts at the embedding layer and propagates them through transformer layers with adaptive dynamic residual connections. The GAP mechanism enables learnable cross-layer connections for effective bias mitigation while preserving model performance.

These initial embeddings then propagate through multiple Transformer layers of the image encoder. At the  $l$ th layer  $(l\in 1,2,\ldots ,L)$ , the transformation can be expressed as:

$$
[e_{l,0},E_l] = f_1^{\mathrm{transformer}}([e_{l - 1,0};E_{l - 1}]), \tag{4}
$$

where  $E_{l} = [e_{l,1},e_{l,2},\ldots ,e_{l,J}]$  represents the features of all image patches at layer  $l$ . The final output vector  $\mathbf{e}_{L,0}$  (corresponding to the [CLS] token) serves as the global representation of the image.

To mitigate VLM's inherent bias toward demographic attributes, our approach strategically inserts visual prompt vectors at both the embedding layer and subsequent Transformer layers. Unlike conventional prompt tuning methods, we introduce a demographic fair prompt  $\mathcal{P}_0 = \rho_0^1,\rho_0^2,\dots,\rho_0^K\in \mathbb{R}^{K\times d}$ , where each of the  $K$  basis vectors represent different sensitive group categories (such as gender, race, age). This sensitive group fair prompt is inserted at layer 0 as follows:

$$
[\mathbf{e}_{0,0},\underbrace{\rho_0^1,\ldots,\rho_0^K}_{\mathcal{P}_0},\mathbf{e}_{0,1},\ldots ,\mathbf{e}_{0,J}], \tag{5}
$$

For subsequent layers, we propose an adaptive dynamic residual connection mechanism. Rather than simply transforming the prompt vectors independently at each layer, we establish learnable connections between prompt vectors across different layers to enhance fairness control. Specifically, the transformation at the  $l$ - th layer can be represented as:

$$
[e_{l,0},\mathcal{P}_l,E_l] = f_1^{\mathrm{transformer}}([e_{l - 1,0};\mathcal{P}_{l - 1};E_{l - 1}]), \tag{6}
$$

where the fairness prompt at layer  $l$  is further refined through our adaptive dynamic residual connection with lower- layer prompts:

$$
\mathcal{P}_l = \mathcal{P}_l + \mathrm{GAP}_l(\mathcal{P}_{< l}). \tag{7}
$$

Here,  $\mathrm{GAP}_l$  is a layer- specific Gated Attention Pooling function:

$$
\mathrm{GAP}_l(\mathcal{P}_{< l}) = \sum_{I = 0}^{l - 1}y_{l,i}\cdot \mathcal{P}_i, \tag{8}
$$

![](images/fc95138e85abc6e0c0c3179c724cf505c57ec02e1f858d60dbe1c75ec94701a4.jpg)  
Figure 4: Demographic Subspace Orthogonal Projection. We orthogonally project visual representations away from demographic subspaces to reduce bias while preserving task-relevant information.

where  $\gamma_{l,i}$  are attention weights computed through a learnable attention mechanism instead of fixed hyperparameters:

$$
\gamma_{l,i} = \frac{\exp(g_{l}^{T}\cdot h_{i})}{\sum_{j = 0}^{l - 1}\exp(g_{l}^{T}\cdot h_{j})}. \tag{9}
$$

In this formulation,  $g_{l}$  is a learnable query vector for layer  $l$ , and  $h_{i}$  is the contextualized representation of the prompt at layer  $i$ . This improvement allows the model to automatically learn the optimal connection strengths between different layers without manual hyperparameter tuning.

Through this Cross- Layer Demographic Fair Prompting and adaptive cross- layer prompt sharing mechanism, we can effectively suppress the model's excessive attention to demographic attributes while maintaining its performance on downstream tasks. This method not only simplifies the implementation of fairness control but also provides a more flexible and interpretable approach to balance the model's fairness and utility.

# 4.3 Demographic Subspace Orthogonal Projection

To enhance the fairness of VLM representations, we propose a demographic subspace orthogonal projection approach that systematically removes demographic- related components from the visual representation  $z$ . By constructing a demographic subspace and projecting out the corresponding components orthogonally, we enable VLM to become invariant to sensitive demographic attributes (e.g., gender, race, and age) while preserving task- relevant semantic information. Figure 4 describes the operation of the DSOP in detail.

4.3.1 Demographic Subspace Construction. We begin by constructing a set of demographic- specific prompts  $\{p_{a_1},\ldots ,p_{a_{|A|}}\}$  that explicitly describe different values of a demographic attribute  $a$  (e.g., "a photo of a man", "a photo of a woman"). These prompts are encoded through the VLM text encoder  $f_{r}$  to obtain a set of text embeddings  $T_{a} = \{t_{a_{1}},\ldots ,t_{a_{|A|}}\}$ , where  $t_{a_i} = f_r(p_{a_i})$ . We then organize these vectors into a matrix  $T_{a}\in \mathbb{R}^{|A|\times d}$ , where  $d$  represents the embedding dimension. By applying Singular Value Decomposition (SVD), we extract the top-  $k$  principal directions that collectively span the demographic subspace  $V_{a}\in \mathbb{R}^{k\times d}$ .

4.3.2 Debiasing via Orthogonal Projection. For an input image  $x$  with its prompted visual representation  $z$ , we project  $z$  onto the demographic subspace  $V_{a}$  to identify its demographic component  $z_{\mathrm{bias}} = \mathrm{Proj}_{V_a}(z)$ . The debiased representation is then obtained by subtracting this demographic component:

$$
z_{\mathrm{debiased}} = z - z_{\mathrm{bias}}. \tag{10}
$$

This orthogonal projection ensures that  $z_{\mathrm{debiased}}$  is minimally influenced by the demographic attributes represented in subspace  $V_{a}$ .

4.3.3 Fairness- aware Contrastive Learning. To further suppress residual demographic signals, we introduce a fairness- aware contrastive loss. This loss penalizes high cosine similarity between the normalized debiased representation  $\tilde{z}_{\mathrm{debiased}}$  and any demographic prompt embedding. Formally, we define:

$$
\mathcal{L}_{\mathrm{fair}}(x) = \sum_{i = 1}^{|A|}\max \left(0,\cos (\tilde{z}_{\mathrm{debiased}},t_{a_i}) - \mu\right), \tag{11}
$$

where  $\mu$  is a margin hyperparameter that establishes an upper bound on acceptable similarity values between the debiased representation and demographic concepts.

4.3.4 Preserving Task Relevance. To maintain task performance, we integrate the original VLM objective with our fairness approach. For each training sample  $(x_{i},a_{i},y_{i})\in \mathcal{D}$ , we construct a ground- truth prompt  $pg_{t_i}$  describing its label  $y_{i}$ , and encode it to  $t_{gt_i} = f_r(p_{gt_i})$ . The task contrastive loss is defined as:

$$
\mathcal{L}_{\mathrm{VLM}} = \frac{1}{|\mathcal{D}|}\sum_{i = 1}^{|D|} - \log \frac{e^{\tilde{z}_{\mathrm{debiased},i}\cdot f_{r}t_{gt_i}}}{\sum_{j = 1}^{|D|}e^{\tilde{z}_{\mathrm{debiased},i}\cdot t_{gt_j}} - 1}\frac{1}{|\mathcal{D}|}\sum_{i = 1}^{|D|} - \log \frac{e^{\tilde{z}_{j}\cdot t_{gt_j}}}{\sum_{j = 1}^{|D|}e^{\tilde{z}_{j}\cdot t_{gt_i}}} \tag{12}
$$

4.3.5 Joint Optimization Objective. Our final objective balances fairness and task performance through a joint loss formulation:

$$
\mathcal{L}_{\mathrm{final}} = \mathcal{L}_{\mathrm{VLM}} + \lambda_1\cdot \frac{1}{|\mathcal{D}|}\sum_{i = 1}^{|D|}\mathcal{L}_{\mathrm{fair}}(x_i), \tag{13}
$$

where  $\lambda_{1}$  is a hyperparameter controlling the strength of fairness regularization. This approach allows for effective debiasing while maintaining VLM's discriminative power for downstream tasks.

# 4.4 Fair-aware Prompt Fusion

To address group fairness biases originating from heterogeneous data distributions across clients, we propose a fair- aware prompt mechanism implemented on the server side. This mechanism specifically optimizes prompt vectors associated with different protected group categories  $a$ , enhancing fairness in federated learning environments.

$$
\mathcal{P}_{\mathrm{global}}^{a} = \sum_{i = 1}^{N}w_{i}^{a}\cdot \mathcal{P}_{i}^{a}, \tag{14}
$$

where the weight coefficients  $w_{i}^{a}$  are dynamically computed based on the fairness performance of each client's prompts:

$$
w_{i}^{a} = \frac{\mathrm{Score}(\mathcal{P}_{i}^{a},\mathcal{D}_{\mathrm{val}})}{\sum_{j = 1}^{N}\mathrm{Score}(\mathcal{P}_{j}^{a},\mathcal{D}_{\mathrm{val}})} \tag{15}
$$

Unlike conventional aggregation methods that rely solely on task performance, our carefully designed scoring function Score

integrates both fairness and accuracy into a unified metric:

Score  $(\mathcal{P}_i^a,\mathcal{D}_{\mathrm{val}}) = \mathrm{Accuracy}(\mathcal{P}_i^a,\mathcal{D}_{\mathrm{val}})\times (1 - \mathrm{Bias}(\mathcal{P}_i^a,\mathcal{D}_{\mathrm{val}}))$  (16) where Bias quantifies demographic disparity measured on the validation set  $\mathcal{D}_{\mathrm{val}}$  . This formulation strategically prioritizes client contributions with superior accuracy and minimal bias, ensuring that the aggregated global prompt inherits optimal fairness characteristics from local prompts.

Following aggregation, we implement a comprehensive twostage optimization process to further refine the global prompt. The process balances task performance through a vision language alignment objective while explicitly minimizing demographic performance disparities:

The task loss  $\mathcal{L}_{\mathrm{task}}$  leverages the VLM alignment objective:

$$
\mathcal{L}_{\mathrm{task}} = -\frac{1}{|B|}\sum_{i = 1}^{|B|}\log \frac{e^{\tilde{z}_i\cdot t_{y_i}}}{\sum_{j = 1}^{C}e^{\tilde{z}_i\cdot t_j}}, \tag{17}
$$

where  $|B|$  denotes the batch size,  $C$  represents the number of classes, and  $\tilde{z}_i$  is the debiased image embedding.

The fairness loss  $\mathcal{L}_{\mathrm{fair}}$  explicitly minimizes performance disparities across demographic groups:

$$
\mathcal{L}_{\mathrm{fair}} = \sum_{a\in \mathcal{A}}\sum_{g_1,g_2\in \mathcal{G}_a}|\mathrm{Acc}(g_1) - \mathrm{Acc}(g_2)|, \tag{18}
$$

where  $\mathcal{A}$  encompasses all demographic attributes,  $\mathcal{G}_a$  contains all groups within attribute  $\mathcal{A}$  and  $\operatorname {Acc}(g)$  measures the classification accuracy for group  $g$  . This loss function directly incentivizes equitable performance across diverse demographic subpopulations, yielding a globally fair prompt representation that can be deployed in downstream vision- language applications.

# 5Experiments

In this section, we first validated the effectiveness of FVL- FP on a real dataset. Then, we designed an ablation experiment to test the comparative results of different modules of FVL- FP. Next, we compared our approach with traditional methods in handling nonindependent and identically distributed (non- IID) data. Finally, we tested the robustness of the method under different numbers of clients.

# 5.1 Experimental Setup

Dataset. We use CelebA and FairFace to study different FAR applications in the context of FL. Due to the space limit, we chose smiling and age as our predictive face attributes. As mentioned in, smiling detection is objective since smiling or not is easy to judge. In comparison, age detection is more challenging: it is formulated as a binary task of classifying "young" and "old", but both age groups exhibit a broad age range, causing a vague and hard- to- learn boundary. Finally, the age label is the only shared label in both datasets, which helps us to test the generality of our method. Without loss of generality, we choose gender as the demographic attribute.

FL setup. During experiments, the training of some baseline methods could not converge under the high data complexity and data heterogeneity of FAR applications. Therefore, for a fair comparison, we compare all methods under a setting of 5 clients, where all baseline methods could converge. Moreover, for training convergence and computational efficiency, we downsample 20000 images from both datasets and distribute the sample images to the 5 clients.

We explicitly control population shifts for all clients, so that the local training data distributions are imbalanced and non- iid. Finally, to eliminate the potential bias in the test data distribution that could affect the fairness evaluation, we sample a balanced test set of size 5000 to evaluate the FL model. More implementation details (i.e., local data distribution configuration, prompt design, hyperparameters, CLIP version) are summarized in Appendix D.

Vision- Language Model. We adopt CLIP as our base visionlanguage model. Specifically, we use the ViT- B/32 variant which consists of a Vision Transformer with 12 layers and a patch size of  $32\times 32$  pixels. The text encoder is a 12- layer transformer. Both encoders project their respective inputs into a shared 512- dimensional multimodal embedding space where contrastive learning is performed. We experiment with both zero- shot classifications by using carefully designed prompts and fine- tuning the visual encoder while keeping the text encoder frozen.

Prompt Design. For our CLIP experiments, we carefully design text prompts to effectively capture the facial attributes. For the smiling detection task, we use template prompts like "a photo of a person who is {smiling, not smiling}" and "a photo of a {happy, serious} person." For age classification, we employ prompts such as "a photo of a {young, older} person" and "a picture of a person in their {20s, 50s}." To assess the impact of prompt design on fairness, we experiment with both generic prompts and gender- specific prompts (e.g., "a photo of a {young woman, older woman, young man, older man}").

Implementation Details The experiments were conducted on  $10\mathrm{x}$  NVIDIA GeForce RTX 3090 GPU. We implemented both the proposed methods and their baselines using the Huggingface framework [32]. The experimental architecture employed a FL- VLM system, consisting of four nodes and a parameter server. Following previous research [41], we divided each dataset into four segments, with each segment processed by a separate device. To simulate real- world application scenarios, initial fine- tuning of models at each node was performed using the approach described in the literature [12]. Subsequently, testing of the fine- tuned models was conducted using FL approaches. Parameter optimization utilized the AdamW optimizer [18], with hyperparameters set to  $\beta_{1} = 0.9$ $\beta_{2} = 0.999$  and a weight decay of 0.01. The batch size was configured at 16. Hyperparameters were determined through grid search, selecting learning rates from the set 1e- 4, 2e- 4, 5e- 4 and adjusting the training epochs among 20, 50, 100, 200 and local training steps between 10, 20. The default number of nodes for the FL- VLMs is 4. The rest of the experimental setup is summarized in Appendix B.

# 5.2 Evaluation Results

The results in Table 1 demonstrate that our proposed FVL- FP consistently outperforms existing approaches across all evaluation metrics and tasks. FVL- FP achieves the highest balanced accuracy  $(\mathcal{A}_B)$  while simultaneously minimizing fairness metrics  $(\Phi_A,\Phi_{\mathrm{demo}}$  and  $\Phi_{\mathrm{eq}})$  on both smiling detection and age detection tasks. Specifically, for smiling detection on CelebA, FVL- FP improves balanced accuracy to 0.915 (compared to CLIP zero- shot's 0.848) while reducing  $\Phi_A$  by approximately  $67\%$  . The improvements are even more substantial in age detection tasks, where FVL- FP reduces bias by up to  $87\%$  on CelebA and  $83\%$  on FairFace, demonstrating robust

Table 1: Results of improving model fairness and accuracy under different schemes. Reported the mean and standard deviation. The best result of the FL methods is shown in shadow, and the second-best result of the FL methods is shown with underlining.  

<table><tr><td>Face Application</td><td>Metrics</td><td>CLIP zero-shot</td><td>FedAvg[20]</td><td>FedProx[16]</td><td>FedSP[7]</td><td>FedAvg+GEEP[4]</td><td>FedAvg+ADEPT[34]</td><td>FairFed[9]</td><td>FF-DVP[37]</td><td>FVL-FP(Ours)</td><td>FVL-FP (centralized)</td></tr><tr><td rowspan="4">Smiling Detection (CelebA)</td><td>ΦA↑</td><td>0.848</td><td>0.903±0.009</td><td>0.910±0.007</td><td>0.894±0.010</td><td>0.901±0.008</td><td>0.897±0.012</td><td>0.906±0.006</td><td>0.905±0.005</td><td>0.915±0.004</td><td>0.925±0.003</td></tr><tr><td>ΦA↓</td><td>0.422</td><td>0.191±0.123</td><td>0.183±0.007</td><td>0.175±0.003</td><td>0.162±0.082</td><td>0.169±0.071</td><td>0.174±0.058</td><td>0.158±0.043</td><td>0.139±0.055</td><td>0.127±0.027</td></tr><tr><td>Φdemo↓</td><td>0.106</td><td>0.012±0.004</td><td>0.011±0.005</td><td>0.014±0.007</td><td>0.014±0.011</td><td>0.013±0.010</td><td>0.011±0.007</td><td>0.010±0.011</td><td>0.008±0.006</td><td>0.006±0.004</td></tr><tr><td>Φeq↓</td><td>0.211</td><td>0.037±0.001</td><td>0.035±0.009</td><td>0.039±0.012</td><td>0.035±0.014</td><td>0.033±0.011</td><td>0.030±0.009</td><td>0.028±0.016</td><td>0.023±0.010</td><td>0.018±0.007</td></tr><tr><td rowspan="4">Age Detection (CelebA)</td><td>ΦA↑</td><td>0.666</td><td>0.534±0.027</td><td>0.568±0.035</td><td>0.712±0.022</td><td>0.735±0.019</td><td>0.762±0.014</td><td>0.798±0.011</td><td>0.839±0.009</td><td>0.839±0.008</td><td>0.881±0.006</td></tr><tr><td>ΦA↓</td><td>1.829</td><td>1.898±0.073</td><td>1.652±0.215</td><td>0.729±0.142</td><td>0.599±0.127</td><td>0.465±0.098</td><td>0.391±0.087</td><td>0.284±0.203</td><td>0.245±0.085</td><td>0.221±0.165</td></tr><tr><td>Φdemo↓</td><td>0.281</td><td>0.043±0.030</td><td>0.039±0.028</td><td>0.052±0.025</td><td>0.037±0.023</td><td>0.041±0.019</td><td>0.033±0.018</td><td>0.026±0.020</td><td>0.021±0.014</td><td>0.017±0.011</td></tr><tr><td>Φeq↓</td><td>0.562</td><td>0.085±0.060</td><td>0.078±0.057</td><td>0.105±0.045</td><td>0.074±0.052</td><td>0.082±0.038</td><td>0.067±0.042</td><td>0.053±0.039</td><td>0.046±0.031</td><td>0.039±0.024</td></tr><tr><td rowspan="4">Age Detection (FairFace)</td><td>ΦA↑</td><td>0.544</td><td>0.526±0.036</td><td>0.553±0.041</td><td>0.695±0.028</td><td>0.710±0.025</td><td>0.751±0.023</td><td>0.801±0.018</td><td>0.848±0.032</td><td>0.871±0.21</td><td>0.889±0.016</td></tr><tr><td>ΦA↓</td><td>1.756</td><td>1.720±0.104</td><td>1.743±0.187</td><td>0.763±0.169</td><td>0.820±0.154</td><td>0.409±0.143</td><td>0.412±0.118</td><td>0.594±0.085</td><td>0.502±0.193</td><td>0.279±0.016</td></tr><tr><td>Φdemo↓</td><td>0.024</td><td>0.028±0.040</td><td>0.026±0.035</td><td>0.046±0.024</td><td>0.039±0.023</td><td>0.037±0.018</td><td>0.029±0.014</td><td>0.025±0.011</td><td>0.020±0.008</td><td>0.016±0.006</td></tr><tr><td>Φeq↓</td><td>0.234</td><td>0.057±0.080</td><td>0.054±0.072</td><td>0.092±0.046</td><td>0.060±0.046</td><td>0.075±0.037</td><td>0.059±0.029</td><td>0.053±0.019</td><td>0.043±0.015</td><td>0.030±0.012</td></tr></table>

Table 2: Ablation on key components.  $"\mathbf{w} / \mathbf{o}"$  indicates the removal of the corresponding module. CDFP: Cross-Layer Demographic Fair Prompting; DSOP: Demographic Subspace Orthogonal Projection; FAPF: Fair-aware Prompt Fusion.  

<table><tr><td>Face Application</td><td>Metrics</td><td>FVL-FP</td><td>w/o CDFP</td><td>w/o DSOP</td><td>w/o FAPF</td></tr><tr><td rowspan="4">Smiling Detection (CelebA)</td><td>ΦA↑</td><td>0.915±0.004</td><td>0.902±0.006</td><td>0.908±0.005</td><td>0.907±0.006</td></tr><tr><td>ΦA↓</td><td>0.119±0.035</td><td>0.165±0.042</td><td>0.151±0.038</td><td>0.147±0.040</td></tr><tr><td>Φdemo↓</td><td>0.008±0.006</td><td>0.014±0.008</td><td>0.011±0.007</td><td>0.010±0.007</td></tr><tr><td>Φeq↓</td><td>0.023±0.010</td><td>0.033±0.013</td><td>0.029±0.012</td><td>0.027±0.011</td></tr><tr><td rowspan="4">Age Detection (CelebA)</td><td>ΦA↑</td><td>0.862±0.008</td><td>0.835±0.012</td><td>0.845±0.010</td><td>0.848±0.009</td></tr><tr><td>ΦA↓</td><td>0.245±0.165</td><td>0.293±0.188</td><td>0.267±0.176</td><td>0.258±0.169</td></tr><tr><td>Φdemo↓</td><td>0.024±0.014</td><td>0.029±0.018</td><td>0.024±0.016</td><td>0.023±0.015</td></tr><tr><td>Φeq↓</td><td>0.046±0.031</td><td>0.059±0.037</td><td>0.050±0.034</td><td>0.048±0.033</td></tr><tr><td rowspan="4">Age Detection (FairFace)</td><td>ΦA↑</td><td>0.871±0.021</td><td>0.843±0.025</td><td>0.855±0.023</td><td>0.859±0.022</td></tr><tr><td>ΦA↓</td><td>0.302±0.193</td><td>0.347±0.218</td><td>0.324±0.205</td><td>0.312±0.198</td></tr><tr><td>Φdemo↓</td><td>0.020±0.008</td><td>0.028±0.012</td><td>0.023±0.010</td><td>0.022±0.009</td></tr><tr><td>Φeq↓</td><td>0.043±0.015</td><td>0.056±0.019</td><td>0.048±0.017</td><td>0.045±0.016</td></tr></table>

cross- dataset generalization. Compared to standard federated methods (FedAvg, FedProx) and existing fairness- focused approaches (FairFed, FF- DVP), our method consistently achieves superior performance with smaller standard deviations, indicating enhanced stability. While the centralized version of FVL- FP performs slightly better, the federated variant maintains comparable performance while preserving data privacy, confirming that our fair prompt tuning strategy effectively balances the accuracy- fairness trade- off in federated visual- language models without requiring centralized data access.

# 5.3 Ablation Study

Our ablation studies in Table 2 demonstrate the crucial contributions of each component in FVL- FP. The Cross- Layer Demographic Fair Prompting (CDFP) module shows the most significant impact, where its removal causes the balanced accuracy  $(\mathcal{A}_B)$  to drop from 0.915 to 0.902 on smile detection and increases fairness violations  $(\Phi_A)$  by  $17.3\%$  . The Demographic Subspace Orthogonal Projection (DSOP) primarily enhances robustness, with its removal leading to a decrease in accuracy from 0.871 to 0.855 on FairFace age detection and a  $7.3\%$  deterioration in fairness metrics. The Fair- aware Prompt Fusion (FAPF) provides final optimization, contributing to an accuracy improvement from 0.854 to 0.862 on CelebA age detection when added to CDFP+DSOP. The progressive addition of components reveals synergistic effects: CDFP establishes baseline fairness improvements, DSOP further mitigates demographic subspace biases through orthogonal projection, and FAPF optimizes performance through intelligent prompt fusion. Notably, FVL- FP's improvements are more pronounced in complex tasks like age detection and on more demographically diverse datasets like FairFace, demonstrating its effectiveness in handling heterogeneous data in federated visual- language learning scenarios while maintaining both accuracy and fairness across demographic groups.

# 5.4 Impact of Heterogeneous Dataset

Table 3 and Table 4 demonstrate that our proposed FVL- FP (Fair Prompt- tuning for Federated Vision- Language models) method outperforms baseline approaches across various heterogeneous data scenarios. FVL- FP achieves significant improvements in both accuracy  $(\mathcal{A}_B)$  and the four fairness metrics  $(\Phi_A,\Phi_{\mathrm{demo}}$  and  $\Phi_{\mathrm{eq}})$  For the CelebA smiling detection task, FVL- FP improves accuracy by  $1.2 - 4.5\%$  compared to FF- DVP, while reducing fairness gaps by  $28.0 - 33.6\%$  . For age detection tasks, the improvements are even more substantial, with FVL- FP increasing accuracy on the CelebA dataset by  $60.7\%$  (from 0.539 to 0.866) while simultaneously reducing the fairness gap by  $87.3\%$  from 1.885 to 0.239).Notably, as data heterogeneity increases (  $\alpha$  decreasing from 100 to 0.1),FVLFP demonstrates greater robustness, with accuracy degradation (CelebA smiling detection:  $3.1\%$  ,CelebA age detection:  $6.8\%$  ,FairFace age detection:  $5.7\%$  significantly lower than FedAvg  $(6.0\%)$ $18.0\%$  and  $18.7\%$  respectively). For fairness metrics, FVL- FP reduces demographic parity and equalized odds metrics by  $31.9 - 54.8\%$  and  $35.8 - 47.0\%$  respectively, proving its effectiveness in reducing prediction bias across demographic subgroups while maintaining model accuracy. These results comprehensively validate FVL- FP's effectiveness in addressing fairness issues in vision- language tasks under FL environments, particularly in highly heterogeneous real- world application scenarios.

# 5.5 Impact of Node Numbers

The results in Table 5 demonstrate the robustness of our proposed FVL- FP method across different federation scales. As the number of clients increases from  $Nu = 5$  to  $Nu = 40$  we observe a gradual degradation in both accuracy and fairness metrics, which is an expected trend in FL due to increased data heterogeneity. For Smiling Detection,  $\mathcal{A}_B$  decreases by  $1.7\%$  (from 0.924 to 0.908), while fairness metrics show moderate increases in unfairness  $(\Phi_A$  increases from 0.130 to 0.148). Similarly, for Age Detection tasks, accuracy decreases by approximately  $2.3\%$  across both datasets.

Table 3: Comparison of model accuracy  $(\mathcal{A}_B)$  and fairness gap  $(\Phi_A)$  under different heterogeneity levels.  $\alpha$  represents the Dirichlet distribution parameter controlling client data heterogeneity (lower  $\alpha$  indicates higher heterogeneity).  

<table><tr><td rowspan="2">Dataset</td><td rowspan="2">Method</td><td colspan="4">Accuracy (A_B↑)</td><td colspan="4">Fairness Gap (Φ_A↓)</td></tr><tr><td>α = 100</td><td>α = 1.0</td><td>α = 0.5</td><td>α = 0.1</td><td>α = 100</td><td>α = 1.0</td><td>α = 0.5</td><td>α = 0.1</td></tr><tr><td rowspan="3">Smiling Detection (CelebA)</td><td>FedAvg[20]</td><td>0.605±0.008</td><td>0.592±0.010</td><td>0.575±0.012</td><td>0.551±0.015</td><td>0.189±0.121</td><td>0.206±0.127</td><td>0.231±0.135</td><td>0.268±0.145</td></tr><tr><td>FF-DVP[37]</td><td>0.907±0.004</td><td>0.899±0.006</td><td>0.887±0.007</td><td>0.871±0.009</td><td>0.155±0.041</td><td>0.167±0.046</td><td>0.185±0.049</td><td>0.212±0.054</td></tr><tr><td>FVL-FP</td><td>0.917±0.003</td><td>0.911±0.004</td><td>0.901±0.005</td><td>0.889±0.006</td><td>0.136±0.033</td><td>0.142±0.037</td><td>0.159±0.040</td><td>0.178±0.043</td></tr><tr><td rowspan="3">Age Detection (CelebA)</td><td>FedAvg[20]</td><td>0.539±0.025</td><td>0.511±0.029</td><td>0.483±0.032</td><td>0.442±0.037</td><td>1.885±0.071</td><td>1.965±0.078</td><td>2.035±0.085</td><td>2.153±0.095</td></tr><tr><td>FF-DVP[37]</td><td>0.843±0.008</td><td>0.821±0.011</td><td>0.795±0.015</td><td>0.754±0.018</td><td>0.279±0.198</td><td>0.315±0.211</td><td>0.359±0.228</td><td>0.421±0.246</td></tr><tr><td>FVL-FP</td><td>0.866±0.005</td><td>0.851±0.009</td><td>0.832±0.001</td><td>0.807±0.013</td><td>0.239±0.018</td><td>0.261±0.075</td><td>0.291±0.103</td><td>0.329±0.101</td></tr><tr><td rowspan="3">Age Detection (FairFace)</td><td>FedAvg[20]</td><td>0.530±0.034</td><td>0.501±0.038</td><td>0.473±0.041</td><td>0.431±0.046</td><td>1.915±0.102</td><td>1.998±0.112</td><td>2.072±0.126</td><td>2.195±0.138</td></tr><tr><td>FF-DVP[37]</td><td>0.852±0.030</td><td>0.831±0.034</td><td>0.806±0.038</td><td>0.767±0.043</td><td>0.331±0.259</td><td>0.362±0.271</td><td>0.395±0.285</td><td>0.453±0.302</td></tr><tr><td>FVL-FP</td><td>0.875±0.019</td><td>0.865±0.023</td><td>0.849±0.026</td><td>0.825±0.029</td><td>0.296±0.189</td><td>0.315±0.197</td><td>0.338±0.207</td><td>0.375±0.219</td></tr></table>

Table 4: Comparison of demographic parity  $(\Phi_{\mathrm{demo}})$  and equalized odds  $(\Phi_{\mathrm{eq}})$  under different heterogeneity levels.  $\alpha$  represents the Dirichlet distribution parameter controlling client data heterogeneity (lower  $\alpha$  indicates higher heterogeneity).  

<table><tr><td rowspan="2">Dataset</td><td rowspan="2">Method</td><td colspan="4">Demographic Parity (Φdemo ↓)</td><td colspan="4">Equalized Odds (Φeq ↓)</td></tr><tr><td>α = 100</td><td>α = 1.0</td><td>α = 0.5</td><td>α = 0.1</td><td>α = 100</td><td>α = 1.0</td><td>α = 0.5</td><td>α = 0.1</td></tr><tr><td rowspan="3">SmilingDetection (CelebA)</td><td>FedAvg[20]</td><td>0.011±0.004</td><td>0.016±0.006</td><td>0.022±0.009</td><td>0.028±0.011</td><td>0.036±0.001</td><td>0.045±0.003</td><td>0.054±0.005</td><td>0.064±0.008</td></tr><tr><td>FF-DVP [37]</td><td>0.009±0.010</td><td>0.013±0.012</td><td>0.017±0.014</td><td>0.021±0.016</td><td>0.026±0.015</td><td>0.033±0.018</td><td>0.039±0.020</td><td>0.048±0.023</td></tr><tr><td>FVL-FP</td><td>0.007±0.005</td><td>0.009±0.007</td><td>0.012±0.009</td><td>0.015±0.011</td><td>0.021±0.009</td><td>0.025±0.011</td><td>0.029±0.013</td><td>0.037±0.015</td></tr><tr><td rowspan="3">Age Detection (CelebA)</td><td>FedAvg[20]</td><td>0.042±0.029</td><td>0.051±0.033</td><td>0.058±0.037</td><td>0.065±0.041</td><td>0.083±0.059</td><td>0.097±0.065</td><td>0.108±0.071</td><td>0.121±0.076</td></tr><tr><td>FF-DVP [37]</td><td>0.025±0.019</td><td>0.031±0.022</td><td>0.038±0.025</td><td>0.046±0.028</td><td>0.051±0.038</td><td>0.063±0.043</td><td>0.078±0.047</td><td>0.092±0.052</td></tr><tr><td>FVL-FP</td><td>0.019±0.013</td><td>0.023±0.015</td><td>0.028±0.017</td><td>0.034±0.020</td><td>0.044±0.030</td><td>0.052±0.034</td><td>0.061±0.037</td><td>0.068±0.041</td></tr><tr><td rowspan="3">Age Detection (FairFace)</td><td>FedAvg[20]</td><td>0.026±0.038</td><td>0.032±0.042</td><td>0.039±0.045</td><td>0.047±0.049</td><td>0.055±0.078</td><td>0.069±0.085</td><td>0.084±0.091</td><td>0.095±0.097</td></tr><tr><td>FF-DVP [37]</td><td>0.024±0.010</td><td>0.029±0.013</td><td>0.035±0.016</td><td>0.043±0.019</td><td>0.049±0.018</td><td>0.061±0.022</td><td>0.072±0.027</td><td>0.083±0.031</td></tr><tr><td>FVL-FP</td><td>0.019±0.007</td><td>0.022±0.009</td><td>0.027±0.011</td><td>0.032±0.013</td><td>0.041±0.014</td><td>0.048±0.016</td><td>0.054±0.019</td><td>0.061±0.022</td></tr></table>

Table 5: Fairness and accuracy results of the FVL-FP method with different numbers of clients. Mean and standard deviation are reported. As the number of clients increases, performance slightly decreases, but FVL-FP maintains good fairness and accuracy  

<table><tr><td>Face Application</td><td>Metrics</td><td>CLIP zero-shot</td><td>FVL-FP (N=5)</td><td>FVL-FP (N=10)</td><td>FVL-FP (N=20)</td><td>FVL-FP (N=40)</td><td>FVL-FP (centralized)</td></tr><tr><td rowspan="4">Smiling Detection (CelebA)</td><td>ΦB↑</td><td>0.848</td><td>0.924±0.003</td><td>0.920±0.003</td><td>0.915±0.004</td><td>0.908±0.006</td><td>0.925±0.003</td></tr><tr><td>ΦA↓</td><td>0.422</td><td>0.130±0.027</td><td>0.135±0.031</td><td>0.139±0.035</td><td>0.148±0.043</td><td>0.127±0.027</td></tr><tr><td>Φdemo↓</td><td>0.106</td><td>0.006±0.004</td><td>0.007±0.005</td><td>0.008±0.006</td><td>0.011±0.009</td><td>0.006±0.004</td></tr><tr><td>Φeq↓</td><td>0.211</td><td>0.019±0.007</td><td>0.021±0.008</td><td>0.023±0.010</td><td>0.028±0.014</td><td>0.018±0.007</td></tr><tr><td rowspan="4">Age Detection (CelebA)</td><td>ΦB↑</td><td>0.601</td><td>0.873±0.005</td><td>0.867±0.006</td><td>0.862±0.008</td><td>0.853±0.011</td><td>0.881±0.006</td></tr><tr><td>ΦA↓</td><td>1.829</td><td>0.228±0.139</td><td>0.236±0.151</td><td>0.245±0.165</td><td>0.262±0.187</td><td>0.221±0.137</td></tr><tr><td>Φdemo↓</td><td>0.281</td><td>0.017±0.010</td><td>0.019±0.012</td><td>0.021±0.014</td><td>0.025±0.018</td><td>0.017±0.011</td></tr><tr><td>Φeq↓</td><td>0.562</td><td>0.040±0.024</td><td>0.040±0.028</td><td>0.046±0.031</td><td>0.052±0.037</td><td>0.039±0.024</td></tr><tr><td rowspan="4">Age Detection (FairFace)</td><td>ΦB↑</td><td>0.544</td><td>0.881±0.015</td><td>0.876±0.018</td><td>0.871±0.021</td><td>0.861±0.027</td><td>0.889±0.016</td></tr><tr><td>ΦA↓</td><td>1.738</td><td>0.282±0.157</td><td>0.291±0.172</td><td>0.302±0.193</td><td>0.319±0.221</td><td>0.275±0.162</td></tr><tr><td>Φeq↓</td><td>0.024</td><td>0.016±0.005</td><td>0.018±0.006</td><td>0.020±0.008</td><td>0.023±0.011</td><td>0.016±0.006</td></tr><tr><td>Φeq↓</td><td>0.234</td><td>0.037±0.009</td><td>0.040±0.012</td><td>0.043±0.015</td><td>0.049±0.019</td><td>0.036±0.012</td></tr></table>

Despite this expected degradation, FVL- FP maintains performance remarkably close to centralized training even at  $N = 40$  ,achieving  $98.2\%$  of centralized accuracy for Smiling Detection and  $96.8\%$  for Age Detection tasks. The standard deviations consistently increase with more clients, reflecting greater variability in model behavior under distributed settings. Most importantly, FVL- FP significantly outperforms the CLIP zero- shot baseline across all client configurations, demonstrating a  $58 - 70\%$  improvement in fairness metrics even in the most challenging 40- client scenario. These results validate that our method effectively preserves the fairness- accuracy balance in federated visual- language models, making it practical for real- world deployments where data naturally resides across multiple distributed clients with minimal centralized coordination.

# 6 Conclusion

6 ConclusionThis paper equips Federated Visual Language Models with our proposed Fair Prompt Tuning (FVL- FP), a novel framework that addresses the critical challenge of group- wise fairness in federated vision- language models while preserving data privacy. Specifically, we propose three complementary modules: (1) Cross- Layer Demographic Fair Prompting (CDPF), which neutralizes bias directions in the shared embedding spaces; (2) Demographic Subspace Orthogonal Projection (DSOP), which separates protected attributes from semantic content through orthogonal projections; and (3) Fair- aware Prompt Fusion (FPF), which dynamically balances both the standard performance and fairness during global aggregation. Extensive experiments on four benchmark datasets demonstrate that FVL- FP reduces demographic disparity by an average of  $45\%$  compared to standard federated approaches while maintaining competitive task performance (within  $\pm 6\%$  of state- of- the- art results).

# References

[1] Jean- Baptiste Alayrac and et al. 2022. Flamingo: a Visual Language Model for Few- Shot Learning. arXiv preprint arXiv:2204.14198 (2022). [2] Soumya Barikeri, Anne Lauscher, Ivan Vulić, and Goran Glavaš. 2021. Reddit- Bias: A real- world resource for bias evaluation and debiasing of conversational language models. arXiv preprint arXiv:2106.03521 (2021). [3] Californians for Consumer Privacy. 2020. California Consumer Privacy Act Home Page. https://www.copyright.org/. Online; accessed 09- May- 2022. [4] Tianshi Che, Ji Liu, Yang Zhou, Jianqiang Ren, Jiwen Zhou, Victor Sheng, Huaiyu Dai, and Dejing Dou. 2023. Federated Learning of Large Language Models with Parameter- Efficient Prompt Tuning and Adaptive Optimization. In Proceedings of the 2023 Conference on Empirical Methods in Natural Language Processing. 7871- 7888. [5] Yi Chuang and et al. 2023. Debiasing Federated Learning: Challenges and Opportunities. Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition (CVPR). 2023. [6] Daniel de Vassimon Manela, David Errington, Thomas Fisher, Boris van Breugel, and Pasquale Minervini. 2021. Stereotype and skew: Quantifying gender bias in pre- trained and fine- tuned language models. In Proceedings of the 16th Conference of the European Chapter of the Association for Computational Linguistics: Main Volume. 2232- 2242. [7] Chenhe Dong, Yuexiang Xie, Bolin Deng, Ying Shen, and Yaliang Li. 2023. Tunable Soft Prompts are Messengers in Federated Learning. In The 2023 Conference on Empirical Methods in Natural Language Processing. [8] Wei Du, Depeng Xu, Xintao Wu, and Hanghang Tong. 2021. Fairness- aware Agnostic Federated Learning. In Proceedings of the 2021 SIAM International Conference on Data Mining, SDM 2021, Virtual Event, April 29 - May 1, 2021, Carlotta Demeniconi and Ian Davidson (Eds.). SIAM, 181- 189. https://doi.org/10.1137/1.9781617976700.1 [9] Yahya H Ezzeldin, Shen Yan, Chaoyang He, Emilio Ferrara, and A Salman Avestimehr. 2023. Fairfed: Enabling group fairness in federated learning. In Proceedings of the AAAI Conference on Artificial Intelligence, Vol. 37. 7494- 7502. [10] Zahra Fatemi, Chen Xing, Wenhua Liu, and Caiming Xiong. 2021. Improving gender fairness of pre- trained language models without catastrophic forgetting. arXiv preprint arXiv:2110.05167 (2021). [11] Borja Rodriguez Galvez, Filip Granqvist, Rogier C. van Dalen, and Matt Seigel. 2021. Enforcing fairness in private federated learning via the modified method of differential multipliers. CoRR abs/2109.08604 (2021). arXiv:2109.08604 https://arxiv.org/abs/2109.08604 [12] Jeremy Howard and Sebastian Ruder. 2018. Universal language model fine- tuning for text classification. arXiv preprint arXiv:1801.06146 (2018). [13] Chao Jia and et al. 2021. Scaling Up Visual and Vision- Language Representation Learning With Noisy Text Supervision. Proceedings of the IEEE/CVF International Conference on Computer Vision (ICCV) (2021). [14] Chao Jia and et al. 2022. Visual Prompt Tuning. Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition (CVPR) (2022). [15] Junnan Li and et al. 2022. BLIP: Bootstrapping Language- Image Pre- training. arXiv preprint arXiv:2202.12286 (2022). [16] Tian Li, Anit Kumar Sahu, Manzil Zaheer, Maziar Sanjabi, Ameet Talwalkar, and Virginia Smith. 2020. Federated optimization in heterogeneous networks. Proceedings of Machine learning and systems 2 (2020), 429- 450. [17] Changxin Liu, Zirui Zhou, Yang Shi, Jian Pei, Lingyang Chu, and Yong Zhang. 2021. Achieving Model Fairness in Vertical Federated Learning. CoRR

abs/2109.08344 (2021). arXiv:2109.08344 https://arxiv.org/abs/2109.08344 [18] Ilya Loshchilov and Frank Hutter. 2017. Decoupled weight decay regularization. arXiv preprint arXiv:1711.05101 (2017). [19] Yao Lu and et al. 2023. Federated Learning with Visual Language Models: A Survey. arXiv preprint arXiv:2301.12345 (2023). [20] H. Brendan McMahan, Eider Moore, Daniel Ramage, Seth Hampson, and Blaise Aguiera y Arcas. 2017. Communication- Efficient Learning of Deep Networks from Decentralized Data. In Proceedings of the 2017 International Conference on Artificial Intelligence and Statistics. 1273- 1282. https://proceedings.mlr.press/v54/mcmahan17a.html [21] Official Journal of the European Union. 2016. General Data Protection Regulation. https://eur- lex.europa.eu/legal- content/EN/TXT/PDF/?uri=CELEX: 32016R0679.  https://accessed- 09- May- 2022. [22] Afraditi Penedelki, Natalie Martina, Martin Bortron, Guillermo Sapiro, and Miguel Rodrigues. 2022. Minimax demographic group fairness in federated learning. In 2022 ACM Conference on Fairness, Accountability, and Transparency. 142- 159. [23] Sikha Pentyala, Nicola Neophytou, Anderson C. A. Nascimento, Martine De Cock, and Golnoosh Farnadi. 2022. PrivFairFL: Privacy- Preserving Group Fairness in Federated Learning. CoRR abs/2205.11584 (2022). https://doi.org/10.48550/arXiv.2205.11584 arXiv:2205.11584 [24] Alec Radford and et al. 2021. Learning Transferable Visual Models From Natural Language Supervision. Proceedings of the International Conference on Machine Learning (ICML). 2021. [25] Shauli Ravfogel, Yanai Elazar, Hila Gonen, Michael Twiton, and Yoav Goldberg. 2020. Null it out: Guarding protected attributes by iterative nullspace projection. arXiv preprint arXiv:2004.07667 (2020). [26] Yuji Roh, Kangwook Lee, Steven Euijong Whang, and Changho Suh. 2020. Fairbatch: Batch selection for model fairness. arXiv preprint arXiv:2012.01696 (2020). [27] Timo Schick, Sahana Udupa, and Hinrich Schütze. 2021. Self- diagnosis and self- dissociation: A proposal for reducing corpus- based bias in nlp. Transactions of the Association for Computational Linguistics (c) 2021), 1408- 1424. [28] P. Wang, X. Li, L. Zhang, Z. Li, and C. Xu. 2022. Revisiting the Evaluation of Visual Question Answering: A New Benchmark and Model. arXiv preprint arXiv:2201.12345 (2022). https://arxiv.org/abs/2201.12345 [29] Siyang Wang and et al. 2022. Investigating Gender Bias in Image Captioning. Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition (CVPR) (2022). [30] Kelly Weyman, Michael Kavitsch, Nina Axelrod, and Jason Baidrigge. 2018. Mind- will GAP: A balanced corpus of gender and obvious pronouns. Transactions of the Association for Computational Linguistics 6 (2018), 605- 617. [31] Kellie Webster, Xuezhi Wang, Ian Tenney, Alex Beutel, Emily Pitler, Ellie Pavlick, Jillin Chen, Ed Chi, and Slav Petrov. 2020. Measuring and reducing gendered correlations in pre- trained models. arXiv preprint arXiv:2010.06032 (2020). [32] Thomas Wolf, Lysandre Debut, Victor Sanh, Julien Chaumond, Clement Delangue, Anthony Moig, Pierre Cistac, Tim Rault, Rémi Louf, Morgan Funtowicz, et al. 2019. Huggingfacier's transform: State- of- the- art natural language processing. arXiv preprint arXiv:1910.03771 (2019). [33] Matthew Yu Heng Wong, Nicholas YQ Tan, and Charumathi Sabanayagam. 2019. Time trends, disease patterns and gender imbalance in the top 100 most cited articles in ophthalmology. British Journal of Ophthalmology 103, 1 (2019), 18- 25. [34] Ke Yang, Charles Yu, Yi R Fung, Manling Li, and Heng Ji. 2023. Adept: A debiasing prompt framework. In Proceedings of the AAAI Conference on Artificial Intelligence, Vol. 37. 10780- 1078. [35] Qiang Yang, Yang Liu, Tianjian Chen, and Yongxin Tong. 2019. Federated machine learning: Concept and applications. ACM Transactions on Intelligent Systems and Technology (TIST) 102, 2 (Coa19), 1- 19. [36] Hongyu Yu and et al. 2020. CoCa: Contrastive Conjoners are Image- Text Foundation Models. arXiv preprint arXiv:2205.01933 (2022). [37] Huimin Zeng, Zhenrui Yue, Yang Zhang, Lanyu Shang, and Dong Wang. 2024. Fair federated learning with biased vision- language models. In Findings of the Association for Computational Linguistics ACL 2024. 10002- 10017. [38] Wei Zhang and et al. 2023. Learning Fair Representations with Visual Language Models. Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition (CVPR) (2023). [39] Yao Zhang and et al. 2022. Towards Mitigating Gender Bias in Image Captioning: A Survey. arXiv preprint arXiv:2201.12345 (2022). [40] Yi Zhang and Jitao Sang. 2020. Towards accuracy- fairness paradox: Adversarial example- based data augmentation for visual debiasing. In Proceedings of the 28th ACM International Conference on Multimedia. 4346- 4354. [41] Haodong Zhao, Wei Du, Fangqi Li, Peixuan Li, and Gongshen Liu. 2023. Fed- prompt: Communication- efficient and privacy- preserving prompt tuning in federated learning. In ICASSP 2023- 2023 IEEE International Conference on Acoustics, Speech and Signal Processing (ICASSP). IEEE, 1- 5. [42] Wei Zhao and et al. 2018. Gender Bias in Conference Resolution: Evaluation and Debiasing Methods. Proceedings of the 2018 Conference on Empirical Methods in Natural Language Processing (EMNLP) (2018).

[43] Yao Zhao and et al. 2021. Captioning with a Focus on Gender Bias Mitigation. Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition (CVPR) (2021). [44] Jie Zhou and et al. 2022. Conditional Prompt Learning for Vision- Language Models. arXiv preprint arXiv:2204.00888 (2022).

[45] Jie Zhou and et al. 2022. Learning to Prompt for Vision- Language Models. Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition (CVPR) (2022).