# Conclusion

In this paper, we present Personalized Federated Graph Neural Architecture Search (PFGNAS), a novel approach for personalized federated graph learning. PFGNAS uses LLMs as controllers to design high- performance GNNs collaboratively. We introduce prompt learning to optimize federated graph architecture search using historical performance and strategies. A weight- sharing supernet ensures consistency across personalized models for different clients. Extensive experiments on three datasets show that PFGNAS outperforms baseline methods. In future work, we will emphasize the interpretability of LLMs in the context of federated architecture search and leveraging gradient information to enhance their search capabilities.