# Hyper-modal Imputation Diffusion Embedding with Dual-Distillation for Federated Multimodal Knowledge Graph Completion

<PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, Fellow, IEEE

Abstract- With the increasing multimodal knowledge privatization requirements, multimodal knowledge graphs in different institutes are usually decentralized, lacking of effective collaboration system with both stronger reasoning ability and transmission safety guarantees. In this paper, we propose the Federated Multimodal Knowledge Graph Completion (FedMKGC) task, aiming at training over federated MKGs for better predicting the missing links in clients without sharing sensitive knowledge. We propose a framework named MMFeD3- HidE for addressing multimodal uncertain unavailability and multimodal client heterogeneity challenges of FedMKGC. (1) Inside the clients, our proposed Hyper- modal Imputation Diffusion Embedding model (HidE) recovers the complete multimodal distributions from incomplete entity embeddings constrained by available modalities. (2) Among clients, our proposed Multimodal FeDerated Dual Distillation (MMFeD3) transfers knowledge mutually between clients and the server with logit and feature distillation to improve both global convergence and semantic consistency. We propose a FedMKGC benchmark for a comprehensive evaluation, consisting of a general FedMKGC backbone named MMFeD3, datasets with heterogeneous multimodal information, and three groups of constructed baselines. Experiments conducted on our benchmark validate the effectiveness, semantic consistency, and convergence robustness of MMFeD3- HidE.

Index Terms- Multimodal Knowledge Graphs, Federated Learning, Multimodal Learning.

# I. INTRODUCTION

Multimodal knowledge graphs (MKGs) [1], [2] organize graph structures composed of relational triples (head entity, relation, tail entity) and their visual and textual attributes as Figure 1, which have been widely in multimodal knowledge- intensive tasks [3]- [6]. Due to incomplete construction and new knowledge emergence, Multimodal Knowledge Graph Completion (MKGC) task [7], [8] has been widely- explored [9]- [12] to reason the missing links in MKGs with multimodal information. For example, in Figure 1, given a query (Kobe Bryant, Team member,?), MKGC aims to predict the tail entity L.A. Lakers with graph structures, entity images, and descriptions.

In real world, the MKGs are usually decentralized in different institutes due to commercial interests or data regulations, such as open- sourced MKGs DBpedia [13], Wiki

![](images/f3294e4f33fe9c899f7806ccf76c11e6afca2e0e3db35fed142d396cfa5eb749.jpg)  
Fig. 1. Toy example of the FedMKGC task for decentralized MKGs. The client MKGs have uncertain unavailable modalities (slash shadowed) and heterogeneous multimodal semantics.

data [14], Freebase [15], and domain- specific MKGs in e- commerce [16], [17] or medical health [18]. To address the data- isolation problem, Federated Learning [19]- [21] (FL) has been proposed for enabling cooperatively training a strong global model without data transmission. However, the federated learning on multimodal knowledge graphs, particularly concerning privacy- sensitive graph structures, entity images, and entity descriptions, remains an underexplored area. Existing Federated KGC approaches [22]- [25] mainly focus on modeling graph structures in unimodal KGs, while MKGC approaches [9], [10], [12], [26] all focus on centralized MKGs with full data availability, leaving the collaboration of federated MKGs unexplored. Despite the great progress of existing federated multimodal learning studies [27]- [29], they cannot address the graph structure modality and cross- silo knowledge challenges in federated institutes.

In this paper, we propose the Federated Multimodal Knowledge Graph Completion (FedMKGC) task, aiming at training a global model to complete the missing links in all client MKGs for better overall MKGC performance while preventing decentralized multimodal information transmission. Specifically, as shown in Figure 1, there is a set of client MKGs and a server, where the structural, visual, and textual modalities in client MKGs preserve different aspects of knowledge for the same entity Kobe Bryant. The federated learning over cross- institute MKGs could securely obtain a more comprehensive representation for entity Kobe Bryant to improve the local reasoning. The federated learning on MKGs provides great potential in the real world for addressing increasing multimodal knowledge privatization and secure sharing requirements.

It is non- trivial to propose address the FedMKGC task due to its following novel challenges:

(I) Multimodal uncertain unavailability without recon

struction supervision. In the real world, exclusive knowledge, such as intimate relations, sensitive images, and personal descriptions, can be fragmented in client MKGs and thus unavailable to other MKGs. Incomplete MKG construction [30], [31] also leads to modality unavailability. Thus, the federated MKGs face uncertain missing modalities problem, i.e., one or two of the textual or visual modalities are randomly unavailable. which results in inconsistent multimodal semantics in client MKGs and hinders the reasoning. Existing incomplete multimodal learning approaches [32]- [36] usually reconstruct the multimodal representations for further application. However, they cannot be directly applied to FedMKGC since they usually require modality- complete samples for training, while the missing modalities of certain entities are inherently unavailable for both training and inference. It poses a lack of reconstruction supervision challenge of uncertain missing modalities for FedMKGC.

(2) Multimodal client heterogeneity with inconsistent semantics. The knowledge semantics in client MKGs are usually non-identically distributed. For example, in Figure 1, though three client MKGs all have entity Kobe Bryant, they store different aspects of his knowledge from varying neighbors, descriptions, and images. In terms of structural modality, client MKGs have different relational schemas [22], leading to their heterogeneous topologies and structures. In terms of visual and textual modalities, their semantics are also diverse between clients due to different knowledge coverage and concentrations of institutes. The multimodal uncertain unavailability mentioned above also increases client heterogeneity. The multimodal heterogeneity poses a challenge for robust global convergence [37]-[39] for FedMKGC.

To address above challenges, we propose a novel FedMKGC framework MMFeD3- HidE, Hyper- modal imputation diffusion Embedding with Multi- Modal FeDerated Dual Distillation. Firstly, inspired by missing data imputation research [40], [41], we formulate the incomplete multimodal entity embeddings as hyper- modal data vectors consisting of all modalities and having inherent missing data points. This way, the available modalities could lead the missing ones to learn from the same reconstruction distributions to maintain semantic consistency and multimodal integrity. We exploit the diffusion model to capture the distribution of incomplete hyper- modalities and recover the complete ones from additional Gaussian noises stepwise, and propose to maximize masked variational bound to provide supervision from available modalities. Secondly, we propose MMFeD3 to mutually transfer knowledge between clients and the server through logit and feature distillation. The feature distillation further improves semantic consistency by bringing the imputed entity embeddings in the client model and the incomplete ones in the server model closer, while the logit distillation improves the convergence robustness through mutual enhancement in the decision level.

To thoroughly assess the FedMKGC performance, we propose the FedMKGC benchmark, consisting of federated MKG datasets, basic backbone MMFedE, and three groups of baselines. Extensive experiments conducted on our benchmark validate the advantages of MMFeD3- HidE over other baselines, including effectiveness, semantic consistency, convergence stability, and efficiency. The contributions of our paper can be summarized as follows:

To the best of our knowledge, we are the first to study the FedMKGC task and address its uncertain missing modalities and multimodal client heterogeneity challenges. We propose a novel framework MMFeD3- HidE with hyper- modal imputation diffusion embedding for missing modality reconstruction, and federated logit- feature dual- distillation for addressing above challenges. We construct the FedMKGC benchmark with datasets, backbone, and baselines. Experiments demonstrate the effectiveness, semantic consistency, and convergence efficiency of MMFeD3- HidE.

The paper is organized as follows: Section II introduces related works on FedMKGC, Section III demonstrates the FedMKGC task formulation and benchmark construction, Section IV presents our proposed method to address the two challenges, Section V describes the experiments for validation, and Section VI presents the conclusion of the paper.

# II. RELATED WORK

Incomplete Multimodal Learning (MML). Incomplete MML studies reconstruct the Generative- based approaches impute the missing modalities with GAN- based [42], [43], AE- based [44]- [46], flow- based [47] networks to generate a single missing modality. Reconstruction- based approaches obtain complete modalities with AE- based [32], GNN- based [48], Transformer- based [33]- [35], [49], or Diffusion- based [36] reconstruction networks. Distillation- based approaches [50], [51] distill full- modal knowledge of teacher model to student model with missing modalities. They all require ground- truths of the missing modalities to train the model and transfer to the test phase, leaving supervision of inherent missing modalities in both training and testing phases in MKG entities untackled.

Federated Multimodal Learning. Federated MML studies aim to jointly train a model with decentralized multimodal clients, which has great improvements in diverse tasks including vision- language understanding [28], [52], [53], Health [54], [55], IoT [39], [56], Human Activity Recognition [29]. FedMultimodal benchmark [27] includes tasks such as Emotion Recognition (ER) [57], [58], Multimodal Action Recognition (MAR) [59], [60], Social Media (SM) [61], [62], as shown in Table I. Existing studies neglect graph structure modality that is locally learned and distinguished from other modalities, and cross- silo setting [21], [63] that has fewer

TABLEI OVERVIEW OF FEDERATED MULTIMODAL LEARNING DATASETS.  

<table><tr><td>Task</td><td>Dataset</td><td>FL setting</td><td>Modalities</td><td>Total Instance</td></tr><tr><td>ER</td><td>MELD 
CREMA-D</td><td>cross-device 
cross-device</td><td>Audio,Text 
Audio,Video</td><td>9,718 
4,798</td></tr><tr><td>MAR</td><td>UCF101 
MiT10 
MiT51</td><td>cross-device 
cross-device 
cross-device</td><td>Audio, Video 
Audio, Video 
Audio, Video</td><td>6,837 
41.6K 
157.6K</td></tr><tr><td>SM</td><td>Hateful-Memes 
CrisisMMD</td><td>cross-device 
cross-device</td><td>Image, Text 
Image, Text</td><td>10.0K 
18.1K</td></tr><tr><td>MKGC</td><td>FedMKGC</td><td>cross-silo</td><td>Structure, 
Image, Text</td><td>Entity:14,541 
Triple: 310,116</td></tr></table>

client counts and more local data than cross- device setting. Moreover, Federated MML studies also investigate the real- world noises, such as data corruption [64], label corruption [27], and missing modalities [54], [65], [66], in which the uncertain missing modality problems, especially those without supervision during training, are not explored.

Multimodal Knowledge Graph Completion. Multimodal knowledge graph completion (MKGC) [7], [8] aims to complete the missing links in MKGs with multimodal information. They [9]- [12], [26], [67], [68] extend the unimodal knowledge graph embeddings [69]- [71] with the textual and visual embeddings under the assumption of complete modalities. For the occasionally missing modality in the datasets, they usually pad them with zero or random values. MACO [30] and AdaMF [31] highlight the missing multimodal information in MKGC and propose GAN- based architectures to generate the missing visual or text modality. However, they focus on the single- modal missing situation and cannot handle uncertain missing modalities. In our paper, we focus on the uncertain missing modalities problem, where one or two of the visual and textual modalities could be randomly missing.

Moreover, existing MKGC methods [9]- [12], [26], [67] all focus on centralized settings where all entities, relations, triples, and visual/textual modalities are available and unified. To achieve jointly training of cross- institute MKGs with untransmittable fragmented knowledge, our paper proposes to study the FedMKGC task, where relational triples, entity images, and descriptions are decentralized and jointly trained through federated dual- distillation.

Federated Knowledge Graph Completion. Existing federated KGC methods mainly focus on preserving private structural information by preventing relation embeddings from sharing and learning global structural entity embeddings [22]. FedE [22] proposes to extend FedAvg [20] and average aggregate entity embeddings, accomplished with permutation matrices to map the server entity embeddings to the client ones. FedEC [23] further augment FedE with a contrastive learning objective to constrain the embedding updates. FedLU [24] proposes logit distillation between server and clients to address the structural heterogeneity problems. Some FedKGC methods focus on other problems such as peer- to- peer setting [72], unlearning [24], cross- domain [73], global relation embeddings [74], attacks [75], [76], and defends [77].

In our paper, we focus on the FedMKGC task to focus on multimodal federated learning including structural, visual, and textual modalities. The multimodal uncertain unavailability and multimodal client heterogeneity problems in FedMKGC are also non- explored by FedKGC studies.

# III. FEDMKGC TASK AND BENCHMARK

# A. Federated MKGC Task Formulation

The federated multimodal knowledge graph completion (FedMKGC) task focuses on conducting MKGC on all client MKGs without data sharing, where there is a set of client MKGs  $\mathcal{C} = \{\mathcal{G}^c\}$  and a central server. An MKG is denoted as  $\mathcal{G}^c = \{\mathcal{E}^c,\mathcal{R}^c,\mathcal{T}^c,\mathcal{V}^c,\mathcal{D}^c\}$  with entity set  $\mathcal{E}^c$  relation set  $\mathcal{R}^c$  triple set  $\mathcal{T}^c$  that denotes the relation between entities  $\{(h,r,t)\} \subseteq \mathcal{E}^c\times \mathcal{R}^c\times \mathcal{E}^c$  entity image set  $\mathcal{V}^c$  and Input : Client set  $\mathcal{C} = \{\mathcal{G}^c\}$  permutation mapping matrix  $\mathbf{P}$  existence vector  $\mathbf{V}$  communication rounds  $R$  1 Server initialize  $\mathbf{S}_0^8$ $\mathbf{W}_{v,0}$ $\mathbf{W}_{d,0}$  2 for ro in  $R$  do 3 Sample a client subset  $\mathcal{C}_{ro}\subseteq \mathcal{C}$  4 for  $\mathcal{G}^c\in \mathcal{C}_{ro}$  do 5 Server distributes  $\mathbf{S}_{o}^{c} = \mathbf{P}^{c}\mathbf{S}_{ro}^{s}$ $\mathbf{W}_{v,ro}$ $\mathbf{W}_{d,ro}$  6  $\mathbf{S}_{ro + 1}^{c}$ $\mathbf{W}_{v,ro + 1}^{c}$ $\mathbf{W}_{d,ro + 1}^{c}$ $\mathbf{W}_{v,ro + 1}^{c}$  7 Client uploads  $\mathbf{S}_{ro + 1}^{c}$ $\mathbf{W}_{v,ro + 1}^{c}$ $\mathbf{W}_{d,ro + 1}^{c}$  8 end 9  $\begin{array}{r}\mathbf{S}_{ro + 1}^{s} = (\mathbb{1}\otimes \sum_{c_{ro}}\mathbf{v}^{c})\otimes \sum_{c_{ro}}\mathbf{P}^{c\top}\mathbf{S}_{ro + 1}^{c}; \end{array}$  10 for  $m\in \{v,d\}$  do 11  $\begin{array}{r}\mathbf{W}_{m,ro + 1}^{c} = \sum_{c_{ro}}\alpha^{c}\mathbf{W}_{m,ro + 1}^{c}, \end{array}$  12 end 13 end

# Algorithm 1: MMFedE Backbone

entity description set  $\mathcal{D}^c$  .In each client  $\mathcal{G}^c$  given a query  $q = (h,r,?)$  with head entity  $h$  and relation  $r$  the model aims to rank all the entities in client MKG  $e\in \mathcal{E}^c$  with a score function  $f(h,r,t)\to \mathbb{R}^{\lfloor \mathcal{E}^c\rfloor}$  and make the true tail entity  $t$  the highest score. The head prediction is formed as tail prediction  $(t,r^{- 1},h)$  [69] for unified modeling. The client multimodal features  $E_{m}^{c},m\in \{v,d\}$  are extracted from fixed pretrained encoders BERT [78] and ViT [79]. Main notations used in this paper are shown in Appendix A.

# B. Non-IID MKG Partition

Since there is no federated MKG dataset, we partition the existing MKG to create authentic non- IID distributions similar to real- world MKGs. (1) MKG triple partition with relation IDs [22], [27] that clients have no overlapping relation types. We construct the FedMKGC benchmark based on the FB15K- 237 dataset [69], which has sufficient multimodal information and is widely adopted [10], [11], [26]. The common partition scheme is to partition data through unique client identifier [21], [27] to form non- IID distributions, such as participant IDs. Migrating to the MKGs, the relation IDs are commonly used for partitioning triples between clients [22], [24]. Thus, we follow them to partition triples in clients based on relation IDs. This way, the client KGs concentrate on different relation types, ensuring that the relational schemas and topologies in the client MKGs are heterogeneous.

(2) MKG multimodal information partition following Dirichlet distribution [27], [80] that one entity in different clients has different descriptions and images. Unlike triples, the images and descriptions of an entity do not have client identifiers, thus we need to synthesize the non-IID visual and textual partitions. We first split the paragraph of entity descriptions into multiple descriptive sentences and crawl multiple images for each entity. Then we label the sentences and images with different IDs and partition them following Dirichlet distribution [27], [28], [80] with  $\alpha_{Dir} = 0.1$  for high heterogeneity. After partition, clients have diverse descriptions and images for the same entity.

![](images/faca300bbc6d4571b65bf416ccec8e22d60313f2f9fbcbb7cd46c3a951f3e013.jpg)  
Fig. 2. The overview of our MMFeD3-Hide.  $\mathbf{S}^c$ $\mathbf{V}^c$ $\mathbf{D}^c$  represent structural, visual, and textual modalities in client MKGs, where shadowed blocks represent missing. The Hide imutes the missing modalities of entities with diffusion imputation. The MMFeD3 optimizes the federated MKGC with dual distillation objectives and KGC objectives of clients and server.

# C. Missing Modalities Construction

We randomly generate the modality availability mask of all the entities in  $\mathcal{E}^c$  for each client following Bernoulli distribution [27], [66] with an availability rate of  $r$ . We exploit the multimodal feature mask  $M_{m}^{c} \in \mathbb{R}^{|E^{c}| \times d_{m}}$  to represent availability of modality  $m$  in client  $c$  where  $|\mathcal{E}^{c}|$  is the number of entities in client  $c$  and  $d_{m}$  is feature dimension of modal  $m$ . That is, the  $i$ - th row  $M_{m,(i, \cdot)}^{c} = \mathbf{1}$  if the  $i$ - th entity has available modality  $m$  otherwise  $M_{m,(i, \cdot)}^{c} = \mathbf{0}$ . We assign different masks for visual and text modality and for different clients. The features of unavailable entity images or descriptions are randomly padded [10], [11], [31].

# D. Federated MKGC Backbone

We propose a general backbone named MMFedE with basic average aggregation idea [20], [22]. MMFedE aggregates hybrid structural entity embeddings and visual/textual projection weights. The structural entity embeddings are locally learned and do not expose privacy [22], while extracted visual and textual features from pretrained models are preserved in clients for communication efficiency and data protection. The pipeline of MMFedE is shown in Algorithm 1.

Client end. Clients are trained on local MKGC objectives. We denote the fusion method as  $\Phi (\cdot)$  and the fused multimodal entity embeddings can be denoted as  $\mathbf{E}^{c} = \Phi (\mathbf{S}^{c}, \mathbf{V}^{c}, \mathbf{D}^{c})$  where  $\mathbf{V}^{c} = \mathbf{W}_{v}^{c} E_{v}^{c}$  and  $\mathbf{D}^{c} = \mathbf{W}_{a}^{c} E_{a}^{c}$  are mapped visual and textual embeddings. The probability of target entity  $t$  among the negative entities is as Equation (1):

$$
\begin{array}{rl} & p^{c}(t|(h,r)_{i}) = \mathrm{softmax}(f^{c}(h,r,e)),e\in \{t,N_{i}\} ,\\ & \qquad = \mathrm{softmax}(f^{c}(h,r,e;\mathbf{E}^{c},\mathbf{R}^{c})), \end{array} \tag{1}
$$

where  $\mathbf{R}^{c}$  is the relation embeddings of client  $c$ $N_{i}$  is the negative entity set of  $i$ - th triple. The score function  $f(\cdot)$  could be any KG embedding (KGE) decoder [69]- [71]. The KGC objective is cross- entropy loss as Equation (2). At test time, clients reason with Equation (1) that  $\hat{e} = \arg \max p^{c}(e|(h,r)_{i}; \mathbf{E}^{c}, \mathbf{R}^{c}), e \in \mathcal{E}^{c}$  as predicted entity.

$$
\mathcal{L}_{KGC}^{c} = \sum_{(h,r,t)_{i} \in \mathcal{G}^{c}} - \log [p^{c}(t|(h,r)_{i})] \tag{2}
$$

Server end. At each communication round  $ro$ , the server first samples a client subset  $\mathcal{C}_{ro}$  to collaborate. MMFedE distributes both global structural entity embeddings  $\mathbf{S}_{ro}^{S}$  and multimodal feature projection weights  $\mathbf{W}_{m,ro}, m \in \{v, d\}$ . After the local training, the clients update the local structural embeddings  $\mathbf{S}_{ro + 1}^{c}$  and multimodal weights  $\mathbf{W}_{m,ro}^{c}$  to the server. MMFedE hybridly aggregates the structural embeddings with entity existence ratio  $\mathbb{Q} \sum_{c_{ro}} \mathbf{v}^{c}$  as weights [22], and the multimodal projection matrices with triple ratio  $\alpha^{c} = \frac{|T^{c}|}{\sum_{c} |T^{c}|}$  as weights [20].

# IV. METHOD

The framework of MMFeD3- Hide is shown in Figure 2. The algorithm illustration is in App. B.A. We first reconstruct the incomplete multimodal entity embeddings with hypermodal diffusion imputation, optimized with  $\mathcal{L}_{DI}$ . Then we optimize the federated client MKGs with MMFeD3, including logit distillation  $\mathcal{L}_{LD}$  and feature distillation  $\mathcal{L}_{FD}$ .

# A. Hyper-modal Construction

Inspired by missing data imputation research [40], [41], we treat multimodal embeddings of an entity as a hyper- modal data vector consisting of all modalities and have some inherent missing data points.

We define the hyper- modality feature as the concatenation of the multimodal embeddings in structural vector space as  $\mathbf{H}^{c} = [\mathbf{S}^{c}||\mathbf{V}^{c}||\mathbf{D}^{c}]$ . Since the structural modal embeddings are learned based on the structures inside the client MKG,  $\mathbf{S}^{c}$  is the only fully available modality. Thus, we employed the pre- trained FedE structural embeddings to initialize  $\mathbf{S}^{c}$  for guiding the hyper- modal imputation.

# B. Imputation Diffusion Model Processes

Diffusion models [81]- [83] typically consist of two processes, i.e., forward process and reverse process. Given the input incomplete hyper- modal feature  $\mathbf{x}_{0} = \mathbf{H}^{c}$  as the original state, the forward process corrupts  $\mathbf{x}_{0}$  and constructs a series of latent variables  $\mathbf{x}_{1:T}$  in the Markov chain by adding Gaussian noises gradually in  $T$  steps. In the reverse process, the reconstruction network learns to recover the  $\hat{\mathbf{x}}_{t - 1}$  given  $\mathbf{x}_{t}$ , which starts from  $\mathbf{x}_{T}$  and finally recovers the  $\hat{\mathbf{x}}_{0}$  and the output  $\hat{\mathbf{x}}_{0}$  is the recovered complete hyper- modal feature. In

the end, we impute the  $\hat{\mathbf{x}}_0$  to the missing points of  $\mathbf{x}_0$  as imputed hyper- modal features  $\hat{\mathbf{x}}$ .

Forward process. We add the Gaussian noise to  $\mathbf{x}_0$  as a Markov chain to form  $\mathbf{x}_{t:T}$  according to a variance schedule  $\beta_{1},\ldots ,\beta_{T}$ , where  $t\in \{1,\ldots ,T\}$  denotes diffusion step and  $\beta_{t}$  denotes variance at step  $t$ . The forward process is denoted as Equation (3),  $\mathcal{N}$  denotes Gaussian distribution.

$$
q(\mathbf{x}_t|\mathbf{x}_{t - 1}) = \mathcal{N}(\mathbf{x}_t;\sqrt{1 - \beta_t}\mathbf{x}_{t - 1},\beta_t\mathbf{I}) \tag{3}
$$

The forward process can be reparameterized to sample  $\mathbf{x}_t$  at an arbitrary time step  $t$  [82] as Equation (4), where  $\alpha_{t} = 1 - \beta_{t}$  and  $\bar{\alpha}_{t} = \Pi_{t - 1}^{t}\alpha_{t}$

$$
q(\mathbf{x}_t|\mathbf{x}_0) = \mathcal{N}(\mathbf{x}_t;\sqrt{\bar{\alpha_t}}\mathbf{x}_0,(1 - \bar{\alpha_t})\mathbf{I}) \tag{4}
$$

Thus we can further reparameterize the latent variable in each step as  $\mathbf{x}_t(\mathbf{x}_0,\epsilon) = \sqrt{\bar{\alpha_t}}\mathbf{x}_0 + \sqrt{1 - \bar{\alpha_t}}\epsilon$ , where  $\epsilon \sim \mathcal{N}(0,\mathbf{I})$ . In implementation, we ignore the trainable  $\beta_{t}$  by reparameterization and fix  $\beta_{t}$  as constants for simplicity [82], linearly increasing from  $\beta_{1} = \beta_{low}$  to  $\beta_{T} = \beta_{up}$  where  $\beta_{low}$  and  $\beta_{up}$  are hyper- parameters.

Reverse process. In the reverse process, we recover the complete hyper- modal features starting from  $\mathbf{x}_T$ , by utilizing the reconstruction network to approximate the latent variable states as Equation (5), where  $\mu_{\theta}(\mathbf{x}_t,t)$  and  $\Sigma_{\theta}(\mathbf{x}_t,t)$  is the Gaussian mean and variance parameters outputted by the reconstruction networks with learnable parameter  $\theta$ . We employ Cascade Residual Autoencoder (CRA) [32], [46] as the reconstruction network  $\theta$  for its superior modality reconstruction ability. We simply take the mean of the distribution  $\hat{\mathbf{x}}_{t - 1} = \mu_{\theta}(\mathbf{x}_t,t)$  as the predicted variable at previous step. Finally, the predicted  $\hat{\mathbf{x}}_0$  are seen as the recovered complete hyper- modal features.

$$
p_{\theta}(\mathbf{x}_{t - 1}|\mathbf{x}_t) = \mathcal{N}(\mathbf{x}_{t - 1};\mu_{\theta}(\mathbf{x}_t,t),\Sigma_{\theta}(\mathbf{x}_t,t)) \tag{5}
$$

Imputation process. To fully utilize the existing modalities, we conduct the imputation process to interpolate the generated  $\hat{\mathbf{x}}_0$  into the missing slots of the original hypermodal matrix  $\mathbf{x}_0 = \mathbf{H}^c$  to obtain the imputed hyper- modal  $\hat{\mathbf{x}}_0$  as Equation (6), where the  $M^c$  guarantees the preservation of the available modalities and  $\odot$  is the elementwise product. We construct the multimodal mask of client  $c$  as  $M^c = [M_s^c ||M_v^c ||M_d^c ]$ , where the feature positions of missing modalities are 0 and those of available modalities are 1. The imputed  $\hat{\mathbf{H}}^c$  can be seen as the concatenation of three modal embeddings, where  $\hat{\mathbf{V}}^c$  and  $\hat{\mathbf{D}}^c$  are imputed complete visual and textual entity embeddings. Then we could construct the complete multimodal entity embeddings as  $\hat{\mathbf{E}}^c = \Phi (\hat{\mathbf{S}}^c,\hat{\mathbf{V}}^c,\hat{\mathbf{D}}^c)$

$$
\begin{array}{rl} & {\hat{\mathbf{H}}^c = \check{\mathbf{x}}_0 = M^c\odot \mathbf{x}_0^c +(1 - M^c)\odot \hat{\mathbf{x}}_0^c}\\ & {\qquad = M^c\odot \hat{\mathbf{H}}^c +(1 - M^c)\odot \hat{\mathbf{x}}_0^c}\\ & {\qquad = [\hat{\mathbf{S}}^c ||\hat{\mathbf{V}}^c ||\hat{\mathbf{D}}^c ]} \end{array} \tag{6}
$$

# C. Diffusion Imputation Optimization

Diffusion models are typically optimized by maximizing the evidence lower bound (ELBO) of the likelihood of the original state  $\mathbf{x}_0$  [83]. Based on DDPM [82], ELBO can be simplified as  $\mathbb{E}_{t,\mathbf{x}_0,\epsilon}\left[\left\| \epsilon - \epsilon_\theta (\mathbf{x}_t,t)\right\| _2^2\right]$ , where  $1\leq t\leq T$ . DDPM's reconstruction network outputs the additional noise  $\epsilon_{\theta}$  and optimizes the noise to approximate the distribution step by step. In our paper, we aim to predict the complete hyper- modal distributions from input observed modalities, thus we predict the complete hyper- modal  $\mathbf{x}_0$  at each step instead of  $\epsilon_{\theta}$ . Thus, in our HidE, the reconstruction network  $\theta$  is optimized to regulate its output  $\hat{\mathbf{x}}_{\theta}(\mathbf{x}_t,t)$  closer to  $\mathbf{x}_0$ , similarly as  $\mathbb{E}_{q(\mathbf{x}_t|\mathbf{x}_0)}\left[\left\| \hat{\mathbf{x}}_\theta (\mathbf{x}_t,t) - \mathbf{x}_0\right\| _2^2\right]$ .

![](images/a1911103395c213d4365761aa7a3f3e194a668d037fa458052f28fa3b5ff3d72.jpg)  
Fig. 3. The HidE model details, including hyper-modal construction, forward process, reverse process, and imputation.

However, there are both missing and observed data points in the diffusion processes, we need to design the objective with reconstruction supervision signals that are not affected by the missing modal points. Since the diffusion model preserves the feature size and location in both forward and backward processes, it offers a convenient solution for diffusion imputation optimization. We split the data at each step  $\mathbf{x}_t$  to observed data and missing data  $\mathbf{x}_t = \{\mathbf{x}_t^{obs},\mathbf{x}_t^{mis}\}$ , that  $\mathbf{x}_t^{obs} = M^c\odot \hat{\mathbf{x}}_t$  and  $\mathbf{x}_t^{mis} = (1 - M^c)\odot \mathbf{x}_t,1\leq t\leq T$ . We propose to constrain the diffusion of available modalities  $\mathbf{x}_t^{obs}$  to be similar to their ground- truth features, so that the missing modalities  $\mathbf{x}_t^{mis}$  from the same distributions can be reconstructed to be consistent with observed modalities. Thus the objective of diffusion imputation of each step  $t$  is as (19). We elaborate on simplifying ELBO to masked diffusion imputation in App. B.B.

$$
\begin{array}{rl} & {\mathcal{L}_t = \mathbb{E}_{q(\mathbf{x}_t|\mathbf{x}_0)}\left[\| \hat{\mathbf{x}}_\theta^{obs}(\mathbf{x}_t,t) - \mathbf{x}_0^{obs}\| _2^2\right]}\\ & {\quad = \mathbb{E}_{q(\mathbf{x}_t|\mathbf{x}_0)}\left[\| M^c\odot \hat{\mathbf{x}}_\theta (\mathbf{x}_t,t) - M^c\odot \mathbf{x}_0\| _2^2\right],} \end{array} \tag{7}
$$

Since after the optimization, the observed modalities and their reconstructed features are the same  $\hat{\mathbf{x}}_0^{obs} = \mathbf{x}_0^{obs}$ , and the reconstructed hyper- modal  $\hat{\mathbf{x}}_0\sim p_\theta (\mathbf{x}|\hat{\mathbf{x}}_0^{obs} = \mathbf{x}_0^{obs})$ , thus estimated missing features  $\hat{\mathbf{x}}_0^{mis}$  are also from the same distributions  $\mathbf{x}_0^{mis}\sim p_\theta (\mathbf{x}^{mis}|\hat{\mathbf{x}}_0^{obs} = \mathbf{x}_0^{obs})$ , which made sure the completed three modalities are from the same distributions and maintain the semantic consistency.

In implementation, we uniformly sample  $t\sim \mathcal{U}(1,T)$ . The client diffusion imputation loss is as Equation (8).

$$
\mathcal{L}_{DI}^{c} = \mathbb{E}_{t\sim \mathcal{U}(1,T)}\mathcal{L}_{t}^{c} \tag{8}
$$

# D. Federated Dual-Distillation Optimization

MMFeD3 aims to transfer knowledge between the server and clients to improve global convergence robustness, semantic consistency, and inference accuracy, consisting of feature distillation objective and logit distillation objective.

Federated KGC loss. In MMFeD3, we jointly train the global parameters with server KGC loss with the client ones for the global parameters to better generalize to all client KGs [84]. Server trains the global parameters with KGC loss  $\mathcal{L}_{KGC}^{s,c}$  similarly to Equation (2) with  $p^{s,c}(t|(h,r)_i)$  as Equation (9), where global entity embeddings  $\mathbf{E}^{s,c} = \Phi (\mathbf{S}^s,\mathbf{V}^{s,c},\mathbf{D}^{s,c})$  where  $\mathbf{V}^{s,c} = \mathbf{W}_vE_v^c$ $\mathbf{D}^{s,c} = \mathbf{W}_dE_d^c$  are obtained with the global projection weights  $\mathbf{W}_m$  and the local multimodal features  $E_{m}^{c}$ . The relation embedding  $\mathbf{R}^c$  is from local client without transmission [22].

$$
p^{s,c}(t|(h,r)_i) = \mathrm{softmax}(f(h,r,t;\mathbf{E}^{s,c};\mathbf{R}^c)) \tag{9}
$$

Federated logit distillation loss. We co- distillate the knowledge of existing modalities from the server and the knowledge of imputed modalities from clients with logit distillation [85] of KL divergence as Equation (10). The codistillation could guide the server and clients to converge to the best global and local embeddings. The overall cross- modal distillation loss is denoted as  $\mathcal{L}_{LD}^{c} = \mathcal{L}_{c2s}^{c} + \mathcal{L}_{s2c}^{c}$

$$
\left\{ \begin{array}{ll}\mathcal{L}_{c2s}^{c} = \sum_{(h,r,t)_i\in \mathcal{G}^c}D_{\mathrm{KL}}(p^{c}(t|(h,r)_i),p^{s,c}(t|(h,r)_i))\\ \displaystyle \mathcal{L}_{s2c}^{c} = \sum_{(h,r,t)_i\in \mathcal{G}^c}D_{\mathrm{KL}}(p^{s,c}(t|(h,r)_i),p^{c}(t|(h,r)_i)) \end{array} \right. \tag{10}
$$

Federated feature distillation loss The federated feature distillation objective aims to improve the expressiveness of global entity embeddings, while also guiding the hyper- modal imputation diffusion network with knowledge from global embeddings aggregated from all clients. As Equation (11), we minimize the L2 distance between the imputed full- modal client entity embeddings and available- modal server entity embeddings.

$$
\mathcal{L}_{FD}^{c} = \parallel \hat{\mathbf{E}}^{c} - \mathbf{E}^{s,c}\parallel_{2}^{2} \tag{11}
$$

# E. Overall objective

The overall objective of client  $c$  is as Equation (12), where  $\lambda$  controls  $\mathcal{L}_{DI}$  in HidE,  $\mu$  and  $\eta$  control  $\mathcal{L}_{LD},\mathcal{L}_{FD}$  in MMFeD3. We optimize all the client networks with respective  $\mathcal{L}^c$  . We keep diffusion model parameters  $\theta^c$  local. The server aggregates structural embeddings and multimodal projection weights similar to MMFeD.

$$
\mathcal{L}^c = \mathcal{L}_{KGC}^c +\mathcal{L}_{KGC}^{s,c} + \lambda \mathcal{L}_{DI}^c +\mu \mathcal{L}_{LD}^c +\eta \mathcal{L}_{FD}^c \tag{12}
$$

At test time,  $\hat{e} = \arg \max p^{c}(e|(h,r)_i;\hat{\mathbf{E}}^{c},\mathbf{R}^{c}),e\in \mathcal{E}^{c}$  at each client are predicted as the target entity for a query triple  $(h,r)_i$

# V. EXPERIMENTS

# A. Experimental Settings

Datasets. We construct the FedMKGC benchmark as shown in Section III. We augment the dataset with the entity images [9], [86] and descriptions [87] from FB15K- 237. The

TABLE II DATASET STATISTICS OF FEDERATED MKGC.  

<table><tr><td>Dataset</td><td>|C|</td><td>|Rc|</td><td>|Ec|</td><td>|Tc|</td><td>Scv</td><td>Scd</td></tr><tr><td>FB15K-237-Fed3</td><td>3</td><td>79.0</td><td>12,595.3</td><td>103,373.0</td><td>0.331</td><td>0.508</td></tr><tr><td>FB15K-237-Fed5</td><td>5</td><td>47.4</td><td>11,260.4</td><td>62,023.2</td><td>0.334</td><td>0.523</td></tr><tr><td>FB15K-237-Fed10</td><td>10</td><td>23.7</td><td>8,340.1</td><td>31,011.6</td><td>0.333</td><td>0.542</td></tr></table>

train/valid/test split ratio is 8:1:1. The dataset statistics are shown in Table II, where the client number, and the average number of relations, entities, and triples are reported. We also report the average visual and text similarity  $\overline{S}_m^c$  of the same entity between clients, with ViT embedding similarity and Jaccard similarity respectively.

Evaluation metrics. We predict both head and tail entities and take the average metrics as the client MKG metrics [69], then we report the weighted average metrics of all clients based on their triple number ratio [22]. We report four metrics, i.e. Hits@K,  $\mathrm{K} = 1$  3,10, and mean reciprocal rank (MRR). We employ the widely- used filtered evaluation setting [69] to filter out other ground- truth entities.

Implementation details. We implement our framework with PyTorch. We employ RotatE [31], [71] as KGE decoder  $f(\cdot)$ . We employ Adam [88] to optimize the framework. At each round, we sample all the clients to form  $\mathcal{C}_t$  [22]. We train the local clients for 3 epochs in each round. We early stopped the training after 5 rounds without improvement on the validation set. The batch size is 1,024. The negative sample set  $N_{i}$  is 256 randomly sampled negative entities. The embedding sizes of entity and relation are 512 and 256 [71]. The diffusion noise  $\beta_{low},\beta_{up}$  is  $5e - 4,5e - 2$  respectively, scaled with  $s = 1e - 4$  [89]. We ran our experiments on one A6000 GPU (68GB RAM).

# B. Baselines construction

We experiment with baselines from three groups: (1) MKGC baselines for  $\Phi (\cdot)$ . Average, Weighted [9], Concatenation [9], Split [10], [26], Gated [90]. We first benchmark the MKGC baselines without missing modalities, shown in App. C.B, where weighted fusion approach shows superior performance. Thus, we conduct the following experiments with weighted as default  $\Phi (\cdot)$ . (2) Federated learning (FL) baselines: MMFedE [22], MMFedEC [23], MMFedProx [37], MMFedLU [24], derived from our MMFedE backbone. (3) Incomplete multimodal learning (IMML) baselines: AE [45], CRA [46], MMIN [32], adapted with our masked objective. The details of baseline construction are in App. C.A.

Moreover, we construct the lower and upper bound of FedMKGC. The lower bound S- Ind denotes the average performance of independently trained structural models without FL and multimodal information, while MMInd denotes that of independently trained multimodal models with partitioned images or descriptions. The upper bound MMCen denotes the performance of a centralized trained global model, where all triples of client MKGs are available and multimodal information is not partitioned or missing.

# C. FedMKGC with missing modalities

As shown in Table III, we experiment with  $r = 50\%$  available modalities and construct the evaluation of FL and IMML baselines. HidE outperforms IMML baselines and MMFeD3 outperforms FL baselines on 3 datasets, demonstrating the effectiveness of our method.

Lower and upper bound: Compared to the lower bound, MMFeD3- HidE outperforms S- Ind on three datasets significantly. It demonstrates that MMFeD3- HidE could effectively

TABLE III FEDMKGC PERFORMANCE WITH UNCERTAIN MISSING ENTITY IMAGES AND DESCRIPTIONS. WE EXPERIMENT WITH  $r = 50\%$  AVAILABLE ENTITY IMAGES AND DESCRIPTIONS. THE S-INDEPENDENT AND MMCEN-WEIGHTED ARE LOWER AND UPPER BOUND, RESPECTIVELY. THE DEFAULT MULTIMODAL FUSION METHOD IS WEIGHTED FUSION.  

<table><tr><td rowspan="2">IMML</td><td rowspan="2">FL</td><td colspan="4">FB15K-237-Fed3</td><td colspan="4">FB15K-237-Fed5</td><td colspan="4">FB15K-237-Fed10</td></tr><tr><td>Hits@1</td><td>Hits@3</td><td>Hits@10</td><td>MRR</td><td>Hits@1</td><td>Hits@3</td><td>Hits@10</td><td>MRR</td><td>Hits@1</td><td>Hits@3</td><td>Hits@10</td><td>MRR</td></tr><tr><td rowspan="2">-</td><td>S-Ind</td><td>0.231</td><td>0.390</td><td>0.547</td><td>0.338</td><td>0.220</td><td>0.373</td><td>0.532</td><td>0.325</td><td>0.205</td><td>0.361</td><td>0.517</td><td>0.310</td></tr><tr><td>MMCen-weighted</td><td>0.246</td><td>0.441</td><td>0.616</td><td>0.373</td><td>0.251</td><td>0.451</td><td>0.627</td><td>0.380</td><td>0.240</td><td>0.447</td><td>0.629</td><td>0.373</td></tr><tr><td rowspan="5">AE</td><td>MMFedE</td><td>0.133</td><td>0.284</td><td>0.458</td><td>0.241</td><td>0.109</td><td>0.246</td><td>0.404</td><td>0.208</td><td>0.124</td><td>0.245</td><td>0.388</td><td>0.213</td></tr><tr><td>MMFedEC</td><td>0.135</td><td>0.289</td><td>0.459</td><td>0.244</td><td>0.111</td><td>0.247</td><td>0.409</td><td>0.210</td><td>0.124</td><td>0.238</td><td>0.382</td><td>0.211</td></tr><tr><td>MMFedProx</td><td>0.153</td><td>0.301</td><td>0.465</td><td>0.258</td><td>0.146</td><td>0.286</td><td>0.441</td><td>0.246</td><td>0.115</td><td>0.220</td><td>0.367</td><td>0.198</td></tr><tr><td>MMFedLU</td><td>0.160</td><td>0.317</td><td>0.489</td><td>0.270</td><td>0.140</td><td>0.276</td><td>0.435</td><td>0.238</td><td>0.115</td><td>0.253</td><td>0.409</td><td>0.214</td></tr><tr><td>MMFedD3</td><td>0.173</td><td>0.354</td><td>0.521</td><td>0.293</td><td>0.144</td><td>0.299</td><td>0.451</td><td>0.250</td><td>0.151</td><td>0.309</td><td>0.474</td><td>0.260</td></tr><tr><td rowspan="5">CRA</td><td>MMFedE</td><td>0.167</td><td>0.346</td><td>0.522</td><td>0.287</td><td>0.140</td><td>0.312</td><td>0.493</td><td>0.259</td><td>0.137</td><td>0.313</td><td>0.499</td><td>0.258</td></tr><tr><td>MMFedEC</td><td>0.173</td><td>0.352</td><td>0.529</td><td>0.293</td><td>0.140</td><td>0.309</td><td>0.488</td><td>0.257</td><td>0.136</td><td>0.309</td><td>0.494</td><td>0.256</td></tr><tr><td>MMFedProx</td><td>0.214</td><td>0.408</td><td>0.580</td><td>0.340</td><td>0.198</td><td>0.390</td><td>0.560</td><td>0.324</td><td>0.155</td><td>0.339</td><td>0.520</td><td>0.279</td></tr><tr><td>MMFedLU</td><td>0.072</td><td>0.347</td><td>0.554</td><td>0.242</td><td>0.140</td><td>0.304</td><td>0.472</td><td>0.253</td><td>0.087</td><td>0.316</td><td>0.504</td><td>0.232</td></tr><tr><td>MMFedD3</td><td>0.237</td><td>0.419</td><td>0.590</td><td>0.357</td><td>0.268</td><td>0.428</td><td>0.583</td><td>0.376</td><td>0.238</td><td>0.385</td><td>0.540</td><td>0.341</td></tr><tr><td rowspan="5">MMIN</td><td>MMFedE</td><td>0.157</td><td>0.327</td><td>0.505</td><td>0.274</td><td>0.135</td><td>0.304</td><td>0.481</td><td>0.251</td><td>0.121</td><td>0.290</td><td>0.472</td><td>0.239</td></tr><tr><td>MMFedEC</td><td>0.160</td><td>0.329</td><td>0.506</td><td>0.276</td><td>0.140</td><td>0.297</td><td>0.469</td><td>0.250</td><td>0.125</td><td>0.291</td><td>0.472</td><td>0.242</td></tr><tr><td>MMFedProx</td><td>0.208</td><td>0.397</td><td>0.566</td><td>0.332</td><td>0.199</td><td>0.389</td><td>0.557</td><td>0.323</td><td>0.158</td><td>0.345</td><td>0.521</td><td>0.283</td></tr><tr><td>MMFedLU</td><td>0.139</td><td>0.298</td><td>0.465</td><td>0.249</td><td>0.136</td><td>0.298</td><td>0.470</td><td>0.248</td><td>0.131</td><td>0.280</td><td>0.445</td><td>0.236</td></tr><tr><td>MMFedD3</td><td>0.233</td><td>0.413</td><td>0.581</td><td>0.353</td><td>0.269</td><td>0.431</td><td>0.589</td><td>0.378</td><td>0.237</td><td>0.392</td><td>0.548</td><td>0.343</td></tr><tr><td rowspan="5">HidE</td><td>MMFedE</td><td>0.259</td><td>0.447</td><td>0.612</td><td>0.381</td><td>0.152</td><td>0.449</td><td>0.620</td><td>0.379</td><td>0.237</td><td>0.422</td><td>0.609</td><td>0.369</td></tr><tr><td>MMFedEC</td><td>0.260</td><td>0.446</td><td>0.613</td><td>0.382</td><td>0.151</td><td>0.450</td><td>0.621</td><td>0.379</td><td>0.237</td><td>0.423</td><td>0.609</td><td>0.369</td></tr><tr><td>MMFedProx</td><td>0.258</td><td>0.446</td><td>0.613</td><td>0.380</td><td>0.155</td><td>0.450</td><td>0.618</td><td>0.381</td><td>0.239</td><td>0.424</td><td>0.609</td><td>0.370</td></tr><tr><td>MMFedLU</td><td>0.255</td><td>0.431</td><td>0.601</td><td>0.372</td><td>0.146</td><td>0.441</td><td>0.616</td><td>0.372</td><td>0.239</td><td>0.436</td><td>0.609</td><td>0.366</td></tr><tr><td>MMFedD3</td><td>0.269</td><td>0.449</td><td>0.615</td><td>0.387</td><td>0.260</td><td>0.450</td><td>0.622</td><td>0.382</td><td>0.246</td><td>0.439</td><td>0.614</td><td>0.372</td></tr></table>

utilize the federated learning and the partially available multimodal information to jointly learn better embeddings for MKGC. Compared to the upper bound, MMFeD3- HidE also outperforms MMCen- weighted on three datasets. The reason could be that MMFeD3- HidE is effective in imputing the uncertainly unavailable modalities to further improve FedMKGC performance.

FL methods comparison: MMFeD3 outperforms MMFedE, MMFedEC, MMFedProx, and MMFedLU on three datasets and with different incomplete multimodal learning methods, demonstrating the effectiveness of MMFeD3. With different multimodal learning methods, MMFeD3 codistillates knowledge between predictions and embeddings of clients with reconstructed multimodal and the server with existing modalities. The reason could be that MMFeD3 is effective in guiding all the reconstruction networks, as well as guiding the global prediction accuracy. Moreover, the dual distillation method could effectively deal with the heterogeneity between clients for stable global convergence to better performance.

IMML methods comparison: HidE generally outperforms AE, CRA, and MMIN with different FL approaches on 3 datasets. The reason could be that they lack an effective utilization of available modalities. Our HidE made full use of existing modalities by reserving them in the final  $\hat{\mathbf{E}}^c$  through imputation and step- wise diffusion supervision. Moreover, the original reconstruction- based approaches in their paper [32], [46] are optimized with ground- truth features, which could be constrained by our application scenario that can only exploit the self- supervision from the existing modalities inside the entity. Without the supervised signal from the ground- truth feature, our proposed imputation diffusion model is beneficial for incomplete multimodal learning. Moreover, the diffusion model rebuilds the complete modalities step by step with step- wise supervision, ensuring the reconstruction stability and semantic consistency, leading to better performance.

# D. Data Ablation Study

We conducted the data ablation study on MMFeD3- HidE as shown in Table IV.

Complete v.s. Incomplete Multimodal: In the full multimodal group where  $r = 100\%$ , MMFeD3 outperforms MMFedE by  $1.8\%$  MRR with partitioned multimodal information, demonstrating the effectiveness of MMFeD3. In the uncertain incomplete multimodal group where  $r = 50\%$ , MMFeD3- HidE also outperforms MMFedE by  $2.3\%$  MRR. Moreover, after imputation, the performance of MMFeD3- HidE is similar to MMFeD3 without unavailable modalities, demonstrating the effectiveness of HidE for imputing the missing modalities.

Modality ablation: Comparing  $S + T + V$  with  $S + rD$  and  $S + rV$ , the ablation of visual or textual modality brings little performance drop, demonstrating the stability of MMFedE and MMFeD3 in incorporating multiple modalities. Moreover, the weighted fusion method may be too naive to promote reasoning ability from multimodal complementary, indicating future investigation directions of effective multimodal fusion methods for FedMKGC.

Multimodal partitioned v.s. non- partitioned: The performance of MMFeD3- HidE with partitioned information is similar to that with non- partitioned one, which shows the robustness of our approach under heterogeneous non- IID multimodal

TABLE IV MODALITY ABLATION RESULTS ON FB15K-237-FED3. S, D, AND V REPRESENT STRUCTURAL, TEXTUAL, AND VISUAL MODALITIES, RESPECTIVELY. MM Partitioned Denotes wHERHE ENTITY TEXTS AND IMAGES BETWEEN CLIENTS ARE DIFFERENT.  $r$  REPRESENT THE VISUAL/TEXTUAL AVAILABLE RATE.  

<table><tr><td rowspan="2">r</td><td rowspan="2">Model</td><td rowspan="2">MM Partitioned</td><td colspan="4">S + rD</td><td colspan="4">S + rV</td><td colspan="4">S + rD + rV</td></tr><tr><td>Hits@1</td><td>Hits@3</td><td>Hits@10</td><td>MRR</td><td>Hits@1</td><td>Hits@3</td><td>Hits@10</td><td>MRR</td><td>Hits@1</td><td>Hits@3</td><td>Hits@10</td><td>MRR</td></tr><tr><td rowspan="4">100%</td><td rowspan="2">MMFedE</td><td>✘</td><td>0.241</td><td>0.441</td><td>0.614</td><td>0.369</td><td>0.246</td><td>0.441</td><td>0.611</td><td>0.372</td><td>0.243</td><td>0.443</td><td>0.614</td><td>0.371</td></tr><tr><td>✓</td><td>0.239</td><td>0.442</td><td>0.615</td><td>0.369</td><td>0.242</td><td>0.437</td><td>0.608</td><td>0.368</td><td>0.237</td><td>0.437</td><td>0.612</td><td>0.367</td></tr><tr><td rowspan="2">MMFeD3</td><td>✘</td><td>0.264</td><td>0.448</td><td>0.618</td><td>0.385</td><td>0.264</td><td>0.449</td><td>0.618</td><td>0.386</td><td>0.268</td><td>0.450</td><td>0.618</td><td>0.387</td></tr><tr><td>✓</td><td>0.265</td><td>0.446</td><td>0.616</td><td>0.385</td><td>0.265</td><td>0.446</td><td>0.616</td><td>0.385</td><td>0.265</td><td>0.446</td><td>0.616</td><td>0.385</td></tr><tr><td rowspan="4">50%</td><td rowspan="2">MMFedE</td><td>✘</td><td>0.240</td><td>0.433</td><td>0.600</td><td>0.365</td><td>0.238</td><td>0.432</td><td>0.601</td><td>0.363</td><td>0.243</td><td>0.437</td><td>0.604</td><td>0.368</td></tr><tr><td>✓</td><td>0.239</td><td>0.432</td><td>0.600</td><td>0.364</td><td>0.236</td><td>0.432</td><td>0.600</td><td>0.362</td><td>0.238</td><td>0.432</td><td>0.601</td><td>0.364</td></tr><tr><td rowspan="2">MMFeD3-HidE</td><td>✘</td><td>0.261</td><td>0.441</td><td>0.614</td><td>0.381</td><td>0.260</td><td>0.442</td><td>0.615</td><td>0.381</td><td>0.262</td><td>0.443</td><td>0.611</td><td>0.381</td></tr><tr><td>✓</td><td>0.261</td><td>0.441</td><td>0.615</td><td>0.381</td><td>0.259</td><td>0.443</td><td>0.615</td><td>0.380</td><td>0.269</td><td>0.449</td><td>0.615</td><td>0.387</td></tr></table>

![](images/b26b081e384ce4128f73ef23fa9d2b5cd1573389c075ca7609fd22e487498f97.jpg)  
Fig. 4. The t-SNE visualization of entity embeddings of MMFedE (w/o imputation) and MMFeD3-HidE. The entities have different types and different missing modalities.

information. It also shows that the reconstructed incomplete multimodal information of MMFeD3- HidE is comparable with the unpartitioned complete one.

# E. Semantic Consistency Visualization

We visualize entity embedding of one client in FB15K- 237- Fed3 with t- SNE [91] as shown in Figure 4.

Entity type: The same- type entity embeddings from MMFeD3- HidE are closer in a cluster than those from MMFedE, and the different- type ones are further, showing effectiveness of MMFeD3- HidE in capturing entity semantics to prevent entity ambiguity. The embeddings from MMFedE are more scattered, demonstrating the necessity of effective IMML and FL approach.

Missing type: Some of the MMFedE embeddings with missing modalities are away from the type cluster, while those of MMFeD3- HidE are all clustered close, showing that HidE could dynamically maintain the consistent semantics in entity representations regardless of their uncertain missing types.

# F. Federated Convergence Efficiency Analysis

Figure 5 shows the validation performance of FL methods during training to demonstrate convergence stability and our FL efficiency. Compared to MMFedE/MMFedEC/MMFedLU, MMFeD3 not only reaches better performance than the baselines, but also shows a faster convergence speed that reaches convergence in fewer iterations. Compared with the same distillation- based method MMFedLU, the server and

![](images/c11fe551eb4d818c6975ede404fedf8c108ccbeca8cb48d09659c49a886383b0.jpg)  
Fig. 5. FL baseline performance on valid set during iteration.

client of MMFeD3 can reach better performance through co- guidance with both logit and feature distillation. MMFedLU only exploits logit distillation and underperforms MMFeD3, demonstrating the necessity of feature distillation in further improving server- client semantic consistency. MMFedLU has better server performance than client performance, which is not as stable as MMFeD3. On the contrary, the client performance of MMFeD3 is as high as the server, ensuring the local models have the same MKGC inference ability.

# VI. CONCLUSION

In this paper, we propose the Federated Multimodal Knowledge Graph Completion (FedMKGC) task to collaboratively learn to complete the missing links on client MKGs without data transmission. We explore FedMKGC challenges including multimodal uncertain unavailability and multimodal client heterogeneity. We propose a MMFeD3- HidE framework to impute incomplete multimodal entity embeddings for multimodal semantic integrity and client- server dual distillation for robust federated optimization. Moreover, we propose a FedMKGC benchmark with datasets, a general backbone, and three groups of baselines. Extensive experiments validate the effectiveness, semantic consistency, and convergence efficiency of MMFeD3- HidE. Future works include FedMKGC with provable privacy- preserving, effective multimodal fusion, and adaptive cross- institute communication.

# APPENDIX A NOTATIONS

The notations used in this paper are shown in Table V.

# APPENDIX B METHOD DETAILS

# A. MMFeD3-HidE Pipeline

The pipeline of MMFeD3- HidE is shown in Algorithm 2.

TABLEV NOTATIONS USED IN THE PAPER.  

<table><tr><td>C</td><td>The set of client MKGs.</td></tr><tr><td>GC</td><td>The client multimodal knowledge graph.</td></tr><tr><td>EC,RC,TC</td><td>The set of entities, relations, triples of client.</td></tr><tr><td>Vc,DC</td><td>The set of entity images and descriptions.</td></tr><tr><td>MM</td><td>Modality m availability mask in client c.</td></tr><tr><td>Ni</td><td>Negative sample set for the i-th triple.</td></tr><tr><td>f(·)</td><td>The score function for triple.</td></tr><tr><td>Φ(·)</td><td>The multimodal fusion method.</td></tr><tr><td>Ss,Sc</td><td>Server/Client structural entity embeddings.</td></tr><tr><td>EC,EC</td><td>Client incomplete/Imputed fused entity embed-dings.</td></tr><tr><td>WM</td><td>Server modality m projection weight.</td></tr><tr><td>Wcm</td><td>Client c modality m projection weight.</td></tr><tr><td>Em</td><td>Entity m modal features in client c.</td></tr><tr><td>VC,DC</td><td>Visual/Textual entity embeddings in client c.</td></tr><tr><td>RC</td><td>Relation embeddings in client c.</td></tr><tr><td>HC,HC</td><td>Incomplete/Imputed hyper-modal entity embed-dings.</td></tr><tr><td>ES,c</td><td>Server incomplete fused entity embeddings.</td></tr></table>

Algorithm 2: MMFeD3-HidE Pipeline  

<table><tr><td>Input</td><td>Client set C = {Q^c}, mapping matrix P, existence vector v, communication rounds R</td></tr><tr><td>1</td><td>Server initialize S^0, Wv,0, Wd,0;</td></tr><tr><td>2</td><td>for ro in R do</td></tr><tr><td>3</td><td>Sample a client subset Cro⊆C;</td></tr><tr><td>4</td><td>for G^c∈Cro do</td></tr><tr><td>5</td><td>Server distributes S^c = P^C S^0, Wv,ro, Wd,ro;</td></tr><tr><td>6</td><td>HidE processes S^0 = HidE(E^c)</td></tr><tr><td>7</td><td>Calculate LDI(E^c; θ);</td></tr><tr><td>8</td><td>Calculate L^c(G^c; R^c);</td></tr><tr><td>9</td><td>Calculate L^c(G^c; R^c);</td></tr><tr><td>10</td><td>Calculate L^c(D^c; E^s,c);</td></tr><tr><td>11</td><td>Calculate L^c(D^c; E^s,c; R^c);</td></tr><tr><td>12</td><td>Calculate</td></tr><tr><td>13</td><td>L^c = L^cG^c + L^s,cG^c + λ^cDI + μ^cLD + η^cFD; End-to-end Update</td></tr><tr><td>14</td><td>S^c+1, W^c, v,ro+1, W^c, v,ro+1, θ, R^c, S^s,c; W^s,c, v,ro+1, W^s,c; W^s,c; W^s,c; W^s,c; W^s,c; W^s,c; W^s,c; W^s,c; W^s,c; W^s,c; W^s,c; W^s,c; W^s,c; W^s,c; W^s,c; W^s,c; W^s,c; W^s,c; W^s,c; W^s,c; W^s,c;</td></tr><tr><td>15</td><td>end</td></tr><tr><td>16</td><td>S^ro+1 = {1 ∪ ∑cro, v^c) ∩ ∑cro, P^c^T S^s,c; for m ∈ {v, d} do</td></tr><tr><td>17</td><td>W^m,ro+1 = ∑ro, α^c W^s,c; end</td></tr><tr><td>18</td><td>end</td></tr><tr><td>19</td><td>end</td></tr><tr><td>20</td><td>end</td></tr></table>

# B. Detailed Diffusion Imputation Optimization

Diffusion models are typically optimized by maximizing the evidence lower bound (ELBO) of the likelihood of the original state  $\mathbf{x}_0$  as Equation 13).

$$
\begin{array}{rl} & {\log p(\mathbf{x}_0) = \log \int p(\mathbf{x}_{0:T})\mathrm{d}\mathbf{x}_{1:T}}\\ & {\qquad = \log \mathbb{E}_{q(\mathbf{x}_{1:T}|\mathbf{x}_0)}\left[\frac{p(\mathbf{x}_{0:T})}{q(\mathbf{x}_{1:T}|\mathbf{x}_0)}\right]}\\ & {\qquad \geq \underbrace{\mathbb{E}_{q(\mathbf{x}_1|\mathbf{x}_0)}[\log p_\theta (\mathbf{x}_0|\mathbf{x}_1)]}_{\mathrm{reconstruction term}} - \underbrace{D_{\mathrm{KL}}(q(\mathbf{x}_T|\mathbf{x}_0)\parallel p(\mathbf{x}_T))}_{\mathrm{prior matching term}}}\\ & {\qquad -\sum_{t = 2}^{T}\underbrace{\mathbb{E}_{q(\mathbf{x}_t|\mathbf{x}_0)}[D_{\mathrm{KL}}(q(\mathbf{x}_{t - 1}|\mathbf{x}_t,\mathbf{x}_0)\parallel p_\theta (\mathbf{x}_{t - 1}|\mathbf{x}_t))]}_{\mathrm{denoisin matching term}}} \end{array} \tag{13}
$$

Based on DDPM, ELBO can be simplified as  $\mathbb{E}_{t,\mathbf{x}_0,\epsilon}\left[\| \epsilon - \epsilon_\theta (\mathbf{x}_t,t)\| _2^2\right]$  ,where  $1\leq t\leq T$  .DDPM's reconstruction network outputs the additional noise  $\epsilon_{\theta}$  and optimizes the noise to approximate the distribution step by step. In our paper, we aim to predict the complete hyper- modal distributions from input observed modalities, thus we predict the complete hyper- modal  $\mathbf{x}_0$  at each step instead of  $\epsilon_{\theta}$  .Thus, in our HidE, the reconstruction network  $\theta$  is optimized to regulate its output  $\hat{\mathbf{x}}_{\theta}(\mathbf{x}_t,t)$  closer to  $\mathbf{x}_0$  This way, the output of the diffusion model remains semantic consistency with the observed modalities and has a complete modality set.

However, there are both missing and observed data points in the diffusion processes, we need to design the objective with reconstruction supervision signals that are not affected by the missing modal points. Since the diffusion model preserves the feature size and location in both forward and backward processes, it offers a convenient solution for diffusion imputation optimization. We split the data at each step  $\mathbf{x}_t$  to observed data and missing data  $\mathbf{x}_t = \{\mathbf{x}_t^{obs},\mathbf{x}_t^{mis}\}$  , that  $\mathbf{x}_t^{obs} = M^c\odot \mathbf{x}_t$  and  $\mathbf{x}_t^{mis} = (1 - M^c)\odot \mathbf{x}_t,1\leq t\leq T$  .We propose to constrain the diffusion of available modalities  $\mathbf{x}_t^{obs}$  to be similar to their ground- truth features, so that the missing modalities  $\mathbf{x}_t^{mis}$  from the same distributions can be reconstructed to be consistent with observed modalities. We elaborate on simplifying ELBO to masked diffusion imputation by analyzing the optimization of each term as follows:

1) Prior matching term optimization: The prior matching term measures the KL divergence between the posterior probability  $q(\mathbf{x}_T|\mathbf{x}_0)$  of  $\mathbf{x}_T$  at step  $T$  of  $\mathbf{x}_T$  and the prior probability  $p(\mathbf{x}_T)$  . The prior matching term is omitted as constants since the forward process has no trainable parameters.

2) Denoising matching term optimization: The denoising matching term constrains the reconstruction  $p_{\theta}(\mathbf{x}_{t - 1}|\mathbf{x}_t)$  at each step from 2 to  $T$  exploiting KL divergence to force the generated  $\mathbf{x}_{t - 1}$  to approximate forward process posteriors  $q(\mathbf{x}_{t - 1}|\mathbf{x}_t,\mathbf{x}_0)$  . The approximations are tractable when conditioned on the available modalities in hyper-modal features  $\mathbf{x}_0$  .According to Bayes rules, the forward process posteriors  $q(\mathbf{x}_{t - 1}|\mathbf{x}_t,\mathbf{x}_0)$  can be rewritten as Equation (14), where  $\tilde{\pmb{\mu}}_t(\mathbf{x}_t$  and  $\tilde{\beta}_t$  are its mean and variance.

$$
q(\mathbf{x}_{t - 1}|\mathbf{x}_t,\mathbf{x}_0) = \mathcal{N}(\mathbf{x}_{t - 1};\tilde{\pmb{\mu}}_t(\mathbf{x}_t,\mathbf{x}_0),\tilde{\beta}_t\mathbf{I}) \tag{14}
$$

$$
\left\{ \begin{array}{ll}\tilde{\pmb{\mu}}_t(\mathbf{x}_t,\mathbf{x}_0) = \frac{\sqrt{\alpha_t}(1 - \bar{\alpha}_{t - 1})}{1 - \bar{\alpha}_t}\mathbf{x}_t + \frac{\sqrt{\bar{\alpha}_{t - 1}}(1 - \alpha_t)}{1 - \bar{\alpha}_t}\mathbf{x}_0\\ \tilde{\beta}_t = \frac{1 - \bar{\alpha}_{t - 1}}{1 - \bar{\alpha}_t}\beta_t \end{array} \right. \tag{15}
$$

For reverse process, we first set the learning of  $\pmb{\Sigma}_{\theta}(\mathbf{x}_t,t)$  in reverse process to untrained time- dependent constants as  $\pmb{\Sigma}_{\theta}(\mathbf{x}_t,t) = \sigma_t^2\mathbf{I}$  and  $\sigma_t^2 = \bar{\beta}_t$  . Thus, we define the loss of denoising matching term of ELBO as  $\mathcal{L}_t$  at step  $t$  as Equation (16), to push the  $\pmb{\mu}_{\theta}(\mathbf{x}_t,t)$  closer to  $\tilde{\pmb{\mu}}_t(\mathbf{x}_t,\mathbf{x}_0)$  . By minimizing  $\mathcal{L}_t$  , the denoising matching term of ELBO is maximized.

$$
\begin{array}{rl} & {\mathcal{L}_t\triangleq \mathbb{E}_{q(\mathbf{x}_t|\mathbf{x}_0)}[D_{\mathrm{KL}}(\rho (\mathbf{x}_{t - 1}|\mathbf{x}_t,\mathbf{x}_0)\parallel p_\theta (\mathbf{x}_{t - 1}|\mathbf{x}_t))]}\\ & {\quad = \mathbb{E}_{q(\mathbf{x}_t|\mathbf{x}_0)}\left[\frac{1}{2\sigma_t^2}\left\Vert \pmb {\mu}_\theta (\mathbf{x}_t,t) - \pmb {\tilde{\mu}}_t(\mathbf{x}_t,\mathbf{x}_0)\right\Vert_2^2\right]} \end{array} \tag{16}
$$

Secondly, we denote  $\mu_{\theta}(\mathbf{x}_t,t)$  in the same form in Equation (15) for further simplicity, as Equation (17),

$$
\pmb{\mu}_{\theta}(\mathbf{x}_t,t) = \frac{\sqrt{\alpha_t}(1 - \bar{\alpha}_{t - 1})}{1 - \bar{\alpha}_t}\mathbf{x}_t + \frac{\sqrt{\bar{\alpha}_{t - 1}}(1 - \alpha_t)}{1 - \bar{\alpha}_t}\hat{\mathbf{x}}_\theta (\mathbf{x}_t,t), \tag{17}
$$

where  $\hat{\mathbf{x}}_{\theta}(\mathbf{x}_t,t)$  is the estimated hyper- modal features  $\mathbf{x}_0$  based on  $\mathbf{x}_t$  and step  $t$  which is the output of the reconstruction network. Thus, the  $\mathcal{L}_t$  can be denoted as Equation (18).

$$
\begin{array}{rl} & {\mathcal{L}_t = \mathbb{E}_{q(\mathbf{x}_t|\mathbf{x}_0)}\left[\frac{1}{2\sigma_t^2}\left[\| \pmb {\mu}_\theta (\mathbf{x}_t,t) - \tilde{\pmb{\mu}}_t(\mathbf{x}_t,\mathbf{x}_0)\| _2^2\right]\right.}\\ & {\quad = \mathbb{E}_{q(\mathbf{x}_t|\mathbf{x}_0)}\left[\frac{1}{2\sigma_t^2}\frac{\sqrt{\bar{\alpha}_{t - 1}}(1 - \alpha_t)}{1 - \bar{\alpha}_t}\| \hat{\mathbf{x}}_\theta (\mathbf{x}_t,t) - \mathbf{x}_0\| _2^2\right]}\\ & {\quad \Leftrightarrow \mathbb{E}_{q(\mathbf{x}_t|\mathbf{x}_0)}\left[\| \hat{\mathbf{x}}_\theta (\mathbf{x}_t,t) - \mathbf{x}_0\| _2^2\right]} \end{array} \tag{18}
$$

Since we only exploit observed modalities for optimization, we re- define  $\mathcal{L}_t$  as Equation (19),

$$
\begin{array}{rl} & {\mathcal{L}_t = \mathbb{E}_{q(\mathbf{x}_t|\mathbf{x}_0)}\left[\| \hat{\mathbf{x}}_\theta^{obs}(\mathbf{x}_t,t) - \mathbf{x}_0^{obs}\| _2^2\right]}\\ & {\quad = \mathbb{E}_{q(\mathbf{x}_t|\mathbf{x}_0)}\left[\| M^c\odot \hat{\mathbf{x}}_\theta (\mathbf{x}_t,t) - M^c\odot \mathbf{x}_0\| _2^2\right],} \end{array} \tag{19}
$$

where only the available modalities and their reconstructed features are pulled together.

3) Reconstruction term optimization: The reconstruction term constrains the transition from the hidden state in the first step  $\mathbf{x}_1$  to the original state  $\mathbf{x}_0$  . Similar to the denoising matching term, we define the loss  $\mathcal{L}_1$  as the negative of reconstruction term of ELBO as Equation (20),

$$
\begin{array}{rl} & {\mathcal{L}_1\triangleq -\mathbb{E}_{q(\mathbf{x}_1|\mathbf{x}_0)}[\log p_\theta (\mathbf{x}_0|\mathbf{x}_1)]}\\ & {\quad \propto -\mathbb{E}_{q(\mathbf{x}_1|\mathbf{x}_0)}\left[-\frac{1}{2\sigma_t^2}\| \pmb {\mu}_\theta (\mathbf{x}_1,1) - \mathbf{x}_0\| _2^2\right]}\\ & {\quad \Leftrightarrow \mathbb{E}_{q(\mathbf{x}_1|\mathbf{x}_0)}\left[\| \hat{\mathbf{x}}_\theta^{obs}(\mathbf{x}_1,1) - \mathbf{x}_0^{obs}\| _2^2\right]}\\ & {\quad \Rightarrow \mathbb{E}_{q(\mathbf{x}_1|\mathbf{x}_0)}\left[\| M^c\odot \hat{\mathbf{x}}_\theta (\mathbf{x}_1,1) - M^c\odot \mathbf{x}_0\| _2^2\right],} \end{array} \tag{20}
$$

where we estimate the log- likelihood of  $\log p_{\theta}(\mathbf{x}_0|\mathbf{x}_1)$  thanks to the probability density function of Gaussian distribution  $p_{\theta}(\mathbf{x}_0|\mathbf{x}_1) = \mathcal{N}(\mathbf{x}_0;\pmb {\mu}_\theta (\mathbf{x}_1,1),\sigma_t^2\mathbf{I})$  .We denote the output of reconstruction network as  $\hat{\mathbf{x}}_{\theta}(\mathbf{x}_1,1) = \pmb {\mu}_{\theta}(\mathbf{x}_1,1)$  .We also only constrain the generation of observed slots.

4) Overall diffusion imputation objective: The final objective for diffusion imputation optimization is as simple as Equation (21), that minimizes the L2 distance between available modalities and the output of reconstruction network  $\hat{\mathbf{x}}_{\theta}(\mathbf{x}_t,t)$  at each step  $t$

$$
\begin{array}{rl} & {\arg \max_{\theta}\mathrm{Masked - ELBO}\Leftrightarrow \arg \max_{\theta}\left[-\mathcal{L}_1 - \Sigma_{t = 2}^T\mathcal{L}_t\right]}\\ & {\Leftrightarrow \arg \max_{\theta}\left[-\Sigma_{t = 1}^T\mathcal{L}_t\right]\Leftrightarrow \arg \min_{\theta}\left[\Sigma_{t = 1}^T\mathcal{L}_t\right]} \end{array} \tag{21}
$$

# C. Privacy Preservation Discussion

1) Structural privacy: Structural privacy includes relations and triples. Since relation IDs and relation embeddings remain in their own client and do not participate in communication, one client cannot be aware of that of other clients. As for triples, since the relation types of clients have no overlapping and the relation embeddings are not shared, the relation between two entities belonging to its owner cannot be inferred from other clients.

2) Visual and textual privacy: We preserve the  $E_{m}^{c}$  from open-sourced pre-trained models in their own client, thus the visual and textual features cannot be accessed by other clients. In the imputation process, the parameters of the diffusion models are not shared, for privacy-preserving of local knowledge and reducing communication costs as well. Moreover, our missing modality imputation objective  $\mathcal{L}_{DI}$  aims to maintain the semantic consistency of the available ones instead of bringing extra semantics from other clients, which also preserves semantic privacy.

After training, the structural entity embeddings  $\mathbf{S}^c$  of clients tend to be similar, while the sensitive visual and textual semantics in  $\mathbf{V}^c$  and  $\mathbf{D}^c$  remain local and have not been transmitted. In future work, provable privacy- preserving techniques such as differential privacy that add noise to the uploaded gradients can be applied in MMFeD3- HidE due to our robustness to noise in diffusion model and heterogeneous clients in federated optimization.

# APPENDIX C EXPERIMENTS

# A. Baselines construction

We experiment with baselines from three groups:

(1) MKGC baselines: We include various multimodal fusion functions  $\Phi (\cdot)$  based on existing MKGC methods.

Average: entity embeddings are average of three embeddings as  $\mathbf{E}^c = w_s\mathbf{S}^c +w_v\mathbf{V}^c +w_d\mathbf{D}$  , of which the weights  $w_{m}$  are  $1 / 3$  Weighted [9]: entity embeddings are weighted average of three embeddings, where  $w_{m}$  in the average fusion above are trainable parameters. Concatenation [9]: entity embeddings are the mapped projection of three concatenated embeddings as  $\mathbf{E}^c =$ $\mathrm{MLP}([\mathbf{S}^c ||\mathbf{V}^c ||\mathbf{D}^c ])$  Split [10],[26]: modality split paradigm does not fuse multimodal embeddings and takes an average of three prediction probabilities from each modality as the final prediction, as  $\begin{array}{rlr}{p^c (t|(h,r_i)} & = & {\sum_m\mathrm{softmax}(f^c (h,r,e;\mathbf{E}_m^c,\mathbf{R}_m^c) / 3,} \end{array}$  where  $\mathbf{E}_m^c\in \{\mathbf{S}^c,\mathbf{V}^c,\mathbf{D}^c\}$  Gated [90]: gated fusion exploits structural embeddings to filter visual and textual information as  $\mathbf{V}^c =$ $\mathbf{W}_0(\sigma (\mathbf{W}_1\mathbf{S}^c)*\mathbf{W}_2\mathbf{V}^c)$  visual modality as example), then utilize the average fusion of three embeddings as entity embedding.

(2) Federated learning baselines: We augment the MMFedE backbone with FedKGC or FL baselines by adding their additional objectives to MMFedE.

TABLE VI FEDMKGC PERFORMANCE WITH FULLY AVAILABLE ENTITY DESCRIPTIONS AND IMAGES. THE DESCRIPTIONS AND IMAGES IN GROUP MMIND AND MMFEDE ARE PARTITIONED BETWEEN CLIENTS. IN GROUP MMCEN, THE DESCRIPTIONS AND IMAGES OF ALL CLIENT KGS ARE THE SAME.  

<table><tr><td rowspan="2">FL</td><td rowspan="2">MKGC</td><td colspan="4">FB15K-237-Fed3</td><td colspan="4">FB15K-237-Fed5</td><td colspan="4">FB15K-237-Fed10</td></tr><tr><td>Hits@1</td><td>Hits@3</td><td>Hits@10</td><td>MRR</td><td>Hits@1</td><td>Hits@3</td><td>Hits@10</td><td>MRR</td><td>Hits@1</td><td>Hits@3</td><td>Hits@10</td><td>MRR</td></tr><tr><td>S-Ind</td><td>-</td><td>0.231</td><td>0.390</td><td>0.547</td><td>0.338</td><td>0.220</td><td>0.373</td><td>0.532</td><td>0.325</td><td>0.205</td><td>0.361</td><td>0.517</td><td>0.310</td></tr><tr><td rowspan="5">MMInd</td><td>Avg</td><td>0.232</td><td>0.408</td><td>0.576</td><td>0.349</td><td>0.221</td><td>0.392</td><td>0.555</td><td>0.335</td><td>0.218</td><td>0.384</td><td>0.551</td><td>0.331</td></tr><tr><td>Weighted</td><td>0.233</td><td>0.405</td><td>0.572</td><td>0.348</td><td>0.222</td><td>0.391</td><td>0.551</td><td>0.334</td><td>0.217</td><td>0.381</td><td>0.549</td><td>0.329</td></tr><tr><td>Concat</td><td>0.231</td><td>0.396</td><td>0.562</td><td>0.343</td><td>0.210</td><td>0.368</td><td>0.535</td><td>0.319</td><td>0.203</td><td>0.357</td><td>0.526</td><td>0.311</td></tr><tr><td>Split</td><td>0.232</td><td>0.413</td><td>0.576</td><td>0.346</td><td>0.213</td><td>0.398</td><td>0.559</td><td>0.333</td><td>0.212</td><td>0.391</td><td>0.560</td><td>0.331</td></tr><tr><td>Gated</td><td>0.230</td><td>0.403</td><td>0.569</td><td>0.346</td><td>0.217</td><td>0.385</td><td>0.552</td><td>0.330</td><td>0.215</td><td>0.375</td><td>0.542</td><td>0.325</td></tr><tr><td rowspan="5">MMFedE</td><td>Avg</td><td>0.237</td><td>0.438</td><td>0.610</td><td>0.366</td><td>0.232</td><td>0.436</td><td>0.609</td><td>0.363</td><td>0.217</td><td>0.428</td><td>0.606</td><td>0.357</td></tr><tr><td>Weighted</td><td>0.237</td><td>0.437</td><td>0.612</td><td>0.367</td><td>0.234</td><td>0.439</td><td>0.613</td><td>0.368</td><td>0.221</td><td>0.434</td><td>0.614</td><td>0.357</td></tr><tr><td>Concat</td><td>0.233</td><td>0.436</td><td>0.602</td><td>0.362</td><td>0.230</td><td>0.430</td><td>0.598</td><td>0.358</td><td>0.219</td><td>0.422</td><td>0.595</td><td>0.350</td></tr><tr><td>Split</td><td>0.234</td><td>0.427</td><td>0.597</td><td>0.359</td><td>0.227</td><td>0.425</td><td>0.597</td><td>0.354</td><td>0.220</td><td>0.416</td><td>0.592</td><td>0.349</td></tr><tr><td>Gated</td><td>0.231</td><td>0.406</td><td>0.587</td><td>0.339</td><td>0.192</td><td>0.394</td><td>0.577</td><td>0.324</td><td>0.189</td><td>0.396</td><td>0.584</td><td>0.324</td></tr><tr><td rowspan="5">MMCen</td><td>Avg</td><td>0.245</td><td>0.440</td><td>0.613</td><td>0.371</td><td>0.252</td><td>0.449</td><td>0.627</td><td>0.380</td><td>0.241</td><td>0.448</td><td>0.629</td><td>0.375</td></tr><tr><td>Weighted</td><td>0.246</td><td>0.441</td><td>0.616</td><td>0.373</td><td>0.251</td><td>0.451</td><td>0.627</td><td>0.380</td><td>0.240</td><td>0.447</td><td>0.629</td><td>0.373</td></tr><tr><td>Concat</td><td>0.230</td><td>0.426</td><td>0.602</td><td>0.358</td><td>0.248</td><td>0.444</td><td>0.622</td><td>0.376</td><td>0.234</td><td>0.441</td><td>0.625</td><td>0.368</td></tr><tr><td>Split</td><td>0.235</td><td>0.423</td><td>0.595</td><td>0.353</td><td>0.237</td><td>0.442</td><td>0.611</td><td>0.368</td><td>0.228</td><td>0.437</td><td>0.614</td><td>0.362</td></tr><tr><td>Gated</td><td>0.245</td><td>0.438</td><td>0.613</td><td>0.371</td><td>0.253</td><td>0.446</td><td>0.620</td><td>0.379</td><td>0.243</td><td>0.442</td><td>0.621</td><td>0.373</td></tr></table>

![](images/3857fe05e83658105b98f1fae7e76dd365853d299e4c20ccbb017525ee405bba.jpg)  
Fig. 6. MMFeD3-HidE performance with different reconstruction networks. The rankings of each reconstruction network are labeled above the bars.

![](images/5fa047d3f03d5f6b3462db6b7228f407bfd34dc173d29729c89497a9cf6d5a29.jpg)  
Fig. 7. Performance of MMFeD3-HidE with different hyper-parameters.

MMFedE: optimize the federated MKGs with  $\mathcal{L}_{KGC}^{c}$  MMFedEC: optimize with extra contrastive objective of reconstructed entity embeddings  $\begin{array}{rl}{\mathcal{L}_{EC}^{c}} & = \end{array}$ $- \log \frac{\exp(s(\hat{\mathbf{E}}_{ro}^{c},\mathbf{E}_{ro}^{s}) / \tau}{\exp(s(\hat{\mathbf{E}}_{ro}^{c},\mathbf{E}_{ro}^{s}) / \tau)} +\exp (s(\hat{\mathbf{E}}_{ro}^{c},\hat{\mathbf{E}}_{ro - 1}^{c}) / \tau)$  where  $\hat{\mathbf{E}}^c$  are the inputed local entity embeddings,  $\mathbf{E}^c$  are the incomplete global ones, and  $ro$  is the current round. MMFedProx: optimize with extra proximal term  $\mathcal{L}_{prox}^{c} = ||\mathbf{E}^{s} - \hat{\mathbf{E}}^{c}||_{2}^{2}$  MMFedLU: optimize with extra global KGC objective  $\mathcal{L}_{KGC}^{s,c}$  and logit distillation objective  $\begin{array}{rl}{\mathcal{L}_{LU}^{c}} & = \end{array}$ $\begin{array}{r}\sum_{(h,r,t)_i}D_{\mathrm{KL}}(p^c (t|(h,r)_i),p^{s,c}(t|(h,r)_i)). \end{array}$

(3) Incomplete Multimodal Learning (IMML) baselines: We denote the baselines as  $\theta$  in  $\hat{\mathbf{H}}^c = \theta (\hat{\mathbf{H}}^c)$  , and optimize them with masked objective  $\mathcal{L}_{Econ}^{c} = \parallel M^{c}\odot \hat{\mathbf{H}}^{c} - M^{c}\odot \mathbf{H}^{c}\parallel_{2}^{2}$

AE: AE has 3- layer MLPs activated by ReLU and each layer with input and output size [512, 256,128]. CRA:CRA is 3 cascaded blocks of AEs with residual

connection, where the AEs are as above.

MMIN: MMIN has a CRA encoder and a CRA decoder, and the output of the encoder and decoder are constrained by cycle- consistency imputation loss in two directions as  $\parallel M^c\odot \hat{\mathbf{H}}^c - M^c\odot \hat{\mathbf{H}}^c\parallel_2^2 +\parallel M^c\odot \hat{\mathbf{H}}_c^c - M^c\odot \hat{\mathbf{H}}^c\parallel_2^2$  where  $\hat{\mathbf{H}}_f^c$  and  $\hat{\mathbf{H}}_b^c$  are the output of encoder and decoder respectively.

# B. FedMKGC with full modalities

Table VI shows the FedMKGC performance with full modalities of MKGC baselines in independent learning, federated learning, and centralized learning, respectively.

Federated Setting: Compared to S- Ind, the structural model in independent learning, MMInd with different MKGC baselines generally outperforms S- Ind, demonstrating the benefits of incorporating multimodal information to complement structural information. Comparing MMInd, MMFedE, and

![](images/588f991385883ccdf9cc0c3aef414729688a555d26a29fc2a4f106a33f1c2316.jpg)  
Fig. 8. Performance comparison between MMFedE and MMFeD3-HidE with various multimodal available rates  $r$ .

MMCen, the centralized learning that all structural triples, entity images, and entity descriptions are exploited to train a unified global model has the best performance, outperforming MMInd and MMFedE. The MMFedE outperforms MMInd, demonstrating the effectiveness of our proposed aggregation strategy to jointly train the client MKGs. However, MMFedE does not outperform MMCen, the reason for which can be that the basic aggregation strategy of MMFedE is not effective enough to handle the heterogeneous partitioned multimodal information in client MKGs to outperform the non- partitioned unified multimodal information in MMCen.

MKGC baselines: Comparing different MKGC baselines, they have diverse performance in different settings. The average fusion and split fusion have the most number of best metrics in the MMInd group, weighted fusion has the most in the MMFed and MMGen groups. The reason could be that the fusion strategies have various adaptive abilities in different client MKGs and thus are diverse in independent, federated, and centralized learning. The results show that weighted fusion adapts to different clients the best and is relatively stable in different settings. Thus, we set the default fusion method as weighted fusion in the following experiments. Moreover, it is notable that Gated fusion has worse performance in MMFedE than MMInd and MMCen. The reason could be that Gated itself contains more parameters than other fusion paradigms and the transmission of projection weights in MMFedE is not sufficient enough to reach better global performance.

# C. Effect of Multimodal Available Rate

We study the effect of multimodal available rates  $r$  of available entity images and descriptions. As Figure 8 shows, with little available rate, the performance of MMFedE decreases in general, which shows the harm from uncertain missing modalities. With our MMFeD3 for stable federated optimization and with HidE to impute the uncertain missing modalities, the performance of MMFeD3- HidE is not only higher than MMFedE but also robust to multimodal available rates, that MMFeD3- HidE performance is always higher than MMFedE significantly.

# D. Effect of Diffusion Reconstruction Network

We replace the reconstruction network  $\theta$  in the reverse process of our diffusion imputation with various reconstruction networks, including AE, MLP, and Multi- head Attention (MHA). As shown in Figure 6, CRA shows the best performance in three datasets, while MHA shows the second- best performance. The CRA has shown better incomplete multimodal learning ability for iteratively modeling the residual between the incomplete multimodal and the reconstructed one. The MHA also has strong potential as the denoising network in the diffusion imputation model, probably because it could model better cross- modal interaction as well as intra- modal interaction with the input of hyper- modal features.

# E. Hyperparameter Sensitivity Analysis

We study the hyperparameter sensitivity by setting varying values and reporting their performance as shown in Figure 7. It shows the robustness of MMFeD3- HidE to different hyperparameters, especially the diffusion hyperparameters  $\lambda$  and step counts. Moreover, by selecting appropriate distillation loss ratios  $\eta , \mu$  for logit and feature distillation, MMFeD3- HidE could balance the global and local semantic consistency and federated convergence, to reach a better performance.

# REFERENCES

[1] X. Zhu, Z. Li, X. Wang, X. Jiang, P. Sun, X. Wang, Y. Xiao, and N. J. Yuan, "Multi- modal knowledge graph construction and application: A survey," IEEE Transactions on Knowledge and Data Engineering, vol. 36, no. 2, pp. 715- 735, 2024. [2] K. Liang, L. Meng, M. Liu, Y. Liu, W. Tu, S. Wang, S. Zhou, X. Liu, F. Sun, and K. He, "A survey of knowledge graph reasoning on graph types: Static, dynamic, and multi- modal," IEEE TPAMI, 2024. [3] K. Marino, M. Rastegari, A. Farhadi, and R. Mottaghi, "Ok- vqa: A visual question answering benchmark requiring external knowledge," in CVPR, 2019, pp. 3195- 3204. [4] Y. Ding, J. Yu, R. Liu, Y. Hu, M. Cui, and Q. Wu, "Mkkes: Multimodal knowledge extraction" and accumulation for knowledge- based visual question answering," in Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition, 2022, pp. 5089- 5098. [5] R. Sun, X. Cao, Y. Zhao, J. Wan, K. Zhou, F. Zhang, Z. Wang, and K. Zheng, "Multi- modal knowledge graphs for recommender systems," in CIKM, 2020, pp. 1405- 1414. [6] A. Yang, S. Lin, C. Yeh, M. Shu, Y. Yang, and X. Chang, "Context matters: Distilling knowledge graph for enhanced object detection," IEEE Trans. Multim., vol. 26, pp. 487- 500, 2024. [7] R. Xie, Z. Liu, H. Luan, and M. Sun, "Image- embodied knowledge representation learning," in IJCAI, 2017, pp. 3140- 3146. [8] R. Xie, Z. Liu, J. Jia, H. Luan, and M. Sun, "Representation learning of knowledge graphs with entity descriptions," in AAAI, vol. 30, no. 1, 2016. [9] M. Wang, S. Wang, H. Yang, Z. Zhang, X. Chen, and G. Qi, "Is visual context really helpful for knowledge graph? a representation learning perspective," in ACM MM, 2021, pp. 2735- 2743. [10] Y. Zhao, X. Cai, Y. Wu, H. Zhang, Y. Zhang, G. Zhao, and N. Jiang, "Mose: Modality split and ensemble for multimodal knowledge graph completion," in EMNLP, 2022, pp. 10527- 10536. [11] X. Chen, N. Zhang, L. Li, S. Deng, C. Tan, C. Xu, F. Huang, L. Si, and H. Chen, "Hybrid transformer with multi- level fusion for multimodal knowledge graph completion," in Proceedings of the 45th International ACM SIGIR Conference on Research and Development in Information Retrieval, 2022, pp. 904- 915. [12] B. Shang, Y. Zhao, J. Liu, and D. Wang, "Lafa: Multimodal knowledge graph completion with link aware fusion and aggregation," in Proceedings of the AAAI Conference on Artificial Intelligence, vol. 38, no. 8, 2024, pp. 8957- 8965. [13] S. Auer, C. Bizer, G. Kobilarov, J. Lehmann, R. Cyganiak, and Z. Ives, "Dbpedia: A nucleus for a web of open data," in international semantic web conference. Springer, 2007, pp. 722- 735. [14] D. Vrandecic and M. Krtozsch, "Wikidata: a free collaborative knowledgebase," Communications of the ACM, vol. 57, no. 10, pp. 78- 85, 2014. [15] K. Bollacker, C. Evans, P. Paritosh, T. Sturge, and J. Taylor, "Freebase: a collaboratively created graph database for structuring human knowledge," in Proceedings of the 2008 ACM SIGMOD international conference on Management of data, 2008, pp. 1247- 1250.

[16] Y. Wang, Q. Xie, M. Tang, L. Li, J. Yuan, and Y. Liu, "Amazon- kg: A knowledge graph enhanced cross- domain recommendation dataset," in Proceedings of the 47th International ACM SIGIR Conference on Research and Development in Information Retrieval, 2024, pp. 123- 130. [17] G. Xu, H. Chen, F- L. Li, F. Sun, Y. Shi, Z. Zeng, W. Zhou, Z. Zhao, and J. Zhang, "Alime mkg: A multi- modal knowledge graph for live- streaming e- commerce," in Proceedings of the 30th ACM CIKM, 2021, pp. 4808- 4812. [18] M. Rotmensch, Y. Halpern, A. Tlimat, S. Horng, and D. Sontag, "Learning a health- knowledge graph from electronic medical records," Scientific reports, vol. 7, no. 1, p. 5994, 2017. [19] Q. Yang, Y. Liu, T. Chen, and Y. Tong, "Federated machine learning: Concept and applications," ACM Transactions on Intelligent Systems and Technology (TIST), vol. 10, no. 2, pp. 1- 19, 2019. [20] B. McMahan, E. Moore, D. Ramage, S. Hampson, and B. A. y Areas, "Communication- efficient learning of deep networks from decentralized data," in Artificial intelligence and statistics. PMLR, 2017, pp. 1273- 1282. [21] P. Kairou, H. B. McMahan, B. Avent, A. Bellet, M. Bennis, A. N. Bhagoji, K. Bonawitz, Z. Charles, G. Cormode, R. Cummings et al., "Advances and open problems in federated learning," Foundations and trends in machine learning, vol. 14, no. 1- 2, pp. 1- 210, 2021. [22] M. Chen, W. Zhang, Z. Yuan, Y. Jia, and H. Chen, "Fede: Embedding knowledge graphs in federated setting," in Proceedings of the 10th International Joint Conference on Knowledge Graphs, 2021, pp. 80- 88. [23] "Federated knowledge graph completion via embedding- contrastive learning," Knowledge- Based Systems, 2022. [24] X. Zhu, G. Li, and W. Hu, "Heterogeneous federated knowledge graph embedding learning and unlearning," in Proceedings of the ACM Web Conference 2023, 2023, pp. 2444- 2454. [25] L. Meng, K. Liang, H. Yu, Y. Liu, S. Zhou, M. Liu, and X. Liu, "Fedean: Entity- aware adversarial negative sampling for federated knowledge graph reasoning," IEEE Transactions on Knowledge and Data Engineering, 2024. [26] X. Li, X. Zhao, J. Xu, Y. Zhang, and C. Xing, "Imf: Interactive multimodal fusion model for link prediction," in ACM Web Conference, 2023, pp. 2572- 2580. [27] T. Feng, D. Bose, T. Zhang, R. Hebbar, A. Ramakrishna, R. Gupta, M. Zhang, S. Avestimehr, and S. Narayanan, "Fedmultimodal: A benchmark for multimodal federated learning," in Proceedings of the 29th ACM SIGKDD Conference on Knowledge Discovery and Data Mining, 2023, pp. 4035- 4045. [28] Q. Yu, Y. Liu, Y. Wang, K. Xu, and J. Liu, "Multimodal federated learning via contrastive representation ensemble," in The Eleventh International Conference on Learning Representations, 2023. [29] B. Xiong, X. Yang, F. Qiu, and C. Xu, "A unified framework for multimodal federated learning," Neurocomputing, vol. 480, pp. 110- 118, 2022. [30] Y. Zhang, Z. Chen, and W. Zhang, "Maco: A modality adversarial and contrastive framework for modality- missing multi- modal knowledge graph completion," in CCF International Conference on Natural Language Processing and Chinese Computing. Springer, 2023, pp. 123- 134. [31] Y. Zhang, Z. Chen, L. Liang, H. Chen, and W. Zhang, "Unleashing the power of imbalanced modality information for multi- modal knowledge graph completion," in Proceedings of the 2024 Joint International Conference on Computational Linguistics, Language Resources and Evaluation (LREC- COLING 2024), 2024, pp. 17 120- 17 130. [32] J. Zhao, R. Li, and Q. Jin, "Missing modality imagination network for emotion recognition with uncertain missing modalities," in Proceedings of the 59th Annual Meeting of the Association for Computational Linguistics and the 11th International Joint Conference on Natural Language Processing (Volume 1: Long Papers), 2021, pp. 2608- 2618. [33] Z. Yuan, W. Li, H. Xu, and W. Yu, "Transformer- based feature reconstruction network for robust multimodal sentiment analysis," in Proceedings of the 29th ACM International Conference on Multimedia, 2021, pp. 4400- 4407. [34] J. Zeng, J. Zhou, and T. Liu, "Robust multimodal sentiment analysis via tag encoding of uncertain missing modalities," IEEE Transactions on Multimedia, vol. 25, pp. 6301- 6314, 2022. [35] R. Huan, G. Zhong, P. Chen, and R. Liang, "Unimf: a unified multimodal framework for multimodal sentiment analysis in missing modalities and unaligned multimodal sequences," IEEE Transactions on Multimedia, 2023.

[36] Y. Wang, Y. Li, and Z. Cui, "Incomplete multimodality- diffused emotion recognition," Advances in Neural Information Processing Systems, vol. 36, 2024. [37] T. Li, A. K. Sahu, M. Zaheer, M. Sanjabi, A. Talwalkar, and V. Smith, "Federated optimization in heterogeneous networks," Proceedings of Machine learning and systems, vol. 2, pp. 429- 450, 2020. [38] Q. Li, Y. Diao, Q. Chen, and B. He, "Federated learning on non- iid data silos: An experimental study," 2022 IEEE 38th international conference on data engineering (ICDE). IEEE, 2022, pp. 965- 978. [39] M. Tan, Y. Feng, L. Chu, J. Shi, R. Xiao, H. Tang, and J. Yu, "Fedsea: Federated learning via selective feature alignment for non- iid multimodal data," IEEE Trans. Multim., vol. 26, pp. 5807- 5822, 2024. [40] J. Yoon, J. Jordon, and M. Schaar, "Gain: Missing data imputation using generative adversarial nets," in International conference on machine learning. PMLR, 2018, pp. 5689- 5698. [41] Y. Luo, X. Cui, Y. Zhang, J. Au et al., "Multivariate time series imputation with generative adversarial networks," NeurIPS, vol. 31, 2018. [42] I. Goodfellow, J. Pouget- Abadie, M. Mirza, B. Xu, D. Warde- Farley, S. Ozair, A. Courville, and Y. Bengio, "Generative adversarial networks," Communications of the ACM, vol. 63, no. 11, pp. 139- 144, 2020. [43] C. Shang, A. Palmer, J. Sun, K.- S. Chen, J. Lu, and J. Bi, "Vigan: Missing view imputation with generative adversarial networks," in 2017 IEEE International conference on big data (Big Data). IEEE, 2017, pp. 766- 775. [44] P. Vincent, H. Larochelle, Y. Bengio, and P.- A. Manzagol, "Extracting and composing robust features with denoising autoencoders," in Proceedings of the 25th international conference on Machine learning, 2008, pp. 1096- 1103. [45] P. Baldi, "Autoencoders, unsupervised learning, and deep architectures," in Proceedings of ICML workshop on unsupervised and transfer learning. JMLR Workshop and Conference. Proceedings, 2012, pp. 37- 49. [46] L. Tran, X. Liu, J. Zhou, and R. Jin, "Missing modalities imputation via cascaded residual autoencoder," in Proceedings of the IEEE conference on computer vision and pattern recognition, 2017, pp. 1405- 1414. [47] Y. Wang, Z. Cui, and Y. Li, "Distribution- consistent modal recovering for incomplete multimodal learning," in Proceedings of the IEEE/CVF International Conference on Computer Vision, 2023, pp. 22025- 22034. [48] Z. Lian, L. Chen, L. Sun, B. Liu, and J. Tao, "Gnet: Graph completion network for incomplete multimodal learning in conversation," IEEE Transactions on pattern analysis and machine intelligence, vol. 45, no. 7, pp. 8419- 8432, 2023. [49] J. Zeng, J. Zhou, and T. Liu, "Mitigating inconsistencies in multimodal sentiment analysis under uncertain missing modalities," in Proceedings of the 2022 Conference on Empirical Methods in Natural Language Processing, 2022, pp. 2924- 2934. [50] M. Li, D. Yang, X. Zhao, S. Wang, Y. Wang, K. Yang, M. Sun, D. Kou, Z. Qian, and L. Zhang, "Correlation- decoupled knowledge distillation for multimodal sentiment analysis with incomplete modalities," in Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition, 2024, pp. 12458- 12468. [51] S. Wei, C. Luo, and Y. Luo, "Mmanet: Margin- aware distillation and modality- aware regularization for incomplete multimodal learning," in Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition, 2023, pp. 20039- 20059. [52] F. Liu, X. Wu, S. Ge, W. Fan, and Y. Zou, "Federated learning for vision- and language grounding problems," in AAAI, vol. 34, no. 07, 2020, pp. 1157- 11579. [53] T. Guo, S. Guo, and J. Wang, "Pfedprompt: Learning personalized prompt for vision- language models in federated learning," in Proceedings of the ACM Web Conference 2023, 2023, pp. 1364- 1374. [54] Q. Dai, D. Wei, H. Liu, J. Sun, L. Wang, and Y. Zheng, "Federated modality- specific encoders and multimodal anchors for personalized brain tumor segmentation," in Proceedings of the AAAI Conference on Artificial Intelligence, vol. 38, no. 2, 2024, pp. 1445- 1453. [55] K. Borazjani, N. Khosravan, L. Ying, and S. Hosseinalipour, "Multimodal federated learning for cancer staging over non- iid datasets with unbalanced modalities," IEEE Transactions on Medical Imaging, 2024. [56] Y. Zhao, P. Barnaghi, and H. Haddadi, "Multimodal federated learning on iot data," in 2022 IEEE/ACM IoTDD. IEEE, 2022, pp. 43- 54. [57] S. Poria, D. Hazarika, N. Majumder, G. Naik, E. Cambria, and R. Mihalcea, "Meld: A multimodal multi- party dataset for emotion recognition in conversations," arXiv preprint arXiv:1810.02508, 2018. [58] H. Cao, D. G. Cooper, M. K. Keutmann, R. C. Gur, A. Nenkova, and R. Verma, "Crema- d: Crowd- sourced emotional multimodal actors dataset," IEEE transactions on affective computing, vol. 5, no. 4, pp. 377- 390, 2014.

[59] K. Soomro, A. R. Zamir, and M. Shah, "Ucf101: A dataset of 101 human actions classes from videos in the wild," arXiv:1212.0402, 2012. [60] M. Monfort, A. Andonian, B. Zhou, K. Ramakrishnan, S. A. Bargal, T. Yan, L. Brown, Q. Fan, D. Gutfreund, C. Vondrick et al., "Moments in time dataset: one million videos for event understanding," IEEE transactions on pattern analysis and machine intelligence, vol. 42, no. 2, pp. 502- 508, 2019. [61] D. Kiela, H. Firooz, A. Mohan, V. Goswami, A. Singh, P. Ringshia, and D. Testuggine, "The hateful memes challenge: Detecting hate speech in multimodal memes," Advances in neural information processing systems, vol. 33, pp. 2611- 2624, 2020. [62] F. Alam, F. Ofli, and M. Imran, "Crisismmd: Multimodal twitter datasets from natural disasters," in Proceedings of the international AAAI conference on web and social media, vol. 12, no. 1, 2018. [63] T. Do, B. X. Nguyen, V. Pham, T. Tran, E. Tijputra, Q. D. Tran, and A. Nguyen, "Reducing training time in cross- silo federated learning using multigraph topology," in Proceedings of the IEEE/CVF International Conference on Computer Vision, 2023, pp. 19409- 19419. [64] X. Fang, M. Ye, and X. Yang, "Robust heterogeneous federated learning under data corruption," in Proceedings of the IEEE/CVF International Conference on Computer Vision, 2023, pp. 5020- 5030. [65] B. Xiong, X. Yang, Y. Song, Y. Wang, and C. Xu, "Client- adaptive cross- model reconstruction network for modality- incomplete multimodal federated learning," in Proceedings of the 31st ACM International Conference on Multimedia, 2023, pp. 1241- 1249. [66] J. Chen and A. Zhang, "Fedmsplit: Correlation- adaptive federated multitask learning across multimodal split networks," in Proceedings of the 28th ACM SIGKDD conference on knowledge discovery and data mining, 2022, pp. 87- 96. [67] Y. Zhao, Y. Zhang, B. Zhou, X. Qian, K. Song, and X. Cai, "Contrast then memorize: Semantic neighbor retrieval- enhanced inductive multimodal knowledge graph completion," in Proceedings of the 47th International ACM SIGIR Conference on Research and Development in Information Retrieval, 2024, pp. 102- 111. [68] Y. Zhang, Z. Chen, L. Guo, Y. Xu, B. Hu, Z. Liu, W. Zhang, and H. Chen, "Tokenization, fusion, and augmentation: Towards fine- grained multi- modal entity representation," 2024. [Online]. Available: https://arxiv.org/abs/2404.09468[69] A. Bordes, N. Usunier, A. Garcia- Duran, J. Weston, and O. Yakhnenko, "Translating embeddings for modeling multi- relational data," Advances in neural information processing systems, vol. 26, 2013. [70] T. Trouillon, J. Welbl, S. Riedel, E. Gaussier, and G. Bouchard, "Complex embedding for simple link prediction," in International conference on machine learning. PMLR, 2016, pp. 2071- 2080. [71] Z. Sun, Z.- H. Deng, J.- Y. Nie, and J. Tang, "Rotate: Knowledge graph embedding by relational rotation in complex space," in ICLR, 2018. [72] H. Peng, H. Li, Y. Song, V. Zheng, and J. Li, "Differentially private federated knowledge graphs embedding," in Proceedings of the 30th ACM International Conference on Information & Knowledge Management, 2021, pp. 1416- 1425. [73] W. Huang, J. Liu, T. Li, S. Ji, D. Wang, and T. Huang, "Fedcke: Cross- domain knowledge graph embedding in federated learning," IEEE Transactions on Big Data, 2022. [74] K. Zhang, Y. Wang, H. Wang, L. Huang, C. Yang, X. Chen, and L. Sun, "Efficient federated learning on knowledge graphs via privacy- preserving relation embedding aggregation," in Findings of the Association for Computational Linguistics: EMNLP 2022, 2022, pp. 613- 621. [75] G. Yang, L. Zhang, Y. Liu, H. Xie, and Z. Mao, "Exploiting pre- trained language models for black- box attack against knowledge graph embeddings," ACM Transactions on Knowledge Discovery from Data, 2024. [76] E. Zhou, S. Guo, Z. Ma, Z. Hong, T. Guo, and P. Dong, "Poisoning attack on federated knowledge graph embedding," in Proceedings of the ACM on Web Conference 2024, 2024, pp. 1998- 2008. [77] Y. Hu, W. Liang, R. Wu, K. Xiao, W. Wang, X. Li, J. Liu, and Z. Qin, "Quantifying and defending against privacy threats on federated knowledge graph embedding," in Proceedings of the ACM Web Conference 2023, 2023, pp. 2306- 2317. [78] J. Devlin, M.- W. Chang, K. Lee, and K. Toutanova, "Bert: Pre- training of deep bidirectional transformers for language understanding," in NAACL, 2019, pp. 4171- 4186. [79] A. Dosovitskiy, L. Beyen, A. Kolesnikov, D. Weissenborn, X. Zhai, T. Unterthiner, M. Dehghani, M. Minderer, G. Heigold, S. Gelly et al., "An image is worth 16x16 words: Transformers for image recognition at scale," in International Conference on Learning Representations, 2020.

[80] T.- M. H. Hsu, H. Qi, and M. Brown, "Measuring the effects of nonidentical data distribution for federated visual classification," arXiv preprint arXiv:1909.06335, 2019. [81] J. Sohl- Dickstein, E. Weiss, N. Maheswaranathan, and S. Ganguli, "Deep unsupervised learning using nonequilibrium thermodynamics," in ICML. PMLR, 2015, pp. 2256- 2265. [82] J. Ho, A. Jain, and P. Abbeel, "Denoising diffusion probabilistic models," Advances in neural information processing systems, vol. 33, pp. 6840- 6851, 2020. [83] C. Luo, "Understanding diffusion models: A unified perspective," arXiv preprint arXiv:2208.11970, 2022. [84] P. P. Liang, T. Liu, L. Ziyin, N. B. Allen, R. P. Auerbach, D. Brent, R. Salakhutdinov, and L.- P. Morency, "Think locally, act globally: Federated learning with local and global representations," arXiv preprint arXiv:2001.01523, 2020. [85] G. Hinton, O. Vinylas, and J. Dean, "Distinguishing the knowledge in a neural network," arXiv preprint arXiv:1503.02531, 2015. [86] Y. Liu, H. Li, A. Garcia- Duran, M. Nieupert, D. Onoro- Rubio, and D. S. Rosenblum, "Mmkg: multi- modal knowledge graphs," in The Semantic Web: 16th International Conference, ECSWC 2019, Portorož, Slovenia, June 2- 6, 2019, Proceedings 16. Springer, 2019, pp. 459- 474. [87] L. Yao, C. Mao, and Y. Luo, "Kg- bert: Bert for knowledge graph completion," arXiv preprint arXiv:1909.03193, 2019. [88] D. P. Kingma and J. Ba, "Adam: A method for stochastic optimization," arXiv preprint arXiv:1412.6980, 2014. [89] W. Wang, Y. Xu, F. Feng, X. Lin, X. He, and T.- S. Chua, "Diffusion recommender model," in Proceedings of the 46th International ACM SIGIR Conference on Research and Development in Information Retrieval, 2023, pp. 832- 841. [90] D. Kiela, E. Grave, A. Joulin, and T. Mikolov, "Efficient large- scale multi- modal classification," in AAAI, vol. 32, no. 1, 2018. [91] L. Van der Maaten and G. Hinton, "Visualizing data using t- sne," Journal of machine learning research, vol. 9, no. 11, 2008.