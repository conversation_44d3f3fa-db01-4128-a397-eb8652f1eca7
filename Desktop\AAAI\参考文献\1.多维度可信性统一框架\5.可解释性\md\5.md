# Benchmarking Mutual Information-based Loss Functions in Federated Learning

Sarang  $\mathbf{S}^*$  , Harsh <PERSON>\*, <PERSON>le<PERSON>  $\mathbf{L}\mathbf{i}^{\dagger}$  , <PERSON>\*, <PERSON><PERSON><PERSON> <PERSON><PERSON>\* BITS <PERSON>lani, K K Birla Goa Campus, India \*Queen Mary University of London, United Kingdom {f20210906, f20230804, arnabp}  $@$  goa.bits- pilani.ac.in, {q.li, ahmed.sayed}  $@$  qmul.ac.uk

Abstract- Federated Learning (FL) has attracted considerable interest due to growing privacy concerns and regulations like the General Data Protection Regulation (GDPR), which stresses the importance of privacy- preserving and fair machine learning approaches. In FL, model training takes place on decentralized data, so as to allow clients to upload a locally trained model and receive a globally aggregated model without exposing sensitive information. However, challenges related to fairness- such as biases, uneven performance among clients, and the "free rider" issue- complicates its adoption. In this paper, we examine the use of Mutual Information (MI)- based loss functions to address these concerns. MI has proven to be a powerful method for measuring dependencies between variables and optimizing deep learning models. By leveraging MI to extract essential features and minimize biases, we aim to improve both the fairness and effectiveness of FL systems. Through extensive benchmarking, we assess the impact of MI- based losses in reducing disparities among clients while enhancing the overall performance of FL.

Index Terms- Federated Learning, Fairness in FL, Mutual Information, Client Heterogeneity

# I. INTRODUCTION

Federated Learning (FL) is a paradigm in distributed machine learning (ML) that enables models to be trained cooperatively on decentralized data [1]. The relevance of FL has grown significantly in light of stringent privacy regulations like the General Data Protection Regulation (GDPR) [2]. These laws impose strict penalties for violations, underscoring the need for privacy- preserving and fair machine learning solutions. In FL, selected clients receive a global model from a central server called the aggregator, and the clients then train the model locally using their own datasets. The clients send model updates, rather than sensitive data, back to the server, which aggregates them and iteratively enhances the global model [3].

Fairness in federated learning is crucial due to the black box nature of machine learning systems, which can result in varied client performance, behaviour, or treatment. Unfairness may arise, for example, if models harbour biases against specific data patterns [4], [5], if the aggregator doesn't account for differences in clients' hardware or network capacity, or if a client contributes little to training yet benefits from the shared global models (the 'free rider problem) [6], [7]. Ensuring fairness directly aligns with GDPR's emphasis on equitable treatment and transparency and balancing trade- offs between performance and fairness remains a persistent challenge in FL systems. Mutual information (MI) has emerged as a powerful concept for designing loss functions in deep learning, providing a principled way to measure the dependency between variables. MI quantifies the dependency between two random variables  $X$  and  $Y$  by measuring how different the joint distribution of the pair  $(X,Y)$  is from the product of the marginal distributions of  $X$  and  $Y$  .MI- based losses enable models to quantify and optimize the dependence between input data and learned representations, capturing salient features while minimizing irrelevant information. They have been successfully employed in tasks such as representation learning, clustering, and domain adaptation, often by maximizing MI between input features and learned representations or minimizing MI between independent features to enforce disentanglement. Efficient estimation of MI in high- dimensional settings, a historically challenging problem, has been addressed by leveraging neural network- based approximations such as MINE (Mutual Information Neural Estimation) [8] and variational bounds [9], [10]. These advancements enable the practical use of MI as a core objective in model optimization and potentially improve both fairness and performance [11], [12].

In this paper, we explore and benchmark the effectiveness of MI- based loss functions in federated learning, with a focus on both performance and fairness. Through comprehensive benchmarking, we assess their potential to reduce disparities in client outcomes while improving the overall efficiency of FL systems. Our key contributions include: (1) Evaluation of state- of- the- art Mutual Information- based loss functions for federated learning. (2) Analysis and benchmarking of the trade- offs between performance and fairness across different MI- based loss functions. (3) Empirical results demonstrating that MI- based losses enhance both performance and fairness under diverse FL settings.

# II. BACKGROUND AND RELATED WORK

In this part, we cover the influence of data distributions on FL training and then introduce Mutual Information as an emerging approach to overcome data heterogeneity.

# A. IID and Non-IID Distributions

In machine learning, data can be categorized based on distributional assumptions. Independent and Identically Distributed (IID) data assumes that all samples are drawn indepen

dently from the same underlying probability distribution. This assumption simplifies analysis, enabling reliable theoretical guarantees like convergence rates and performance bounds [13], [14]. In contrast, Non- Independent and Identically Distributed (Non- IID) data violates either the independence, identical distribution, or both conditions. Non- IID data arises in scenarios where samples exhibit dependencies (e.g., temporal or spatial correlations) or where distributions differ across subsets of data, such as demographic groups or devices [15], [16]. Challenges in federated learning are amplified with Non- IID data, as clients often hold data with varying distributions. This heterogeneity leads to slower model convergence, performance degradation, and fairness concerns, particularly when certain groups dominate the learning process while others remain underrepresented [17]. Addressing these challenges requires methods that account for client- level variability and ensure balanced contributions across all distributions [18]–[20].

# B. Mutual Information

Mutual information (MI) [21] measures the dependence between two random variables. It quantifies the reduction in uncertainty about one variable, given knowledge of the other. Formally, MI between two random variables  $X$  and  $Z$  is defined as:

$$
I(X;Z) = H(X) - H(X\mid Z),
$$

where  $H(X)$  is the Shannon entropy of  $X$  ,and  $H(X\mid Z)$  is the conditional entropy of  $X$  given  $Z$  .Intuitively,  $I(X;Z)$  measures how much knowing  $Z$  reduces the uncertainty in  $X$  [22]. Equivalently, MI can also be expressed as the KullbackLeibler (KL) divergence between the joint distribution  $P_{X,Z}$  and the product of marginals  $P_X\otimes P_Z$  ..

$$
I(X;Z) = D_{\mathrm{KL}}(P_{X,Z}\parallel P_X\otimes P_Z).
$$

By modelling the relationships between data distributions across clients, MI can help capture the shared information between client data and the global model. Two widely used representations of MI based on KL formulation are the Donsker- Varadhan (DV) representation [23] and the Nguyen- Wainwright- Jordan (NWJ) representation [24]. The DV representation is defined as:

$$
D_{DV}(X,Y) = \sup_{T:\Omega \to \mathbb{R}}\mathbb{E}_P[T] - \log \left(\mathbb{E}_Q[e^T]\right),
$$

where  $P$  and  $Q$  are probability distributions over the domain  $\Omega \subset \mathbb{R}^d$  and  $T$  is any function. For MI,  $P$  corresponds to the joint distribution  $P_{XY}$  ,and  $Q$  is the product of the marginals  $P_{X}P_{Y}$  . The optimal  $T^{*}$  for this formulation is given by  $\begin{array}{r}T^{*} = \log \frac{dP}{dQ} +C \end{array}$  where  $C$  is a constant. In contrast, the NWJ representation is derived using Fenchel's inequality [25]. It is expressed as:

$$
D_{NWJ}(X,Y) = \sup_{T:\Omega \to \mathbb{R}}\mathbb{E}_P[T] - \mathbb{E}_Q\left[e^{T - 1}\right].
$$

The optimal  $T^{*}$  in this case is  $\log \frac{dP}{dQ} +1$  , which differs from the DV formulation due to its self- normalization [8]. While both representations are foundational to variational MI estimation, the DV representation guarantees tighter lower bounds for MI, as shown by Ruderman et al. [26] and Polyanskiy and Wu (2014) [27]. Additionally, it has a self- normalizing property, as noted by Belghazi et al. [8]. These dual representations provide robust theoretical tools for constructing variational bounds on MI.

Variational MI Estimation Neural networks have led to the development of various neural network- based variational bounds for mutual information (MI). These bounds are widely used in applications like contrastive learning [9], [28], [29] and generative adversarial training [8], [30]. Typically, they aim to estimate  $T^{*}$  using a neural network  $T_{\theta}: \Omega \to \mathbb{R}$ , referred to as the statistics network [8], which produces a single real- valued output for input sample pairs. Leveraging the dual representation of KL divergence, MI can be efficiently estimated and optimized, enabling its integration into loss functions. Although MI- based losses have shown computational tractability, they still struggle with their instability during optimization and training caused by drifting and exploding neural network outputs [12]. A simple yet effective regularization has been proposed for the dual representation of KL Divergence to combat the instability [12]. This regularized representation is incorporated into variational MI bounds and used in MI losses designed to balance the maximization of MI, with additional terms to ensure stability and prevent overfitting.

# C. Fairness evaluation

Fairness is a critical challenge in Federated Learning (FL) due to its decentralized nature, where clients with diverse data distributions, participation levels, and computational capabilities collaboratively train a global model. Unlike centralized machine learning, FL amplifies fairness concerns such as unequal model performance, biases against under- represented subgroups, and disproportionate rewards for contributions [16], [31]. These challenges are particularly concerning in high- stakes domains like healthcare and finance, where fairness is not only a technical objective but also a societal and ethical requirement [32], [33]. Ensuring fairness in FL systems is vital to fostering trust among participants and promoting its widespread adoption in sensitive applications. Traditional definitions of fairness, such as "fairness through awareness" (treating similar inputs similarly) [32] and "group fairness" (ensuring statistical parity for sensitive subgroups) [33], are partially relevant but insufficient for FL's unique complexities. The decentralized setup of FL introduces additional layers of heterogeneity, requiring novel fairness metrics and frameworks tailored to the interplay between clients and the server [34]. Symptom- based fairness approaches, like those proposed by Vucinich et al. [35], identify measurable indicators of unfairness, such as performance variance across clients, and are intuitive and actionable. Mechanism- driven fairness, such as that defined by Rafi et al. [36], focuses on mitigating unfairness through specific interventions like client resource balancing but may overlook system- wide dynamics like free- riding or

malicious behaviour [37]. To measure fairness, metrics are required. Despite the additional complexity with FL, some of the centralized metrics to detect algorithmic bias are still insightful in the context of individual client's performance [38]. These include "Equalized Odds" under the umbrella of "group fairness" that measures the difference in the true positive and false positive rates between two groups, one with and one without a binary sensitive attribute [33]. Uniformity over certain metrics can be indicative of fairness, with the most indicative measure being Jain's Fairness Index (JFI) [39].

Jain's Fairness Index (JFI) is a bounded, non- linear function based on the coefficient of variation, which is the ratio of the standard deviation to the mean of a population [39]. The value of  $\mathbf{J}(\mathbf{x})$  ranges from 0 to 1, where a value of 1 indicates that all clients are performing equally, while a value of  $1 / |S_{k}|$  represents the worst- case scenario, where the performance results are entirely non- uniform for a given variable  $x$ :

$$
J(x) = \frac{\left(\sum_{i = 1}^{|S_k|}x_i\right)^2}{|S_k|\sum_{i = 1}^{|S_k|}x_i^2}
$$

JFI quantifies fairness based on the coefficient of variation, which can be accuracy, contribution or the trade- off between two different metrics. Together, these tools enable a nuanced understanding of fairness in FL, addressing individual, group, and global equity in a decentralized and heterogeneous learning environment [16], [40].

# III. METHODOLOGY

This section outlines the experimental setup, datasets, federated learning strategies, loss functions, and other configurations used in this study. The experiments were conducted using simulations implemented in the Flower [41] framework, a flexible and scalable platform for federated learning research.

# A. Mutual Information-based losses

We test three realizations for each representation of Mutual Information based on KL Divergence, as shown in Table I.

We evaluate the performance of mutual information (MI)- based losses using the regularized versions of these MI estimations introduced in Choi et al. [12], including ReMINE  $(I_{\mathrm{MINE}})$ , ReInfoNCE  $(I_{\mathrm{InfoNCE}})$ , ReSMILE  $(I_{\mathrm{SMILE}})$ , ReNWJ  $(I_{\mathrm{NWJ}})$ , ReTUBA  $(I_{\mathrm{TUBA}})$ , ReJS  $(I_{\mathrm{JS}})$ , and ReNWJJS  $(I_{\mathrm{NWJJS}})$ , and compare them against the Cross- Entropy loss as our baseline. We fine- tune the parameters  $\alpha$  and  $\beta$ , which control the regularization strength in these losses and select the best- performing values to incorporate into the experimental setup.

# B. Fairness Metrics

We benchmark the MI- based losses on the federated fairness metrics introduced in Dillley et al. [43] to present a complete, symptom- driven (in order to be measurable and independent of the system's design) and logical definition of fairness. The following notions define the metrics used; each has an associated  $f$  value, valid  $f \in (0,1]$  with a value of 1 attributed to complete fairness.

TABLE I: MI Representations and Losses.  

<table><tr><td>Representation</td><td>Realization</td><td>Reference</td></tr><tr><td rowspan="3">Donsker-Varadhan</td><td>IMINE</td><td>Belghazi et al. [8]</td></tr><tr><td>ISMILE</td><td>Song et al. [42]</td></tr><tr><td>INFORCE</td><td>Oord et al. [9]</td></tr><tr><td rowspan="3">Nguyen-Wainwright-Jordan</td><td>INWJ</td><td>Nguyen et al. [24]</td></tr><tr><td>ITUBA</td><td>Poole et al. [10]</td></tr><tr><td>IJS</td><td>Hjelm et al. [11]</td></tr></table>

Individual Fairness: The measure of client performance is proportionate to their contribution. Using  $G$ , which is 'Performance Gains' or the ratio of the accuracy of each client after training to their contribution to the global model (measured in Shapely Values) [44], as the coefficient of variability in JFI [39], it measures the uniformity of fairness to each individual client.

$$
f_{j,k} = J(\mathcal{G}),
$$

where  $\mathcal{G} = \{x_{n,k} / s_n\}$ , with  $x_{n,k}$  as client  $n$ 's performance in  $k$  round and  $s_n$  their contribution.

Group Fairness: Assesses equity across sensitive subgroups defined by attributes such as demographics. Based on Equalized Odds  $[E_n(a)]$  (parity in true and false positive rates between groups) for each client, it evaluates the median of  $E_{n}(a)$  across all clients to ensure resiliency to outliers.

$$
f_{g,k} = \mathrm{median}\left(\left\{\frac{1}{|A|}\sum_{a\in A}E_n(a)\right\}\right),
$$

where  $A$  represents the set of all sensitive attributes,  $E_{n}(a)$  is the fairness score for sensitive attribute  $a$  and  $f_{g,k}$  aggregates fairness scores across all attributes.

Incentive Fairness: Evaluates whether rewards, such as model improvements, are distributed in proportion to contributions. Similar to Individual Fairness, it uses JFI [39] with  $R$ , which is the ratio of the reward each client receives, which, in the general case, we take as the accuracy achieved by the global model sent to each local client, to the contribution of each individual client (Shapely Values) [44].

$$
f_{r,k} = J(R),
$$

where  $R = \{r_{n,k} / s_n\}$ .

Orchestrator Fairness: This metric works as a measure of the progress of the server's objective, which, in the general federated learning case, we take as the accuracy of the global model by taking the mean of the normalized accuracy across all clients.

$$
f_{o,k} = \frac{1}{|S_k|}\sum_{n\in S_k}\hat{x}_{n,k},
$$

where  $\hat{x}_{n,k}$  is the normalized accuracy of client  $n$  in round  $k$  ensuring the global model benefits all equitably. These four notions  $[f_j, f_g, f_r, f_o]$  together encompass a complete definition of fairness in federated learning systems [43].

![](images/e840378eab33d8cc3abaf4fcb1e5e60524d5d15479d5312987cbda74d1b02175.jpg)  
Fig. 1: Cross-Device FL in IID & Non-IID settings.

# C. Experimental Setup

We evaluate the performance of mutual information (MI)- based loss functions in federated learning using three federated strategies: FedAvg [1], Ditto [45], and q- FedAvg [46]. The experiments were performed on the CIFAR- 10 dataset, a widely used image classification benchmark, under various scenarios: (1) Cross- Silo (10 clients) and Cross- Device (100 clients), with a client participation rate of  $5\%$ . (2) Data partitioning based on IID and non- IID distributions. The experiments are run over 30 communication rounds, with each client performing 10 local epochs per round, consistent with Dilley et al. [43]. Each client uses a local train- test split of 90- 10, and the model is optimized using the Adam optimizer, ensuring stability during training. All experiments were conducted on a computing node equipped with two NVIDIA RTX 4090 GPUs to ensure efficient training. We use the model from the Flower Tutorial [41]. It consists of a simple convolutional neural network with two convolutional layers, one max- pooling layer, and three fully connected layers, as used in FL literature [1], [43].

# IV. ANALYSIS

We present a comprehensive evaluation of various MI- based loss functions, examining their impact on both performance and fairness across different federated learning algorithms and data distribution scenarios. Our analysis focuses on two key metrics: General Fairness and Performance (Orchestrator Fairness). General Fairness  $(F_{t})$  measures system fairness by combining multiple fairness interpretations described in Section III- B. It is computed as the arithmetic mean of four distinct fairness components:

$$
F_{t} = (f_{j} + f_{g} + f_{r} + f_{o}) / 4
$$

where  $f_{j}, f_{g}, f_{r}$ , and  $f_{o}$  represent individual fairness, Group fairness, Incentive fairness, and Orchestrator fairness, respectively. Performance, measured through Orchestrator Fairness, captures the system's overall effectiveness from the aggregator's perspective by computing the average performance across all clients. To understand the practical implications of different loss functions, we analyze the inherent trade- off between General Fairness and Performance across various experimental settings. This approach allows us to identify solutions that achieve high fairness without significantly compromising system performance.

# A. Does data distribution have an effect on fairness?

Our experimental results reveal a significant impact of data distribution on fairness outcomes in federated learning. We observe consistently higher general fairness values in IID scenarios compared to Non- IID scenarios across Cross- Device (Fig. 1) and Cross- Silo (Fig. 2) settings. This pattern suggests that achieving fairness is more straightforward when data is independently and identically distributed across clients. This is likely because IID inherently represents a fair data distribution, regardless of the chosen loss function.

Performance of MI- based losses in IID settings. In IID environments, MI- based loss functions demonstrate remarkable performance and fairness across FedAvg and Ditto algorithms. Figure 1a shows that in cross- device scenarios, losses such as  $I_{\mathrm{ReJS}}$  and  $I_{\mathrm{ReMINE}}$  extract both higher performance and fairness from the Ditto algorithm. In both cross- device and cross- silo settings,  $I_{\mathrm{ReNWI}}$  and ReSMILE achieve higher fairness and performance for FedAvg.

Performance of MI- based losses in Non- IID settings. While Non- IID scenarios present more challenging conditions, MI- based loss functions maintain their effectiveness. In the cross- device Non- IID setting (Fig. 1b), the combination of FedAvg with  $I_{\mathrm{ReInfNCE}}$  achieves 0.5 general fairness over CrossEntropy while matching the performance, and similar trends can be observed for  $q - \mathrm{FedAvg}$  as well. An impressive jump in general fairness can be observed in the case of Ditto ( $I_{\mathrm{ReInfNCE}}$  (0.725) vs  $CE$  (0.5)) in Fig. 2b. These results demonstrate the adaptability of MI- based approaches to heterogeneous data distributions.

# B. Are the benefits consistent across different FL scenarios?

The fairness- performance trade- offs show remarkable consistency between cross- device and cross- silo settings, as demonstrated by comparing Figures 1a and 2a (IID) and Figures 1b and 2b (Non- IID), indicating that MI- based loss functions offer scalable benefits across various federated learning scenarios and client configurations.

![](images/327ca5b3cff7c68fbd2b749ac9a75feabc7952a290fe57c8a4b6b778e18f3336.jpg)

# C. Breaking down the fairness metrics

![](images/69290e87b8277aa6befe29b097c848a30d5a5913d7b7c99e47a0a9879d288a49.jpg)  
Fig. 2: Cross-Silo FL in IID and Non-IID settings.  
Fig. 3: Comparison between Ditto cross-device IID experiments for CE and  $I_{\mathrm{ReJS}}$

![](images/046d7eeb2da946f6af59d971417eb391ce8604113dae6aad5f0f1b557f4e9a93.jpg)  
Fig. 4: Comparison between FedAvg cross-device Non-IID experiments for CE and  $I_{\mathrm{ReInfoNCE}}$

Figure 3 shows the component- wise breakdown of the fairness metrics for  $CE$  and  $I_{\mathrm{ReJS}}$  for the Ditto algorithm, which clearly illustrates the higher mean and lower variance for each of the four components of  $I_{\mathrm{ReJS}}$ , which leads to its overall higher general fairness. Figure 4 shows the component- wise breakdown of the fairness metrics for  $CE$  and  $I_{\mathrm{ReInfoNCE}}$  using FedAvg in a Non- IID setting. This clearly illustrates higher mean and maximum values for Individual and Incentive Fairness as well as lower variance for Protected Group and Orchestrator Fairness using  $I_{\mathrm{ReInfoNCE}}$ . Although this ensures overall higher general fairness, it is worth noting that in this particular setting,  $CE$  has a marginally higher mean in Group Fairness and lower variance in Incentive fairness, ensuring more consistency across those metrics. Our results demonstrate that MI- based loss functions can effectively enhance fairness in federated learning systems without significantly compromising performance. This is particularly noteworthy in challenging Non- IID scenarios, where traditional approaches often struggle to maintain client fairness. The consistent performance across different architectures and data distributions suggests that MI- based loss functions provide a robust solution for fairness- aware federated learning.

# V. CONCLUSION

In this paper, we explored the effectiveness of Mutual Information (MI)- based loss functions in federated learning by focusing on fairness and performance. Our results show that MI- based losses improve fairness across different FL strategies and data distributions, particularly in Non- IID settings where traditional approaches struggle. Notably, losses like  $I_{\mathrm{ReJS}}$  and  $I_{\mathrm{ReInfoNCE}}$  achieved higher fairness while maintaining strong performance. While MI- based losses introduce computational overhead, they offer a promising direction for fairness- aware FL. Future work can optimize their efficiency and explore

hybrid approaches that introduce task- specific priors for higher fairness. Overall, MI- based losses provide a viable solution for improving fairness in decentralized learning.

# REFERENCES

[1] Brendan McMahan, Eider Moore, Daniel Ramage, Seth Hampson, and Blaise Aguera y Arcas. Communication- Efficient Learning of Deep Networks from Decentralized Data. In International Conference on Artificial Intelligence and Statistics, 2017. [2] European Parliament and Council of the European Union. Regulation (EU) 2016/679 of the European Parliament and of the Council. [3] Ahmed M. Abdelmoniem, Yomna M. Abdelmoniem, and Ahmed Elzanaty. A2fl: Availability- aware selection for machine learning on clients with federated big data. In IEEE International Conference on Communications (ICC), 2023. [4] Yasmine Djebrouni, Nawel Benarba, Ousmane Touat, Pasquale De Rosa, Sara Bouchenak, Angela Bonifati, Pascal Felber, Vania Marangozova, and Valerio Schiavoni. Bias mitigation in federated learning for edge computing. Proc. ACM Interact. Mob. Wearable Ubiquitous Technol. 7(4), 2024. [5] Hongyan Chang and Reza Shokri. Bias propagation in federated learning, 2023. [6] Yann Fraboni, Richard Vidal, and Marco Lorenzi. Free- rider attacks on model aggregation in federated learning. In Proceedings of The 24th International Conference on Artificial Intelligence and Statistics, 2021. [7] Zhenqian Zhu, Jiangang Shu, Xing Zou, and Xiaohua Jia. Advanced free- rider attacks in federated learning. In NeurIPS Workshop on New Frontiers in Federated Learning. Privacy, Fairness, Robustness, Personalization and Data Ownership, 2021. [8] Mohamed Ishmael Belghazi, Aristide Baratin, Sai Rajeshwar, Sherjil Ozair, Yoshua Bengio, Aaron Courville, and R Devon Hjelm. Mutual information neural estimation. In International Conference on Machine Learning, 2018. [9] Aaron van den Oord, Yazhe Li, and Oriol Vinyals. Representation learning with contrastive predictive coding. In Advances in Neural Information Processing Systems, 2018. [10] Ben Poole, Sherjil Ozair, Aaron Van Den Oord, Alexander A Alemi, and George Tucker. On variational bounds of mutual information. In International Conference on Machine Learning, 2019. [11] R Devon Hjelm, Alexander Fedorov, Samuel Lavoie- Marchildon, Karan Grewal, Philip Bachman, Adam Trischler, and Yoshua Bengio. Learning deep representations by mutual information estimation and maximization. In International Conference on Learning Representations, 2019. [12] Kwanghee Choi and Siyeong Lee. Combating the instability of mutual information- based losses via regularization. In The 38th Conference on Uncertainty in Artificial Intelligence, 2022. [13] Christopher M. Bishop. Pattern Recognition and Machine Learning. Springer, 2006. [14] Ian Goodfellow, Yoshua Bengio, and Aaron Courville. Deep Learning. MIT Press, 2016. [15] Dengyong Shen, David Carlson, and Lawrence Carin. Modeling temporal dependencies with recurrent neural networks. Journal of Machine Learning Research, 17(1):660- 688, 2016. [16] Peter Kairouz, H. Brendan McMahan, et al. Advances and open problems in federated learning. Foundations and Trends in Machine Learning, 14(1- 2):1- 210, 2021. [17] Ahmed M. Abdelmoniem, Chen- Yu Ho, Pantelis Papageorgiou, and Marco Canini. A comprehensive empirical study of heterogeneity in federated learning. IEEE Internet of Things Journal, 10(16):14071- 14083, 2023. [18] Tian Li, Anit Kumar Sahu, et al. Federated optimization in heterogeneous networks. In Proceedings of Machine Learning Research, volume 120, pages 429- 443, 2020. [19] Ahmed M. Abdelmoniem, Atal Narayan Sahu, Marco Canini, and Suhaib A. Fahmy. REFL: Resource- Efficient Federated Learning. In Proceedings of ACM EuroSys, page 215- 232, 2023. [20] Ahmad Faraz Khan, Azal Ahmad Khan, Ahmed M. Abdelmoniem, Samuel Fountain, Ali R. Butt, and Ali Anwar. Float: Federated learning optimizations with automated tuning. In ACM EuroSys, 2024. [21] C. E. Shannon. A mathematical theory of communication. The Bell System Technical Journal, 27(3):379- 423, 1948. [22] Thomas M. Cover and Joy A. Thomas. Elements of Information Theory. John Wiley & Sons, Inc., 2005.

[23] Monroe D. Donsker and S. R. Srinivasa Varadhan. Asymptotic evaluation of certain markov process expectations for large time, i. Communications on Pure and Applied Mathematics, 28(1):1- 47, 1975. [24] XuanLong Nguyen, Martin J. Wainwright, and Michael I. Jordan. Estimating divergence functionals and the likelihood ratio by convex risk minimization. IEEE Transactions on Information Theory, 56(11):5847- 5861, 2010. [25] Jean- Baptiste Hiriat- Urruty and Claude Lemarechal. Convex Analysis and Minimization Algorithms I: Fundamentals, volume 305 of Grundlehren der mathematischen Wissenschaften. Springer Science & Business Media, illustrated edition, 1996. [26] Avraham Ruderman, Mark Reid, Dario Garcia- Garcia, and James Petterson. Tighter variational representations of f- divergences via restriction to probability measures, 2012. [27] Yury Polyanskiy and Yihong Wu. Lecture notes on information theory, 2014. Lecture Notes for ECE563 (UIUC), 6 (2012- 2016):7. [28] Ting Chen, Simon Kornblith, Mohammad Norouzi, and Geoffrey Hinton. A simple framework for contrastive learning of visual representations. In International Conference on Machine Learning, 2020. [29] Gengxiang Chen, Kai Li, Ahmed M. Abdelmoniem, and Linlin You. Exploring representational similarity analysis to protect federated learning from data poisoning. In Companion Proceedings of the ACM Web Conference, 2024. [30] Sebastian Nowozin, Botond Cseke, and Ryota Tomioka. f- gan: Training generative neural samplers using variational divergence minimization, 2016. [31] Abhishek Mohan and Omkar Thakkar. Federated learning and the path to fair ai systems. IEEE Access, 10:30205- 30225, 2022. [32] Cynthia Dwork, Moritz Hardt, Toniann Pitassi, Omer Reingold, and Richard S. Zemel. Fairness through awareness. In Proceedings of the 3rd Innovations in Theoretical Computer Science Conference, 2012. [33] Moritz Hardt, Eric Price, and Nati Srebro. Equality of opportunity in supervised learning. In Advances in Neural Information Processing Systems (NeurIPS), volume 29. Curran Associates, Inc., 2016. [34] Mehryar Mohri, Gary Sivek, and Ananda Theertha Suresh. Agnostic federated learning. In Proceedings of the 36th International Conference on Machine Learning (ICML), pages 4615- 4625. PMLR, 2019. [35] Matej Vucinich, Rok Novak, and Bojan Slivnik. Symptom- based fairness in federated learning. In International Conference on Autonomous Agents and MultiAgent Systems, 2020. [36] Muhammad Rafi, Sanjay Kumar, and Shruti Gupta. Mechanism- driven fairness in federated systems. Journal of Machine Learning Research, 22:1- 30, 2021. [37] Gengxiang Chen, Xiaoyi Li, Linlin You, Ahmed M. Abdelmoniem, Yan Zhang, and Chau Yuen. A data poisoning resisitible and privacy protection federated- learning mechanism for ubiquitous iot. IEEE Internet of Things Journal, 12(8):10736- 10750, 2025. [38] Solin Barocas, Moritz Hardt, and Arvind Narayanan. Fairness in Machine Learning: Concepts, Metrics, and Applications. O'Reilly Media, 2017. [39] Raj Jain, Dah- Ming W. Chiu, and William R. Hawe. Quantitative measure of fairness and discrimination for resource allocation in shared systems. DEC Research Report TR- 307, pages 1- 38, 1984. [40] Anna Arouj and Ahmed M. Abdelmoniem. Towards energy- aware federated learning via collaborative computing approach. Computer Communications, 221:131- 141, 2024. [41] Daniel J Beutel, Taner Topal, Akhil Mathur, Xinchi Qiu, Javier Fernandez- Marques, Yan Gao, Lorenzo Sani, Hei Li Kwing, Titouan Parcollet, Pedro PB de Gusmão, and Nicholas D Lane. Flower: A friendly federated learning research framework. arXiv preprint arXiv:2007.14390, 2020. [42] Yang Song and Stefano Ermon. Generative modeling by estimating gradients of the data distribution, 2020. [43] Oscar Dilley, Juan Marcelo Parra- Ullauri, Rasheed Hussain, and Dimitra Simeonidou. Federated fairness analytics: Quantifying fairness in federated learning, 2024. [44] Lloyd S. Shapley. Notes on the n- person game- ii: The value of an n- person game. Research memorandum, RAND Corporation, Santa Monica, CA, 1951. [45] Tian Li, Shengyuan Hu, Ahmad Beirami, and Virginia Smith. Ditto: Fair and robust federated learning through personalization, 2021. [46] Tian Li, Maziar Sanjabi, Ahmad Beirami, and Virginia Smith. Fair resource allocation in federated learning. In International Conference on Learning Representations, 2020.