# Multimodal Federated Learning With Missing Modalities through Feature Imputation Network

Pranav Poudel $^{2,4}$ , <PERSON><PERSON><PERSON> $^{2}$ , <PERSON><PERSON>nna Gyawali $^{3}$ , Georgios Leontidis $^{1}$ , and <PERSON>od Bhattarai $^{1*}$

$^{1}$  University of Aberdeen, UK   $^{2}$  NepAl Applied Mathematics and Informatics Institute for research, Nepal   $^{3}$  West Virginia University, USA   $^{4}$  Fogsphere (Redev.AI), UK

Abstract. Multimodal federated learning holds immense potential for collaboratively training models from multiple sources without sharing raw data, addressing both data scarcity and privacy concerns—two key challenges in healthcare. A major challenge in training multimodal federated models in healthcare is the presence of missing modalities due to multiple reasons, including variations in clinical practice, cost and accessibility constraints, retrospective data collection, privacy concerns, and occasional technical or human errors. Previous methods typically rely on publicly available real datasets or synthetic data to compensate for missing modalities. However, obtaining real datasets for every disease is impractical, and training generative models to synthesize missing modalities is computationally expensive and prone to errors due to the high dimensionality of medical data. In this paper, we propose a novel, lightweight, low- dimensional feature translator to reconstruct bottleneck features of the missing modalities. Our experiments on three different datasets (MIMIC- CXR, NIH Open- I, and CheXpert), in both homogeneous and heterogeneous settings consistently improve the performance of competitive baselines. The code and implementation details are available at:https://github.com/bhattarailab/FedFeatGen

Keywords: Multimodal Learning  $\cdot$  Federated Learning  $\cdot$  Missing Modalities  $\cdot$  Feature Generation

# 1 Introduction

Multimodal learning has emerged as a transformative field of research within medical machine learning. By combining information from diverse sources like imaging, omics, and pathology, multimodal AI holds the potential to transform the healthcare landscape [1]. Inspired by the human capacity to integrate multisensory information for more effective perception and interaction, these models leverage data from multiple modalities to construct a holistic representation of diseases, thereby significantly improving diagnostic accuracy [23,19]. However,

![](images/fcb1e3b08a791840d4ed706d3fd969482d53a9f04125696bf716b26b4dda6ee7.jpg)  
Fig. 1: This figure shows sample of data from three different datasets collected at three different institutions. In CheXpert, there are only X-ray scans available, while the two other benchmarks have both X-ray scans and radiology reports. This demonstrates an instance of missing modality in a real-world scenario.

effective training of such models typically requires substantial amounts of centralized data, which presents a major challenge, especially in healthcare settings, where data are often dispersed across multiple medical centers due to privacy concerns.

Multimodal federated learning is an important research topic due to its growing applications, and it offers an innovative approach for collaboratively training a shared model on heterogeneous healthcare data—such as X- rays, diagnostic reports, and time- series data—without sharing the raw data [21,8]. The main drawback of most prior work on multimodal federated learning is that it assumes the availability of complete modalities, overlooking the challenges posed by missing modalities [17,16,4]. In reality, as shown in Figure 1, multimodal systems often encounter challenges related to missing or incomplete modalities across medical centers due to the unavailability of clinical equipment, variability in data acquisition procedures, or limitations in data storage capacity [21]. Naïve strategies such as zero- imputation and uniform filling are often used to handle missing modalities [26,13]. However, these approaches introduce bias into the global model, which adversely affects its performance and leads to suboptimal federated training. As a result, an increasing number of studies are aiming to fundamentally address the problem of missing modalities [24,21]. While research in the natural domain has been growing rapidly in recent years [25,13,3,20], there are still only a handful of studies in the medical domain [15,18], partly due to the lack of standardized benchmarks.

The existing literature in multimodal federated learning are broadly categorized into three groups: public data- based methods [15,25], class prototype- based methods [13], and architecture- focused methods [3,20]. The primary drawback of public data- based approaches is that model performance heavily depends on the quality and representativeness of the available public data. Class prototype- based methods rely on impractical assumptions, such as having distinct and independent class prototypes, thus limiting their applicability to multiclass classification tasks only. Meanwhile, architecture- focused methods typically require specialized designs, restricting their generalizability. Recently, generative model- based approaches [18] have also been explored, where missing reports are synthesized by pre- training generative models using data from available multimodal clients. Al

though this approach is conceptually simple and easy to implement, it demands significant data and computational resources for effective pre- training. Moreover, the data in healthcare are high- dimensional, such as Whole Slide Images (WSIs) have dimensions of 100,000, and reconstructing such high dimensional data incurs errors introducing un- necessary artifacts, which ultimately results in inferior performance [2].

To address the limitations of existing methods, we propose learning lowdimensional bottleneck features of the missing modalities. To this end, we propose to train a lightweight feature- generation model in a federated- manner that conditionally synthesizes abstract, high- level modality- specific feature representations based on available input modalities (e.g., image features conditioned on text or vice versa). Our approach brings several advantages compared to the previous methods. First, our method does not need to have access of publicly available real data. Also, the bottleneck features have low dimensionality, typically of a few hundred dimensions, which makes them easier to reconstruct with less error [2]. It is also lightweight, which is another advantage. To the best of our knowledge, this is the first work to compare the imputation of missing modalities at different level of input to offer a clear understanding of missing modality imputation strategies in federated scenarios where modality completeness cannot be guaranteed.

To validate our idea, we conducted experiments simulating real- world federated learning scenarios using standard medical imaging datasets (MMIC- CXR [11], NIH Open- I [6], and CheXpert [10]). These experiments evaluate performance across configurations with varying ratios of multimodal and unimodal clients (image- only or text- only), reflecting typical data heterogeneity. Our experimental results outperform naive baselines as well as the most closely related generative method, and achieve performance competitive with methods based on publicly available datasets.

Our key contributions are:

- We propose a feature imputation method trained to handle missing modalities in multimodal federated learning.- We present the first direct comparison and extensive evaluations of imputation strategies operating at the feature level versus the input (raw data) level in the context of federated learning.

# 2 Method

# 2.1 Problem Formulation

We consider a multimodal federated learning setting with  $C$  clients, each having its private dataset  $D_{C}$  with  $n_{C}$  samples. The  $i^{th}$  data sample in  $D_{C}$  is represented by tuple  $(\{X_{m}^{(i)}\}_{m = 1}^{M_{C}}, Y^{(i)})$ , where  $Y^{(i)}$  and  $M_{C}$  represent the label set and the number of modalities in the  $C^{th}$  client respectively. Without loss of generalizability, we assume a scenario with two modalities: image  $(I)$  and  $\text{text} (T)$ . All clients utilize the same global architecture, which consists of modality- specific

encoders  $(f_{e})$ , concatenation- based fusion  $(\oplus)$ , and a classifier head  $(f_{c})$ . Hence, we define the complete model as the set  $\{f_{e}^{I}, f_{e}^{T}, \oplus , f_{c}\}$ .

$$
\operatorname {argmin}_wL(\pmb {w}) = \sum_{e = 1}^{C}\frac{|D_c|}{|D|} l_c(\pmb {w},D_c) \tag{1}
$$

$$
l_{c}(\pmb {w},D_{c}) = \frac{1}{|D_{c}|}\sum_{(X_{I}^{(i)},X_{T}^{(i)},Y^{(i)})\in D_{c}}\mathcal{L}(f_{c}(f_{e}^{I}(X_{I}^{(i)})\oplus f_{e}^{T}(X_{T}^{(i)})),Y^{(i)}) \tag{2}
$$

Here,  $D$  represents the combined dataset across all clients, while  $L$  denotes the local loss function at the client level for the model  $\{f_{e}^{I}, f_{e}^{T}, f_{c}\}$  when applied to a data sample  $(X_{I}^{(i)}, X_{T}^{(i)}, Y^{(i)})$ . A widely used method for handling missing modalities is imputation sampled from a distribution. When a text sample is absent, the local loss function becomes:

$$
\mathcal{L}\big(f_{c}(f_{e}^{I}(X_{I}^{(i)})\oplus \psi),Y^{(i)}\big)\quad \mathrm{where}\quad \psi \sim \left\{ \begin{array}{l l}{0} & {\mathrm{if~zero - filling}}\\ {\mathcal{U}(0,1)} & {\mathrm{if~uniform~sampling}} \end{array} \right. \tag{3}
$$

Naively optimizing Equation 3 results in sub- optimal performance.

To mitigate such issues, we introduce the Feature Imputation Network(FIN), which approximates the missing modality feature based on the available modality. For modality- incomplete samples in unimodal clients, this network reconstructs the bottleneck features of the missing modality conditioned on the available one. Our overall method is illustrated in Figure 2.

# 2.2 Feature Imputation Network (FIN)

The feature imputation network generates the feature vector of the missing modality from the available one. Let  $z_{I}^{(i)} = f_{e}^{I}(X_{I}^{(i)})$  denote the latent feature representation extracted by the image encoder for the  $i^{th}$  sample, and  $z_{T}^{(i)} = f_{e}^{T}(X_{T}^{(i)})$  denote the latent text feature representation. We define imputation networks  $\Phi_{T}$  and  $\Phi_{I}$  such that  $\Phi_{T}$  aims to approximate the text features from image features  $(\Phi_{T}:z_{I}\mapsto \hat{z}_{T}$ , where  $\hat{z}_{T}\approx z_{T}$ ) and  $\Phi_{I}$  aims to approximate image features from text features  $(\Phi_{I}:z_{T}\mapsto \hat{z}_{I}$ , where  $\hat{z}_{I}\approx z_{I}$ ). At the start of each communication round, the server dispatches imputation networks  $(\Phi_{T},\Phi_{I})$  and a complete global model  $\{f_{e}^{I}, f_{e}^{T}, \oplus , f_{c}\}$  to all clients. At multimodal clients, after training the model on local data for  $k$  steps, we proceed to train the imputation networks. Without loss of generalization, let us assume we are training  $\Phi_{T}$  to approximate text features. We first generate a pool of paired image- text feature vectors  $P_{c} = \{(z_{I}^{(i)},z_{T}^{(i)})\mid (X_{I}^{(i)},X_{T}^{(i)},\cdot)\in D_{c}\}$ . As shown in Figure 2 (b), this pool  $P_{c}$  is then used to train  $\Phi_{T}$  by minimizing the Mean Squared Error (MSE) between the predicted text feature  $\hat{z}_{T}^{(i)} = \Phi_{T}(z_{I}^{(i)})$  and the ground truth text feature  $z_{T}^{(i)}$ :

![](images/5fd7cd47deda421efd4dc23c22c59abb7114cb2f5334090878dc891cd858f43c.jpg)  
Fig. 2: Illustration of Feature Imputation Network-based Multimodal Federated Learning. (a) Multimodal Federated Learning system with different types of clients. (b) Training of the Feature Imputation Network in multimodal client. (c) Architecture of Feature Imputation Network (d) Unimodal image client training with the help of the Feature Imputation Network.

$$
\mathcal{L}(\Phi_T) = \frac{1}{|D_c|}\sum_{i = 1}^{|D_c|}\| \Phi_T(z_I^{(i)}) - z_T^{(i)}\| _2^2 \tag{4}
$$

After optimizing  $\Phi_T$  (and symmetrically  $\Phi_I$ ) for  $k$  steps, both the updated main model and the updated imputation networks are uploaded to the server for aggregation. To complete the missing modalities, the unimodal clients receive both the model and the imputation network at the start of each round, and use the imputation network (e.g.,  $\Phi_T$ ) purely for inference to generate features for missing modalities as shown in Figure 2(d). The inferred features are concatenated with the bottleneck feature of the counterpart available modality and fed into the task- specific learnable network. And, the objectives for the multimodal learning in the clients with only image and text become Equations 5 and 6, respectively.

$$
\mathcal{L}\big(f_c\big(f_e^I\big(X_I^{(i)}\big)\oplus \Phi_T\big(f_e^I\big(X_I^{(i)}\big)\big),Y^{(i)}\big) \tag{5}
$$

$$
\mathcal{L}\big(f_c\big(f_e^T\big(X_T^{(i)}\big)\oplus \Phi_I\big(f_e^T\big(X_T^{(i)}\big)\big),Y^{(i)}\big) \tag{6}
$$

After optimizing for  $k$  steps, the model is uploaded to the server, where models from all clients and feature imputation networks from multimodal clients are aggregated. We have implemented a simple 6- layer Transformer decoder [22]

with n=4 heads and 1024 feed- forward dimensions as our feature imputation network. This choice is motivated by the demonstrated success of Transformers in numerous cross- modal tasks. We employ FedAvg [14] as the aggregation strategy in the server.

# 3 Experiments and Results

# 3.1 Datasets and Setups:

Following [15], we utilize three publicly available datasets- MIMIC- CXR [11], NIH Open- I [6], and CheXpert [10] to design two experimental setups: Homogeneous and Heterogeneous. Both setups consist of frontal chest X- rays and common validation and test sets derived from the official MIMIC- CXR splits, where the global model is validated and tested. In the homogeneous setup, 10 clients each contain training data from 810 patients, all sampled from MIMIC- CXR. Conversely, the heterogeneous setup consists of eightimage- only clients, each with data from 900 patients in CheXpert, and two multimodal clients, each containing data from 1,116 patients in NIH Open- I. This setup reflects real- world situations where data characteristics and label distributions vary across clients types. For simplicity, we denote client configurations using the format I:T:X, where I, T, and M represent the number of image- only, text- only clients, and the number of multimodal clients, respectively.

# 3.2 Implementation Details:

We employ pre- trained ResNet- 50 [9] and BERT- base [7] as the image and text encoders, respectively. Their outputs are transformed into 256- dimensional vectors and L2- normalized before fusion. We use a straightforward concatenation approach for multimodal fusion, followed by a linear layer for classification. The models are trained locally using Adam [12] with a learning rate of  $1e^{- 4}$  for three epochs per communication round, totaling 30 rounds. For evaluation, we measure macro AUC (the average area under the Receiver Operating Characteristic curve) on the multimodal test set, following the evaluation protocol outlined in [15]. The reported values represent the mean results from experiments conducted with three random seeds.

# 3.3 Baselines:

We compare our method against two naive but common approaches: Zero- filling and Uniform- filling, along with state- of- the- art methods that depend on generative models and public datasets. Zero- filling imputes the missing data using zero vectors, while Uniform- filling imputes the missing data with feature vectors sampled from uniform distributions.

R2Gen [5] is a generative model that generates radiology reports from X- ray images. We trained R2Gen in a federated manner and subsequently used it to

generate missing reports from images. For a fair comparison, the original ResNet- 101 visual feature extractor was replaced with ResNet- 50 to maintain consistency with our feature imputation approach.

CAR- MFL [15] It is a state- of- the- art method in multimodal federated learning with missing modalities. This method is not directly comparable to ours, as it relies on publicly available real datasets to fill the missing modality gap.

Table 1: AUC+ Performance in Homogeneous and Heterogeneous setups in Unimodal Image Client Settings  

<table><tr><td>Partition</td><td colspan="3">Homogeneous</td><td>Heterogeneous</td></tr><tr><td>I:T:M</td><td>8:0:2</td><td>6:0:4</td><td>4:0:6</td><td>8:0:2</td></tr><tr><td>Zero-fillings</td><td>79.8</td><td>82.81</td><td>86.94</td><td>72.76</td></tr><tr><td>Uniform-fillings</td><td>80.6</td><td>84.83</td><td>87.79</td><td>71.16</td></tr><tr><td>R2Gen</td><td>77.32</td><td>83.1</td><td>86.83</td><td>67.32</td></tr><tr><td>Feature Imputation (Ours)</td><td>86.16</td><td>87.61</td><td>89.31</td><td>77.94</td></tr></table>

Table 2: AUC+ Performance in Homogeneous Across Various Client Settings. An asterisk  $(^{*})$  indicates that the method is not directly comparable to other methods.  

<table><tr><td>Partition</td><td colspan="6">Homogeneous</td></tr><tr><td>I:T:M</td><td>0:8:2</td><td>0:6:4</td><td>0:4:6</td><td>4:4:2</td><td>3:3:4</td><td>2:2:6</td></tr><tr><td>CAR-MFL*</td><td>89.22</td><td>90.12</td><td>90.06</td><td>88.93</td><td>89.62</td><td>89.94</td></tr><tr><td>Zero-fillings</td><td>86.18</td><td>86.8</td><td>88.21</td><td>80.3</td><td>84.57</td><td>87.09</td></tr><tr><td>Uniform-fillings</td><td>88.37</td><td>88.64</td><td>89.27</td><td>85.74</td><td>86.9</td><td>88.49</td></tr><tr><td>Feature Imputation (Ours)</td><td>88.98</td><td>89.3</td><td>89.52</td><td>88.79</td><td>88.12</td><td>89.12</td></tr></table>

# 3.4 Quantitative Results

Tables 1 and 2 present the performance of all baseline methods and our proposed approach across various client configurations in both experimental setups. As shown in Table 1, Feature Imputation method significantly outperforms all other imputation techniques in both homogeneous and heterogeneous settings, demonstrating its applicability to real- world, challenging scenarios. Remarkably, with only two multimodal clients (8:0:2), Feature Imputation achieves performance comparable to that of zero- filling in a six- client multimodal setting (4:0:6). This demonstrates that our method has a tremendous advantage even when

only a few clients have data with complete modalities. Whereas, our competitor method severely fails to generalize in such a scenario as demonstrated by the performance gap of nearly  $10\%$ . Similarly, in Table 2, which details performance across various homogeneous client settings, our Feature Imputation method consistently surpasses the other baseline imputation techniques (Zero- fillings and Uniform- fillings). Notably, our approach achieves results highly competitive with the CAR- MFL method, even though CAR- MFL benefits from access to public datasets to fill modality gaps, a condition our method does not require, highlighting its practical applicability.

Predicting in the representation space rather than the input space simplifies the task and encourages the model to learn abstract, high- level, meaningful features [2]. Generative models make predictions in token space, which are prone to generating unnecessary or irrelevant tokens—especially when the model is trained on limited data. Consequently, both the feature extractor and the classification head are affected by noisy gradient updates, leading to degraded performance. In contrast, feature imputation methods operate directly in the representation space, reducing the likelihood of injecting irrelevant information. Moreover, these methods influence only the classification head during training, leaving the learned features of the missing modality encoders unaffected by gradient updates. This explains why even simple strategies like zero- filling or uniform- filling sometimes outperform generative models.

# 3.5 Qualitative Results

![](images/520904e8f0cfd4cc8f38b5b5259661bb889f8de2952f8035c5421c454cce267c.jpg)  
Fig. 3: t-SNE plot of feature vectors from the model trained in (a) the homogeneous setup and (b) the heterogeneous setup. In the Figure, Upperbound refers to the model trained in a federated manner with complete modalities. Feature vectors are generated using the validation data.

Figure 3 displays a t- SNE visualization comparing different feature representations derived from the validation dataset, specifically within the 8:0:2 federated setting (8 image- only, 2 multimodal clients). This allows for a qualitative assessment of the embeddings generated by our Feature Imputation network (approximating text features from image features) against those resulting from baseline imputation techniques (zero/uniform filling, text encoder output of model trained in generated text report) and an ideal upper- bound model. The upper- bound is the scenario when all the clients have complete modalities. In the homogeneous setup, the representations generated by upper- bound model (represented by red dots) and our Feature Imputation network (represented by blue dots) exhibit notably similar structural characteristics, primarily forming one large cluster accompanied by smaller scattered clusters, with some degree of overlap between them. In contrast, embeddings from the generative model trained on raw generated data (purple) do not closely align with the structure of the upper- bound representations. Additionally, the zero- imputation (green) and uniform vector imputation methods (orange), which produce embeddings from fixed distributions, result in dense clusters localized in single, confined regions.

# 3.6 Computational Complexity and Communication Cost

Table 3: Comparison of model components in terms of approximate parameter counts and FLOPs (Floating Point Operations Per Second). FLOPs were estimated using the fvcore library.  

<table><tr><td>Model</td><td>Parameters</td><td>FLOPS</td></tr><tr><td>Generative Model (R2Gen)</td><td>59.74 M</td><td>94.059 G</td></tr><tr><td>Feature Imputation Network</td><td>6.324 M</td><td>6.318 M</td></tr></table>

Table 3 presents a comparison between the Feature Imputation Network and the Generative Model in terms of computational efficiency. We can see that the Feature Imputation Network reduces communication cost by nearly  $10\times$  per round and computational costs by approximately  $1000\times$  per inference.

# 4 Conclusion

We presented a feature imputation network designed to synthesize feature vectors for missing modalities using available data within a multimodal federated learning framework. Extensive experiments demonstrated that our approach significantly outperforms common baselines, including input- level generative models. Future research could explore extensions to more complex multimodal scenarios and investigate alternative architectures for the feature imputation network.

# References

1. Acosta, J.N., Falcone, G.J., Rajpurkar, P., Topol, E.J.: Multimodal biomedical ai. Nature Medicine 28(9), 1773-1784 (2022)2. Assran, M., Duval, Q., Misra, I., Bojanowski, P., Vincent, P., Rabbat, M., LeCun, Y., Ballas, N.: Self-supervised learning from images with a joint-embedding predictive architecture. In: Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition. pp. 15619-15629 (2023)3. Chen, J., Zhang, A.: Fedmsplit: Correlation-adaptive federated multi-task learning across multimodal split networks. In: Proceedings of the 28th ACM SIGKDD conference on knowledge discovery and data mining. pp. 87-96 (2022)4. Chen, J., Pan, R.: Medical report generation based on multimodal federated learning. Computerized Medical Imaging and Graphics p. 102342 (2024)5. Chen, Z., Song, Y., Chang, T.H., Wan, X.: Generating radiology reports via memory-driven transformer. In: Proceedings of the 2020 Conference on Empirical Methods in Natural Language Processing (Nov 2020)6. Demner-Fushman, D., Kohli, M.D., Rosenman, M.B., Shooshan, S.E., Rodriguez, L., Antani, S., Thoma, G.R., McDonald, C.J.: Preparing a collection of radiology examinations for distribution and retrieval. Journal of the American Medical Informatics Association 23(2), 304-310 (2016)7. Derdin, J., Chang, M.W., Lee, K., Toutanova, K.: Bert: Pre-training of deep bidirectional transformers for language understanding. arXiv preprint arXiv:1810.04005 (2018)8. Feng, T., Bose, D., Zhang, T., Hebbar, R., Ramakrishna, A., Gupta, R., Zhang, M., Avestimehr, S., Narayanan, S.: Fedmultimodal: A benchmark for multimodal federated learning. In: Proceedings of the 29th ACM SIGKDD Conference on Knowledge Discovery and Data Mining. pp. 4035-4045 (2023)9. He, K., Zhang, X., Ren, S., Sun, J.: Deep residual learning for image recognition. In: Proceedings of the IEEE conference on computer vision and pattern recognition. pp. 770-778 (2016)10. Irvin, J., Rajpurkar, P., Ko, M., Yu, Y., Ciurea-Ilcus, S., Chute, C., Marklund, H., Haghgoo, B., Ball, R., Shpanskaya, K., et al.: Chexpert: A large chest radiograph dataset with uncertainty labels and expert comparison. In: Proceedings of the AAAI conference on artificial intelligence. vol. 33, pp. 590-597 (2019)11. Johnson, A.E., Pollard, T.J., Berkowitz, S.J., Greenbaum, N.R., Lungren, M.P., Deng, C.y., Mark, R.G., Horng, S.: Mimic-cxr, a de-identified publicly available database of chest radiographs with free-text reports. Scientific data 6(1), 317 (2019)12. Kingma, D.P., Ba, J.: Adam: A method for stochastic optimization. arXiv preprint arXiv:1412.6980 (2014)13. Le, H.Q., Thwal, C.M., Qiao, Y., Tun, Y.L., Nguyen, M.N., Hong, C.S.: Cross-modal prototype based multimodal federated learning under severely missing modality. arXiv preprint arXiv:2401.13898 (2024)14. McMahan, B., Moore, E., Ramage, D., Hampson, S., y Areas, B.A.: Communication-efficient learning of deep networks from decentralized data. In: Artificial intelligence and statistics. pp. 1273-1282. PMLR (2017)15. Poudel, P., Shrestha, P., Amgain, S., Shrestha, Y.R., Gyawali, P., Bhattarai, B.: Car-mfl: Cross-modal augmentation by retrieval for multimodal federated learning with missing modalities. In: International Conference on Medical Image Computing and Computer-Assisted Intervention. pp. 102-112. Springer (2024)

16. Qayyum, A., Ahmad, K., Ahsan, M.A., Al-Fuqaha, A., Qadir, J.: Collaborative federated learning for healthcare: Multi-modal covid-19 diagnosis at the edge. IEEE Open Journal of the Computer Society 3, 172-184 (2022)17. Sachin, D., Annappa, B., Ambasange, S., Tony, A.E.: A multimodal contrastive federated learning for digital healthcare. SN Computer Science 4(5), 674 (2023)18. Saha, P., Mishra, D., Wagner, F., Kamnitsas, K., Noble, J.A.: Examining modality incongruity in multimodal federated learning for medical vision and language-based disease detection. arXiv preprint arXiv:2402.05294 (2024)19. Shrestha, P., Amgain, S., Khanal, B., Linte, C.A., Bhattarai, B.: Medical vision language pretraining: A survey. arXiv preprint arXiv:2312.06224 (2023)20. Sun, G., Mendieta, M., Dutta, A., Li, X., Chen, C.: Towards multi-modal transformers in federated learning. In: European Conference on Computer Vision. pp. 229-246. Springer (2024)21. Thrasher, J., Devkota, A., Siwakotai, P., Chivukula, R., Poudel, P., Hu, C., Bhattarai, B., Gyawali, P.: Multimodal federated learning in healthcare: a review. arXiv preprint arXiv:2310.09650 (2023)22. Vaswani, A., Shazeer, N., Parmar, N., Uszkoreit, J., Jones, L., Gomez, A.N., Kaiser, L., Polosukhin, I.: Attention is all you need. Advances in neural information processing systems 30 (2017)23. Venugopalan, J., Tong, L., Hassanzadeh, H.R., Wang, M.D.: Multimodal deep learning models for early detection of alzheimer's disease stage. Scientific reports 11(1), 3254 (2021)24. Wu, R., Wang, H., Chen, H.T., Carneiro, G.: Deep multimodal learning with missing modality: A survey. arXiv preprint arXiv:2409.07825 (2024)25. Yu, Q., Liu, Y., Wang, Y., Xu, K., Liu, J.: Multimodal federated learning via contrastive representation ensemble. arXiv preprint arXiv:2302.08888 (2023)26. Zheng, T., Li, A., Chen, Z., Wang, H., Luo, J.: Autofed: Heterogeneity-aware federated multimodal learning for robust autonomous driving. arXiv preprint arXiv:2302.08646 (2023)