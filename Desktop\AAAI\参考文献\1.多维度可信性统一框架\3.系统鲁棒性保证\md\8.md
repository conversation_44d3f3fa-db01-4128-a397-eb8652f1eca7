# Hierarchical Knowledge Structuring for Effective Federated Learning in Heterogeneous Environments

<PERSON><PERSON>*, <PERSON><PERSON><PERSON>, <PERSON>*  *<PERSON> University of London, London, United Kingdom  {w.tam, q.li, ahmed.nayed}@qmul.ac.uk

Abstract—Federated learning enables collaborative model training across distributed entities while maintaining individual data privacy. A key challenge in federated learning is balancing the personalization of models for local clients with generalization for the global model. Recent efforts leverage logit- based knowledge aggregation and distillation to overcome these issues. However, due to the non- IID nature of data across diverse clients and the imbalance in the client's data distribution, directly aggregating the logits often produces biased knowledge that fails to apply to individual clients and obstructs the convergence of local training. To solve this issue, we propose a Hierarchical Knowledge Structuring (HKS) framework that formulates sample logits into a multi- granularity codebook to represent logits from personalized per- sample insights to globalized per- class knowledge. The unsupervised bottom- up clustering method is leveraged to enable the global server to provide multi- granularity responses to local clients. These responses allow local training to integrate supervised learning objectives with global generalization constraints, which results in more robust representations and improved knowledge sharing in subsequent training rounds. The proposed framework's effectiveness is validated across various benchmarks and model architectures.Index Terms—Federated Learning, Knowledge Structuring, Clustering, Generalization, Personalization

Index Terms- Federated Learning, Knowledge Structuring, Clustering, Generalization, Personalization

# I. INTRODUCTION

Federated learning (FL) [1] is a distributed machine learning approach under privacy protection. In FL, clients join forces to learn a global model without sending their data. In realworld applications, data distributions across clients are often non- independent and identically distributed (Non- IID). For instance, lighting conditions and weather can cause variations in the data collected by distributed video surveillance cameras [2]- [5].Conventional FL methods like FedAvg [1] often falter, resulting in lower global models' performance under the nonIID data distributions [6],[7], where data is not dispersed similarly among clients.

The primary goals of alleviating this issue are improving model generalization to serve more clients or enhancing model personalization to fit local data distributions better. However, as local data distributions often differ from the global distribution in non- IID FL, these two optimal goals are usually at odds.

Researchers proposed Personalized Federated Learning (PFL) as an alternative to traditional Federated Learning (FL) to tackle the problems of statistical heterogeneity in local datasets (i.e., non- IID data), enabling clients to train models better suited to their data [8]. Unlike conventional FL, which focuses on training a shared global model by minimizing the average loss across clients, PFL aims to create personalized models tailored to the unique data distribution of each client. PFL seeks a balance during training, i.e., personalized models must fit their local data distributions while benefiting from shared context knowledge obtained through collaborative training. However, a key challenge with PFL is enabling personalized models to learn unique patterns of the client's data and the shared knowledge between all clients to achieve a good combination of personalization and generalization.

While recent PFL methods outperform traditional FL approaches, they often depend on a single global model to guide local training, which limits their ability to achieve effective personalization. The current PFL often derive personalized models from a global model trained by FedAvg [9], [10]. However, if client data distributions are vastly different (i.e., non- IID), the global model may fail to represent all distributions effectively. Relying on a single global model can degrade the overall performance of personalized models. Therefore, balancing personalization and generalization is crucial, as local and out- of- distribution classes often appear during inference.

To address this, we introduce Hierarchical Knowledge Structuring (HKS), a framework that balances personalization and generalization in FL. In HKS, clients train models locally and share knowledge with the server, which organizes logits into a bottom- up hierarchical structure. Clients then utilize the aggregated logits at a granularity level suited to their tasks.

Our main contributions can be summarized as follows:

Hierarchical Knowledge Distillation: We propose a method that enhances both global performance and personalization by reinforcing knowledge exchange through hierarchical clustering, addressing the trade- off between generalization and personalization in FL. Stronger Data Privacy- Preservation: Unlike existing methods, HKS does not require clients to share data labels, effectively reducing privacy risks while enabling personalized learning via sharing logits with the server. Effective Performance in Heterogeneous Settings: Experimental results demonstrate that HKS outperforms state- of- the- art methods in personalized tasks, while maintaining competitive global performance in diverse non- IID scenarios.

# II. RELATED WORK

# A. Federated Learning

Numerous FL strategies, such as FedAvg [1], FedProx [11], FedMA [12], FedDyn [13], and SCAFFOLD [14], aim to develop generalized global models. Recently, personalized FL has gained attention, exploring methods based on multitask learning [?], interpolation and fine- tuning [15], metalearning [?], regularization [16], and knowledge distillation [17]. However, these methods often struggle to balance generalization and personalization, which is critical in real- world scenarios where both in- distribution and out- of- distribution classes can appear during inference.

# B. Personalized Federated Learning (PFL)

PFL arises to address the challenges posed by statistical heterogeneity and non- IID data distributions in traditional FL. Conventional FL methods, such as FedAvg, often experience decreases in accuracy when training on non- IID data, mainly due to client drift. Tan et al. [18] categorize PFL approaches into two main strategies: 1) personalization through a global model, which involves training a single global model, and 2) developing personalized models, where individual models are trained specifically for each client.

Most personalization techniques for global FL models [10] have a two- step process. The first step is training a global model together across clients, and the second step is refining the global model using each client's private data to tailor it to their local data distribution [9]. Typically, PFL methods employ FedAvg as the default training algorithm during global model training in FL settings, followed by additional local adaptation steps to personalize the model for individual clients. Alternatively, the personalized model learning strategy modifies the FL aggregation process and employs diverse learning algorithms to achieve PFL. These strategies can be further classified into architecture- based and similarity- based methods. The objective of this approach is to collaboratively train personalized models for a group of clients, leveraging their non- IID data distributions while simultaneously preserving their privacy [19].

# C. Knowledge Distillation in Federated Learning

Knowledge distillation (KD) [20] involves training a simpler student model to follow the predictions of a more complex teacher model. This approach ensures that the student model consistently aligns its outputs with the teacher's, effectively transferring knowledge. The difference between their predictions, often measured using KL divergence (soft loss), is the primary objective for training the student. In federated settings, distillation- based approaches [17], [21]- [23] use model distillation techniques, or knowledge transfer methods, to share insights by exchanging output logit vectors instead of model parameters. For instance, FedDistill [23] offers an alternative to centralizing local models. Aggregating soft scores rather than local models enables the creation of personalized models with diverse architectures. Similarly, FedCache [17] leverages hashes and labels to identify the R nearest neighbors within the same class using Hierarchical Navigable Small World (HNSW). It aggregates the collected soft labels based on the hash similarity of the samples. It sends the resulting aggregated logits back to the local nodes, allowing them to update their models with the latest global aggregation update. However, the assistance of labels on the server could potentially lead to privacy leakage.

# D. Federated Learning for Generalized Optimization

Global optimization methods in FL aim to develop a model generalising data from all participating clients. These methods can be categorized into these approaches:

# 1) Approaches to Mitigate Client Drift

To address client drift during the local training phase, various methods leverage global knowledge to guide local updates. Weight- based methods [11], [24] use proximal terms to minimize discrepancies between local and global models or apply drift correction factors to address parameter variations. Feature- based methods [25] aim to align the outputs of clients in latent spaces or confine client learning to similar representations by penalizing inconsistencies through feature contrast. Despite their potential, these methods often fail to address feature heterogeneity, limiting performance improvements. Predictionbased approaches [26], [27] rely on public datasets and aggregate soft- label predictions instead of model parameters or gradients. This reduces communication overhead and facilitates knowledge distillation.

# 2) Utilizing Auxiliary Data for Training

Given the heterogeneity of client data, local models often struggle to generalize to missing classes. To address this, researchers have explored: 1) Sharing public datasets [21], 2) Generating synthetic data [23], [28], or 3) Using truncated versions of private datasets [29].

While these approaches improve performance, they raise privacy concerns as raw or synthetic data can expose sensitive information.

# E. Federated Learning for Personalized Optimization

E. Federated Learning for Personalized OptimizationPFL aims to develop tailored models for individual clients based on their requirements and data characteristics. These methods [18], [30] enhance the global model's generalization capability before fine-tuning it with local client data. Data-based techniques use data augmentation to mitigate statistical heterogeneity [28], [31], [32] or adaptively sample subsets to accelerate convergence [33], [34]. However, sharing augmented data introduces privacy risks, and Model-based techniques regulate the personalization of local models by leveraging global knowledge [35], [36]. For example, PLGU [37] integrates universal knowledge to optimize personalization while preventing clients from experiencing suboptimal performance.

# III. METHODOLOGY

# A. Problem Formulation

We consider an FL system with  $N$  clients, denoted as  $\mathcal{C} =$ $\{C_i\}_{i = 1}^N$  , each having a local dataset  $D_{i} = \{X_{i},Y_{i}\}$  . The goal of the FL system is to collaboratively train a global model

![](images/da4d4f338c010b0dca47437ddd7dc225a9ca294f4a38a6a47486b6b7b3bbd6c2.jpg)  
Fig. 1. Overview of federated learning frameworks with edge devices, highlighting the key distinguishing features of FedAvg, FedDistill, and FedCache (marked in red). FedAvg necessitates homogeneous model architectures and faces challenges such as high communication overhead and privacy concerns due to the sharing of model parameters. FedDistill relies on coarse-grained logit exchange, which is insufficient for achieving effective personalized federated learning (PFL). FedCache involves sharing client-labels, raising privacy risks, and lacks a hierarchical structure to control the granularity of knowledge exchange.

$\theta$  by aggregating the local updates  $\theta_{i}$  from the clients. Each client  $C_k$  also maintains a local model  $M_k$ . The central server  $S$  coordinates the training process by collecting local updates and combining them to refine the global model.

In traditional FL [1], clients collectively aim to find a model parameter vector  $\mathbf{w} \in \mathbb{R}^n$  that maps input  $\mathbf{x}$  to output label  $\mathbf{y}$  such that  $\mathbf{w}$  minimizes the global loss. The global objective is expressed as:

$$
F(\mathbf{w}) = \sum_{k = 1}^{N}p_kF_k(\mathbf{w}), \tag{1}
$$

where  $p_k$  is the proportion of data held by client  $k$  and  $F_k(\mathbf{w})$  is the local objective function for client  $k$  defined as:

$$
F_{k}(\mathbf{w}) = \frac{1}{|\mathcal{B}_{k}|}\sum_{\xi \in \mathcal{B}_{k}}f(\mathbf{w},\xi), \tag{2}
$$

where  $B_{k}$  is the local dataset for client  $k$  containing  $|\mathcal{B}_k|$  data samples drawn from its local distribution  $D_{k}$ . The function  $f(\mathbf{w}, \xi)$  is the composite loss for an individual data point  $\xi$

Due to high data heterogeneity across clients, the globally optimized parameter vector  $\mathbf{w}^*$  that minimizes the global objective  $F(\mathbf{w})$  may not generalize well for clients whose local objectives  $F_k(\mathbf{w})$  deviate considerably from the global model. Such clients may opt to train their personalized models  $\mathbf{w}_k \in \mathbb{R}^{n_k}$  by focusing solely on their local objectives. While this approach is effective for clients with abundant training data (where the local distribution  $\mathcal{D}_k$  is well- represented by the empirical distribution  $\hat{\mathcal{D}}_k$ ), it struggles for clients with limited data. For such clients, the mismatch between  $\mathcal{D}_k$  and  $\hat{\mathcal{D}}_k$  results in poor generalization and diminished benefits from FL participation. Additionally, standard FL assumes all clients use identical model architectures, which often fails to account for real- world device variability. This heterogeneity leads to a mismatch between model sizes and architectures across clients.

PFL addresses these challenges by adapting the global model  $M_G$  to better align with the specific data distributions  $\mathcal{D}_k$  of individual clients while leveraging the collective knowledge of all participants.

# B. Knowledge Distillation

Given a sample  $\xi$  consider a student network  $M_s$  and a teacher network  $M_t$ . Knowledge distillation involves transferring the teacher's knowledge to the student by aligning their softened probability distributions. The softened probabilities are calculated as:

$$
q = \mathrm{softmax}\left(\frac{z}{T}\right), \tag{3}
$$

where  $z$  is the logit output of  $M(\xi)$ , and  $T$  is the temperature factor controlling the smoothness of  $q$ . The alignment loss helps the student network learn the relative confidence of the teacher network across categories, enhancing generalization [38]. A common alignment loss is the Kullback- Leibler (KL) divergence:

$$
L_{\mathrm{Align}}(w_s,w_t;x) = L_{\mathrm{KL}}(q_s,q_t) = -\sum_{i = 1}^{C}q_t(i)\log \left[\frac{q_s(i)}{q_t(i)}\right]. \tag{4}
$$

where  $C$  is the number of categories.

# C. Hierarchical Knowledge Structuring (HKS)

To address these challenges, we propose Hierarchical Knowledge Structuring (HKS), which introduces a bottom- up hierarchical clustering mechanism on the server. The framework aims to:

1) allow different levels of personalization to mitigate the effects of data heterogeneity; and 
2) accommodate heterogeneous devices with varying model architectures by sharing logits instead of model parameters.

HKS leverages knowledge distillation to guide local learning, addressing data imbalance and overfitting issues.

![](images/2dd77d00a9e6c002e41617004183b4b3877982d662b2deaf0c63ad0d0377b450.jpg)  
Fig. 2. Overview of the HKS framework.

# D. HKS Training Framework

As illustrated in Figure 2, the HKS training process consists of the following key steps:

1) Local Training: Each client performs local training for 10 warm-up epochs. 
2) Logits Upload: After local training, each client uploads its logits to the server. 
3) Clustering: The server performs agglomerative hierarchical clustering on the received logits and aggregates them within each cluster. 
4) Clustered Knowledge Sharing: The aggregated logits are shared with the corresponding clients. 
5) Knowledge Distillation: Each client performs local training while distilling knowledge from the new logits.

# E. Personalized Federated Learning with HKS

Logit Clustering: The global server aggregates logits from all clients and applies a bottom- up clustering approach to structure them hierarchically. Each level of the hierarchy represents logits at varying levels of granularity:

- Per-sample logits: Fine-grained knowledge tailored to individual samples.- Per-class logits: Intermediate knowledge generalized across specific classes.- Global logits: Coarse-grained knowledge capturing global patterns across all clients.

Knowledge Distillation: During local training, each client incorporates logits from the hierarchical structure:

- Fine-grained logits for personalized insights.- Coarse-grained logits to encourage global generalization.

The HKS framework balances generalization and personalization by enabling knowledge transfer at multiple levels of granularity. Each client's training objective is formulated as:

$$
\mathcal{L}_{\mathrm{total}} = \mathcal{L}_{\mathrm{local}} + \alpha \cdot \mathcal{L}_{\mathrm{distill}}, \tag{5}
$$

where  $\mathcal{L}_{\mathrm{local}}$  is the local training loss,  $\mathcal{L}_{\mathrm{distill}}$  is the knowledge distillation loss, and  $\alpha$  is a weighting factor.

By integrating hierarchical clustering and knowledge distillation, HKS ensures that even clients with limited data can benefit from participating in FL, improving both generalization and personalization.

# IV. EXPERIMENTS AND EVALUATIONS

Our evaluation aims to address the following questions:

- Can the proposed HKS framework achieve superior personalized or generalized performance compared to baseline methods like FedDistill and FedCache?- How does data heterogeneity, controlled by partitioning coefficients  $(\alpha)$ , impact personalized and generalized model performance?

# A. Experimental Setup

We evaluate our proposed HKS framework using the FashionMNIST dataset, a widely- used benchmark for image classification with 10 classes. The dataset comprises 60,000 training images and 10,000 test images of clothing items distributed across 20 clients in an FL environment. To simulate non- IID data distributions, we partition the dataset using a Dirichlet distribution with partitioning coefficients  $(\alpha)$  of 1.0 and 0.5, representing moderate and high levels of data heterogeneity, respectively.

The FL setup runs 18 communication rounds, with each client performing 1 local training epoch per round using SGD as the optimizer (learning rate  $= 0.01$ , batch size  $= 8$ ). The distillation loss weight  $(\alpha)$  in the knowledge distillation loss formulation is set to 1.5 by default, balancing local and distillation- based learning.

1) Model Heterogeneity: To simulate realistic deployment scenarios with diverse device capabilities, we implement three different ResNet architectures of varying capacities:

- ResNet-8: A lightweight model with [1,1,1] basic blocks, suitable for resource-constrained devices.

# Algorithm 1 HKS: Hierarchical Knowledge Structuring

1: Input: Client datasets  $\{D_k\}_{k = 1}^N$  , number of classes  $C$  temperature  $T$  , balancing factor  $\alpha$  , pre- trained encoder  $E$  warmup epochs  $W$  2: Output: Personalized models  $\{M_k\}_{k = 1}^N$  3: Initialize KnowledgeCache  $\kappa$  with  $C$  classes 4: Initialize client models  $\{M_k\}_{k = 1}^N$  5: for each client  $k$  in parallel do 6: Get feature hashes  $\{h_k\}$  for samples using encoder  $E$  7: Add hashes to  $\kappa$  8:end for 9: Build hash relations in  $\kappa$  using HNSW index 10: for each epoch  $t$  do 11: for each client  $k$  in parallel do 12: for each batch  $(x_{i},y_{i})$  in  $D_{k}$  do 13: Forward pass  $\ell_i = M_k(x_i)$  14: Compute cross- entropy loss  $L_{CE}(\ell_i,y_i)$  15: if  $t\geq W$  then 16: Update  $\kappa$  with current logits  $\ell_i$  17: Fetch teacher knowledge based on granularity: 18: "top": highest level cluster 19: "middle": mid- level cluster 20: "bottom": lowest level cluster 21: "all": knowledge from all levels 22: Compute KD loss  $L_{KD}$  with temperature  $T$  23: Total loss:  $L = L_{CE} + \alpha L_{KD}$  24: else 25: Total loss:  $L = L_{CE}$  26: end if 27: Update model parameters using SGD 28: end for 29: end for 30: if  $t\geq W$  then 31: Perform hierarchical clustering on  $\kappa$  to  $C$  clusters 32: Update cluster assignments and paths 33: end if 34:end for

ResNet- 16: A medium- capacity model with [2,2,2] basic blocks, for moderate- capability devices. ResNet- 20: A full- capacity model with [3,3,3] basic blocks, for high- capability devices.

Client models are assigned following a systematic pattern where  $i$  is the index of the client:

Every client with  $i$  modulus  $3 = 0$  receives a ResNet- 8. Every client with  $i$  modulus  $3 = 1$  receives a ResNet- 16. Every client with  $i$  modulus  $3 = 2$  receives a ResNet- 20.

This distribution ensures a balanced mix of device capabilities across the federated network while reflecting real- world heterogeneity in computational resources.

2) Data Distribution: To simulate non-IID data distributions, we partition the dataset using a Dirichlet distribution with partitioning coefficients  $(\alpha)$  of 1.0 and 0.5, representing moderate and high levels of data heterogeneity, respectively. This setup

![](images/abc1f00c1d54be8c0de700ee645b8391c2f17a506a6ac8bd8decbb514e48d5df.jpg)  
Fig. 3. Cluster path of a target sample in the bottom-up hierarchical clustering. The top-most node represents the initial cluster containing the target sample. Each subsequent row represents a merged cluster showing member samples.

allows us to evaluate our method's effectiveness under varying degrees of data distribution skew across heterogeneous devices.

3) Hyperparameter: For FedCache, results for various related sample settings  $(R\in \{1,4,16,64,256\})$  are included. The hyperparameter  $R$  controls the number of nearest neighbors of the same class from which the logits are aggregated for knowledge distillation. A larger  $R$  allows more information from a wider range of neighboring samples to be distilled.

For HKS, results are reported for different granularity levels (top, middle, bottom, all) illustrated in Figure 3. The granularity hyperparameter defines how logits are aggregated from the hierarchical structure:

Top: The logits are aggregated at the highest level of the hierarchy, where the number of clusters equals the number of classes. This prioritizes sharing generalized knowledge across all clients. Middle: Aggregation occurs at the middle layer of the hierarchy, balancing between general and specific knowledge. Bottom: Aggregation is performed at the first level of the hierarchy, emphasizing personalized knowledge by leveraging finer- grained, client- specific clusters. All: The KD loss is averaged across all clusters in the cluster path of a sample, combining information from all hierarchical levels.

# B. Baselines and Metrics

We compare HKS against three baseline methods:

FedAvg [1]: The standard FL algorithm that averages model parameters from clients to update the global model. FedDistill [23]: A distillation- based framework that transfers client knowledge via sample- grained logits with a public dataset or class- grained logits. FedCache [17]: An enhanced distillation method that leverages cached logits to reduce communication cost.

The evaluation metrics include:

Personalized Model Accuracy (MAUA) [39]: Maximum average user model accuracy across communication rounds

TABLE I RESULTS FOR PERSONALIZED AND GENERALIZED METRICS WITH  $\alpha = 1.0$  

<table><tr><td>Method</td><td>Hyperparameters</td><td>Personalized (MAUA)</td><td>Generalized (Global Test Accuracy)</td></tr><tr><td rowspan="3">FedAvg</td><td>ResNet-8</td><td>49.53</td><td>44.06</td></tr><tr><td>ResNet-16</td><td>42.24</td><td>36.79</td></tr><tr><td>ResNet-20</td><td>44.71</td><td>31.35</td></tr><tr><td>FedDistill</td><td>-</td><td>86.67</td><td>67.22</td></tr><tr><td rowspan="4">FedCache</td><td>R=1</td><td>87.49</td><td>68.17</td></tr><tr><td>R=14</td><td>87.44</td><td>68.88</td></tr><tr><td>R=66</td><td>87.55</td><td>68.26</td></tr><tr><td>R=256</td><td>87.52</td><td>68.87</td></tr><tr><td rowspan="4">HKS (ours)</td><td>Granularity: top</td><td>86.96</td><td>68.59</td></tr><tr><td>Granularity: middle</td><td>89.16</td><td>68.30</td></tr><tr><td>Granularity: bottom</td><td>88.44</td><td>68.31</td></tr><tr><td>Granularity: all</td><td>89.33</td><td>69.00</td></tr></table>

For  $\alpha = 1.0$  , the highest values are shown in bold and the second-highest values are underlined.

TABLE II RESULTS FOR PERSONALIZED AND GENERALIZED METRICS WITH  $\alpha = 0.5$  

<table><tr><td>Method</td><td>Hyperparameters</td><td>Personalized (MAUA)</td><td>Generalized (Global Test Accuracy)</td></tr><tr><td rowspan="3">FedAvg</td><td>ResNet-8</td><td>20.91</td><td>13.88</td></tr><tr><td>ResNet-16</td><td>22.44</td><td>20.89</td></tr><tr><td>ResNet-20</td><td>24.81</td><td>23.19</td></tr><tr><td>FedDistill</td><td>-</td><td>89.76</td><td>58.72</td></tr><tr><td rowspan="5">FedCache</td><td>R=1</td><td>89.20</td><td>59.49</td></tr><tr><td>R=4</td><td>89.45</td><td>59.03</td></tr><tr><td>R=16</td><td>89.57</td><td>59.15</td></tr><tr><td>R=64</td><td>89.68</td><td>58.73</td></tr><tr><td>R=256</td><td>89.38</td><td>58.74</td></tr><tr><td rowspan="4">HKS (ours)</td><td>Granularity: top</td><td>89.33</td><td>59.75</td></tr><tr><td>Granularity: middle</td><td>90.79</td><td>56.09</td></tr><tr><td>Granularity: bottom</td><td>90.61</td><td>58.26</td></tr><tr><td>Granularity: all</td><td>90.56</td><td>56.89</td></tr></table>

For  $\alpha = 0.5$  , the highest values are shown in bold and the second-highest values are underlined.

on clients' local datasets, measuring the effectiveness of personalization across different device capabilities.

Generalized Model Accuracy: Average global test accuracy across all clients on an evenly distributed global dataset, indicating methods' ability to maintain uniform performance across heterogeneous devices.

# C. Experimental Results and Analysis

Table I presents the results of the experiments conducted on the FashionMNIST dataset [40], highlighting the performance of the proposed HKS framework compared to baseline methods (FedAvg [1], FedDistill [23] and FedCache [17]). The results present both personalized accuracy (MAUA) [39] and generalized accuracy (i.e., global test accuracy) for the compared methods. Results are reported under two settings of data heterogeneity controlled by the partition coefficient  $\alpha$  (1.0 and 0.5), after 18 communication rounds. Table II presents the results for the same metrics under the more challenging data heterogeneity setting with  $\alpha = 0.5$ . The performance results from the compared methods under varying levels of data heterogeneity are as follows:

1) FedAvg: struggles notably in environments with high data diversity, such as when  $\alpha = 0.5$ . Both personalized and global accuracy drop considerably because model parameters are averaged across clients with imbalanced data to align the divergent updates. This naive averaging causes the global models to struggle with adapting to the distinct characteristics of each client's local dataset. Its low MAUA and global test accuracy further emphasize its inability to adapt effectively to local data variations. In  $\alpha = 1.0$  setting, deeper models like ResNet-20 overfit to local data distribution as they capture intricate patterns, leading to poorer generalization (31.35% global accuracy) because deeper models' overfitted parameters misalign during aggregation, which exacerbates model divergence. In contrast, shallow models (ResNet-8/16) alleviate overfitting by learning simpler features, performing better on global tests (44.06% / 36.70%). All models struggle due to extreme non-IIDness at  $\alpha = 0.5$  because skewed data amplifies the conflicting parameter updates, which results in a global model that poorly aligns with any client's data distribution.

As local models are initialized from a global model that is updated via parameter averaging, if the global model is poor due to aggregation issues (i.e., parameter divergence), local training starts from a suboptimal base, limiting its ability to learn effectively on local data (i.e., low MAUA).

2) FedDistill: performs better in more balanced settings, such as when  $\alpha = 1.0$  where it achieves a high MAUA of  $86.67\%$  and a global test accuracy of  $67.22\%$ . Transferring knowledge through logits helps mitigate the effects of data heterogeneity. However, its global accuracy declines to  $58.72\%$  in the  $\alpha = 0.5$  setting, although it achieves a strong MAUA of  $89.76\%$ . This performance decay indicates that while FedDistill can effectively leverage local knowledge, it may struggle to generalize in high data heterogeneity.

3) FedCache: benefits from aggregating logits from multiple neighbors within the same class where more logit aggregation with higher hyperparameter value  $R$  generally improve global test accuracy. For example, with  $R = 64$ , FedCache achieves the highest global test accuracy of  $69.57\%$  when  $\alpha = 1.0$ . The MAUA values for FedCache remain good across different  $R$  values, indicating that it effectively balances personalized and generalized accuracy. However, larger  $R$  (e.g., 256) slightly reduces performance  $68.87\%$  as this introduces noisy or less relevant neighbors from the HNSW graph, diluting the distilled knowledge's quality. Moreover, in the more challenging setting of  $\alpha = 0.5$ , the global test accuracy is lower due to intra-class noise, introduced by blending dominant local features with sparse/biased external features, diluting knowledge quality (e.g.,  $90\%$  "sandals" in one client while  $5\%$  in another), as the combined logits blend conflicting information.

4) HKS: consistently achieves higher personalized MAUA across both partition settings  $\alpha = 1.0$  and  $\alpha = 0.5$  reflecting its strength in tailoring models to client-specific data distributions. For global test accuracy, HKS outperforms baseline methods in the more challenging heterogeneity setting  $\alpha = 0.5$  and achieves comparable results to FedCache in an easier scenario (i.e.,  $\alpha = 1.0$ ). In particular, the "all" granularity achieves the highest accuracy compared to other granularity at  $\alpha = 1.0$  combining broad and fine-grained features to maximize generalization and personalization. However, clients develop unique feature specialisations at  $\alpha = 0.5$ . Aggregating all levels mixes conflicting cluster knowledge, slightly lowering performance. Hence, targeted granularity (e.g., "middle") outperforms "all" in non-IID settings by avoiding conflicting clusters. Since HKS clustering is class-agnostic, it could group similar features between classes, which is more robust to class imbalance.

When the data is highly heterogeneous (non- IID, e.g.,  $\alpha = 0.5$ ), each client primarily trains on a narrow subset of the overall data distribution (e.g., specific classes or features). This setting allows local models to "over- specialize" to their private data. The model performs exceptionally well on its local testing data because the training and testing datasets are from the same subset, leading to higher MAUA. However, the lack of exposure to a diverse data distribution causes poorer performance on the global test dataset, where classes are more evenly distributed.

In contrast, less heterogeneous settings  $\alpha = 1.0$  enable more effective global knowledge sharing, as clients' data distributions align more closely with the global distribution, i.e., class samples are more evenly spread across clients instead of being skewed on a few clients. The broader class overlap enables more meaningful knowledge transfer between clients. KD acts as a regularizer, enhancing shared understanding of the global data distribution and improving generalization.

Analysis of different granularity levels in HKS reveals trade- offs between personalization and generalization. At the top level, knowledge sharing occurs across all clients, promoting better generalization as evidenced by higher global test accuracy  $(68.59\%$  for  $\alpha = 1.0$  and  $59.75\%$  for  $\alpha = 0.5$  but potentially sacrificing some personalization benefits. The middle granularity level achieves the strongest personalization performance (MAUA of  $89.16\%$  for  $\alpha = 1.0$  and  $90.79\%$  for  $\alpha = 0.5$  by balancing local specialization with moderate knowledge sharing among similar clients. The bottom granularity restricts knowledge sharing to very similar clients, maintaining strong personalization  $(88.44\%$  and  $90.61\%$  respectively) while still outperforming baseline methods.

The choice of optimal granularity level depends on the data heterogeneity. In less heterogeneous settings, using all granularity levels (all) achieves the best overall performance  $(89.38\%$  MAUA and  $69.00\%$  global accuracy), suggesting that multiple levels of knowledge sharing are beneficial when client data distributions are similar. However, in highly heterogeneous settings, different granularity levels show more pronounced trade- offs: the top level excels in generalization  $(59.75\%$  global accuracy) while the middle level achieves better personalization  $(90.79\%$  MAUA).

These findings suggest that practitioners should select granularity levels based on their requirements and data characteristics. When generalization is essential or when working with highly heterogeneous data, top- level granularity is recommended because it promotes broad knowledge sharing across all clients, mitigating over- specialization and improving global test accuracy. For applications prioritizing personalization, middle granularity offers the best performance as it enables targeted knowledge sharing among similar clients, preserving local specialization while benefiting from shared insights. When data distributions are relatively uniform, enabling all granularity levels can provide the best of both worlds. This multi- level knowledge sharing takes full advantage of the uniform overlap across clients, maximizing personalization and generalization. These findings show that HKS can balance personalization and generalization trade- offs by leveraging knowledge at different granularity. Its hierarchical knowledge structuring achieves superior personalized performance and competitive global performance, especially under higher levels of heterogeneity, showing its effectiveness for FL in diverse settings.

# V. CONCLUSION AND FUTURE WORK

V. CONCLUSION AND FUTURE WORKThis study introduces the HKS framework as a new way to balance personalization and generalization in FL when faced with diverse devices and data. By structuring logits into a multi-level hierarchy, HKS facilitates more efficient knowledge sharing among clients with varying models and data. The framework is tailored to account for differences in data distributions across clients. An important advantage of the HKS approach is that it does not require access to client label information or a public dataset to achieve competitive performance. Instead, HKS utilizes hierarchical clustering to organize the knowledge exchange to improve learning. Additionally, this approach maintains communication costs at a low level, as only logits are exchanged between clients rather than full model parameters or data. Experimental results show that HKS consistently achieves superior personalized accuracy (MAUA), particularly in more heterogeneous settings, while demonstrating competitive global performance compared to state-of-the-art methods. HKS is a promising approach for FL systems in heterogeneous environments where privacy and communication costs are critical. Future work should focus on dynamic hyperparameter tuning to maintain robustness across varying non-IID scenarios.

# REFERENCES

[1] H. B. McMahan, E. Moore, D. Ramage, S. Hampson, and B. A. y Arcas, "Communication- efficient learning of deep networks from decentralized data," arXiv 1602.05629, 2023. [2] S. Kornblith, M. Norouzi, H. Lee, and G. Hinton, "Similarity of neural network representations revisited," arXiv 1905.00414, 2019. [3] H. Venkateswara, J. Eusebo, S. Chakraborty, and S. Panchanathan, "Deep hashing network for unsupervised domain adaptation," in 2017 IEEE Conference on Computer Vision and Pattern Recognition (CVPR), 2017. [4] S. Leroux, B. Li, and P. Simoens, "Multi- branch neural networks for video anomaly detection in adverse lighting and weather conditions," in 2022 IEEE/CVF Winter Conference on Applications of Computer Vision (WACV), 2022. [5] Q. Mou, L. Wei, C. Wang, D. Luo, S. He, J. Zhang, H. Xu, C. Luo, and C. Gao, "Unsupervised domain- adaptive scene- specific pedestrian detection for static video surveillance," Pattern Recognition, vol. 118, p. 108038, 2021. [6] Y. Zhao, M. Li, L. Lai, N. Suda, D. Civin, and V. Chandra, "Federated learning with non- iid data," arXiv preprint arXiv:1806.00582, 2018. [7] H. Zhu, J. Xu, S. Liu, and Y. Jin, "Federated learning on non- iid data: A survey," arXiv 2106.06343, 2021. [8] M. Firdaus, S. Noh, Z. Qian, H. Larasati, and K. H. Rhee, "Personalized federated learning for heterogeneous data: A distributed edge clustering approach," Mathematical Sciences and engineering: MBE, vol. 20, pp. 10 725- 10 740, 04 2023. [9] V. Kulkarni, M. Kulkarni, and A. Pant, "Survey of personalization techniques for federated learning," in 2020 Fourth World Conference on Smart Trends in Systems, Security and Sustainability (WorldS4), 2020. [10] K. C. Sim, P. Zadrazil, and F. Beaufays, "An investigation into on- device personalization of end- to- end automatic speech recognition models," arXiv 1909.06678, 2019. [11] T. Li, A. K. Sahu, M. Zaheer, M. Sanjabi, A. Talwalkar, and V. Smith, "Federated optimization in heterogeneous networks," arXiv 1812.06127, 2020. [12] H. Wang, M. Yurochkin, Y. Sun, D. Papailiopoulos, and Y. Khazaeni, "Federated learning with matched averaging," arXiv 2002.06440, 2020. [13] D. A. E. Acar, Y. Zhao, R. M. Navarro, M. Mattina, P. N. Whatmough, and V. Saligrama, "Federated learning based on dynamic regularization," arXiv 2111.04263, 2021. [14] S. P. Karimireddy, S. Kale, M. Mohri, S. J. Reddi, S. U. Stich, and A. T. Suresh, "Scaffold: Stochastic controlled averaging for federated learning," arXiv 1910.06378, 2021.

[15] Y. Deng, M. M. Kamani, and M. Mahdavi, "Adaptive personalized federated learning," arXiv 2003.13461, 2020. [16] T. Li, S. Hu, A. Beirami, and V. Smith, "Ditto: Fair and robust federated learning through personalization," arXiv 2012.04221, 2021. [17] Z. Wu, S. Sun, Y. Wang, M. Liu, K. Xu, W. Wang, X. Jiang, B. Gao, and J. Lu, "Fedcache: A knowledge cache- driven federated learning architecture for personalized edge intelligence," arXiv 2308.07816, 2024. [18] A. Z. Tan, H. Yu, L. Cui, and Q. Yang, "Towards personalized federated learning," IEEE Transactions on Neural Networks and Learning Systems, vol. 34, no. 12, p. 9587- 9603, Dec. 2023. [19] M. Firdaus, S. Noh, Z. Qian, and K. H. Rhee, BPFL: Blockchain- Enabled Distributed Edge Cluster for Personalized Federated Learning. Springer Nature Singapore Pte Ltd, 06 2023, pp. 431- 437. [20] G. Hinton, O. Vinyals, and J. Dean, "Distilling the knowledge in a neural network," arXiv 1503.02531, 2015. [21] D. Li and J. Wang, "Fedmd: Heterogeneous federated learning via model distillation," arXiv 1910.03587, 2019. [22] T. Lin, L. Kong, S. U. Stich, and M. Jaggi, "Ensemble distillation for robust model fusion in federated learning," arXiv 2006.07242, 2021. [23] E. Jeong, S. Oh, H. Kim, J. Park, M. Bennis, and S.- L. Kim, "Communication- efficient on- device machine learning: Federated distillation and augmentation under non- iid private data," arXiv 1811.11479, 2023. [24] L. Gao, H. Fu, L. Li, Y. Chen, M. Xu, and C.- Z. Xu, "Feddc: Federated learning with non- iid data via local drift decoupling and correction," arXiv 2203.11751, 2022. [25] Q. Li, B. He, and D. Song, "Model contrastive federated learning," arXiv 2103.16257, 2021. [26] G. Lee, M. Jeong, Y. Shin, S. Bae, and S.- Y. Yun, "Preservation of the global knowledge by not- true distillation in federated learning," arXiv 2106.03097, 2022. [27] S. Han, S. Park, F. Wu, S. Kim, C. Wu, X. Xie, and M. Cha, "Fedx: Unsupervised federated learning with cross knowledge distillation," arXiv 2207.09158, 2022. [28] W. Hao, M. El- Khamy, J. Lee, J. Zhang, K. J. Liang, C. Chen, and L. Carin, "Towards fair federated learning with zero- shot data augmentation," arXiv 2104.13417, 2021. [29] N. Guha, A. Talwalkar, and V. Smith, "One- shot federated learning," arXiv 1902.11175, 2019. [30] Y. Mansour, M. Mohri, J. Ro, and A. T. Suresh, "Three approaches for personalization with applications to federated learning," arXiv 2002.10619, 2020. [31] H. Wen, Y. Wu, J. Li, and H. Duan, "Communication- efficient federated data augmentation on non- iid data," in 2022 IEEE/CVF Conference on Computer Vision and Pattern Recognition Workshops (CVPR), 2022. [32] M. Duan, D. Liu, X. Chen, R. Liu, Y. Tan, and L. Liang, "Self- balancing federated learning with global imbalanced data in mobile systems," IEEE Transactions on Parallel and Distributed Systems, vol. 32, no. 1, pp. 59- 71, 2021. [33] H. Wang, Z. Kaplan, D. Niu, and B. Li, "Optimizing federated learning on non- iid data with reinforcement learning," in IEEE INFOCOM 2020 - IEEE Conference on Computer Communications, 2020. [34] Y. Jee Cho, J. Wang, and G. Joshi, "Towards understanding biased client selection in federated learning," in Proceedings of The 25th International Conference on Artificial Intelligence and Statistics, 28- 30 Mar 2022. [35] A. Fallah, A. Mokhtari, and A. Ozdaglar, "Personalized federated learning with theoretical guarantees: a model- agnostic meta- learning approach," in Proceedings of the 34th International Conference on Neural Information Processing Systems, 2020. [36] C. T. Dinh, N. H. Tran, and T. D. Nguyen, "Personalized federated learning with moreaus envelopes," arXiv 2006.08848, 2022. [37] Z. Qu, X. Li, X. Han, R. Duan, C. Shen, and L. Chen, "How to prevent the poor performance clients for personalized federated learning?" in 2023 IEEE/CVF Conference on Computer Vision and Pattern Recognition (CVPR), 2023. [38] J. Wang, D. Huang, X. Wu, Y. Tang, and L. Lan, "Continuous review and timely correction: Enhancing the resistance to noisy labels via self- not- true distillation," in ICASSP 2024 - 2024 IEEE International Conference on Acoustics, Speech and Signal Processing (ICASSP), 2024. [39] J. Mills, J. Hu, and G. Min, "Multi- task federated learning for personalized deep neural networks in edge computing," IEEE Transactions on Parallel and Distributed Systems, vol. 33, pp. 630- 641, 2020.

[40] H. Xiao, K. Rasul, and R. Vollgraf, "Fashion- mnist: a novel image dataset for benchmarking machine learning algorithms," arXiv 1708.07747, 2017.