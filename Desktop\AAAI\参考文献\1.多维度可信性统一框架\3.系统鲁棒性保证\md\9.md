# ClusMFL: A Cluster-Enhanced Framework for Modality-Incomplete Multimodal Federated Learning in Brain Imaging Analysis

<PERSON><PERSON><PERSON><PERSON>†, <PERSON><PERSON>‡, <PERSON>§, <PERSON><PERSON>†, <PERSON><PERSON><PERSON>‡ and <PERSON>§

Abstract—Multimodal Federated Learning (MFL) has emerged as a promising approach for collaboratively training multimodal models across distributed clients, particularly in healthcare domains. In the context of brain imaging analysis, modality incompleteness presents a significant challenge, where some institutions may lack specific imaging modalities (e.g., PET, MRI, or CT) due to privacy concerns, device limitations, or data availability issues. While existing work typically assumes modality completeness or oversimplifies missing- modality scenarios, we simulate a more realistic setting by considering both client- level and instance- level modality incompleteness in this study. Building on this realistic simulation, we propose ClusMFL, a novel MFL framework that leverages feature clustering for cross- institutional brain imaging analysis under modality incompleteness. Specifically, ClusMFL utilizes the FINCH algorithm to construct a pool of cluster centers for the feature embeddings of each modality- label pair, effectively capturing fine- grained data distributions. These cluster centers are then used for feature alignment within each modality through supervised contrastive learning, while also acting as proxies for missing modalities, allowing cross- modal knowledge transfer. Furthermore, ClusMFL employs a modality- aware aggregation strategy, further enhancing the model's performance in scenarios with severe modality incompleteness. We evaluate the proposed framework on the ADNI dataset, utilizing structural MRI and PET scans. Extensive experimental results demonstrate that ClusMFL achieves state- of- the- art performance compared to various baseline methods across varying levels of modality incompleteness, providing a scalable solution for cross- institutional brain imaging analysis.

# I. INTRODUCTION

Multimodal Federated Learning (MFL) has emerged as a transformative approach for collaboratively training machine learning models across distributed clients with multimodal data, especially in privacy- sensitive domains like healthcare [1]- [3]. By integrating diverse data modalities, MFL facilitates the development of robust and accurate models for intelligent clinical decision support systems. However, real- world healthcare applications, particularly in cross- institutional brain imaging analysis, often face the challenge of modality incompleteness [4]. Specifically, some institutions may have access solely to PET imaging data, while others may have access only to MRI data. This disparity in available modalities significantly undermines the performance of traditional federated learning frameworks, which typically assume that all clients have access to the same set of modalities.

To address this challenge, researchers have proposed various methods to mitigate the impact of missing modalities in MFL. These methods can be broadly categorized into prototype- based approaches and generative approaches, each with distinct strengths and limitations. Prototype- based methods [4]- [6] leverage the average of feature embeddings within the same class to serve as prototypes for feature alignment or modality completion. While this approach is effective in capturing general class- level characteristics, it struggles to encapsulate the inherent diversity and complexity of individual data points [7]- [9]. As a result, the simplistic averaging process often produces prototypes that inadequately represent the underlying data distribution, leading to suboptimal alignment of modality- specific feature embeddings and ultimately degrading the global model's performance.

On the other hand, generative approaches attempt to reconstruct missing modalities using generative models, which hold promise for improving data completeness [10]- [12]. However, these generative methods typically require modality- complete instances for effective training, yet such instances are often scarce in real- world settings of modality- incomplete MFL [11]. This limitation restricts their applicability to incomplete data and increases the likelihood of introducing noise or inaccuracies during the reconstruction process. Moreover, methods based on Generative Adversarial Networks (GANs) [13] are particularly susceptible to mode collapse, where the model fails to capture the full diversity of the data, leading to the generation of highly similar, non- representative samples [14], [15]. These limitations render generative approaches less robust and reliable in practical applications of modality- incomplete MFL.

Beyond the limitations of prototype- based and generative approaches in addressing modality incompleteness in MFL, existing studies on modality- incomplete MFL share several common shortcomings from the perspective of federated learning. One such limitation is the unrealistic simulation of MFL scenarios, which assumes uniform modality availability or consistent missing patterns across all clients. These oversimplified simulations fail to accurately represent real- world modality incompleteness scenarios, thereby limiting their practical applicability, particularly in cross- institutional brain imaging analysis [16]. Furthermore, much of the current work on MFL with missing modalities employs uniform

aggregation weights for different modules within the model during the federated learning process [17]. This uniform approach overlooks the varying modality distributions across clients, resulting in suboptimal global model performance.

To more accurately reflect the modality incompleteness in real- world scenarios of cross- institutional brain imaging analysis, this paper introduces a realistic and comprehensive setting for modality distribution. Unlike prior studies that assume uniform modality availability or consistent missing modalities across clients, our setting simulates both client- level and instance- level modality incompleteness, as illustrated in Fig. 1, capturing the diversity and complexity of modality distribution in real- world applications.

Building on this simulation, we propose ClusMFL, a novel framework designed to address the challenges of modality incompleteness in MFL. Concretely, we apply the FINCH clustering algorithm [18] to cluster feature embeddings and construct a pool of cluster centers for each pair of modality and label across clients, providing a finer- grained representation of data distributions compared to traditional prototype- based methods. To ensure that modality- specific encoders accurately extract modality- specific features related to the labels, we perform feature alignment by employing supervised contrastive loss [19] over local feature embeddings and the pool of cluster centers. Furthermore, to mitigate the impact of severe modality incompleteness, we use these cluster centers as proxies for the feature embeddings of the missing modalities during the training process, enabling cross- modality knowledge transfer. In addition, we utilize a modality- aware aggregation method that assigns different aggregation weights to various modules of the model based on the modality distributions, effectively balancing the contributions from each client to different modules.

To evaluate the effectiveness of the proposed framework, we conduct extensive experiments under various settings of modality incompleteness and compare ClusMFL with several traditional federated learning algorithms, as well as algorithms specifically designed for modality- incomplete MFL. Specifically, we use brain imaging data from the real- world ADNI dataset, which includes structural MRI and PET scans for 915 participants stratified into three diagnostic categories: healthy controls, mild cognitive impairment (MCI), and Alzheimer's disease (AD) patients. By simulating realistic settings of modality incompleteness on this dataset, we aim to evaluate our framework under conditions that reflect real- world clinical challenges. The results show that ClusMFL outperforms existing approaches, particularly in scenarios with substantial modality incompleteness, underscoring its capability to address real- world challenges in MFL.

# II. MODALITY INCOMPLETENESS SETTING

In this study, we focus on two representative brain imaging modalities: PET and MRI, and simulate a more realistic setting by considering both client- level and instance- level modality incompleteness. Each instance is represented as a triplet  $(\mathbf{x}_P,\mathbf{x}_M,y)$  ,where  $\mathbf{x}_P$  corresponds to data from the PET modality,  $\mathbf{x}_M$  corresponds to data from the MRI modality, and  $y$  represents the associated label. Based on the availability of modalities, data instances are categorized into the following three types, as shown in Fig. 1:

![](images/a881e2fbc64f56131589e1933c3a5d92d159c1d43901264c035b6e94b6214d25.jpg)  
Fig. 1. Illustration of Modality Incompleteness Setting. Dash boxes denote the missing modality, while each pair of boxes represents an instance.

1) PET-only instances: These instances contain data solely from the PET modality, with no corresponding MRI data. Such instances are formally denoted as  $d_{P} = (\mathbf{x}_{P},\mathcal{O},y)$  
2) MRI-only instances: These instances exclusively contain data from the MRI modality, with no accompanying PET data. These are represented as  $d_{M} =$ $(\mathcal{O},\mathbf{x}_M,y)$  
3) Multimodal instances: These instances include data from both modalities, and are represented as  $d_{B} =$ $(\mathbf{x}_P,\mathbf{x}_M,y)$

At the client level, we categorize clients into three groups based on the composition of their instances:

1) PET-only clients: These clients exclusively host PET-only instances. Their datasets are denoted as  $\mathcal{D}_P = \{d_P^i\}_{i = 1}^n$  where  $n$  is the number of instances. 
2) MRI-only clients: These clients solely host MRI-only instances, denoted as  $\mathcal{D}_M = \{d_M^i\}_{i = 1}^n$  
3) Multimodal clients: These clients contain a mix of all three types of instances. Their datasets are represented as:

$$
\mathcal{D}_B = \{d_P^i\}_{i = 1}^{\beta_1n}\cup \{d_M^i\}_{i = 1}^{\beta_2n}\cup \{d_B^i\}_{i = 1}^{(1 - \beta_1 - \beta_2)n},
$$

where  $\beta_{1}$  and  $\beta_{2}$  indicate the proportions of PET- only and MRI- only instances on multimodal clients, respectively, while  $1 - \beta_{1} - \beta_{2}$  denotes the proportion of multimodal instances on multimodal clients.

For client  $i$  we denote the number of instances on this client as  $n_i$  . The overall dataset is defined as follows:

$$
\mathcal{D} = \bigcup_{i = 1}^{\alpha_1N}\mathcal{D}_P^i\cup \bigcup_{i = 1}^{\alpha_2N}\mathcal{D}_M^i\cup \bigcup_{i = 1}^{(1 - \alpha_1 - \alpha_2)N}\mathcal{D}_B^i, \tag{1}
$$

where  $\alpha_{1},\alpha_{2}$  ,and  $1 - \alpha_{1} - \alpha_{2}$  represent the proportions of PET- only, MRI- only, and multimodal clients, respectively, and  $N$  denotes the total number of clients participating in the federated learning framework.

![](images/4763dfd2707c34300db4cd019b9240d34a860deacf12192fa12a561508772209.jpg)  
Fig. 2. Overview of ClusMFL. In this figure, PET-only instances are used as examples of single-modality instances in local training. Different patterns represent different modalities, and different colors indicate different labels.

# III. METHOD

# A. Preliminary

In this study, we adopt a typical architecture for multimodal models, which includes two encoders one for each modality and a classifier. Let  $f_{P}$  and  $f_{M}$  denote the encoders for PET modality and MRI modality, respectively. The encoders  $f_{P}$  and  $f_{M}$  are responsible for extracting the relevant features from each modality. The model also includes a classifier, denoted as  $g$ , which concatenates the embeddings from the encoders and performs the final prediction.

During the inference stage, for instances containing both modalities, the input data  $\mathbf{x}_{P}$  and  $\mathbf{x}_{M}$  are processed through their respective encoders  $f_{P}$  and  $f_{M}$ . Specifically, the feature embeddings are obtained as  $\mathbf{z}_{P} = f_{P}(\mathbf{x}_{P})$  and  $\mathbf{z}_{M} = f_{M}(\mathbf{x}_{M})$ , which are subsequently concatenated and passed to the classifier  $g$  for the final prediction,  $\hat{y} = g(\mathbf{z}_{P}, \mathbf{z}_{M})$ . For instances with only a single modality, the feature embedding of the missing modality is replaced with a tensor of zeros, denoted as 0.

The federated training process repeats the construction of the pool of cluster centers and cluster sizes (i.e.,  $\mathbf{C}^{\mathrm{global}}$  and  $\mathbf{S}^{\mathrm{global}}$ ), followed by local training and aggregation in each round, which are explained in detail in the following sections. We provide an overview of the construction of  $\mathbf{C}^{\mathrm{global}}$  and  $\mathbf{S}^{\mathrm{global}}$  and local training in Fig. 2.

# B. Constructing The Pool of Cluster Centers

Unlike traditional prototype learning methods, which use the mean of features as the prototype and result in a shift between individual samples and the prototype, this study adopts the FINCH [18] algorithm to construct a pool of cluster centers, thereby more effectively representing the clients' data distribution.

On each client, we begin by applying the FINCH clustering algorithm to calculate the local cluster centers for feature embeddings of each pair of modality and label. The resulting cluster centers, along with cluster sizes, are then uploaded to the server. For instance, consider the modality PET. The feature embeddings associated with modality PET are extracted as  $\mathbf{Z}_{P} = f_{P}(\mathbf{X}_{P})$ , where  $\mathbf{X}_{P}$  represents all PET data on this client. The FINCH algorithm is then applied to  $\mathbf{Z}_{P}$  for clustering, yielding the corresponding cluster centers and cluster sizes. Specifically, for label  $j$  in modality PET, FINCH identifies the cluster centers as:

$$
(\mathbf{C}_{P,j},\mathbf{S}_{P,j}) = \mathrm{FINCH}(\mathbf{Z}_{P,j}), \tag{2}
$$

where  $\mathbf{Z}_{P,j}$  denotes the feature embeddings with modality PET corresponding to label  $j$ , and  $\mathbf{C}_{P,j} = (c_{P,j}^{1}, c_{P,j}^{2}, \ldots , c_{P,j}^{K_{P,j}})$  represents the  $K_{P,j}$  cluster centers obtained from the FINCH algorithm for label  $j$ . The set  $\mathbf{S}_{P,j} = (s_{P,j}^{1}, s_{P,j}^{2}, \ldots , s_{P,j}^{K_{P,j}})$  represents the sizes of the corresponding clusters, where each  $s_{P,j}^{k}$  denotes the number of feature embeddings assigned to the  $k$ - th cluster for label  $j$ , with  $c_{P,j}^{k}$  being the cluster center.  $K_{P,j}$  represents the number of clusters, which is determined automatically by FINCH.

For client  $i$ , the cluster centers and sizes associated with modality PET and label  $j$  are denoted as  $\mathbf{C}_{P,j}^{i}$  and  $\mathbf{S}_{P,j}^{i}$ , respectively. If client  $i$  lacks data for modality PET, we set  $\mathbf{C}_{P,j}^{i} = \emptyset$  and  $\mathbf{S}_{P,j}^{i} = \emptyset$ .

Once the cluster centers and cluster sizes for each client are computed, they are sent to the server, where they are collected to form a global pool of cluster centers. Specifically, the global pool of cluster centers for label  $j$  in modality PET, denoted as  $\mathbf{C}_{P,j}^{\mathrm{global}}$ , is constructed by concatenating the cluster centers from all clients:

$$
\mathbf{C}_{P,j}^{\mathrm{global}} = \bigoplus_{i = 1}^{N}\mathbf{C}_{P,j}^{i}. \tag{3}
$$

Similarly, the corresponding cluster sizes for label  $j$  are cancatenated into a global pool, denoted as  $\mathbf{S}_{P,j}^{\mathrm{global}}$ :

$$
\mathbf{S}_{P,j}^{\mathrm{global}} = \bigoplus_{i = 1}^{N}\mathbf{S}_{P,j}^{i}. \tag{4}
$$

The global pools  $\mathbf{C}_{P,j}^{\mathrm{global}}$  and  $\mathbf{S}_{P,j}^{\mathrm{global}}$  encapsulate the aggregated cluster centers and their respective sizes for each label  $j$  across all clients, enabling the model to leverage a comprehensive representation of the data distribution. These global pools are then distributed back to each client, allowing them to utilize the global information during local training.

The same process is performed for MRI modality. Let  $\mathbf{C}_{M,j}^{i}$  and  $\mathbf{S}_{M,j}^{i}$  represent the cluster centers and cluster sizes, respectively, for MRI and label  $j$  on client  $i$ . The global pools for MRI are constructed as:

$$
\mathbf{C}_{M,j}^{\mathrm{global}} = \bigoplus_{i = 1}^{N}\mathbf{C}_{M,j}^{i},\quad \mathbf{S}_{M,j}^{\mathrm{global}} = \bigoplus_{i = 1}^{N}\mathbf{S}_{M,j}^{i}. \tag{5}
$$

These global pools for MRI are also distributed back to the clients to ensure that the global knowledge from both modalities is available for further training and optimization.

# C. Feature Alignment

To ensure that the encoders  $f_{M}$  and  $f_{P}$  extract the correct modality- specific features and mitigate overfitting under severe modality incompleteness, we combine global cluster centers and local feature embeddings and apply supervised contrastive loss for feature alignment.

For any multimodal client with dataset  $\mathcal{D}_B$  let  $\mathbf{X}_P$  and  $\mathbf{X}_M$  represent all PET and MRI data on this client, respectively, along with their corresponding labels  $\mathbf{y}_P$  and  $\mathbf{y}_M$ . The feature embeddings for PET modality and MRI modality are computed as  $\mathbf{Z}_P = f_P(\mathbf{X}_P)$  and  $\mathbf{Z}_M = f_M(\mathbf{X}_M)$  respectively. To incorporate global information, the feature embeddings  $\mathbf{Z}_P$  are concatenated with the global cluster centers  $\mathbf{C}_{P}^{\mathrm{global}}$ :

$$
\mathbf{Z}_{P,G} = \mathbf{Z}_P\oplus \bigoplus_{j = 1}^{J}\mathbf{C}_{P,j}^{\mathrm{global}}, \tag{6}
$$

where  $J$  is the total number of unique labels. Similarly, the label  $\mathbf{y}_P$  is extended as:

$$
\mathbf{y}_{P,G} = \mathbf{y}_P\oplus \bigoplus_{j = 1}^{J}(j)^{|\mathbf{C}_{P,j}^{\mathrm{global}}|}, \tag{7}
$$

where  $(j)^{|\mathbf{C}_{P,j}^{\mathrm{global}}|}$  denotes a list containing label  $j$  repeated  $|\mathbf{C}_{P,j}^{\mathrm{global}}|$  times.

We compute the supervised contrastive loss over  $\mathbf{Z}_{P,G}$ . Let  $\mathcal{I} = \{1,2,\ldots ,|\mathbf{y}_{P,G}|\}$  denote the index set, and define

$\mathcal{A}(i) = \mathcal{I}\backslash \{i\}$  as the set of all indices excluding  $i$ . The supervised contrastive loss for PET modality is defined as:

$$
\mathcal{L}_{\mathrm{ctr}}^{P}(\mathbf{X}_{P}) = \mathbb{E}_{i\in \mathcal{I}}\left[-\frac{1}{|\mathcal{P}(i)|}\sum_{p\in \mathcal{P}(i)}\log \frac{v_{i,p}}{\sum_{a\in\mathcal{A}(i)}v_{i,a}}\right], \tag{8}
$$

where:

$\mathcal{P}(i) = \{p\in \mathcal{A}(i)\mid \mathbf{y}_{P,G}^{(p)} = \mathbf{y}_{P,G}^{(i)}\}$  represents the set of indices corresponding to positive examples for  $\mathbf{Z}_{P,G}^{(i)}$ .  $v_{i,p} = \exp (\sin (\mathbf{Z}_{P,G}^{(i)},\mathbf{Z}_{P,G}^{(p)}) / \tau)$ , where  $\sin (\cdot ,\cdot)$  denotes the cosine similarity between two embeddings, and  $\tau$  is a temperature parameter.

The similar process is applied to calculate  $\mathcal{L}_{\mathrm{ctr}}^{M}(\mathbf{X}_M)$  and the overall supervised contrastive loss for  $\mathcal{D}_B$  is then computed as:

$$
\mathcal{L}_{\mathrm{CTR}}(\mathcal{D}_B) = \frac{|\mathbf{y}_P|\cdot\mathcal{L}_{\mathrm{ctr}}^P(\mathbf{X}_P) + |\mathbf{y}_M|\cdot\mathcal{L}_{\mathrm{ctr}}^M(\mathbf{X}_M)}{|\mathbf{y}_P| + |\mathbf{y}_M|}. \tag{9}
$$

For clients with data from only one modality, the loss is computed solely for the corresponding modality, without involving the other modality.

# D. Modality Completion Loss

To further enhance the model's classification performance in the presence of missing modalities, we use  $\mathbf{C}_{\mathrm{global}}^{\mathrm{global}}$  as approximations for feature embeddings of the missing modality. Specifically, for a PET- only instance with data  $d_{P} = (\mathbf{x}_{P},\mathcal{O},y)$  or an MRI- only instance with data  $d_{M} = (\mathcal{O},\mathbf{x}_{M},y)$ , we first pass the available modality through the corresponding encoder to obtain the feature embedding  $\mathbf{z}_P$  and  $\mathbf{z}_M$ . We then approximate the missing modality using the  $i$ - th cluster center from the corresponding global cluster centers. The prediction for each instance is given by:

$$
\hat{y}_{c}^{i} = \left\{ \begin{array}{ll}g(\mathbf{z}_{P},\mathbf{C}_{M,y}^{\mathrm{global},(i)}) & \mathrm{for~PET - only~instance},\\ g(\mathbf{C}_{P,y}^{\mathrm{global},(i)},\mathbf{z}_{M}) & \mathrm{for~MRI - only~instance}, \end{array} \right. \tag{10}
$$

where  $\mathbf{C}_{M,y}^{\mathrm{global},(i)}$  and  $\mathbf{C}_{P,y}^{\mathrm{global},(i)}$  represents the  $i$ - th cluster center from  $\mathbf{C}_{M,y}^{\mathrm{global}}$  and  $\mathbf{C}_{P,y}^{\mathrm{global}}$  respectively.

The loss for a PET- only instance is then computed as:

$$
\mathcal{L}_{\mathrm{mc}}(d_P) = \frac{1}{T_{M,y}}\sum_{i = 1}^{|\mathbf{S}_{M,y}^{\mathrm{global}}|}\mathbf{S}_{M,y}^{\mathrm{global},(i)}\cdot \mathcal{L}_{\mathrm{CE}}(\hat{y}_c^i,y), \tag{11}
$$

where  $\mathcal{L}_{\mathrm{CE}}$  denotes the cross- entropy loss function, and  $T_{M,y} = \sum_{i = 1}^{|\mathbf{S}_{M,y}^{\mathrm{global}}|}\mathbf{S}_{M,y}^{\mathrm{global},(i)}$ .

Similarly, for an MRI- only instance, the loss is computed as:

$$
\mathcal{L}_{\mathrm{mc}}(d_M) = \frac{1}{T_{P,y}}\sum_{i = 1}^{|\mathbf{S}_{P,y}^{\mathrm{global}}|}\mathbf{S}_{P,y}^{\mathrm{global},(i)}\cdot \mathcal{L}_{\mathrm{CE}}(\hat{y}_c^i,y), \tag{12}
$$

where  $T_{P,y} = \sum_{i = 1}^{|\mathbf{S}_{P,y}^{\mathrm{global}}|}\mathbf{S}_{P,y}^{\mathrm{global},(i)}$ .

For any client  $i$  let  $\begin{array}{rlr}{\mathcal{D}_i^{\mathrm{single}}} & = & {\{d\quad \mid \quad d\quad \in} \end{array}$ $\mathcal{D}_i,d$  is a single modality instance} be the subset of instances with a single modality (either PET- only or MRIonly). The overall modality completion loss is calculated as:

$$
\mathcal{L}_{\mathrm{MC}}(\mathcal{D}_i) = \mathbb{E}_{d\in \mathcal{D}_{\mathrm{single}}}\left[\mathcal{L}_{\mathrm{mc}}(d)\right]. \tag{13}
$$

In this manner, we use the global cluster centers of the available modality to approximate the missing modality, allowing the model to still make meaningful predictions in the presence of incomplete data.

# E. Overall Loss Function

For client  $i$  with dataset  $\mathcal{D}_i$  , the prediction  $\hat{y}_i$  is generated as described in Section III- A for each instance  $d_{i}$  .Let  $\hat{\mathbf{y}} =$ $(\hat{y}_1,\hat{y}_2,\dots ,\hat{y}_{n_i})$  denote the vector of predicted labels for the  $m$  instances in the client's dataset, and  $\mathbf{y} = (y_{1},y_{2},\dots ,y_{n_{i}})$  denote the corresponding true labels. The overall loss is computed as:

$$
\mathcal{L}(\mathcal{D}_i) = \mathcal{L}_{\mathrm{CE}}(\hat{\mathbf{y}},\mathbf{y}) + \lambda_1\mathcal{L}_{\mathrm{CTR}}(\mathcal{D}_i) + \lambda_2\mathcal{L}_{\mathrm{MC}}(\mathcal{D}_i), \tag{14}
$$

where  $\lambda_{1}$  and  $\lambda_{2}$  are the regularization coefficients that balance the contributions of the contrastive loss and the modality completion loss, respectively.

# F. Modality-Aware Aggregation

In this section, we describe the modality- aware aggregation (MAA) method adopted in our framework. Specifically, we use different aggregation weights for different modules within the model, based on the number of instances for each modality at each client.

Let  $n_P^i$  represent the number of instances with the PET modality at client  $i$  and  $n_M^i$  represent the number of instances with the MRI modality at client  $i$  . The total numbers of instances with PET and MRI modalities across all clients are denoted as  $n_P^{\mathrm{total}}$  and  $n_M^{\mathrm{total}}$  , and are computed as:

$$
n_{P}^{\mathrm{total}} = \sum_{i = 1}^{N}n_{P}^{i},\quad n_{M}^{\mathrm{total}} = \sum_{i = 1}^{N}n_{M}^{i}. \tag{15}
$$

For client  $i$  the aggregation weights for the encoders are computed as:

$$
w_{P}^{i} = \frac{n_{P}^{i}}{n_{\mathrm{total}}^{i}},\quad w_{M}^{i} = \frac{n_{M}^{i}}{n_{\mathrm{total}}^{M}}, \tag{16}
$$

where  $w_{P}^{i}$  and  $w_{M}^{i}$  are the aggregation weights for encoder  $f_{P}$  and encoder  $f_{M}$  , respectively.

The aggregation processes for encoders  $f_{P}$  and  $f_{M}$  are as follows:

$$
\mathbf{f}_P^{\mathrm{global}} = \sum_{i = 1}^N w_P^i\mathbf{f}_P^i,\quad \mathbf{f}_M^{\mathrm{global}} = \sum_{i = 1}^N w_M^i\mathbf{f}_M^i, \tag{17}
$$

where  $\mathbf{f}_P^i,\mathbf{f}_M^i$  are the model parameters of the PET encoder and MRI encoder, respectively.

For the classifier  $g$  , the aggregation process is given by:

$$
\mathbf{g}^{\mathrm{global}} = \frac{1}{\sum_{i = 1}^{N}n_{i}}\sum_{i = 1}^{N}n_{i}\cdot \mathbf{g}^{i}, \tag{18}
$$

where  $\mathbf{g}^i$  denotes the model parameters for classifier  $g$

# IV. EXPERIMENT

# A. Experimental Setup

The brain imaging dataset used in this study is sourced from the Alzheimer's Disease Neuroimaging Initiative (ADNI) public repository [20] and comprises 915 participants stratified into three diagnostic categories: 297 healthy controls (HC), 451 mild cognitive impairment (MCI), and 167 Alzheimer's disease (AD) patients. Multimodal neuroimaging acquisitions encompass structural Magnetic Resonance Imaging (VBM- MRI) and 18 F- florbetapir PET (AV45- PET), enabling examination of brain structure and amyloid plaque deposition, respectively.

Consistent with established neuroimaging processing pipelines [21], [22], we preprocess neuroimaging data to region- of- interest (ROI) features from each participant's images. First, the multi- modality imaging scans are aligned to each participant's same visit. All imaging scans are aligned to a T1- weighted template image. Subsequently, the images are segmented into gray matter (GM), white matter (WM) and cerebrospinal fluid (CSF) maps. They are normalized to the standard Montreal Neurological Institute (MNI) space as  $2\times 2\times 2\mathrm{mm}^3$  voxels, being smoothed with an 8 mm full- width at half- maximum (FWHM) Gaussian kernel. We preprocess the structural MRI scans with voxel- based morphometry (VBM) by using the SPM software [23], and register the AV45- PET scans to the MNI space by SPM. For both MRI and PET scans, we pancellate the brain into 90 ROIs (excluding the cerebellum and vermis) based on the AAL- 90 atlas [24], and computed ROI- level measures by averaging voxel- wise values within each region.

In the original dataset, each instance contains both modalities. We first split the dataset into a training set and a test set with a ratio of 1:4. For the test set, we ensure that the proportions of the three types of instances are equal, i.e., each type accounts for  $\frac{1}{3}$  of the total. In the federated learning setup, we distribute the training instances equally across clients while preserving label distribution. Each instance is then assigned a type (multimodal or single- modality) based on the MFL settings controlled by  $\alpha_{1}$ $\alpha_{2}$ $\beta_{1}$  ,and  $\beta_{2}$  as described in Section II. Single- modality instances drop the corresponding modality. For simplicity, we set  $\alpha_{1} = \alpha_{2} = \alpha$  and  $\beta_{1} = \beta_{2} = \beta$

To evaluate the effectiveness of our proposed method, we compare it against several baseline approaches, including FedAvg [25], FedProx [26], FedMed- GAN [10], FedMI [4], MFCPL [5], and PmcmFL [6]. FedAvg and FedProx are traditional federated learning algorithms, with FedAvg employing simple parameter averaging, while FedProx introduces a proximal term to the objective to mitigate client heterogeneity. FedMed- GAN employs CycleGAN [27] to complete missing modalities, enhancing diagnosis accuracy. FedMI, MFCPL, and PmcmFL leverage prototype learning to model class distributions and align features, effectively addressing incomplete modalities and heterogeneous data across clients. All methods are evaluated under the same experimental setup to ensure a fair comparison.

<table><tr><td rowspan="2">α</td><td rowspan="2">Method</td><td colspan="12">β = 0.2</td></tr><tr><td>Precision</td><td>Recall</td><td>FI</td><td>Accuracy</td><td>AUC</td><td>Precision</td><td>Recall</td><td>FI</td><td>Accuracy</td><td>AUC</td><td></td><td></td></tr><tr><td rowspan="7">0.2</td><td>FedAvg</td><td>53.94 ± 3.56</td><td>54.05 ± 5.53</td><td>53.21 ± 3.90</td><td>53.41 ± 3.71</td><td>69.39 ± 2.05</td><td>53.39 ± 2.34</td><td>53.38 ± 2.77</td><td>52.27 ± 2.32</td><td>52.64 ± 2.14</td><td>68.10 ± 2.72</td><td></td><td></td></tr><tr><td>FedProx</td><td>55.81 ± 1.58</td><td>54.37 ± 3.19</td><td>54.32 ± 2.21</td><td>54.62 ± 2.18</td><td>68.29 ± 2.93</td><td>54.58 ± 2.69</td><td>54.80 ± 1.82</td><td>53.30 ± 2.99</td><td>53.52 ± 2.97</td><td>68.11 ± 2.74</td><td></td><td></td></tr><tr><td>FedMed-GAN</td><td>54.62 ± 1.02</td><td>55.18 ± 2.73</td><td>53.46 ± 0.97</td><td>53.52 ± 1.07</td><td>68.80 ± 2.16</td><td>53.87 ± 3.66</td><td>54.66 ± 3.57</td><td>53.35 ± 3.61</td><td>53.41 ± 3.63</td><td>68.09 ± 2.53</td><td></td><td></td></tr><tr><td>FedMI</td><td>54.77 ± 2.94</td><td>55.22 ± 4.36</td><td>54.26 ± 2.82</td><td>54.40 ± 2.83</td><td>69.15 ± 2.73</td><td>54.03 ± 3.88</td><td>54.46 ± 3.56</td><td>53.54 ± 4.22</td><td>53.63 ± 4.14</td><td>67.61 ± 3.93</td><td></td><td></td></tr><tr><td>MFCCPL</td><td>54.57 ± 1.88</td><td>56.06 ± 4.54</td><td>53.88 ± 1.84</td><td>54.07 ± 2.93</td><td>69.39 ± 2.71</td><td>53.83 ± 5.42</td><td>54.01 ± 3.44</td><td>53.22 ± 5.06</td><td>53.30 ± 5.34</td><td>67.58 ± 3.67</td><td></td><td></td></tr><tr><td>PmcMFL</td><td>53.14 ± 1.60</td><td>52.73 ± 3.10</td><td>52.58 ± 1.61</td><td>52.64 ± 1.03</td><td>67.51 ± 4.07</td><td>49.06 ± 1.55</td><td>48.01 ± 5.03</td><td>48.38 ± 2.20</td><td>48.46 ± 2.31</td><td>63.71 ± 2.50</td><td></td><td></td></tr><tr><td>ClusMFL</td><td>57.16 ± 2.32</td><td>54.73 ± 3.93</td><td>56.56 ± 2.36</td><td>56.92 ± 2.41</td><td>72.81 ± 3.64</td><td>56.06 ± 1.31</td><td>55.44 ± 2.19</td><td>55.38 ± 1.07</td><td>55.49 ± 1.10</td><td>72.50 ± 2.02</td><td></td><td></td></tr><tr><td rowspan="7">0.4</td><td>FedAvg</td><td>53.75 ± 3.36</td><td>54.26 ± 3.76</td><td>52.76 ± 4.64</td><td>53.08 ± 4.34</td><td>67.91 ± 3.48</td><td>52.60 ± 3.00</td><td>53.95 ± 3.66</td><td>51.84 ± 3.06</td><td>51.98 ± 3.07</td><td>67.03 ± 3.22</td><td></td><td></td></tr><tr><td>FedProx</td><td>54.36 ± 3.50</td><td>54.28 ± 4.17</td><td>53.77 ± 3.48</td><td>53.96 ± 3.51</td><td>67.26 ± 3.53</td><td>52.16 ± 2.20</td><td>52.93 ± 3.76</td><td>51.35 ± 2.13</td><td>51.54 ± 3.07</td><td>67.43 ± 3.64</td><td></td><td></td></tr><tr><td>FedMed-GAN</td><td>52.97 ± 2.53</td><td>52.89 ± 2.41</td><td>52.43 ± 1.74</td><td>52.93 ± 1.63</td><td>68.59 ± 2.48</td><td>53.16 ± 3.57</td><td>54.02 ± 3.98</td><td>52.64 ± 2.97</td><td>52.86 ± 2.63</td><td>67.91 ± 2.93</td><td></td><td></td></tr><tr><td>FedMI</td><td>55.40 ± 3.65</td><td>53.82 ± 2.45</td><td>54.84 ± 2.75</td><td>54.55 ± 2.69</td><td>67.59 ± 3.45</td><td>54.50 ± 2.07</td><td>52.36 ± 1.98</td><td>53.52 ± 3.14</td><td>53.85 ± 2.06</td><td>66.70 ± 2.63</td><td></td><td></td></tr><tr><td>MFCCPL</td><td>55.07 ± 4.34</td><td>54.66 ± 3.93</td><td>54.34 ± 4.37</td><td>54.51 ± 4.57</td><td>66.60 ± 2.58</td><td>52.84 ± 4.14</td><td>54.69 ± 3.92</td><td>51.70 ± 3.89</td><td>51.87 ± 3.74</td><td>67.81 ± 3.48</td><td></td><td></td></tr><tr><td>PmcMFL</td><td>51.71 ± 5.54</td><td>59.80 ± 6.21</td><td>50.77 ± 5.21</td><td>50.88 ± 5.42</td><td>65.54 ± 5.22</td><td>52.45 ± 3.96</td><td>50.03 ± 5.74</td><td>50.40 ± 4.72</td><td>50.77 ± 4.66</td><td>63.31 ± 5.17</td><td></td><td></td></tr><tr><td>ClusMFL</td><td>56.59 ± 4.53</td><td>54.68 ± 4.49</td><td>56.17 ± 4.08</td><td>56.37 ± 4.10</td><td>73.25 ± 4.11</td><td>54.83 ± 6.13</td><td>54.88 ± 6.09</td><td>54.22 ± 5.89</td><td>54.40 ± 6.03</td><td>72.22 ± 4.47</td><td></td><td></td></tr></table>

![](images/13b8fd014d6724429f4352b26cf0a648afc7299cb5fe8b8d378d9e4d7b54bdbc.jpg)  
Fig. 3. Training curves of different methods.

In our experiment, we set the number of clients to  $N = 10$  with the number of communication rounds fixed at 30 and each client performing 10 local training epochs. For optimization, we employ the Adam [28] optimizer with an initial learning rate of 0.01. To dynamically adjust the learning rate during training, we use cosine annealing strategy. We conduct 5- fold cross- validation and report the results as the mean  $\pm$  standard deviation across all folds. For evaluation metrics, we adopt a weighted average for precision, F1- score, and ROC- AUC, while using a macro average for recall.

# B. Main Results

Table I presents a comprehensive comparison of the performance of our proposed method against several baseline approaches. The experiments were conducted under varying configurations of  $\beta$  and  $\alpha$  ,which denote modality incompleteness and client diversity, respectively.

As indicated in Table I, our method consistently outperforms the baseline approaches across different settings of modality incompleteness, achieving superior results in terms of precision, recall, F1 score, accuracy, and AUC. Notably, certain algorithms specifically designed for modalityincomplete MFL fail to outperform traditional federated learning methods, such as FedProx, in some scenarios.

Moreover, as the values of  $\alpha$  and  $\beta$  increase- corresponding to a higher proportion of instances with only a single modality- most of the baseline algorithms exhibit a noticeable decline in performance. In contrast, our proposed method demonstrates stable and robust performance, highlighting its effectiveness and resilience in handling varying degrees of modality incompleteness.

<table><tr><td>MAA</td><td>LCTR</td><td>CMC</td><td>Precision</td><td>Recall</td><td>F1-score</td><td>Accuracy</td><td>AUC</td></tr><tr><td rowspan="7">✓</td><td rowspan="7">✓</td><td rowspan="7">✓</td><td>53.74 ± 6.81</td><td>53.62 ± 7.24</td><td>52.69 ± 7.45</td><td>52.86 ± 5.54</td><td>72.10 ± 5.10</td></tr><tr><td>54.89 ± 6.49</td><td>54.12 ± 6.42</td><td>53.95 ± 6.23</td><td>54.18 ± 6.39</td><td>72.42 ± 4.22</td></tr><tr><td>53.50 ± 1.60</td><td>54.08 ± 1.80</td><td>53.62 ± 1.85</td><td>53.19 ± 1.80</td><td>71.34 ± 2.69</td></tr><tr><td>56.39 ± 2.95</td><td>53.69 ± 3.39</td><td>55.47 ± 2.58</td><td>55.82 ± 2.84</td><td>71.29 ± 3.35</td></tr><tr><td>54.67 ± 2.21</td><td>52.27 ± 2.50</td><td>54.13 ± 2.16</td><td>54.40 ± 2.27</td><td>71.41 ± 2.60</td></tr><tr><td>55.49 ± 0.70</td><td>54.40 ± 2.27</td><td>54.40 ± 2.27</td><td>55.16 ± 1.00</td><td>71.61 ± 2.29</td></tr><tr><td>56.59 ± 4.53</td><td>54.68 ± 4.49</td><td>56.17 ± 4.08</td><td>56.37 ± 4.10</td><td>73.25 ± 4.11</td></tr></table>

TABLE II ABLATION STUDY: PERFORMANCE EVALUATION UNDER THE SETTING OF  $\alpha = 0.4$ $\beta = 0.2$  (MEAN  $\pm$  STANDARD DEVIATION  $\mathcal{K}$

# C. Ablation Study

In order to assess the contributions of each component, we conduct an ablation study under the setting of  $\alpha = 0.4$  and  $\beta = 0.2$  as shown in Table II. The results demonstrate that applying modality- aware aggregation (MAA) alone yields lower performance across all metrics. Incorporating the contrastive loss  $(\mathcal{L}_{\mathrm{CTR}})$  improves the results significantly, while the modality completion loss  $(\mathcal{L}_{\mathrm{MOD}})$  also leads to moderate gains. Combining both losses without MAA further enhances performance. The full method, which includes both MAA and the two loss functions, achieves the highest performance, with precision, recall, F1- score, accuracy, and AUC all showing notable improvements. These findings highlight the effectiveness of combining modality- aware aggregation with the contrastive and modality completion losses in addressing modality incompleteness in MFL.

# D. Convergence Efficiency

To better analyze the convergence behavior of different algorithms in modality- incompleteness scenarios, we conduct experiments with four different random seeds in the setting of the first fold, with  $\alpha = 0.4$  and  $\beta = 0.2$ . The training curves, displayed in Fig. 3, represent the mean values across these

random seeds, while the shaded areas indicate the standard deviation. From Fig. 3, we make the following observations:

- F1 Score and AUC: Our method exhibits a rapid and consistent increase in both F1 score and AUC during the initial communication rounds, and subsequently stabilizes at higher values compared to the baseline methods. This indicates its superior ability to quickly capture relevant patterns. Furthermore, the consistent improvement in both metrics suggests that our approach enhances class discrimination more effectively as training progresses.- Test Loss: Our method achieves convergence with fewer communication rounds compared to the baseline methods. Specifically, it converges around the 11th communication round, whereas most baseline methods require approximately 20 rounds to reach convergence, highlighting the superior efficiency of our approach.- Time: Our method achieves significant performance improvements without a substantial increase in training time. Furthermore, the training time of our method remains significantly lower than that of FedMed-GAN, which requires adversarial training for GANs.

Time: Our method achieves significant performance improvements without a substantial increase in training time. Furthermore, the training time of our method remains significantly lower than that of FedMed- GAN, which requires adversarial training for GANs.

# V. CONCLUSION

In this study, we propose ClusMFL, a novel framework designed to address modality incompleteness in multimodal federated learning. ClusMFL enhances local data representation with the FINCH clustering algorithm, mitigates missing modality effects through supervised contrastive loss and modality completion loss, and employs a modality- aware aggregation strategy to adaptively integrate client contributions. Extensive experiments on the ADNI dataset demonstrate that ClusMFL outperforms state- of- the- art methods, particularly in scenarios with severe modality incompleteness, though at a slightly higher computational cost. Future research will explore the incorporation of additional modalities to further enhance the proposed framework's applicability.

# REFERENCES

[1] J. Thrasher, A. Devkota, P. Siwakotai, R. Chivukula, P. Poudel, C. Hu, B. Bhattarai, and P. Gyawali, "Multimodal federated learning in healthcare: a review," arXiv preprint arXiv:2310.09650, 2023. [2] L. Che, J. Wang, Y. Zhou, and F. Ma, "Multimodal federated learning: A survey," Sensors, vol. 23, no. 15, p. 6986, 2023. [3] H. Pan, X. Zhao, L. He, Y. Shi, and X. Lin, "A survey of multimodal federated learning: background, applications, and perspectives," Multimedia Systems, vol. 30, no. 4, p. 222, 2024. [4] Y. An, Y. Bai, Y. Liu, L. Guo, and X. Chen, "A multimodal federated learning framework for modality incomplete scenarios in healthcare," in International Symposium on Bioinformatics Research and Applications. Springer, 2024, pp. 245- 256. [5] H. Q. Le, C. M. Thwal, Y. Qiao, Y. L. Tun, M. N. Nguyen, and C. S. Hong, "Cross- modal prototype based multimodal federated learning under severely missing modality," arXiv preprint arXiv:2401.13898, 2024. [6] G. Bao, Q. Zhang, D. Miao, Z. Gong, L. Hu, K. Liu, Y. Liu, and C. Shi, "Multimodal federated learning with missing modality via prototype mask and contrast," arXiv preprint arXiv:2312.13508, 2023. [7] Y. Chen, Z. Liu, H. Xu, T. Darrell, and X. Wang, "Meta- baseline: Exploring simple meta- learning for few- shot learning," in Proceedings of the IEEE/CVF international conference on computer vision, 2021, pp. 9062- 9071.

[8] S. Yang, L. Liu, and M. Xu, "Free lunch for few- shot learning: Distribution calibration," in International Conference on Learning Representations, 2021. [9] W. Fan, J. Wen, X. Jia, L. Shen, J. Zhou, and Q. Li, "Epl: Empirical prototype learning for deep face recognition," 2024. [Online]. Available: https://arxiv.org/abs/2405.12447 [10] J. Wang, G. Xie, Y. Huang, J. Lyu, F. Zheng, Y. Zheng, and Y. Jin, "Fedmed- gan: Federated domain translation on unsupervised cross- modality brain image synthesis," Neurocomputing, vol. 546, p. 126282, 2023. [11] B. Xiong, X. Yang, Y. Song, Y. Wang, and C. Xu, "Client- adaptive cross- model reconstruction network for modality- incomplete multimodal federated learning," in Proceedings of the 31st ACM International Conference on Multimedia, 2023, pp. 1241- 1249. [12] R. Wu, H. Wang, H.- T. Chen, and G. Carneiro, "Deep multimodal learning with missing modality: A survey," arXiv preprint arXiv:2409.07825, 2024. [13] I. Goodfellow, J. Pouget- Abadie, M. Mirza, B. Xu, D. Warde- Farley, S. Ozair, A. Courville, and Y. Bengio, "Generative adversarial networks," Communications of the ACM, vol. 63, no. 11, pp. 139- 144, 2020. [14] S. Duan, C. Liu, P. Han, X. Jin, X. Zhang, T. He, H. Pan, and X. Xiang, "Ht- fed- gan: Federated generative model for decentralized tabular data synthesis," Entropy, vol. 25, no. 1, p. 88, 2022. [15] P. J. Maliakel, S. Ilager, and I. Brandic, "Fligan: Enhancing federated learning with incomplete data using gan," 2024. [Online]. Available: https://arxiv.org/abs/2403.16930 [16] M. D. Nguyen, T. T. Nguyen, H. H. Pham, T. N. Hoang, P. L. Nguyen, and T. T. Huynh, "Fedmac: Tackling partial- modality missing in federated learning with cross- modal aggregation and contrastive regularization," 2024. [Online]. Available: https://arxiv.org/abs/2410.03070 [17] Q. Fan and L. Shuai, "Adaptive hyper- graph aggregation for modality- agnostic federated learning," in 2024 IEEE/CVF Conference on Computer Vision and Pattern Recognition (CVPR), 2024, pp. 12312- 12321. [18] S. Sarfraz, V. Sharma, and R. Stiefelhagen, "Efficient parameter- free clustering using first neighbor relations," in Proceedings of the IEEE/CVF conference on computer vision and pattern recognition, 2019, pp. 8934- 8943. [19] P. Khosla, P. Teterwak, C. Wang, A. Sarna, Y. Tian, P. Isola, A. Maschinot, C. Liu, and D. Krishnan, "Supervised contrastive learning," 2021. [Online]. Available: https://arxiv.org/abs/2004.11362 [20] S. G. Mueller, M. W. Weiner, L. J. Thal, R. C. Petersen, C. Jack, W. Jagust, J. Q. Trojanowski, A. W. Toga, and L. Beckett, "The alzheimer's disease neuroimaging initiative," Neuroimaging Clinics, vol. 15, no. 4, pp. 869- 877, 2005. [21] E. Barshan and P. Fieguth, "Stage- wise training: An improved feature learning strategy for deep models," in Feature extraction: modern questions and challenges. PMLR, 2015, pp. 49- 59. [22] Y. Zhu, S. Murali, W. Cai, X. Li, J. W. Suk, J. R. Potts, and R. S. Ruoff, "Graphene and graphene oxide: synthesis, properties, and applications," Advanced materials, vol. 22, no. 35, pp. 3906- 3924, 2010. [23] J. Ashburner and K. J. Friston, "Voxel- based morphometry—the methods," Neuroimage, vol. 11, no. 6, pp. 805- 821, 2000. [24] N. Tzourio- Mazoyer, B. Landeau, D. Papathanassiou, F. Crivello, O. Etard, N. Delcroix, B. Mazoyer, and M. Joliot, "Automated anatomical labeling of activations in spm using a macroscopic anatomical parcellation of the mni mri single- subject brain," Neuroimage, vol. 15, no. 1, pp. 273- 289, 2002. [25] B. McMahan, E. Moore, D. Ramage, S. Hampson, and B. A. y Arcas, "Communication- efficient learning of deep networks from decentralized data," in Artificial intelligence and statistics. PMLR, 2017, pp. 1273- 1282. [26] T. Li, A. K. Sahu, M. Zaheer, M. Sanjabi, A. Talwalkar, and V. Smith, "Federated optimization in heterogeneous networks," Proceedings of Machine learning and systems, vol. 2, pp. 429- 450, 2020. [27] J.- Y. Zhu, T. Park, P. Isola, and A. A. Efros, "Unpaired image- to- image translation using cycle- consistent adversarial networks," in Proceedings of the IEEE international conference on computer vision, 2017, pp. 2223- 2232. [28] D. P. Kingma, "Adam: A method for stochastic optimization," arXiv preprint arXiv:1412.6980, 2014.