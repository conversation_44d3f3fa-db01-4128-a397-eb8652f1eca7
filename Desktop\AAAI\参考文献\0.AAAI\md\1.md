# FedCross: Intertemporal Federated Learning Under Evolutionary Games

Jianfeng Lu $^{1,2}$ , <PERSON> $^{1}$ , <PERSON><PERSON><PERSON> $^{3*}$ , <PERSON><PERSON><PERSON> $^{1}$ , <PERSON> $^{4}$ , <PERSON><PERSON> Fu $^{4}$

$^{1}$ School of Computer Science and Technology, Wuhan University of Science and Technology, China   $^{2}$ Key Laboratory of Social Computing and Cognitive Intelligence (Dalian University of Technology), Ministry of Education, China   $^{3}$ School of Computer Science and Technology, Zhejiang Normal University, China   $^{4}$ Hubei Province Key Laboratory of Intelligent Information Processing and Real- time Industrial System, Wuhan University of Science and Technology, China  {jianfenglu, ravenb7}@wust.edu.cn, <EMAIL>, {shu<PERSON><PERSON><PERSON>, luijing_cs, fuhao}@wust.edu.cn

# Abstract

Federated Learning (FL) mitigates privacy leakage in decentralized machine learning by allowing multiple clients to train collaboratively locally. However, dynamic mobile networks with high mobility, intermittent connectivity, and bandwidth limitation severely hinder model updates to the cloud server. Although previous studies have typically addressed user mobility issue through task reassignment or predictive modeling, frequent migrations may result in high communication overhead. Addressing this challenge involves not only dealing with resource constraints, but also finding ways to mitigate the challenges posed by user migrations. We therefore propose a intertemporal incentive framework, FedCross, which ensures the continuity of FL tasks by migrating interrupted training tasks to feasible mobile devices. FedCross comprises two distinct stages: Specifically, in Stage 1, we address the task allocation problem across regions under resource constraints by employing a multi- objective migration algorithm to quantify the optimal task receivers. Moreover, we adopt evolutionary game theory to capture the dynamic decision- making of users, forecasting the evolution of user proportions across different regions to mitigate frequent migrations. In Stage 2, we utilize a procurement auction mechanism to allocate rewards among base stations, ensuring that those providing high- quality models receive optimal compensation. This approach incentivizes sustained user participation, thereby ensuring the overall feasibility of FedCross. Finally, experimental results validate the theoretical soundness of FedCross and demonstrate its significant reduction in communication overhead.

# Introduction

With the explosive growth of artificial intelligence (AI), concerns regarding privacy and security issues associated with centralized learning have been intensifying (Mothukuri et al. 2021). Federated Learning (FL), introduced by Google (McMahan et al. 2017), marks a paradigm shift in distributed learning, tackling data privacy and communication challenges by decentralizing model training. FL mitigates the risk of private data leakage by allowing multiple clients to perform local training based on a globally shared model, and then sending the updated model parameters to a central server for aggregation. However, frequent communication between clients and servers can lead to significant overhead. Using edge servers, such as base stations, as intermediaries for early aggregation in a hierarchical federated learning (HFL) framework (Xu et al. 2021) is undoubtedly a better choice. Since users participating in model training inevitably consume computational and communication resources (Wang et al. 2021), it is essential to design an effective HFL framework that strategically incentivizes users to contribute high- quality model updates during the training.

Currently, research on incentives in HFL may still be insufficiently comprehensive. On the one hand, in FL, mobile users within a region may not be able to consistently provide services to the current base station, as the number of users in a specific area may not remain constant over an extended period (Zhu et al. 2022). As the migration rate increases, the model accuracy is expected to decline and the convergence speed will decrease, while the associated communication overhead will also increase. Unfortunately, previous researches have largely focused on single issues (Rawashdeh et al. 2021), neglecting the interplay between task migration decisions and resource allocation schemes. Task migration is a factor affecting task continuity, while resource allocation influences task migration decisions. On the other hand, although existing studies have designed appropriate incentives to encourage users to participate in training, the sustainability of the incentives versus the limited budget is again a problem (Yu et al. 2020). Therefore, it is necessary to implement appropriate incentive mechanisms to mitigate these negative impacts.

Addressing the abovementioned issues is highly urgent and most confront three major challenges. (i) Ensuring task continuity during migration. Some participants may leave the coverage area of the current base station at any given moment, leading to interruptions in FL tasks (Liu et al. 2023). Consequently, maintaining task continuity while minimizing migration overhead is a critical challenge to prevent data wastage. (ii) Non- IID data distribution among mobile users.

As numerous users participate in the training process, different data distribution among mobile users arising from diverse local environments may lead to a loss of model accuracy (Wang et al. 2024b). (iii) Incentivizing user participation over time. With resource constraints and limited user rationality, participants may lose interest or be infeasible to continue contributing over a longer period of time. Therefore, we need to ensure the sustainability of the incentives.

Considering the aforementioned issues and challenges, we introduce a spatiotemporal Hierarchical Federated Learning framework (FedCross) that leverages evolutionary game theory and procurement auctions to ensure the continuity of FL tasks during dynamic training. Specifically, we first simulate task migration through binary crossover and polynomial mutation operations among individuals, and perform online task allocation based on each user's communication resources. FedCross innovatively integrates evolutionary game theory to construct a dynamic clustering model. By analyzing the stability of the replicator dynamic equations using replicator dynamic functions, we maximize user participation rates. Additionally, FedCross employs procurement auctions to simulate the trading process, ensuring that base stations providing high- quality model updates receive maximum benefits. Overall, FedCross is an effective solution for addressing user mobility in FL, particularly in highly dynamic networks such as vehicular networks. The main contributions of this paper are as follows:

FedCross considers both users' bounded rationality and resource limitations, which leads to effective task migration strategies and resource allocation schemes, ensuring continuity of FL task training while improving model accuracy under limited communication and computational resources. The dynamic decision- making process of mobile users is modeled as an evolutionary game. A cross- region online mobility algorithm is proposed to allocate resources based on users' channel capacity, addressing the user mobility challenge in FL training. To further incentivize user participation, the transaction between cloud servers and base stations is modeled as a greed- based procurement auction. The optimal set of winners is explored, ensuring fairness for both parties. Numerical simulations were conducted to support our theoretical analysis and verify the validity of the proposed framework, demonstrating its practical effectiveness under various conditions.

# Related Work

Research on Mobility in FL. Due to the characteristics of mobile users and the geographical limitations of regionally deployed base stations, cross- region issues are inevitable in current FL research. However, privacy constraints, such as those outlined by the GDPR (Linden et al. 2018), strictly prohibit research involving users' actual geographical locations. Traditional solutions to this problem typically involve transfer learning, where a model trained in one region is applied to another (Mallick et al. 2021). Nevertheless, in the FL scenario, the heterogeneity of user data across different regions can lead to significant model bias when transferring models. Some studies have further explored this issue. For instance, Liu et al.(2022) segmented the global model in heterogeneous FL scenarios and considered factors such as edge devices' storage capacity and network conditions for migration. In real- world healthcare scenarios, Singh et al.(2024) applied FL for patient data transfer, effectively reducing the average total cost using the minimum cost algorithm (MCA) in FedAvg. Notably, these studies are not applicable in dynamically changing user mobility scenarios. To address this, we propose FedCross to capture users' dynamic cross- region behaviors.

Game- Based Incentive Mechanisms in FL. Many researchers have utilized game theory as the foundation for incentive design, ensuring the effectiveness of the mechanism. Zhao et al.(2023) employed a Stackelberg game model to optimize FL in wireless environments, where the incentive is realized by deriving the optimal reward function for the base station. Tang et al.(2024) modeled the interaction between the server and devices during FL training as a repeated game, using a zero- determinant strategy- based incentive mechanism to achieve incentivization. Similarly, Donahue and Kleinberg(2021) used coalition game theory to calculate the optimal arrangement of users, discussing user incentives by providing the Price of Anarchy bounds between individual incentives and social welfare. Meanwhile, Abou El Houda et al.(2022) proposed a novel framework called FedGame, which constructs a non- cooperative game to incentivize MEC nodes to actively participate in resource competition in the face of IIoT attacks. However, the aforementioned games are not suitable for scenarios where users change their decisions over time. Therefore, some works have turned their attention to evolutionary games. For instance, Hammoud et al.(2020) leveraged evolutionary game theory to maintain the stability of coalition members in the presence of dynamic strategies. However, their centralized mechanism might undermine the independence of user decisions. In contrast, FedCross adopts evolutionary games to predict dynamic decisions across user regions, thereby mitigating frequent user migration behaviors.

Auction in Federated Learning. In FL (auction- based mechanisms), auction theory is typically a crucial tool for incentive mechanism design and game- theoretic analysis. For instance, in the satellite edge cloud domain, Xia, Xu, and Hou(2023) proposed a quantity- dependent double auction incentive mechanism for participating satellites, aiming to achieve efficient resource allocation and fair incentives for participating nodes. Wang et al.(2022) introduced a multi- dimensional reverse auction- based double privacy- preserving incentive mechanism, which addresses the issue of sensitive data leakage through bid obfuscation and task reassignment. Tang and Yu(2024) proposed an efficient large- scale personalizable bidding method for multi- agent auction- based federated learning. Inspired by these works, we adopt a procurement auction to ensure the identification of the maximum number of optimal winners under communication constraints, thereby achieving long- term incentives for users.

![](images/ced3cce81c050ebe597dbdae76cdb572234825569b0fe821b03508de49d6e5f7.jpg)  
Figure 1: The workflow of FedCross framework.

# System Model

# System Setting

In this section, we provide the architecture of FedCross with corresponding details.

Structure of FedCross: In our comprehensive framework, FedCross, the cloud server responsible for aggregating model parameters is denoted by  $\mathcal{L}$ . Additionally, there exists a set of Base Stations (BS), represented as  $\mathcal{B}_S = \{1,\dots ,b_s,\dots ,B_s\}$ , which are located in various regions with good mobile network coverage. Within these corresponding regions are mobile users, denoted by  $\mathcal{N} = \{1,\dots ,n,\dots ,N\}$ . Without loss of generality, we assume that each user  $(n\in \mathcal{N})$  can only establish a connection with one BS  $(b_{s}\in \mathcal{B}_{f})$  and uses their smart devices to interactively train a globally shared FL model using their local datasets.

Workflow of FedCross: As illustrated in Fig.1, each round of FL training in FedCross consists of four stages:

(1) Formation of Regions: The process of region formation is conceptualized as an evolutionary game, wherein users make decisions based on the rewards offered by BSs and their respective mobility strategies. Through multiple iterations of evolution, optimal strategies gradually emerge, leading to a stable clustering of users.

(2) Mobility-Based Local Training via Migration Algorithm: Users participate in the training of the global model using their local data. The tasks of users leaving the region are recorded in an online queue, where receiving users are selected based on the resource requirements of these tasks for intra-region migration. The aggregation of updated gradient parameters is handled by the BS, which then transmits the merged parameters to the cloud server for integration.

(3) Greedy-Based Procurement Auction: The cloud server collects local model accuracy and bids which provided by the BSs and employs a greedy algorithm to select the winning combination. Subsequently, the actual payment required from the winning BSs is determined based on a critical value rule.

(4) Aggregation and Distribution: The cloud server then distributes the global model to the winning BSs, which in turn disseminate it to the members within their regions for iterative training on mobile devices. This process is repeated until the training is completed.

# Communication Model

During the training process of FL tasks, communication between mobile users and the base station typically involves two components: the base station propagates the global model, and users upload gradient updates. Given that the transmission power of the base station is significantly higher than that of user devices (Farooq et al. 2023; Hu and Larsson 2023), we do not consider channel allocation behavior and compression errors in the downlink in this paper. However, users face resource constraints in their upload rates (Wang et al. 2024a). Therefore, it is necessary to design an efficient task allocation scheme for mobile users to mitigate these adverse effects.

We consider a block fading channel model, where the channel coefficients remain constant during each FL iteration. Simultaneously, we employ Orthogonal Frequency Division Multiple Access (OFDMA) (Jiang, Song, and Zhang 2010) to ensure orthogonal resource allocation across different links, thereby preventing interference during users' upload of updated parameters. Both large- scale fading  $\beta_{k}$  and small- scale fading  $h_n(t)$  are considered for the channels between the  $n$ - th mobile users and BSs. Let the transmit power of the  $n$ - th user's device be  $P_{n}(t)\leq P_{n}^{max}$ , and the power of the additive white Gaussian noise (AWGN) be  $\epsilon \sim \mathcal{N}(0,\sigma_w^2)$ . According to the Shannon formula (Shannon 1948), the channel capacity for the  $n$ - th user is given as follows:

$$
Q_{n}(t) = \log_{2}\left(1 + \frac{P_{n}(t)\beta_{n}|h_{n}(t)|^{2}}{\sigma_{w}^{2}}\right), \tag{1}
$$

where  $\beta_{n}$  and  $h_n$  denotes the large- scale and small- scale fading of the channel from the  $n$ - th mobile device to the BS, respectively.

Furthermore, to reduce communication overhead and enhance the training efficiency of each participant, we suggest that user devices employ an appropriate model shifting compression scheme (Li et al. 2022) based on the channel capacity given by (1). For privacy and security considerations, we allow users to add Gaussian noise to the gradient updates, expressed as  $g_t^n = \tilde{g}_t^n +\xi_t^n$ , where  $\tilde{g}_t^n$  is the gradient shift, and  $\xi_t^n$  represents noise drawn from a normal distribution  $N(0,\sigma_P^2 I)$ . This perturbation reduces the model's reliance on individual users and makes data recovery more challenging for potential attackers. During each iteration, participants compress their gradient estimates using a compression operator  $C:\mathbb{R}^d\to \mathbb{R}^d$ , resulting in a compressed vector  $v_{t}^{i} = C(\tilde{g}_{t}^{i})$ , which is then sent to the base station. Finally, the cloud server aggregates all compressed vectors to update the global model.

# Online Migrate Strategies For Cross Areas

Trigger migration. During local model training, the task status of mobile users may change. When they move out of the current area, the BS is unable to receive model parameters due to geographical constraints. To ensure the continuity of FL training and further enhance model accuracy, inspired by (Shi et al. 2023), we employ a resource- constrained migration algorithm to facilitate cross- regional training for mo

bile users. For users with bounded rationality, we posit that they will trigger the migration process when they cannot achieve reasonable returns in the current area.

In such cases, users preparing to leave will terminate their tasks prematurely and send updated parameters to the online queue to obtain prior training rewards. However, due to the early termination, the training quality is relatively lower, resulting in the offline users receiving fewer rewards. Nonetheless, they still receive some compensation to ensure their efforts are not wasted. The online queue will poll and execute the migration algorithm, dynamically reallocating the interrupted tasks to other users within the current area to ensure that data is not wasted.

Migration process. Algorithm 1 illustrates the task migration process within the online queue. Our migration strategy is built upon the foundation of a genetic algorithm. In each iteration, two individuals are randomly selected from the population  $P$  and based on their dominance relationship, one is added to the mating pool  $P_t^{m}$ . Each individual in the mating pool then undergoes crossover and mutation operations, generating new offspring. The parent and offspring individuals are combined and undergo non- dominated sorting. Through environmental selection, the non- dominated fronts that meet the size constraints are added to the next generation population  $P_{t + 1}$ . Subsequently, task migration is performed based on the available resources of each user. We calculate the channel capacity  $Q_{n}(t)$  for each user and decide on task assignment based on this capacity. If a user's channel capacity meets the task requirements, the task is assigned to that user. The computational overhead in this part mainly comes from the non- dominated sorting step, which has a complexity of  $O(N^2)$  per iteration for  $N$  devices. To address this, we employ parallel execution of selection, crossover, and mutation during the migration phase, and experimental results confirm the effectiveness of this optimization. Finally,  $P_{t + 1}$  is set as the new population  $P$  for the next iteration. Upon reaching the maximum number of iterations  $t_{\mathrm{max}}$ , the final population  $P_{t_{\mathrm{max}}}$  is returned as the result.

# Theoretical Analysis of FedCross

# Stage 1: Evolutionary Games

We model the dynamic behavior of users moving in different areas as an evolutionary game(Luo et al. 2022) as follows:

Definition 1. An evolutionary game for mobile user selection can be described as a four- tuple  $(\mathcal{N},S,\mathbf{X},\mathcal{U})$  where  $\mathcal{N}$  is the set of users,  $S$  is the strategy space,  $\mathbf{X}$  is the population state, and  $\mathcal{U}$  is the utility function.

At the initial stage, users randomly enter a region based on their needs, forming an initial population. In the evolutionary game model, each mobile user dynamically adjusts their strategy according to their utility. Throughout the strategy evolution process, users with bounded rationality continuously undergo trial and error, gradually changing their strategies over time. They decide whether to continue uploading model parameters to the base station in the current region or migrate the FL task to stop losses in a timely manner by calculating the mobile payoffs.

Utility Function and Replicator Dynamics. The base station determines the reward based on the model accuracy uploaded by the users, which in turn affects a portion of the users' utility. The strategy adaptation process and the corresponding training strategy evolution can be modeled and analyzed using replicator dynamics (Han et al. 2022), which are a set of ordinary differential equations.

Algorithm 1: Online Migrate Strategies For Cross Areas  

<table><tr><td>Input: Initial population P = {P1,P2,...,PN},
Online task queue T
Output: Final population Pmax</td><td></td><td></td></tr><tr><td>1 for t = 1 to tmax do</td><td></td><td></td></tr><tr><td>2</td><td>Pm = ∅ for i = 1 to N do</td><td></td></tr><tr><td>3</td><td>x1 = RandomSelect(P)</td><td></td></tr><tr><td>4</td><td>x2 = RandomSelect(P) if x1 dominates x2 then
    Pm = Pm ∪ {x1}</td><td></td></tr><tr><td>5</td><td>else</td><td></td></tr><tr><td>6</td><td>Pm = Pm ∪ {x2}</td><td></td></tr><tr><td>7</td><td>Qm = ∅ for individual i ∈ Pm do
    child = SBX(Pm[i]) mutant = PM(child)</td><td></td></tr><tr><td>8</td><td>Qm = Qm ∪ {mutant}</td><td></td></tr><tr><td>9</td><td>Z = P ∪ Qm</td><td></td></tr><tr><td>10</td><td>Pt+1 = ∅ F = NonDominatedSorting(Z) for each front Fh ∈ F do</td><td></td></tr><tr><td>11</td><td>if size of Fh ≤ N - |Pt+1| then
    Pt+1 = Pt+1 ∪ Fh</td><td></td></tr><tr><td>12</td><td>for each task Tj ∈ T do</td><td></td></tr><tr><td>13</td><td>for each user u ∈ Pt+1 do</td><td></td></tr><tr><td>14</td><td>if Ck (based on Eq.1) is sufficient for Tj then</td><td></td></tr><tr><td>15</td><td>Assign Tj to user u break</td><td></td></tr><tr><td>16</td><td>17</td><td>P = Pt+1</td></tr></table>

We use  $u_{b_s}$  to represent the utility obtained by the user from the BS  $b_{s}$ , i.e.,

$$
u_{b_s}(x_b) = R_{n,b_s}\frac{x_{n,b_s}M_n}{\sum_{b_s = 1}^{B_s}x_{n,b_s}M_n} -\xi Q_n, \tag{2}
$$

where  $R_{n,b_s}$  represents the reward allocation held by BS  $b_{s}$ $M_{n}$  denotes the data volume of user  $n$ $Q_{n}$  denotes the channel capacity as previously mentioned, and  $\frac{x_{n,b_s}M_n}{\sum_{b_s = 1}^{B_s}x_{n,b_s}M_n}$  represents the reward share based on the data contribution of the mobile user, while  $\xi$  is the per- unit training cost. Additionally, we assume that  $U(\cdot)$  is a linear utility function representing the risk neutrality of data owners without loss of generality (Lim et al. 2021a). The net utility that users in the area of BS  $b_{s}$  can obtain at time  $t$  is given as follows:

$$
u_{b_s}(x_{b_s}(t)) = R_{n,b_s}\frac{x_{n,b_s}(t)\mathcal{M}_n}{\sum_{b_s = 1}^{B_s}x_{n,b_s}(t)\mathcal{M}_n} -\xi Q_n(t). \tag{3}
$$

Accordingly, the average utility of the area with user  $b$  as the BS is:

$$
\overline{u} (x_{b_s})(t) = \sum_{b_s = 1}^{B_s}u_{b_s}(x_{b_s})(t)x_{b_s}(t). \tag{4}
$$

After multiple generations of evolution, a steady- state strategy, known as an Evolutionarily Stable Strategy (ESS)(Zhu, Hossain, and Niyato 2013), will emerge as the solution. This is a more successful strategy that propagates over time. When the entire system reaches an ESS state, the population proportions of different species will be in a stable condition. Correspondingly, the replicator dynamic equation is given by:

$$
\dot{x}_{b_s}(t) = y_{b_s}(x(t)) = \Delta x_{b_s}(t)(u_{b_s} - \overline{u}),\forall b_s\in \mathcal{B}_s, \tag{5}
$$

where  $\Delta$  refers to the learning rate of strategy adaptation.

Theoretical Analysis of Replicator Dynamics. We provide a theoretical analysis of the evolution of the proportion of the population in a region as a result of user mobility.

Lemma 1 The first- order derivatives of  $y_{b_s}(x(t))$  with respect to  $x_{\hat{b}_s(t)}$  are bounded for all  $\hat{b}_s\in B_s$

Theorem 1 For any initial condition  $x(0)\in X$  there exists a unique and stable evolutionary equilibrium to the dynamics defined in Equation (5).

Consequently, we demonstrate that within FedCross, areas modeled under evolutionary game theory can achieve dynamic stability. Even if users cross different areas for varied factors, FedCross can continually replicate successful strategies, leading back to a stable state.

# Stage 2: Greedy-Based Procurement Auction

Auction Design. Inspired by Pang et al.(2022) and Le et al.(2021a), we propose a greedy- based procurement auction to model the allocation between BSs and cloud servers. Existing works has explored solutions to the allocation problem, such as deep learning- based auctions (Lim et al. 2021b) and greedy policy auctions (Zeng et al. 2020). Unlike the above, the scenarios in this paper take into account the transaction process between BSs and servers in a cross- regional environment, realising the incentives for the participants.

Allocation Rule. First, we need to determine the set of winning base stations. To ensure fairness, we require that at least  $K$  base stations are selected in each round, and each base station can be selected at most once per round. Based on the training within regions from the previous phase, we take into account the communication overhead in each region. Regions with lower resource consumption are prioritized, as this indicates higher training efficiency of the users within the region (Luo et al. 2021).

The model quality obtained by mobile users training within the region and the valuation cost of the base station together form the bid price  $\mathrm{Bid}_{b_s,j}$  . Thus, our allocation rules can be formalized as follows:

$$
\begin{array}{rl} & {\min \sum_{b_s\in B_f}\sum_{j\in J}\mathbf{B}\mathrm{id}_{b_s,j}\cdot x_{b_s,j}}\\ & {\quad \quad \quad \left\{ \begin{array}{ll}\sum_{b_s\in B_f}y_{b_s}(t)\geq K,\quad \forall j\in J,\\ T_g\geq \frac{1}{1 - \max_{b_s,j}\mathrm{A}\mathrm{c}\mathrm{c}\mathrm{u}_{b_s,j}} x_{b_s,j}, \end{array} \right.}\\ & {\quad \quad \quad \quad \left\{ \begin{array}{ll} \sum_{j\in J}x_{b_s,j}\leq 1,\quad \forall b_s\in B_f,\\ x_{b_{s},j}\leq \frac{t_{\mathrm{cmp}} + Q_b(t) / \eta}{t_{\mathrm{max}}}, \end{array} \right.} \end{array} \tag{6}
$$

For the aforementioned social cost minimization problem, we solve it using a greedy algorithm (Seo, Niyato, and Elmroth 2022). Here,  $T_{g}$  is the global iteration count calculated based on the maximum model accuracy provided by the selected base stations,  $\mathrm{Accur}_{b_s,j}$  is the model accuracy provided by the  $j$  - th bid of base station  $b_{s}$ $Q_{n}(t)$  is the channel capacity as previously described, and  $y_{b_s}(t)$  is a binary variable that indicates whether the  $\mathrm{BS}_{b_s}$  is selected during the global iteration  $t$  Specifically,  $y_{b_s}(t) = 1$  if client  $i$  is selected in iterationt; otherwise,  $y_{b_s}(t) = 0$

Payment Rule. To ensure individual rationality and incentive compatibility of the auction mechanism, we calculate the payments based on the critical bid using the critical value rule Archer and Tardos 2001).We determine the payment for each newly selected schedule by computing it relative to the critical bid. The process of finding the critical bid involves first identifying the bid with the second smallest average cost among all feasible bids and then calculating the payment for each selected schedule based on the critical value rule. Then, the algorithm design of the whole auction is shown as follows:

Algorithm 2 addresses the base station selection problem under multiple constraints Eq.(6) using a greedy strategy to minimize the payment cost. We initialize an empty set  $S$  to store selected base stations and iterate until the number of selected base stations meets or exceeds the predetermined minimum  $K$  . In each iteration, we select a base station- task combination  $(b_{s},j)$  from the bidding set  $J^{T_g}$  that satisfies all constraints and has the lowest cost. Once identified, the combination is added to  $B_{S}$  and removed from  $J^{T_g}$  to prevent duplicates. In the payment rule section, we compute the payment for each selected base station by finding its critical bid. Finally, we return the selected base stations  $B_{S}$  and their payments  $P$

The complexity analysis reveals that the main complexity arises from the search for the lowest- cost base station that meets the constraints. In the worst case, this phase has complexity  $O(K\times n)$  ,where  $K$  is the minimum number of base stations and  $n$  is the size of the bidding set. Each iteration requires  $O(n)$  to find the lowest- cost base station, and up to  $K$  base stations must be selected, leading to a total complexity of  $O(K\times n)$  . The payment rule phase also has complexity  $O(K\times n)$  , as finding the critical bid for each base station takes  $O(n)$  time. Therefore, the overall time complexity is  $O(K\times n)$  , meaning the runtime is linearly dependent on  $K$

Algorithm 2: Optimized Base Station Selection with Payment CalculationInput: Set of qualified bids  $J^{T_g}$ , utility increment  $R_{il}(S)$ , number of global iterations  $T_g$ , minimum number of base stations  $K$ , communication time  $t_{\mathrm{cmp}}$ , channel capacity  $Q_{n}(t)$ , maximum allowable time  $t_{\mathrm{max}}^{b_s}$ Output: Set of selected base stations  $B_S$  and payments  $P$ 1 Initialize an empty set  $S$  for selected base stations;2 while  $|S| < K$  do3 Find the bid  $(b_{s}^{*}, j^{*})$  in  $J^{T_g}$  with the minimum cost satisfying:  $T_g \geq \frac{1}{1 - \mathrm{Accuracy}, j^{*}}$  and  $\frac{t_{\mathrm{cmp}} + Q_n(t) / \eta}{t_{\mathrm{max}}^{b_{s}^{*}}} \geq 1$ Add base station  $b_{s}^{*}$  to the set  $S, S \cup \{b_{s}^{*}\}$ ;4 Set  $x_{b_{s}^{*}j^{*}} \leftarrow 1$ ;5 Remove  $(b_{s}^{*}, j^{*})$  from  $J^{T_g}$ ;6 foreach  $b_{s}$  in  $B_S$  do7 Find the critical bid  $(b_{s}, j_{0})$  such that:  $(b_{s}, j_{0}) = \arg \min_{(b_{s}, j) \in J^{T_g} \setminus S} \left\{\frac{r_{b_{s}, j}}{R_{b_{s}, j}(S)} \right\}$ Calculate the payment for the selected bid  $(b_{s}^{*}, j^{*})$  using the critical value rule:  $p_{b_{s}^{*}} = R_{b_{s}, j^{*}}(S) - \frac{r_{b_{s}, j_{0}}}{R_{b_{s}, j_{0}}(S)} R_{b_{s}, j^{*}}(S)$ Set the payment  $o_{b_{s}^{*}}$  for the base station  $b_{s}^{*}$ ;8 return Set of selected base stations  $B_S$  and payments  $P$ ;

and  $n$

Property Analysis. Based on the above, we analyze the properties of FedCross by the following theorems.

Theorem 1. The payment rule satisfies both individual rationality and incentive compatibility. Specifically, each bidder's payment ensures that their utility is non- negative, i.e.,  $v_{b_s} \geq \theta_{b_s}$ , and a bidder with a bogus bid  $B_{id_{b_s}} \neq v_{b_s}$  does not gain more utility compared to bidding truthfully  $B_{id_{b_s}} = v_{b_s}$ .

The above theorems ensure that, regardless of the behavior of other BSs, honest bidding remains the optimal strategy for each BS in the auction. All theoretical proofs in the paper are available in the appendix.

# Experiment

# Experimental Setups

Baselines. We compared with the following methods:

BasicFL: This baseline overlooks user migration and incentive mechanisms, representing a basic FL framework

Table 1: Simulation Parameters  

<table><tr><td>Parameters</td><td>Simulated Values</td></tr><tr><td>Total number of Servers</td><td>10</td></tr><tr><td>Total number of Areas</td><td>[2, 3]</td></tr><tr><td>Total number of users</td><td>[50, 300]</td></tr><tr><td>Congestion coefficient</td><td>10</td></tr><tr><td>Reward</td><td>[600, 900]</td></tr><tr><td>Momentum</td><td>(0, 0.9)</td></tr></table>

similar to (He et al. 2023), where training assumes an ideal environment.

SAVFL: Katal, Bajoria, and Sethi(2021) uses simulated annealing to optimize virtual machine migration, focusing on the optimal path and strategy. However, it failed to address frequent migrations, unlike the FedCross.- WCNFL: Le et al.(2021b) Introduces a reverse auction incentive mechanism, allowing service providers to select cost- effective devices within budget, enhancing global model performance and user participation.

Implementation. We use PyTorch (Paszke et al. 2019) to implement FedCross and the other baselines. The simulation data is shown Table 1.

# Experimental Results

We provide the population proportion evolution resulting from regional user migration strategies under different scenarios, demonstrate the applicability of user migration processes under a multi- objective evolutionary algorithm, and assess the role of the auction algorithm in the base station selection process, thus validating the theoretical correctness.

Evolution of migration. In Fig.2(a), we first present the overall distribution of the three areas in a two- dimensional state. Given that dynamic changes may occur throughout the training process, we selected a specific time segment for analysis to demonstrate that the proposed framework can achieve dynamic equilibrium under theoretical Eq.(5) support. When the user distribution proportions in the three areas are  $18\%$ ,  $32\%$ , and  $50\%$ , respectively, the blue curve exhibits an upward trend. This indicates that when mobile users migrate and enter this area, the current base station can attract more users to continue their training in this area. It is important to emphasize that this explanation does not imply that a large number of users choose to train in this area. Such an assumption would be unreasonable, as users' migration decisions are influenced by various external factors, including geographical location and reward levels. Instead, this proportion refers to the percentage of migrating users who are willing to continue their training in the current area. This phenomenon can reduce the frequency of users exiting the training in the current area, thereby lowering the communication overhead in frequent migrations.

In contrast, the green curve shows a significant decline, indicating that the incentives and migration compensation provided by the current base station fail to offset the communication costs, leading users to abandon further training in this area. The red curve initially rises slightly, then drops

![](images/5e08540dc81150111e3837701a531c65233e1c85a84e99af560564ea7389e864.jpg)  
Figure 2: FedCross under different baselines.

![](images/aa673e7ad3aeab558641d47f9cbe863db50a3e44108f47be9e7d85d936fee8fa.jpg)  
Figure 3: Auction impact.

rapidly, followed by a slight increase, and eventually stabilizes. This suggests that dynamic stability has been reached within this area. After time exceeds 300, the vertical axis reveals that the training proportions across the areas tend to stabilize. Again, this does not imply that users cease migrating. Stability here is a dynamic concept, designed to demonstrate the game- theoretic equilibrium we aim to establish.

In reference to Fig.2(b), it further substantiates our previous explanation based on Fig.2(a). The three areas corresponding to the blue curve have proportions of [0.25, 0.35, 0.4], while the red curve corresponds to [0.3, 0.4, 0.5], and the green curve corresponds to [0.15, 0.25, 0.35]. It is evident that as time progresses, the system's final state converges consistently (though this does not imply that the user proportions within each area are identical).

Migration process under algorithms. In Fig.2(c), we present the task allocation results under different baselines. Specifically, BasicFL represents an ideal scenario that neglects user mobility, and therefore employs random search as a simulation method. It can be observed that, due to the lack of a clear optimization direction, the yellow curve fails to significantly improve the task allocation performance, resulting in a relatively poor overall outcome. In contrast, SAVFL explores different solutions in the initial phase; however, due to the early exploration of suboptimal solutions, its initial performance is subpar. As the solution space gradually narrows, the algorithm converges and ultimately achieves superior performance, although this convergence requires a significant number of iterations. Finally, the mi gration algorithm we propose, by comprehensively considering multiple objectives such as resource overhead and fairness loss, is able to quickly update and return a more optimal task allocation scheme with fewer iterations.

Incentive effects of auctions. Fig.2(d) and Fig.3(a), we illustrate the role of the auction in the second phase of our framework. Fig.2(d) demonstrates the incentivization effect of the auction on user participation, with an increased number of base stations to more clearly reveal this effect. As shown in the figure, users are more inclined to participate in training when tangible rewards are offered by the base stations. In Fig.3(a), we compare the payment costs incurred under different allocation rules during the auction phase. BasicFL, which follows a traditional auction allocation rule, results in higher payment costs. WCNFL employs a reverse auction mechanism to select winners; however, its optimization effect on Eq.6 is still inferior to that of FedCross.

In Fig.3(b), we demonstrate the effectiveness of the threshold- based payment algorithm. In contrast, although the non- payment algorithm (depicted by the red curve) occasionally shows a downward trend under the same payment costs, the overall user payment cost exhibits unstable growth. This high degree of fluctuation may lead to a decline in user trust, as the rewards provided by the base station are highly unpredictable, making it difficult to anticipate future reward amounts. On the other hand, the payment algorithm (represented by the blue curve) exhibits a more stable and lower payment cost, which not only helps maintain consistent user participation but also ensures the accuracy of the models provided by the base station, thereby positively impacting the overall system performance.

# Conclusion

We have introduced FedCross, an intertemporal incentive framework that addresses FL resource allocation and task continuity in mobile user scenarios, while ensuring improved model accuracy through incentivized user participation. FedCross employs a multi- objective migration algorithm for continuous task training, uses evolutionary game theory to model dynamic user decisions, and leverages procurement auctions to reward base stations for high- quality updates, encouraging sustained participation. Finally, experimental results demonstrate its effectiveness.

# Acknowledgements

AcknowledgementsThis work was supported in part by the National Natural Science Foundation of China under Grants 62372343, 62272417, 62402352, and 62072411, in part by the Zhejiang Provincial Natural Science Foundation of China under Grant LR21F020001, in part by the Key Research and Development Program of Hubei Province under Grant 2023BEB024, and in part by the Open Fund of Key Laboratory of Social Computing and Cognitive Intelligence (Dalian University of Technology), Ministry of Education under Grant SCCI2024TB02.

# References

ReferencesAbou El Houda, Z.; Brik, B.; Ksentini, A.; Khoukhi, L.; and Guizani, M. 2022. When federated learning meets game theory: A cooperative framework to secure iiot applications on edge computing. IEEE Transactions on Industrial Informatics, 18(11): 7988- 7997. Archer, A.; and Tardos, E. 2001. Truthful mechanisms for one- parameter agents. In Proceedings 42nd IEEE Symposium on Foundations of Computer Science, 482- 491. IEEE. Donahue, K.; and Kleinberg, J. 2021. Optimality and stability in federated learning: A game- theoretic approach. Advances in Neural Information Processing Systems, 34: 1287- 1298. Farooq, M.; Vu, T. T.; Ng, H. Q.; and Tran, L.- N. 2023. Massive MIMO for serving federated learning and non- federated learning users. IEEE Transactions on Wireless Communications, 23(1): 247- 262. Hammoud, A.; Mourad, A.; Otrok, H.; Wahab, O. A.; and Harmanani, H. 2020. Cloud federation formation using genetic and evolutionary game theoretical models. Future Generation Computer Systems, 104: 92- 104. Han, Y.; Niyato, D.; Leung, C.; Miao, C.; and Kim, D. I. 2022. Dynamics in coded edge computing for iot: A fractional evolutionary game approach. IEEE Internet of Things Journal, 9(15): 13978- 13994. He, Y.; Luo, M.; Wu, B.; Sun, L.; Wu, Y.; Liu, Z.; and Xiao, K. 2023. A game theory- based incentive mechanism for collaborative security of federated learning in energy blockchain environment. IEEE Internet of Things Journal, 10(24): 21294- 21308. Hu, Z.; Chen; and Larsson, E. G. 2023. Scheduling and aggregation design for asynchronous federated learning over wireless networks. IEEE Journal on Selected Areas in Communications, 41(4): 874- 886. Jiang, T.; Song, L.; and Zhang, Y. 2010. Orthogonal frequency division multiple access fundamentals and applications. CRC Press. Katal, A.; Bajoria, V.; and Sethi, V. 2021. Simulated annealing based approach for virtual machine live migration. In 2021 8th International Conference on Smart Computing and Communications (ICSCC), 219- 224. IEEE. Le, T. H. T.; Tran, N. H.; Tun, Y. K.; Nguyen, M. N.; Pandey, S. R.; Han, Z.; and Hong, C. S. 2021a. An incentive mechanism for federated learning in wireless cellular networks:

An auction approach. IEEE Transactions on Wireless Communications, 20(8): 4874- 4887. Le, T. H. T.; Tran, N. H.; Tun, Y. K.; Nguyen, M. N.; Pandey, S. R.; Han, Z.; and Hong, C. S. 2021b. An incentive mechanism for federated learning in wireless cellular networks: An auction approach. IEEE Transactions on Wireless Communications, 20(8): 4874- 4887. Li, Z.; Zhao, H.; Li, B.; and Chi, Y. 2022. SoteriaFL: A unified framework for private federated learning with communication compression. Advances in Neural Information Processing Systems, 35: 4285- 4300. Lim, W. Y. B.; Huang, J.; Xiong, Z.; Kang, J.; Niyato, D.; Hua, X.- S.; Leung, C.; and Miao, C. 2021a. Towards federated learning in uav- enabled internet of vehicles: A multidimensional contract- matching approach. IEEE Transactions on Intelligent Transportation Systems, 22(8): 5140- 5154. Lim, W. Y. B.; Ng, J. S.; Xiong, Z.; Jin, J.; Zhang, Y.; Niyato, D.; Leung, C.; and Miao, C. 2021b. Decentralized edge intelligence: A dynamic resource allocation framework for hierarchical federated learning. IEEE Transactions on Parallel and Distributed Systems, 33(3): 536- 550. Linden, T.; Khandelwal, R.; Harkous, H.; and Fawaz, K. 2018. The privacy policy landscape after the GDPR. arXiv preprint arXiv:1809.08396. Liu, F.; Yu, H.; Huang, J.; and Taleb, T. 2023. Joint service migration and resource allocation in edge IoT system based on deep reinforcement learning. IEEE Internet of Things Journal, 11: 11341- 11352. Liu, J.; Xu, Y.; Xu, H.; Liao, Y.; Wang, Z.; and Huang, H. 2022. Enhancing federated learning with intelligent model migration in heterogeneous edge computing. In 2022 IEEE 38th International Conference on Data Engineering (ICDE), 1586- 1597. IEEE. Luo, B.; Li, X.; Wang, S.; Huang, J.; and Tassiulas, L. 2021. Cost- effective federated learning design. In IEEE INFOCOM 2021- IEEE Conference on Computer Communications, 1- 10. IEEE. Luo, X.; Zhang, Z.; He, J.; and Hu, S. 2022. Strategic analysis of the parameter servers and participants in federated learning: An evolutionary game perspective. IEEE Transactions on Computational Social Systems, 11(1): 132- 143. Mallick, T.; Balaprakash, P.; Rask, E.; and Macfarlane, J. 2021. Transfer learning with graph neural networks for short- term highway traffic forecasting. In 2020 25th International Conference on Pattern Recognition (ICPR), 10367- 10374. IEEE. McMahan, B.; Moore, E.; Ramage, D.; Hampson, S.; and y Arcas, B. A. 2017. Communication- efficient learning of deep networks from decentralized data. In Artificial intelligence and statistics, 1273- 1282. PMLR. Mothukuri, V.; Parizi, R. M.; Pouriyeh, S.; Huang, Y.; Dehghantanha, A.; and Srivastava, G. 2021. A survey on security and privacy of federated learning. Future Generation Computer Systems, 115: 619- 640.

Pang, J.; Yu, J.; Zhou, R.; and Lui, J. C. 2022. An incentive auction for heterogeneous client selection in federated learning. IEEE Transactions on Mobile Computing, 22(10): 5733- 5750. Paszke, A.; Gross, S.; Massa, F.; Lerer, A.; Bradbury, J.; Chanan, G.; Killeen, T.; Lin, Z.; Gimelshein, N.; Antiga, L.; et al. 2019. Pytorch: An imperative style, high- performance deep learning library. Advances in neural information processing systems, 32: 8026- 8037. Rawashdeh, M.; Al Zamil, M. G.; Samarah, S. M.; Obaidat, M.; and Masud, M. 2021. IOT- based service migration for connected communities. Computers & Electrical Engineering, 96: 107530. Seo, E.; Niyato, D.; and Elmroth, E. 2022. Resource- efficient federated learning with non- iid data: An auction theoretic approach. IEEE Internet of Things Journal, 9(24): 25506- 25524. Shannon, C. E. 1948. A mathematical theory of communication. The Bell system technical journal, 27(3): 379- 423. Shi, Z.; Zhao, T.; Li, Q.; Zhang, Z.; and Cui, Z. 2023. Workflow migration in uncertain edge computing environments based on interval many- objective evolutionary algorithm. Egyptian Informatics Journal, 24(4): 100418. Singh, H.; Pratap, A.; Yadav, R. N.; and Das, D. 2024. Loss Aware Federated Learning for Service Migration in Multimodal E- Health Services. IEEE Transactions on Services Computing, 17: 2571- 2582. Tang, C.; Yang, B.; Xie, X.; Chen, G.; Al- Qaness, M. A.; and Liu, Y. 2024. An Incentive Mechanism for Federated Learning: A Continuous Zero- Determinant Strategy Approach. IEEE/CAA Journal of Automatica Sinica, 11(1): 88- 102. Tang, X.; and Yu, H. 2024. Efficient Large- Scale Personalizable Bidding for Multi- Agent Auction- Based Federated Learning. IEEE Internet of Things Journal, 11: 26518- 26530. Wang, D.; Ren, J.; Wang, Z.; Wang, Y.; and Zhang, Y. 2022. Privaim: A dual- privacy preserving and quality- aware incentive mechanism for federated learning. IEEE Transactions on Computers, 72(7): 1913- 1927. Wang, H.; Jia, Y.; Zhang, M.; Hu, Q.; Ren, H.; Sun, P.; Wen, Y.; and Zhang, T. 2024a. FedDSE: Distribution- aware Submodel Extraction for Federated Learning over Resource- constrained Devices. In Proceedings of the ACM on Web Conference 2024, 2902- 2913. Wang, H.; Qu, Z.; Guo, S.; Wang, N.; Li, R.; and Zhuang, W. 2021. LOSP: Overlap synchronization parallel with local compensation for fast distributed training. IEEE Journal on Selected Areas in Communications, 39(8): 2541- 2557. Wang, H.; Xu, H.; Li, Y.; Xu, Y.; Li, R.; and Zhang, T. 2024b. FedCDA: Federated Learning with Cross- rounds Divergence- aware Aggregation. In 2024 12th International Conference on Learning Representations (ICLR). OpenReview. Xia, Q.; Xu, Z.; and Hou, Z. 2023. Incentive Mechanism Based on Double Auction for Federated Learning in Satellite Edge Clouds. In 2023 19th International Conference on Mobility, Sensing and Networking (MSN), 660- 668. IEEE.

Xu, B.; Xia, W.; Wen, W.; Liu, P.; Zhao, H.; and Zhu, H. 2021. Adaptive hierarchical federated learning over wireless networks. IEEE Transactions on Vehicular Technology, 71(2): 2070- 2083. Yu, H.; Liu, Z.; Liu, Y.; Chen, T.; Cong, M.; Weng, X.; Niyato, D.; and Yang, Q. 2020. A fairness- aware incentive scheme for federated learning. In Proceedings of the AAAI/ACM Conference on AI, Ethics, and Society, 393- 399. Zeng, R.; Zhang, S.; Wang, J.; and Chu, X. 2020. Fmore: An incentive scheme of multi- dimensional auction for federated learning in mec. In 2020 IEEE 40th international conference on distributed computing systems (ICDCS), 278- 288. IEEE. Zhao, H.; Zhou, M.; Xia, W.; Ni, Y.; Gui, G.; and Zhu, H. 2023. Economic and energy- efficient wireless federated learning based on stackelberg game. IEEE Transactions on Vehicular Technology, 73: 2995- 2999. Zhu, K.; Hossain, E.; and Niyato, D. 2013. Pricing, spectrum sharing, and service selection in two- tier small cell networks: A hierarchical dynamic game approach. IEEE Transactions on Mobile Computing, 13(8): 1843- 1856. Zhu, Y.; Ye, Y.; Liu, Y.; and James, J. 2022. Cross- area travel time uncertainty estimation from trajectory data: a federated learning approach. IEEE Transactions on Intelligent Transportation Systems, 23(12): 24966- 24978.