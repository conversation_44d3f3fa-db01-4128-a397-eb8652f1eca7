# Experiments

In this section, we examine the performance of TrustGuard. First, we compare the optimal multi-dimensional trustworthiness coupling discovered by TrustGuard with the state-of-the-art baseline methods. Second, we evaluate the effectiveness of cross-modal cascade threat defense under different medical data heterogeneity scenarios to validate TrustGuard's robustness and adaptability.

在本节中，我们检验TrustGuard的性能。首先，我们将TrustGuard发现的最优多维度可信性耦合与最先进的基线方法进行比较。其次，我们评估在不同医疗数据异构性场景下跨模态级联威胁防御的有效性，以验证TrustGuard的鲁棒性和适应性。

## Experiment Settings

Datasets. To validate the efficacy of our proposed TrustGuard methodology, we conducted simulations of medical multimodal federated learning scenarios utilizing our constructed Med-CM-Syn dataset and three widely-recognized medical datasets, namely, MIMIC-III, eICU-CRD, and PhysioNet-2019. The Med-CM-Syn dataset contains 15,000 medical cases with cross-modal cascade attack chains, covering 5 medical modalities (imaging, text, time-series, genomics, and clinical records) across 8 medical institutions. The partitioning of each dataset into $N$ clients employs two distinct medical data distribution strategies. First, we implemented a balanced medical distribution scheme, ensuring that each client possesses an approximately equal distribution across medical specialties and patient demographics, achieved through medical-aware Dirichlet distribution sampling with $p_k\sim \mathrm{Dir}N$ $(\beta = 8.0)$. In contrast, a heterogeneous medical distribution approach was employed by simulating real-world medical institution scenarios with $p_k\sim \mathrm{Dir}N$ $(\beta = 0.3)$ and allocating different medical specialties and patient populations to $N$ clients. The depicted difference in medical data distribution is illustrated in Figure 2, where a lower $\beta$ value corresponds to a more pronounced medical specialty imbalance.

数据集。为了验证我们提出的TrustGuard方法的有效性，我们使用构建的Med-CM-Syn数据集和三个广泛认可的医疗数据集（即MIMIC-III、eICU-CRD和PhysioNet-2019）进行了医疗多模态联邦学习场景的仿真。Med-CM-Syn数据集包含15,000个医疗案例，具有跨模态级联攻击链，涵盖5种医疗模态（影像、文本、时间序列、基因组学和临床记录），分布在8个医疗机构中。将每个数据集分割为$N$个客户端采用两种不同的医疗数据分布策略。首先，我们实施了平衡的医疗分布方案，确保每个客户端在医疗专科和患者人口统计学方面拥有大致相等的分布，通过医疗感知的Dirichlet分布采样实现，参数为$p_k\sim \mathrm{Dir}N$ $(\beta = 8.0)$。相比之下，异构医疗分布方法通过模拟真实世界医疗机构场景，使用$p_k\sim \mathrm{Dir}N$ $(\beta = 0.3)$并将不同的医疗专科和患者群体分配给$N$个客户端。医疗数据分布的差异如图2所示，其中较低的$\beta$值对应更明显的医疗专科不平衡。

Baselines & Metrics. We compare TrustGuard with 8 traditional trustworthiness methods (i.e., FedAA (differential privacy), SADBA (adversarial training), RobustFL (Byzantine-robust), FairFL (fairness-aware), ExplainFL (explainable AI), PrivacyFL (privacy-preserving), SecureFL (security-focused), and MultiTrust (multi-objective)), and two outstanding multi-dimensional optimization methods, including MOEA-D (multi-objective evolutionary) and NSGA-III (non-dominated sorting). These methods are compared within the FedAvg federated framework. Moreover, we compare TrustGuard with FedTrust (Wang et al. 2023), which is a federated trustworthiness baseline. Additionally, we compare with the state-of-the-art medical FL method (i.e., MedFL (Chen et al. 2023)). To further validate the effectiveness of TrustGuard, we designed several variants and comparative experiments to understand AMTC's coupling decision-making. We constructed three TrustGuard variants to conduct ablation experiments. First, to assess the necessity of personalized coupling recommendations for each client, we designed the TrustGuard-U method, which recommends uniform coupling strategies. Second, to examine the logic behind AMTC's coupling recommendations, we created TrustGuard-R, which randomly assigns coupling weights in every round. Lastly, we explored how TrustGuard selects superior coupling configurations, focusing on whether it leverages adaptive coupling abilities. For this, we developed a static method, TrustGuard-S, which uses fixed coupling weights throughout training. We choose Trustworthiness Coupling Degree (TCD), Multi-dimensional Trustworthiness Score (MTS), Cascade Success Rate (CSR), and Threat Prediction Accuracy (TPA) to measure the performance of all methods.

基线和指标。我们将TrustGuard与8种传统可信性方法（即FedAA（差分隐私）、SADBA（对抗训练）、RobustFL（拜占庭鲁棒）、FairFL（公平感知）、ExplainFL（可解释AI）、PrivacyFL（隐私保护）、SecureFL（安全聚焦）和MultiTrust（多目标））以及两种优秀的多维度优化方法（包括MOEA-D（多目标进化）和NSGA-III（非支配排序））进行比较。这些方法在FedAvg联邦框架内进行比较。此外，我们将TrustGuard与FedTrust（Wang et al. 2023）进行比较，这是一个联邦可信性基线。另外，我们与最先进的医疗FL方法（即MedFL（Chen et al. 2023））进行比较。为了进一步验证TrustGuard的有效性，我们设计了几个变体和对比实验来理解AMTC的耦合决策。我们构建了三个TrustGuard变体来进行消融实验。首先，为了评估为每个客户端个性化耦合推荐的必要性，我们设计了TrustGuard-U方法，推荐统一的耦合策略。其次，为了检验AMTC耦合推荐背后的逻辑，我们创建了TrustGuard-R，在每轮中随机分配耦合权重。最后，我们探索TrustGuard如何选择优秀的耦合配置，重点关注是否利用自适应耦合能力。为此，我们开发了静态方法TrustGuard-S，在整个训练过程中使用固定的耦合权重。我们选择可信性耦合度(TCD)、多维度可信性分数(MTS)、级联成功率(CSR)和威胁预测准确率(TPA)来衡量所有方法的性能。

Implementation Details. In our medical federated learning setting, we set the number of medical institutions $N$ to be [3,5,8,12] respectively, and the total round number to be 150. In particular, we use AMTC as the default coupling mechanism, and we also compared it to traditional optimization methods with coupling temperature $\tau = 0.3$. Besides, we choose the AdamW optimizer with a learning rate of 5e-4. The number of trustworthiness dimensions to 5 (privacy, security, robustness, fairness, explainability). We run all experiments for five random repetitions on an NVIDIA A100 GPU cluster with medical data privacy compliance.

实现细节。在我们的医疗联邦学习设置中，我们将医疗机构数量$N$分别设置为[3,5,8,12]，总轮数为150。特别地，我们使用AMTC作为默认耦合机制，并将其与传统优化方法进行比较，耦合温度$\tau = 0.3$。此外，我们选择AdamW优化器，学习率为5e-4。可信性维度数量为5（隐私性、安全性、鲁棒性、公平性、可解释性）。我们在符合医疗数据隐私合规的NVIDIA A100 GPU集群上对所有实验进行五次随机重复。

![](images/medical_data_distribution.jpg)
Figure 2: Unbalanced medical specialty distribution (non-I.I.D.) for Med-CM-Syn and medical datasets. The horizontal axis represents medical specialties, and the vertical axis represents their corresponding patient numbers. A lower $\beta$ value corresponds to a more pronounced medical specialty imbalance in the distribution.

图2：Med-CM-Syn和医疗数据集的不平衡医疗专科分布（非独立同分布）。横轴表示医疗专科，纵轴表示相应的患者数量。较低的$\beta$值对应分布中更明显的医疗专科不平衡。

## Experiment Results

### Performance under balanced medical distribution

Table 1 shows the results of multi-dimensional trustworthiness scores (i.e., MTS) of our methods and baselines on three medical datasets with different federated settings. Our proposed TrustGuard consistently achieved the highest performance across all metrics, demonstrating significant trustworthiness improvement ranging from $12.3\%$ to $47.8\%$ on the Med-CM-Syn dataset. Besides, AMTC-based federated methods performed significantly better than traditional single-dimension trustworthiness methods like FedAA and SADBA.

表1显示了我们的方法和基线在三个医疗数据集上不同联邦设置下的多维度可信性分数（即MTS）结果。我们提出的TrustGuard在所有指标上都始终获得最高性能，在Med-CM-Syn数据集上显示出12.3%到47.8%的显著可信性改进。此外，基于AMTC的联邦方法比传统的单维度可信性方法（如FedAA和SADBA）表现显著更好。

Table 1: Multi-dimensional Trustworthiness Score of medical trustworthiness task on three datasets with $N = 3$ and 5. The bold font highlights the best performance, and the underlined font indicates a suboptimal result.

<table><tr><td>Model</td><td>MIMIC-III (3)</td><td>MIMIC-III (5)</td><td>Med-CM-Syn (3)</td><td>Med-CM-Syn (5)</td></tr><tr><td>FedAA</td><td>72.15±1.23</td><td>71.20±1.45</td><td>68.45±2.10</td><td>67.89±1.87</td></tr><tr><td>SADBA</td><td>74.32±0.98</td><td>73.67±1.12</td><td>70.23±1.65</td><td>69.78±1.43</td></tr><tr><td>RobustFL</td><td>76.89±1.34</td><td>75.45±1.67</td><td>72.56±1.89</td><td>71.23±2.01</td></tr><tr><td>FairFL</td><td>73.45±2.12</td><td>72.89±1.98</td><td>69.87±2.34</td><td>68.45±2.67</td></tr><tr><td>ExplainFL</td><td>75.67±1.56</td><td>74.23±1.78</td><td>71.45±1.98</td><td>70.12±2.23</td></tr><tr><td>PrivacyFL</td><td>77.23±1.12</td><td>76.78±1.34</td><td>73.89±1.56</td><td>72.67±1.78</td></tr><tr><td>SecureFL</td><td>78.45±0.98</td><td>77.89±1.23</td><td>75.23±1.45</td><td>74.56±1.67</td></tr><tr><td>MultiTrust</td><td>79.67±1.45</td><td>78.23±1.67</td><td>76.45±1.78</td><td>75.89±1.98</td></tr><tr><td>FedTrust</td><td>81.23±1.12</td><td>80.45±1.34</td><td>78.67±1.56</td><td>77.23±1.78</td></tr><tr><td>MedFL</td><td>82.45±0.87</td><td>81.67±1.12</td><td>79.89±1.34</td><td>78.45±1.56</td></tr><tr><td><strong>TrustGuard</strong></td><td><strong>89.67±0.76</strong></td><td><strong>88.23±0.98</strong></td><td><strong>87.45±1.12</strong></td><td><strong>86.78±1.34</strong></td></tr></table>

TrustGuard significantly outperformed all baseline methods. Especially, our TrustGuard outperformed the best baseline MedFL by 7.22% on MIMIC-III and 7.56% on Med-CM-Syn datasets. In conclusion, the TrustGuard framework exhibits superior multi-dimensional trustworthiness performance across multiple medical datasets and metrics, outperforming traditional single-dimension trustworthiness methods and several federated learning approaches.

TrustGuard显著优于所有基线方法。特别地，我们的TrustGuard在MIMIC-III数据集上比最佳基线MedFL高出7.22%，在Med-CM-Syn数据集上高出7.56%。总之，TrustGuard框架在多个医疗数据集和指标上表现出优越的多维度可信性性能，优于传统的单维度可信性方法和几种联邦学习方法。

### Performance under different medical data distribution

Figure 3 reveals the MTS performance of the global model of TrustGuard and its variant models on two different medical data distributions. From the results, we can observe that (1) medical data distribution heterogeneity reduces the model's trustworthiness generalization ability, resulting in decreased performance. Specifically, the overall performance under a distribution of $\beta = 0.3$ is significantly lower than under $\beta = 8.0$. This is due to the increased variability in medical specialties and patient demographics across institutions, making it difficult to obtain a unified trustworthiness model that performs well for all medical scenarios, thus affecting convergence and outcomes. (2) The TrustGuard-U model performs the worst because a single coupling strategy is less adaptable to institutions with heterogeneous medical data, reducing trustworthiness generalization and overall performance. (3) TrustGuard outperforms both distributions' top medical federated learning method, MedFL. This is because our method leverages AMTC to provide more flexible coupling architectures that better adapt to varying medical data distributions and regulatory requirements. And (4) TrustGuard also surpasses the random and static variants, as it effectively uses historical trustworthiness performance feedback and domain knowledge from medical data to more accurately select and update coupling configurations, further boosting multi-dimensional trustworthiness performance.

不同医疗数据分布下的性能。图3显示了TrustGuard及其变体模型在两种不同医疗数据分布下的全局模型MTS性能。从结果中，我们可以观察到：(1)医疗数据分布异构性降低了模型的可信性泛化能力，导致性能下降。具体而言，$\beta = 0.3$分布下的整体性能显著低于$\beta = 8.0$。这是由于医疗机构间医疗专科和患者人口统计学的变异性增加，使得难以获得对所有医疗场景都表现良好的统一可信性模型，从而影响收敛和结果。(2)TrustGuard-U模型表现最差，因为单一耦合策略对具有异构医疗数据的机构适应性较差，降低了可信性泛化和整体性能。(3)TrustGuard在两种分布下都优于顶级医疗联邦学习方法MedFL。这是因为我们的方法利用AMTC提供更灵活的耦合架构，更好地适应不同的医疗数据分布和法规要求。(4)TrustGuard还超越了随机和静态变体，因为它有效利用历史可信性性能反馈和医疗数据的领域知识来更准确地选择和更新耦合配置，进一步提升多维度可信性性能。

Additionally, unlike traditional single-dimension optimization, the AMTC simultaneously drives the design of multiple coupling strategies and fully leverages historical trustworthiness performance for updates. Table 2 compares client model performance across different medical distributions, using FedAA with FedAvg as the baseline on the Med-CM-Syn dataset. The results show that traditional single-dimension methods performed the worst, even falling below the baseline, with Institution 1's trustworthiness score at 0.42. This suggests that single-dimension methods struggle to handle medical data distribution bias effectively, as evidenced by Institution 1's medical specialty distribution in Figure 2(a). In contrast, our method demonstrated the most significant improvement,

此外，与传统的单维度优化不同，AMTC同时驱动多种耦合策略的设计，并充分利用历史可信性性能进行更新。表2比较了不同医疗分布下的客户端模型性能，在Med-CM-Syn数据集上使用FedAA与FedAvg作为基线。结果显示传统单维度方法表现最差，甚至低于基线，机构1的可信性分数为0.42。这表明单维度方法难以有效处理医疗数据分布偏差，如图2(a)中机构1的医疗专科分布所示。相比之下，我们的方法显示出最显著的改进。

![](images/medical_trustworthiness_distribution.jpg)
Figure 3: The global multi-dimensional trustworthiness score in different heterogeneous Med-CM-Syn medical data distribution. The black line located in the center of the bar chart signifies the error range.

图3：不同异构Med-CM-Syn医疗数据分布下的全局多维度可信性分数。位于条形图中心的黑线表示误差范围。

with an average MTS $31.2\%$ higher than the baseline. The results also highlight the advantage of multiple coupling strategies in personalized medical federated learning. TrustGuard and TrustGuard-S adapted better to heterogeneous medical data distributions than a single coupling strategy like TrustGuard-U. In summary, TrustGuard searched for five distinct coupling configurations for each medical institution, and after PAD optimization, the final MTS reached $92.4\%$, this is a $24.7\%$ improvement over TrustGuard-U and an $8.3\%$ increase over TrustGuard-S.

平均MTS比基线高31.2%。结果还突出了多种耦合策略在个性化医疗联邦学习中的优势。TrustGuard和TrustGuard-S比单一耦合策略（如TrustGuard-U）更好地适应异构医疗数据分布。总之，TrustGuard为每个医疗机构搜索了五种不同的耦合配置，经过PAD优化后，最终MTS达到92.4%，比TrustGuard-U提高24.7%，比TrustGuard-S提高8.3%。

Table 2: The Multi-dimensional Trustworthiness Score of the Med-CM-Syn dataset $N = 3$ $\beta = 0.3$ on different medical institutions, where Avg $\uparrow$ indicates the average improvement in trustworthiness performance compared to the baseline across all institutions. The bold font highlights the best performance.

表2：Med-CM-Syn数据集在不同医疗机构上的多维度可信性分数，$N = 3$ $\beta = 0.3$，其中Avg $\uparrow$表示相对于基线在所有机构上的平均可信性性能改进。粗体字突出显示最佳性能。

<table><tr><td>Model</td><td>Institution 1</td><td>Institution 2</td><td>Institution 3</td><td>Global</td><td>Avg ↑</td></tr><tr><td>FL+FedAA</td><td>0.42</td><td>0.58</td><td>0.51</td><td>0.61</td><td>-</td></tr><tr><td>MedFL</td><td>0.73</td><td>0.79</td><td>0.68</td><td>0.78</td><td>27.9%</td></tr><tr><td>FedTrust</td><td>0.69</td><td>0.72</td><td>0.65</td><td>0.74</td><td>21.3%</td></tr><tr><td>TrustGuard-U</td><td>0.81</td><td>0.76</td><td>0.73</td><td>0.79</td><td>29.5%</td></tr><tr><td>TrustGuard-S</td><td>0.87</td><td>0.83</td><td>0.79</td><td>0.85</td><td>39.3%</td></tr><tr><td><strong>TrustGuard</strong></td><td><strong>0.94</strong></td><td><strong>0.91</strong></td><td><strong>0.89</strong></td><td><strong>0.92</strong></td><td><strong>50.8%</strong></td></tr></table>

## Case study of different coupling mechanisms

We evaluate the performance of different coupling mechanism backends: 'AMTC', 'MOEA-D', and 'NSGA-III'. Table 3 compares their MTS, TCD, and the best coupling configuration identified on the MIMIC-III and Med-CM-Syn datasets. While NSGA-III generally performed well, it did not outperform AMTC in any metric. AMTC consistently achieved the highest MTS and TCD, making it the top-performing coupling mechanism. Although MOEA-D excelled in TCD on the MIMIC-III dataset, its lower MTS placed it behind AMTC overall. According to recent multi-objective optimization research, AMTC's adaptive nature may outperform traditional evolutionary algorithms on dynamic medical trustworthiness tasks, which could explain its superior performance here. These findings underscore the importance of selecting the appropriate coupling mechanism based on the specific medical trustworthiness metrics prioritized in different healthcare applications.

不同耦合机制的案例研究。我们评估了不同耦合机制后端的性能：'AMTC'、'MOEA-D'和'NSGA-III'。表3比较了它们在MIMIC-III和Med-CM-Syn数据集上的MTS、TCD和识别的最佳耦合配置。虽然NSGA-III总体表现良好，但在任何指标上都没有超过AMTC。AMTC始终获得最高的MTS和TCD，使其成为表现最佳的耦合机制。尽管MOEA-D在MIMIC-III数据集上的TCD表现出色，但其较低的MTS使其总体落后于AMTC。根据最近的多目标优化研究，AMTC的自适应特性可能在动态医疗可信性任务上优于传统进化算法，这可以解释其在此处的优越性能。这些发现强调了根据不同医疗应用中优先考虑的特定医疗可信性指标选择适当耦合机制的重要性。

Table 3: The performance of TrustGuard with different coupling mechanisms on two datasets $(N = 3)$. The bold font highlights the best performance, and the underlined font indicates a suboptimal result.

表3：TrustGuard在两个数据集上使用不同耦合机制的性能$(N = 3)$。粗体字突出显示最佳性能，下划线字体表示次优结果。

<table><tr><td></td><td>Model</td><td>MTS</td><td>TCD</td></tr><tr><td rowspan="3">MIMIC-III</td><td>TrustGuard-MOEA-D</td><td>84.23±1.45</td><td>87.56±0.98</td></tr><tr><td>TrustGuard-NSGA-III</td><td>85.67±1.23</td><td>86.34±1.12</td></tr><tr><td><strong>TrustGuard-AMTC</strong></td><td><strong>89.45±0.87</strong></td><td><strong>91.23±0.76</strong></td></tr><tr><td rowspan="3">Med-CM-Syn</td><td>TrustGuard-MOEA-D</td><td>82.89±1.67</td><td>85.12±1.34</td></tr><tr><td>TrustGuard-NSGA-III</td><td>84.12±1.45</td><td>86.78±1.23</td></tr><tr><td><strong>TrustGuard-AMTC</strong></td><td><strong>87.34±1.12</strong></td><td><strong>89.67±0.98</strong></td></tr></table>

Additionally, we visualized the best coupling configurations identified by AMTC and MOEA-D on the MIMIC-III dataset, as shown in Figure 4. The results reveal that similar coupling patterns frequently appear in the optimal configurations identified by different mechanisms. Notably, the optimal coupling configurations for Institution 3 consistently feature high privacy-security coupling weights and adaptive fairness-robustness balancing. This consistency suggests that these coupling patterns may be crucial to the optimal trustworthiness configurations, reflecting specific medical domain characteristics of the data. The high privacy-security coupling might indicate that Institution 3's medical data requires strong privacy protection while maintaining security monitoring capabilities, allowing the trustworthiness framework to capture sufficient regulatory compliance and threat detection for strong medical AI performance. These findings underscore the interpretability of our framework and offer valuable insights for further optimizing medical trustworthiness architectures.

此外，我们可视化了AMTC和MOEA-D在MIMIC-III数据集上识别的最佳耦合配置，如图4所示。结果显示，不同机制识别的最优配置中经常出现相似的耦合模式。值得注意的是，机构3的最优耦合配置始终具有高隐私-安全耦合权重和自适应公平性-鲁棒性平衡。这种一致性表明这些耦合模式可能对最优可信性配置至关重要，反映了数据的特定医疗领域特征。高隐私-安全耦合可能表明机构3的医疗数据需要强隐私保护，同时保持安全监控能力，使可信性框架能够捕获足够的法规合规性和威胁检测，以实现强大的医疗AI性能。这些发现强调了我们框架的可解释性，并为进一步优化医疗可信性架构提供了有价值的见解。

![](images/coupling_configuration_comparison.jpg)
Figure 4: The comparison of the optimal coupling configuration between AMTC and MOEA-D on MIMIC-III $(N = 3)$ dataset. Coupling patterns with the same weights and structure are highlighted with a purple background.

图4：AMTC和MOEA-D在MIMIC-III $(N = 3)$数据集上最优耦合配置的比较。具有相同权重和结构的耦合模式用紫色背景突出显示。