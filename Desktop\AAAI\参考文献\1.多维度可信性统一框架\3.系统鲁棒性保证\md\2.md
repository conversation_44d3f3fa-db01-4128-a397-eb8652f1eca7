# FedRecon: Missing Modality Reconstruction in Heterogeneous Distributed Environments

Junming Liu $^{1,2}$ , <PERSON><PERSON>g $^{1*}$ , <PERSON>g <PERSON> $^{2}$ , Yanting Gao $^{1}$ , <PERSON><PERSON><PERSON> Jin $^{1}$ $^{1}$ Tongji University   $^{2}$ Shanghai Artificial Intelligence Laboratory  Shanghai, China  {liu_junming6917,g<PERSON><PERSON>,2331952,ji<PERSON><PERSON><PERSON>}@tongji.edu.cn, <EMAIL>

![](images/35ee713cb992c072e2798dcb9e19475db0a4f70d316626b18e7dc5896984e686.jpg)  
Figure 1: Impact of modality and data heterogeneity on the convergence of the global model in MFL. Client 1 and 2 have complete modalities but suffer from data heterogeneity due to divergent local distributions. In contrast, client  $i$  and client  $N$  are missing modalities and perform unimodal self-reconstruction and cross-modal reconstruction respectively.

# Abstract

Multimodal data are often incomplete and exhibit Non- Independent and Identically Distributed (Non- IID) characteristics in real- world scenarios. These inherent limitations lead to both modality heterogeneity through partial modality absence and data heterogeneity from distribution divergence, creating fundamental challenges for effective federated learning (FL). To address these coupled challenges, we propose FedRecon, the first method targeting simultaneous missing modality reconstruction and Non- IID adaptation in multimodal FL. Our approach first employs a lightweight Multimodal Variational Autoencoder (MVAE) to reconstruct missing modalities while preserving cross- modal consistency. Distinct from conventional imputation methods, we achieve sample- level alignment through a novel distribution mapping mechanism that guarantees both data consistency and completeness. Additionally, we introduce a strategy employing global generator freezing to prevent catastrophic forgetting, which in turn mitigates Non- IID fluctuations. Extensive evaluations on multimodal datasets demonstrate FedRecon's superior performance in modality reconstruction under Non- IID conditions, surpassing state- of- the- art methods. Our code has been released on https://github.com/Wings- OfDisaster/FedRecon.

# CCS Concepts

- Computing methodologies  $\rightarrow$  Distributed artificial intelligence.

# Keywords

Multimodal Federated Learning, Multimodal Variational Autoencoder, Modality Reconstruction

# 1 Introduction

Federated Learning (FL) has gained significant attention as a promising framework for decentralized machine learning, ensuring privacy protection in distributed environments [33, 34, 41, 53, 70]. While existing FL methods demonstrate strong performance in unimodal settings [8, 72], their inability to capture cross- modal interactions fundamentally limits real- world applicability [20, 22, 39]. To bridge this gap, Multimodal Federated Learning (MFL) has emerged as a critical solution by enabling complementary information fusion across modalities [6, 76], with successful deployments in autonomous driving [9], video action capture [47], and multimedia sentiment analysis [27, 42]. By explicitly modeling inter- modal dependencies, MFL achieves holistic understanding of complex phenomena [7, 35], enabling precise real- world inference under data scarcity.

The majority of existing MFL approaches presume full modality availability across clients [21]. However, this assumption is often

misaligned with real- world conditions, where data availability is frequently restricted. For instance, certain clients may possess limited resources or only support unimodal data acquisition, whereas others may have access to more advanced multimodal setups [51]. Furthermore, the quality of the data can be severely impacted by fluctuations during the collection process, leading to inconsistencies that undermine the reliability of the captured information [15]. In this study, we formally consider a MFL system with  $K$  clients and up to  $|M|$  modalities. All clients and the central server share a common model architecture, but each client  $i$  has access to a subset  $M_{i} \subseteq M$  of the available modalities. This results in a scenario where  $M_{i} \neq M_{i^{\prime}}$  for some clients  $i, i^{\prime} \in [K]$ . Consequently, the system introduces data heterogeneity across clients and disrupts inter- modal connections within each client, exacerbating the challenges in MFL.

The global model  $F$  requires comprehensive processing of all modalities, precluding the discarding of any modality during local training. This necessitates reconstruction of missing modalities  $M_{i}^{\prime} = M \setminus M_{i}$  through feature generation from available client data. Existing reconstruction methods range from naive zero- filling [63] to advanced self- supervised synthesis [37, 77]. However, zero- filling frequently degrades model accuracy, while self- supervised methods that reduce label dependency fail to adequately address the complex challenge of cross- modal alignment [44]. Considering the limited storage and communication capacity of clients in MFL, it is crucial to adopt a lightweight and more efficient alignment method.

While some methods have focused on the alignment issues during the modality reconstruction process [67, 69, 71, 73], they inadequately preserve semantic consistency across samples sharing identical labels. As demonstrated in Figure 1, reconstructed modalities show inconsistent feature representations despite matching labels. This arises because existing methods align modalities through label correspondence but ignore semantic nuances specific to individual samples. Such limitations highlight that label agreement alone cannot guarantee multimodal coherence, as inherent variations within classes persist across clients.

In this paper, we present FedRecon, a novel framework designed to address missing modality reconstruction in distributed heterogeneous environments. By employing a lightweight Multimodal Variational Autoencoder (MVAE) [29, 48, 56, 68] as the core engine, FedRecon performs cross- modal alignment and modality imputation while overcoming client resource constraints in training and communication. Compared to computationally intensive alternatives like GANs [14] and Diffusion models [18], our architecture substantially reduces training time and communication cost through its streamlined encoder- decoder design, with complete efficiency investigation provided in Appendix A. Critically, we introduce a latent vector mapping mechanism that uniquely establishes sample- level alignment through direct feature coupling across modalities, ensuring semantic consistency during reconstruction. Furthermore, we maintain a frozen auxiliary model initialized from the global MVAE to stabilize the generation process in Non- IID environments, collaborating with local models to generate missing modalities.

The contributions of this paper are as follows:

- To the best of our knowledge, we are the first to leverage MVAEs for partial modality reconstruction in MFL, addressing scenarios with variable missing ratios while reducing training and communication costs.- We propose the first latent variable mapping mechanism for MVAE-based reconstruction, achieving sample-level alignment without dependency on joint sampling. Additionally, we are also the first to introduce a global generator freezing strategy in MFL, effectively mitigating catastrophic forgetting in Non-IID environments.- Experimental results validate state-of-the-art performance, surpassing baseline methods by  $2.99\%$  and  $4.57\%$  absolute gains on MELD and CrisisMMD benchmarks respectively, confirming the efficacy of FedRecon in real-world scenarios.

# 2 Related Work

# 2.1 Multimodal Variational Autoencoder

The Multimodal Variational Autoencoder (MVAE) extends the framework of Variational Autoencoders (VAE) [29] to enable joint learning and generation across heterogeneous data modalities. Wu and Goodman [68] pioneered this direction by introducing a scalable architecture capable of learning shared representations in weakly supervised settings, where only a subset of data samples contain all modalities. Subsequent work further advanced the generative coherence and fidelity of MVAEs. Shi et al. [56] proposed a mixture- of- experts formulation (MMVAE) that decomposes latent spaces into shared and modality- specific subspaces while ensuring coherent joint generation through multimodal integration. Palumbo et al. [48] enhanced this framework by explicitly isolating distributions of shared and private latent factors, thereby improving robustness to variations in latent space design without compromising semantic consistency. Further innovations integrated Diffusion models into the MVAE architecture [49], demonstrating enhanced unconditional generation performance for complex real- world data modalities. Despite their effectiveness in aligning modalities through joint latent space learning, current methods exhibit limitations in maintaining consistent cross- modal alignment across diverse samples sharing the same label, especially when used on highly variable datasets or data with real- world noise. Notably, FedRecon mitigates these deficiencies through a simple mapping mechanism, demonstrating practical efficacy in cross- modal alignment.

# 2.2 Missing Modality Reconstruction in MFL

Reconstructing missing modalities in MFL ensures robust model performance under heterogeneous data distributions. Xiong et al. propose CACMRN [69], a client- adaptive transformer leveraging instance relationships and federated optimization to prevent local overfitting during cross- modal reconstruction. Wang et al. develop FedMMR [67], aligning feature spaces via modality synthesis while integrating reconstructed and existing modalities as complementary inputs. Liu et al. design FedMobile [43], prioritizing cross- node latent feature sharing to address extreme modality incompleteness. Yu et al. introduce FedInMM [74], utilizing LSTM- based dynamic weighting and temporal fusion for irregular multimodal data. While effective in reconstructing modalities under limited samples in MFL, these methods overlook sample- level alignment across clients with

identical labels. Additionally, they inadequately address instability caused by Non- IID client data during cross- modal generation. Fe- dRecon stabilizes training by freezing a global generator replica to reduce distributional shifts, thereby mitigating fluctuations induced by client- specific data divergence.

# 3 Problem Formulation

We consider a standard MFL framework involving  $K$  clients, where the complete modality set  $M$  is heterogeneously distributed across clients. Each client  $i$  possesses a private dataset  $D_{i} = \{(X_{i}^{j},y_{i}^{j})\}_{i = 1}^{n_{i}}$  where  $X_{i}^{j} = \{X_{i,m}^{j}\in \mathbb{R}^{d_{x_{m}}}\mid m\in M_{i}\}$  represents the multimodal inputs and  $y_{i}^{j}\in Y_{i}$  denotes the corresponding label. Here,  $M_{i}\subseteq M$  indicates the subset of modalities available to client  $i$ ,  $d_{x_{m}}$  specifies the feature dimension of modality  $m$ , and  $n_i = |D_i|$  is the number of data points available at client  $i$ . The label space  $Y_{i}$  follows a client- specific distribution that determines both the label categories and their cardinality within the local dataset. The set of missing modalities for client  $i$  is denoted by  $M_{i}^{\prime} = M\setminus M_{i}$

Our goal is to train a global model  $F$  that includes modality- specific feature extractors  $\{E_i:\mathbb{R}^{d_{x_m}}\to \mathbb{R}^{d_{fm}}\}_{i\in M}$ , where  $d_{fm}$  is the feature space for modality  $m$ , along with a shared classifier  $H$ . The global model undergoes initialization and dissemination to all clients at the beginning of each communication round  $t\in \{1,\ldots ,T\}$ . During round  $t$ , every client  $i\in \{1,\ldots ,K\}$  executes local model training utilizing their respective available modalities  $M_{i}$ . For the missing modalities  $M_{i}^{\prime}$ , reconstructing the missing data is essential to maintaining consistency in local training across clients, and is achieved using a generator  $G$

Thus, the local loss function for client  $i$  on the sample  $(X_{i},y_{i})$  can be expressed as:

$$
\mathcal{L}_i(X_i,y_i;\phi_i) = \mathcal{L}_{\mathrm{task}}\left(H\left(\oplus_{m\in M_i}\{f_i,m\}\right),y_i\right), \tag{1}
$$

where  $\phi_{i}$  denotes the set of parameters for client  $i$ , and  $f_{i,m} = E_{i,m}(X_{i,m})$  represents the features extracted by the encoder  $E_{i,m}$  for modality  $m$ . Here,  $\oplus$  refers to the fusion operation that combines the features from all available modalities into a unified representation. In line with multi- task MFL frameworks [12, 67], the global objective  $\mathcal{L}$  for MFL is expressed as:

$$
\min_{\phi}\mathcal{L}(F(\phi)) = \frac{K}{K}\sum_{i = 1}^{K}\mathcal{L}_i(F_i(X_i,y_i;\phi_i)), \tag{2}
$$

where  $\mathcal{L}_i$  denotes the local loss function for client  $i$ ,  $F_{i}$  represents the client- specific model trained on its available modalities, and  $\phi$  refers to the parameters of the global model.

# 4 Proposed Method

A promising approach to mitigate the missing modality issue is to enforce consistency across clients [67]. Our method builds on this foundation with two key components: (1) a lightweight MVAE that reconstructs missing modalities via an innovative distribution mapping mechanism to preserve cross- modal consistency, and (2) a global generator freezing strategy to stabilize Non- IID learning dynamics. The complete workflow is shown in Figure 2.

# 4.1 Missing Modality Reconstruction

Considering the challenges of training cost, storage capacity, and communication overhead in MFL scenarios, we conducted a comprehensive survey of various generative architectures. Among them, VAE emerges as particularly advantageous due to its relatively compact parameter structure. This characteristic translates to diminished computational requirements and storage consumption on client devices, while simultaneously minimizing data transmission burdens. Such attributes render VAE exceptionally suitable for MFL environments where efficient resource utilization is critical.

Each modality  $m\in M$  has its own VAE- based generator  $G_{i,m}$  for data reconstruction, which includes two main components: the encoder  $G_{i,m}^{\mathrm{enc}}$  and the decoder  $G_{i,m}^{\mathrm{dec}}$ . The encoder maps the input  $X_{i,m}$  to a distribution over the latent space, while the decoder generates data  $X_{i,m}$  from the latent variable  $z$ . These generators and local models are uploaded to the server, from where they are shared with other clients in the next MFL communication round.

The goal of VAE is to maximize the log- likelihood of the data, but due to computational complexity, it is often optimized via a lower bound [29]. The optimization objective of VAE is:

$$
\begin{array}{rl} & {\mathcal{L}_{\mathbb{G}_{i,m}}(X_{i,m}) = -\mathbb{B}_{q_{\psi}(z|X_{i,m})}\left[\log \frac{p_{\theta}(X_{i,m},z)}{q_{\psi}(z|X_{i,m})}\right]}\\ & {= -\mathbb{B}_{q_{\psi}(z|X_{i,m})}\left[\log p_{\theta}(X_{i,m}|z)\right] + D_{\mathrm{KL}}\left(\| q_{\psi}(z|X_{i,m})\| p(z)\right),} \end{array} \tag{3}
$$

where the input data  $X_{i,m}$  corresponds to modality  $m$ , with  $z$  denoting the latent variables. The approximate posterior  $q_{\psi}(z|X_{i,m})$  is parameterized by the encoder with learnable parameters  $\psi$ , and  $p_{\theta}(X_{i,m},z) = p(z)p_{\theta}(X_{i,m}|z)$  is the joint distribution generated by the decoder with parameters  $\theta$

As a result, the loss can be divided into two components. The reconstruction loss  $\mathcal{L}_{\mathrm{recon}}$  quantifies the ability of the decoder to accurately reconstruct the input data  $X_{i,m}$  based on the latent variable  $z$ , whereas the KL divergence term  $\mathcal{L}_{\mathrm{KL}}$  imposes a regularization on the posterior distribution  $q_{\psi}(z|X_{i,m})$ , encouraging it to approximate the prior distribution  $p(z)$ . Notably, the VAE- based generator we adopt is label- agnostic. By leveraging the label space  $Y_{i}$ , the generator models a distribution that aligns with the client's attributes, serving as a high- level abstraction of the raw data and mitigating privacy risks without exposing sensitive information.

As mentioned above, we perform self- reconstruction for each modality to obtain highly correlated data on missing modalities. Our generator is particularly suited for MFL environments, producing high- quality data while maintaining a lightweight architecture.

# 4.2 Cross-modal Alignment and Modality Awareness Fusion

Merely performing self- reconstruction using the data of the missing modality is insufficient. Leveraging cross- modal alignment to guide the generator is indispensable. Considering the issue of cross- modal interactions, the reconstructed modality and the other available modalities should exhibit similarity in the feature space. This fundamental insight drives our expansion of generator capabilities from self- reconstruction to cross- modal synthesis.

While advanced MVAE architectures leverage shared latent variables [48, 56] to enable cross- modal interaction, they often suffer

![](images/962f4bbe108bfa9ed7d51d73b238b92f22ae5c2324e068f58d2500e35bf3b6b4.jpg)  
Figure 2: (a) The workflow of FedRecon, illustrating the process of reconstructing missing modality in distributed heterogeneous environments. (b) The communication mechanism of FedRecon, where each client downloads and submits model according to its modalities, reducing overhead during distributed learning.

from limited diversity in reconstructed outputs and fail to achieve precise sample- level alignment across modalities. Our framework introduces a distribution re- encoding mechanism through mapping modelsl  $T_{n,m}$  , which adapt the latent variable  $z_{m}$  to better suit each target modality. This design provides modality- specific latent inputs for each decoder  $\mathcal{G}_{i,m}^{\mathrm{dec}}$  allowing for both decoupled distribution modeling and more accurate sample- level alignment across modalities. The optimization objective in the multimodal setting becomes:

$$
\begin{array}{rl} & {\mathcal{L}_{G_{i,m}}(X_i) = -\frac{1}{M}\sum_{m = 1}^{M}\mathbb{E}_{q_{\psi_m}(z_m|X_{i,m})}\left[\log \frac{p_\Theta(X_i,z_m)}{q_{\psi_m}(z_m|X_{i,m})}\right],}\\ & {p_\Theta (X_i,z_m) = p(z_m)p_{\theta_m}(X_{i,m}|z_m)\prod_{n = 1\atop n\neq m}^{M}p_{\theta_n}(X_{i,n}|T_{n,m}(z_m)),} \end{array} \tag{4}
$$

where  $T_{n,m}$  is a mapping model that adjusts the distribution of  $z_{m}$  to approximate  $z_{n}$  effectively bridging the representational gap between heterogeneous modalities.

Compared to optimizing feature alignment across different modalities using similarity at the feature level [40, 67], directly aligning a compressed distribution proves to be more efficient. The structure of the model  $T_{n,m}$  is based on a simple MLP architecture, which projects the latent distribution from modality  $m$  onto that of modality  $n$  in the latent space. However, we found that directly training the model using the objective in Equation 4 leads to instability and poor convergence. To address this, we decompose the training process into two stages. In the first stage, we jointly train the encoder and decoder components using the objective in Equation 3 to learn modality- specific representations and enable accurate reconstruction. In the second stage, we freeze the encoder and decoder parameters and exclusively optimize the mapping models  $T_{n,m}$  so that the latent variable encoded from modality  $m$  can be transformed to faithfully reconstruct the data from modality  $n$

To further stabilize this training process, we optionally apply KL divergence as a regularization term to promote convergence when the mapping  $T_{n,m}$  is hard to optimize. This encourages the mapped distribution to align better with the target latent space. The final training objective for  $T_{n,m}$  is defined as:

$$
\mathcal{L}_{\mathrm{match}} = \alpha \cdot D_{\mathrm{KL}}\left(T_{n,m}(z_m)\parallel z_n\right) + \mathcal{L}_{\mathrm{recon}}, \tag{5}
$$

where  $\alpha \in \{0,1\}$  is a binary coefficient controlling the use of the KL regularization term, and  $\mathcal{L}_{\mathrm{recon}}$  denotes the reconstruction loss.

To enhance semantic consistency between the reconstructed and available modalities, we adopt an attention- based feature fusion mechanism inspired by [12]. Given the extracted features  $f_{i,m}$  and its reconstructed counterpart  $f_{i,m}^{\prime}$  , we concatenate them into a joint representation  $h = [\{f_{i,m},f_{i,m}^{\prime}\} ]$  , and compute attention weights as follows:

$$
\begin{array}{c}{u = \tanh (Wh + b),a = \mathrm{softmax}(u^T c),}\\ {f_{i,m}^{*} = \sum_ia_ih_i,} \end{array} \tag{6}
$$

where  $W,b$  ,and  $\bar{C}$  are learnable parameters, and the final fused feature  $f_{i,m}^{*}$  is obtained through a weighted sum over the concatenated input. This attention mechanism can be extended to a multi- head setting by introducing multiple context vectors, enabling finergrained fusion across modalities.

# 4.3 Global Generator Freezing Strategy

In Section 4.2, we train the generator using complete modality data  $X_{i}^{j}$  , and employ it during inference to predict the missing modality  $X_{i,m^{\prime}}^{k}$  from the available modality  $X_{i,m}^{k}$  . However, in Non- . IID settings, the missing modality  $X_{i,m^{\prime}}^{k}$  may exhibit a noticeable distribution shift with respect to the local data of client  $i$  , making it underrepresented during local training and thus prone to forgetting. In such cases, the missing data  $X_{i,m^{\prime}}^{k}$  may be better aligned with the distribution of another client  $i^{\prime}$  , where similar samples  $X_{i}^{j},$  are more common. Consequently, relying solely on local generators can lead to suboptimal reconstruction performance.

To mitigate this, we propose a global generator freezing strategy, where each client maintains two generators: one global generator initialized at deployment and kept frozen, and another updated locally. This allows clients to fall back on the global generator when their own reconstruction suffers from data shift or forgetting.

![](images/869de1445422437fb820530f0a9359ee29026cf77e3a5fb20c43d5ee00b62551.jpg)  
Figure 3: An illustrative example of the Global Generator Freezing Strategy on the PolyMNIST dataset.

As illustrated in Figure 3, we replace the original synthetic data with a concatenation of outputs from both global and local generators, and reduce the weight of synthetic features during the attention fusion process to achieve a balancing effect. Despite the additional requirement of storing both generators and mapping models on each client, our approach remains significantly more lightweight than GAN- or Diffusion- based alternatives. The detailed algorithmic workflow is outlined in Algorithm 1.

# 5 Experiments

# 5.1 Experimental Setups

Datasets. To evaluate the effectiveness of our MFL framework, we utilize two real- world multimodal datasets: (1) MELD [52], a multiparty conversational dataset comprising over 9k utterances with both audio and textual transcripts from the TV series Friends, and (2) CrisisMMD [2], which contains 18.1k tweets accompanied by both visual and textual information, collected during various crisis events. To further assess the generative quality of our MVAE model, we additionally adopt two benchmark multimodal datasets: (3) PolyMNIST [59], a synthetic dataset featuring five modalities, where each consists of MNIST digits composited over diverse background images; and (4) CUB [66], the Caltech- UCSD Birds dataset, which pairs bird images with fine- grained textual descriptions, posing a challenging setting due to the modality- specific nuances in visual and textual representations.

Baselines. For evaluating the effectiveness of our MFL framework, we compare FedRecon against a range of both unimodal and multimodal FL methods. Unimodal baselines include FedAvg [45], FedProx [36], FedRS [38], Scaffold [24], and FedOpt [54], all implemented with zero- filling for missing modalities following the FedMultimodal [12] benchmark. Multimodal baselines include CACMRN [69], FedMMR [67], and minFredMC [75], where zero- filling is likewise adopted in the absence of modality- specific generative modules. We also considered CLAP [10], but excluded it in the main comparison due to incompatible settings. To assess the generative performance of our MVAE model, we compare against several representative multimodal generative approaches, including MVAE [68], MVTCAE [23], mmJSD [58], MMVAE [56], MoPoE- VAE [60], MMVAE+ [48], and MMVM [61].

Implementations. For our MFL framework, we implement FedRecon atop the FedMultimodal benchmark [12]. Following their protocol, we utilize MobileNetV2 [19], MobileBERT [57], and MFCC

# Algorithm 1 Two-Stage Federated Training for FedRecon

Notations:  $\phi^{(t)}$  : global model parameters at round  $t$ $G_{k,m}^{(t)}$  : MVAE parameters (encoder and decoder) for client  $k$  and modality  $m$ $\bar{G}_{k,m}^{(t)}$  : frozen global MVAE parameters  $T_{n,m}^{(t)}$  : mapping model from modality  $m$  to  $n$  abbreviated as  $T^{(t)}$ $T_{n,m}^{(t)}$  : frozen mapping model 1: for round  $t = 1,2,\ldots ,T$  do 2: for client  $k = 1$  to  $K$  in parallel do 3: Stage 1: Local training of MVAE 4:  $\{\bar{G}_{k,m}^{(t)}\}_{m = 1}^{M}$ $\{G_{k,m}^{(t)}\}_{m = 1}^{M}$ $\{G_{m}^{(t - 1)}\}_{m = 1}^{M}$  5:  $\bar{T}_{k}^{(t)}$ $T_{k}^{(t)}\gets T^{(t - 1)}$  6: Update  $\{G_{k,m}^{(t)}\}_{m = 1}^{M}$  using  $\mathcal{L}_{G_{k,m}}$  for  $L_{1}$  epochs 7: Stage 2: Freeze MVAE and train mapping modules 8:  $\{G_{k,m}^{(t)}\}_{m = 1}^{M}\gets \mathrm{Freeze}(\{G_{k,m}^{(t)}\}_{m = 1}^{M})$  9: Update  $T_{k}^{(t)}\gets \mathrm{SGD}(T_{k}^{(t)})$  for  $L_{2}$  epochs 10: Stage 3: Update task model 11: Reconstruct using both global  $\bar{G}_{k,n}^{(t)}$  and local  $G_{k,n}^{(t)}$  12: Update  $\phi_{k}^{(t)}\gets \mathrm{SGD}(\phi^{(t - 1)})$  for  $L_{3}$  epochs 13: end for 14:  $\{G_{m}^{(t)}\}_{m = 1}^{M}\gets \left\{\frac{1}{K}\sum_{k = 1}^{K}G_{k,m}^{(t)}\right\}_{m = 1}^{M}$  15:  $\begin{array}{r}T^{(t)}\gets \frac{1}{K}\sum_{k = 1}^{K}T_{k}^{(t)} \end{array}$  16:  $\begin{array}{r}\phi^{(t)}\gets \frac{1}{K}\sum_{k = 1}^{K}\phi_{k}^{(t)} \end{array}$  17: end for

(Mel- Frequency Cepstral Coefficients) [11] to extract features from visual, textual, and audio modalities respectively, enabling efficient and scalable federated training. Therefore, a simple Conv+RNN architecture is used to process visual and audio data, while text is handled by an RNN- only model. For the MELD dataset, which contains only audio and text modalities, we adapt our model structure based on the VAE design provided in a prior implementation to better handle the audio modality. For CrisisMMD, we adopt the vision- language VAE architecture proposed in the original MVAE framework to accommodate the image- text modality pair. We use FedAvg as the baseline method for MELD, while for CrisisMMD we adopt FedOpt. We report Unweighted Average Recall (UAR) on MELD and F1- score on CrisisMMD, along with Top- 1 accuracy on both datasets for a more comprehensive evaluation. To evaluate generative baselines, we build upon the official MMVAE+ implementation and refer to their reported metrics for consistency. We also reproduce MMVM results as an additional reference point. Generative quality is assessed using Fechet Inception Distance (FID) [16] and generative coherence [56] metrics. All experiments are repeated with three different random seeds to ensure robustness and are conducted on a single NVIDIA A100 GPU with 80GB memory. Further implementation details, including those for reproducing closed- source baselines, are provided in Appendix C.

Modality Heterogeneity. We simulate missing modalities by modeling their availability using a Bernoulli distribution, where

![](images/5a64c8927edc7728e93731736c17d238ba177f677ae5c070dade38ef6d355203.jpg)  
Figure 4: Top1-F1 and Top1-accuracy on CrisisMMD under Dedicated FL scenarios, comparing the effects of low  $(\alpha = 0.1)$  and high  $(\alpha = 5.0)$  label imbalance.

Table 1: Comparison of UAR classification results for Dedicated FL on MELD, with  $\rho$  varying from  $10\%$  to  $90\%$  

<table><tr><td></td><td colspan="10">MELD, Top1-Unweighted Average Recall (UAR) ↑</td></tr><tr><td>Algorithm</td><td>10%</td><td>20%</td><td>30%</td><td>40%</td><td>50%</td><td>60%</td><td>70%</td><td>80%</td><td>90%</td><td></td></tr><tr><td>FedAvg</td><td>55.03</td><td>53.62</td><td>54.04</td><td>53.11</td><td>52.73</td><td>51.53</td><td>50.60</td><td>48.31</td><td>46.65</td><td></td></tr><tr><td>FedProx</td><td>54.78</td><td>53.49</td><td>53.04</td><td>53.09</td><td>53.08</td><td>51.65</td><td>50.47</td><td>49.16</td><td>48.45</td><td></td></tr><tr><td>FedRS</td><td>54.24</td><td>53.19</td><td>53.81</td><td>52.80</td><td>53.26</td><td>51.39</td><td>51.79</td><td>49.52</td><td>46.53</td><td></td></tr><tr><td>SCAFFOLD</td><td>50.87</td><td>49.82</td><td>48.49</td><td>48.14</td><td>47.16</td><td>46.34</td><td>42.87</td><td>40.43</td><td>29.44</td><td></td></tr><tr><td>FedOpt</td><td>54.67</td><td>52.38</td><td>51.88</td><td>51.69</td><td>52.81</td><td>49.93</td><td>48.54</td><td>49.78</td><td>47.94</td><td></td></tr><tr><td>CACMRRN</td><td>54.62</td><td>53.41</td><td>52.13</td><td>51.65</td><td>53.38</td><td>52.51</td><td>50.56</td><td>50.23</td><td>47.37</td><td></td></tr><tr><td>mmFedMC</td><td>55.31</td><td>53.30</td><td>53.85</td><td>50.95</td><td>52.65</td><td>50.13</td><td>50.03</td><td>48.98</td><td>46.10</td><td></td></tr><tr><td>FedMMR</td><td>54.47</td><td>53.49</td><td>52.83</td><td>52.78</td><td>54.06</td><td>51.52</td><td>50.69</td><td>49.26</td><td>47.79</td><td></td></tr><tr><td>FedRecon</td><td>55.44</td><td>56.08</td><td>54.93</td><td>55.47</td><td>54.72</td><td>53.18</td><td>51.34</td><td>51.83</td><td>49.67</td><td></td></tr></table>

each modality has an equal probability of being absent. In this work, we adopt the Dedicated FL paradigm [67], where both the training and testing sets experience the same missing rate. The data missing rate is varied from 0.1 to 0.9.

Statistical Heterogeneity. For the MELD dataset, we partition the data based on client IDs, as the data is naturally organized by speaker identifiers. In contrast, the CrisisMMD dataset lacks such realistic client partitions, so we use the Dirichlet distribution to partition the data with  $\alpha = \{0.1, 5.0\}$ , where  $\alpha = 0.1$  and  $\alpha = 5.0$  correspond to high and low data heterogeneity, respectively. Figure 7 illustrates the inconsistent data distributions.

# 5.2 Main Results

We evaluate FedRecon in two parts. The first part focuses on classification performance within the MFL setting, while the second part assesses generative quality based on the evaluation protocol from MMVAE+. To highlight performance differences, we use boldface to indicate the best result and underline for the second- best.

MELD Results. The MELD dataset consists of audio and text modalities, which are highly consistent in the information they convey. As a result, the impact of missing modalities on performance is relatively mild, and reconstruction brings limited gains. As shown in Table 1 and Table 2, our method achieves strong performance across different missing ratios. For instance, with  $50\%$  modalities missing, FedRecon attains  $54.72\%$  UAR, outperforming FedAvg by  $1.99\%$ . While the reconstruction process does not lead to significant improvements in classification performance due to inherent modality redundancy, FedRecon still consistently outperforms existing baselines. Notably, under the extreme condition where  $90\%$  of modalities are missing, it achieves a Top1- UAR of  $49.67\%$  and a Top1- accuracy of  $65.50\%$  in the Dedicated FL setting. Additionally, we observe that although the accuracy metric fluctuates noticeably across different random seeds, the trends in UAR and accuracy remain largely consistent. Among the compared MFL methods, both FedMMR and CACMRRN incorporate modality reconstruction, which contributes to their superior performance.

Table 2: Comparison of Top1-accuracy classification results for Dedicated FL on MELD, with  $\rho$  varying from  $10\%$  to  $90\%$  

<table><tr><td></td><td colspan="10">MELD, Top1-accuracy ↑</td><td></td><td></td><td></td><td></td><td></td><td></td><td></td></tr><tr><td>Algorithm</td><td>10%</td><td>20%</td><td>30%</td><td>40%</td><td>50%</td><td>60%</td><td>70%</td><td>80%</td><td>90%&lt;ecelfcel&gt;67.09</td><td>67.05</td><td>66.78</td><td>66.02</td><td>63.46</td><td>64.66</td><td>64.81</td><td>62.51</td><td>61.39</td></tr><tr><td>FedAvg</td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td></tr><tr><td>FedProx</td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td></tr><tr><td>FedRS</td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td></tr><tr><td>SCAFFOLD</td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td></tr><tr><td>FedOpt</td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td></tr><tr><td>CACMRRN</td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td></tr><tr><td>mmFedMC</td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td></tr><tr><td>FedMMR</td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td></tr><tr><td>FedRecon</td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td></tr></table>

CrisisMMD Results. Similarly, due to the sample distribution issue, we evaluate the CrisisMMD dataset using both F1- score and Top1- accuracy. As noted in [12], the dataset is particularly sensitive to modality missing. In all missing rate settings, an increase in the missing rate causes a marked decline in performance across all methods. However, occasional performance gains are observed at certain missing ratios, likely because real- world noise in the data can make some modalities less informative—removing them may inadvertently lead to better results. Despite this inherent fluctuation, FedRecon consistently outperforms its counterparts. As

![](images/46aa381eb8d568789373f3b2cdd13a260c50b48225d48eb7434a5000745779a9.jpg)  
Figure 5: Cross-modal generation results on PolyMNIST. Each image is generated from modality m4, showcasing reconstructions and translations into modalities  $\mathbf{m0 - m4}$

![](images/c763e2b7206adb96b86dd04f02b4de9b91fcb021dc5b273a9a1af6761844c953.jpg)  
Figure 6: Cross-modal generation on CUB: images generated from the text modality  $(\mathbf{m1})$  to the image modality  $(\mathbf{m0})$ . Each column corresponds to a set of images generated from the same text input.

Table 3: Conditional generation performance on PolyMNIST and CUB. Higher coherence and lower FID indicate better performance. We adopt the default hyperparameters used in MMVAE  $^+$  for fair comparison.  

<table><tr><td rowspan="2">Method</td><td colspan="2">PolyMNIST</td><td colspan="2">CUB</td></tr><tr><td>Coherence</td><td>FID</td><td>Coherence</td><td>FID</td></tr><tr><td>MVAE</td><td>0.093 (±0.009)</td><td>82.59 (±6.22)</td><td>0.271 (±0.007)</td><td>172.21 (±3.61)</td></tr><tr><td>MVTCAE</td><td>0.509 (±0.006)</td><td>58.98 (±0.62)</td><td>0.221 (±0.007)</td><td>208.43 (±1.10)</td></tr><tr><td>mmJSD</td><td>0.785 (±0.023)</td><td>209.98 (±1.26)</td><td>0.556 (±0.158)</td><td>262.80 (±6.93)</td></tr><tr><td>MMVAE</td><td>0.837 (±0.004)</td><td>153.11 (±4.11)</td><td>0.713 (±0.057)</td><td>232.20 (±2.14)</td></tr><tr><td>MoPoE-VAE</td><td>0.723 (±0.006)</td><td>160.29 (±4.12)</td><td>0.579 (±0.158)</td><td>265.55 (±4.01)</td></tr><tr><td>MMVAE+</td><td>0.796 (±0.010)</td><td>80.75 (±0.18)</td><td>0.721 (±0.090)</td><td>164.94 (±1.50)</td></tr><tr><td>MMVM</td><td>0.773 (±0.041)</td><td>98.34 (±7.11)</td><td>0.714 (±0.079)</td><td>160.44 (±3.20)</td></tr><tr><td>FedRecon</td><td>0.845 (±1.199)</td><td>78.915 (±8.33)</td><td>0.729 (±0.062)</td><td>171.35 (±10.45)</td></tr></table>

evidenced by Figure 4, FedRecon outperforms the unimodal baseline, FedOpt, by an average margin of  $2.81\%$  in F1- score under the high imbalance setting  $(\alpha = 0.1)$  across all missing ratios. In the low imbalance setting  $(\alpha = 5.0)$ , it maintains an average improvement of  $3.84\%$ , further demonstrating its robustness across varying data distributions.

PolyMNIST and CUB Results. As shown in the Table 3, we directly adopt the reported results of MMVAE  $^+$  as a reference benchmark, and further compare FedRecon against MMVM for a comprehensive evaluation. In terms of FID, FedRecon performs relatively poorly on the CUB dataset but yields satisfactory results on PolyMNIST. We speculate that this discrepancy stems from the difference in latent distribution characteristics: the Laplace distribution used for CUB is more challenging to map effectively, whereas the Gaussian distribution adopted for PolyMNIST aligns more naturally with our mapping mechanism. While the reconstruction quality is constrained by this setup, our method achieves sample- level alignment across modalities, which helps improve the overall data distribution and leads to a slight FID improvement over the original MVAE. Regarding generative coherence, since we achieve sample- level alignment, the accuracy of the classifier improves significantly, yielding superior results. FedRecon outperforms both MMVAE  $^+$  and MMVM, reaching state- of- the- art performance in this domain. Figure 5 and Figure 6 showcase several randomly selected crossmodal generation results on the PolyMNIST and the CUB test sets.

# 5.3 Ablation Studies

Contribution of Each Module. We first evaluate the impact of two key modules: the cross- modal alignment reconstruction generator (MMR) and the Global Generator Freezing Strategy (GGFS). To assess their contributions to model performance, we conduct experiments where these modules are excluded from FedRecon. The results underscore the critical role of these components in

![](images/0d187da7294f4873a4709fb5dfa075adb9cec38807d140faed827e5151f3c5de.jpg)  
Figure 7: Plot of the number of samples per class allocated to each client in CrisisMMD, with  $\alpha = 5.0$  and  $\alpha = 0.1$ .

![](images/69fd1839838acf2665237ac9d0ff811147373f8874e07c397f02c6002974213c.jpg)  
Figure 8: Ablation results on CrisisMMD under low label imbalance  $(\alpha = 0.1)$ .

enhancing overall effectiveness. As shown in Figure 8, FedRecon demonstrates a marked improvement over the baseline, highlighting the necessity of both MMR and GGFS for superior performance. Owing to space limitations, we exclude detailed results on CrisisMMD with  $\alpha = 5.0$  and the MELD dataset. Nonetheless, we briefly summarize the findings here. On CrisisMMD  $(\alpha = 5.0)$ , MMR and GGFS achieve average performance gains of  $1.47\%$  and  $0.88\%$  respectively across varying missing rates. For MELD, GGFS shows limited or even negative effects, while the MMR module consistently improves UAR by an average of  $2.10\%$ .

Impact of Modality- Aware Fusion. This component can be universally applied to all comparison methods by adaptively weighting the modalities before classification. To ensure a fair comparison in reconstructing missing modalities, we incorporate this mechanism across all baselines. Ablating this module yields divergent effects across datasets: on MELD, the UAR score increases by  $5\% - 6\%$ , whereas on CrisisMMD, the F1- score decreases by  $3\% - 4\%$ . These outcomes align with the observations reported in FedMultimodal, reflecting the nuanced influence of modality- aware fusion depending on dataset characteristics.

Rationale Behind GGFS. We hypothesize that certain clients may hold samples that are inherently difficult to reconstruct due to limitations in local data coverage. This assumption reflects scenarios where local distributions fail to capture the complexity of the global data space. As illustrated in Figure 7, this hypothesis is reasonable and supports the design motivation of GGFS.

![](images/757281ce2f81f1cf6a0a281be26ee7ab98ae02b12d6009935c5ed41f7a570b92.jpg)  
Figure 9: Examples of Text (m1) generated from Image (m6) on the CUB dataset, using encoder and decoder trained under the MMVAE+ framework.

![](images/50d3749c08c93a2a8b7ba349786a28f68e6e6a9718eeae267429cd40be643106.jpg)  
Figure 10: Examples of Text generated from Image on the CrisisMMD dataset, using the backbone trained with the MMVAE+ framework.

# 5.4 Further Analysis

In fact, the mapping mechanism we propose can be applied to any pre- trained VAE. By simply freezing the encoder and decoder, we can train a mapping model to adapt the latent distributions. Essentially, this method aligns the generated data across modalities, ultimately forming a complete data pair. We extend this approach to the architecture trained with MVAE, MMVAE and MMVAE+, and observe that this reconstruction method also facilitates sample alignment to some extent. As illustrated in the Figure 9 and Figure 10, we present generation results on both the CUB and CrisisMMD datasets using the MMVAE+ framework.

We further evaluate the effectiveness of text- to- image generation on the CrisisMMD dataset. The generated images tend to appear relatively blurry, which may be attributed to the high resolution of the original images, posing challenges for VAE- based synthesis. Given sufficient computational resources, employing diffusion models would likely offer a more suitable alternative.

# 6 Conclusion

In this paper, we tackle the challenge of missing modalities in the MFL paradigm, which often leads to feature distribution gaps and disrupted inter- modal correlations. We propose FedRecon, a novel framework that incorporates two key components: (1) a cross- modal reconstruction strategy that enables both label- level and sample- level imputation, and (2) a global generator freezing strategy designed to address the issue of unreconstructable samples in Non- IID settings with limited data. Extensive experiments across diverse scenarios demonstrate the effectiveness of FedRecon in enhancing both modality reconstruction and overall performance under Non- IID conditions, paving the way for more robust MFL systems.

# A A Survey on Generative Models

A A Survey on Generative ModelsTo assess the parameter sizes of various models, we conducted an extensive survey on platforms, including GitHub, Hugging Face, Papers with Code, focusing on representative architectures from three major types of generative models: GANs, VAEs, and Diffusion models. Specifically, we considered ACGAN [46], DeGAN [1], Seg_DeGAN [5], MADGAN [13], MAD- GAN [32], BEGAN [4], Boundary- Seeking GAN [17], StyleGAN2 [26] and DragGAN [50] from the GAN class, VAE [29], VQ- VAE [64], VAR [62], MVAE [68] and MMVAE+ [48] from the VAE class, as well as DDPM [18], DDPM- Seg [3], Tab- DDPM [30], Stable Diffusion [55] and Mini- EDM [25] from the Diffusion models. These models were selected for their prominence and diversity, and we sampled and computed their parameter sizes to facilitate a comparative analysis.

Table 4: Comparison of Model Parameters and File Sizes  

<table><tr><td>Category</td><td>Model</td><td>Parameters (M)</td><td>Size (MB)</td></tr><tr><td rowspan="9">GAN</td><td>ACGAN¹</td><td>6.64</td><td>24.53</td></tr><tr><td>DeGAN²</td><td>7.15</td><td>24.17</td></tr><tr><td>Seg-DeGAN³</td><td>7.15</td><td>24.19</td></tr><tr><td>MADGAN⁴</td><td>0.34</td><td>1.36</td></tr><tr><td>MAD-GAN⁵</td><td>1.05</td><td>3.96</td></tr><tr><td>BEGAN⁶</td><td>1.79</td><td>7.15</td></tr><tr><td>B-S GAN⁷</td><td>1.96</td><td>7.82</td></tr><tr><td>DragGAN⁸</td><td>59.26</td><td>347.17</td></tr><tr><td>StyleGAN²</td><td>59.26</td><td>347.10</td></tr><tr><td rowspan="6">VAE</td><td>Simple VAE¹⁰</td><td>0.21</td><td>0.80</td></tr><tr><td>VQ-VAE¹¹</td><td>0.26</td><td>1.00</td></tr><tr><td>VAR¹²</td><td>310.28</td><td>1185.55</td></tr><tr><td>VAE in MVAE</td><td>4.43</td><td>17.72</td></tr><tr><td>MVAE</td><td>22.16</td><td>84.63</td></tr><tr><td>MMVAE+¹³</td><td>22.16</td><td>84.63</td></tr><tr><td rowspan="5">Diffusion</td><td>DDPM¹⁴</td><td>35.87</td><td>136.85</td></tr><tr><td>DDPM-Seg¹⁵</td><td>2.20</td><td>8.41</td></tr><tr><td>Tab-DDPM¹⁶</td><td>1.44</td><td>5.77</td></tr><tr><td>Stable Diffusion¹⁷</td><td>1068.40</td><td>4372.48</td></tr><tr><td>Mini-EDM¹⁸</td><td>15.71</td><td>59.92</td></tr></table>

1 https://github.com/lukedeo/Leras-acgan 2 https://github.com/kucl/DeGAN 3 https://github.com/vaushal-pytorch-vegan 4 https://github.com/vinx-210%/MVAGAN-DEGAN 5 https://github.com/Guillen9/madgan-pytorch 6 https://github.com/eriklinder-noren/PyTorch-GAN 7 https://github.com/eriklinder-noren/PyTorch-GAN 8 https://github.com/XingangHan/DrugGAN 9 https://github.com/NVlabs/svylegan2 10 https://github.com/bojone/vme 11 https://github.com/Mishal.askin/vqvae 12 https://github.com/FoundationVision/VAR 13 https://github.com/epalu/mnvaeplus 14 https://github.com/zoubohac/DenoisingDiffusionProbabilityModel-ddpm- 15 https://github.com/yandex-research/ddpm-segmentation 16 https://github.com/yandex-research/tab-ddpm 17 https://github.com/CompVis/stable-diffusion 18 https://github.com/yuanzhi-zhu/mini_edm

As illustrated in Table 4, for repositories containing multiple model variants, we selected the smallest model for comparison to ensure fairness in parameter and size analysis. VAE exhibits a lightweight advantage over the other two model families, delivering competitive performance while maintaining significantly lower resource consumption. However, VAE also has its limitations. When adapted to multimodal tasks, the quality of the synthesized data often falls short compared to other generative models, exhibiting noticeable artifacts such as blurry edges in reconstructions [28, 65]. Regarding training time, since each model is designed for different tasks and configurations, a unified evaluation is challenging. Nevertheless, we conducted a comparative test using the most basic implementations of VAE, GAN, and diffusion models on the CIFAR- 10 [31] dataset. Among them, VAE demonstrated the fastest training speed.

# B Datasets

Datasets. We employ four multimodal datasets in our experiments, serving two distinct purposes. To evaluate the effectiveness of our multimodal federated learning framework, we use two real- world datasets: (1) MELD [52], a multiparty conversational corpus comprising over 9k utterances with both audio and textual transcripts from the TV series Friends, and (2) CrisisMMD [2], which contains  $18.1\mathrm{k}$  tweets with paired visual and textual information collected during various real- world crisis events. To further assess the generative quality of our MVAE model, we additionally adopt two benchmark multimodal datasets: (3) PolyMNIST [59], a synthetic dataset featuring five modalities, each constructed by compositing MNIST digits over diverse background images; and (4) CUB [66], the Caltech- UCSD Birds dataset, which pairs bird images with fine- grained textual descriptions, posing a challenging setting due to the nuanced modality- specific alignments.

Due to time constraints, we acknowledge certain limitations in our current evaluation. In particular, we did not integrate PolyMNIST and CUB into the federated learning setting, resulting in a gap between our generative evaluation and task- based distributed experiments. In future work, we plan to include these datasets in the federated learning pipeline to establish a more unified benchmark. This extension would enable a three- fold evaluation framework: (1) assessing the overall quality of the generated data across all four datasets, (2) examining the generation performance within a distributed setting to understand how well the model synthesizes data in federated environments, and (3) evaluating the utility of the generated data in downstream tasks to determine whether reconstructions can effectively support task- specific models.

Next, we provide additional details on how the four datasets were utilized in our experiments. Our experimental setup is primarily based on the federated configuration from [12]. For the MELD dataset, we applied a simplification by retaining only the four most frequent emotion categories: neutral, happy, sad, and angry, in order to reduce class imbalance and improve model convergence. For CrisisMMD, we adopted the original configuration and partitioned the data following the event- based distribution protocol described in prior work. Detailed statistics of these two datasets are provided in Tables 5 and 6.

As for the remaining two datasets, PolyMNIST and CUB, we followed the standard processing protocols established in previous

Table 5: MELD Statistics.  $\{\mathbf{a},\mathbf{v},\mathbf{t}\} =$  audio, visual, text  

<table><tr><td>MELD Statistics</td><td>Train</td><td>Dev</td><td>Test</td></tr><tr><td># of modalities</td><td>{a,v,t}</td><td>{a,v,t}</td><td>{a,v,t}</td></tr><tr><td># of unique words</td><td>10,643</td><td>2,384</td><td>4,361</td></tr><tr><td>Avg./Max utterance length</td><td>8.0/69</td><td>7.9/37</td><td>8.2/45</td></tr><tr><td># of dialogues</td><td>1039</td><td>114</td><td>280</td></tr><tr><td># of dialogues dyadic MELD</td><td>2560</td><td>270</td><td>577</td></tr><tr><td># of utterances</td><td>9989</td><td>1109</td><td>2610</td></tr><tr><td># of speakers</td><td>260</td><td>47</td><td>109</td></tr><tr><td>Avg. # of utterances per dialogue</td><td>9.6</td><td>9.7</td><td>9.3</td></tr><tr><td>Avg. # of emotions per dialogue</td><td>3.3</td><td>3.3</td><td>3.2</td></tr><tr><td>Avg./Max # of speakers per dialogue</td><td>2.7/9</td><td>3.0/8</td><td>2.6/8</td></tr><tr><td># of emotion shift</td><td>4003</td><td>427</td><td>1003</td></tr><tr><td>Avg. duration of an utterance</td><td>3.59s</td><td>3.59s</td><td>3.58s</td></tr></table>

work [56, 59, 66]. PolyMNIST is a synthetic dataset constructed from five image modalities. Each sample comprises five distinct images, each representing an MNIST digit placed over a randomly cropped background from five unique texture images- one per modality. All five images share the same digit label, which constitutes the shared information across modalities, while the style of handwriting and the background image vary across modalities, capturing modality- specific noise. The digit labels serve as the class labels in our experiments. CUB (Caltech- UCSD Birds) is a fine- grained dataset that pairs bird images with natural language descriptions. Each datapoint includes an image of a bird and one or more corresponding captions. This dataset poses a more realistic and challenging multimodal learning scenario due to the substantial amount of modality- specific information: the image conveys visual appearance details (e.g., plumage patterns), while the captions vary in focus and granularity, leading to varying degrees of semantic overlap between modalities.

Together, these four datasets allow us to evaluate both the quality and robustness of generative models under varying conditions of modality alignment and shared content. As illustrated in Figures 11, 12, 13, and 14, we present representative examples from the original datasets to better convey the nature and complexity of each modality. These examples help visualize the diversity of the multimodal inputs, as well as the varying levels of semantic overlap across modalities. The images are adapted from the MMVAE  $^+$  repository.

Table 6:Crisis Dataset Statistics  

<table><tr><td>Crisis name</td><td># tweets</td><td># images</td><td># filtered tweets</td><td># sampled tweets</td></tr><tr><td>Hurricane Irma</td><td>3,517,280</td><td>176,972</td><td>5,739</td><td>4,041 (4,525)</td></tr><tr><td>Hurricane Harvey</td><td>6,664,349</td><td>321,435</td><td>19,967</td><td>4,000 (4,443)</td></tr><tr><td>Hurricane Maria</td><td>2,953,322</td><td>52,231</td><td>6,587</td><td>4,000 (4,562)</td></tr><tr><td>California wildfires</td><td>455,311</td><td>10,130</td><td>1,488</td><td>1,486 (1,589)</td></tr><tr><td>Mexico earthquake</td><td>383,341</td><td>7,111</td><td>1,241</td><td>1,239 (1,382)</td></tr><tr><td>Iraq-Iran earthquake</td><td>207,729</td><td>6,307</td><td>501</td><td>499 (600)</td></tr><tr><td>Sri Lanka floods</td><td>41,809</td><td>2,108</td><td>870</td><td>832 (1,025)</td></tr><tr><td>Total</td><td>14,223,141</td><td>576,294</td><td>36,403</td><td>16,097 (18,126)</td></tr></table>

# C Reproducibility

Regarding the comparative experiments, all our unimodal baselines are based on implementations provided by FedMultimodal. For multimodal comparisons, we reproduced mmFedMC [75],CACMRN [69], and FedMMR [67], as none of these methods have publicly available code. In the following, we provide detailed explanations of how each of these baselines was implemented in our setup.

For mmFedMC, the original paper does not address the issue of modality completion, focusing instead on communication, aggregation, and client selection strategies. We followed the formulas and procedures described in the paper, preserving the ensemble model setup and uploading the task model for evaluation. Clients are ranked based on a combination of Shapley values and task loss, and the final model is aggregated accordingly.

For CACMRN, we adopted their Transformer- based reconstruction module. However, since FedMultimodal provides pre- extracted modality features, we directly applied the reconstruction mechanism to these features without incorporating an additional feature extractor. Specifically, we implemented the Normalized SelfAttention (NSA) mechanism as described in the original work. In NSA, both the input features and the projection matrices  $(W_{Q},W_{K},W_{V})$  are L2- normalized, projecting them onto a hyperspherical space. This operation treats the parameters as cluster centers of the data distribution. Attention scores are computed between instances to adaptively update the projection matrices that are most relevant to the local data, thereby mitigating overfitting risks. Clients with complete multimodal data train the reconstruction model, optimized using three loss functions: Cycle- Consistency Loss  $(L_{CM})$  minimizes MSE between original and reconstructed features in both directions; Semantic Consistency Loss  $(L_{SCL})$  aligns features by minimizing KL divergence in classification outputs; and Divergence Loss  $(L_{DI})$  encourages diversity in projection matrices to enhance self- attention. We followed the original experimental hyperparameters for all these components to ensure a faithful reproduction of CACMRN's reconstruction behavior within our federated setting.

As for FedMMR, the original paper did not specify the exact architecture used for its generator. Therefore, we implemented a generator based on standard GAN architectures and followed similar experimental settings. Since the original experiments were conducted on image and audio datasets and focused on reconstructing a single missing modality, we adopted the same setup in our reproduction.

# D Degradation of MVAE

D Degradation of MVAEWe train our MVAE generator using complete modality data. However, as the modality missing rate increases, the number of complete modality samples decreases, leading to a decline in modality alignment capability. When the missing rate reaches  $100\%$ , MVAE degenerates into several unimodal VAEs. In our experimental setup, due to the distributional issues in modality partitioning, reconstruction remains a challenge. Specifically, when a single federated learning client experiences  $100\%$  modality missing, reconstruction becomes exceedingly difficult. This is where GGFS (Global Generator Freezing Strategy) comes into play, effectively preventing full degeneration by ensuring stable generative performance even under severe modality loss.

![](images/6686f57cbe71a73e7b9475a74081dfb32d3be0e86af72ae0c7f995e81dc23e78.jpg)  
Figure 11: An example from the MELD dataset, illustrating multimodal data in emotional dialogue analysis.

![](images/8c7ce1a50feef580df33855c9ac81d695fab6a488d7a154988bc619a1333c62b.jpg)  
Figure 12: An example from the CrisisMMD dataset, showing multimodal data used for crisis situation modeling.

![](images/7bc5d60480f7e7a58d56440d8325194af64ab1f3e0e6bc8bd8a3ec76fe0573ac.jpg)  
Figure 13: An example from the PolyMNIST dataset, illustrating five synthetic modalities where each digit shares the same label but varies in background and handwriting style.

![](images/71048504336bb6e33d9dd1025df1064ee63288f82319ae6fdd751ba392198171.jpg)  
Figure 14: An example from the CUB dataset, showing bird images paired with fine-grained textual descriptions, highlighting the modality-specific nuances in visual and linguistic features.

# E Limitations

One limitation of our method lies in the difficulty of achieving stable convergence for the mapping model. Interestingly, we observed conceptual similarities between our design and the training paradigm adopted in LLaVA [40], which inspired us to adapt their training methodology. This proved beneficial—particularly for challenging datasets such as CrisisMMD, where we incorporated KL regularization to align the distributions of the two latent variables. This approach partially alleviated convergence issues.

We also experimented with a more direct solution by explicitly matching the means and variances of the latent distributions. While this method succeeded in narrowing the gap in statistical moments, the alignment was still imperfect and did not fully resolve the discrepancy in latent space structure. Notably, we observed that variance alignment was relatively easier to achieve, whereas the means tended to be less sensitive and exhibited smaller shifts. We acknowledge these training instabilities as an area for improvement and plan to refine our optimization strategy in future work, with the aim of enhancing the robustness and reliability of FedRecon.

# F Tricks

We provide a potentially effective training trick: differentiating the latent distributions of different modalities. For instance, one could assign a Gaussian prior to modality A and a Laplacian prior to modality B. Alternatively, even if both modalities follow Gaussian distributions, their means and variances can be deliberately set to differ. As noted in4, the standard normal distribution is often the optimal default choice in the absence of distributional knowledge, due to its trainability and capacity to approximate diverse data distributions. However, if prior knowledge of the modality- specific distribution is available, we hypothesize that imposing distinct priors across modalities can enhance generative diversity and modality differentiation, ultimately improving generative coherence.

We conducted preliminary experiments across all four datasets using this trick, but the results exhibited high variance. We believe that with appropriately curated datasets or more stable training settings, this approach holds promise and could be practically beneficial.

# G Visualazation

To qualitatively assess the effectiveness of our reconstruction framework, we visualize generated samples obtained by reusing the encoder and decoder from MMVAE  $^+$  , followed by our trained modalitymapping network. These visualizations demonstrate how well our mapping model can align and reconstruct missing modalities based on available inputs.

In certain scenarios, the mapping achieves noticeably better cross- modal alignment, yielding more coherent and semantically consistent samples. This highlights the model's ability to preserve modality- specific characteristics while capturing shared semantic content across modalities. Figures 15- 23 showcases representative examples from different datasets to illustrate the alignment quality and generative fidelity.

![](images/5a8a8308bd2f8c44c21021784c6a53a2ac3480ec5919604bf4e8e52729aa275f.jpg)  
Figure 15: Visualization example 1: Unconditional self-reconstruction using the MMVAE+ encoder and decoder.

Figure 16:Visualization example 2: Unconditional selfreconstruction using the MMVAE  $^+$  encoder and decoder.  

<table><tr><td colspan="35">Figure 16: Visualization example 2: Unconditional self-reconstruction using the MMVAE+ encoder and decoder.</td></tr><tr><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td></tr><tr><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td></tr></table>

![](images/0c6ce0e7a7b66f120d1aa5060aa834b18437ec67f36583ea733541d124a5a46a.jpg)  
Figure 17: Visualization example 3: Cross-modal generation using our trained mapping network and MMVAE+ backbone.

![](images/5ed7b808ac23e6aa72323af7d577f69d243aa4521d8fe554a50ed5d8a0256440.jpg)

![](images/5fc8ee084f300046dd1aebe68edd915f94c7dd469976b2e23adebbf5fd3efada.jpg)  
Figure 19: Visualization example 5: Cross-modal generation using our trained mapping network and MMVAE+ backbone.

![](images/fc466affd917b565623e1eb0c74360dc2b9fc3944eff3fefca8a2b8a860d1869.jpg)  
Figure 18: Visualization example 4: Cross-modal generation using our trained mapping network and MMVAE+ backbone.  
Figure 20: Visualization example 6: Cross-modal generation using our trained mapping network and MMVAE+ backbone.

![](images/eae6b40c2741e8b64c7228a1fd4df5b859a99fd165bd5baf1329f78cef2071d3.jpg)  
Figure 21: Visualization example 7: Cross-modal generation using our trained mapping network and MMVAE+ backbone.

![](images/5049ed067baba0376d5865ae41d2880ffc16e0d3628aa86afd75239405fb5557.jpg)  
Figure 22: Visualization example 8: Cross-modal generation using our trained mapping network and MMVAE+ backbone.

![](images/47d9c3d7d7a8ddf586eb29c0c642777370e27b50acb1df6eced8bc95e0e31cd8.jpg)  
Figure 23: Visualization example 9: Cross-modal generation using our trained mapping network and MMVAE+ backbone.

# References

[1] Sravanti Addepalli, Gaurav Kumar Nayak, Anirban Chakraborty, and Venkatesh Babu Radhakrishnan. 2019. DeGAN : Data- Enriching GAN for Retrieving Representative Samples from a Trained Classifier. In Proceedings of the AAAI Conference on Artificial Intelligence. doi:10.48550/arXiv.1912.11960 [2] Firoj Alam, Ferda Ofli, and Muhammad Imran. 2018. CrisisMMD: Multimodal Twitter Datasets from Natural Disasters. Proceedings of the International AAAI Conference on Web and Social Media 12, 1 (Jun. 2018). doi:10.1609/icwsm.v12i1. 14983 [3] Dmitry Baranchuk, Ivan Rubachev, Andrey Voynov, Valentin Krulkov, and Artem Babenko. 2021. Label- efficient semantic segmentation with diffusion models. arXiv preprint arXiv:2112.03126 (2021). [4] David Berthelot, Thomas Schumm, and Luke Metz. 2017. Began: Boundary equilibrium generative adversarial networks. arXiv preprint arXiv:1703.10717 (2017). [5] Kaushal Bhogale. 2020. Data- free knowledge distillation for segmentation using data- enriching gan. arXiv preprint arXiv:2011.00809 (2020). [6] Liwei Che, Jiaqi Wang, Yao Zhou, and Fenglong Ma. 2023. Multimodal Federated Learning: A Survey. Sensors 13, 15 (2023). doi:10.3390/s23156986 [7] Jiayi Chen and Aindong Zhang. 2024. FedMBridge: Bridgeable Multimodal Federated Learning. In Forty- first International Conference on Machine Learning. https://openreview.net/forum?id=jrHUbftLd6 [8] Minghui Chen, Ruinan Jin, Wenlong Deng, Yuanyuan Chen, Zhi Huang, Han Yu, and Xiaoxiao Li. 2025. Can Textual Gradient Work in Federated Learning?. In The Thirteenth International Conference on Learning Representations. https://openreview.net/forum?id=6y5IKyVbR3 [9] Henggang Cui, Vladan Radosavljevic, Fang- Chieh Chou, Tsung- Han Lin, Thi Nguyen, Tzu- Kuo Huang, Jeff Schweder, and Nemanja Djuric. 2019. Multimodal Trajectory Predictions for Autonomous Driving using Deep Convolutional Networks. In 2019 International Conference on Robotics and Automation (ICRA). 2090- 2096. doi:10.1109/ICRA.2019.8793868 [10] Sen Cui, Abudukelimu Wuerkaiyixi, Weishen Pan, Jian Liang, Lei Fang, Changshui Zhang, and Fei Wang. 2024. CLAP: Collaborative Adaptation for Patchwork Learning. In The Twelfth International Conference on Learning Representations. https://openreview.net/forum?id=8yRkdQ3j2 [11] Steven Davis and Paul Mermelstein. 1980. Comparison of parametric representations for monosyllabic word recognition in continuously spoken sentences. IEEE Transactions on Acoustics, Speech, and Signal Processing 28, 4 (1980), 357- 366. doi:10.1109/IASSP.1980.1163420 [12] Tiantian Feng, Digbalay Bose, Tuo Zhang, Rajat Hebbar, Anil Ramakrishna, Rahul Gupta, Mi Zhang, Salman Avestimehr, and Shrikanth Narayanan. 2023. FedMultimodal A Benchmark for Multimodal Federated Learning. In Proceedings of the 29th ACM SIGKDD Conference on Knowledge Discovery and Data Mining (Long Beach, CA, USA) (KDL '23). Association for Computing Machinery, New York, NY, USA, 4035- 4045. doi:10.1145/3580305.3599825 [13] Arnab Ghosh, Viveka Kuharia, Vinay P. Namboodiri, Philip H.S. Torr, and Puneet K. Dokania. 2018. Multi- Agent Diverse Generative Adversarial Networks. In Proceedings of the IEEE Conference on Computer Vision and Pattern Recognition (CVPR). [14] I. Goodfellow, J. Pouget- Abadie, M. Mirza, B. Xu, D. Warde- Farley, S. Ozair, A. Courville, and Y. Bengio. 2014. Generative Adversarial Nets. In Advances in Neural Information Processing Systems, Z. Ghahramani, M. Welling, C. Cortes, N. Lawrence, and K.Q. Weinberger (Eds.), Vol. 27. Curran Associates, Inc. https://proceedings.neurips.cc/paper_files/paper/2014/file/5ca3e9b122f61f8f06494c97b1a6fccf3- Paper.pdf [15] Javaria Hassan, Jovin Leong, and Bertrand Schneider. 2021. Multimodal Data Collection Made Easy: The LZ- MMLA Toolkit: A data collection website that provides educators and researchers with easy access to multimodal data streams. In LAK21: 11th International Learning Analytics and Knowledge Conference (Irvine, CA, USA). (LAK21). Association for Computing Machinery, New York, NY, USA, 579- 585. doi:10.1145/3448139.3448201 [16] Martin Heusel, Hubert Ramsauer, Thomas Unterthiner, Bernhard Nessler, and Sepp Hochreiter. 2017. GANs Trained by a Two Time- Scale Update Rule Converge to a Local Nash Equilibrium. In Advances in Neural Information Processing Systems, I. Guyon, U. Von Luxburg, S. Pengio, H. Wallach, R. Fergus, S. Vishwanathan, and R. Garnett (Eds.), Vol. 30. Curran Associates, Inc. https://proceedings.neurips.cc/paper_files/paper/2017/file/8a1d694707eb0efe65871369074926d- Paper.pdf [17] R. Devon Hjelm, Athul Paul Jacob, Tong Che, Adam Trischler, Kyunghyun Cho, and Yoshua Bengio. 2017. Boundary- seeking generative adversarial networks. arXiv preprint arXiv:1702.08431 (2017). [18] Jonathan Ho, Ajay Jain, and Pieter Abbeel. 2020. Denoising Diffusion Probabilistic Models. In Advances in Neural Information Processing Systems, H. Larochelle, M. Ranzato, R. Hadsell, M.F. Balcan, and H. Lin (Eds.), Vol. 33. Curran Associates, Inc., 6840- 6851. https://proceedings.neurips.cc/paper_files/paper/2020/file/4c5bfcfec8584a0f0967f1ab0179ca4b- Paper.pdf [19] Andrew G Howard, Menglong Zhu, Bo Chen, Dmitry Kalenichenko, Weijun Wang, Tobias Weyand, Marco Andreetto, and Hartwig Adam. 2017. Mobilenets:

Efficient convolutional neural networks for mobile vision applications. arXiv preprint arXiv:1704.04861 (2017). [20] Weip Huang, Andi Han, Yongqiang Chen, Yuan Cao, Zhiqiang Xu, and Taiji Suzuki. 2024. On the Comparison between Multi- modal and Single- modal Contrastive Learning. In Advances in Neural Information Processing Systems, A. Globerson, L. Mackey, D. Belgrave, A. Fan, U. Paquet, J. Tomczak, and C. Zhang (Eds.), Vol. 37. Curran Associates, Inc., 81549- 81605. https://proceedings.neurips.cc/paper_files/paper/2024/file/948404c1497ead6a84f93f02Debaika- Paper- Conference.pdf [21] Wei Huang, Dexian Wang, Xiaocao Ouyang, Jilong Wan, Jia Liu, and Tianrui Li. 2024. Multimodal federated learning: Concept, methods, applications and future directions. Information Fusion 112 (2024), 102576. doi:10.1016/j.inffus.2024.102576 [22] Yu Huang, Chenzhuang Du, Zihui Xue, Xuanyao Chen, Hang Zhao, and Longbo Huang. 2021. What Makes Multi- Modal Learning Better than Single (Provably). In Advances in Neural Information Processing Systems, M. Bagnato, A. Bougelprimer, Y. Dauphin, P.S. Liang, and J. Wortman Vaughan (Eds.), Vol. 34. Curran Associates, Inc., 10944- 10956. https://proceedings.neurips.cc/paper_files/paper/2021/file/5aa3045a3f865c10f4204a7b55cbff3- Paper.pdf [23] HyeongJoo Hwang, Geon- Hyeong Kim, Seunghoon Hong, and Kee- Eung Kim. 2021. Multi- View Representation Learning via Total Correlation Objective. In Advances in Neural Information Processing Systems, M. Ranzato, A. Beygelzimer, Y. Dauphin, P.S. Liang, and J. Wortman Vaughan (Eds.), Vol. 34. Curran Associates, Inc., 12194- 12207. https://proceedings.neurips.cc/paper_files/paper/2021/file/65a99bba311515dede20da98b08a370f- Paper.pdf [24] Sai Praneth Karimireddy, Satyer Kale, Mehrray Mohri, Sashank Reddi, Sebastian Stich, and Ananda Theyen Tha Suresh. 2020. SCAFFOLD: Stochastic Controlled Averaging for Federated Learning. In Proceedings of the 37th International Conference on Machine Learning (Proceedings of Machine Learning Research, Vol. 119), Hal Daume III and Aarti Singh (Eds.), PMLR, 5132- 5143. https://proceedings.mlr.press/v119/karimireddy20a.html [25] Tero Karras, Milka Aittala, Timo Aila, and Samuli Laine. 2022. Elucidating the Design Space of Diffusion- Based Generative Models. In Advances in Neural Information Processing Systems, S. Koyejo, S. Mohamed, A. Agarwal, D. Belgrave, K. Cho, and A. Oh (Eds.), Vol. 35. Curran Associates, Inc., 26565- 26577. https://proceedings.neurips.cc/paper_files/paper/2022/file/a98846e949c61cf88b5e694d946ce6b- Paper Conference, pdf [26] Tero Karras, Samuli Laine, Milka Aittala, Jan Hellssten, Jaakko Lehtinen, and Timo Aila. 2020. Analyzing and Improving the Image Quality of StyleGAN. In Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition (CVPR). [27] Ramandeep Kaur and Sandeep Kautish. 2019. Multimodal sentiment analysis: A survey and comparison. International Journal of Service Science, Management, Engineering, and Technology (IJSSMET) 10, 2 (2019), 38- 58. [28] Amirhossein Kazzerouni, Ehsan Khodapanan, Aghdam, Moein Heidari, Reza Azad, Mohsen Fayyaz, Ilker Hacihalliloglu, and Dorn Merhof. 2022. Diffusion models for medical image analysis: A comprehensive survey. arXiv preprint arXiv:2211.07804 (2022). [29] Diederik P Kingma, Max Welling, et al. 2014. Auto- Encoding Variational Bayes. In International Conference on Learning Representations (ICLR). https://arxiv.org/abs/1312.6114 [30] Akim Kotelnikov, Dmitry Baranchuk, Ivan Rubachev, and Artem Babenko. 2023. Tabdppm: Modelling tabular data with diffusion models. In International Conference on Machine Learning. PMLR, 17564- 17579. [31] Alex Krizhevsky, Geoffrey Hinton, et al. 2009. Learning multiple layers of features from tiny images. (2009). [32] Dan Li, Dacheng Chen, Baihong Jin, Lei Shi, Jonathan Goh, and See- Kiong Ng. 2019. MAD- GAN: Multivariate anomaly detection for time series data with generative adversarial networks. In International conference on artificial neural networks. Springer, 703- 716. [33] Li Li, Yuxi Fan, Mike Tse, and Kuo- Yi Lin. 2016. A review of applications in federated learning. Computers & Industrial Engineering 149 (2020), 106854. doi:10.1016/j.cie.2020.106854 [34] Qiushi Li, Wenwu Zhu, Chao Wu, Xinglin Pan, Fan Yang, Yuezhi Zhou, and Yaoxue Zhang. 2020. InvisibleFL: Federated Learning over Non- Informative Intermediate Updates against Multimedia Privacy Leakages. In Proceedings of the 28th ACM International Conference on Multimedia (Seattle, WA, USA) (MM 20). Association for Computing Machinery, New York, NY, USA, 753- 762. doi:10.1145/3394171.3413923 [35] Shuai Li, Fan Qi, Zixin Zhang, and Changzheng Xu. 2024. Cross- Modal Meta Consensus for Heterogeneous Federated Learning. In Proceedings of the 32nd ACM International Conference on Multimedia (Melbourne VIC, Australia) (MM 24). Association for Computing Machinery, New York, NY, USA, 975- 984. doi:10.1145/366447.3681510 [36] Tian Li, Amit Kumar Sahu, Manzil Zaheer, Maziar Sanjabi, Ameet Talwalkar, and Virginia Smith. 2020. Federated Optimization in Heterogeneous Networks. In Proceedings of Machine Learning and Systems, I. Dhillon, D. Papailiopoulos, and V. Sze (Eds.), Vol. 2. 429- 450. https://proceedings.mlsys.org/paper_files/paper/2020/file/1f5fe83998a09396ebe6477d9475ba0c- Paper.pdf

[37] Xuelong Li, Di Hu, and Feipoing Nie. 2017. Deep Binary Reconstruction for Cross- modal Hashing. In Proceedings of the 25th ACM International Conference on Multimedia (Mountain View, California, USA) (MM '17). Association for Computing Machinery, New York, NY, USA, 1398- 1406. doi:10.1145/3123266.3123355[38] Xin- Chun Li and De- Chu, Zhan. 2021. FedRS: Federated Learning with Restricted Softmax for Label Distribution Non- IID Data. In Proceedings of the 27th ACM SIGKDD Conference on Knowledge Discovery & Data Mining (Virtual Event, Singapore) (KDD '21). Association for Computing Machinery, New York, NY, USA, 995- 1005. doi:10.1145/344438.3466752[39] Yi- Ming Lin, Yuan Gao, Mao- Guo Gong, Si- Jia Zhang, Yuan- Qiao Zhang, and Zhi- Yuan Li. 2023. Federated learning on multimodal data: A comprehensive survey. Machine Intelligence Research 20, 4 (2023), 539- 553. [40] Haotian Liu, Chunyuan Li, Qingyang Wu, and Yong Jae Lee. 2023. Visual Instruction Tuning. In Advances in Neural Information Processing Systems, A. Oh T. Naumann, A. Globerson, K. Saenko, M. Hardt, and S. Levine (Eds.), Vol. 36. Curran Associates, Inc., 34892- 34916. https://proceedings.neurips.cc/paper_files/paper/2023/file/6dcf277a32ce328891faf4369f6ede0- Paper- Conference.pdf[41] Junming Liu, Yanting Gao, Siyuan Meng, Yifei Sun, Aoqi Wu, Yufei Jin, Yirong Chen, Ding Wang, and Guoshin Zeng. 2025. Mosaic: Data- Free Knowledge Distillation via Mixure- of- Exports for Heterogeneous Distributed Environments. arXiv preprint arXiv:2505.1969v2(2025).[42] Junming Liu, Siyuan Meng, Yanting Gao, Song Mao, Pinlong Cai, Guohang Yan, Yirong Chen, Zilin Bian, Botian Shi, and Ding Wang. 2025. Aligning Vision to Language: Text- Free Multimodal Knowledge Graph Construction for Enhanced LLMs Reasoning. arXiv preprint arXiv:2503.12972 (2025).[43] Yi Liu, Cong Wang, and Xingliang Yuan. 2025. FedMobile: Enabling Knowledge Contribution- aware Multi- modal Federated Learning with Incomplete Modalities. arXiv preprint arXiv:2502.15899 (2025).[44] Mengmeng Ma, Jian Ren, Long Zhao, Sergey Tulyakov, Cathy Wu, and Xi Peng. 2021. SMIL: Multimodal Learning with Severely Missing Modality. Proceedings of the 16AAI Conference on Artificial Intelligence 35, 3 (May 2021), 2302- 2310. doi:10.1609/aaaai.v35i3.16330[45] Brendan McMahan, Eider Moore, Daniel Ramage, Seth Hampson, and Blaise Aguera y Arcas. 2017. Communication- Efficient Learning of Deep Networks from Decentralized Data. In Proceedings of the 20th International Conference on Artificial Intelligence and Statistics (Proceedings of Machine Learning Research, Vol. 54), Aarti Singh and Jerry Zhu (Eds.). PMLR, 1273- 1282. https://proceedings.mlr.press/v54/mcahan17a1. html[46] Vogelander, O. J. Schleifer, O. Thak, and Jonathan Silke. 2017. Confational Image Synthesis with Auxiliary Classifier GANs. In Proceedings of the 34th International Conference on Machine Learning (Proceedings of Machine Learning Research, Vol. 70), Doin Presup70/odena17a.html[47] Ferda Ofli, Rizwan Chaudhry, Gregorij Kurillo, Rene Vidal, and Ruzena Bajcsy. 2013. Berkeley MHAD: A comprehensive Multimodal Human Action Database. In 2013 IEEE Workshop on Applications of Computer Vision (WACV). 53- 60. doi:10.1109/WACV.2013.6474999[48] Emanuele Palumbo, Imam Daunhawer, and Julia E Vogt. 2023. MMVAE+: Enhancing the Generative Quality of Multimodal VAEs without Compromises. In The Eleventh International Conference on Learning Representations. https://openreview.net/forum?id=sdQGxouELX[49] Emanuele Palumbo, Laura Manduchi, Sonia Laguna, Daphné Chopard, and Julia E Vogt. 2024. Deep Generative Clustering with Multimodal Diffusion Variational Autoencoders. In The Twelfth International Conference on Learning Representations. https://openreview.net/forum?id=ksTHrhxDV3[50] Xingang Pan, Ayush Tewari, Thomas Leimkühler, Lingjie Liu, Abhimitra Meka, and Christian Theobalt. 2023. Drag Your CAN: Interactive Point- based Manipulation on the Generative Image. In研究成果 IN: ACM SIGGRAPH 2023 Conference Proceedings (Los Angeles, CA, USA/SIGGRAPH '23). Association for Computing Machinery, New York, NY, USA, Article 78, 11 pages. doi:10.1145/3588432.3591500[51] Soujanya Poria, Erik Cambria, Rajiv Bajpai, and Amir Hussain. 2017. A review of affective computing: From unimodal analysis to multimodal fusion. Information Fusion 37 (2017), 98- 125. doi:10.1016/j.inffus.2017.02.003[52] Soujanya Poria, Devanathy Hazzarka, Navonh Majumder, Gautam Naik, Erik Cambria, and Rada Mihalcea. 2018. Meld: A multimodal multi- party dataset for emotion recognition in conversations. arXiv preprint arXiv:1810.02508 (2018).[53] Yangjie Qin, Ming Li, and Jia Zhu. 2023. Privacy- preserving federated learning framework in multimedia courses recommendation. Wireless Networks 29, 4 (2023), 1535- 1544. doi:10.1007/s11276- 021- 02854- 1[54] Sashank Reddi, Zachary Charles, Manzil Zaheer, Zachary Garrett, Keith Rush, Jakub Konecny, Sanjiv Kumar, and H Brendan McMahan. 2020. Adaptive federated optimization. arXiv preprint arXiv:2003.00295 (2020).[55] Robin Rombach, Andreas Blattmann, Dominik Lorenz, Patrick Esser, and Bjorn Ommer. 2022. High- resolution image synthesis with latent diffusion models. In Proceedings of the IEEE/CVF Conference on computer vision and pattern recognition. 10684- 10695.

[56] Yuge Shi, Siddharth N, Brooks Paige, and Philip Torr. 2019. Variational Mixtures of- Experts Autoencoders for Multi- Modal Deep Generative Models. In Ad- . vances in Neural Information Processing Systems, H. Wallach, H. Larochelle, A. Beygelzimer, F. dAlche- Bu, E. Fox, and R. Garnett (Eds.), Vol. 32. Curran Associates, Inc. https://preceed.com/papers.pdf[57] Zhiqing Sun, Hongkun Yu, Xiaodan Song, Benjie Liu, Yiming Yang, and Denny Zhou. 2020. Mobilebert: a compact task- agnostic bert for resource- limited devices. arXiv preprint arXiv:2004.02948 (2020).[58] Thomas Sutter, Imant Daunhawer, and Julia Vogt. 2020. Multimodal Generative Learning Utilizing Jensen- Shannon- Divergence. In Advances in Neural Information Processing Systems, H. Larochelle, M. Ranzato, R. Hadsell, M.F. Balcan, and H. Lin (Eds.), Vol. 33. Curran Associates, Inc., 6100, 6110. https://proceedings.neurips.cc/paper_file/paper/2020/file/43bb733c1b62a5e374c3cb22fa457b4- Paper.pdf[59] Thomas M Sutter, Imant Daunhawer, and Julia E Vogt. 2021. Generalized multimodal ELBO. arXiv preprint arXiv:2105.02470 (2021).[60] Thomas M Sutter, Imant Daunhawer, and Julia E Vogt. 2021. Generalized multimodal ELBO. arXiv preprint arXiv:2105.02470 (2021).[61] Thomas M Sutter, Yang Meng, Andrea Agostini, Daphne Chopard, Norbert Fortin, Julia E. Vogt, Babak Shahbbah, and Stephan Mandt. 2024. Unity by Diversity: Improved Representation Learning for Multimodal VAEs. In Advances in Neural Information Processing Systems, A. Globerson, L. Mackey, D. Belgrave, A. Fan, U. Paquet, J. Tomczak, and C. Zhang (Eds.), Vol. 37. Curran Associates, Inc., 74262- 74297. https://preceedings.neurips.cc/paper_files/paper/2024/file/87726969ce38e9a676ca1fd4459ba77d- Paper- Conference.pdf[62] Keyu Tian, Yi Jiang, Zehuan Yuan, Bingyue Peng, and Liwei Wang. 2024. Visual Autoregressive Modeling: Scalable Image Generation via Next- Scale Prediction. In Advances in Neural Information Processing Systems, A. Globerson, L. Mackey, D. Belgrave, A. Fan, U. Paquet, J. Tomczak, and C. Zhang (Eds.), Vol. 37. Curran Associates, Inc., 84839- 84865. https://preceedlings.neurips.cc/paper_files/paper/2024/file/9a2e28481877662681440ba15c416bb- Paper- Conference.pdf[63] Stef Van Buuren and Stef Van Buuren. 2012. Flexible imputation of missing data. Vol. 10. CRC press Boca Raton, FL.[64] Aaron van den Oord, Oriol Vinvals, and koray kavukcuoglu. 2017. Neural Discrete Representation Learning. In Advances in Neural Information Processing Systems, I. Guyon, U. Von Luxburg, S. Bengio, H. Wallach, R. Fergus, S. Vishwanathan, and R. Garnett (Eds.), Vol. 30. Curran Associates, Inc. https://proceedings.neurips.cc/paper_file/2017/01/01- 01- 01- 01- 01- 01- 01- 01- 01- 01- 01- 01- 01- 01- 01- 01- 01- 01- 01- 01- 01- 01- 01- 01- 01- 01- paper- Labekanathan (Eds.), Vol. 30. Curran Associates, Inc. https://preceedlings.neurips.cc/sanchayan Vivekanathan. 2024. Comparative analysis of generative models: Enhancing image synthesis with vaes, gans, and stable diffusion. arXiv preprint arXiv:2408.08751 (2024).[66] Catherine Wah, Steve Branson, Peter Welinder, Pietro Perona, and Serge Belongie. 2011. The caltech- ucsd birds- 200- 2011 dataset. (2011).[67] Shu Wang, Zhe Qu, Yuan Liu, Shichao Kan, Yixiong Liang, and Jianxin Wang. 2024. FedMRI: Multi- Modal Federated Learning via Missing Modality Reconstruction. In 2024 IEEE International Conference on Multimedia and Expo (ICME). 1- 6. doi:10.1109/ICME57554.2024.10688162[68] Mike Wu and Noah Goodman. 2018. Multimodal Generative Models for Scalable Weakly- Supervised Learning. In Advances in Neural Information Processing Systems, S. Bengio, H. Wallach, H. Larochelle, K. Grauman, N. Cesa- Bianchi, and R. Garnett (Eds.), Vol. 31. Curran Associates, Inc. https://proceedings.neurips.cc/paper_files/paper/2018/01/10/102a3265d77c9004f3c89d0e889w- Paper.pdf[69] Baochen Xiong, Xiaoshan Yang, Yuguang Song, Songyeo Wei Wang, and Changsheng Xu. 2023. Client- Adaptive Cross- Model Reconstruction Network for Modality- Incomplete Multimodal Federated Learning. In Proceedings of the 21st ACM International Conference on Multimedia (Ottawa ON, Canada) (MM '23). Association for Computing Machinery, New York, NY, USA, 1241- 1249. doi:10.1145/3581783.3611757[70] Hu Xiong, Hang Yan, Mohammad S. Obaidat, Jingxue Chen, Mingsheng Cao, Sachin Kumar, Kadambril Agarwal, and Saruh Kumari. 2024. Efficient and Privacy- Enhanced Asynchronous Federated Learning for Multimedia Data in Edge- based IoT. ACM Trans. Multimedia Comput. Commun. Appl. (Aug. 2024). doi:10.1145/3088802 Just Accepted.[71] Yunlu Yan, Hong Wang, Yawen Huang, Namun He, Lei Zhu, Yong Xu, Yuexiang Li, and Yefeng Zheng. 2024. Cross- Modal Vertical Federated Learning for MRI Reconstruction. IEEE Journal of Biomedical and Health Informatics 28, 11 (2024), 6384- 6394. doi:10.1109/JBHI.2024.3360720[72] Hao Yu, Xin Yang, Lei Zhang, Hanlin Gu, Tianrui Li, Lixin Fan, and Qiang Yang. 2024. Addressing Spatial- Temporal Data Heterogeneity in Federated Continual Learning via Tail Anchor. arXiv preprint arXiv:2412.18355 (2024).[73] Qiying Yu, Yang Liu, Yimu Wang, Ke Xu, and Jingjing Liu. 2023. Multimodal Federated Learning via Contrastive Representation Ensemble. In The Eleventh International Conference on Learning Representations. https://openreview.net/forum?id=Hnk1WRMAQg[74] Songcan Yu, Junbo Wang, Walid Hussein, and Patrick C.K. Hung. 2024. Robust multimodal federated learning for incomplete modalities. Computer Communications 214 (2024), 234- 243. doi:10.1016/j.comcom.2023.12.003

[75] Liangqi Yuan, Dong- Jun Han, Su Wang, Devesh Upadhyay, and Christopher G Brinton. 2024. Communication- efficient multimodal federated learning: Joint modality and client selection. arXiv preprint arXiv:2401.16685 (2024).[76] Yuchen Zhao, Payam Barnaghi, and Hamed Haddadi. 2022. Multimodal Federated Learning on IoT Data. In 2022 IEEE/ACM Seventh International Conference

[77] Yongshuo Zong, Oisin Mac Aodha, and Timothy Hospedales. 2024. Self- Supervised Multimodal Learning: A Survey. IEEE Transactions on Pattern Analysis and Machine Intelligence (2024), 1–20. doi:10.1109/TPAMI.2024.3429301