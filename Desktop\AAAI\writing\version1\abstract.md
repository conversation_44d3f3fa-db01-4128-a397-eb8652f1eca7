# TrustGuard: A Multi-Dimensional Trustworthiness Framework with Cross-Modal Defense for Multimodal Federated Learning in Healthcare

## Abstract

Federated Learning (FL) has emerged as a promising paradigm for collaborative model training across healthcare institutions while preserving data privacy. However, the integration of multimodal data in federated settings introduces unprecedented challenges in ensuring trustworthiness across multiple dimensions including privacy, security, robustness, fairness, and explainability. Existing approaches typically address these concerns in isolation, lacking a unified framework that comprehensively guarantees trustworthiness in multimodal federated learning environments. Moreover, recent studies have revealed significant vulnerabilities in medical multimodal systems, where cross-modal attacks can achieve high success rates, highlighting the urgent need for robust defense mechanisms. To address these challenges, we propose TrustGuard, a novel trustworthy multimodal federated learning framework specifically designed for healthcare applications. TrustGuard introduces a multi-dimensional trustworthiness evaluation system that holistically assesses privacy preservation, security robustness, fairness guarantee, and explainability through unified metrics. To counter sophisticated cross-modal threats, we develop a novel cross-modal threat modeling and defense mechanism that identifies and mitigates attacks spanning different modalities. Additionally, we design a dynamic trustworthiness assessment algorithm that continuously monitors trustworthiness metrics and adaptively adjusts client participation and aggregation strategies based on real-time evaluations. Extensive experiments on medical multimodal datasets demonstrate that TrustGuard significantly outperforms existing methods in trustworthiness guarantees while maintaining competitive model performance.

## 摘要

联邦学习（FL）作为一种在保护数据隐私的同时跨医疗机构进行协作模型训练的有前景范式已经兴起。然而，在联邦环境中集成多模态数据引入了前所未有的挑战，需要在隐私、安全、鲁棒性、公平性和可解释性等多个维度确保可信性。现有方法通常孤立地解决这些问题，缺乏在多模态联邦学习环境中全面保证可信性的统一框架。此外，最近的研究揭示了医疗多模态系统中的重大漏洞，跨模态攻击可以实现很高的成功率，突出了对鲁棒防御机制的迫切需求。为了解决这些挑战，我们提出了TrustGuard，一个专门为医疗应用设计的新颖可信多模态联邦学习框架。TrustGuard引入了多维度可信性评估系统，通过统一指标全面评估隐私保护、安全鲁棒性、公平性保证和可解释性。为了对抗复杂的跨模态威胁，我们开发了新颖的跨模态威胁建模和防御机制，识别和缓解跨不同模态的攻击。此外，我们设计了动态可信性评估算法，持续监控可信性指标，并基于实时评估自适应调整客户端参与和聚合策略。在医疗多模态数据集上的大量实验证明，TrustGuard在可信性保证方面显著优于现有方法，同时保持竞争性模型性能。

## Keywords

Federated Learning, Multimodal Learning, Trustworthy AI, Healthcare AI, Cross-modal Defense

## 关键词

联邦学习，多模态学习，可信人工智能，医疗人工智能，跨模态防御
