# References

<PERSON>, <PERSON><PERSON>; <PERSON>, <PERSON>; <PERSON>, <PERSON>; <PERSON>, <PERSON>; and <PERSON><PERSON>, S. J. 2023. Personalized subgraph federated learning. In International Conference on Machine Learning, 1396- 1415. PMLR<PERSON>

<PERSON><PERSON>, <PERSON><PERSON>; <PERSON><PERSON><PERSON>, <PERSON>; <PERSON>, <PERSON><PERSON>; <PERSON>, <PERSON>; <PERSON>, <PERSON>; <PERSON><PERSON><PERSON>, <PERSON><PERSON>; <PERSON><PERSON>, <PERSON>; and <PERSON>, S. 2020. Scaling graph neural networks with approximate pagerank. In Proceedings of the 26th ACM SIGKDD International Conference on Knowledge Discovery & Data Mining, 2464- 2473.

<PERSON>, <PERSON><PERSON>; <PERSON>, <PERSON>.; <PERSON>, <PERSON>; <PERSON>, T.; and <PERSON>, J. 2022. Personalized Federated Learning With a Graph. In Proceedings of the Thirty- First International Joint Conference on Artificial Intelligence. International Joint Conferences on Artificial Intelligence.

Chen, H.- Y.; and <PERSON>, W.- L. 2021. On Bridging Generic and Personalized Federated Learning for Image Classification. In International Conference on Learning Representations.

Chen, W.; <PERSON>, J.; <PERSON>, <PERSON>.; <PERSON>, <PERSON>; <PERSON>, <PERSON>; <PERSON>, <PERSON>.; <PERSON>, <PERSON><PERSON>; <PERSON><PERSON>, <PERSON><PERSON>; and <PERSON>, H. 2024. DeepASD: a deep adversarial- regularized graph learning method for ASD diagnosis with multimodal data. Translational Psychiatry, 14(1): 375.

<PERSON>, <PERSON><PERSON>; and <PERSON>, Y. 2019. Searching for a robust neural architecture in four gpu hours. In Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition, 1761- 1770.

Duan, M.; <PERSON>, D.; <PERSON>, X.; Liu, R.; Tan, Y.; and Liang, L. 2020. Self- balancing federated learning with global imbalanced data in mobile systems. IEEE Transactions on Parallel and Distributed Systems, 32(1): 59- 71.

Fallah, A.; Mokhtari, A.; and Ozdaglar, A. 2020. Personalized federated learning: A meta- learning approach. arXiv preprint arXiv:2002.07948.

Fang, H.; Xiao, Z.; Zheng, P.; Chen, H.; Li, Z.; Bu, J.; and Wang, H. 2024. Learning Co- occurrence Patterns for Next Destination Recommendation. IEEE Transactions on Mobile Computing, 23(6): 7225- 7237.

Fu, W.; Wang, H.; Gao, C.; Liu, G.; Li, Y.; and Jiang, T. 2024. Privacy- preserving individual- level covid- 19 infection prediction via federated graph learning. ACM Transactions on Information Systems, 42(3): 1- 29.

Gao, Y.; Yang, H.; Zhang, P.; Zhou, C.; and Hu, Y. 2021. Graph neural architecture search. In International joint conference on artificial intelligence. International Joint Conference on Artificial Intelligence.

[Note: This is a partial list of references. The complete reference list contains many more entries that would exceed the line limit for this response.]