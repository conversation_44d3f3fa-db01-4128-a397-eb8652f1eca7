# GCFL: A Gradient Correction-based Federated Learning Framework for Privacy-preserving CP<PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>bstract—Federated learning, as a distributed architecture, shows great promise for applications in Cyber- Physical- Social Systems (CPSS). In order to mitigate the privacy risks inherent in CPSS, the integration of differential privacy with federated learning has attracted considerable attention. Existing research mainly focuses on dynamically adjusting the noise added or discarding certain gradients to mitigate the noise introduced by differential privacy. However, these approaches fail to remove the noise that hinders convergence and correct the gradients affected by the noise, which significantly reduces the accuracy of model classification. To overcome these challenges, this paper proposes a novel framework for differentially private federated learning that balances rigorous privacy guarantees with accuracy by introducing a server- side gradient correction mechanism. Specifically, after clients perform gradient clipping and noise perturbation, our framework detects deviations in the noisy local gradients and employs a projection mechanism to correct them, mitigating the negative impact of noise. Simultaneously, gradient projection promotes the alignment of gradients from different clients and guides the model towards convergence to a global optimum. We evaluate our framework on several benchmark datasets, and the experimental results demonstrate that it achieves state- of- the- art performance under the same privacy budget.

Index Terms—Federated learning, Gradient correction, Differential privacy, Information security and privacy.

# I. INTRODUCTION

REVOLUTIONIZED a wide range of sectors, including smart manufacturing, autonomous driving [1], and healthcare [2], [3], driving substantial advancements in both efficiency and intelligence [4]. By seamlessly integrating computation, communication, and physical processes, CPSS enables sophisticated real- time monitoring and control within complex systems, offering significant improvements in operational performance and decision- making processes [5]. However, the expanding scope of CPSS applications and the rapid pace of technological advancements present dual challenges: a dramatic increase in data processing demands and escalating system complexity [6]. Traditional centralized computing architectures are increasingly inadequate to address these demands, particularly when confronted with large- scale data processing and stringent real- time requirements [7], [8].

Furthermore, CPSS relies heavily on sensors and devices for real- time data acquisition and decision support, involving the processing of vast amounts of sensitive information [9], [10]. Ensuring data privacy while maintaining system performance has become a critical concern. Federated Learning (FL) [11] offers a promising solution as a novel distributed learning paradigm. By distributing data processing tasks across multiple nodes for parallel execution, FL significantly enhances computational efficiency, fault tolerance, and real- time responsiveness in CPSS. Moreover, FL enables collaborative model training without exchanging raw data, mitigating the traditional data silo problem and facilitating knowledge sharing among participants, thereby improving the performance of various tasks within CPSS.

While FL offers significant advantages in distributed model training, recent studies have exposed vulnerabilities in FL systems that exclusively rely on the exchange of model parameters [12], [13]. Attackers can infer sensitive information [14] or even reconstruct individual data [15], [16] by analyzing model updates or gradients, even without direct access to the training data. Such risks present substantial threats to the privacy of CPSS. Moreover, more sophisticated attacks, such as linkage attacks, can re- identify anonymized data, further compromising individual privacy [17]. As a result, ensuring data privacy, particularly preventing the leakage of training data, becomes paramount in the context of CPSS.

Differential Privacy (DP) [18] has emerged as a crucial tool for safeguarding data privacy in FL. By adding noise to data, DP ensures that individual information cannot be gleaned from analyzing model outputs. The advent of Differentially Private Stochastic Gradient Descent (DP- SGD) has enabled the effective application of DP to deep learning training. Through gradient clipping and noise injection, DP- SGD effectively protects the privacy of training data, preventing attackers from inferring sensitive individual information from model outputs. Within the FL framework, local clients utilize DP to add noise to model parameters or gradients, masking the influence of individual data. Clients then upload the updated models to a central server, which aggregates the updates from multiple clients to generate a globally improved model with enhanced learning and generalization capabilities.

While recent research demonstrates the efficacy of combining DP and FL for data privacy, the interplay between

privacy preservation and distributed training often significantly impacts model performance, especially in resource- constrained CPSS environments. To address this challenge, researchers have proposed various improved federated aggregation algorithms. For example, adaptively reducing the noise scale can balance privacy budgets and model performance, offering new avenues for privacy preservation and performance optimization in CPSS [19]. Furthermore, recent efforts have explored evaluating gradients based on validation tests in each iteration, applying only convergence- inducing updates to the model. However, these approaches primarily focus on reducing the added noise during the iterative process or discarding heavily noise- affected gradients, without explicitly removing convergence- hindering noise or correcting noise- affected gradients during gradient aggregation. This often leads to suboptimal performance and slow convergence under high noise levels. Therefore, precisely controlling noise and mitigating its negative impact on the model remains a crucial challenge for enhancing CPSS performance in edge computing contexts.

Inspired by the aforementioned observations, this paper proposes a novel DP- FL framework that provides strong privacy guarantees through client- side gradient perturbation and employs an innovative server- side gradient correction mechanism to explicitly mitigate the adverse effects of noise on model convergence, ensuring the accuracy and effectiveness of deep learning models trained on sensitive personal data. Specifically, we evaluate gradients from different clients at the server and project those gradients whose noise contributions lead to model divergence. This projection operation effectively removes noise that disrupts the correct update direction of model parameters, maintaining stable model performance even under high noise levels. Simultaneously, the projection brings gradients from different clients closer, correcting the model update direction from local optima towards the global optimum. This local optima correction mechanism enhances the performance of DP- FL in CPSS while guaranteeing strong privacy for client data. Our main contributions are summarized as follows:

We propose a novel DP- FL framework designed to optimize privacy preservation in CPSS while simultaneously enhancing model performance. This framework ensures client data privacy in high- noise edge environments while improving model performance. We introduce an innovative gradient correction mechanism. This mechanism leverages gradient projection to mitigate the adverse effects of noise on model updates, maintaining stable model performance even under high noise levels. Furthermore, gradient projection promotes alignment of gradients from different clients, guiding model convergence towards a direction closer to the global optimum. Extensive experiments are conducted on three benchmark datasets. State- of- the- art performance is demonstrated compared to mainstream baseline models under the same privacy budget.

# II. RELATED WORK

The proliferation of CPSS in industrial applications hinges on effective data sharing and collaboration [20]. However, growing concerns surrounding data privacy pose significant challenges to traditional data sharing paradigms. This challenge is further exacerbated in edge computing environments, where distributed data storage and processing amplify the risk of privacy breaches [21]. To enable multi- client data training without compromising individual privacy, researchers have explored integrating techniques such as differential privacy, homomorphic encryption, and secure multi- party computation into federated learning [22]. Our work focuses on the synergy between differential privacy and federated learning to address these privacy concerns, specifically aiming to optimize the trade- off between model performance and privacy preservation within the context of edge computing.

Federated learning. FL enables multiple edge devices to collaboratively train deep learning models under the control of a central server. Clients train on local datasets and send only model updates, rather than raw data. The central server aggregates these updates iteratively until a set number of training rounds is completed or a performance threshold is achieved. Initially proposed by Google, FL gained traction with the FedAvg algorithm introduced by McManan et al. [23], which employs weighted averaging of client updates on the server. Subsequent research has addressed two key challenges in FL: 1) Data Heterogeneity. The non- independent and identically distributed nature of data across clients poses a significant hurdle. To mitigate this, Li et al. [24] introduced regularization terms in local model updates to reduce deviations from the global model. Karimireddy et al. [25] utilized variance reduction approaches to counteract client drift in the process of local updates. Further, to enhance privacy and reduce the computational burden, Dai et al. [26] integrated FL with an isolation forest. Xu et al. [27] introduced a distributed privacy- preserving framework. Adaptive aggregation mechanisms have also been explored for global model updates. To address the slow convergence often observed in FL with complex models or high data heterogeneity, Jhunjhunwala et al. [28] devised a FL optimization method with dynamic server step size adjustment applied to pseudo- gradients. 2) Robust Privacy Guarantees. Even with decentralized datasets in FL, user privacy remains vulnerable. A potentially "honest- but- curious" server or third- party access to exchanged model parameters during or after training can expose sensitive information [29], [30]. DP, achieved by adding random noise to model updates, has become a popular approach for integration with FL. Our work aims to address both the local drift issue in model updates and provide robust privacy guarantees.

Differential Privacy. Differential Privacy (DP) is primarily a privacy- preserving technique for data analysis, underpinned by a rigorous mathematical framework for quantifying and enforcing data privacy. It was originally developed for use on data- collection servers. It prevents differential attacks by introducing precisely calibrated noise to statistical query outputs, making it nearly impossible for an attacker to distinguish between two adjacent datasets. Dwork pioneered the original

![](images/d9a118b9474f3f61f249c2bc5e49649a03b7ec29477e1649342eba96e610fa77.jpg)  
Fig. 1. An application of differential privacy and federated learning involves multiple clients training models locally, leveraging differential privacy to protect data privacy. A central server aggregates updates from multiple clients to process a global model with enhanced learning and generalization capabilities.

definition of differential privacy and introduced the Laplace mechanism as a key enabling technique [31]. Subsequently, relaxed versions of DP were proposed to improve data utility and model performance while still offering meaningful privacy protection [32]. For instance, Dong et al. explored the use of Gaussian noise in differential privacy as an alternative to the traditional Laplace mechanism [33]. Building on Renyi divergence, Mironov et al. proposed Renyi Differential Privacy [34]. Differentially Private Stochastic Gradient Descent (DPSGD) marked a significant step by integrating differential privacy with deep learning. It employs Poisson sampling to select a batch of samples randomly, computes and clips each sample's gradient, and then adds Gaussian noise to each gradient based on differential privacy principles, preventing unintentional leakage of private training data. While numerous efforts have focused on practical applications of DP in industry, such as Dankar et al.'s exploration of DP in healthcare data protection [35] and Hernandez et al.'s investigation of optimal local DP mechanisms for varying privacy needs [36], these often face a trade- off between strict privacy budgets and high performance. Recently, Fu et al. [37] achieved notable results by selectively applying beneficial gradient updates based on a validation testing strategy, discarding noisy or unhelpful updates that hinder convergence in traditional DPSGD. Furthermore, adaptive gradient clipping methods [19] and simulated annealing- based DP schemes [38] have been proposed to enhance model performance under high noise conditions. These works share similarities with our approach.

However, we observe that existing work primarily focuses on reducing the magnitude of added noise during the iterative process or discarding heavily noise- affected gradients, without explicitly correcting the negative impact of noise on model updates. This often leads to suboptimal performance and slow convergence, particularly under high noise regimes. We propose GCFL, which incorporates a gradient correction mechanism to achieve data privacy while enabling efficient model training and updates on resource- constrained edge devices.

# III. PRELIMINARIES

We aim to maximize data privacy while applying federated learning to CPSS. Integrating differential privacy has become a standard approach for protecting user privacy [39], [40], as illustrated in Figure 1.

Definition 3.1. (Differential Privacy). The core objective of differential privacy is to ensure that the presence or absence of a single data point in the input dataset does not significantly affect the output statistics [41]. A mechanism satisfies  $(\epsilon ,\delta) - DP$  if, for any two neighboring datasets  $D$  and  $D^{\prime}$  differing by at most one data point, and for any output subset  $S\subset$ $Range(M)$  ..

$$
Pr[M(D)\in S]< e^{\epsilon}Pr[M(D^{\prime})\in S] + \delta , \tag{1}
$$

where  $\epsilon$  is the privacy loss parameter, controlling the strength of the privacy guarantee. The parameter  $\delta$  represents the probability that the algorithm may fail to satisfy the privacy guarantee. In practice,  $\delta$  should be negligibly small.

Definition 3.2. (Rényi Differential Privacy (RDP)). In the DP- FL framework, the number of iterations required for model convergence is often substantial, necessitating a more concise privacy loss composition measure. To analyze the cumulative privacy loss more accurately, RDP, based on Renyi divergence [42], extends the traditional definition of differential privacy.

![](images/a40f0f312ac313dad603e4d8c156d85269556fe5a6f7a0f391ff4a7af3228282.jpg)  
Fig. 2. Overview of GCFL: The server receives the noisy model gradients, applies a gradient correction mechanism to adjust gradients that deviate from the global optimal direction, and then returns the aggregated gradients to the client for training.

Given two probabilities distributions  $P$  and  $Q$ , the Rényi divergence is defined as:

$$
D_{\alpha}(P\parallel Q) = \frac{1}{\alpha - 1} lnE_{x\sim Q}[(\frac{P(x)}{Q(x)})^{\alpha}], \tag{2}
$$

where  $\alpha$  is the order of the Rényi divergence, controlling the sensitivity of the divergence. As  $\alpha$  approaches 1, the Rényi divergence converges to the classic Kullback- Leibler divergence.

RDP measures the privacy difference in the output distributions of a mechanism on adjacent datasets using Rényi divergence. Specifically, a mechanism  $M$  satisfies the conditions of  $(\alpha , R) - RDP$  if:

$$
D_{\alpha}(M(D))\parallel M(D^{\prime}))\leq \epsilon . \tag{3}
$$

It is important to note that RDP provides an alternative approach for analyzing privacy composition, aiming to offer a tighter analysis of privacy guarantees.

Definition 3.3. (Conversion from RDP to DP [43]). RDP can be converted into standard Differential Privacy. Given an  $(\alpha , R) - RDP$  guarantee, the relationship can be expressed as:

$$
\epsilon = R + ln\left(\frac{(\alpha - 1)}{\alpha}\right) - \frac{(ln\delta + ln\alpha)}{(\alpha - 1)}. \tag{4}
$$

In complex medical data protection scenarios, RDP as an extension of traditional DP, offers a more flexible tool for privacy analysis.

Definition 3.4. (Federated Learning). FL is a distributed learning framework that has garnered widespread attention in recent years. In the FL framework, multiple client devices collaboratively train a deep learning model under the coordination of a central server, without the need to share raw data that may contain sensitive information. In contrast, clients keep their data locally and contribute to the collective learning process by training models locally and then sending model parameters to the central server for aggregation. Subsequently, the server aggregates the updates of all model parameters and redistributes the improved model to all participants. Formally, the server aggregates the parameters received from N clients as follows:

$$
w = \sum_{i = 1}^{N}p_{i}w_{i}, \tag{5}
$$

where  $w_{i}$  presents the model parameters from the  $i - th$  client,  $w$  represents the model parameters on the server side,  $N$  is the number of clients, and  $p_{i}$  is the weight of the model parameters from the i- th client. Therefore, the optimization objective of federated learning can be defined as:

$$
w^{*} = argmin\sum_{i_{1}}^{N}p_{i}L_{i}(w,D_{i}), \tag{6}
$$

where  $L(\cdot)$  is the local loss function used by the  $i - th$  client.

Definition 3.5. (Horizontal Federated Learning (HFL)). HFL is applicable to scenarios where participating clients hold different samples but share the same feature space. This means that each client's data samples are distinct, but these samples share the same features. The optimization objective of HFL is to train a general global model that is applicable to all clients' data.

Definition 3.6. (Vertical Federated Learning (VFL)). VFL is applicable to scenarios where the data is partitioned along the feature space. Each client possesses different features for the same set of samples. The optimization objective is to train a more accurate prediction model on the server by combining these features.

Definition 3.7. (Threat Model). In the federated learning framework, attackers may launch attacks from both the client side and the server side. We assume that the server is curious but honest, meaning that the server can receive individual updates from different clients and use this information to infer the training data of each participant. However, external attackers may attempt to steal private information. These external attackers can infer from the SGD (Stochastic Gradient Descent) algorithm to perform active attacks, because the gradients computed by the SGD algorithm change depending on the importance of the training samples. If a training sample causes a large loss, the SGD algorithm adjusts the parameters to reduce the loss for that sample. If the training dataset does not contain this sample, the model's loss change remains stable. Through repeated inference attacks, attackers can steal high- confidence private information.

# IV. METHODOLOGY

We consider a setup with a central server and  $N$  local users. In this setup, each client  $i\in N$  holds a private dataset  $D_{i} =$ $(d_1^i,d_2^i,\dots,d_R^i)$  ,where  $R$  denotes the number of data points in the dataset. Let  $D = D_{1},\ldots D_{N}\cup \ldots \cup D_{N}$  represent the union of all client datasets, with the assumption that these datasets are disjoint. Let  $w$  be the model parameters. Our objective is to optimize the following function:

$$
\min_{w\in \mathbb{R}^d}\mathcal{L}(x) = \frac{1}{N}\sum_{i = 1}^{N}\mathcal{L}_i(w), \tag{7}
$$

where  $\mathcal{L}_i(w)$  represents the cross- entropy loss function for local training on client  $i$  and the overall loss function  $\mathcal{L}(w)$  is the weighted average of the losses across all clients.

Fig 2 illustrates the overall architecture of GCFL. Each client first trains the model on its local dataset. In each iteration, the client selects a batch of training data using Poisson sampling and trains the model using the DP- SGD algorithm. Specifically, after computing the gradients of all model parameters, the client adds noise that satisfies the differential privacy requirements. The noisy gradients are then uploaded to the central server. The server is responsible for denoising and aggregating the received gradients. Finally, the server distributes the aggregated global gradients to all clients for updating their local models. This process iterates until the predefined model accuracy or training rounds are reached.

# A. Local Training Process

During the local training phase, the objective is to ensure a certain level of training accuracy while mitigating the risk of information leakage from a single dataset  $D_{i}$  when users share model updates. Based on the setting in DP- scaffold, our analysis focuses on record- level differential privacy in the combined dataset  $D$  . Specifically, for two neighboring datasets  $D_{i}$  and  $D_{j}$  (differing by a single data record, with all other parts identical), an attacker cannot determine the presence of a specific data record from the query results. All data remains stored locally during training, and clients cannot directly share their raw datasets. Each client  $i$  maintains an identical DL model structure and uses its local dataset  $D_{i}$  as input. The model output is denoted as  $\hat{y}$  . We adopt the cross- entropy loss function, expressed as:

$$
\mathcal{L}_i(w) = -\frac{1}{R}\sum_{j = 1}^R y_j^i\log \hat{y}_j^i, \tag{8}
$$

where  $w$  represents the model parameters,  $y$  is the true label, and  $j$  indexes the  $j$  - th sample in the dataset.

During each iteration  $t$  client  $i\in N$  performs Poisson sampling to select a mini- batch  $S$  of size  $\lfloor sR\rfloor$  from its local dataset  $D_{i}$  ,where  $s$  is the sampling rate. Gradients  $g_{t}(x) = \nabla \mathcal{L}_{t}(w)$  are then computed for all data points in the batch  $S$  .To limit the influence of individual samples on the model updates within a predefined range, gradient clipping is applied based on a pre- determined clipping threshold  $C_t$  ..

$$
g_{t}^{i - \mathrm{clipped}}(w) = g_{t}(w)\cdot min(1,\frac{C_{t}}{\|g_{t}^{i}(w)\|_{2}}). \tag{9}
$$

The purpose of the clipping threshold is to limit the update norm of all clients within  $C_t$  , ensuring that the impact of any single client's update on the final aggregated result, in terms of the L2 norm, does not exceed  $C_t$  .During the gradient descent process, an anomalous data point or one that has a significant impact on the loss function may produce a gradient or model update with a very large norm. Without clipping, this large- norm update could significantly affect the global model, potentially leaking sensitive information about the data. After clipping, the contribution of each sample to the gradient is restricted within the range  $[0,C_t]$  ,thereby reducing the sensitivity to  $2C / sR$  .To further obscure the influence of sensitive data, Gaussian noise is added to the clipped gradients to further obscure the influence of sensitive data. The magnitude of the Gaussian noise is determined by the noise multiplier  $\sigma$  and the clipping threshold, as detailed below:

$$
\hat{g}_t^i = \frac{1}{|S_t|}\left(\sum_{i\in S_t}g_t^{i - \mathrm{clipped}}(w_i) + \mathcal{N}(0,\sigma^2 C_t^2)\right). \tag{10}
$$

The standard deviation  $\sigma$  of the Gaussian noise controls the extent to which the algorithm satisfies  $(\epsilon ,\delta) - DP$  .A larger  $\sigma$  value increases the level of privacy protection but also amplifies the noise's effect on the gradients, potentially degrading the model's performance.

# B. Server Aggregation Process

After clients hide their model information using differential privacy, the central server needs to aggregate the uploaded gradients  $\hat{g_t^i}$  . Unlike traditional methods that directly use weighted averages to combine gradients from different clients, we first perform denoising on these gradients. Our goal is to adjust the gradients in a way that promotes positive interactions among

# Algorithm 1 Overall of GCFL

Input: Training dataset  $D_{i} = (d_{1}^{i},d_{2}^{i},\ldots ,d_{R}^{i})$  ; hyperparameters: learning rate  $n$  , batch size, clipping threshold  $C_t$  noise multiplier  $\sigma$

Output: The final trained model  $w_{t}$

1: Initialize  $t = 1$ $w_{0} = \mathrm{Initial}()$  2: while  $t< T$  do 3: User subsampling by the server: 4: Sample  $Cl_t\subset [N]$  5: Server sends  $w_{t - 1}$  to user  $i\in Cl_t$  6: for user  $i\in Cl_t$  do 7: Initialize model: 8: for  $k = 1,2,\ldots ,K$  do 9: Data subsampling by user  $i$  10: Randomly sample a batch  $S_{i}^{k}\subset D_{i}$  of size  $\lfloor sR\rfloor$  11: for each sample  $j\in S_i^k$  do 12: Compute gradient: 13:  $\begin{array}{r}g_t^i (w)\leftarrow \nabla \mathcal{L}_t(w) \end{array}$  14: Clip gradient: 15:  $\begin{array}{r}g_t^{i - \mathrm{clipped}}(w)\leftarrow g_t(w)\cdot \min \left(1,\frac{C_t}{\|g_t^i(w)\|_2}\right) \end{array}$  16: end for 17: Add DP noise to local gradients: 18:  $\begin{array}{r}\hat{g}_t^i = \frac{1}{|S^k|} (\sum_{i\in S_t^k}\hat{g}_t^{i - \mathrm{clipped}}(w_i) + \mathcal{N}(0,\sigma^2 C_t^2)) \end{array}$  19: User  $i$  send  $\hat{g}_t^i$  to server 20: end for 21: end for 22: Server denoising and correction: 23: for  $\hat{g}_t^i\in \hat{g}_t$  do 24: for  $\hat{g}_t^j\in \hat{g}_t$  do 25: Evaluate the impact of noise: 26:  $\begin{array}{r}\cos \phi_{ij} = \frac{\hat{g}_t^i\cdot\hat{g}_t^j}{|\hat{g}_t^i||\hat{g}_t^j|} \end{array}$  27: if  $\cos \phi_{ij}< 0$  then 28:  $\begin{array}{r}\hat{g}_t^{i - cor} = \hat{g}_t^i - \frac{\hat{g}_t^i\cdot\hat{g}_t^j}{||\hat{g}_t^i||_2}\hat{g}_t^j \end{array}$  29: end if 30: end for 31: end for 32:  $\begin{array}{r}w_{t}\leftarrow w_{t - 1} - \eta \frac{1}{N}\sum_{i\in Cl_{t}}\hat{g}_{t}^{i - cor} \end{array}$  33: end while 34: return Final trained model  $w_{t}$

client gradients without making assumptions about the form of the model. However, completely eliminating the influence of Gaussian noise is unrealistic. Therefore, we need to identify gradients that severely impede the model's convergence in the correct direction and correct them.

To achieve this, in each iteration  $t$  we randomly select a batch of clients and assume that their gradients in that iteration correctly point toward the model convergence direction. Subsequently, for all other client gradients, we determine whether they conflict with the correct gradient direction using the following equation:

$$
\cos \phi_{ij} = \frac{\hat{g}_t^i\cdot\hat{g}_t^j}{|\hat{g}_t^i||\hat{g}_t^j|}, \tag{11}
$$

where  $\hat{g}_t^i$  represents the model gradient uploaded by the  $i$  - th client in iteration  $t$

If the cosine similarity between the gradients of two different clients is negative, we consider that the gradient has deviated from the correct direction due to the added noise and needs correction. For the gradient  $\hat{g}_t^j$  that conflicts with the correct gradient  $\hat{g}_t^j$  , we apply the following correction method:

$$
\hat{g}_t^{i - cor} = \hat{g}_t^i -\frac{\hat{g}_t^i\cdot\hat{g}_t^j}{||\hat{g}_t^j||_2}\hat{g}_t^j. \tag{12}
$$

This operation projects the gradient  $\hat{g}_t^i$  onto the normal plane of gradient  $\hat{g}_t^j$  . Intuitively, this is equivalent to removing the conflicting component of the gradient, thereby reducing noise interference. From a vector analysis perspective, the gradients uploaded by clients can be viewed as the superposition of the original gradient and Gaussian noise. Since clients are training on the same task, their original gradient directions usually do not have significant discrepancies; thus, the projection operation does not significantly affect the original gradients. As for the random noise, because it is difficult for it to align with the complex gradient directions in high- dimensional space, it may hinder model updates or even oppose the original gradient direction. This portion of the noise will be weakened or eliminated by the projection operation, and only a small amount of noise close to the original gradient direction will be retained. The overall computational complexity of gradient correction is  $O(M\cdot (N - M)\cdot D + K\cdot D)$  ,where M represents the number of clients selected in each iteration,  $Nu$  is the total number of clients participating in the federated learning process

The server calibrates the updated gradients received from the remaining clients using a randomly selected subset of client gradients. This process continues until the gradients from all clients are adjusted towards a similar direction, effectively mitigating the detrimental effects of high- magnitude noise. Subsequently, the server aggregates the gradients from the different clients using a weighted aggregation scheme. The weights for this aggregation process are determined based on the number of training samples available at each client during the current iteration. After the gradients from all clients have been denoised and aggregated through weighted averaging, the central server broadcasts the resulting global model update to all clients. Each client then uses this update to refine its local model via backpropagation. This iterative process continues until a predetermined number of training rounds is completed or the desired model accuracy is achieved. The overall GCFL procedure is shown in Algorithm 1.

# V. EXPERIMENTAL EVALUATION

V. EXPERIMENTAL EVALUATIONThis section presents the empirical evaluation of GCFL against baseline models on several datasets. Section V-A details the datasets used, the baseline models employed for comparison, and the evaluation metrics. The experimental results and analysis are presented in Section V-B.

# A. Experimental Setting

A. Experimental SettingDatasets. The performance of the proposed GCFL framework is evaluated using three benchmark datasets: COVID-19

TABLEI EXPERIMENTAL PARAMETER SETTINGS.  

<table><tr><td>Parameter</td><td>Value</td></tr><tr><td>Number of edge servers N</td><td>2</td></tr><tr><td>Total Number of epochs T</td><td>{30,35,80}</td></tr><tr><td>Learning Rate η</td><td>{0.001,0.002,0.005}</td></tr><tr><td>Batch size for training</td><td>{32,48}</td></tr><tr><td>Batch size for testing</td><td>1024</td></tr><tr><td>Differential privacy noise multiplier σ</td><td>0.8</td></tr><tr><td>Maximum clipping threshold per sample Ct</td><td>1.5</td></tr><tr><td>Privacy leakage probability δ</td><td>1e-5</td></tr></table>

Radiography, MNIST, and CIFAR- 10. These datasets represent a range of task scenarios, allowing us to assess the framework's effectiveness in noisy edge environments.

The COVID- 19 Radiography dataset includes 21,165 chest  $\textrm{X}$  - ray images, consisting of 10,192 normal images, 6,012 with lung opacities, and 1,345 with viral pneumonia. All images have a resolution of  $299\times 299$  pixels, with a training set of 17,993 images and a test set of 3,172 images.

The MNIST dataset comprises 70,000 images of handwritten digits, each in grayscale with a  $28\times 28$  pixel resolution. The dataset is partitioned into 60,000 training samples and 10,000 test samples, with 7,000 images per digit class.

The CIFAR- 10 dataset contains 60,000 color images, equally distributed among 10 categories, with 5,000 images per category. All images have dimensions of  $32\times 32$  pixels, with 50,000 images allocated for training and 10,000 for testing.

The non- IID dataset is constructed by evenly splitting the original MNIST training set of 60,000 images into two contiguous halves. The first 30,000 samples (labels 0- 4) are assigned to client 1, and the remaining 30,000 samples (labels 5- 9) to client 2. This preserves MNIST's image resolution and overall class proportions while introducing non- IID heterogeneity across the two clients.

Baselines. To evaluate the effectiveness of our framework, we conducted a comparative analysis with the following baseline methods.

DP- FedAvg [23] This is the most classic federated learning algorithm. The server updates the global model by taking a weighted average of the model parameters uploaded by the clients, making it both simple and efficient. DP- FedProx [24] This approach seeks to mitigate the performance decline resulting from data heterogeneity and inconsistency. By incorporating a regularization term into the local client optimization objective, it constrains the disparity between local updates and the global model, thereby avoiding significant deviations from the global model during each local training phase. DP- Scaffold [25] The goal of this approach is to reduce the bias between local updates on different clients. The server maintains a set of control variates, and when clients update their local models, these control variates are taken into account to eliminate the bias in the local model updates. DP- FedExP [28] This method adaptively adjusts the server's step size based on the dynamically changing pseudo- gradient during the FL process. It builds on the relationship between FedAvg and the projection onto convex sets (POCS) algorithm, providing an extrapolation mechanism that accelerates convergence, especially in over- parameterized convex problems.

Evaluation Metrics. To ensure consistency and facilitate fair comparison with existing methods, we employed identical hyperparameter settings and evaluation metrics across all experiments. Specifically, the number of clients was fixed at 2, the differential privacy parameter  $\delta$  was initialized to  $10^{- 5}$  the learning rate  $\eta$  was tuned amongst  $\{0.001,0.002,0.005\}$  and the gradient clipping threshold was set to 1.5. The remaining hyperparameters are detailed in Table I. Comparative experiments were conducted on a simplified network architecture comprising only convolutional and fully connected layers. Following standard practice in the field, we evaluated model performance using accuracy, recall, and F1- score as primary metrics. The reported results represent the average performance across three independent runs conducted on an NVIDIA RTX 4070 Super GPU. Accuracy is defined as the ratio of the number of correctly predicted samples to the total number of samples. Recall measures the model's ability to identify all positive class samples, while the F1- score [44] is an important metric for evaluating the overall performance of a classifier, especially in the case of imbalanced classes.

# B. Main Results

In this section, we evaluate the performance of our proposed framework against existing methods using three benchmark datasets. All evaluations were conducted under the same privacy budget, with the results summarized in Table II. Across all datasets, our framework consistently outperforms the baseline methods, demonstrating the efficacy of the gradient correction mechanism in identifying and denoising perturbed gradients that impair proper model updates, while retaining critical information from the original gradients. Notably, on the MNIST dataset, our framework achieves significant improvements over all baselines. This may be attributed to the dataset's relative simplicity, which allows the model to quickly learn feature relationships and enables our framework to more effectively aggregate updates from different clients. In contrast, on the more complex COVID- 19 and CIFAR- 10 datasets, although our framework still surpasses the baselines, the performance gains are comparatively less pronounced. This may be due to the use of a simplified convolutional network architecture in our experiments, and also because on more complex datasets, the model finds it more difficult to follow optimal gradient directions during the early training stages. In such cases, projecting excessively noisy gradients can impede performance and slow convergence.

We evaluated the performance of our framework in different privacy budget settings by comparing it with leading competitive frameworks. Performance in the MNIST dataset was tested under various privacy budgets, and the results are summarized in Table III. Remarkably, our framework consistently exhibits the best performance under all conditions, achieving a significant balance between accuracy and privacy preservation. This both affirms the framework's effectiveness

TABLE II COMPARISON OF ACCURACY, RECALL AND F1 SCORE ON THREE IMAGE DATASETS (  $\epsilon = 2$  EPOCH  $= 60$  ). BEST RESULTS IN BOLD. ISOLATED DENOTES LOCAL-MODEL TRAINING.  

<table><tr><td rowspan="2">Models</td><td colspan="3">COVID-19</td><td colspan="3">MNIST</td><td colspan="3">CIFAR-10</td></tr><tr><td>ACC</td><td>REC</td><td>F1</td><td>ACC</td><td>REC</td><td>F1</td><td>ACC</td><td>REC</td><td>F1</td></tr><tr><td>Isolate</td><td>64.01</td><td>54.28</td><td>53.68</td><td>79.14</td><td>78.60</td><td>77.78</td><td>65.58</td><td>65.58</td><td>65.52</td></tr><tr><td>DP-FedAvg</td><td>69.10</td><td>60.85</td><td>60.51</td><td>85.50</td><td>85.26</td><td>85.12</td><td>75.61</td><td>73.61</td><td>75.75</td></tr><tr><td>DP-FedProx</td><td>68.28</td><td>58.38</td><td>58.06</td><td>86.28</td><td>86.07</td><td>85.97</td><td>75.55</td><td>75.55</td><td>75.53</td></tr><tr><td>DP-Scaffold</td><td>69.33</td><td>61.46</td><td>61.12</td><td>87.13</td><td>86.96</td><td>86.92</td><td>74.68</td><td>74.68</td><td>74.75</td></tr><tr><td>DP-FedExp</td><td>70.21</td><td>63.79</td><td>63.55</td><td>89.37</td><td>88.94</td><td>89.22</td><td>73.93</td><td>73.58</td><td>73.43</td></tr><tr><td>GCFL</td><td>71.34</td><td>65.17</td><td>67.29</td><td>91.11</td><td>91.02</td><td>91.03</td><td>76.83</td><td>76.60</td><td>76.58</td></tr></table>

TABLE III COMPARISON WITH BASELINE MODELS UNDER DIFFERENT PRIVACY RIGHTS ON THE MNIST DATASET. THE BEST PERFORMANCE RESULTS FROM THIS EXPERIMENT ARE HIGHLIGHTED IN BOLD.  

<table><tr><td rowspan="2">Models</td><td colspan="3">ε = 2</td><td colspan="3">ε = 3</td><td colspan="3">ε = 4</td></tr><tr><td>ACC</td><td>REC</td><td>F1</td><td>ACC</td><td>REC</td><td>F1</td><td>ACC</td><td>REC</td><td>F1</td></tr><tr><td>DP-FedAvg</td><td>85.50</td><td>85.26</td><td>85.12</td><td>88.73</td><td>88.62</td><td>88.59</td><td>90.51</td><td>90.43</td><td>90.41</td></tr><tr><td>DP-FedProx</td><td>86.28</td><td>86.07</td><td>85.97</td><td>90.57</td><td>90.49</td><td>90.47</td><td>92.18</td><td>92.13</td><td>92.12</td></tr><tr><td>DP-Scaffold</td><td>87.13</td><td>86.96</td><td>86.92</td><td>90.98</td><td>90.82</td><td>90.90</td><td>92.13</td><td>92.09</td><td>92.09</td></tr><tr><td>DP-FedExp</td><td>89.07</td><td>88.94</td><td>89.22</td><td>91.57</td><td>91.37</td><td>91.47</td><td>92.33</td><td>92.28</td><td>92.31</td></tr><tr><td>GCFL</td><td>91.11</td><td>91.02</td><td>91.03</td><td>92.76</td><td>92.43</td><td>92.70</td><td>93.15</td><td>93.13</td><td>93.09</td></tr></table>

![](images/c0abf22696d09647d50767df93c9db7efca1a921d217a5c975031a949fa2bd57.jpg)  
Fig. 3. The impact of different parameters on the test accuracy in MNIST dataset.

and showcases its robustness under different privacy budget conditions, making it appropriate for applications requiring stringent privacy protection. Our framework demonstrates a substantial advantage over other frameworks under strict privacy constraints  $(\epsilon = 2)$ . This is because frameworks such as DP- FedAvg lack additional mechanisms to handle the noise introduced by differential privacy. Consequently, under high noise conditions, the model's update direction is misled by erroneous gradients, hindering convergence. GCFL effectively addresses this issue.

Figure 3 illustrates the impact of various hyperparameters on the GCFL framework. A peak accuracy of  $93.81\%$  is achieved with a learning rate  $\eta$  of 0.002. For  $\eta > 0.002$ , the accuracy begins to decline, likely due to the large learning rate hindering model convergence. Figures 3(b) and (c) demonstrate the robustness of GCFL to variations in  $\sigma$  and  $C_t$ , parameters that influence the noise added for differential privacy. The performance of GCFL does not fluctuate dramatically with changes in these parameters. It is worth noting that excessively large values for  $\sigma$  and  $C_t$  do not yield further benefits. This is because very large values can lead to the model correcting gradients that deviate significantly from the convergence direction, resulting in corrected gradients approaching zero and contributing minimally to model convergence. As shown in Figure 3(d), GCFL achieves optimal performance with a batch size of 32.

We further compared the test accuracy of GCFL against baseline models on both the MNIST and CIFAR- 10 datasets across different epochs. The baseline models were categorized into two groups: Constrained Framework, encompassing DP- Prox and DP- Scaffold, which incorporate additional constraints or global correction techniques; and Averaging Strategy Framework, consisting of DP- FedAvg and DP- FedExP. The comparative results are presented in Figures 4 and 5. As shown, the performance of the Constrained Framework models (DP- Prox and DP- Scaffold) is similar on MNIST, but diverges significantly on CIFAR- 10. While DP- FedExP demonstrate faster early convergence on CIFAR- 10 than GCFL, suggesting a more rapid initial aggregation and accuracy improvement, their final performance falls short

![](images/36e33c84833919587e614444ccb84550736a21fed6ebdd5bbfcfa9acb0a3ed69.jpg)  
Fig. 4. Comparison of different frameworks and epochs on the MNIST dataset.

![](images/99a64db497deb3ee0510a9332c0260415d5fa1ecc93db90fe82b206afe377ff8.jpg)  
Fig. 5. Comparison of different frameworks and epochs on the CIFAR-10 dataset.

![](images/0f3f869bac456eecc4803ee951d3f1f3ccafee7388dd53d2cc8b42b528f115dc.jpg)  
Fig. 6. Impact of differential privacy noise levels on the training loss convergence performance.

of GCFL. In contrast, GCFL exhibits superior stability and robustness across both datasets. This indicates that GCFL more effectively coordinates the learning process across participants, maintaining model consistency and performance in the face of varying data distributions and complexities. This stability likely stems from GCFL striking a better balance between global consensus and local model optimization, leading to superior performance in diverse data environments.

![](images/5f767777a026b459ef089b44ce822c1c636f655c18b3ccbc1b900bc17c787e35.jpg)

![](images/e6df2facc246cb906c85f1c9871c0eb1280b9138ec0a056cf726cfa22491056c.jpg)

TABLE IV COMPARISON WITH BASELINE MODELS ON NON-IID DATASETS.  

<table><tr><td>Models</td><td>ACC</td><td>REC</td><td>F1</td></tr><tr><td>DP-FedAvg</td><td>84.53</td><td>84.31</td><td>84.12</td></tr><tr><td>DP-FedProx</td><td>86.95</td><td>86.77</td><td>86.57</td></tr><tr><td>DP-Scaffold</td><td>86.98</td><td>86.19</td><td>86.84</td></tr><tr><td>DP-FedExpP</td><td>87.69</td><td>87.35</td><td>87.34</td></tr><tr><td>GCFL</td><td>88.98</td><td>88.80</td><td>88.82</td></tr></table>

TABLE V COMPARISON OF SPEED (SAMPLES PER SECOND) BETWEEN THE BASELINE MODEL AND GCFL.  

<table><tr><td rowspan="2">Model</td><td colspan="2">MNIST</td><td colspan="2">CIFAR-10</td></tr><tr><td>Train</td><td>Test</td><td>Train</td><td>Test</td></tr><tr><td>DP-FedAvg</td><td>8109.43</td><td>11447.12</td><td>6165.22</td><td>9184.04</td></tr><tr><td>DP-FedProx</td><td>7669.85</td><td>11765.76</td><td>5731.56</td><td>9021.14</td></tr><tr><td>DP-Scaffold</td><td>7801.86</td><td>11787.53</td><td>5913.84</td><td>8725.34</td></tr><tr><td>DP-FedExP</td><td>8221.33</td><td>11847.57</td><td>6310.38</td><td>9359.29</td></tr><tr><td>GCFL</td><td>7598.38</td><td>11347.57</td><td>5639.38</td><td>8772.95</td></tr></table>

As illustrated in Figure 6(a), under moderate noise  $\sigma =$  0.8), all methods initially exhibit rapid loss reduction. DPFedExP, utilizing its POCS extrapolation mechanism, achieves the fastest initial convergence, reducing the loss to approximately 0.35 within the first 30 rounds. However, its convergence rate diminishes notably between rounds 30 and 50, accompanied by minor loss oscillations thereafter. DP- FedProx and DP- Scaffold also display fluctuations in later training stages, suggesting that conventional regularization or global correction mechanisms offer limited mitigation of gradient deviations under these conditions. In contrast, GCFL maintains a consistently smooth loss decrease throughout training, ultimately converging to the lowest loss value. This result confirms that the gradient projection- based correction mechanism effectively filters noise components, promotes stable convergence, and enhances final model performance.

Furthermore, under stricter privacy constraints  $\sigma = 1.0$  ),as shown in Figure 6(b), the initial convergence of all baseline methods is significantly impeded. DP- FedExP's loss decreases only to about 0.65 within the first 20 rounds before plateauing. DP- FedExg exhibits substantial fluctuations under high noise, with its final converged loss remaining above 0.6, indicating its simple averaging strategy is insufficient to counteract strong noise interference. Although DP- FedProx and DP- Scaffold demonstrate a marginal advantage during the intermediate stages, they also suffer from oscillations and an increase in loss after round 40. In sharp contrast, GCFL sustains a continuous and stable loss reduction even in this high- noise setting. After round 50, its loss drops below 0.25, outperforming the secondbest method by achieving a final loss that is approximately  $3\% - 5\%$  lower. This underscores the effectiveness of the proposed gradient correction mechanism in eliminating erroneous gradient components while preserving informative ones, even within challenging high- noise environments.

To validate the effectiveness of the proposed GCFL framework in addressing the local drift issue under data heterogeneity, we conducted experiments on non- IID datasets. The experimental results in Table IV demonstrate that GCFL consistently outperforms all baseline models across all three evaluation metrics. Specifically, when compared to the simplest baseline model, DP- FedExg, which relies on weighted gradient averaging, GCFL exhibits significant improvements: an increase of  $4.45\%$  in accuracy,  $4.49\%$  in recall, and  $4.70\%$  in F1 score. These findings indicate that GCFL effectively mitigates the local drift issue prevalent in federated learning with non- IID data by more effectively aligning client gradients to a consistent global direction.

In summary, under the same privacy budget, GCFL outperforms all comparison methods on three datasets. At the same time, GCFL maintains the highest accuracy across all privacy budgets, with the most notable improvement observed under strict privacy conditions. This is due to the gradient correction mechanism in GCFL, which effectively counteracts the noise distortion. In contrast, other methods struggle to converge due to excessive noise. In terms of convergence, comparison methods converge quickly in the early stages of training. However, they lag behind our model in the later stages. GCFL demonstrates better stability and final performance over a larger number of iterations, effectively avoiding local optima, continuing to explore the global optimal solution, and coping with local drift issues. Finally, we examined the computational overhead of GCFL, and the results show that with the introduction of a server- side projection correction mechanism, the performance overhead is minimal, resulting in a reasonable trade- off for a significant accuracy improvement.

Finally, we evaluated both the training and inference speeds of GCFL against baseline models. Experiments were conducted with a batch size of 48 and a learning rate of 0.005. As shown in Table V, even with the added gradient projection mechanism on the server, GCFL's training and inference speeds remain comparable to DP- FedEx and DP- Scaffold. DP- FedExg and the built- upon DP- FedExP achieve suboptimal and optimal performance, respectively. We attribute this to FedAvg's simple weighted averaging of client gradients. The modest trade- off in training and inference speed incurred by GCFL is justified by the gains in predictive performance.

# VI. CONCLUSIONS

This paper presents a novel differentially private federated learning framework designed to address the crucial challenges of privacy preservation and model performance enhancement in Cyber- Physical- Social Systems applications. By combining the strengths of differential privacy and federated learning, our framework mitigates the negative impact of noise during model aggregation while guaranteeing data privacy. The innovative server- side gradient correction mechanism, based on gradient projection, plays a vital role in overcoming the challenges posed by noisy updates and local optima. This mechanism ensures data privacy for individual clients and simultaneously boosts the global model's convergence speed and accuracy.

We are considering enhancing the framework's generalizability, for instance, by adapting the loss function to further improve its performance on non- IID datasets. Furthermore, because our framework requires traversing gradients from different clients on the server side, the training speed per iteration is slightly slower than mainstream models. We plan to address this performance bottleneck in future work.

# ACKNOWLEDGMENT

This work was supported in part by the National Natural Science Foundation of China under Grant 92267104 and Grant 62372242, Jiangsu Provincial Major Project on Basic Research of Cutting- edge and Leading Technologies, under grant no. BK20232032, and the Natural Science Foundation of Jiangsu Province under Grant BK20240692.

[1] C. Zhao, Y. Zhao, J. Li, N. Guo, R. Zhu, and T. Qiu, "Deep reinforcement learning for solving the trip planning query," in Advanced Data Mining and Applications, 2023, pp. 569- 583. [2] X. Zhou, W. Liang, A. Kawai, K. Fueda, J. She, and K. I.- K. Wang, "Adaptive segmentation enhanced asynchronous federated learning for sustainable intelligent transportation systems," IEEE Transactions on Intelligent Transportation Systems, vol. 25, no. 7, pp. 6658- 6666, 2024. [3] Y. Sun, C. Liu, X. Tong, and B. Hu, "An integrated medical recommendation mechanism combining promote product singular value decomposition and knowledge graph," in Advanced Data Mining and Applications, 2022, pp. 67- 78. [4] X. Xu, H. Dong, H. Xiang, X. Hu, X. Li, X. Xia, X. Zhang, L. Qi, and W. Dou, "C2lrec: Causal contrastive learning for user cold- start recommendation with social variable," ACM Transactions on Information Systems, 2025. [5] W. Ding, M. Qian, C. Lu, J. Yi, H. Pu, and J. Luo, "Differential evolution with joint adaptation of mutation strategies and control parameters via distributed proximal policy optimization," Tsinghua Science and Technology, 2024. [6] A. Yao, G. Li, X. Li, F. Jiang, J. Xu, and X. Liu, "Differential privacy in edge computing- based smart city applications: Security issues, solutions and future directions," Arxiv, vol. 19, 2023. [7] X. Zhou, Q. Yang, Q. Liu, W. Liang, K. Wang, Z. Liu, J. Ma, and Q. Jin, "Spatial- temporal federated transfer learning with multi- sensor data fusion for cooperative positioning," Information Fusion, vol. 105, 2024. [8] X. Xu, F. Wu, M. Bilal, X. Xia, W. Dou, L. Yao, and W. Zhong, "Xrl- shap- cache: an explainable reinforcement learning approach for intelligent edge service caching in content delivery networks," Science China Information Sciences, vol. 67, no. 7, 2024. [9] X. Zhou, W. Liang, I. Kevin, K. Wang, K. Yada, L. T. Yang, J. Ma, and Q. Jin, "Decentralized federated graph learning with lightweight zero trust architecture for next- generation networking security," IEEE Journal on Selected Areas in Communications, 2025. [10] V. A. Kanthur, S. Rajagecar, P. Rathore, R. R. M. Doss, L. Pan, B. Ray, M. Chowdhury, C. Srimathi, and M. A. S. Durai, "Cyber attack detection in iot networks with small samples: Implementation and analysis," in Advanced Data Mining and Applications, 2022, pp. 118- 130. [11] X. Zhou, X. Zheng, X. Cui, J. Shi, W. Liang, Z. Yan, L. T. Yang, S. Shimizu, and K. I.- K. Wang, "Digital twin enhanced federated reinforcement learning with lightweight knowledge distillation in mobile networks," IEEE Journal on Selected Areas in Communications, vol. 41, no. 10, pp. 3191- 3211, 2023. [12] R. Li, H. Wang, Q. Lu, J. Yan, S. Ji, and Y. Ma, "Research on medical image classification based on improved fedavg algorithm," Tsinghua Science and Technology, 2025. [13] J. Li, X. Zhang, H. Xiang, and A. Beheshti, "Federated anomaly detection with isolation forest for iot network traffics," in 2023 IEEE 29th International Conference on Parallel and Distributed Systems (ICPADS), 2023, pp. 2622- 2629. [14] C. Song, T. Ristenpart, and V. Shmatikov, "Machine learning models that remember too much," in Proceedings of the 2017 ACM SIGSAC Conference on Computer and Communications Security, 2017, pp. 587- 601. [15] K. Mahmood, X. Li, S. A. Chaudhry, H. Naqvi, S. Kumari, A. K. Sangaiah, and J. I. Rodrigues, "Pairing based anonymous and secure key agreement protocol for smart grid edge computing infrastructure," Future Generation Computer Systems, vol. 88, pp. 491- 500, 2018. [16] X. Zhou, X. Zheng, T. Shi, W. Liang, K. I.- K. Wang, L. Qi, S. Shimizu, and Q. Jin, "Information theoretic learning- enhanced dual- generative adversarial networks with causal representation for robust ood generalization," IEEE Transactions on Neural Networks and Learning Systems, pp. 1- 14, 2023. [17] L. Melis, C. Song, E. De Cristofaro, and V. Shmatikov, "Exploiting unintended feature leakage in collaborative learning," in 2019 IEEE Symposium on Security and Privacy, 2019, pp. 691- 706. [18] Y. Tian, Y. Shi, and Y. Zhang, "Differentially private system for residential energy management via markov decision process," Tsinghua Science and Technology, 2024. [19] J. Fu, Z. Chen, and X. Han, "Adap dp- fi: Differentially private federated learning with adaptive noise," in 2022 IEEE International Conference on Trust, Security and Privacy in Computing and Communications, 2022, pp. 656- 663.

[20] X. Zhou, J. Wu, W. Liang, K. I.- K. Wang, Z. Yan, L. T. Yang, and Q. Jin, "Reconstructed graph neural network with knowledge distillation for lightweight anomaly detection," IEEE Transactions on Neural Networks and Learning Systems, 2024. [21] X. Zhou, X. Ye, I. Kevin, K. Wang, W. Liang, N. K. C. Nair, S. Shimizu, Z. Yan, and Q. Jin, "Hierarchical federated learning with social context clustering- based participant selection for internet of medical things applications," IEEE Transactions on Computational Social Systems, vol. 10, no. 4, pp. 1742- 1751, 2023. [22] K. Munjal and R. Bhatia, "A systematic review of homomorphic encryption and its contributions in healthcare industry," Complex & Intelligent Systems, vol. 9, no. 4, pp. 3759- 3786, 2023. [23] B. McMahan, E. Moore, D. Ramage, S. Hampson, and B. A. y. Arcas, "Communication- efficient learning of deep networks from decentralized data," in Artificial Intelligence and Statistics, 2017, pp. 1273- 1282. [24] T. Li, A. K. Sahu, M. Zaheer, M. Sanjabi, A. Talwalkar, and V. Smith, "Federated optimization in heterogeneous networks," Proceedings of Machine Learning and Systems, vol. 2, pp. 429- 450, 2020. [25] S. P. Karimireddy, S. Kale, M. Mohri, S. Reddi, S. Stich, and A. T. Suresh, "Scaffold: Stochastic controlled averaging for federated learning," in International conference on machine learning, 2020, pp. 5132- 5143. [26] H. Xiang, X. Zhang, X. Xu, A. Beheshti, L. Qi, Y. Hong, and W. Dou, "Federated learning- based anomaly detection with isolation forest in the ion- edge continuum," ACM Transactions on Multimedia Computing, Communications, and Applications, vol. 19, no. 11, 2024. [27] X. Xu, K. Meng, H. Xiang, G. Cui, X. Xia, and W. Dou, "Blockchain- enabled secure, fair and scalable data sharing in zero- trust edge- end environment," IEEE Journal on Selected Areas in Communications, 2025. [28] D. Jhunjhunwala, S. Wang, and G. Joshi, "Fedexp: Speeding up federated averaging via extrapolation," in Proceedings of the 2023 International Conference on Learning Representations, 2023. [29] M. Fredrikson, S. Jha, and T. Ristenpart, "Model inversion attacks that exploit confidence information and basic countermeasures," in Proceedings of the 22nd ACM SIGSAC Conference on Computer and Communications Security, 2015, pp. 1322- 1333. [30] J. Geiping, H. Bauermeister, H. Droge, and M. Moeller, "Inverting gradients—how easy is it to break privacy in federated learning?" Advances in Neural Information Processing Systems, vol. 33, pp. 16937- 16947, 2020. [31] C. Dwork, "Differential privacy," in International Colloquium on Automata, Languages, and Programming, 2006, pp. 1- 12. [32] H. Shen, J. Li, G. Wu, and M. Zhang, "Data release for machine learning via correlated differential privacy," Information Processing & Management, vol. 60, no. 3, 2023. [33] J. Dong, A. Roth, and W. J. Su, "Gaussian differential privacy," Journal of the Royal Statistical Society: Series B (Statistical Methodology), vol. 84, no. 1, pp. 3- 37, 2022. [34] I. Mironov, "Rényi differential privacy," in 2017 IEEE 30th Computer Security Foundations Symposium, 2017, pp. 263- 275. [35] F. K. Dankar and K. El Emam, "Practicing differential privacy in health care: A review," Transactions on Data Privacy, vol. 6, no. 1, pp. 35- 67, 2013. [36] A. Hernandez- Matamoros and H. Kikuchi, "Comparative analysis of local differential privacy schemes in healthcare datasets," Applied Sciences, vol. 14, no. 7, 2024. [37] J. Fu, Q. Ye, H. Hu, Z. Chen, L. Wang, K. Wang, and X. Ran, "Dpsur: Accelerating differentially private stochastic gradient descent using selective update and release," in Proceedings of the 50th International Conference on Very Large Data Bases, 2024, pp. 1175- 1181. [38] J. Fu, Z. Chen, and X. Ling, "Sa- dpsgd: Differentially private stochastic gradient descent based on simulated annealing," CoRR, 2022. [39] X. Zhou, Q. Yang, X. Zheng, W. Liang, K. I.- K. Wang, J. Ma, Y. Pan, and Q. Jin, "Personalized federated learning with model- contrastive learning for multi- modal user modeling in human- centric metaverse," IEEE Journal on Selected Areas in Communications, vol. 42, no. 4, pp. 817- 831, 2024. [40] X. Zhou, W. Huang, W. Liang, Z. Yan, J. Ma, Y. Pan, and K. I.- K. Wang, "Federated distillation and blockchain empowered secure knowledge sharing for internet of medical things," Information Sciences, vol. 662, 2024. [41] C. Dwork, A. Roth et al., "The algorithmic foundations of differential privacy," Foundations and Trends in Theoretical Computer Science, vol. 9, no. 3- 4, pp. 211- 407, 2014.

[42] Y. Huang, F. Xiao, Z. Cao, and C.- T. Lin, "Higher order fractal belief rényi divergence with its applications in pattern classification," IEEE Transactions on Pattern Analysis and Machine Intelligence, 2023.  [43] B. Balle, G. Barthe, M. Gaboardi, J. Hsu, and T. Sato, "Hypothesis testing interpretations and rényi differential privacy," in International Conference on Artificial Intelligence and Statistics, 2020, pp. 2496- 2506.  [44] D. Chicco and G. Jurman, "The advantages of the matthews correlation coefficient (mcc) over f1 score and accuracy in binary classification evaluation," BMC genomics, vol. 21, pp. 1- 13, 2020.

![](images/d086956fec85fc74b4fc362cc12faa3a66d81a3b950fd63a92ca98882d740879.jpg)

Xiaolong Xu (Senior Member, IEEE) was a Research Scholar with Michigan State University, East Lansing, MI, USA, from April 2017 to May 2018. He is currently a Full Professor with the School of Software, Nanjing University of Information Science and Technology, Nanjing. He has authored or co- authored more than 60 peer- reviewed articles in international journals and conferences including TKDE, ITPDS, TSC, TFS, TITS, and ICSOC. His research interests include edge computing, the IoT, cloud computing, big data, and service computing.

![](images/77e2c4167e6c232a4545eb4e8746d1af3aed653a99ebb81a298f6c64c359e751.jpg)

Jiayi Wan is currently pursuing a B.S. degree in Software Engineering at the School of Software Engineering, Nanjing University of Information Science and Technology. His research interests focus on federated learning, differential privacy, and information extraction. Contact <NAME_EMAIL>.

![](images/528a4607769dad9e2173c8dd54ed235ffd35ffcec10589938eca1e8d7432ced7.jpg)

Xiang Zhu received a B.E. degree from Tsinghua University, Beijing, China, in 2010 and an M.E. degree from National University of Defense Technology, Hunan, China, in 2013. Currently, he is Ph.D. student at the College of Computer, National University of Defense Technology, Hunan, China. His research interests include data mining and big data analysis. Xiang Zhu has published in prestigious journals and conferences, such as ICC, TREC, and ICPADS. Contact him at zhuxiang@nudt.

![](images/559dcf5354b606a719ea2c2f5edbe550857a88fbc90ffb4e46165f2e3940a579.jpg)

Fanzhen Liu earned his PhD in Computer Science from Macquarie University, Sydney, Australia, in 2024, where he currently serves as a Postdoctoral Research Fellow. He is also a Visiting Scientist at CSIRO's Data61, Australia. His research interests include graph mining, anomaly detection, trustworthy machine learning, and social network analysis. Dr. Liu has published in prestigious journals and conferences, such as VLDB J., IEEE TKDE, IEEE TNNLS, KDD, ICDM, and IJCAI. Contact him at Fanzhen.Liu@data61. csiro.au

![](images/c1fa39869b50c45fbcd56128d119f3aef766cebb39fa07738eb44176eb31b9f8.jpg)

Wei Fan is currently working as a Postdoctoral Researcher in the Medical Sciences Division at the University of Oxford, UK. His research focuses on data- centric AI, time series modeling, and spatial- temporal data mining. Wei earned his Ph.D. degree in Computer Science from the University of Central Florida. Wei has published over 20 papers in ICLR, NeurIPS, AAAI, IJCAI, TKDE, etc. Contact <NAME_EMAIL>.