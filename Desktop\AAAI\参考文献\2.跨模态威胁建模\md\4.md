# Detecting Backdoor Attacks in Federated Learning via Direction Alignment Inspection

<PERSON><PERSON><PERSON> of Nevada, Reno  {jiahao<PERSON>,zika<PERSON>,ruihu}@du

# Abstract

The distributed nature of training makes Federated Learning (FL) vulnerable to backdoor attacks, where malicious model updates aim to compromise the global model's performance on specific tasks. Existing defense methods show limited efficacy as they overlook the inconsistency between benign and malicious model updates regarding both general and fine- grained directions. To fill this gap, we introduce AlignIns, a novel defense method designed to safeguard FL systems against backdoor attacks. AlignIns looks into the direction of each model update through a direction alignment inspection process. Specifically, it examines the alignment of model updates with the overall update direction and analyzes the distribution of the signs of their significant parameters, comparing them with the principle sign across all model updates. Model updates that exhibit an unusual degree of alignment are considered malicious and thus be filtered out. We provide the theoretical analysis of the robustness of AlignIns and its propagation error in FL. Our empirical results on both independent and identically distributed (IID) and non- IID datasets demonstrate that AlignIns achieves higher robustness compared to the state- of- the- art defense methods. The code is available at https://github.com/JiahaoXU/AlignIns.

# 1. Introduction

Unlike traditional centralized training methods, which require gathering and processing all data at a central location such as a server, Federated Learning (FL) [32], as a decentralized training paradigm, allows a global model to learn from data distributed across various local clients, thereby achieving the goal of privacy- preserving. During training, the server distributes the global model to local clients, and each client trains the received global model using its local dataset, and then submits its local model update to the server for global model refinement. FL has been applied in various fields, including healthcare[35], finance [28], and remote sensing [27], where local data privacy is essential.

Although promising, the distributed nature of FL systems makes them vulnerable to a range of advanced poisoning attacks [15, 26, 45]. This vulnerability primarily stems from the server's lack of close monitoring of the local data and the training algorithm on clients. Consequently, this drawback allows attackers to compromise the data of local clients or interfere with the training algorithm, enabling them to inject malicious local model updates that distort the performance of the global model. For example, backdoor attacks [4, 14, 46, 48, 54] have gained significant attention due to their stealthiness and practical effectiveness. In detail, backdoor attacks in FL seek to preserve the performance of the global model on clean inputs (i.e., the main task), while inducing the global model to make incorrect predictions on inputs that contain a certain predefined feature (i.e., the backdoor task). As backdoor attacks maintain the main task and the backdoor task simultaneously, the malicious local model updates are statistically similar to benign ones [36, 46] (poison- coupling effect [20]), making anomaly detection more challenging on the server side.

Existing defense methods (i.e., aggregation rule) usually aim to identify malicious model updates and filter them out to achieve better robustness by magnitude- based metrics extracted from local model updates (e.g., Manhattan distance [19, 22] and Euclidean distance [6, 15]). However, magnitude- based metrics are ineffective in distinguishing stealthy backdoor attacks where benign and malicious model updates are usually similar in magnitude. Additionally, when the global model tends to converge, the magnitude of each model update becomes very small, making effective malicious manipulation on magnitude negligible. To this end, some works employ Cosine similarity to check the pair- wise directional information of model updates [11, 36, 42]. However, pair- wise Cosine similarity between two model updates only captures their general directional similarity and overlooks fine- grained information (e.g., signs of parameters), resulting in limited robustness. In addition, in FL settings with non- IID data, the pair- wise Cosine similarity of model updates can be easily perturbed

by the naturally diverse benign model updates. Furthermore, there is a deficiency in theoretical analysis within the literature concerning the effects of data heterogeneity on defense methods deployed by the server in FL.

In this work, we propose a novel defense method designed to defend against backdoor attacks in FL, named AlignIns (Direction Alignment Inspection), which examines local model updates for directional alignment at different granularity levels to identify malicious updates. Specifically, after receiving all model updates from clients, AlignIns evaluates each update by (1) inspecting temporal directional alignment with the global model of the latest round with Cosine similarity and (2) assessing more fine- grained sign alignment with the principal sign across all updates with a novel metric sign alignment ratio. Particularly, when calculating the sign alignment ratio, AlignIns focuses on the signs of important parameters in each update to accurately capture alignment information. Using these two directional metrics, AlignIns performs anomaly detection with the robust MZ_score which requires minimal hyperparameters to filter updates with unusual directional patterns out. Finally, AlignIns clips the remaining updates to mitigate the impact of updates with abnormally large magnitudes. We also provide a theoretical analysis of AlignIns' robustness and its propagation error in FL. The main contributions of this work are three folds:

We present a novel defense method, AlignIns, to defend against backdoor attacks in FL. To the best of our knowledge, AlignIns is the first defense method in FL that analyzes the directional patterns of local model updates at different levels of granularity. AlignIns is fully compatible with existing FL frameworks. To the best of our knowledge, we provide the first theoretical robustness analysis for a filtering- based defense method against backdoor attacks under non- IID data in FL. Moreover, we prove that the propagation error of AlignIns is bounded during the training of FL. We empirically evaluate the effectiveness of AlignIns through extensive experiments on both IID and non- IID datasets against various state- of- the- art (SOTA) backdoor attacks. Compared to existing SOTA defense methods, AlignIns exhibits superior robustness.

# 2. Background and Related Works

Federated Learning. In a typical FL system, a central server controls a set of  $n$  clients to train a global model  $\theta \in \mathbb{R}^d$  collaboratively. The objective of FL is to solve the following optimization problem:  $\begin{array}{r}\min_{\theta}(1 / n)\sum_{i = 1}^{n}\mathcal{L}_i(\theta ;\mathcal{D}_i) \end{array}$  where  $\mathcal{L}_i(\cdot)$  denotes the learning objective specific to client  $i$  and  $\mathcal{D}_i$  denotes the local dataset for client  $i$  .The commonly used method to solve this problem iteratively is Fe $\mathrm{dAvg}$  [33]. In detail, at round  $t$  of FedAvg, each client  $i\in [n]$  downloads the current global model  $\theta^t$  , updates it by optimizing its local objective, resulting in  $\theta_i^t$  , and transmits its model update  $\Delta_i^t = \theta_i^t - \theta^t$  to the server. The server then refines the global model by averaging these updates as follows:  $\begin{array}{r}\theta^{t + 1} = \theta^t +(1 / n)\sum_{i = 1}^{n}\Delta_i^t \end{array}$  . This process continues until the global model reaches convergence.

Backdoor attacks in FL. Empirical evidence has shown that FL is vulnerable to backdoor attacks [4, 9, 14, 23, 37, 46, 48, 52, 54] due to its lack of access to local training data [4]. For instance, Projected Gradient Descent (PGD) attack [46] periodically projects the local model onto a small sphere centered around the global model from the previous training round, with a predefined radius. Distributed Backdoor Attack (DBA) [48] decomposes the centralized trigger into several smaller, distributed local triggers. Each poisoned client uses one of these local triggers, but during testing, the adversary injects the full trigger into the test samples. Recently, research has focused on trigger- optimization backdoor attacks [1, 9, 29, 37, 52], which aim to search optimized triggers to enhance the effectiveness and stealthiness.

Defending against backdoor attacks in FL. Generally, based on how defense methods mitigate the impact of malicious updates, existing defense methods can be categorized into filtering- based methods [6, 7, 19, 22, 36, 42, 50, 51] and influence- reduction methods [11, 20, 38, 40, 41].

1) influence-reduction methods aim to integrate all model updates but employ strategies to reduce the impact of malicious updates. For instance, RFA [40] is proposed to use the geometric median of local models as the aggregation result, under the assumption that malicious models significantly deviate from benign models. Foolsgold [11] assumes that the malicious updates are consistent with each other. It assigns aggregation weights to model updates based on the maximum Cosine similarity between the last layers of pairwise model updates. A higher Cosine similarity value indicates a higher probability that the updates are malicious, leading to smaller aggregation weights being assigned. The effectiveness of influence-reduction methods is inherently limited because they cannot eliminate the impact of malicious activity, leading to a significant risk of compromise.

2) Filtering-based methods aim to detect and remove malicious local model updates before aggregation thus attempting to achieve the highest robustness. For example, Multi-Krum [6] selects the multiply reliable local model updates for aggregation by identifying the one with the smallest sum of squared Euclidean distances to all other updates. Multi-Metrics [19] explores the combination of Manhattan distance, Euclidean distance, and Cosine similarity for each update to collaboratively filter out outliers. However, due to the dual objectives of backdoor attacks—that is, maintaining accuracy on the main task while maximizing accuracy on the backdoor task—malicious updates must mimic benign model updates, important weights for the main task

# Algorithm 1: AlignIns

Input: Set of  $n$  local model updates  $\{\Delta_i^t\}_{i = 1}^n$  where  $m$  of them are malicious, global model  $\theta^t$  TDA radius  $\lambda_{c}$  , MPSA radius  $\lambda_{s}$  , extraction parameter  $k$  Output: Aggregated model update  $\tilde{\Delta}$  1 Initialize benign set  $S\gets \emptyset$  2  $\begin{array}{r}\omega \leftarrow \{\mathrm{TDA}(\Delta_i^t,\theta)\}_{i = 1}^n \end{array}$  < by Equation (1) 3  $\begin{array}{r}p\gets \mathrm{sgn}(\sum_{i = 1}^{n}\mathrm{sgn}(\Delta_{i}^{t})) \end{array}$  4  $\begin{array}{r}\rho \gets \{\mathrm{MPSA}(\Delta_i^t,p,k)\}_{i = 1}^n \end{array}$  < by Equation (2) 5 for  $i\in [n]$  do 6  $\lambda_{i,c}\gets \mathrm{MZ\_score}(\omega_i,\omega)$  < by Equation (3) 7  $\lambda_{i,s}\gets \mathrm{MZ\_score}(\rho_i,\rho)$  < by Equation (3) 8 if  $|\lambda_{i,c}|\leq \lambda_{c}$  and  $|\lambda_{i,s}|\leq \lambda_{s}$  then 9  $S\gets S\cup \{i\}$  10 end 11 end 12  $\begin{array}{rl} & c\leftarrow \operatorname {med}(\{\| \Delta_i^t\| \} _i\in \mathcal{S})\\ & \widetilde{\Delta}\leftarrow (1 / |S|)\sum_{i\in \mathcal{S}}(\Delta_i^t\cdot \min \{1,c / \| \Delta_i^t\| \}) \end{array}$  13 14 return  $\tilde{\Delta}$

typically have large values and can dominate the magnitude of a model update. As a result, magnitude- based detection methods become ineffective against backdoor attacks. Additionally, methods that rely solely on Cosine similarity also show limited effectiveness since they capture general directional alignment and overlook finer- grained information.

# 3.Our Solution: AlignIns

Our method, AlignIns, detailed in Algorithm 1, mitigates the impact of malicious updates through a two- step process. First, direction alignment inspection is applied to examine each local model update comprehensively in terms of direction. Second, post- filtering model clipping is used to further enhance the robustness of AlignIns on defending potential magnitude- based attack methods before final aggregation.

Direction alignment inspection. Existing defense methods against backdoor attacks in FL primarily focus on examining the magnitude (e.g., Manhattan distance and Euclidean distance) and the overall direction (e.g., Cosine similarity) of model updates. However, backdoor attacks are designed to maintain the main task accuracy, making the magnitude difference between malicious and benign updates nearly indistinguishable. Additionally, advanced attacks such as PGD [4c] and Lie [5] attacks are specifically crafted to bypass magnitude- based defenses. Therefore, AlignIns focuses on direction- based analysis to identify suspect updates, using two processes described below.

1) Temporal direction alignment checking: Since malicious clients need to maintain both the main task and the backdoor task, the optimization direction of a malicious

local model tends to deviate from that of benign models. AlignIns leverages this deviation and performs a Temporal Direction Alignment (TDA) checking, which calculates the Cosine similarity between a local update and the latest global model (line 2 in Algorithm 1) to assess the general alignment level of each local update. Formally, the TDA value  $\omega_{i}$  of a local model update  $\Delta_i^t$  is calculated as

$$
\omega_{i}\coloneqq \langle \Delta_{i}^{t},\theta^{t}\rangle /(\| \Delta_{i}^{t}\| /\| \theta^{t}\|). \tag{1}
$$

We use local model updates rather than local models because our goal is to measure how closely each client's updates align with the direction of the global model. Local model updates specifically capture these incremental adjustments. Notably, malicious clients tend to exhibit similar TDA values, which differ from those of benign clients, creating an opportunity for detection. It is important to note that while the magnitude of model updates typically decreases as the global model converges, the TDA value does not follow the same trend. Consequently, magnitudebased anomaly detection becomes progressively less effective throughout training due to the decreasing magnitude. In contrast, the variability in TDA values continues to be useful for identifying malicious behavior.

2) Masked principal sign alignment checking: In backdoor attacks where the manipulations are stealthy, subtle malicious directional information can easily blend into the parameters of models with large magnitude, especially for models with large dimensions, which makes the TDA less useful under strong backdoor attacks since the TDA captures the overall directional information. Therefore, in addition to the TDA, we look into the signs of parameters to provide a finer-grained directional assessment of local model updates. The signs of a vector represent its coordinate-wise direction. In the context of backdoor attacks, the distributions of the signs of malicious model updates differ from those of benign updates. This is particularly significant when the model is close to convergence, at which point the magnitude of model updates becomes very small, making large manipulations on magnitudes impractical. Therefore, manipulation of the direction, or the signs of parameters, can emerge as a more significant and effective strategy. Several works also utilize the signs of models for enhancing backdoor robustness. For example, RLR [38] assigns an opposite global learning rate to a coordinate of the averaged model update if the signs on this coordinate do not consistently align with the majority across all updates. SignGuard [49] calculates the proportions of positive, zero, and negative signs for each model update as the input of a clustering algorithm to identify malicious model updates. However, these methods utilize the signs of all parameters in the model update, regardless of their significance. Consequently, the performance of sign-based metrics can be significantly impacted by those many unimportant parameters,

especially for large DNN models, leading to an inaccurate representation of the model update's direction.

To this end, AlignIns utilizes a Masked Principle Sign Alignment (MPSA) checking to inspect the sign alignment degree between the important parameters of each local update and a well- designed principle sign of all local updates. Specifically, to construct the principle sign over local updates, for each coordinate of local updates, we take the majority of the signs across all model updates as the principal sign of this coordinate, which can be mathematically formulated as  $p \coloneqq \operatorname {sgn}\left(\sum_{i = 1}^{n} \operatorname {sgn}\left(\Delta_i^k\right)\right)$ , where  $p \in \mathbb{R}^d$  represents the vector of principal signs and  $\operatorname {sgn}(\cdot)$  is the function to take the signs of a vector. Note that the principal sign represents sign- voting results for each coordinate, making it stand for the major direction/dynamic for each coordinate. With this principle sign over local updates, we inspect the alignment of the signs of important parameters of each model update with it. More specifically, we use a Top-  $k$  indicator defined as follows to identify the  $k$  most important parameters that have the largest absolute values in a vector.

Definition 1 (Top-  $k$  Indicator  $\mathrm{Top_k(\cdot)}$ ). For a vector  $x \in \mathbb{R}^d$  and a masking parameter  $k$  where  $1 \leq k \leq d$ , the Top-  $k$  indicator  $\mathrm{Top_k(\cdot)}$ :  $\mathbb{R}^d = \mathbb{R}^d$  is defined as  $[\mathrm{Top_k}(x)]_j = 1$  if  $[x]_j \in \xi$  and  $[\mathrm{Top_k}(x)]_j = 0$  otherwise, where  $\xi = \{|x_{\pi (1)}|, |x_{\pi (2)}|, \ldots , |x_{\pi (k)}|\}$ , here  $\pi$  is a permutation of  $[d]$  such that  $|x_{\pi (i)}| \geq |x_{\pi (i + 1)}|$  for all  $1 \leq i < d$ .

The Top-  $k$  indicator  $\mathrm{Top_k(\cdot)}$  takes each local model update as input and outputs a mask vector in which each element is either 1 or 0 with the same size as the input. To quantify the alignment in sign distributions of each local model update and the principle sign, we define the Sign Alignment Ratio (SAR) as follows.

Definition 2 (Sign Alignment Ratio). For vectors  $x \in \mathbb{R}^d$  and  $y \in \mathbb{R}^d$ , the sign alignment ratio  $\rho$  of  $x$  to  $y$  is defined as  $\rho \coloneqq 1 - \| \operatorname {sgn}(x) - \operatorname {sgn}(y)\| _0 / d$  where  $\| \cdot \| _0$  is  $L_0$ - norm.

Here,  $\rho \in [0,1]$  and a larger  $\rho$  indicate a higher degree of alignment between the signs of  $x$  and  $y$ . Combining  $\mathrm{Top_k(\cdot)}$  and SAR, we have the MPSA value  $\rho_i$  for local update  $\Delta_i^t$  formulated as follows:

$$
\rho_{i}\coloneqq 1 - \left\| \left(\mathrm{sgn}(\Delta_{i}^{t}) - p\right)\odot \mathrm{Top}_{k}(\Delta_{i}^{t})\right\|_{0} / k, \tag{2}
$$

where  $\odot$  is the Hadamard product,  $\operatorname {sgn}(\Delta_i^t) - p$  computes a sign difference vector, capturing the difference between the sign of  $\Delta_i^t$  and the principal reference sign  $p$ . Since MPSA checking focuses on the important parameters, this difference vector is element- wise multiplied with the Top-  $k$  mask derived from  $\Delta_i^t$ , effectively setting unimportant coordinates to zero. The  $L_0$ - norm is then applied to count the not- aligned elements and with the masking parameter  $k$  to ultimately determine the SAR. MPSA checking effectively reveals malicious local updates by combining both magnitude and directional information from model updates, allowing for clear differentiation between malicious and benign updates. AlignIns calculates the MPSA value for each update with the principal sign iteratively (line 3- 4) and forward them to the following anomaly detection process.

3) Efficient anomaly detection with MZ_score: W apply robust filtering to remove updates with abnormal TDA and MPSA values. Specifically, we use the robust standardization metric named the Median-based Z-score (MZ_score) [50, 51], detailed in Definition 3, which is a variant of the traditional Z-score standardization metric.

Definition 3 (MZ_score). For a set of values  $X \coloneqq \{x_1, \ldots , x_n\}$  with median  $\operatorname {med}(X)$  and standard deviation  $\sigma$ , the MZ_score  $\lambda_i$  of any  $x_i \in X$  is defined as

$$
\lambda_{i}\coloneqq (x_{i} - \mathrm{med}(X)) / \sigma . \tag{3}
$$

MZ_score calculates the number of standard deviations an element is from the median, which may be either positive or negative. In AlignIns, the MZ_scores for TDA and MPSA values are computed for each local update (line 6- 7). Those with high absolute MZ_scores (i.e., outliers) are excluded using two predetermined filtering radii:  $\lambda_c$  for TDA and  $\lambda_s$  for MPSA (line 8- 9). The use of the MZ_score allows for the adaptation to the varying range of TDA and MPSA values during training, requiring only minimal hyper- parameters. Additionally, by configuring  $\lambda_c$  and  $\lambda_s$ , we can manage the trade- off between the robustness and main task accuracy of AlignIns. For example, when robustness is the primary concern in the FL, choosing small  $\lambda_c$  and  $\lambda_s$  values is essential to attain the highest robustness.

Post- filtering model clipping. After filtering, the remaining clients, considered benign, are included in the set  $\mathcal{S}$  (line 9) and contribute to the model averaging process. However, since our filtering primarily focuses on the direction of model updates (although MPSA does consider magnitude when using the Top-  $k$  indicator), there is a risk that it might overlook updates with large magnitudes, such as those updates generated by Scaling attack [4]. To this end, AlignIns re- scales model updates in  $\mathcal{S}$  by using the median of the  $L_2$ - norms of these updates as a clipping threshold and aggregates the clipped model updates as the global model update  $\Delta_i$  (line 12- 13). It is worth noting that performing clipping before filtering does not affect the filtering results. However, clipping after filtering enhances robustness, as the clipping threshold is more likely determined by benign updates. We discuss the computational cost of AlignIns and compare it with other baselines in Appendix Section 12.

# 4. Robustness and Propagation Error Analysis

In this section, we conduct a theoretical analysis of the robustness of AlignIns, as well as its propagation error in FL.

Before presenting our theoretical results, we make the following assumptions. Note that Assumption 1- 2 are commonly used in the theoretical analysis of distributed learning systems [18, 34, 49]. Assumption 3 states a standard measure of inter- client heterogeneity in FL [2, 8, 21]. This heterogeneity complicates the problem of FL with backdoor adversaries, as it may cause the server to confuse malicious updates with flawed model updates from benign clients holding outlier data points [2].

Assumption 1 ( $\mu$ - smoothness [34]). Each local objective function  $\mathcal{L}_i$  for benign client  $i \in \mathcal{B}$  is  $\mu$ - Lipschitz smooth with  $\mu > 0$ , i.e., for any  $x, y \in \mathbb{R}^d$ ,  $\| \nabla \mathcal{L}_i(x) - \nabla \mathcal{L}_i(y)\| \leq \mu \| x - y\| , \forall i \in \mathcal{B}$ , which further gives:  $\mathcal{L}_i(x) - \mathcal{L}_i(y) \leq \nabla \mathcal{L}_i(x)^T (y - x) + (\mu /2)\| x - y\| ^2, \forall i \in \mathcal{B}$ .

Assumption 2 (Unbiased gradient and bounded variance). The stochastic gradient at each benign client is an unbiased estimator of the local gradient, i.e.,  $\mathbb{E}[g_i(x)] = \nabla \mathcal{L}_i(x)$  and has bounded variance, i.e., for any  $x \in \mathbb{R}^d$ ,  $\mathbb{E}\| g_i(x) - \nabla \mathcal{L}_i(x)\| ^2 \leq \nu_i^2, \forall i \in \mathcal{B}$ , where the expectation is over the local mini- batches. We also denote  $\bar{\nu} \coloneqq (1 / |\mathcal{B}|)\sum_{i \in \mathcal{B}} \nu_i^2$  for convenience.

Assumption 3 (Bounded heterogeneity). There exist a real value  $\bar{\zeta}$  such that for any  $x \in \mathbb{R}^d$ ,  $(1 / |\mathcal{B}|)\sum_{i \in \mathcal{B}} \| \nabla \mathcal{L}_i(x) - \nabla \mathcal{L}_\mathcal{B}(x)\| ^2 \leq \bar{\zeta}$ , where the  $\nabla \mathcal{L}_\mathcal{B}(x) = (1 / |\mathcal{B}|)\sum_{i \in \mathcal{B}} \mathcal{L}_i(x)$ .

Note that these assumptions apply to benign clients only since malicious clients do not need to follow the prescribed local training protocol of FL.

# 4.1. Robustness Analysis of AlignIns

To theoretically evaluate the efficacy of a filtering- based defense method like AlignIns, we introduce the concept of  $\kappa$ - robust filtering [50] as defined in Definition 4. Note that Definition 4 is similar to  $(f, \kappa)$ - robustness defined in [2, 3],  $(\delta_{\max}, c)$ - ARAgg defined in [13, 21, 31], and  $(f, \lambda)$ - resilient averaging defined in [10]. Our robustness definition adopts a constant upper bound and focuses on quantifying the distance between the output of a filtering- based defense method and the average of all benign updates, which represents the optimal output of such a rule.

Definition 4 ( $\kappa$ - robust filtering [50]). A filtering- based aggregation rule  $F \colon \mathbb{R}^{d \times n} \to \mathbb{R}^d$  is called  $\kappa$ - robust if for any vectors  $\{x_1, \ldots , x_n\} \in \mathbb{R}^d$  and a benign set  $\mathcal{B} \subseteq [n]$  of size  $n - m$ , the output  $\hat{x} \coloneqq F(x_1, \ldots , x_n)$  satisfies  $\| \hat{x} - \bar{x}_\mathcal{B}\| ^2 \leq \kappa$ , where  $\bar{x}_\mathcal{B} \coloneqq (1 / |\mathcal{B}|)\sum_{i \in \mathcal{B}} x_i$ , and  $\kappa \geq 0$  refers to the robustness coefficient of  $F$ .

Remark 1. The  $\kappa$ - robust filtering guarantees that the error of a filtering- based aggregation rule in estimating the average of the benign inputs is upper- bounded by a constant  $\kappa$ .

This measure provides a quantitative way to assess the robustness of the filtering- based aggregation rule. A smaller  $\kappa$  indicates a smaller discrepancy between the empirical output and the optimal output of  $F$ . If  $F$  identifies and removes all malicious inputs and keeps all benign inputs, we have  $\kappa = 0$ , achieving the highest level of robustness.

Based on Definition 4, we prove that the proposed AlignIns, when applied to  $n$  input models, of which  $m$  are malicious, satisfies  $\kappa$ - robust filtering with  $\kappa = O(1 + m / (n - 2m))$ , as stated in Lemma 1.

Lemma 1 ( $\kappa$ - robustness of AlignIns). Under Assumption 2- 3, assume  $n > 1, 0 \leq m < n / (3 + \epsilon)$  with a positive constant  $\epsilon$ , AlignIns satisfies  $\kappa$ - robust filtering with

$$
\begin{array}{rl} & {\kappa = (1 + m / (n - 2m))((2 / \epsilon +1)(2\bar{\nu} +\bar{\zeta}) + 8c^2)}\\ & {\quad = O(1 + m / (n - 2m)),} \end{array}
$$

if the local learning rate satisfies  $\eta \leq 1 / 2\tau$  and there exist two sufficiently large filtering radii such that  $|S|\geq n-$ $2m$  .Here,  $\bar{\nu}$  and  $\bar{\zeta}$  represent the gradient variance and local divergence, respectively;  $c$  is the clipping threshold.

Proof. The proof is given in Appendix Section 13.2.

Remark 2. The condition on  $S$  highlights the importance of selecting appropriate filtering radii. These radii cannot be zero or too small; otherwise, only the median or a few model updates will be averaged to update the global model. This can lead to a performance drop due to the lack of model updates. Moreover, the model clipping threshold  $c$  can effectively control the magnitude of potential malicious updates in the selection set, thus preventing  $\kappa$  from exploding due to updates with large magnitudes. Indeed, in the literature, model clipping has demonstrated its effectiveness in mitigating the impact of malicious model updates [39, 49, 53]. In addition, the result also shows the importance of reducing the gradient variance of stochastic gradient and local heterogeneity to enhance robustness performance. Our work is orthogonal to existing variance or divergence reduction methods [13, 20] and can be combined with them to further improve the robustness. We argue that AlignIns enjoys comparable robustness with several classical defense methods, for example, non- filtering- based method RFA [40]  $(O(1 + m / (n - 2m))^2)$ , and filtering- based method Krum [6]  $(O(1 + m / (n - 2m))^1$ .

# 4.2. Propagation Error of AlignIns in FL

Based on the  $\kappa$ - robustness of AlignIns, we analyze its propagation error during training. Specifically, let  $\theta$  denote the

model trained with Fed- AlignIns under backdoor attacks, where  $m$  of the  $n$  clients are malicious, and let  $\theta^{*}$  denote a model trained exclusively with benign clients using Fe $\mathrm{dAvg}$ . Starting from the same initial model  $\theta^0$ , we aim to measure the difference between these two models after  $T$  rounds of training, defined as  $\| \theta^T - \theta^{T,*}\|$ , referred to as the propagation error [39]. Let  $\theta^{t, + }$  represent the output of AlignIns at the  $t$ - th round. If the highest level of robustness is not achieved at round  $t$ , the error  $\| \theta^t - \theta^{t, + }\|$  will propagate to the next round, resulting in a shifted starting point for local SGD at round  $t + 1$ . This discrepancy will gradually widen the gap between  $\theta$  and  $\theta^{*}$ . Our analysis captures this robustness error at each round and examines its cumulative effect after  $T$  rounds. In Lemma 2, we show that, assuming Assumption 1- 3 hold, the propagation error of AlignIns remains bounded.

Lemma 2 (Bounded Propagation Error). Let Assumption 1- 3 hold. If the local learning rate  $\eta \leq 1 / 2\tau$ , the propagation error of AlignIns is bounded as

$$
\begin{array}{r}\| \theta^T -\theta^{T,*}\| \leq \phi (T)(2 + 3\mu^2)^{\phi (T)}(\kappa +2\bar{\nu}), \end{array}
$$

under backdoor attacks where  $m$  out of  $n$  clients are malicious. Here,  $\kappa$  is given in Lemma 1,  $\phi (T) = \sum_{t = 1}^{T}(\alpha^{t})^{2}$  is the cumulative global learning rate, and  $\alpha^{t}$  is a global learning rate scheduler possibly static.

Proof. The detailed proof is in Appendix Section 13.3.

Remark 3. When  $T\rightarrow \infty$ ,  $\phi (T)$  converges to a constant for learning rate schedulers like exponential decay, which implies a constant bounded on propagation error. The result shows that besides the robustness error bounded by  $\kappa$ , the error of local gradient estimation, which is bounded by  $\bar{\nu}$ , in local SGD also propagates during the training, increasing the overall propagation error. This is because at any round  $t$ , if the benign starting point for local training is the same, i.e.,  $\theta^{t} = \theta^{t,*}$ , then the local gradients/model updates on  $\theta^{t}$  and  $\theta^{t,*}$  will be identical for benign clients. Therefore, the gap between the updated global models  $\theta^{t + 1}$  and  $\theta^{t + 1,*}$  solely depends on the robustness error (i.e., the effectiveness of AlignIns in filtering out malicious updates). However, if  $\theta^{t}\neq \theta^{t,*}$ , which means  $\theta^{t}$  is not benign and has been poisoned in previous rounds, the local gradients/model updates on  $\theta^{t}$  and  $\theta^{t,*}$  will differ for benign clients, resulting in an error bounded by the gradient variance, even if AlignIns successfully filters out all malicious updates. Hence, to further reduce the propagation error, AlignIns can be combined with variance- reduction methods like [13, 30], which is orthogonal to AlignIns.

# 5. Experimental Settings

Datasets: In our experiments, we primarily use CIFAR- 10 [24] and CIFAR- 100 [24] datasets to evaluate the per

Datasets: In our experiments, we primarily use CIFAR- 10 [24] and CIFAR- 100 [24] datasets to evaluate the performance of various defense methods. Additionally, we present the superior performance of AlignIns on other benchmark datasets (MNIST [25], FMNIST [47], and Sentiment140 [12]) in Appendix Section 10.6. For all datasets, we simulate a cross- silo FL system with 20 clients. Additionally, we also present the superior performance of AlignIns on a cross- device FL system with 100 clients and client sampling. We consider both IID and non- IID settings. For IID settings, we distribute the training data evenly to local clients. For non- IID settings, we follow [17, 19, 20] to use Dirichlet distribution  $Dir(\beta)$  to simulate the non- IID settings with a default non- IID degree  $\beta = 0.5$ .

Learning Settings: We use SGD as the local solver, with the initial learning rates set as  $\alpha = 1.0$  and  $\eta = 0.1$  and the number of local training epochs set as 2. The number of training rounds is set as  $T = 100$  for CIFAR- 100 and  $T = 150$  for CIFAR- 10. For AlignIns, the default filtering radii are set as  $\lambda_{c} = 1.0$  and  $\lambda_{s} = 1.0$ . We conduct extensive experiments to study the impact of filtering radii and present results and analysis in Appendix Section 11. The default masking parameter is set as  $k = 0.3\times d$ , where  $d$  is the model dimension so that the Top- 30% of model parameters are used for the MPSA checking.

Evaluated Attack Methods: We consider 5 SOTA backdoor attacks, including Badnet [14], DBA [48], Scaling [4], PGD [46], and Neurotoxin [54]. We provide the detailed attack model and settings for attack methods in Appendix Section 8.1- 8.2. We present the empirical performance of AlignIns under the strong trigger- optimization attack [9] in Appendix Section 10.3. Moreover, we study the potential adaptive attacks tailored to AlignIns and untargeted attacks [49] in Appendix Section 10.4- 10.5, although these are beyond the primary scope of this work. To simulate effective backdoor attacks (achieving a BA over 60% [22]), the malicious client will poison  $r = 50\%$  of its local data, where  $r$  represents the data poisoning ratio. The attack ratio is set to 20% by default, which means 20% of the clients in the system are malicious. Experiments of AlignIns on defending backdoor attacks with various attack ratios are given in Appendix Section 10.7.

Evaluated Defense Methods: We present the detailed defense model in Appendix Section 9. We comprehensively compare AlignIns with the non- robust baseline FedAvg and six existing SOTA defense methods, including RLR [38], RFA [40], Multi- Krum (MKrum) [6], Foolsgold [11], Multi- Metric (MM) [19], and Lockdown [20]. Additionally, we compare our approach with an ideally perfect filtering- based robust aggregation, FedAvg*, which is assumed to perfectly identify and remove all malicious updates and average all the benign updates to update the global model.

Evaluation Metrics: We use three metrics to evaluate the performance of defense methods, including main task accuracy (MA), which measures the percentage of clean

Table 1. The clean MA, BA, and RA results of baselines and AlignIns on IID CIFAR-10 and CIFAR-100 datasets. Results are shown in  $\%$  

<table><tr><td rowspan="3">Dataset (Model)</td><td rowspan="3">Methods</td><td rowspan="3">Clean MA↑</td><td colspan="4">Badnet</td><td colspan="4">DBA</td><td colspan="4">Neurotoxin</td><td>Avg. BA↓</td><td>Avg. RA↑</td><td></td></tr><tr><td colspan="2">BA↓</td><td colspan="2">RA↑</td><td colspan="2">BA↓</td><td colspan="2">RA↑</td><td colspan="2">BA↓</td><td colspan="2">RA↑</td><td>Avg. BA↓</td><td rowspan="2">Avg. RA↑</td><td rowspan="2"></td></tr><tr><td>r=0.3</td><td>r=0.5</td><td>r=0.3</td><td>r=0.5</td><td>r=0.3</td><td>r=0.3</td><td>r=0.5</td><td>r=0.3</td><td>r=0.5</td><td>r=0.3</td><td>r=0.5</td><td>r=0.3</td><td>r=0.5</td></tr><tr><td rowspan="9">CIFAR-10 (ResN9 [16])</td><td>FedAvg</td><td>89.47</td><td>51.56</td><td>67.61</td><td>45.79</td><td>31.24</td><td>56.21</td><td>70.42</td><td>40.62</td><td>27.92</td><td>44.89</td><td>79.40</td><td>50.41</td><td>19.60</td><td>61.68</td><td>35.93</td><td></td></tr><tr><td>FedAvg*</td><td>89.47</td><td>2.06</td><td>2.06</td><td>85.60</td><td>85.60</td><td>2.06</td><td>2.06</td><td>85.60</td><td>85.60</td><td>2.06</td><td>2.06</td><td>85.60</td><td>85.60</td><td>2.06</td><td>85.60</td><td></td></tr><tr><td>RLR</td><td>79.16</td><td>2.32</td><td>2.00</td><td>76.72</td><td>73.33</td><td>3.01</td><td>3.04</td><td>77.09</td><td>77.13</td><td>3.12</td><td>3.87</td><td>73.98</td><td>73.29</td><td>2.89</td><td>35.93</td><td></td></tr><tr><td>RFA</td><td>87.73</td><td>70.67</td><td>90.24</td><td>27.74</td><td>9.26</td><td>47.67</td><td>66.97</td><td>47.29</td><td>30.14</td><td>81.27</td><td>96.13</td><td>17.11</td><td>3.69</td><td>75.49</td><td>22.54</td><td></td></tr><tr><td>MKrum</td><td>87.02</td><td>81.10</td><td>97.47</td><td>18.11</td><td>2.51</td><td>2.17</td><td>4.33</td><td>83.89</td><td>79.10</td><td>65.28</td><td>89.18</td><td>31.81</td><td>10.01</td><td>56.59</td><td>37.57</td><td></td></tr><tr><td>Foolsgold</td><td>89.49</td><td>69.14</td><td>68.84</td><td>29.64</td><td>50.10</td><td>51.18</td><td>60.75</td><td>44.85</td><td>56.08</td><td>2.91</td><td>2.82</td><td>83.27</td><td>84.76</td><td>42.60</td><td>51.78</td><td></td></tr><tr><td>MM</td><td>89.15</td><td>41.19</td><td>93.88</td><td>53.88</td><td>6.01</td><td>52.24</td><td>51.30</td><td>43.54</td><td>45.08</td><td>43.92</td><td>83.92</td><td>51.12</td><td>15.11</td><td>61.08</td><td>35.79</td><td></td></tr><tr><td>Lockdown</td><td>88.56</td><td>6.31</td><td>10.82</td><td>81.88</td><td>79.50</td><td>11.63</td><td>6.03</td><td>78.82</td><td>75.77</td><td>3.40</td><td>3.27</td><td>82.73</td><td>83.14</td><td>6.91</td><td>80.31</td><td></td></tr><tr><td>AlignIns</td><td>88.64</td><td>1.91</td><td>2.21</td><td>86.03</td><td>85.57</td><td>2.13</td><td>2.14</td><td>85.77</td><td>85.88</td><td>2.66</td><td>2.20</td><td>85.46</td><td>85.31</td><td>2.21</td><td>85.67</td><td></td></tr><tr><td rowspan="9">CIFAR-100 (YGG9 [44])</td><td>FedAvg</td><td>64.29</td><td>99.20</td><td>99.54</td><td>0.68</td><td>0.35</td><td>99.25</td><td>99.36</td><td>0.64</td><td>0.54</td><td>94.41</td><td>93.36</td><td>4.36</td><td>5.28</td><td>97.52</td><td>1.98</td><td></td></tr><tr><td>FedAvg*</td><td>64.29</td><td>0.62</td><td>0.62</td><td>53.03</td><td>53.03</td><td>0.62</td><td>0.62</td><td>53.03</td><td>53.03</td><td>0.62</td><td>0.62</td><td>53.03</td><td>53.03</td><td>0.62</td><td>53.03</td><td></td></tr><tr><td>RLR</td><td>44.34</td><td>96.57</td><td>99.85</td><td>1.81</td><td>0.12</td><td>24.41</td><td>94.08</td><td>24.97</td><td>3.22</td><td>0.04</td><td>0.00</td><td>29.07</td><td>29.73</td><td>52.49</td><td>14.82</td><td></td></tr><tr><td>RFA</td><td>53.92</td><td>4.32</td><td>1.45</td><td>37.60</td><td>39.88</td><td>2.15</td><td>0.78</td><td>39.73</td><td>41.51</td><td>99.74</td><td>89.59</td><td>0.21</td><td>6.59</td><td>33.01</td><td>27.59</td><td></td></tr><tr><td>MKrum</td><td>51.28</td><td>1.33</td><td>1.54</td><td>38.13</td><td>38.49</td><td>1.36</td><td>1.54</td><td>37.85</td><td>37.91</td><td>99.82</td><td>99.87</td><td>0.12</td><td>0.10</td><td>36.21</td><td>25.49</td><td></td></tr><tr><td>Foolsgold</td><td>64.13</td><td>99.02</td><td>99.30</td><td>0.83</td><td>0.57</td><td>99.15</td><td>99.39</td><td>0.74</td><td>0.51</td><td>21.79</td><td>6.21</td><td>42.06</td><td>46.40</td><td>70.81</td><td>15.19</td><td></td></tr><tr><td>MM</td><td>63.26</td><td>99.51</td><td>99.87</td><td>0.37</td><td>0.11</td><td>99.53</td><td>99.70</td><td>0.35</td><td>0.19</td><td>98.48</td><td>98.97</td><td>1.32</td><td>0.83</td><td>99.34</td><td>0.53</td><td></td></tr><tr><td>Lockdown</td><td>62.88</td><td>55.21</td><td>24.14</td><td>28.45</td><td>43.06</td><td>34.37</td><td>49.02</td><td>34.06</td><td>27.93</td><td>0.85</td><td>0.67</td><td>42.56</td><td>47.04</td><td>27.38</td><td>37.20</td><td></td></tr><tr><td>AlignIns</td><td>63.45</td><td>0.79</td><td>0.71</td><td>50.45</td><td>51.53</td><td>0.45</td><td>0.57</td><td>50.81</td><td>52.08</td><td>0.49</td><td>0.53</td><td>51.11</td><td>50.66</td><td>0.59</td><td>51.11</td><td></td></tr></table>

test samples that are accurately classified to their ground truth labels by the global model; backdoor attack accuracy (BA), which measures the percentage of triggered samples that are misclassified to the target label by the global model; and robustness accuracy (RA), which measures the percentage of triggered samples that are accurately classified to their ground- truth labels by the global model, despite the presence of the trigger. A good defense method should achieve high MA and RA and low BA.

# 6. Experimental Results

Main results in IID setting. In Table 1, we report the performance of various defense methods under no attack (denoted by "Clean"), Badnet, DBA, and Neurotoxin attacks for IID CIFAR- 10 and CIFAR- 100. The best results are highlighted in bold font, and the second best results are underlined. Overall, AlignIns demonstrates superior performance compared with other baselines as it achieves the best average BA and RA over three attack methods. Specifically, for CIFAR- 10, while RLR offers a satisfactory degree of robustness (an average BA of  $2.89\%$ ), it suffers from a notable decline in RA, with an average reduction of  $49.74\%$  in comparison to AlignIns. This drop results from RLR's strategy of flipping the global learning rate for parameters in the aggregated model update that are inconsistent with the majority's sign, consequently resulting in the loss of benign local parameters. AlignIns, however, demonstrates outstanding performance with consistently low BA and high RA, ranking first or second among its counterparts. Notably, compared to the second- best results, AlignIns achieves an average improvement of  $+0.68\%$  in BA and  $+5.36\%$  in RA. Similarly, superior results are observed in CIFAR- 100 experiments, where AlignIns significantly outperforms other methods in both BA and RA. These results underscore AlignIns' effectiveness as a promising defense method for protecting FL from various backdoor attacks, significantly enhancing the trustworthiness of FL systems.

![](images/9a5e9e62b51cfb4937ed67d676b667af23720bbcb15f2f5160bc0b6eaa4739a2.jpg)  
Figure 1. RA of AlignIns under various non-IID degrees, compared with Lockdown, RFA, and RLR under Neurotoxin.

Effectiveness under various Non- IID degrees. We examine the defense performance of AlignIns across various degrees of non- IIDness, a factor that significantly complicates backdoor defense. Figure 1 presents the RA of AlignIns under different non- IID conditions on the CIFAR- 10 dataset, compared with Lockdown, RFA, and RLR. The experiments were conducted using the Neurotoxin attack, with both a default attack ratio of  $20\%$  and a higher attack ratio of  $30\%$ . The Dirichlet parameter  $\beta$  varies from 0.1 to 1.0, where a smaller  $\beta$  suggests a more intense non- IIDness. We observe that only AlignIns consistently attains

Table 2. Performance of different methods in cross-device FL settings on IID and non-IID CIFAR-10 datasets under Badnet attack.  

<table><tr><td rowspan="2">Method</td><td colspan="3">CIFAR-10 (IID)</td><td colspan="3">CIFAR-10 (Non-IID)</td><td rowspan="2">Avg. 
RA↑</td></tr><tr><td>MA↑</td><td>BA↓</td><td>RA↑</td><td>MA↑</td><td>BA↓</td><td>RA↑</td></tr><tr><td>Foolsgold</td><td>82.99</td><td>99.99</td><td>0.01</td><td>67.97</td><td>99.99</td><td>0.00</td><td>0.01</td></tr><tr><td>Lockdown</td><td>83.52</td><td>99.99</td><td>0.00</td><td>73.91</td><td>99.92</td><td>0.06</td><td>0.00</td></tr><tr><td>RRL</td><td>56.81</td><td>4.67</td><td>41.38</td><td>41.56</td><td>14.12</td><td>38.17</td><td>46.78</td></tr><tr><td>AlignIns</td><td>85.01</td><td>0.92</td><td>82.74</td><td>79.51</td><td>1.90</td><td>75.81</td><td>79.28</td></tr></table>

robustness against strong Neurotoxin attacks with a varying  $\beta$  Specifically, as  $\beta$  increases, the RA of AlignIns, Lockdown, and RLR increases correspondingly. However, AlignIns outperforms them with a consistently higher RA. When the attack ratio rises to  $30\%$  RLR,RFA,and Lockdown fail to provide satisfactory robustness. However, our method AlignIns still demonstrates its robustness under various non- IIDness, even in an extremely non- IID case when  $\beta = 0.1$  AlignIns is designed to examine the alignment of model updates on important parameters only, hence, it mitigates the challenge of identifying malicious model updates in non- IID settings where updates are heterogeneous, thereby achieving superior performance in even extreme non- IID settings compared with existing methods. We also provide more comprehensive results of AlignIns and other baselines on non- IID datasets in Appendix Section 10.1.

Effectiveness of AlignIns in cross- device FL with client sampling. While most of our experiments focus on the cross- slo FL setting, evaluating the cross- device FL scenario is also essential given the large number of clients involved. For this purpose, we simulate a cross- device FL environment with 100 clients, where the server randomly selects 20 clients per round for training. We conduct experiments on IID and non- IID CIFAR- 10 cases using Foolsgold, Lockdown, RLR, and AlignIns and summarize the MA,BA,and RA results in Table 2. The results show that both Foolsgold and Lockdown completely lose their effectiveness in both cases, achieving an average RA of nearly  $0.00\%$  . RLR achieves a moderate level of backdoor robustness but at the cost of main task accuracy, with an average MA of only  $49.19\%$  .In contrast, AlignIns performs robustly in the cross- device FL setting, achieving a significantly lower BA in both IID  $(0.92\%)$  and non- IID  $(1.90\%)$  cases compared with other methods. Furthermore, AlignIns achieves an average RA of  $79.28\%$  . These results highlight AlignIns's ability to maintain both accuracy and robustness in challenging cross- device FL scenarios, underscoring its adaptability and effectiveness in real- world applications.

Ablation study of AlignIns. As AlignIns consists of two alignment components (TDA and MPSA) to improve backdoor robustness, we conduct a detailed ablation study to investigate how each component functions. Experimental results on IID and non- IID CIFAR- 10 datasets under Badnet attack are summarized in Table 3. (i) Component ablation. We observe that using MPSA or TDA alone in

Table 3. Performance of different components in AlignIns.  

<table><tr><td rowspan="2">Configuration</td><td colspan="3">CIFAR-10 (IID)</td><td colspan="3">CIFAR-10 (non-IID)</td><td rowspan="2">Avg. 
RA↑</td></tr><tr><td>MA↑</td><td>BA↓</td><td>RA↑</td><td>MA↑</td><td>BA↓</td><td>RA↑</td></tr><tr><td>MPSA(30%)</td><td>88.55</td><td>2.88</td><td>85.02</td><td>80.65</td><td>94.07</td><td>5.79</td><td>45.41</td></tr><tr><td>TDA</td><td>88.56</td><td>3.82</td><td>83.88</td><td>83.86</td><td>77.58</td><td>21.31</td><td>52.60</td></tr><tr><td>MPSA(70%+TDA</td><td>88.14</td><td>2.18</td><td>85.77</td><td>83.84</td><td>61.83</td><td>31.86</td><td>58.82</td></tr><tr><td>MPSA(50%)+TDA</td><td>88.05</td><td>2.21</td><td>85.46</td><td>84.84</td><td>77.93</td><td>19.96</td><td>52.71</td></tr><tr><td>MPSA(30%)+TDA</td><td>88.14</td><td>2.04</td><td>85.82</td><td>82.88</td><td>47.04</td><td>45.30</td><td>65.56</td></tr><tr><td>AlignIns</td><td>88.05</td><td>2.44</td><td>85.27</td><td>82.88</td><td>1.70</td><td>81.32</td><td>83.30</td></tr><tr><td>AlignIns+</td><td>88.48</td><td>2.14</td><td>85.74</td><td>83.31</td><td>1.11</td><td>82.13</td><td>83.94</td></tr></table>

IID scenarios only slightly reduces robustness compared to AlignIns, as benign updates follow consistent patterns that enable effective detection by a single metric. In non- IID settings, however, where local updates diverge, neither MPSA nor TDA alone provides sufficient robustness. When combined, MPSA and TDA improve BA and RA from  $94.07\%$  and  $5.79\%$  to  $47.04\%$  and  $45.30\%$  , respectively, showing their complementary strengths. AlignIns further enhances robustness by integrating MPSA, TDA, and post- filtering model clipping, which normalizes benign update magnitudes and improves malicious update detection, yielding the highest average RA. (ii) Masking parameter  $k$  ablation. We try to involve more non- essential parameters in the MPSA checking by using the Top-  $50\% /70\%$  of parameters to calculate MPSA values. By doing so, the effectiveness of malicious identification is reduced. In contrast, when using the Top-  $30\%$  of parameters, compared to the Top-  $50\%$  case, BA and RA are improved by  $+30.89\%$  and  $+25.34\%$  , respectively. This demonstrates the effectiveness of focusing important parameters when calculating MPSA in improving the filtering accuracy, especially in non- IID cases. (iii) Variance reduction method further enhances robustness. Our theoretical results reveal the impact of variance reduction techniques on improving the robustness of AlignIns and reducing the propagation error of AlignIns in FL, we additionally test a variant of AlignIns named "AlignIns  $^+$  ", in which local SGD with momentum is used to reduce the local gradient variance with momentum coefficient 0.1. AlignIns  $^+$  achieves a slightly better performance than AlignIns, verifying our theoretical results.

# 7.Conclusion

7. ConclusionThis paper introduces a novel defense method AlignIns to defend against backdoor attacks in FL. AlignIns examines each model update's direction at different granularity levels, thus effectively identifying stealthy malicious local model updates and filtering them out to avoid them participating in aggregation in FL to enhance robustness. We provide a theoretical analysis of AlignIns' robustness and its impact on propagation errors in FL. Extensive experiments demonstrate the effectiveness of AlignIns, with results showing that it outperforms SOTA defense methods against various advanced attacks.

# References

[1] Manaar Alam, Esha Sarkar, and Michail Maniatakos. Perdoor: Persistent non- uniform backdoors in federated learning using adversarial perturbations. arXiv preprint arXiv:2205.13523, 2022. 2[2] Youssef Allouah, Sadegh Farhadkhani, Rachid Guerraoui, Nirupam Gupta, Rafael Pinot, and John Stephan. Fixing by mixing: A recipe for optimal byzantine ml under heterogeneity. In International Conference on Artificial Intelligence and Statistics, pages 1232- 1300. PMLR, 2023. 5[3] Youssef Allouah, Refaid Guerraoui, Nirupam Gupta, Rafael Pinot, and Geovani Rizk. Robust distributed learning: Tight error bounds and breakdown point under data heterogeneity. In Thirty- seventh Conference on Neural Information Processing Systems, 2023. 5[4] Eugene Bagdasaryan, Andreas Veit, Yiqing Hua, Deborah Estrin, and Vitaly Shmatikov. How to backdoor federated learning. In International conference on artificial intelligence and statistics, pages 2938- 2948. PMLR, 2020. 1, 2, 4, 6, 12[5] Gilad Baruch, Moran Baruch, and Yoav Goldberg. A little is enough: Circumventing defenses for distributed learning. Advances in Neural Information Processing Systems, 32, 2019. 3, 14[6] Peva Blanchard, El Mahdi El Mhamdi, Rachid Guerraoui, and Julien Stainer. Machine learning with adversaries: Byzantine tolerant gradient descent. Advances in neural information processing systems, 30, 2017. 1, 2, 5, 6, 12[7] Xiaoyu Cao, Minghong Fang, Jia Liu, and Neil Zhenqiang Gong. Fltrust: Byzantine- robust federated learning via trust bootstrapping. arXiv preprint arXiv:2012.13995, 2020. 2, 12[8] El Mahdi El- Mhamdi, Sadegh Farhadkhani, Rachid Guerraoui, Arsany Guirguis, Lê- Nguyên Hoang, and Sébastien Rouault. Collaborative learning in the jungle (decentralized, byzantine, heterogeneous, asynchronous and nonconvex learning). Advances in Neural Information Processing Systems, 34:25044- 25057, 2021. 5[9] Pei Fang and Jinghui Chen. On the vulnerability of backdoor defenses for federated learning. In Proceedings of the AAAI Conference on Artificial Intelligence, pages 11800- 11808, 2023. 2, 6, 13[10] Sadegh Farhadkhani, Rachid Guerraoui, Nirupam Gupta, Rafael Pinot, and John Stephan. Byzantine machine learning made easy by resilient averaging of momentums. In International Conference on Machine Learning, pages 6246- 6283. PMLR, 2022. 5[11] Clement Fung, Chris JM Yoon, and Ivan Beschastnikh. The limitations of federated learning in sybil settings. In 23rd International Symposium on Research in Attacks, Intrusions and Defenses (RAID 2020), pages 301- 316, 2020. 1, 2, 6[12] Alec Go, Richa Bhayani, and Lei Huang. Twitter sentiment classification using distant supervision. CS224N project report, Stanford, 1(12):2009, 2009. 6[13] Eduard Gorbunov, Samuel Horváth, Peter Richtárik, and Gauthier Gidel. Variance reduction is an antidote to byzantines: Better rates, weaker assumptions and communication compression as a cherry on the top. arXiv preprint arXiv:2206.00529, 2022. 5, 6

[14] Tianyu Gu, Brendan Dolan- Gavitt, and Siddharth Garg. Badnets: Identifying vulnerabilities in the machine learning model supply chain. arXiv preprint arXiv:1708.06733, 2017. 1, 2, 6, 12[15] Rachid Guerraoui, Sébastien Rouault, et al. The hidden vulnerability of distributed learning in byzantium. In International Conference on Machine Learning, pages 3521- 3530. PMLR, 2018. 1[16] Kaiming He, Xiangyu Zhang, Shaoqing Ren, and Jian Sun. Deep residual learning for image recognition. In Proceedings of the IEEE conference on computer vision and pattern recognition, pages 770- 778, 2016. 7[17] Tzu- Ming Harry Hsu, Hang Qi, and Matthew Brown. Measuring the effects of non- identical data distribution for federated visual classification. arXiv preprint arXiv:1909.06335, 2019. 6[18] Rui Hu, Yuanxiong Guo, and Yanmin Gong. Federated learning with sparsified model perturbation: Improving accuracy under client- level differential privacy. IEEE Transactions on Mobile Computing, 2023. 5[19] Siquan Huang, Yijiang Li, Chong Chen, Leyu Shi, and Ying Gao. Multi- metrics adaptively identifies backdoors in federated learning. In Proceedings of the IEEE/CVF International Conference on Computer Vision, pages 4652- 4662, 2023. 1, 2, 6[20] Tiansheng Huang, Sihao Hu, Ka- Ho Chow, Fatih Ilhan, Selim Tekin, and Ling Liu. Lockdown: Backdoor defense for federated learning with isolated subspace training. Advances in Neural Information Processing Systems, 36, 2024. 1, 2, 6[21] Sai Praneeth Karimireddy, Lie He, and Martin Jaggi. Byzantine- robust learning on heterogeneous datasets via bucketing. In International Conference on Learning Representations, 2021. 5[22] Torsten Krauß and Alexandra Dmitrienko. Mesas: Poisoning defense for federated learning resilient against adaptive attackers. In Proceedings of the 2023 ACM SIGSAC Conference on Computer and Communications Security, pages 1526- 1540, 2023. 1, 2, 6[23] Torsten Krauß, Jan König, Alexandra Dmitrienko, and Christian Kanzow. Automatic adversarial adaption for stealthy poisoning attacks in federated learning. In To appear soon at the Network and Distributed System Security Symposium (NDSS), 2024. 2[24] Alex Krizhevsky et al. Learning multiple layers of features from tiny images. University of Toronto, 2009. 6[25] Yann LeCun, Léon Bottou, Yoshua Bengio, and Patrick Haffner. Gradient- based learning applied to document recognition. Proceedings of the IEEE, 86(11):2278- 2324, 1998. 6[26] Han Liu, Zhiyuan Yu, Mingming Zha, XiaoFeng Wang, William Yeoh, Yevgeniy Vorobeychik, and Ning Zhang. When evil calls: Targeted adversarial voice over ip network. In Proceedings of the 2022 ACM SIGSAC Conference on Computer and Communications Security, pages 2009- 2023, 2022. 1

[27] Yi Liu, Jiangtian Nie, Xuandi Li, Syed Hassan Ahmed, Wei Yang Bryan Lim, and Chunyan Miao. Federated learning in the sky: Aerial- ground air quality sensing framework with uav swarms. IEEE Internet of Things Journal, 8(12):9827- 9837, 2020. 1[28] Guodong Long, Yue Tan, Jing Jiang, and Chengqi Zhang. Federated learning for open banking. In Federated Learning: Privacy and Incentive, pages 240- 254. Springer, 2020. 1[29] Xiaoting Lyu, Yufei Han, Wei Wang, Jingkai Liu, Bin Wang, Jiqiang Liu, and Xiangliang Zhang. Poisoning with cerebus: Stealthy and colluded backdoor attack against federated learning. In Proceedings of the AAAI Conference on Artificial Intelligence, pages 9020- 9028, 2023. 2[30] Grigory Malinovsky, Kai Yi, and Peter Richtarik. Variance reduced proxskip: Algorithm, theory and application to federated learning. Advances in Neural Information Processing Systems, 35:15176- 15189, 2022. 5, 6[31] Grigory Malinovsky, Peter Richtarik, Samuel Horvath, and Eduard Gorbunov. Byzantine robustness and partial participation can be achieved simultaneously: Just clip gradient differences. arXiv preprint arXiv:2311.14127, 2023. 5[32] Brendan McMahan, Eider Moore, Daniel Ramage, Seth Hampson, and Blaise Aguera y Arcas. Communication- efficient learning of deep networks from decentralized data. In Artificial intelligence and statistics, pages 1273- 1282. PMLR, 2017. 1[33] Brendan McMahan, Eider Moore, Daniel Ramage, Seth Hampson, and Blaise Aguera y Arcas. Communication- efficient learning of deep networks from decentralized data. In Artificial intelligence and statistics, pages 1273- 1282. PMLR, 2017. 2[34] Yurii Nesterov et al. Lectures on convex optimization. Springer, 2018. 5[35] Dinh C Nguyen, Quoc- Viet Pham, Pubudu N Pathirana, Ming Ding, Aruna Seneviratne, Zihuai Lin, Octavia Dobre, and Won- Joo Hwang. Federated learning for smart healthcare: A survey. ACM Computing Surveys (CSUR), 55(3): 1- 37, 2022. 1[36] Thien Duc Nguyen, Phillip Rieger, Roberta De Viti, Huili Chen, Bjorn B Brandenburg, Hossein Yalame, Helen Mollering, Hossein Fereidooni, Samuel Marchal, Markus Miettinen, et al. FLAME: Taming backdoors in federated learning. In 31st USENIX Security Symposium (USENIX Security 22), pages 1415- 1432, 2022. 1, 2[37] Thuy Dung Nguyen, Tuan A Nguyen, Anh Tran, Khoa D Doan, and Kok- Seng Wong. Iba: Towards irreversible backdoor attacks in federated learning. Advances in Neural Information Processing Systems, 36, 2024. 2[38] Mustafa Safa Ozdayi, Murat Kantarcioglu, and Yulia R Gel. Defending against backdoors in federated learning with robust learning rate. In Proceedings of the AAAI Conference on Artificial Intelligence, pages 9268- 9276, 2021. 2, 3, 6[39] Ashwinee Panda, Saeed Mahloujifar, Arjun Nitin Bhagoji, Supriyo Chakraborty, and Prateek Mittal. Sparsefed: Mitigating model poisoning attacks in federated learning with sparsification. In International Conference on Artificial Intelligence and Statistics, pages 7587- 7624. PMLR, 2022. 5, 6

[40] Krishna Pillutla, Sham M Kakade, and Zaid Harchaoui. Robust aggregation for federated learning. IEEE Transactions on Signal Processing, 70:1142- 1154, 2022. 2, 5, 6[41] Phillip Rieger, Torsten Kraub, Markus Miettinen, Alexandra Dmitrienko, and Ahmad- Reza Sadeghi. Crowdguard: Federated backdoor detection in federated learning. arXiv preprint arXiv:2210.07714, 2022. 2[42] Phillip Rieger, Thien Duc Nguyen, Markus Miettinen, and Ahmad- Reza Sadeghi. Deepsight: Mitigating backdoor attacks in federated learning through deep model inspection. arXiv preprint arXiv:2201.00763, 2022. 1, 2, 12[43] Virat Shejwalkar and Amir Hourmansadr. Manipulating the byzantine: Optimizing model poisoning attacks and defenses for federated learning. In NDSS, 2021. 12[44] Karen Simonyan and Andrew Zisserman. Very deep convolutional networks for large- scale image recognition. arXiv preprint arXiv:1409.1556, 2014. 7[45] Zhiyi Tian, Lei Cui, Jie Liang, and Shui Yu. A comprehensive survey on poisoning attacks and countermeasures in machine learning. ACM Computing Surveys, 55(8):1- 35, 2022. 1[46] Hongyi Wang, Kartik Sreenivasan, Shashank Rajput, Harit Vishwakarma, Saurabh Agarwal, Jy- yong Sohn, Kangwook Lee, and Dimitris Papailiopoulos. Attack of the tails: Yes, you really can backdoor federated learning. Advances in Neural Information Processing Systems, 33:16070- 16084, 2020. 1, 2, 3, 6, 12[47] Han Xiao, Kashif Rasul, and Roland Vollgraf. Fashion- mistry: a novel image dataset for benchmarking machine learning algorithms. arXiv preprint arXiv:1708.07747, 2017. 6[48] Chulin Xie, Keli Huang, Pin- Yu Chen, and Bo Li. Dba: Distributed backdoor attacks against federated learning. In International conference on learning representations, 2019. 1, 2, 6, 12[49] Jian Xu, Shao- Lun Huang, Linqi Song, and Tian Lan. Signguard: Byzantine- robust federated learning through collaborative malicious gradient filtering. arXiv preprint arXiv:2109.05872, 2021. 3, 5, 6, 14[50] Jiahao Xu, Zikai Zhang, and Rui Hu. Achieving byzantine- resilient federated learning via layer- adaptive sparsified model aggregation. arXiv preprint arXiv:2409.01435, 2024. 2, 4, 5[51] Jiahao Xu, Zikai Zhang, and Rui Hu. Identify backdoored model in federated learning via individual unlearning. arXiv preprint arXiv:2411.01040, 2024. 2, 4[52] Hangfan Zhang, Jinyuan Jia, Jinghui Chen, Lu Lin, and Dinghao Wu. A3fl: Adversarially adaptive backdoor attacks to federated learning. Advances in Neural Information Processing Systems, 36, 2024. 2[53] Jingzhao Zhang, Tianxing He, Su- rvit Sra, and Ali Jadbabaie. Why gradient clipping accelerates training: A theoretical justification for adaptivity. In International Conference on Learning Representations, 2019. 5[54] Zhengming Zhang, Ashwinee Panda, Linyue Song, Yaoqing Yang, Michael Mahoney, Prateek Mittal, Ramchandran Kannan, and Joseph Gonzalez. Neurotoxin: Durable backdoors

in federated learning. In *International Conference on Machine Learning*, pages 26429–26446. PMLR, 2022. 1, 2, 6, 12

# Detecting Backdoor Attacks in Federated Learning via Direction Alignment Inspection

Supplementary Material

![](images/b2d01f02aad7875c9cad0481b03cac8cf1c3f15294b0a0f0fc5c8e485b70809c.jpg)  
Figure 2. Illustration of backdoor triggers used in evaluation.

# 8. Attack Model and Detailed Attack Settings

# 8.1.Attack Model

8.1. Attack ModelWe follow the threat model in previous works [6, 42, 43]. Specifically, the attacker controls  $m$  malicious clients, which can be fake injected into the system by the attacker or benign clients compromised by the attacker. These malicious clients are allowed to co- exist in the FL system.  $i$  Attacker's goal. The backdoor attackers in FL have two primary objectives. First, they aim to maintain the accuracy of the global model on benign inputs, ensuring that its overall performance remains unaffected. Second, they seek to manipulate the global model so that it behaves as predefined by the attacker on inputs containing a specific trigger, such as misclassifying triggered inputs to a specific backdoor label. ii) Attacker's capability. The attacker controls  $m$  malicious clients in FL. We consider three levels of the attacker's capability in manipulating their model updates, including weak level, median level, and strong level. The malicious clients controlled by weak attackers (e.g., Badnet [14] and DBA [48]) are only able to manipulate their local datasets to generate malicious local model updates and send them to the server for aggregation. For a median attacker, malicious clients can additionally modify the training algorithm (e.g., Scaling [4] and PGD [46]) to generate malicious local model updates. These two assumptions are common in existing works for attackers who control malicious devices but do not have access to additional information from servers or benign clients. For a strong attacker (e.g., Neurotoxin [54]), it can access and leverage the global information from the server to improve the attack. Note that the defense method employed by the server is confidential to the attacker.

# 8.2. More Detailed Settings of Attack Methods.

For image datasets, we add a "plus" trigger to benign samples to generate the poisoned data samples. For Sentiment140 dataset, we insert a trigger sentence "This is a backdoor trigger" into benign samples to generate poisoned data samples. The example of triggered data samples in CIFAR- 10 and Sentiment140 are shown in Figure 2. For

DBA attack, we decompose the "plus" trigger into four local patterns, and each malicious client only uses one of these local patterns. For Scaling attack, we use a scale factor of 2.0 to scale up all malicious model updates. For PGD attack, malicious local models are projected onto a sphere with a radius equal to the  $L_{2}$  - norm of the global model in the current round for all datasets, except CIFAR- 10 where we make the radius of the sphere be 10 times smaller than the norm. For Neurotoxin attack, malicious model updates are projected to the dimensions that have Bottom-  $75\%$  importance in the aggregated update from the previous round.

# 9. Defense Model

9. Defense ModelIn this work, we assume the server to be the defender.  $i$  Defender's goal. As stated in [7], an ideal defense method against poisoning attacks in FL should consider the following three aspects: Fidelity, Robustness, and Efficiency. To ensure fidelity, the defense method does not significantly degrade the global model's performance on benign inputs, thus preserving its effectiveness. For robustness, the defense method should successfully mitigate the impact of malicious model updates, limiting the global model's malicious behavior on triggered inputs. Regarding efficiency, the defense method should be computationally efficient, ensuring that it does not hinder the overall efficiency of the training process. In this work, we assume that the server aims to achieve the highest level of robustness by removing all malicious updates without significant computational complexity and accuracy degradation on benign inputs. ii) Defender's capability. In FL, the server has no access to the local datasets of clients, but it has the global model and all the local model updates. We assume the server has no prior knowledge of the number of malicious clients. We also assume that each client transmits their local update anonymously, making the actions of individual clients untraceable. Additionally, the server does not know the specifics of backdoor attacks, such as the type of trigger involved. To defend against backdoor attacks, the server will apply a robust aggregation rule  $F$  to the local model updates received from clients and generate an aggregated model update at each training round.

# 10. More Superior Results of AlignIns

# 10.1. Comprehensive Results on non-IID Datasets

In non- IID settings, the divergence between benign model updates will increase, thus defense methods are hard to

Table 4. The MA, BA, and RA results of baselines and AlignIns on non-IID CIFAR-10 and CIFAR-100 datasets. Results are shown in  $\%$  

<table><tr><td rowspan="3">Dataset (Model)</td><td rowspan="3">Methods</td><td rowspan="3">Clean MA↑</td><td colspan="4">Badnet</td><td colspan="4">DBA</td><td colspan="4">Neurotoxin</td><td>Avg. BA↓</td><td>Avg. RA↑</td></tr><tr><td colspan="2">BA↓</td><td colspan="2">RA↑</td><td colspan="2">BA↓</td><td colspan="2">RA↑</td><td colspan="2">BA↓</td><td colspan="2">RA↑</td><td rowspan="2">Avg. BA↓</td><td rowspan="2">Avg. RA↑</td></tr><tr><td>r=0.3</td><td>r=0.5</td><td>r=0.3</td><td>r=0.5</td><td>r=0.3</td><td>r=0.5</td><td>r=0.3</td><td>r=0.5</td><td>r=0.3</td><td>r=0.5</td><td>r=0.3</td><td>r=0.5</td></tr><tr><td rowspan="9">CIFAR-10 (FAGNet)</td><td>FedAvg</td><td>85.05</td><td>42.34</td><td>86.33</td><td>51.60</td><td>13.22</td><td>42.24</td><td>71.64</td><td>49.63</td><td>25.26</td><td>42.29</td><td>76.63</td><td>48.76</td><td>20.73</td><td>53.57</td><td>36.29</td></tr><tr><td>FedAvg*</td><td>85.05</td><td>1.78</td><td>1.78</td><td>83.09</td><td>83.09</td><td>1.78</td><td>1.78</td><td>83.09</td><td>83.09</td><td>1.78</td><td>1.78</td><td>83.09</td><td>1.3.09</td><td>1.78</td><td>83.09</td></tr><tr><td>RLR</td><td>59.87</td><td>3.27</td><td>0.94</td><td>55.54</td><td>55.53</td><td>1.98</td><td>1.87</td><td>59.98</td><td>59.52</td><td>0.21</td><td>0.27</td><td>45.60</td><td>46.02</td><td>1.92</td><td>53.04</td></tr><tr><td>RFA</td><td>79.80</td><td>56.26</td><td>97.42</td><td>36.49</td><td>2.30</td><td>53.70</td><td>90.70</td><td>39.00</td><td>8.10</td><td>4.29</td><td>22.26</td><td>71.93</td><td>56.60</td><td>50.27</td><td>39.36</td></tr><tr><td>MKrum</td><td>70.89</td><td>72.70</td><td>95.57</td><td>20.98</td><td>3.71</td><td>2.12</td><td>53.81</td><td>69.80</td><td>35.09</td><td>1.18</td><td>1.22</td><td>74.02</td><td>71.08</td><td>49.58</td><td>37.78</td></tr><tr><td>Foolsgold</td><td>83.97</td><td>20.24</td><td>85.27</td><td>68.91</td><td>16.14</td><td>42.20</td><td>65.56</td><td>50.79</td><td>31.62</td><td>3.77</td><td>1.49</td><td>78.08</td><td>80.22</td><td>42.88</td><td>62.45</td></tr><tr><td>MM</td><td>82.02</td><td>50.52</td><td>95.70</td><td>41.41</td><td>4.08</td><td>66.88</td><td>43.69</td><td>28.18</td><td>47.38</td><td>85.58</td><td>98.86</td><td>13.02</td><td>1.04</td><td>63.12</td><td>30.83</td></tr><tr><td>Lockdown</td><td>84.05</td><td>6.68</td><td>8.01</td><td>75.23</td><td>75.73</td><td>7.11</td><td>6.03</td><td>76.63</td><td>75.77</td><td>1.24</td><td>2.19</td><td>73.82</td><td>73.81</td><td>5.21</td><td>75.07</td></tr><tr><td>AlignIns</td><td>85.77</td><td>2.48</td><td>1.7</td><td>81.17</td><td>81.32</td><td>1.54</td><td>1.10</td><td>81.24</td><td>81.11</td><td>2.73</td><td>2.08</td><td>81.54</td><td>80.42</td><td>1.77</td><td>80.48</td></tr><tr><td rowspan="9">CIFAR-100 (YGG9)</td><td>FedAvg</td><td>63.33</td><td>99.57</td><td>99.63</td><td>0.35</td><td>0.33</td><td>99.52</td><td>99.74</td><td>0.45</td><td>0.23</td><td>97.58</td><td>97.18</td><td>1.94</td><td>2.25</td><td>98.66</td><td>0.92</td></tr><tr><td>FedAvg*</td><td>63.33</td><td>0.59</td><td>0.59</td><td>50.21</td><td>50.21</td><td>0.59</td><td>0.59</td><td>50.21</td><td>50.21</td><td>0.59</td><td>0.59</td><td>50.21</td><td>50.21</td><td>0.59</td><td>50.21</td></tr><tr><td>RLR</td><td>35.83</td><td>58.31</td><td>98.94</td><td>9.22</td><td>0.47</td><td>2.31</td><td>76.82</td><td>22.61</td><td>7.79</td><td>0.00</td><td>15.54</td><td>11.31</td><td>15.54</td><td>42.26</td><td>11.66</td></tr><tr><td>RFA</td><td>34.16</td><td>3.19</td><td>0.89</td><td>25.07</td><td>26.58</td><td>0.91</td><td>4.25</td><td>24.68</td><td>25.66</td><td>99.47</td><td>8.52</td><td>0.36</td><td>22.82</td><td>22.51</td><td>20.93</td></tr><tr><td>MKrum</td><td>45.10</td><td>99.44</td><td>1.84</td><td>0.43</td><td>34.89</td><td>99.30</td><td>1.22</td><td>0.55</td><td>34.05</td><td>99.71</td><td>99.20</td><td>0.23</td><td>0.49</td><td>54.69</td><td>14.69</td></tr><tr><td>Foolsgold</td><td>62.77</td><td>99.58</td><td>99.56</td><td>0.38</td><td>0.38</td><td>99.52</td><td>99.67</td><td>0.43</td><td>0.29</td><td>11.64</td><td>11.06</td><td>43.01</td><td>42.20</td><td>70.23</td><td>10.27</td></tr><tr><td>MM</td><td>60.22</td><td>99.65</td><td>99.93</td><td>0.28</td><td>0.04</td><td>99.90</td><td>99.94</td><td>0.10</td><td>0.06</td><td>99.73</td><td>99.82</td><td>0.23</td><td>0.14</td><td>99.53</td><td>0.18</td></tr><tr><td>Lockdown</td><td>60.91</td><td>29.19</td><td>40.08</td><td>32.91</td><td>30.60</td><td>11.90</td><td>20.08</td><td>34.97</td><td>32.79</td><td>0.13</td><td>0.07</td><td>44.42</td><td>42.72</td><td>21.73</td><td>36.47</td></tr><tr><td>AlignIns</td><td>59.18</td><td>0.66</td><td>0.54</td><td>47.51</td><td>44.67</td><td>0.19</td><td>0.42</td><td>47.33</td><td>48.77</td><td>1.20</td><td>1.09</td><td>49.17</td><td>45.70</td><td>0.64</td><td>47.86</td></tr></table>

identify malicious model updates. From Table 4, We can conclude MM still fails to detect malicious model updates on two non- IID cases. Foolsgold can only exhibit a limited degree of robustness under Neurotoxin attack. Specifically, in the non- IID CIFAR- 10 under DBA attack, Foolsgold was unable to effectively detect malicious model updates. This resulted in a BA of  $42.20\%$  and  $63.56\%$  and an RA of  $50.79\%$  and  $31.62\%$ . The reason for this lies in the feature of the Neurotoxin attack, where the malicious model updates are projected to the Bottom-  $k$  parameters of the aggregated model update in the latest round. This process makes the malicious model updates generated by Neurotoxin attacks have the same Top parameters, reducing local variance between them. Foolsgold enjoys a more accurate identification of malicious model updates as it works based on the assumption that malicious model updates are consistent with each other. In contrast, AlignIns exhibits outstanding robustness in the same case as AlignIns achieves significantly superior performance, yielding the lowest BA at  $1.54\%$  and  $1.10\%$  and the highest RA at  $81.24\%$  and  $81.11\%$ . This marks an improvement of  $+40.66\%$  and  $+62.46\%$  in BA and  $+30.45\%$  and  $+49.49\%$  in RA over Foolsgold. For CIFAR- 100 dataset, AlignIns still have a lower BA and higher RA than their counterparts, underlining the enhanced detection and robustness capabilities of AlignIns in challenging non- IID conditions.

# 10.2. Results on Larger Datasets

We also evaluate AlignIns on the Tiny- ImageNet dataset, which is typically the largest dataset considered in related works. The BA and RA results are summarized in Table 5. AlignIns demonstrates strong robustness against both BadNet and Neurotoxin attacks, achieving the lowest BA  $(0.31\%)$  and the highest RA  $(35.43\%)$ . These results highlight the practical effectiveness of AlignIns on large, real- world datasets.

Table 5. Performance of AlignIns on Tiny-ImageNet dataset.  

<table><tr><td rowspan="2">Method</td><td colspan="2">Badnet</td><td colspan="2">Neurotoxin</td><td rowspan="2">Avg. BA↓</td><td rowspan="2">Avg. RA↑</td></tr><tr><td>BA↓</td><td>RA↑</td><td>BA↓</td><td>RA↑</td></tr><tr><td>RLR</td><td>55.54</td><td>18.25</td><td>0.54</td><td>22.01</td><td>28.04</td><td>20.13</td></tr><tr><td>RFA</td><td>0.38</td><td>32.40</td><td>97.41</td><td>1.97</td><td>48.90</td><td>17.19</td></tr><tr><td>MKrum</td><td>0.36</td><td>32.60</td><td>29.37</td><td>25.55</td><td>14.87</td><td>29.08</td></tr><tr><td>Foolsgold</td><td>93.59</td><td>4.68</td><td>0.26</td><td>37.05</td><td>46.93</td><td>20.87</td></tr><tr><td>MM</td><td>97.01</td><td>2.11</td><td>90.85</td><td>5.27</td><td>93.93</td><td>3.69</td></tr><tr><td>Lockdown</td><td>72.08</td><td>17.09</td><td>0.34</td><td>28.18</td><td>36.21</td><td>22.64</td></tr><tr><td>AlignIns</td><td>0.22</td><td>34.55</td><td>0.40</td><td>36.30</td><td>0.31</td><td>35.43</td></tr></table>

# 10.3. Trigger-Optimization Attack

We evaluate the experimental performance of AlignIns under the strong trigger- optimization attack. Specifically, we consider the SOTA trigger- optimization attack F3BA [9] and conduct experiments on CIFAR- 10 dataset under both IID and varying degrees of non- IID settings. As the results shown in Table 6, FedAvg is vulnerable to F3BA as it has a high BA and low RA. Similarly, RLR also cannot provide enough robustness to F3BA especially when the data heterogeneity is high. In contrast, AlignIns consistently achieves the highest robustness across all scenarios. Specifically, compared to Bulyan, AlignIns yields an

average increase of  $+22.63\%$  in BA and  $+19.11\%$  in RA. While trigger- optimization attacks typically search for an optimal trigger to enhance their stealthiness and effectiveness, AlignIns can still identify malicious and benign model updates by inspecting their alignments.

Table 6. Performance of AlignIns under trigger-optimization attack on CIFAR-10 dataset in both IID and non-IID settings.  

<table><tr><td rowspan="3">Method</td><td colspan="7">Data Distribuion</td><td></td></tr><tr><td colspan="2">β=0.3</td><td colspan="2">β=0.5</td><td colspan="2">β=0.7</td><td>IID</td><td></td></tr><tr><td>BA↓</td><td>RA↑</td><td>BA↓</td><td>RA↑</td><td>BA↓</td><td>RA↑</td><td>BA↓</td><td>RA↑</td></tr><tr><td>FedAvg</td><td>93.97</td><td>5.13</td><td>93.44</td><td>6.06</td><td>94.36</td><td>4.83</td><td>94.16</td><td>5.50</td></tr><tr><td>RLR</td><td>92.58</td><td>6.71</td><td>93.20</td><td>6.42</td><td>81.78</td><td>15.80</td><td>86.23</td><td>13.23</td></tr><tr><td>Bulyan</td><td>60.97</td><td>27.49</td><td>5.57</td><td>58.12</td><td>17.82</td><td>57.71</td><td>15.61</td><td>64.40</td></tr><tr><td>AlignIns</td><td>5.22</td><td>65.12</td><td>2.33</td><td>72.82</td><td>1.99</td><td>70.50</td><td>2.91</td><td>75.71</td></tr></table>

# 10.4. Effectiveness under Adaptive Attack

Recall that in our attack model, the attacker is assumed to be unaware of the defense method the server deployed. Here, we assume the attacker has such knowledge and evaluate AlignIns under attacks tailored to circumvent it. Specifically, we design two adaptive attacks: ADA_A, where each malicious client randomly selects a benign model update and mirrors its sign, and ADA_B, where each malicious client aligns with the principal sign of all model updates. Results are summarized in Table 7. In the results, AlignIns shows strong resistance to both ADA_A and ADA_B attacks. For ADA_A, although it leverages benign signs, MPSA focuses on the signs of important weights, which typically differ from those of benign models, allowing AlignIns to counter ADA_A effectively. For ADA_B, using the principal sign yields an MPSA value of 1.0, which our  $\mathrm{MZ}_{\mathrm{S}}$  core can readily detect. These results confirm that AlignIns effectively limits backdoor success and preserves the main task and robust accuracy, even against adaptive attack strategies tailored to exploit its defenses.

Table 7. Performance of AlignIns on Adaptive Attacks.  

<table><tr><td rowspan="2">Dataset</td><td colspan="3">ADA_A</td><td colspan="3">ADA_B</td></tr><tr><td>MA↑</td><td>BA↓</td><td>RA↑</td><td>MA↑</td><td>BA↓</td><td>RA↑</td></tr><tr><td>CIFAR-10</td><td>88.22</td><td>2.34</td><td>85.44</td><td>88.33</td><td>1.82</td><td>86.49</td></tr><tr><td>CIFAR-100</td><td>62.10</td><td>0.48</td><td>51.87</td><td>62.86</td><td>0.37</td><td>53.55</td></tr></table>

# 10.5. Effectiveness under Untargeted Attack

In this section, we conduct experiments to illustrate how AlignIns performs with respect to untargeted attacks (also known as Byzantine attacks). Byzantine attacks aim to degrade the model's overall performance during the training as much as possible. We consider the SOTA Byzantine attack method ByzMean [49] which uses the Lie attack [5] as the backbone of the attack baseline. We also involve the SOTA

Table 8. The MA of AlignIns under untargeted attack on CIFAR10 dataset in both IID and non-IID settings.  

<table><tr><td rowspan="2">Method</td><td colspan="4">Attack Ratio=10%</td><td colspan="4">Attack Ratio=20%</td></tr><tr><td>β=0.3</td><td>β=0.5</td><td>β=0.7</td><td>IID</td><td>β=0.3</td><td>β=0.5</td><td>β=0.7</td><td>IID</td></tr><tr><td>FedAvg</td><td>10.95</td><td>13.21</td><td>11.66</td><td>20.71</td><td>10.85</td><td>12.96</td><td>10.33</td><td>18.62</td></tr><tr><td>RFA</td><td>77.43</td><td>78.26</td><td>80.45</td><td>87.03</td><td>77.02</td><td>76.93</td><td>79.76</td><td>86.03</td></tr><tr><td>MKrum</td><td>67.99</td><td>71.14</td><td>76.76</td><td>86.87</td><td>65.61</td><td>74.39</td><td>77.16</td><td>86.39</td></tr><tr><td>SignGuard</td><td>85.11</td><td>85.58</td><td>86.84</td><td>89.23</td><td>85.40</td><td>84.69</td><td>86.22</td><td>88.45</td></tr><tr><td>AlignIns</td><td>85.32</td><td>85.61</td><td>87.13</td><td>89.23</td><td>85.49</td><td>84.98</td><td>86.18</td><td>88.54</td></tr></table>

Byzantine- robust method SignGuard [49] in our experiments. Table 8 reports the MA of FedAvg, RFA, MKrum, SignGuard, and our method AlignIns, in defending against ByzMean attack on CIFAR- 10 dataset with attack ratios of  $10\%$  and  $20\%$  under different data settings. The results indicate that non- robust baseline FedAvg collapsed when facing to ByzMean attack in all cases, yielding an accuracy below  $20\%$ . RFA and MKrum provide a certain but limited Byzantine- robustness. In contrast, AlignIns consistently achieves comparable accuracy with SOTA SignGuard across all scenarios. These results demonstrate AlignIns' generalization ability for both backdoor and Byzantine attacks, making it a potential and potent method for practical application in real- world scenarios where there is no prior knowledge about the attack type.

# 10.6. Effectiveness on More Datasets

To validate that the achieved robustness by AlignIns can be generalized to other datasets, we show our evaluation results on MNIST, FMNIST, and Sentiment140 under Badnet attack in Table 9. We also involve the perfectly robust FedAvg* for comparison. Notably, AlignIns consistently aligns with FedAvg* in MA, BA, and RA, indicating AlignIns can accurately identify malicious model updates and preserve benign model updates at the same time to attain such a high robustness and model performance. Additionally, AlignIns shows SOTA defense efficacy compared to other counterparts. For example, AlignIns maintains the highest BA at  $0.36\%$ ,  $0.01\%$ , and  $41.43\%$ , with an improvement of  $+21.42\%$ ,  $+0.03\%$ , and  $+57.62\%$  over RLR on the respective three datasets. Besides, AlignIns also achieves the highest RA across all datasets, averaging a  $+22.21\%$  increase compared to RFA. These findings verify the robustness and stability of AlignIns across various datasets.

Table 9. Performance of AlignIns on More Datasets.  

<table><tr><td rowspan="2">Method</td><td colspan="3">MNIST</td><td colspan="3">FMNIST</td><td colspan="3">Sentiment140</td></tr><tr><td>MA↑</td><td>BA↓</td><td>RA↑</td><td>MA↑</td><td>BA↓</td><td>RA↑</td><td>MA↑</td><td>BA↓</td><td>RA↑</td></tr><tr><td>FedAvg*</td><td>97.66</td><td>99.87</td><td>0.13</td><td>88.34</td><td>98.40</td><td>1.46</td><td>66.16</td><td>85.55</td><td>14.45</td></tr><tr><td>FedAvg*</td><td>97.63</td><td>0.37</td><td>97.60</td><td>88.44</td><td>0.60</td><td>76.72</td><td>67.31</td><td>41.57</td><td>58.43</td></tr><tr><td>RLR</td><td>96.48</td><td>21.78</td><td>75.39</td><td>86.51</td><td>0.04</td><td>75.44</td><td>51.23</td><td>99.05</td><td>0.95</td></tr><tr><td>RFA</td><td>97.72</td><td>0.61</td><td>97.53</td><td>88.53</td><td>13.08</td><td>69.09</td><td>60.71</td><td>99.90</td><td>0.10</td></tr><tr><td>AlignIns</td><td>97.76</td><td>0.36</td><td>97.73</td><td>88.50</td><td>0.01</td><td>77.04</td><td>69.26</td><td>41.43</td><td>58.57</td></tr></table>

# 10.7. Effectiveness under Various Attack Ratios.

10.7. Effectiveness under Various Attack Ratios.We further evaluate the performance of AlignIns under various attack ratios in non- IID settings. We conduct the experiments under PGD and Scaling attacks with the attack ratio varying from  $5\%$  to  $30\%$  on non- IID CIFAR- 10 and CIFAR- 100 datasets. As shown in Figure 3, the RA of RLR and MKrum generally decreases as the attack ratio increases. For instance, when the attack ratio exceeds  $20\%$ , MKrum loses effectiveness, with RA dropping to as low as  $0.02\%$ . This decline is primarily due to the PGD attack, which projects malicious model updates within a sphere centered around the global model, limiting magnitude- based methods like MKrum. Lockdown achieves comparable robustness with AlignIns at low attack ratios on the CIFAR- 10 dataset. Yet, it fails to effectively protect against both types of attacks when the attack ratios are high  $(30\%)$ , resulting in considerable declines in robustness. Compared to its counterparts, AlignIns achieves a higher and more stable performance. As the attack ratio increases, AlignIns only has a minor decrease in RA.

![](images/fb11ec52ae1323d1b1c532bae83a7db2c4939276b4e0d238cc867e1a8e714874.jpg)  
Figure 3. RA of AlignIns under various attack ratios on CIFAR-10 (upper row) and CIFAR-100 (lower row) datasets, compared with Lockdown, MKrum, and RLR.

# 11. Impact of Filtering Radii

Here, we dive into the impact of different configurations of filtering radii,  $\lambda_{s}$  and  $\lambda_c$  on the efficacy of AlignIns. A smaller  $\lambda_{s}$  or  $\lambda_c$  indicates more stringent filtering and results in a smaller benign set for aggregation. We conduct the experiments on non- IID CIFAR- 10 and CIFAR100 datasets under Badnet and PGD attacks. The results, as detailed in Table 10, show the ideal configurations of  $\lambda_{s}$  and  $\lambda_c$  that effectively balance the filtering intensity while maximizing the robustness of the model. Specifically, for CIFAR- 10 dataset, the optimal RA is attained when  $\lambda_{s}$  and  $\lambda_c$  are both set to 1.0 under both Badnet and PGD attacks, suggesting an ideal level of filtering intensity. A reduction in either  $\lambda_{s}$  or  $\lambda_c$  leads to a slight drop in RA, implying that some benign updates may be erroneously discarded due to an overly stringent filtering radius. In contrast, when  $\lambda_{s}$  and  $\lambda_c$  are increased to 2.0, there's a significant decline in AlignIns' RA, due to the excessively permissive filtering threshold. As for CIFAR- 100 dataset, AlignIns' performance remains stable against variations in both radii. Specifically, under the Badnet attack, AlignIns performs best when both radii are at 2.0, while for the PGD attack, the radii at 1.0 are most effective. This is mainly because PGD attack limits the large malicious model update changes, conducting a more stealthy attack than Badnet. By doing so, it makes the malicious model updates more similar to benign ones, leading to a smaller filter radius.

Table 10. Performance of AlignIns with Different Filtering Radii.  

<table><tr><td rowspan="2">Config.</td><td colspan="4">CIFAR-10</td><td colspan="4">CIFAR-100</td><td></td></tr><tr><td colspan="2">Badnet</td><td colspan="2">PGD</td><td colspan="2">Badnet</td><td colspan="2">PGD</td><td></td></tr><tr><td>λs</td><td>λc</td><td>BA↓</td><td>RA↑</td><td>BA↓</td><td>RA↑</td><td>BA↓</td><td>RA↑</td><td>BA↓</td><td>RA↑</td></tr><tr><td>0.5</td><td>0.5</td><td>0.58</td><td>76.37</td><td>3.29</td><td>79.39</td><td>0.59</td><td>43.22</td><td>0.59</td><td>46.17</td></tr><tr><td>1.0</td><td>0.5</td><td>4.71</td><td>78.27</td><td>63.60</td><td>32.27</td><td>0.49</td><td>44.41</td><td>0.62</td><td>46.83</td></tr><tr><td>0.5</td><td>1.0</td><td>3.11</td><td>78.99</td><td>1.73</td><td>79.37</td><td>0.58</td><td>43.18</td><td>0.19</td><td>44.67</td></tr><tr><td>1.0</td><td>1.0</td><td>1.70</td><td>81.32</td><td>2.31</td><td>81.18</td><td>0.54</td><td>44.67</td><td>0.52</td><td>48.37</td></tr><tr><td>2.0</td><td>2.0</td><td>57.47</td><td>37.53</td><td>81.33</td><td>17.69</td><td>0.76</td><td>47.07</td><td>0.68</td><td>46.99</td></tr></table>

# 12. Computational Cost of AlignIns

12. Computational Cost of AlignInsWe compare the computational cost of AlignIns with other counterparts. AlignIns calculates the MPSA metric using the Top-  $k$  indicator, incurring a complexity of  $O(d \log d)$  due to the use of sorting algorithms like merge sort in the parameter space of the local update. As a result, the total computational expense of AlignIns in the worst-case scenario is  $O(nd \log d)$ . Nonetheless, we argue that the computational burden of AlignIns is comparable with several robust aggregation methods such as Krum and MKrum, both of which have a complexity of  $O(dn^2)$ , the Coordinate-wise median with  $O(dn)$ , and Trmean at  $O(dn \log n)$ . Each method shows a linear dependency on  $d$ , which can be considerably large in modern deep neural networks (i.e.,  $d \gg n$ ), and thus is the predominant factor in computational complexity. Empirically, AlignIns imposes minimal computational overhead on the server side (0.13 seconds per round), compared to 4.02 seconds for another filtering-based method MM. Other methods like Lockdown introduce additional computational overhead on local clients, which is undesirable in many scenarios.

# 13. Proof preliminaries

# 13.1. Useful Inequalities

Lemma 3. Given any two vectors  $a,b\in \mathbb{R}^d$

$$
2\langle a,b\rangle \leq \alpha \| a\| ^2 +\frac{1}{\alpha}\| b\| ^2,\forall \alpha >0.
$$

Lemma 4. Given any two vectors  $a,b\in \mathbb{R}^d$

$$
\| a + b\| ^2\leq (1 + \delta)\| a\| ^2 +(1 + \delta^{-1})\| b\| ^2,\forall \delta >0.
$$

Lemma 5. Given arbitrary set of n vectors  $\{a_{i}\}_{i = 1}^{n},a_{i}\in \mathbb{R}^{d}$

$$
\left\| \sum_{i = 1}^{n}a_{i}\right\|^{2}\leq n\sum_{i = 1}^{n}\left\| a_{i}\right\|^{2}.
$$

Lemma 6. If the learning rate  $\eta \leq 1 / 2\tau$  , under Assumption 2 and Assumption 3, the local divergence of benign model updates are bounded as follows:

$$
\frac{1}{|\mathcal{B}|}\sum_{i\in \mathcal{B}}\mathbb{E}\left\| \Delta_{i} - \bar{\Delta}_{\mathcal{B}}\right\|^{2}\leq 2\bar{\nu} +\bar{\zeta}
$$

Proof. Given that  $\begin{array}{r}\Delta_{i} = \eta \sum_{s = 0}^{\tau - 1}g_{i}^{s} \end{array}$  where  $\eta$  is the learning rate and  $g_{i}^{s}$  is the local stochastic gradient over the mini- batch  $s$  We have

$$
\begin{array}{r l}&{\frac{1}{|\mathcal{B}|}\sum_{i\in\mathcal{B}}\mathbb{E}|\Delta_{i}-\Delta_{B}|^{2}=\frac{1}{|\mathcal{B}|}\sum_{i\in\mathcal{B}}\mathbb{E}|\eta\sum_{s=0}^{\tau-1}g_{i}^{s}-\frac{1}{|\mathcal{B}|}\sum_{i\in\mathcal{B}}\eta\sum_{s=0}^{\tau-1}g_{i}^{s}|^{2}}\\ &{\qquad=\frac{\eta^{2}}{|\mathcal{B}|}\sum_{i\in\mathcal{B}}\mathbb{E}|\sum_{s=0}^{\tau-1}g_{i}^{s}-\frac{1}{|\mathcal{B}|}\sum_{i\in\mathcal{B}}\sum_{s=0}^{\tau-1}g_{i}^{s}|^{2}}\\ &{\qquad\leq\frac{\tau\eta^{2}}{|\mathcal{B}|}\sum_{i\in\mathcal{B}}\sum_{s=0}^{\tau-1}\mathbb{E}|g_{i}^{s}-\frac{1}{|\mathcal{B}|}\sum_{i\in\mathcal{B}}g_{i}^{s}|^{2}}\\ &{\qquad=\frac{\tau\eta^{2}}{|\mathcal{B}|}\sum_{i\in\mathcal{B}}\sum_{s=0}^{\tau-1}\mathbb{E}|g_{i}^{s}-\nabla\mathcal{L}_{i}(\theta_{i}^{s})|^{2}+(\nabla\mathcal{L}_{B}(\theta_{i}^{s})-\frac{1}{|\mathcal{B}|}\sum_{i\in\mathcal{B}}g_{i}^{s})+(\nabla\mathcal{L}_{i}(\theta_{i}^{s})-\nabla\mathcal{L}_{B}(\theta_{i}^{s})|^{2}}\\ &{\qquad\leq\frac{3\tau\eta^{2}}{|\mathcal{B}|}\sum_{i\in\mathcal{B}}\sum_{s=0}^{\tau-1}\mathbb{E}|g_{i}^{s}-\nabla\mathcal{L}_{i}(\theta_{i}^{s})|^{2}+\frac{3\tau\eta^{2}}{|\mathcal{B}|}\sum_{i\in\mathcal{B}}\sum_{s=0}^{\tau-1}\mathbb{E}|\nabla\mathcal{L}_{B}(\theta_{i}^{s})-\frac{1}{|\mathcal{B}|}\sum_{i\in\mathcal{B}}g_{i}^{s}|^{2}}\\ &{\qquad+\frac{3\tau\eta^{2}}{|\mathcal{B}|}\sum_{i\in\mathcal{B}}\sum_{s=0}^{\tau-1}\mathbb{E}|\nabla\mathcal{L}_{i}(\theta_{i}^{s})-\nabla\mathcal{L}_{B}(\theta_{i}^{s})|^{2},}\end{array}
$$

where the first inequality follows Lemma 5, and the last second follows Lemma 4. For  $T_{1}$  , with Assumption 2, we have

$$
T_{1}\leq \bar{\nu}. \tag{5}
$$

For  $T_{2}$  , we have

$$
T_{2} = \mathbb{E}\left\| \nabla \mathcal{L}_{B}(\theta_{i}^{s}) - \frac{1}{|\mathcal{B}|}\sum_{i\in \mathcal{B}}g_{i}^{s}\right\|^{2} = \mathbb{E}\left\| \frac{1}{|\mathcal{B}|}\sum_{i\in \mathcal{B}}(\nabla \mathcal{L}_{i}(\theta_{i}^{s}) - g_{i}^{s})\right\|^{2}\leq \frac{1}{|\mathcal{B}|}\sum_{i\in \mathcal{B}}\mathbb{E}\| \nabla \mathcal{L}_{i}(\theta_{i}^{s}) - g_{i}^{s}\|^{2}\leq \bar{\nu}, \tag{6}
$$

where the first inequality follows Lemma 5, and the last inequality follow Assumption 2. For  $T_{3}$ , by Assumption 3, we have

$$
T_{3} = \frac{3\tau\eta^{2}}{|\mathcal{B}|}\sum_{i\in \mathcal{B}}\sum_{s = 0}^{\tau -1}\mathbb{E}\| \nabla \mathcal{L}_{i}(\theta_{i}^{s}) - \nabla \mathcal{L}_{\mathcal{B}}(\theta_{i}^{s})\|^{2}\leq 3\tau \eta^{2}\sum_{s = 0}^{\tau -1}\bar{\zeta} = 3\tau^{2}\eta^{2}\bar{\zeta}. \tag{7}
$$

Plugging Inequality (5), Inequality (6), and Inequality (7) back to Inequality (4), with  $\eta \leq 1 / 2\tau$ , we have

$$
\frac{1}{|\mathcal{B}|}\sum_{i\in \mathcal{B}}\mathbb{E}\left\| \Delta_i - \bar{\Delta}_{\mathcal{B}}\right\| ^2\leq 3\tau^2\eta^2 (2\bar{\nu} +\bar{\zeta})\leq 2\bar{\nu} +\bar{\zeta}. \tag{8}
$$

This concludes the proof.

# 13.2. Proof of Lemma 1

Proof. Recall that our method is denoted by  $F\colon \mathbb{R}^{d\times n}\to \mathbb{R}^d$  . Given that  $\begin{array}{r}\Delta^t = F(\Delta_1^t,\Delta_2^t,\dots,\Delta_n^t) = 1 / |\mathcal{S}^t |\sum_{i\in \mathcal{S}^t}\Delta_i^t \end{array}$  where  $S^t$  is the selected set by  $F$  in round  $t$  and  $m< n / 2$  . Let  $\begin{array}{r}\Delta_{\mathcal{B}}^{t} = 1 / |\mathcal{B}|\sum_{i\in \mathcal{B}}\Delta_{i}^{t} \end{array}$  be the average of benign updates in round  $t$  , where  $|\mathcal{B}| = n - m$  . We have

$$
\mathbb{E}\left\| \Delta^t -\Delta_{\mathcal{B}}^t\right\| ^2 = \mathbb{E}\left\| \frac{1}{|S^t|}\sum_{i\in S^t}(\Delta_i^t -\Delta_{\mathcal{B}}^t)\right\| ^2\leq \mathbb{E}\frac{1}{|S^t|}\sum_{i\in S^t}\left\| \Delta_i^t -\Delta_{\mathcal{B}}^t\right\| ^2, \tag{9}
$$

where the first inequality follows Lemma 5.

If  $S^t\subseteq B$  ,thus  $S^{t}\backslash B = \emptyset$  and  $B\backslash S^t\subseteq B$  we have

$$
\begin{array}{rl} & {\mathbb{E}\left\| \Delta^{t} - \Delta_{\mathcal{B}}^{t}\right\|^{2}\leq \mathbb{E}\frac{1}{|S^{t}|}\sum_{i\in S^{t}}\left\| \Delta_{i}^{t} - \Delta_{\mathcal{B}}^{t}\right\|^{2}\leq \mathbb{E}\frac{1}{|S^{t}|}\sum_{i\in \mathcal{B}}\left\| \Delta_{i}^{t} - \Delta_{\mathcal{B}}^{t}\right\|^{2}}\\ & {\qquad \leq \frac{|\mathcal{B}|}{|S^{t}|}\left(2\bar{\nu} +\bar{\zeta}\right)}\\ & {\qquad = \frac{n - m}{|S^{t}|}\left(2\bar{\nu} +\bar{\zeta}\right),} \end{array} \tag{10}
$$

where the last inequality follows Lemma 6.

If  $S\not\subseteq B$  , we let  $S\backslash B = \mathcal{R}$  , where  $|\mathcal{R}|\leq m$  , and  $S\cap B = \mathcal{P}$  , one yields

$$
\begin{array}{rl} & {\mathbb{E}\left\| \Delta^{t} - \Delta_{\mathcal{B}}^{t}\right\|^{2}\leq \mathbb{E}\frac{1}{|S^{t}|}\sum_{i\in S^{t}}\left\| \Delta_{i}^{t} - \Delta_{\mathcal{B}}_{i}^{t}\right\|^{2} = \mathbb{E}\frac{1}{|S^{t}|}\left[\sum_{i\in \mathcal{P}}\left\| \Delta_{i}^{t} - \Delta_{\mathcal{B}}_{i}^{t}\right\|^{2} + \sum_{i\in \mathcal{R}}\left\| \Delta_{i}^{t} - \Delta_{\mathcal{B}}_{i}^{t}\right\|^{2}\right]}\\ & {\qquad = \mathbb{E}\frac{1}{|S^{t}|}\left[\sum_{i\in \mathcal{R}}\left\| \Delta_{i}^{t} - \Delta_{\mathcal{P}}^{t} + \Delta_{\mathcal{P}}^{t} - \Delta_{\mathcal{B}}^{t}\right\|^{2} + \sum_{i\in \mathcal{P}}\left\| \Delta_{i}^{t} - \Delta_{\mathcal{B}}^{t}\right\|^{2}\right]}\\ & {\qquad \leq \mathbb{E}\frac{1}{|S^{t}|}\left[2\sum_{i\in \mathcal{R}}\left\| \Delta_{i}^{t} - \Delta_{\mathcal{P}}^{t}\right\|^{2} + 2\sum_{i\in \mathcal{R}}\left\| \Delta_{\mathcal{P}}^{t} - \Delta_{\mathcal{B}}^{t}\right\|^{2} + \sum_{i\in \mathcal{P}}\left\| \Delta_{i}^{t} - \Delta_{\mathcal{B}}^{t}\right\|^{2}\right],} \end{array} \tag{11}
$$

where the first inequality follows Lemma 4.

Due to the use of MZ- score, models in  $S^t$  are centered around the median within a  $\lambda_c$  (and  $\lambda_s$  ) radius. If the radius parameter  $\lambda_c$  or  $\lambda_s$  equals zero, only the median model (based on Cosine similarity or masked principal sign alignment ratio) will be selected for averaging. To maximize benign model inclusion in averaging, we assume the radius parameters  $\lambda_c$  and  $\lambda_s$  are set sufficiently large to ensure  $|\mathcal{S}^t |\geq n - 2m$  . More precisely, assume there exist two positive constants  $\lambda_c^+$  and  $\lambda_s^+$  , and if the radius parameters  $\lambda_c$  and  $\lambda_s$  in Algorithm 1 satisfy  $\lambda_c\geq \lambda_c^+,\lambda_s\geq \lambda_s^+$  , we have  $|\mathcal{S}^t |\geq n - 2m$  . Additionally, if  $m< n / (3 + \epsilon)$  , we can have at least one benign clients in  $S^t$  and the ratio of  $|\mathcal{R}| / |\mathcal{P}|$  is bounded by  $1 / \epsilon$  . Consequently, we

$$
\begin{array}{r l} & {\mathbb{E}\left\| \Delta^{t} - \Delta_{B}^{t}\right\|^{2}\leq \mathbb{E}\frac{1}{|S^{t}|}\left[2\sum_{i\in \mathcal{R}}\left[\frac{1}{|\mathcal{P}|}\sum_{j\in \mathcal{P}}\left\| \Delta_{i}^{t} - \Delta_{j}^{t}\right\|^{2}\right] + \frac{2|\mathcal{R}|}{|\mathcal{P}|}\sum_{i\in \mathcal{P}}\left\| \Delta_{i}^{t} - \Delta_{B}^{t}\right\|^{2} + \sum_{i\in \mathcal{P}}\left\| \Delta_{i}^{t} - \Delta_{B}^{t}\right\|^{2}\right]}\\ & {\qquad \leq \mathbb{E}\frac{1}{|S^{t}|}\left[8|\mathcal{R}|c^{2} + \left(\frac{2|\mathcal{R}|}{|\mathcal{P}|} +1\right)\sum_{i\in \mathcal{P}}\left\| \Delta_{i}^{t} - \Delta_{B}^{t}\right\|^{2}\right]}\\ & {\qquad \leq \mathbb{E}\frac{1}{|S^{t}|}\left[8|\mathcal{R}|c^{2} + \left(\frac{2|\mathcal{R}|}{|\mathcal{P}|} +\mathbb{1}\right)|\mathcal{B}|(2\bar{\nu} +\bar{\zeta})\right]}\\ & {\qquad = \frac{|\mathcal{B}|}{|S^{t}|}\left(\frac{2|\mathcal{R}|}{|\mathcal{P}|} +1\right)(2\bar{\nu} +\bar{\zeta}) + \frac{8|\mathcal{R}|c^{2}}{|S^{t}|}}\\ & {\qquad \leq \frac{|\mathcal{B}|}{|S^{t}|}\left(\frac{2}{\epsilon} +1\right)(2\bar{\nu} +\bar{\zeta}) + \frac{8|\mathcal{R}|c^{2}}{|S^{t}|},} \end{array} \tag{12}
$$

where the first inequality follows Lemma 5, the second inequality holds as the model updates in  $S^{t}$  is bounded by  $c$ , the third inequality follows Lemma 6.

Summarizing Inequality (10) and Inequality (12), we have

$$
\begin{array}{r l} & {\mathbb{E}\left\| \Delta^{t} - \Delta_{\mathcal{B}}^{t}\right\|^{2}\leq \left\{ \begin{array}{l l}{\frac{n - m}{n - 2m}\left(2\bar{\nu} +\bar{\zeta}\right),} & {\mathrm{if~}\mathcal{S}^{t}\subseteq \mathcal{B}}\\ {\frac{n - m}{n - 2m}\left(\frac{2}{\epsilon} +1\right)\left(2\bar{\nu} +\bar{\zeta}\right) + \frac{8m c^{2}}{n - 2m},} & {\mathrm{if~}\mathcal{S}^{t}\notin \mathcal{B}} \end{array} \right.}\\ & {\qquad \leq \frac{n - m}{n - 2m}\left(\frac{2}{\epsilon} +1\right)\left(2\bar{\nu} +\bar{\zeta}\right) + \frac{8m c^{2}}{n - 2m}}\\ & {\qquad \leq \left(1 + \frac{m}{n - 2m}\right)\left(\left(\frac{2}{\epsilon} +1\right)\left(2\bar{\nu} +\bar{\zeta}\right) + 8c^{2}\right),} \end{array} \tag{13}
$$

which concludes the proof.

# 13.3. Proof of Lemma 2

Proof. We use  $\theta$  to denote the model trained over  $[n]$  which contains  $B\in [n],\mathcal{M}\in [n]$  where  $B$  is the set of benign clients and  $\mathcal{M}$  is the set of malicious clients. Obviously,  $B\cup \mathcal{M} = [n]$  and  $B\cap \mathcal{M} = \emptyset$ . We use  $\theta^{*}$  to denote the clean model which is trained over  $B$ . The update rules for  $\theta$  and  $\theta^{*}$  are as follows,

$$
\begin{array}{c}{\theta^{t + 1} = \theta^t -\alpha \Delta^t}\\ {\theta^{t + 1,*} = \theta^{*,*} - \alpha \Delta^{t,*}.} \end{array} \tag{14}
$$

With Equation (14) and Equation (15), we have

$$
\begin{array}{r l} & {\left\| \theta^{t + 1} - \theta^{t + 1,*}\right\|^{2} = \left\| \theta^{t} - \alpha \Delta^{t} - (\theta^{t,*} - \alpha \Delta^{t,*})\right\|^{2}}\\ & {\qquad = \left\| \theta^{t} - \theta^{t,*} + \alpha \Delta^{t} - \alpha \Delta^{t,*}\right\|^{2}}\\ & {\qquad \leq 2\left\| \theta^{t} - \theta^{t,*}\right\|^{2} + \underbrace{2\alpha^{2}\left\|\Delta^{t} - \Delta^{t,*}\right\|^{2}}_{T_{1}},} \end{array} \tag{16}
$$

where the first inequality follows Lemma 4.

Now, we treat  $T_{1}$ . As  $\Delta^{t,*} = 1 / |\mathcal{B}|\sum_{i\in \mathcal{B}}\Delta_{i}^{t,*}$ , let  $\Delta_{\mathcal{B}}^{t} = 1 / |\mathcal{B}|\sum_{i\in \mathcal{B}}\Delta_{i}^{t}$ , we have

$$
\begin{array}{r l} & {T_{1} = 2\alpha^{2}\left\| \Delta^{t} - \Delta_{\mathcal{B}}^{t} + \Delta_{\mathcal{B}}^{t} - \Delta^{t,*}\right\|^{2}}\\ & {\qquad \leq \underbrace{4\alpha^{2}\left\|\Delta^{t} - \Delta_{\mathcal{B}}^{t}\right\|^{2}}_{T_{2}} + \underbrace{4\alpha^{2}\left\|\Delta_{\mathcal{B}}^{t} - \Delta^{t,*}\right\|^{2}}_{T_{3}},} \end{array} \tag{17}
$$

where the first inequality follows Lemma 4.

We now treat  $T_{2},T_{3}$  , respectively. For  $T_{2}$  , given that  $\Delta^{t} = F(\Delta_{1}^{t},\Delta_{2}^{t},\dots,\Delta_{n}^{t})$  , we have

$$
T_{2} = 4\alpha^{2}\left\| \Delta^{t} - \Delta_{B}^{t}\right\|^{2}\leq 4\alpha^{2}\kappa , \tag{18}
$$

where the first inequality follows Lemma 1 in the paper. Define  $\begin{array}{r}\Delta_{B}\coloneqq \frac{1}{|\mathcal{B}|}\sum_{i\in \mathcal{B}}\Delta_{i}^{t} = \frac{1}{|\mathcal{B}|}\sum_{i\in \mathcal{B}}\eta_{i}^{t} \end{array}$  . For  $T_{3}$  , we have

$$
\begin{array}{rl} & {= 4\alpha^{2}\left\| \Delta_{B}^{t} - \Delta^{t,*}\right\|^{2} = 4\alpha^{2}\eta^{2}\left\| g_{B}^{t} - g^{t,*}\right\|^{2}}\\ & {\quad = 4\alpha^{2}\eta^{2}\left\| g_{B}^{t} - \nabla \mathcal{L}_{B}(\theta^{t}) + \nabla \mathcal{L}_{B}(\theta^{t}) - g^{t,*} - \nabla \mathcal{L}_{B}(\theta^{t,*}) + \nabla \mathcal{L}_{B}(\theta^{t,*})\right\|^{2}}\\ & {\quad = 4\alpha^{2}\eta^{2}\left\| g_{B}^{t} - \nabla \mathcal{L}_{B}(\theta^{t}) - (g^{t,*} - \nabla \mathcal{L}_{B}(\theta^{t,*})) + \nabla \mathcal{L}_{B}(\theta^{t}) - \nabla \mathcal{L}_{B}(\theta^{t,*})\right\|^{2}}\\ & {\quad \leq \underbrace{12\alpha^{2}\eta^{2}\left\|g_{B}^{t} - \nabla\mathcal{L}_{B}(\theta^{t})\right\|^{2}}_{T_{4}} + \underbrace{12\alpha^{2}\eta^{2}\left\|(g^{t,*} - \nabla\mathcal{L}_{B}(\theta^{t,*}))\right\|^{2}}_{T_{5}} + \underbrace{12\alpha^{2}\eta^{2}\left\|\nabla\mathcal{L}_{B}(\theta^{t}) - \nabla\mathcal{L}_{B}(\theta^{t,*})\right\|^{2}}_{T_{6}}} \end{array} \tag{19}
$$

where the first inequality follows Lemma 4. For  $T_{4}$  , we have

$$
\begin{array}{rl} & {T_4 = 12\alpha^2\eta^2\left\| g_B^t -\nabla \mathcal{L}_B(\theta^t)\right\| ^2 = 12\alpha^2\eta^2\left\| \frac{1}{|\mathcal{B}|}\sum_{i\in \mathcal{B}}g_i^t -\frac{1}{|\mathcal{B}|}\sum_{i\in \mathcal{B}}\nabla \mathcal{L}_i(\theta_i^t)\right\| ^2 = 12\alpha^2\eta^2\left\| \frac{1}{|\mathcal{B}|}\sum_{i\in \mathcal{B}}\left(g_i^t -\nabla \mathcal{L}_i(\theta_i^t)\right)\right\| ^2}\\ & {\quad \leq \frac{12\alpha^2\eta^2}{|\mathcal{B}|}\sum_{i\in \mathcal{B}}\left\| g_i^t -\nabla \mathcal{L}_i(\theta_i^t)\right\| ^2 = \frac{12\alpha^2\eta^2}{|\mathcal{B}|}\sum_{i\in \mathcal{B}}\left\| \sum_{s = 0}^{\tau -1}g_i^{t,s} - \sum_{s = 0}^{\tau -1}\nabla \mathcal{L}_i(\theta_i^{t,s})\right\| ^2}\\ & {\quad \leq \frac{12\alpha^2\eta^2}{|\mathcal{B}|}\sum_{i\in \mathcal{B}}\sum_{s = 0}^{\tau -1}\left\| g_i^{t,s} - \nabla \mathcal{L}_i(\theta_i^{t,s})\right\| ^2\leq 12\alpha^2\eta^2\sum_{s = 0}^{\tau -1}\nu}\\ & {\quad = 12\alpha^2\tau^2\eta^2\bar{\nu},} \end{array} \tag{20}
$$

where the both first and second inequality follow Lemma 5, the third inequality follows Assumption 2. Similarly, we have

$$
T_{5}\leq 12\alpha^{2}\tau^{2}\eta^{2}\bar{\nu}. \tag{21}
$$

For  $T_{6}$  we have

$$
T_{6} = 12\alpha^{2}\eta^{2}\left\| \nabla \mathcal{L}_{B}(\theta^{t}) - \nabla \mathcal{L}_{B}(\theta^{t,*})\right\|^{2}\leq 12\alpha^{2}\eta^{2}\mu^{2}\left\| \theta^{t} - \theta^{t,*}\right\|^{2}, \tag{22}
$$

where the first inequality follows Assumption 1.

Plugging Inequality (22), Inequality (21), and Inequality (20) back to Inequality (19), we have:

$$
T_{3}\leq 24\alpha^{2}\tau^{2}\eta^{2}\bar{\nu} +12\alpha^{2}\eta^{2}\mu^{2}\left\| \theta^{t} - \theta^{t,*}\right\|^{2}. \tag{23}
$$

Plugging Inequality (23), Inequality (18) back to Inequality (17), we have

$$
T_{1}\leq 4\alpha^{2}\kappa +24\alpha^{2}\tau^{2}\eta^{2}\bar{\nu} +12\alpha^{2}\eta^{2}\mu^{2}\left\| \theta^{t} - \theta^{t,*}\right\|^{2}. \tag{24}
$$

Therefore, we have

$$
\begin{array}{rl} & {\left\| \theta^{t + 1} - \theta^{t + 1,*}\right\| ^2\leq 2\left\| \theta^t -\theta^{t,*}\right\| ^2 +4\alpha^2\kappa +24\alpha^2\tau^2\eta^2\bar{\nu} +12\alpha^2\eta^2\mu^2\left\| \theta^t -\theta^{t,*}\right\| ^2}\\ & {\qquad = (2 + 12\alpha^2\eta^2\mu^2)\left\| \theta^t -\theta^{t,*}\right\| ^2 +4\alpha^2 (\kappa +6\tau^2\eta^2\bar{\nu})}\\ & {\qquad \leq (2 + 3\alpha^2\tau^{-2}\mu^2)\left\| \theta^t -\theta^{t,*}\right\| ^2 +4\alpha^2 (\kappa +2\bar{\nu})}\\ & {\qquad \leq (2 + 3\alpha^2\mu^2)\left\| \theta^t -\theta^{t,*}\right\| ^2 +4\alpha^2 (\kappa +2\bar{\nu}),} \end{array} \tag{25}
$$

where the second inequality follows  $\eta \leq 1 / 2\tau$  , and the last inequality holds as  $\tau^{- 2}\leq 1$

We inductively prove the Lemma 2, assume for  $T - 1$  the statement of Lemma holds. Let  $\begin{array}{r}\phi (T) = \sum_{i = 1}^{T}(\alpha^{i})^{2} \end{array}$  , by Inequality (25), we have

$$
\left\| \theta^{T} - \theta^{T,*}\right\|^{2}\leq (2 + 3\mu^{2}(\alpha^{T})^{2})\phi (T - 1)(2 + 3\mu^{2})^{\phi (T - 1)}(\kappa +2\bar{\nu}) + (\kappa +2\bar{\nu})(\alpha^{T})^{2}. \tag{26}
$$

By Bernoulli's inequality we have

$$
\begin{array}{r l} & {\left\| \theta^{T} - \theta^{T,*}\right\|^{2}\leq \phi (T - 1)(2 + 3\mu^{2})^{\phi (T - 1) + (\alpha^{T})^{2}}(\kappa +2\bar{\nu}) + (\kappa +2\bar{\nu})(\alpha^{T})^{2}}\\ & {\qquad = \phi (T - 1)(2 + 3\mu^{2})^{\phi (T)}(\kappa +2\bar{\nu}) + (\kappa +2\bar{\nu})(\alpha^{T})^{2}}\\ & {\qquad \leq (\phi (T - 1) + (\alpha^{T})^{2})(2 + 3\mu^{2})^{\phi (T)}(\kappa +2\bar{\nu})}\\ & {\qquad \leq \phi (T)(2 + 3\mu^{2})^{\phi (T)}(\kappa +2\bar{\nu}),} \end{array} \tag{27}
$$

which concludes the proof.