Received XX Month, XXXX; revised XX Month, XXXX; accepted XX Month, XXXX; Date of publication XX Month, XXXX; date of current version XX Month, XXXX.

Digital Object Identifier 10.1109/TMLCN.2022.1234567

# Convergence-Privacy-Fairness Trade-Off in Personalized Federated Learning

Xiyu Zhao $^{1,2}$ , <PERSON><PERSON> $^{1,3}$  (Senior Member, IEEE), Wei<PERSON>i Li $^{1,4}$ , <PERSON> $^{4}$  (Fellow, IEEE), <PERSON><PERSON><PERSON> $^{5}$  (Fellow, IEEE), <PERSON><PERSON> $^{2}$ , <PERSON>feng Tao $^{1,3}$  (Senior Member, IEEE), and <PERSON> $^{1,3}$  (Fellow, IEEE)

$^{1}$ School of Information and Communication Engineering, Beijing University of Posts and Telecommunications, Beijing 100876, China   $^{2}$ School of Computing, Macquarie University, Sydney, NSW 2109, Australia   $^{3}$ Department of Broadband Communication, Peng Cheng Laboratory, Shenzhen 518055, China   $^{4}$ School of Electrical and Data Engineering, University of Technology Sydney, Sydney, NSW 2007, Australia   $^{5}$ Department of Electrical and Computer Engineering, University of Manitoba, Winnipeg, MB R3T 2N2, Canada

Corresponding author: <PERSON><PERSON> (email: <EMAIL>).

The work was supported by the Joint funds for Regional Innovation and Development of the National Natural Science Foundation of China(No.U21A20449) and the Fundamental Research Funds for the Central Universities (No.2242022k60006).

ABSTRACT Personalized federated learning (PFL), e.g., the renowned Ditto, strikes a balance between personalization and generalization by conducting federated learning (FL) to guide personalized learning (PL). While FL is unaffected by personalized model training, in Ditto, PL depends on the outcome of the FL. However, the clients' concern about their privacy and consequent perturbation of their local models can affect the convergence and (performance) fairness of PL. This paper presents PFL, called DP- Ditto, which is a non- trivial extension of Ditto under the protection of differential privacy (DP), and analyzes the trade- off among its privacy guarantee, model convergence, and performance distribution fairness. We also analyze the convergence upper bound of the personalized models under DP- Ditto and derive the optimal number of global aggregations given a privacy budget. Further, we analyze the performance fairness of the personalized models, and reveal the feasibility of optimizing DP- Ditto jointly for convergence and fairness. Experiments validate our analysis and demonstrate that DP- Ditto can surpass the DP- perturbed versions of the state- of- the- art PFL models, such as FedAMP, pFedMe, APPLE, and FedALA, by over  $32.71\%$  in fairness and  $9.66\%$  in accuracy.

INDEX TERMS Personalized federated learning, differential privacy, convergence, fairness.

# I. Introduction

RECENTLY, personalized federated learning (PFL) has been proposed, such as federated learning (FL) with meta- learning. It tailors learning processes to provide personalized models for individual clients while benefiting from the global perception offered by FL, thereby capturing both generalization and personalization in the models. PFL can strike a balance between personalized models and the global model, e.g., via a global- regularized multi- task framework [1]. It can provide customized services for applications with heterogeneous local data distributions or tasks, e.g., intelligent Internet of Things networks with geographically dispersed clients [2]–[4]. A popular PFL technique is called

Ditto, which was developed to adapt to the heterogeneity in FL settings by simultaneously learning a global model and distinct personal models for multiple agents [1].

While preserving personal data locally throughout its training process, similar to FL, PFL can still suffer from privacy leakage due to the incorporation of FL. Differential privacy (DP) [5] can be potentially applied to protect the privacy of Ditto. In each round, every client trains two models separately, including its local model and personalized model, based on its local dataset and the global model broadcast by the server in the last round. With DP, the clients perturb their local models by adding noise before uploading them to the server, where the perturbed local models are aggregated

to update the global model needed for the clients to train their local models and personalized models further. This privacy- preserving PFL model, namely DP- Ditto, is a new framework. It is important to carefully balance convergence, privacy, and fairness in DP- Ditto. However, the impact of privacy preservation, i.e., the incorporation of DP, on the convergence and fairness of the personalized models has not been investigated in the literature. Let alone a PFL design with a balanced consideration between convergence, privacy, and fairness.

Although privacy and fairness have been studied separately in the contexts of both FL and PFL, e.g., [6]- [21], they have never been considered jointly in PFL, such as Ditto [1]. Their interplay has been overlooked. The majority of the existing studies, e.g., [21]- [30], have focused on the model accuracy of PFL. Some other existing works, e.g., [1], [31], [32], have attempted to improve the performance distribution fairness of FL. None of these studies has addressed the influence of DP on the fairness and accuracy of PFL.

This paper studies the trade- off between privacy guarantee, model convergence, and performance distribution fairness of privacy- preserving PFL, more specifically, DP- Ditto. We analyze the convergence upper bound of the personalized learning (PL) and accordingly optimize the aggregation number of FL given a privacy budget. We also analyze the fairness of PL in DP- Ditto on a class of linear problems, revealing the possibility of maximizing the fairness of privacy- preserving PFL given a privacy budget and aggregation number. To the best of our knowledge, this is the first work that investigates the trade- off among the privacy, convergence, and fairness of PFL, and optimizes PFL for convergence under the constraints of performance distribution fairness and privacy requirements. The major contributions of this paper are summarized as follows:

We propose a new privacy- preserving PFL framework, i.e., DP- Ditto, by incorporating an  $(\epsilon ,\delta)$ - DP perturbation mechanism into Ditto. This extension is non- trivial and necessitates a delicate balance between convergence, privacy, and fairness. A convergence upper bound of DP- Ditto is derived, capturing the impact of DP on the convergence of personalized models. The number of global aggregations is identified by minimizing the convergence upper bound. We analyze the fairness of DP- Ditto on a class of linear problems to reveal the conditional existence and uniqueness of the optimal setting balancing convergence and fairness, given a privacy requirement.

Extensive experiments validate our convergence and fairness analysis of DP- Ditto and the obtained optimal FL aggregation number and weighting coefficients of FL versus PL. Three image classification tasks are performed using deep neural network (DNN), multi- class linear regression (MLR), and convolutional neural network (CNN) on the Federated MNIST, Federated FMNIST, and Federated CIFAR10 datasets. DP- Ditto can outperform its benchmarks, i.e., the DP- perturbed FedAMP [24], pFedMe [22], APPLE [25], and FedALA [26], by  $99.98\%$ $32.71\%$ $97.04\%$  and  $99.72\%$  respectively, in fairness and  $59.06\%$ $9.66\%$ $28.67\%$  and  $64.31\%$  in accuracy.

The rest of this paper is structured as follows. Section II presents a review of related works. Section III outlines the system and threat models of DP- Ditto and analyzes its privacy and DP noise variance. In Section IV, the convergence upper bound of DP- Ditto is established, and the optimal number of FL global aggregations is obtained accordingly. In Section V, we analyze the fairness of PL on a class of linear regression problems to demonstrate the feasibility of fairness maximization. The experimental results are discussed in Section VI. The conclusions are given in Section VII.

# II. Related Work

# A. Personalization

PFL frameworks have been explored to combat statistical heterogeneity through transfer learning (TL) [28], meta- learning [21], [27], and other forms of multitask learning (MTL) [22]- [26]. None of these has addressed the fairness among the participants of PFL. TL conveys knowledge from an originating domain to a destination domain. TL- based FL enhances personalization by diminishing domain discrepancy of the global and local models [33]. FedMD [28] is an FL structure grounded in TL and knowledge distillation (KD), enabling clients to formulate autonomous models utilizing their individual private data. Preceding the FL training and KD, TL is implemented by employing a model previously trained on a publicly available dataset.

Meta- learning finds utility in FL in enhancing the global model for rapid personalization. In [27], a variation of FedAvg, named Per- FedAvg, was introduced, leveraging the Model- Agnostic Meta- Learning (MAML). It acquired a proficient initial global model that is effective on a novel heterogeneous task and can be achieved through only a few gradient descent steps. You et al. [29] further proposed a Semi- Synchronous Personalized FederatedAveraging (PerFedS) mechanism based on MAML, where the server sends a meta- model to a set of UEs participating in the global updating and the stragglers in each round. In another meta- learning- based PFL framework [21], a privacy budget allocation scheme based on Rényi DP composition theory was designed to address information leakage arising from two- stage gradient descent.

MTL trains a model to simultaneously execute several related tasks. By considering an FL client as a task, there is the opportunity to comprehend the interdependence among the clients manifested by their diverse local data. In [22], pFedMe employing Moreau envelopes as the regularized loss functions for clients was recommended to disentangle the optimization of personalized models from learning the global model. The global model is obtained by aggregating the local models updated based on the personalized models

of the clients. Each client's personalized model maintains a bounded distance from the global model. In [23], FedProx was formulated by incorporating a proximal term into the local subproblem. Consequently, the contrast was delineated between the global and local models to ease the modulation of the influence of local updates. In [30], a federated multitask learning (FMTL) framework was developed, where the server broadcasts a set of global models aggregated based on the local models of different clusters of clients, and each client selects one of the global models for its local model updating.

Huang et al. [24] integrated PFL with supplementary terms and employed a federated attentive message passing (FedAMP) strategy to mitigate the impact of diverse data. Consequently, the convergence of the FedAMP was guaranteed. A protocol named APPLE [25] was proposed to improve the personalized model of each client based on the others' models. Clients obtain the personalized models locally by aggregating the core models of other clients downloaded from the server. The aggregation weights and the core models are locally learned from the personalized model by adding a proximal term to the local objectives. Instead of overwriting the old local model with the downloaded global model, FedALA [26] aggregates the downloaded global model and the old local model for local model initialization.

These existing PFL frameworks [21]- [28] have focused primarily on model accuracy. None of these has taken the fairness of the personalized models into consideration.

# B. Privacy

Existing studies [9]- [13] have explored ways to integrate privacy techniques into FL to provide a demonstrable assurance of safeguarding privacy. However, little to no consideration has been given to the personalization of learning models and their fairness. In [9], a DP- based framework was suggested to avert privacy leakage by introducing noise to obfuscate the local model parameters. In [10], three local DP (LDP) techniques were devised to uphold privacy, where LDP was incorporated into FL to forecast traffic status, mitigate privacy risks, and diminish communication overhead in crowdsourcing scenarios. The authors of [11] suggested FL with LDP, wherein LDP- based perturbation was applied during model uploading, adhering to individual privacy budgets. Liu et al. [17] proposed a transceiver protocol to maximize the convergence rate under privacy constraints in a MIMO- based DP FL system, where a server performs over- the- air model aggregation and parallel private information extraction from the uploaded local gradients with a DP mechanism.

In [12], DP noises were adaptively added to local model parameters to preserve user privacy during FL. The amplitude of DP noises was adaptively adjusted to balance preserving privacy and facilitating convergence. FedDual [13] was designed to preserve user privacy by adding DP noises locally and aggregating asynchronously via a gossip protocol. Noise- cutting was adopted to alleviate the impact of the DP noise on the global model. Hu et al. [14] proposed privacy- preserving PFL using the Gaussian mechanism, which provides a privacy guarantee by adding Gaussian noise to the uploaded local updates. In [15], the Gaussian mechanism was considered in a mean- regularized MTL framework, and the accuracy was analyzed for single- round FL using a Bayesian framework. In [21], the allocation of a privacy budget was considered for meta- learning- based PFL. In [18], differentially private federated MTL (DPFML) was designed for human digital twin systems by integrating DPFML and a computational- efficient blockchain- enabled validation process.

These existing works [9]- [15], [21] have given no consideration to fairness among the participants in FL, especially in the presence of statistical heterogeneity.

# C. Fairness

Some existing studies, e.g., [1], [31], [32], have attempted to improve performance distribution fairness, i.e., by mitigating the variability in model accuracy among different clients. Yet, none has taken user privacy into account. In [31],  $q$ - FFL was proposed to achieve a more uniform accuracy distribution across clients. A parameter  $q$  was used to reweight the aggregation loss by assigning bigger weights to clients undergoing more significant losses. In [32], FedMGDA+ was suggested to enhance the robustness of the model while upholding fairness with positive intentions. A multi- objective problem was structured to diminish the loss functions across all clients. It was tackled by employing Pareto- steady resolutions to pinpoint a collective descent direction suitable for all the chosen clients. Li et al. [1] designed a scalable federated MTL framework Ditto, which simultaneously learns personalized and global models in a global- regularized framework. Regularization was introduced to bring the personalized models in proximity to the optimal global model. The optimal weighting coefficient of Ditto was designed in terms of fairness and robustness. These studies [1], [31], [32] have overlooked privacy risks or failed to address the influence of DP on fairness.

# III. Framework of PFL

# A. PFL

PFL consists of a server and  $N$  clients.  $\mathbb{N}$  denotes the set of clients.  $\mathcal{D}_n$  denotes the local dataset at client  $n\in \mathbb{N}$ .  $\mathcal{D}$  is the collection of all data samples.  $|\mathcal{D}| = \sum_{n = 1}^{N}|\mathcal{D}_n|$  is the total size of all data samples,  $|\cdot |$  stands for cardinality. Like Ditto, PFL has both global and personal objectives for FL and PL, respectively. At the server, the global objective is to learn a global model with the minimum global training loss:

$$
\min_{\omega}F(F_{1}(\omega),\dots ,F_{N}(\omega)), \tag{1}
$$

where  $\omega \in \mathbb{R}^d$  is the model parameter with  $d$  elements,  $F_{n}(\cdot)$  is the local loss function of client  $n\in \mathbb{N}$ , and  $F(\cdot ,\dots ,\cdot)$  is the global loss function:

$$
F(F_{1}(\omega),\dots ,F_{N}(\omega)) = \sum_{n = 1}^{N}p_{n}F_{n}(\omega), \tag{2}
$$

where  $p_n\triangleq |\mathcal{D}_n| / |\mathcal{D}|$  with  $\textstyle \sum_{n = 1}^{N}p_{n} = 1$  . We assume the size of each client's local dataset is the same, i.e.,  $\begin{array}{r}p_{n} = \frac{1}{N} \end{array}$

To capture both generalization and personalization as in Ditto, for client  $n$  we encourage its personalized model to be close to the optimal global model, i.e.,

$$
\min_{\pmb {\pi}_n}f_n(\pmb {\pi}_n;\pmb {\omega}^*) = \left(1 - \frac{\lambda}{2}\right)F_n(\pmb {\pi}_n) + \frac{\lambda}{2}\parallel \pmb {\pi}_n - \pmb {\omega}^*\parallel^2
$$

$$
\mathrm{s.t.}\omega^{*} = \arg \min_{n}\frac{1}{N}\sum_{n = 1}^{N}F_{n}\left(\omega\right), \tag{3b}
$$

where  $f_{n}(\cdot)$  is the loss function of the personalized model;  $\lambda \in [0,2]$  is a weighting coefficient that controls the tradeoff between the global and local models. When  $\lambda = 0$  PFL trains a local model for each client based on its local datasets. When  $\lambda = 2$  the personal objective becomes obtaining an optimal global model with no personalization. Let  $\pmb {u}_n^*$  and  $\pi_{n}^{*}$  be the optimal local model based on the local data and the optimal personalized model, i.e.,

$$
\pmb {u}_n^* = \underset {\pmb {u}_n}{\arg \min}F_n(\pmb {u}_n);\pmb {\pi}_n^* = \underset {\pmb {\pi}_n}{\arg \min}f_n(\pmb {\pi}_n;\pmb {\omega}^*). \tag{4}
$$

According to (1)- (3), a local model  $\pmb {u}_n$  is trained for global aggregation. A personalized model  $\pi_{n}$  is adjusted according to local training and the global model at each client  $n$  .The training of the global model and that of the personalized models are assumed to be synchronized, i.e., at each round  $t + 1$  . Client  $n$  updates its personalized model  $\pi_{n}^{t + 1}$  based on the global model  $\omega^t$  updated at the  $t$  - th round.

# B. Threat Model

The server may attempt to recover the training datasets or infer the private features based on the models uploaded by the clients. There may be external attackers who intend to breach the privacy of the clients. Although the clients train models locally, the local models that the clients share with the server can be analyzed to potentially compromise their privacy under inference attacks during learning [34] and model- inversion attacks during testing [35]. The private information can be recovered by the attackers.

Typical privacy- preserving methods for FL include homomorphic encryption, secure multi- party computation, and DP [36]. While preventing the server from deciphering local models, homomorphic encryption requires all devices to use the same private key and cannot stop them from eavesdropping on each other. Secure multi- party computation enables clients to collaboratively compute an arbitrary functionality, but requires multiple interactions in a learning process, e.g., public key sharing among clients for key agreement, at the expense of high computation and communication overhead [37]. Typically, homomorphic encryption and secure multi- party computation are computationally expensive and need a trusted third party for key agreement [38]. To this end, DP is employed to preserve the privacy of PFL in this paper.

TABLE 1. Notation and definitions  

<table><tr><td>Notation</td><td>Definition</td></tr><tr><td>D, Dn</td><td>The local datasets of all clients and client n</td></tr><tr><td>F(·), Fn(·)</td><td>The global and the local loss function of client n</td></tr><tr><td>fn(·)</td><td>The loss function of client n&#x27;s personalized model</td></tr><tr><td>VF(·)</td><td>Gradient of a function F(·)</td></tr><tr><td>gn(ωt,n; ωt)</td><td>The stochastic gradient of fn(ωt,n; ωt)</td></tr><tr><td>ω, ω*</td><td>The global and optimal global models</td></tr><tr><td>un, un*</td><td>The local and optimal local models of client n</td></tr><tr><td>ωn, ωn*</td><td>The personalized and optimal personalized models of client n</td></tr><tr><td>ωt</td><td>The global model with DP noise at t-th aggregation</td></tr><tr><td>un, un*</td><td>The local and personalized models of client n with DP noise at t-th aggregation</td></tr><tr><td>zt</td><td>The DP noise added by client n at t-th aggregation</td></tr><tr><td>zt</td><td>The equivalent noise term imposed on the global model after t-th aggregation</td></tr><tr><td>σu, σz</td><td>The standard deviation of the DP noise zt and zt</td></tr><tr><td>C</td><td>The clipping threshold of local model</td></tr><tr><td>T</td><td>The maximum number of global aggregations</td></tr><tr><td>ε, δ</td><td>The DP requirement</td></tr><tr><td>λ</td><td>The weighting coefficient of the personalized models</td></tr><tr><td>Δs</td><td>The sensitivity of client n&#x27;s local training process</td></tr><tr><td>ηG, ηL</td><td>The learning rates of the local model and the personalized model</td></tr><tr><td>ρ(·)</td><td>The fairness of a group of models</td></tr><tr><td>Yn, Xn</td><td>The local observations of client n</td></tr><tr><td>δ</td><td>The number of local samples</td></tr><tr><td>un</td><td>The estimate of un*</td></tr><tr><td>τn</td><td>The errors between un and ω*</td></tr><tr><td>νn</td><td>The errors between Yn and Xn un*</td></tr><tr><td>ζ2, σ2</td><td>The diagonal elements of τn and νn</td></tr><tr><td>σw</td><td>The diagonal element of the diagonal matrix ωn</td></tr><tr><td>ρ</td><td>The diagonal element of Xn Xn</td></tr></table>

# C. FL With DP

Consider the threat model described in Section III- B. The risk of privacy breaches arises from uploading FL local models to the server for FL global model aggregation. To preserve data privacy from the uploaded local models, a Gaussian DP mechanism can be used to guarantee  $(\epsilon ,\delta)$  DP by adding artificial Gaussian noises [39]. Let  $\mathbf{z}_n^t\sim \mathcal{N}(0,\sigma_u^2)$  denote the Gaussian noise added by client  $n$  at the  $t$ - th communication round.  $\mathbf{z}^t = \sum_{n = 1}^{N}\mathbf{z}_n^t$  is the noise imposed on the global model after the  $t$ - th aggregation. The additive noise of each client is independent and identically distributed (i.i.d.). Each element in  $\mathbf{z}^t$  follows  $\mathcal{N}(0,\sigma_z^2)$  with  $\sigma_z^2 = N\sigma_u^2$ .

Note that the DP noise is only added when a client uploads its local model for global model aggregation. Before uploading its local model in round  $t$ , each client  $n$  clips its local model to prevent gradient explosion, as given by

$$
\pmb {u}_n^{t + 1} = \pmb {u}_n^{t + 1} / \max (1,\frac{\parallel\pmb{u}_n^{t + 1}\parallel}{C}), \tag{5}
$$

where  $C$  is the pre- determined clipping threshold to ensure  $\| \pmb {u}_n\| \leq C$  [12].

To guarantee  $(\epsilon ,\delta)$  - DP with respect to the data used in the training of the local model, the standard deviation of  $\mathbf{z}_n^t$  from the Gaussian mechanism should satisfy  $\sigma_{u} =$ $\frac{\Delta s\sqrt{2qT\ln(1 / \delta)}}{\epsilon}$  [9], where  $T$  is the maximum number of communication rounds and  $q$  is the sampling ratio. Assume that all clients train and upload their local models at each communication round, i.e.,  $q = 1 / N$ $\Delta s$  is the sensitivity of client  $n$  's local training process, which captures the magnitude that a sample can change the training model in the worst case, as given by

$$
\Delta s = \max_{\mathcal{D}_n\in \mathcal{D}_n^{\prime}}\| \pmb {u}_n(\mathcal{D}_n) - \pmb {u}_n(\mathcal{D}_n^{\prime})\| , \tag{6}
$$

where  $\pmb {u}_n(\mathcal{D}_n)$  and  $\pmb {u}_n(\mathcal{D}_n^{\prime})$  are the local models obtained from the datasets  $\mathcal{D}_n$  and  $\mathcal{D}_n^{\prime}$  , respectively. Here,  $\mathcal{D}_n = \mathcal{D}\cup s$  and  $\mathcal{D}_n^{\prime} = \mathcal{D}\cup s^{\prime}$  are two adjacent datasets with the same size and differ by one sample, i.e.,  $s\in \mathcal{D}_n$ $s^{\prime}\in \mathcal{D}_n^{\prime}$  , and  $s\neq s^{\prime}$  . Considering the local model training from  $\mathcal{D}_n$  and  $\mathcal{D}_n^{\prime}$  we have [9]

$$
\begin{array}{rl} & {\Delta s = \underset {\mathcal{D}_n,\mathcal{D}_n^{\prime}}{\max}\left\| \frac{1}{|\mathcal{D}_n|}\sum_{s^{\prime}\in \mathcal{D}_n}\arg \underset {\omega}{\min}F_n(\omega ,\mathcal{D}_n)\right.}\\ & {\qquad \left. - \frac{1}{|\mathcal{D}_n^{\prime}|}\sum_{s^{\prime \prime}\in \mathcal{D}_n^{\prime}}\arg \underset {\omega}{\min}F_n(\omega ,\mathcal{D}_n^{\prime})\right\|}\\ & {\qquad \underset {s,s^{\prime}}{\max}\left\| \arg \min_{\omega}F_n(\omega ,s) - \arg \min_{\omega}F_n(\omega ,s^{\prime})\right\|}\\ & {\qquad = \frac{s,s^{\prime}}{\left|\mathcal{D}_n\right|}}\\ & {\qquad = \frac{2C}{\left|\mathcal{D}_n\right|},} \end{array} \tag{7}
$$

Clearly,  $\Delta s$  only depends on the size of datasets  $|\mathcal{D}_n|$  and the clipping threshold  $C$

According to (6), the standard deviations of the DP noise  $\sigma_{u}$  per client and  $\mathbf{z}^t$  are given by

$$
\sigma_{u} = \frac{\Delta s\sqrt{2TN\ln(1 / \delta)}}{\epsilon N};\sigma_{z} = \frac{\Delta s\sqrt{2T\ln(1 / \delta)}}{\epsilon}. \tag{8}
$$

The operations of PFL are summarized in Algorithm 1 and illustrated in Fig. 1. At each round  $t$  client  $n$  executes local training, and updates its local model  $\omega_{n}^{t + 1}$  and personalized model  $\pi_{n}^{t + 1}$  . The learning rates of the local and personalized models  $\sim t + 1$ $\eta_{\mathrm{G}}$  and  $\eta_{\mathrm{L}}$  , respectively. The noisy local model  $\tilde{\mathbf{u}}_n^t$  after clipping and DP perturbation is uploaded by client  $n$  to the server for updating the global model  $\tilde{\omega}^{t + 1}$

# IV. Convergence of Privacy-Preserving PFL

This section establishes the convergence upper bound of PFL and optimizes the number  $T$  of aggregation rounds to minimize the convergence upper bound of PL. The following assumptions facilitate the convergence analysis of PFL.

# Assumption 1.  $\forall n\in \mathbb{N}$

$F_{n}(\cdot)$  is  $\mu$  - strongly convex [40]- [42] and  $L$  smooth [41]- [43], i.e.,  $\begin{array}{r}F(\omega) - F(\omega^{*})\leq \frac{1}{2\mu}\parallel \nabla F(\omega)\parallel^{2} \end{array}$

# Algorithm 1 Privacy-Preserving PFL

Input:  $T,\lambda ,\omega^{0},\{\pi_{n}^{0}\}_{n\in \mathbb{N}},N,\eta_{\mathrm{G}},\eta_{\mathrm{L}},\epsilon$  and  $\delta$  Output:  $\omega^T$ $\{\pi_n^T\}_{n\in \mathbb{N}}$

1: for  $t = \{0,\dots ,T - 1\}$  do 2: // Local training process for global model; 3: for  $n\in \mathbb{N}$  do 4: Obtain  $\omega^t$  and let  $\pmb {u}_n^t = \pmb {\omega}^t$  5: Update the local model:  $\pmb {u}_n^{t + 1} = \pmb {u}_n^t - \eta_{\mathrm{G}}\nabla F_n(\pmb {u}_n^t)$  6: Clip the local model:  $\begin{array}{rlr}{\pmb {u}_n^{t + 1}} & = & {} \end{array}$ $\omega_{n}^{t + 1} / \max (1,\| \mathbf{u}_{n}^{t + 1}\|)$  7: Add noise and upload:  $\tilde{\pmb{u}}_n^{t + 1} = \pmb {u}_n^{t + 1} + \pmb {z}_n^{t + 1}$  8: // Local training process for personalized model; 9: Update personalized model  $\pi_{n}^{t + 1}$  10:  $\begin{array}{r}\pi_{n}^{t + 1} = \pi_{n}^{t} - \eta_{\mathrm{L}}((1 - \frac{\lambda}{2})\nabla F_{n}(\pi_{n}^{t}) + \lambda (\pi_{n}^{t} - \pi_{n}^{t}) + \lambda (\pi_{n}^{t} - \pi_{n}^{t}) + \lambda (\pi_{n}^{t} - \pi_{n}^{t}) + \lambda (\pi_{n}^{t} - \pi_{n}^{t}) + \lambda (\pi_{n}^{t} - \pi_{n}^{t}) + \lambda (\pi_{n}^{t} - 1)) \end{array}$ $\omega^t)$  11: end for 12: // Global model aggregating process;  $\omega^{t + 1} = \frac{1}{N}\sum_{n = 1}^{N}\mathbf{u}_n^{t + 1}$  13: Update the global model:  $\begin{array}{r}\omega^{t + 1} = \frac{1}{N}\sum_{n = 1}^{N}\mathbf{u}_n^{t + 1} \end{array}$  14: end for

![](images/b2824101c8ddebaa7e401a3d25adef31b61cfcf0d2ffbf8e8791ee9dba264db8.jpg)  
FIGURE 1. The diagram of PFL: In each round, every client trains its local model and its personalized model based on its local dataset and the global model broadcast by the server in the last round. Then, the clients perturb and upload their local models to the server, and the server aggregates the perturbed local models into the global model and broadcasts the global model.

and  $\parallel \nabla F(\omega) - \nabla F(\omega^{\prime})\parallel \leq L\parallel \omega - \omega^{\prime}\parallel$  Here,  $\mu$  and  $L$  are constants; The global learning rate  $\begin{array}{r}\eta_{\mathrm{G}}\leq \frac{2}{L} \end{array}$  and  $\begin{array}{r}\mu >\frac{2 - 2\lambda}{2 - \lambda} \end{array}$ $\mathbb{E}\left[\Vert \nabla F_n(\omega^t)\Vert^2\right]\leq G_0^2$  with  $G_{0}$  being a constant.;  $\parallel \pmb {u}_n^* - \pmb {\omega}^*\parallel \leq M$  where  $M$  is a constant.

# A. Convergence Analysis

1) Convergence of FL

The convergence upper bound of FL with DP has been established in the literature [9, Eq. (16)]

$$
\mathbb{E}\left[F\left(\tilde{\omega}^T\right) - F\left(\omega^*\right)\right]\leq \epsilon_{\mathrm{G}}^T\psi_1 + (1 - \epsilon_{\mathrm{G}}^T)\frac{\phi^T}{N\epsilon^2}, \tag{9}
$$

where, for conciseness,  $\epsilon_{\mathrm{G}} = 1 - 2\mu \eta_{\mathrm{G}} + \mu \eta_{\mathrm{G}}^{2}L$ $\Psi_{1} =$ $F(\omega^{0}) - F(\omega^{*})$  and  $\begin{array}{r}\phi = \frac{L^2\Delta s^2\ln(1 / \delta)}{\mu} \end{array}$  . Clearly, the DP noise increases the convergence upper bound with  $\begin{array}{r}\phi = \frac{L^2\sigma_n^2}{2T} \end{array}$

# 2) Convergence of PL

Under Assumption 1, the convergence upper bound of PL is established in the following.

Lemma 1. Given the  $PL$  rate  $\eta_{\mathrm{L}}$  and the weighting coefficient  $\lambda$  under Assumption 1, the expected difference between the personalized model  $\overline{\mathbb{C}_n^{t + 1}}$  and the optimum  $\overline{\mathbb{C}_n^*}$  at the end of the  $t$  - th communication round is upper- bounded by

$$
\begin{array}{rl} & {\mathbb{E}\left[\| \widetilde{\pmb{\omega}}_n^{t + 1} - \pmb {\omega}_n^* \| ^2\right]\leq \epsilon_{\mathrm{L}}\mathbb{E}\left[\| \widetilde{\pmb{\omega}}_n^t -\pmb {\omega}_n^*\| ^2\right] + }\\ & {\quad \left(\eta_{\mathrm{L}}^2 +\eta_{\mathrm{L}}^2\lambda^2\right)G + \frac{4\eta_{\mathrm{L}}^2\lambda^2 + 2\eta_{\mathrm{L}}\lambda^2}{\mu}\mathbb{E}\Big[F\big(\widetilde{\pmb{\omega}}^t\big) - F(\pmb {\omega}^*)\Big],} \end{array} \tag{10}
$$

where  $G\triangleq \left((1 - \frac{\lambda}{2})G_0 + \lambda \left(\frac{G_0}{\mu} +M\right)\right)^2$  and  $\epsilon_{\mathrm{L}} = 1-$ $\eta_{\mathrm{L}}\big((1 - \frac{\lambda}{2})\mu +\lambda \big) + \eta_{\mathrm{L}}$  .Clearly,  $\epsilon_{\mathrm{L}}$  increases with  $\lambda$  when  $\mu >2$

# Proof:

# See Appendix A.

Lemma 1 can be verified in two special cases. One is that PFL is only executed locally  $\lambda = 0$  .By Lemma 1, we have

$$
\begin{array}{r}\mathbb{E}\left[\| \widetilde{\pmb{\omega}}_n^{t + 1} - \pmb {\omega}_n^*\| ^2\right]\leq \epsilon_{\mathrm{L}}\mathbb{E}\left[\| \widetilde{\pmb{\omega}}_n^t -\pmb {\omega}_n^*\| ^2\right] + \eta_L^2 G, \end{array} \tag{11}
$$

which depends only on the FL local training. The obtained personalized models are obviously unaffected by the DP noise.

Another special case involves no PL, i.e.,  $\lambda = 2$  .Then,  $\overline{\mathbb{C}_n^*} = \arg \min \parallel \overline{\mathbb{C}_n} - \omega^*\parallel^2$  ,i.e.,  $\overline{\mathbb{C}_n^*} = \omega^*$  . Hence,

$$
\begin{array}{rl} & {\mathbb{E}\bigg[\| \widetilde{\pmb{\omega}}_n^{t + 1} - \pmb {\omega}^*\| ^2\bigg]\leq \epsilon_{\mathrm{L}}\mathbb{E}\bigg[\| \widetilde{\pmb{\omega}}_n^t -\pmb {\omega}^*\| ^2\bigg] + 5\eta_L^2 G}\\ & {\quad +\frac{16\eta_L^2 + 8\eta_L}{\mu}\mathbb{E}\bigg[F\big(\widetilde{\pmb{\omega}}^t\big) - F(\pmb {\omega}^*)\bigg]} \end{array} \tag{12a}
$$

$$
< (\epsilon_{\mathrm{L}} + 5\eta_{L}^{2})(\frac{G_{0}}{\mu} +M)^{2} + \frac{16\eta_{L}^{2} + 8\eta_{L}}{\mu}\mathbb{E}\big[F\big(\widetilde{\pmb{\omega}}^{t}\big) - F(\pmb{\omega}^{*})\big], \tag{12b}
$$

where (12b) is obtained by substituting (39) into (12a).As revealed in (12b), the convergence of PL depends only on the FL and hence the DP in PFL.

Lemma 2. Given  $F(\omega) - F(\omega^{*})\leq \frac{1}{2\mu}\| \nabla F(\omega)\|^{2}$  under Assumption 1, the expectation of the difference between the FL model in the t- th communication round, i.e.,  $\widetilde{\omega}^{t + 1}$  and the optimal global model  $\omega^{*}$  is upper bounded by

$$
\begin{array}{r}\mathbb{E}\left[F(\widetilde{\omega}^{t + 1}) - F(\omega^*)\right]\leq \epsilon_{\mathrm{G}}\mathbb{E}\left[F(\widetilde{\omega}^t) - F(\omega^*)\right] + \phi_{\mathrm{L}}T, \end{array} \tag{13}
$$

where  $\begin{array}{r}\phi_{\mathrm{L}} = \sigma_z^2\frac{dL}{2TN^2} = \frac{\Delta s^2Ld\ln(1 / \delta)}{N^2\epsilon^2} \end{array}$  based on (8).

Proof:

# See Appendix B.

Theorem 1. Under Assumption 1, the convergence upper bound of  $PL$  after  $T$  aggregation rounds is given as follows:

$$
\begin{array}{rl} & {\mathbb{E}\left[\| \widetilde{\pmb{\omega}}_n^T -\pmb {\omega}_n^*\| ^2\right]\leq \epsilon_{\mathrm{L}}^T\Psi_2 + (1 + \lambda^2)\eta_L^2 G\frac{\epsilon_{\mathrm{L}}^T - 1}{\epsilon_{\mathrm{L}} - 1}}\\ & {+\frac{(4\eta_L^2 + 2\eta_L)\lambda^2}{\mu}\Big[\frac{\epsilon_{\mathrm{L}}^T - \epsilon_{\mathrm{G}}^T}{\epsilon_{\mathrm{L}} - \epsilon_{\mathrm{G}}}\Psi_1 + \Big(\frac{\epsilon_{\mathrm{L}}^{T - 1} - \epsilon_{\mathrm{G}}}{\epsilon_{\mathrm{G}} - 1} -\frac{\epsilon_{\mathrm{L}}^{T - 1} - 1}{\epsilon_{\mathrm{L}} - 1}\Big)\frac{\phi_{\mathrm{L}}T}{\epsilon_{\mathrm{G}} - 1}\Big];} \end{array} \tag{14}
$$

When  $\epsilon_{\mathrm{L}} = \epsilon_{\mathrm{G}}$

$$
\begin{array}{rl} & {\mathbb{E}\left[\| \widetilde{\pmb{\omega}}_n^T -\pmb {\omega}_n^*\| ^2\right]\leq \epsilon_{\mathrm{L}}^T\Psi_2 + (1 + \lambda^2)\eta_L^2 G\frac{\epsilon_{\mathrm{L}}^T - 2}{\epsilon_{\mathrm{L}} - 1}}\\ & {+\frac{(4\eta_{\mathrm{L}}^2 + 2\eta_{\mathrm{L}})\lambda^2}{\mu}\Big[\epsilon_{\mathrm{L}}^{T - 1}\Psi_1 + \Big((T - 1)\epsilon_{\mathrm{L}}^{T - 1} - \frac{\epsilon_{\mathrm{L}}^{T - 1} - 1}{\epsilon_{\mathrm{L}} - 1}\Big)\frac{\phi_{\mathrm{L}}T}{\epsilon_{\mathrm{L}} - 1}\Big],} \end{array} \tag{15}
$$

where, for conciseness,  $\Psi_{2} = \| \pmb{\pi}_{n}^{0} - \pmb{\pi}_{n}^{*}\|^{2}$

# Proof:

# See Appendix C.

A trade- off between convergence and privacy is revealed in (14) and (15): As the DP noise variance  $\sigma_z^2$  increases, the convergence upper bound of PL increases since the term resulting from the DP noise is positive since  $\phi_{\mathrm{L}} > 0$  and  $\begin{array}{r}\sum_{x = 0}^{t - 1}\sum_{y = 0}^{t - 1 - x}\epsilon_{\mathrm{L}}^{x}\epsilon_{\mathrm{G}}^{y} > 0 \end{array}$  in the bound. Moreover, the convergence upper bound of PL depends on  $\lambda$  .A larger  $\lambda$  leads to a more significant impact of the DP noise on the convergence of PL when  $\mu \geq 2$  .The reason is that, when  $\mu >2$ $\epsilon_{\mathrm{L}} = 1 + \eta_{\mathrm{L}} - \eta_{\mathrm{L}}\left[\mu +\left(1 - \frac{\mu}{2}\right)\lambda \right]$  increases with  $\lambda$  (see Lemma 1); when  $\mu = 2$ $\epsilon_{\mathrm{L}}$  is independent of  $\lambda$  ,but the impact of DP still grows with  $\lambda$  due to the coefficient  $\lambda^2$  in (43). Note that Theorem 1 holds under DP noises with other distributions, since the DP noise has no impact on Lemma 1 while the RHS of (13) in Lemma 2 depends only on the mean and variance  $\sigma_z^2$  of the DP noise.

On the other hand, the impact of DP on the convergence of PL may not always intensify with  $\lambda$  when  $\mu < 2$  , because the sign of the first derivative of  $\begin{array}{r}\lambda^2\left(\frac{\epsilon_{\mathrm{L}}^t - \epsilon_{\mathrm{G}}^t}{\epsilon_{\mathrm{G}}^t - 1} - \frac{\epsilon_{\mathrm{L}}^t - 1}{\epsilon_{\mathrm{L}} - 1}\right) \end{array}$  in (43b) and  $\begin{array}{r}\lambda^2\left(t\epsilon_{\mathrm{L}}^t - \frac{\epsilon_{\mathrm{L}}^t - 1}{\epsilon_{\mathrm{L}} - 1}\right) \end{array}$  in (44b) with respect to  $\lambda$  depends on  $\eta_{L},\epsilon_{G}$  ,and  $t$  when  $\mu < 2$

# B. Optimization for Convergence

Let  $h(T,\lambda)$  denote the convergence upper bound of PL after the  $T$  aggregation rounds of FL, given the privacy budget.

# 1) When  $\epsilon_{\mathrm{L}}\neq \epsilon_{\mathrm{G}}$

$h(T,\lambda)$  is given by the right- hand side (RHS) of (14). After reorganization, we have

$$
\begin{array}{r l} & {h(T,\lambda) = \big(\Psi_{2} + \frac{\beta\Psi_{1}}{\epsilon_{\mathrm{L}} - \epsilon_{\mathrm{G}}} -\eta_{\mathrm{L}}^{2}\left(1 + \lambda^{2}\right)\frac{G}{1 - \epsilon_{\mathrm{L}}} -\frac{\beta\phi_{\mathrm{L}}}{\left(1 - \epsilon_{\mathrm{L}}\right)\left(\epsilon_{\mathrm{L}} - \epsilon_{\mathrm{G}}\right)} T\big)\epsilon_{\mathrm{L}}^{T} + }\\ & {\qquad \big(-\frac{\beta\Psi_{1}}{\epsilon_{\mathrm{L}} - \epsilon_{\mathrm{G}}} +\frac{\beta\phi_{\mathrm{L}}}{\left(1 - \epsilon_{\mathrm{G}}\right)\left(\epsilon_{\mathrm{L}} - \epsilon_{\mathrm{G}}\right)} T\big)\epsilon_{\mathrm{G}}^{T} + }\\ & {\qquad \frac{\beta\phi_{\mathrm{L}}}{\left(1 - \epsilon_{\mathrm{G}}\right)\left(1 - \epsilon_{\mathrm{L}}\right)} T + \eta_{\mathrm{L}}^{2}\left(1 + \lambda^{2}\right)\frac{G}{1 - \epsilon_{\mathrm{L}}}} \end{array} \tag{16a}
$$

$$
\begin{array}{r l} & {\quad = (H_{1} + H_{2}T)\epsilon_{\mathrm{L}}^{T} + (H_{3} + H_{4}T)\epsilon_{\mathrm{G}}^{T} + H_{5}T + H_{6},}\\ & {\mathrm{where~}\beta = \frac{4\eta_{\mathrm{L}}^{2}\lambda^{2} + 2\eta_{\mathrm{L}}\lambda^{2}}{\mu},H_{1} = \Psi_{2} + \frac{\beta\Psi_{1}}{\epsilon_{\mathrm{L}} - \epsilon_{\mathrm{G}}} -\eta_{\mathrm{L}}^{2}(1 + \lambda^{2})\frac{G}{1 - \epsilon_{\mathrm{L}}},}\\ & {H_{2} = -\frac{\beta\phi_{\mathrm{L}}}{(1 - \epsilon_{\mathrm{L}})(\epsilon_{\mathrm{L}} - \epsilon_{\mathrm{G}})},H_{3} = -\frac{\beta\Psi_{1}}{\epsilon_{\mathrm{L}} - \epsilon_{\mathrm{G}}},H_{4} = \frac{\beta\phi_{\mathrm{L}}}{(1 - \epsilon_{\mathrm{G}})(\epsilon_{\mathrm{L}} - \epsilon_{\mathrm{G}})},}\\ & {H_{5} = \frac{\beta\phi_{\mathrm{L}}}{(1 - \epsilon_{\mathrm{G}})(1 - \epsilon_{\mathrm{L}})} >0,\mathrm{and}H_{6} = \eta_{\mathrm{L}}^{2}(1 + \lambda^{2})\frac{G}{1 - \epsilon_{\mathrm{L}}} >0.} \end{array} \tag{16b}
$$

Let  $H_{0}$  denote the lower bound of  $(H_{1} + H_{2}T)\epsilon_{\mathrm{L}}^{T} + (H_{3} + H_{4}T)\epsilon_{\mathrm{G}}^{T} + H_{6}$  in (16b). Then,  $h(T,\lambda)$  has a linear lower bound, denoted by  $h_{\mathrm{Low}}(T)$ ; that is

$$
h(T,\lambda)\geq h_{\mathrm{Low}}(T) = H_0 + H_5T, \tag{17}
$$

with  $(H_{1} + H_{2}T)\epsilon_{\mathrm{L}}^{T} + (H_{3} + H_{4}T)\epsilon_{\mathrm{G}}^{T} + H_{6}\geq H_{0}$

1) When  $\epsilon_{\mathrm{L}} > \epsilon_{\mathrm{G}}$ $H_{2}< 0$  . Then,  $H_{2}T\epsilon_{\mathrm{L}}^{T}$  first decreases and then increases in  $T$  . There exists  $\min_{T}(H_{2}T\epsilon_{\mathrm{Low}}^{T})$  Also,  $H_{3}< 0$ $H_{3}\epsilon_{\mathrm{G}}^{T}\geq H_{3}$  since  $H_{3}\epsilon_{\mathrm{G}}^{T}$  is an increasing function of  $T$ $H_{4} > 0$  ; thus,  $H_{4}T\epsilon_{\mathrm{G}}^{T}\geq 0$  . Considering two possible cases concerning  $H_{1}$ $H_{0}$  is given by

a) If  $H_{1}\geq 0$  , then  $H_{0} = \min_{T}(H_{2}T\epsilon_{\mathrm{Low}}^{T}) + H_{3} + H_{6}$  
b) If  $H_{1}< 0$  , then  $H_{0} = H_{1} + \min_{T}(H_{2}T\epsilon_{\mathrm{Low}}^{T}) + H_{3}+$ $H_{6}$

2) When  $\epsilon_{\mathrm{L}}< \epsilon_{\mathrm{G}}$ $H_{2} > 0$  . Hence,  $H_{2}T\epsilon_{\mathrm{L}}^{T}\geq 0$  .Also,  $H_{3} > 0$  and hence  $H_{3}\epsilon_{\mathrm{G}}^{T} > 0$  . Moreover,  $H_{4}< 0$  and thus  $H_{4}T\epsilon_{\mathrm{G}}^{T}$  first decreases and then increases with respect to  $T$  , with the existence of  $\min_{T}(H_{4}T\epsilon_{\mathrm{Low}}^{T})$  confirmed. Considering two possible cases concerning  $H_{1}$ $H_{0}$  can be given by a) If  $H_{1}\geq 0$  , then  $H_{0} = \min_{T}(H_{4}T\epsilon_{\mathrm{Low}}^{T}) + H_{6}$  b) If  $H_{1}< 0$  , then  $H_{0} = H_{1} + \min_{T}(H_{4}T\epsilon_{\mathrm{Low}}^{T}) + H_{6}$

As illustrated in Fig. 2, given  $\lambda$  the optimal  $T$  denoted by  $T^{*}$  , which minimizes  $h(T,\lambda)$  , is within  $(0,T^{\prime})$  , where  $T^{\prime}$  satisfies  $h_{\mathrm{Low}}(T') = h(0,\lambda)$  , i.e.,  $\begin{array}{r}T^{\prime} = \frac{H_{1} + H_{3} + H_{6} - H_{0}}{H_{5}} \end{array}$  . This is because  $h(T,\lambda) > h_{\mathrm{Low}}(T') = h(0,\lambda)\geq \bar{h} (T^*,\lambda)$ $\forall T>$ $T^{\prime}$  . As a result,  $T^{*}$  can be obtained effectively using a one- dimensional search with a step size of 1 within  $(0,T^{\prime})$

2) When  $\epsilon_{\mathrm{L}} = \epsilon_{\mathrm{G}}$

$h(T,\lambda)$  is given by the RHS of (15):

$$
\begin{array}{r l} & {h(T,\lambda) = \Big(-\frac{(1 + \lambda^{2})\eta_{\mathrm{L}}^{2}G}{1 - \epsilon_{\mathrm{L}}} +\Psi_{2} + \frac{(4\eta_{\mathrm{L}}^{2} + 2\eta_{\mathrm{L}})\lambda^{2}}{\epsilon_{\mathrm{L}}\mu}.}\\ & {\qquad \frac{\Psi_{1}(1 - \epsilon_{\mathrm{L}})^{2} - \epsilon_{\mathrm{L}}\phi_{\mathrm{L}}}{(1 - \epsilon_{\mathrm{L}})^{2}} T - \frac{(4\eta_{\mathrm{L}}^{2} + 2\eta_{\mathrm{L}})\lambda^{2}\phi_{\mathrm{L}}}{\epsilon_{\mathrm{L}}\mu(1 - \epsilon_{\mathrm{L}})} T^{2}\Big)\epsilon_{\mathrm{L}}^{T}}\\ & {\qquad -\frac{(1 + \lambda^{2})\eta_{\mathrm{L}}^{2}G}{1 - \epsilon_{\mathrm{L}}} -\frac{(4\eta_{\mathrm{L}}^{2} + 2\eta_{\mathrm{L}})\lambda^{2}}{\mu(1 - \epsilon_{\mathrm{L}})}}\\ & {\qquad = (\mathcal{H}_{1} + \mathcal{H}_{2}T + \mathcal{H}_{3}T^{2})\epsilon_{\mathrm{L}}^{T} + \mathcal{H}_{4},\qquad (18)} \end{array} \tag{18a}
$$

where, for the brevity of notation,  $\begin{array}{r}\mathcal{H}_1 = - \frac{(1 + \lambda^2)\eta_{\mathrm{L}}^2G}{1 - \epsilon_{\mathrm{L}}} +\Psi_2, \end{array}$ $\begin{array}{r}\mathcal{H}_2 = \frac{(4\eta_{\mathrm{L}}^2 + 2\eta_{\mathrm{L}})\lambda^2}{\epsilon_{\mathrm{L}}\mu} (\Psi_1 - \frac{\epsilon_{\mathrm{L}}\phi_{\mathrm{L}}}{(1 - \epsilon_{\mathrm{L}})}),\mathcal{H}_3 = - \frac{(4\eta_{\mathrm{L}}^2 + 2\eta_{\mathrm{L}})\lambda^2\phi_{\mathrm{L}}}{\epsilon_{\mathrm{L}}\mu(1 - \epsilon_{\mathrm{L}})} < \end{array}$  0, and  $\begin{array}{r}\mathcal{H}_4 = - \frac{(1 + \lambda^2)\eta_{\mathrm{L}}^2G}{1 - \epsilon_{\mathrm{L}}} - \frac{(4\eta_{\mathrm{L}}^2 + 2\eta_{\mathrm{L}})\lambda^2}{\mu(1 - \epsilon_{\mathrm{L}})} < 0. \end{array}$

Let  $\mathcal{H}_0$  denote the lower bound of  $(\mathcal{H}_1 + \mathcal{H}_2T)\epsilon_{\mathrm{L}}^T +\mathcal{H}_4$  in (18b). Then, the lower bound of  $h(T,\lambda)$  is given by

$$
h(T,\lambda)\geq h_{\mathrm{Low}}(T) = \mathcal{H}_0 + \mathcal{H}_3T^2\epsilon_{\mathrm{L}}^T, \tag{19}
$$

![](images/7ee49b907f0b5d6160466ac97bb93381079f15f122cf1d4a05fa3edaead61385.jpg)  
FIGURE 2. Comparison between  $h(T,\lambda)$  and its lower bound  $h_{\mathrm{Low}}(T)$  where the simulation parameters are as specified for the MLR model trained on the MNIST dataset, i.e.,  $\lambda = 0.1$ ,  $\epsilon = 100$ , and  $\delta = 0.01$ ; see Section VI-B.

Since  $\mathcal{H}_3< 0$ $\mathcal{H}_3T^2\epsilon_{\mathrm{L}}^T$  first decreases and then increases.

1) When  $\mathcal{H}_2< 0$ $\mathcal{H}_2T\epsilon_{\mathrm{L}}^T$  first decreases and then increases with respect to  $T$  . Then,  $\min_{T}(\mathcal{H}_2T\epsilon_{\mathrm{Low}}^T)$  exists. Considering two possible cases concerning  $\mathcal{H}_1$ $\mathcal{H}_0$  is given by a) If  $\mathcal{H}_1\geq 0$  , then  $\mathcal{H}_0 = \min_{T}(h_2T\epsilon_{\mathrm{Low}}^T) + \mathcal{H}_4$  b) If  $\mathcal{H}_1< 0$  , then  $\mathcal{H}_0 = \mathcal{H}_1 + \min_{T}(\mathcal{H}_2T\epsilon_{\mathrm{Low}}^T) + \mathcal{H}_4$  
2) When  $\mathcal{H}_2\geq 0$ $\mathcal{H}_2T\epsilon_{\mathrm{L}}^T\geq 0$  .Considering the two possible cases concerning  $\mathcal{H}_1$ $\mathcal{H}_0$  is given by a) If  $\mathcal{H}_1\geq 0$  , then  $\mathcal{H}_0 = \mathcal{H}_4$  b) If  $\mathcal{H}_1< 0$  , then  $\mathcal{H}_0 = \mathcal{H}_1 + \mathcal{H}_4$

Similarly, the optimal  $T^{*}$  under a given  $\lambda$  can be obtained using a one- dimensional search with a step size of 1 within  $(0,T^{\prime \prime})$  , where  $T^{\prime \prime}$  satisfies  $h_{\mathrm{Low}}(T^{\prime \prime}) = h(0,\lambda)$

In both cases, clearly, the convergence of PL depends heavily on that of FL and, in turn, the DP in PFL.

# V. Fairness Analysis of Privacy-Preserving PFL

In this section, we analyze the fairness of PL under privacy constraints and uncover an opportunity to maximize the fairness by optimizing  $\lambda$ . We focus on the fairness of performance distribution to measure the degree of uniformity in performance across the clients. Several fairness metrics have been adopted in FL, including distance (e.g., cosine distance [44] and Euclidean distance [45]), variance [1], risk difference (e.g., demographic parity [46] and equal opportunity [47]), and Jain's fairness index (JFI) [48]. Among these metrics, variance and JFI are suitable for measuring the fairness of performance distribution for FL/PFL. This is because distance metrics are suitable for the contribution fairness of participants, while risk difference metrics are adequate for group fairness to eliminate prejudice toward some specific groups. While variance and JFI are typically negatively correlated, variance is more sensitive to outliers. For this reason, we adopt variance as the metric of fairness to encourage uniformly distributed performance across the

PL models. The definition of fairness measured by variance is provided as follows.

Definition 1 (Fairness [1]). For a group of personalized models  $\{\pi_n\}_{n\in \mathbb{N}}$  the performance distribution fairness is measured by

$$
\rho (\pi_n) = \operatorname {var}_N\left[F_n(\pi_n(\lambda))\right], \tag{20}
$$

where  $\operatorname {var}_N\left[F_n(\pi_n(\lambda))\right]$  is the variance across the local training losses of the personalized models of all clients. A set of models  $\{\pi_n'\}_{n\in \mathbb{N}}$  is fairer than another set  $\{\pi_n\}_{n\in \mathbb{N}}$  if  $\rho^{\prime} = \operatorname {var}_N\{F_n(\pi_n^{\prime})\}_{n\in \mathbb{N}}< \rho$  .The optimal  $\lambda$  denoted by  $\lambda^{*}$  is defined as

$$
\lambda^{*} = \underset {\lambda}{\arg \min}\mathbb{E}\left\{\rho (\pi_{n}^{*}(\lambda))\right\} , \tag{21}
$$

where, given  $\lambda$ $\pi_n^*\left(\lambda\right)$  is client  $n$  's optimal PL model.

# A. Personalized Bayesian Linear Regression With DP

It is generally different to analyze the performance fairness of PFL, since the performance, e.g., loss, of PFL is typically analytically unavailable and can only be empirically obtained a- posteriori after PFL training. With reference to Ditto [1], we consider that each client trains a personalized Bayesian linear regression model to shed useful light on the fairness of general ML models. Bayesian linear regression models treat regression coefficients and the disturbance variance as random variables [49]. We set the optimal FL global model  $\omega^{*}$  as the non- information prior on  $\mathbb{R}^d$  , i.e., uniformly distributed on  $\mathbb{R}^d$

Suppose the optimal FL local model  $\pmb {u}_n^*$  of client  $n$  is distributed around the optimal FL global model  $\omega^{*}$  ..

$$
\pmb {u}_n^* = \pmb {\omega}^* +\pmb {\tau}_n, \tag{22}
$$

where  $\tau_{n}\sim \mathcal{N}(0,\zeta^{2}\mathbf{I}_{d})$ $\forall n$  are i.i.d. random variables, and  $\mathbf{I}_d$  is the  $d\times d$  identity matrix. The local data of client  $n$  satisfies

$$
\mathbf{Y}_n = \mathbf{X}_n\pmb {u}_n^* +\pmb {\nu}_n. \tag{23}
$$

Here,  $\mathbf{X}_n\in \mathbb{R}^{b\times d}$  and  $\mathbf{Y}_n\in \mathbb{R}^b$  denote  $b$  samples of client  $n$  , and  $\nu_{n}\in \mathbb{R}^{b}$  under the assumption of  $\nu_{n}\sim \mathcal{N}(0,\sigma^{2}\mathbf{I}_{b})$

Given the samples  $(\mathbf{X}_n,\mathbf{Y}_n)$  the loss function of the Bayesian linear regression problem at client  $n$  is written as

$$
F_{n}(\pmb{u}_{n}) = \frac{1}{b}\| \mathbf{X}_{n}\pmb{u}_{n} - \mathbf{Y}_{n}\|^{2}. \tag{24}
$$

By minimizing (24), the estimate of  $\pmb {u}_n^*$  is [1, Eq. (12)]

$$
\hat{\pmb{u}}_n = (\mathbf{X}_n^\mathsf{T}\mathbf{X}_n)^{-1}\mathbf{X}_n^\mathsf{T}\mathbf{Y}_n. \tag{25}
$$

By plugging (24) into (3b), the optimal global model is

$$
\begin{array}{c}{\omega^{*} = \arg \min_{\omega}\frac{1}{Nb}\sum_{n = 1}^{N}\| \mathbf{X}_{n}\omega -\mathbf{Y}_{n}\|^{2}}\\ {= \sum_{n = 1}^{N}\left(\mathbf{X}^{\mathsf{T}}\mathbf{X}\right)^{-1}\mathbf{X}_{n}^{\mathsf{T}}\mathbf{X}_{n}\hat{\pmb{u}}_{n},} \end{array} \tag{26b}
$$

where  $\mathbf{X}\in \mathbb{R}^{Nb\times d}$  collects  $Nb$  samples of all clients, i.e.,  $\mathbf{X} = (\mathbf{X}_1^\top \mathbf{X}_2^\top \dots \mathbf{X}_N^\top)^\top$

With DP, the server receives the perturbed version of the estimated local model, denoted by  $\tilde{\pmb{u}}_n$  , as given by

$$
\widetilde{\pmb{u}}_n = \hat{\pmb{u}}_n + \mathbf{z}_n = (\mathbf{X}_n^\mathsf{T}\mathbf{X}_n)^{-1}\mathbf{X}_n^\mathsf{T}\mathbf{Y}_n + \mathbf{z}_n. \tag{27}
$$

By replacing  $\hat{\pmb{u}}_n$  with  $\tilde{\pmb{u}}_n$  , the DP perturbed version of the optimal global model, denoted by  $\tilde{\omega}^{*}$  , is given by

$$
\tilde{\omega}^{*} = \sum_{n = 1}^{N}(\mathbf{X}^{\mathsf{T}}\mathbf{X})^{-1}\mathbf{X}_{n}^{\mathsf{T}}\mathbf{X}_{n}\tilde{\pmb{u}}_{n}, \tag{28}
$$

Lemma 3. With  $\mathbf{X}_n^\mathsf{T}\mathbf{X}_n = \rho \mathbf{I}_d$ $\forall n\in \mathbb{N}$  under independently sampled or generated data samples [1], the optimal  $PL$  model with DP,  $\tilde{\pi_n^*} (\lambda)$  and the optimal  $FL$  local model,  $\pmb {u}_n^*$  in (22), can be written as

$$
\begin{array}{r l} & {\widetilde{\pmb{\omega}}_{n}^{*}(\lambda) = \frac{b}{(2 - \lambda)\rho + b\lambda}((\frac{(2 - \lambda)\rho}{b} +\frac{\lambda}{N})\hat{\pmb{u}}_{n}}\\ & {\qquad \left. + \frac{\lambda}{N}\sum_{m\in \mathbb{N},m\neq n}\hat{\pmb{u}}_{m} + \frac{\lambda}{N}\sum_{n = 1}^{N}\pmb{z}_{n});}\\ & {\qquad \left.\hat{\pmb{u}}_{n}^{*} = \frac{\sigma_{w}^{2}\rho}{\sigma^{2}}\hat{\pmb{u}}_{n} + \frac{\sigma_{w}^{2}\rho}{\sigma^{2} + N\zeta^{2}\rho}\sum_{m\in \mathbb{N},m\neq n}\hat{\pmb{u}}_{m} + \pmb{\theta}_{n},} \end{array} \tag{29}
$$

where  $\begin{array}{r}\pmb {\theta}_n\sim \mathcal{N}(0,\sigma_w^2\mathbf{I}_d) \end{array}$  and  $\begin{array}{r}\sigma_w^2 = \left(\frac{N - 1}{\frac{\sigma^2}{\rho} + N\zeta^2} +\frac{\rho}{\sigma^2}\right)^{- 1}. \end{array}$

Proof:

# See Appendix D.

We note that in Ditto, the optimal personalized models  $\pi_n^*\left(\lambda\right)$ $\forall n\in \mathbb{N}$  are deterministic, provided the estimated local models  $\hat{\pmb{u}}_n$ $(\forall n\in \mathbb{N})$  and  $\lambda$  By contrast, in DP- Ditto, the PL models  $\tilde{\pi_n^*} (\lambda)$ $\forall n\in \mathbb{N}$  are not deterministic. This is because the DP noise,  $\mathbf{z}_n$  , is added on the estimated local models  $\hat{\pmb{u}}_n$  for the FL global model updating and, consequently, it is coupled with  $\lambda$  in  $\tilde{\pi_n^*} (\lambda)$  see (29). Considering the difference between the optimal FL local model  $\pmb {u}_n^*$  in (30) and its estimate  $\hat{\pmb{u}}_n$  is random and captured by  $\hat{\pmb{v}}_n$  , we have to analyze the joint distribution of the two random variables  $\mathbf{z}_n$  in (29) and  $\hat{\pmb{v}}_n$  in (30),  $\forall n\in \mathbb{N}$  ,and obtain the fairness expression with respect to  $\lambda$

Given the optimal FL local model without DP,  $\pmb {u}_n^*$  , and the optimal PL model with DP,  $\tilde{\pi_n^*} (\lambda)$  , the fairness  $\rho (\pi_n^* (\lambda))$  of the PL models among all clients is established, as follows.

Theorem 2. Given  $\lambda$  and the variance  $\sigma_z^2$  of the DP noise, the fairness of the personalized Bayesian linear regression model,  $R(\lambda)$  can be measured by

$$
\begin{array}{r}R(\lambda) = 2d\bigg[\sigma_w^2 +[\alpha_0(\lambda)]^2\frac{\sigma_z^2}{N^2}\bigg] + 4\bigg[\sigma_w^2 +[\alpha_0(\lambda)]^2\frac{\sigma_z^2}{N^2}\bigg]\times \\ (S_1 - S_2\alpha_0(\lambda))^2 G_1 + [S_1 - S_2\alpha_0(\lambda)]^4 (G_2 - G_1^2), \end{array} \tag{31}
$$

where  $\begin{array}{r}\alpha_0(\lambda) = \frac{b\lambda}{(2 - \lambda)\rho + b\lambda} \end{array}$ $\begin{array}{r}S_{1} = \frac{\sigma^{2}}{N(\sigma^{2} + \rho\zeta^{2})} \end{array}$ $\begin{array}{r}S_{2} = \frac{1}{N} \end{array}$ $G_{1} =$ $\begin{array}{r}\sum_{l = 1}^{d}\frac{1}{N}\sum_{n = 1}^{N}\left[\alpha_{nl}^{2}\right] \end{array}$  and  $\begin{array}{r}G_{2} = \frac{1}{N}\sum_{n = 1}^{N}\left[\left(\sum_{l = 1}^{d}\alpha_{nl}^{2}\right)^{2}\right]. \end{array}$

# Proof:

# See Appendix E.

See Appendix E.According to Theorem 2, DP degrades the fairness of PL, as  $R(\lambda)$  increases with  $\sigma_z^2$ . On the other hand, the dependence of fairness on  $\lambda$  is much more complex, which is different from Ditto. As will be revealed later, given  $\sigma_z^2$ , fairness depends on not only  $\lambda$  but also the model clipping threshold  $C$ . The uniqueness of the optimal  $\lambda$ , i.e.,  $\lambda^*$ , can be ascertained when  $C$  is sufficiently small. Note that Theorem 2 holds under DP noises with other distributions, since the fairness  $R(\lambda)$  depends only on the mean and variance of the DP noises, according to Definition 1.

# B. Convergence-Privacy-Fairness Trade-off

We analyze the existence of the optimal  $\lambda^{*}$  and  $T^{*}$  to balance the trade- off between the convergence, privacy, and fairness of DP- Ditto. For conciseness, we rewrite  $\alpha_0(\lambda)$  and  $R(\lambda)$  as  $\alpha_0$  and  $R$  , respectively.

Theorem 3. Given the  $DP$  noise variance  $\sigma_z^2$  the optimal  $\lambda^{*}$  which maximizes  $R(\lambda)$  exists and is unique when the model clipping threshold  $\begin{array}{r}C< \frac{\sqrt{d}}{2NS_1} \end{array}$ $\lambda^{*}\in [0,2]$  satisfies

$$
\begin{array}{c}{4d\frac{\sigma_z^2}{N^2}\alpha_0^* +8G_1\frac{\sigma_z^2}{N^2} (S_1 - S_2\alpha_0^*)^2\alpha_0^* -8S_2G_1\big(\sigma_w^2 +[\alpha_0^* ]^2\frac{\sigma_z^2}{N^2}\big)\cdot}\\ {(S_1 - S_2\alpha_0^*) - 4S_2(G_2 - G_1^2)(S_1 - S_2\alpha_0^*)^3 = 0,\quad (32)} \end{array} \tag{32}
$$

where  $\begin{array}{r}\alpha_0^* = \frac{b\lambda^*}{(2 - \lambda^*)\rho + b\lambda^*} \end{array}$

# Proof:

# See Appendix F

With the privacy consideration, we jointly optimize  $\lambda$  and  $T$  to improve the trade- off between the convergence, privacy, and fairness of DP- Ditto. From (16) and (32),  $\lambda^{*}$  and  $T^{*}$  satisfy

$$
\min_{\lambda ,T}h(T,\lambda),\quad s.t. (32), \tag{33}
$$

which can be solved through an iterative search. For  $T$  a one- dimensional search can be carried out. Given the aggregation round number  $T$  32) can be solved analytically, e.g., using the Cardano method [50]. The optimal  $\lambda^{*}$  depends on  $\sigma_z^2$

Corollary 1. The optimal  $\lambda^{*}$  which minimizes the fairness measure  $R(\lambda)$  decreases as the  $DP$  noise variance  $\sigma_z^2$  increases (i.e., the privacy budget  $\epsilon$  decreases).

# Proof:

# See Appendix G.

For a given  $T$  up to three feasible solutions to  $\lambda$  can be obtained by solving (32), as (32) is a three- order polynomial equation. As revealed in Theorem 3, one of the three solutions is within  $[0,2]$  .By comparing  $h(T,\lambda)$  among all the obtained  $(T,\lambda)$  pairs, the optimal  $(T^{*},\lambda^{*})$  can be achieved and the existence of  $(T^{*},\lambda^{*})$  is guaranteed. The complexity of this iterative search is determined by the one- dimensional search for  $T$  and the Cardano method for solving 32) under each given  $T$  . The worst- case complexity of the onedimensional search with a step size of 1 is  $\mathcal{O}(T_{\mathrm{max}})$  , where  $T_{\mathrm{max}}$  is the maximum number of communication rounds permitted. Being an analytical method, the Cardano method provides closed- form solutions and incurs a complexity of  $\mathcal{O}(1)$  [51]. As a result, the overall complexity of the iterative search is  $\mathcal{O}(T_{\mathrm{max}})$

In a more general case, the ML model is not linear, and  $\lambda$  cannot be analytically solved since there is no explicit analytical expression of  $\lambda$  .Different  $\lambda$  values can be tested. Per  $\lambda$  the corresponding optimal  $T$  can be obtained via a onedimensional search. Given the optimal  $T$  , the corresponding optimal  $\lambda$  can be obtained by testing different  $\lambda$  values (e.g., one- dimensional search for  $\lambda$  ).We can restart the search for the optimal  $T$  corresponding to the optimal  $\lambda$  so on and so forth, until convergence (i.e., the optimal  $T^{*}$  and  $\lambda^{*}$  stop changing), as done experimentally in Section VI.

# VI. Experiments and Results

In this section, we assess the trade- off between the convergence, accuracy, and fairness of DP- Ditto experimentally. The impact of privacy considerations on those aspects of DP- Ditto is discussed. We set  $N = 20$  clients by default. The clipping threshold is  $C = 20$  and the privacy budget is  $\delta = 0.01$  [9]. We consider three network models, i.e., MLR, DNN, and CNN.

MLR: This classification method generalizes logistic regression to multiclass problems. It constructs a linear predictor function to predict the probability of an outcome based on an input observation. DNN: This model consists of an input layer, a fully connected hidden layer (with 100 neurons), and an output layer. The rectified linear unit (ReLU) activation function is applied to the hidden layer. CNN: This model contains two convolutional layers with 32 and 64 convolutional filters per layer, and a pooling layer between the two convolutional layers to prevent over- fitting. Following the convolutional layers are two fully connected layers. We use the ReLU in the convolutional and fully connected layers.

The learning rates of FL and PL are  $\eta_{\mathrm{G}} = 0.005$  and  $\eta_{\mathrm{L}} =$  0.005, respectively.

We consider four widely used public datasets, i.e., MNIST, Fashion- MNIST (FMNIST), and CIFAR10. Cross- entropy loss is considered for the datasets. Apart from Ditto [1], the following benchmarks are considered:

pFedMe [22]: The global FL model is updated in the same way as the typical FL Learning from the global model, each personalized model is updated based on a regularized loss function using the Moreau envelope. APPLE [25]: Each client uploads to the server a core model learned from its personalized model and downloads the other clients' core models in each round. The

![](images/66483149d64c8690c842801061633a09a0a5c8c44ce9c50f00856e94abfd947e.jpg)  
FIGURE 3. Comparison of testing accuracy and fairness between the benchmarks with DP and DP-Ditto under the optimal  $\lambda^{*} = 0.005$ ,  $\epsilon = 10$ , and  $\delta = 0.01$ .  
FIGURE 4. Comparison of testing accuracy and fairness with the benchmarks with DP and DP-Ditto under the optimal  $\lambda^{*} = 0.01$ ,  $\epsilon = 100$ , and  $\delta = 0.01$ .

personalized model is obtained by locally aggregating the core models with learnable weights.

FedAMP [24]: The server has a personalized cloud model. Each client has a local personalized model. In each round, the server updates the personalized cloud models using an attention- inducing function of the uploaded local models and combination weights. Upon receiving the cloud model, each client locally updates its personalized model based on a regularized loss function. FedALA [26]: In every round of FedALA, each client adaptively initializes its local model by aggregating the downloaded global model and the old local model with learned aggregation weights before local training.

# 1) Comparison With the State of the Art

We compare the accuracy and fairness between the proposed DP- Ditto and the benchmarks, i.e., FedAMP [24], pFedMe [22], APPLE [25], and FedALA [26], where DP noises are added to perturb the local models in the benchmarks under different  $\epsilon$  values (i.e.,  $\epsilon = 10,100$ ) and datasets (i.e., MNIST, FMNIST, and CIFAR10).  $\delta = 0.01$  .The DNN model is considered on the MNIST dataset. The CNN model is considered on the FMNIST and CIFAR10 datasets.

Figs. 3 and 4 plot the testing accuracy and fairness of privacy- preserving PFL with the growth of  $T$  where  $\epsilon = 10$  and 100, respectively. DP- Ditto (with  $\lambda^{*} = 0.005$  under  $\epsilon = 10$  or  $\lambda^{*} = 0.01$  under  $\epsilon = 100$ ) provides the best accuracy and fairness compared to the privacy- enhanced benchmarks (i.e., FedAMP, pFedMe, APPLE, and FedALA), since  $\lambda^{*}$  is adapted to the DP perturbation in DP- Ditto. We note that the PL models are obtained by aggregating the downloaded models in APPLE [25] and FedALA [26], or based on a weighted downloaded global model aggregated from the previous personalized models in pFedMe [22] and FedAMP [24], both without considering privacy.

For fair comparisons with DP- Ditto, the PL models of the benchmarks are updated based on the aggregated noisy models perturbed using DP to enhance the privacy aspect of the models. Due to their limited flexibility in balancing personalization and generalization, the benchmarks are highly sensitive to DP noises, resulting in significant performance degradation. By contrast, under DP- Ditto, the impact of DP

![](images/6de38ac5567ec4251ca78db1b6d4cb71a01f0517c2bd7038b5a159eadba52930.jpg)  
FIGURE 5. Training loss of the personalized model with respect to the maximum global aggregation number  $T$  under different  $\epsilon$  values.  $\delta = 0.01$ .

noise can be adjusted by properly configuring the weighting coefficient  $\lambda$  between personalization and generalization, hence alleviating the adverse effect of the DP noise.

By comparing Figs. 3 and 4, we see that the testing accuracy and fairness of DP- Ditto can be maintained even under high privacy requirements (e.g.,  $\epsilon = 10$ ). This is achieved by configuring a smaller  $\lambda^{*}$  when  $\epsilon$  is smaller (i.e.,  $\sigma_{u}^{2}$  is higher), which encourages the PL models to be closer to the local models and hence alleviates the adverse effect of DP noise. By contrast, the benchmarks degrade significantly when  $\epsilon$  is small (e.g.,  $\epsilon = 10$ ) and/or  $T$  is large because of their susceptibility to the DP noises. As  $T$  grows, the testing accuracy decreases under FedALA and FedAMP, or increases when  $T \leq 5$  and then decreases under APPLE. Moreover, the testing accuracy of pFedMe decreases under the CNN model on the FMNIST and CIFAR10 datasets.

# 2) Impact of Privacy Budget

Fig. 5 evaluates the impact of  $\epsilon$  and  $T$  on the convergence of DP- Ditto, where  $\lambda = 0.1$  for the DNN and MLR models on the MNIST dataset, the CNN model on the CIFAR10 dataset, and the CNN model on the FMNIST dataset.  $\epsilon = 1, 10, 20, 100$ , or  $\epsilon = +\infty$  (i.e., Ditto). Figs. 5(a)- 5(d) show that the training loss decreases and eventually approaches the case with no DP (i.e.,  $\epsilon = +\infty$ ), as  $\epsilon$  increases. This is because a larger  $\epsilon$  leads to a smaller variance of the DP noise and, consequently, the server can obtain better quality local models from the clients. On the other hand, a smaller  $\epsilon$  leads to a larger DP noise variance (i.e.,  $\sigma_{u}^{2}$ ) based on (8) and, hence, the accumulated effect of the DP noise would be significant. However, when both  $\epsilon$  and  $T$  are small, the training loss of PL decreases initially due to the benefits of generalization brought from FL. Once  $T$  exceeds a certain critical value, the accumulated impact of the DP noises outweighs the benefits of generalization, leading to a degradation in PL performance. This observation validates the discussion in Section IV.

![](images/7cea644b278e07e54d17c2046dbe288ffd4bddbffdfb4eae7fa393f75d2be4ac.jpg)  
FIGURE 6. Comparison of accuracy and fairness vs. aggregation time  $t$ , under different  $\lambda$  values with  $T = 30$ ,  $\epsilon = 10$ ,  $\delta = 0.01$ .

# 3) Impact of  $T$  and  $\lambda$

Figs. 6 demonstrates the testing accuracy and fairness of DP- Ditto with the growing number  $t$  of aggregations under different  $\lambda$  values and datasets (i.e., MNIST, FMNIST, and CIFAR10). We set  $\epsilon = 10$ ,  $\delta = 0.01$ ,  $T = 30$ ,  $\lambda = 0.1$ , 0.5, and 1.0, as well as two special cases with  $\lambda = 0$  (i.e., local training only with no global aggregation) and  $\lambda = 2$  (i.e., FL with no personalization). Fig. 6(a) shows the model accuracy increases with  $t$  when  $\lambda < 0.1$ , but increases first and then decreases when  $\lambda > 0.1$ . The reason is that the FL global model is most affected by the DP noise through the aggregations of noisy local model parameters. By contrast, the FL local training only depends on the local datasets and is unaffected by the DP noises. Moreover,  $\lambda$  can be controlled to balance the accuracy between the FL global and local models. It adjusts the effect of the DP noises on the PL models.

Fig. 6(b) gauges the fairness of DP- Ditto measured by the standard deviation of the training losses concerning the PL models of all clients. The fairness of the PL models first degrades and then improves, and then degrades again as  $t$  rises. The reason is that the accuracy of the clients' models is poor and, hence, fairness is high initially. As  $t$  rises, the DP noises increasingly affect the PL models. The fairness is better than that of the global FL model after a specific  $t$  because the clients ignore the heterogeneity of their local data when all clients utilize the same global FL model for image classification. The PL model with a smaller  $\lambda$  offers better fairness. When  $\lambda = 0.1$  the PL model offers the best fairness.

It is observed from Figs. 6(a), 6(c), and 6(e) that the testing accuracy first increases and then decreases with the increase of  $t$  under  $\lambda < 2$ . This is due to the fact that the effect of the DP noise accumulates as  $t$  grows, causing performance degradation when  $t$  is excessively large. By contrast, the updates of the local models (i.e., the PL models at  $\lambda = 0$  are unaffected by the DP noises. As shown in Fig. 6(c)- 6(f), under the CNN models on the FMNIST and CIFAR10 datasets, when  $t$  is small, the PL models operate the best in accuracy and fairness at  $\lambda = 1$  and 2; in other words, the PL models are closer to the FL global models. This is because the adverse effect caused by noise accumulation is insignificant when  $t$  is small, and the PL models can benefit from the generalization offered by the FL global model. On the other hand, when  $t$  is large, the PL models perform the best at  $\lambda = 0$ ; i.e., the PL models are closer to the FL local models, due to DP noise accumulation.

We further obtain the optimal  $(T^{*},\lambda^{*})$  for the DNN model on the MNIST dataset, where  $\epsilon = 100$ ,  $\delta = 0.01$  and  $C = 10$  as described in Section V- C. Fig. 7(a) demonstrates the fairness of the PL models with  $\lambda$  under different  $T$  values. Fig. 7(a) shows that the optimal  $\lambda^{*}$  decreases as  $T$  increases. When  $\lambda$  is small (i.e.,  $\lambda \rightarrow 0^{+}$ ), the fairness of the PL models improves with  $T$  as DP- Ditto is dominated by PL with little assistance from FL. Consequently, the DP noise has little impact on the PL models. When  $\lambda$  is large (i.e.,  $\lambda \rightarrow 2$ ), the fairness degrades as  $T$  increases, as priority is given to generalization over personalization. The adverse effect of the DP noise becomes increasingly strong, compromising the fairness of the PL models. Fig. 7(b) demonstrates the training loss of the PL models against  $T$  under different  $\lambda$  values. Given  $T$  the training loss decreases as  $\lambda$  decreases. Moreover, the optimal  $T^{*}$  increases as  $\lambda$  decreases. By comparing Figs. 7(a) and 7(b), the optimal configuration  $(T^{*} = 80$ ,  $\lambda^{*} = 0.1)$  is obtained to minimize the training loss and guarantee the fairness of the PL models.

# VII. Conclusion

We have proposed DP- Ditto, and analyzed its convergence and fairness performance under a given privacy budget. In particular, we have derived the convergence upper bound of the PL models under DP- Ditto and the optimal number of global aggregations. We also analyzed the performance fairness of the PL models and illustrated the feasibility of jointly optimizing the DP- Ditto configuration for convergence and fairness. Extensive experiments have corroborated our analysis and showed that DP- Ditto can outperform its benchmarks significantly by over  $24.3\%$  in fairness and  $48.16\%$  in accuracy. This work contributes to a better understanding of the interplay among the privacy, convergence, and fairness of PFL. In the future, we will analyze fairness for nonlinear PFL models in imperfect wireless environments and design methods to balance the trade- off among the convergence, fairness, and privacy of the models.

![](images/ae33cfb9f89a11fbf7a7925e72ddada2c88755969655feba4e3a88921e9f615d.jpg)  
FIGURE 7. Fairness and training loss of PL vs.  $\lambda$  and  $T$  where  $\epsilon = 100$ ,  $\delta = 0.01$  and  $C = 10$ . MNIST is considered.

# Appendix

# A. Proof of Lemma 1

Let  $g_{n}(\pi_{n}^{t};\omega^{t})$  be the stochastic gradient of  $f_{n}(\pi_{n}^{t};\omega^{t})$

$$
g_{n}(\pi_{n}^{t};\omega^{t}) = \left(1 - \frac{\lambda}{2}\right)\nabla F_{n}(\pi_{n}^{t}) + \lambda (\pi_{n}^{t} - \omega^{t}). \tag{34}
$$

As per the PL model of client  $n$  at the  $(t + 1)$ - th model update, we have [1, Eq. (96)]

$$
\begin{array}{rl} & {\mathbb{E}\Big[\| \widetilde{\omega}_{n}^{t + 1} - \overline{\omega}_{n}^{*}\|^{2}\Big] = \mathbb{E}\Big[\| \widetilde{\omega}_{n}^{t} - \overline{\omega}_{n}^{*}\|^{2}\Big] + \eta_{\mathrm{L}}^{2}\times}\\ & {\mathbb{E}\Big[\| g_{n}(\widetilde{\omega}_{n}^{t};\widetilde{\omega}^{t})\|^{2}\Big] + 2\eta_{\mathrm{L}}\mathbb{E}\Big\langle g_{n}(\widetilde{\omega}_{n}^{t};\widetilde{\omega}^{t}),\overline{\omega}_{n}^{*} - \widetilde{\omega}_{n}^{t}\Big\rangle .} \end{array}
$$

The third term on the RHS of (35) becomes

$$
\begin{array}{rl} & {2\eta_{\mathrm{L}}\mathbb{E}\Big\langle g_{n}(\widetilde{\pmb{\omega}}_{n}^{t};\widetilde{\pmb{\omega}}^{t}),\pmb{\pi}_{n}^{*} - \widetilde{\pmb{\omega}}_{n}^{t}\Big\rangle}\\ & {\quad \leq 2\eta_{\mathrm{L}}\Big(1 - \frac{\lambda}{2}\Big)\mathbb{E}\left[F_{n}\left(\pmb{\pi}_{n}^{*};\widetilde{\pmb{\omega}}^{t}\right) - F_{n}\left(\widetilde{\pmb{\omega}}_{n}^{t};\widetilde{\pmb{\omega}}^{t}\right)\right]}\\ & {\quad \quad -\eta_{\mathrm{L}}\left(1 - \frac{\lambda}{2}\right)\mu \mathbb{E}\left[\| \pmb{\pi}_{n}^{*} - \widetilde{\pmb{\omega}}_{n}^{t}\|^{2}\right]}\\ & {\quad \quad +2\eta_{\mathrm{L}}\mathbb{E}\left\langle \lambda (\widetilde{\pmb{\omega}}_{n}^{t} - \widetilde{\pmb{\omega}}^{t}),\pmb{\pi}_{n}^{*} - \widetilde{\pmb{\omega}}_{n}^{t}\right\rangle}\\ & {\quad = 2\eta_{\mathrm{L}}\mathbb{E}\left[f_{n}\left(\pmb{\pi}_{n}^{*};\widetilde{\pmb{\omega}}^{t}\right) - f_{n}\left(\widetilde{\pmb{\omega}}_{n}^{t};\widetilde{\pmb{\omega}}^{t}\right)\right]}\\ & {\quad \quad -\eta_{\mathrm{L}}\left(\left(1 - \frac{\lambda}{2}\right)\mu +\lambda\right)\mathbb{E}\left[\| \pmb{\pi}_{n}^{*} - \widetilde{\pmb{\omega}}_{n}^{t}\|^{2}\right].} \end{array} \tag{36b}
$$

Here, (36a) is obtained by first applying (34) and considering the  $\mu$ - strong convexity of  $F_{n}(\cdot)$ , followed by substituting (3a) into (36a) and yielding (36b).

By substituting (36b) into (35), we obtain the upper bound of  $\mathbb{E}\left[\| \widetilde{\pmb{\pi}}_n^{t + 1} - \pmb {\pi}_n^*\| ^2\right]$  in (36b), as given by

$$
\begin{array}{rl}&{\mathbb{E}\left[\|\widetilde{\pmb{\omega}}_n^{t+1}-\pmb{\omega}_n^*\|^2\right]\leq(1-\eta_\mathbb{L}\left(\left(1-\frac{\lambda}{2}\right)\mu+\lambda\right))\times}\\&{\qquad\mathbb{E}\left[\|\widetilde{\pmb{\omega}}_n^t-\pmb{\omega}_n^*\|^2\right]+\eta_\mathbb{L}^2\mathbb{E}\left[\|\widetilde{\pmb{\omega}}_n^t;\widetilde{\omega}_n^*\|^2\right]}\\&{\qquad+2\eta_\mathbb{L}\mathbb{E}\left[f_n(\widetilde{\pmb{\omega}}_n^*\widetilde{\omega}_n^t)-f_n(\widetilde{\pmb{\omega}}_n^t;\widetilde{\omega}_n^t)\right]}\\&{\leq(1-\eta_\mathbb{L}\left(\left(1-\frac{\lambda}{2}\right)\mu+\lambda\right))\mathbb{E}\left[\|\widetilde{\pmb{\omega}}_n^t-\widetilde{\pmb{\omega}}_n^*\|^2\right]}\\&{\qquad+\eta_\mathbb{L}^2\mathbb{E}\left[\|\widetilde{\pmb{\omega}}_n^t;\widetilde{\omega}_n^*\|^2\right]+\eta_\mathbb{L}^2\lambda^2\mathbb{E}\left[\|\widetilde{\pmb{\omega}}^t-\widetilde{\omega}_n^*\|^2\right]}\\&{\qquad+2\eta_\mathbb{L}^2\lambda\sqrt{\mathbb{E}\left[\|\widetilde{\pmb{\omega}}_n^t;\widetilde{\omega}_n^*\|^2\right]}\sqrt{\mathbb{E}\left[\|\widetilde{\pmb{\omega}}^t-\widetilde{\omega}_n^*\|^2\right]}}\\&{\qquad+2\eta_\mathbb{L}\lambda\sqrt{\mathbb{E}\left[\|\widetilde{\pmb{\omega}}_n^t-\widetilde{\omega}_n^*\|^2\right]}\mathbb{E}\left[\|\widetilde{\pmb{\omega}}^t-\widetilde{\omega}_n^*\|^2\right]}\\&{\leq(1-\eta_\mathbb{L}\left(\left(1-\frac{\lambda}{2}\right)\mu+\lambda\right)+\eta_\mathbb{L}\mathbb{E}\left[\|\widetilde{\pmb{\omega}}_n^t-\widetilde{\omega}_n^*\|^2\right]+\left(\eta_\mathbb{L}^2+\eta_\mathbb{L}^2\lambda^2\right)\times}\\&{\qquad\mathbb{E}\left[\|\widetilde{\pmb{\omega}}_n^t;\widetilde{\omega}_n^*\|^2\right]+\left(\eta_\mathbb{L}^2\lambda^2+\eta_\mathbb{L}\lambda^2\right)\mathbb{E}\left[\|\widetilde{\pmb{\omega}}^t-\widetilde{\omega}_n^*\|^2\right]}\\&{\leq(1-\eta_\mathbb{L}\left(\left(1-\frac{\lambda}{2}\right)\mu+\lambda\right)+\eta_\mathbb{L}\mathbb{E}\left[\|\widetilde{\pmb{\omega}}_n^t;\widetilde{\omega}_n^*\|^2\right]}\\&{\qquad+(\eta_\mathbb{L}^2+\eta_\mathbb{L}^2\lambda^2)\mathbb{E}\left[\|\widetilde{\pmb{\omega}}_n^t;\widetilde{\omega}_n^*\|^2\right]}\\&{\qquad+\frac{4\eta_\mathbb{L}^2\lambda^2+2\eta_\mathbb{L}\lambda^2}{\mu}\mathbb{E}\left[F(\widetilde{\pmb{\omega}}^t)-F(\widetilde{\pmb{\omega}}^*)\right],}\end{array} \tag{37a}
$$

where (37b) and (37c) are based on Cauchy- Schwarz inequality, and (37d) is due to the  $\mu$ - strong convexity of  $F(\cdot)$  and the inequality  $\| \widetilde{\omega}^t - \omega^*\| ^2 \leq \frac{2}{\mu} \left[ F(\widetilde{\omega}^t) - F(\omega^*) \right]$ .

Further, we establish the upper bounds for the squared distances between the PL model and the FL local model, and between the PL model and the optimal FL global model, and for the squared norm of the gradient of the PL model,

$$
\mathbb{E}\left[\| \widetilde{\pmb{\omega}}_n^t -\pmb {u}_n^*\| ^2\right]\leq \frac{1}{\mu^2}\mathbb{E}\left[\| \nabla F_n(\widetilde{\pmb{\omega}}_n^t)\| ^2\right]\leq \frac{G_0^2}{\mu^2}; \tag{38}
$$

$$
\begin{array}{rlr} & {} & {\mathbb{E}\left[\| \widetilde{\pmb{\omega}}_n^t -\pmb {\omega}^*\| ^2\right]\leq \mathbb{E}\left[\| \widetilde{\pmb{\omega}}_n^t -\pmb {u}_n^*\| ^2\right] + \mathbb{E}\left[\| \pmb {u}_n^* -\pmb {\omega}^*\| ^2\right]}\\ & {} & {+\mathbb{E}\left[\| \widetilde{\pmb{\omega}}_n^t -\pmb {u}_n^*\| \times \| \pmb {u}_n^* -\pmb {\omega}^*\| \right]}\\ & {} & {\leq \frac{G_0^2}{\mu^2} +M^2 +\frac{2MG_0}{\mu};\qquad (39)} \end{array} \tag{39}
$$

$$
\mathbb{E}\left[\| g(\widetilde{\pmb{\omega}}_n^t,\pmb {\omega}^*)\| ^2\right]\leq G. \tag{40}
$$

Here, (38) is due to the convexity of  $F_{n}(\cdot)$  and the assumption that  $\mathbb{E}\left[\| \nabla F_n(\widetilde{\omega}^t)\| ^2\right] \leq G_0^2$ . (39) is obtained by first leveraging the Cauchy- Schwarz inequality and then substituting (38). Likewise, (40) is obtained by first using the Cauchy- Schwarz inequality and then plugging (39).

By plugging (38)- (40) into (37d), it readily follows that

$$
\begin{array}{r}\mathbb{E}\left[\| \widetilde{\pmb{\omega}}_n^{t + 1} - \pmb {\omega}_n^*\| ^2\right]\leq \epsilon_{\mathbb{L}}\mathbb{E}\left[\| \widetilde{\pmb{\omega}}_n^t -\pmb {\omega}_n^*\| ^2\right] + (\eta_\mathbb{L}^2 +\eta_\mathbb{L}^2\lambda^2)G\\ +\frac{4\eta_\mathbb{L}^2\lambda^2 - 2\eta_\mathbb{L}\lambda^2}{\mu}\mathbb{E}\left[F(\widetilde{\pmb{\omega}}^t) - F(\pmb {\omega}^*)\right], \end{array} \tag{41}
$$

which concludes this proof.

# B. Proof of Lemma 2

Considering the aggregation process with the DP noise added in the  $(t + 1)$ - th aggregation, the expected difference between  $\widetilde{\omega}^{t + 1}$  and  $\omega^{*}$  is upper bounded by [9, Eq. (59)]

$$
\begin{array}{rl} & {\mathbb{E}\left[F\left(\widetilde{\omega}^{t + 1}\right)\right] - F\left(\omega^{*}\right)\leq \mathbb{E}\left[F\left(\widetilde{\omega}^{t}\right)\right] - F\left(\omega^{*}\right)}\\ & {+\eta_{\mathbb{G}}\left(\frac{\eta_{\mathbb{G}}L}{2} -1\right)\| \nabla F(\widetilde{\omega}^{t})\|^{2} + \frac{L}{2N^{2}}\mathbb{E}\left[\| \pmb {z}^{t}\|^{2}\right].} \end{array} \tag{37a}
$$

By substituting  $F(\omega) - F(\omega^{*}) \leq \frac{1}{2\mu} \| \nabla F(\omega) \| ^2$ ,  $\mathbb{E}\left[\| \mathbf{z}^{t}\|^{2}\right] = d\sigma_{z}^{2}$ , and (8) into (42), Lemma 2 readily follows.

# C. Proof of Theorem 1

When  $\epsilon_{\mathrm{L}} \neq \epsilon_{\mathrm{G}}$ , it follows from (41) and (13) that

$$
\begin{array}{rl} & {\mathbb{E}\big[\big\| \widetilde{\omega}_n^{t + 1} - \widetilde{\omega}_n^*\big\| ^2\big]\leq \epsilon_{\mathrm{L}}^{t + 1}\mathbb{E}\big[\| \widetilde{\omega}_n^0 -\widetilde{\omega}_n^*\| ^2\big] + (\eta_{\mathrm{L}}^2 +\eta_{\mathrm{L}}^2\lambda^2)G\sum_{x = 0}^{t}\epsilon_{\mathrm{L}}^x}\\ & {\quad +\frac{4\eta_{\mathrm{L}}^2\lambda^2 + 2\eta_{\mathrm{L}}\lambda^2}{\mu}\bigg[\sum_{x = 0}^{t}\epsilon_{\mathrm{L}}^x\epsilon_{\mathrm{G}}^{t - x}\mathbb{E}\left[F(\omega^0) - F(\omega^*)\right]}\\ & {\quad +\sum_{x = 0}^{t - 1}\sum_{y = 0}^{t - 1 - x}\epsilon_{\mathrm{L}}^x\epsilon_{\mathrm{G}}^y\phi_{\mathrm{L}}T\bigg]}\\ & {= \epsilon_{\mathrm{L}}^{t + 1}\mathbb{E}\left[\| \widetilde{\omega}_n^0 -\widetilde{\omega}_n^*\| ^2\right] + (1 + \lambda^2)\eta_{\mathrm{L}}^2 G\frac{\epsilon_{\mathrm{L}}^{t + 1} - 1}{\epsilon_{\mathrm{L}} - 1}}\\ & {\quad +\frac{(4\eta_{\mathrm{L}}^2 + 2\eta_{\mathrm{L}})\lambda^2}{\mu}\bigg[\frac{\epsilon_{\mathrm{L}}^{t + 1} - \epsilon_{\mathrm{G}}^{t + 1}}{\epsilon_{\mathrm{L}} - \epsilon_{\mathrm{G}}}\mathbb{E}\left[F(\omega^0) - F(\omega^*)\right]}\\ & {\quad +\bigg(\frac{\epsilon_{\mathrm{L}}^t - \epsilon_{\mathrm{G}}^t}{\epsilon_{\mathrm{G}} - 1} -\frac{\epsilon_{\mathrm{L}}^t - 1}{\epsilon_{\mathrm{L}} - 1}\bigg)\frac{\phi_{\mathrm{L}}T}{\epsilon_{\mathrm{G}}}\bigg].} \end{array} \tag{43b}
$$

Here, (43b) is based on geometric series  $\sum_{x = 0}^{t} a^{x} = \frac{a^{t + 1} - 1}{a - 1}$ , where  $a = \frac{\epsilon_{\mathrm{L}}}{\epsilon_{\mathrm{G}}} \neq 1$ .

When  $\epsilon_{\mathrm{L}} = \epsilon_{\mathrm{G}}$ , (43a) is rewritten as

$$
\begin{array}{rl} & {\mathbb{E}\big[\big\| \widetilde{\omega}_n^{t + 1} - \widetilde{\omega}_n^*\big\| ^2\big]\leq \epsilon_{\mathrm{L}}^{t + 1}\mathbb{E}\big[\| \widetilde{\omega}_n^0 -\widetilde{w}_n^*\| ^2\big] + (\eta_{\mathrm{L}}^2 +\eta_{\mathrm{L}}^2\lambda^2)G\sum_{x = 0}^{t}\epsilon_{\mathrm{L}}^x}\\ & {\quad +\frac{4\eta_{\mathrm{L}}^2\lambda^2 + 2\eta_{\mathrm{L}}\lambda^2}{\mu} \bigg[\sum_{x = 0}^{t}\epsilon_{\mathrm{L}}^t\mathbb{E}\left[F(\omega^0) - F(\omega^*)\right]}\\ & {\quad +\sum_{x = 0}^{t - 1}\epsilon_{\mathrm{L}}^{t - 1 - x}\epsilon_{\mathrm{L}}^y\phi_{\mathrm{L}}T\bigg]}\\ & {= \epsilon_{\mathrm{L}}^{t + 1}\mathbb{E}\left[\| \widetilde{\omega}_n^0 -\widetilde{\omega}_n^*\| ^2\right] + (1 + \lambda^2)\eta_{\mathrm{L}}^2 G\frac{\epsilon_{\mathrm{L}}^{s + 1} - 1}{\epsilon_{\mathrm{L}} - 1}}\\ & {\quad +\frac{(4\eta_{\mathrm{L}}^2 + 2\eta_{\mathrm{L}})\lambda^2}{\mu}\left[(t + 1)\epsilon_{\mathrm{L}}^t\mathbb{E}\left[F(\omega^0) - F(\omega^*)\right]\right.}\\ & {\quad \left. + \left(\epsilon_{\mathrm{L}}^t -\frac{\epsilon_{\mathrm{L}}^t - 1}{\epsilon_{\mathrm{L}} - 1}\right)\frac{\phi_{\mathrm{L}}T}{\epsilon_{\mathrm{L}} - 1}\right].} \end{array} \tag{44b}
$$

With  $\mu > \frac{2 - 2\lambda}{2 - \lambda}$  under Assumption 1,  $\epsilon_{\mathrm{L}} < 1$  and therefore DP- Ditto converges as  $t$  increases. After  $T$  aggregations, the convergence upper bound of DP- Ditto is (14) or (15).

# D. Proof of Lemma 3

By finding the solution to the first derivative of  $f_{n}(\pi_{n};\omega^{*})$  in (3a) and then replacing  $\omega^{*}$  with  $\widetilde{\omega}^{*}$  in the solution, the optimal PL model is given by

$$
\widetilde{\omega}_{n}^{*}(\lambda) = \left(\frac{2 - \lambda}{b}\mathbf{X}_{n}^{\intercal}\mathbf{X}_{n} + \lambda I\right)^{-1}\left(\frac{2 - \lambda}{b}\mathbf{X}_{n}^{\intercal}\mathbf{Y}_{n} + \lambda\widetilde{\omega}^{*}\right) \tag{45a}
$$

$$
\begin{array}{l}{= \left(\frac{2 - \lambda}{b}\mathbf{X}_n^\top \mathbf{X}_n + \lambda I\right)^{-1}\left(\left(\frac{2 - \lambda}{b}\mathbf{X}_n^\top \mathbf{X}_n\right)\hat{\pmb{u}}_n\right.}\\ {\left. + \lambda \sum_{n = 1}^{N}(\mathbf{X}^\top \mathbf{X})^{-1}\mathbf{X}_n^\top \mathbf{X}_n\left(\hat{\pmb{u}}_n + \mathbf{z}_n\right)\right),} \end{array} \tag{45b}
$$

which is obtained by plugging (28) and then (27) in (45a).

Without DP, given  $\hat{u}_n$ ,  $n \in \mathbb{N}$  in (25) and the distributions of  $\tau_n$  and  $\nu_n$ ,  $\hat{u}_n^*$  is given in [1, Lemma 6]:

$$
\begin{array}{rl} & {\pmb {u}_n^* = \Phi (\pmb {u}_n^*)(\Phi_n - \zeta^2\mathbf{I}_d)^{-1}\hat{\pmb{u}}_n}\\ & {\qquad +\Phi (\pmb {u}_n^*)(\Phi_{\setminus n}(\pmb {u}_n^*))^{-1}\pmb{u}_{\setminus n}(\pmb {\omega}^*) + \pmb {\theta}_n,} \end{array} \tag{46}
$$

where  $\hat{\pmb{u}}_n \sim \mathcal{N}(0, \Phi (\pmb {u}_n^*))$ , and

$$
\begin{array}{rl} & {\Phi_{n}\triangleq \sigma^{2}\left(\mathbf{X}_{n}^{\intercal}\mathbf{X}_{n}\right)^{-1} + \zeta^{2}\mathbf{I}_{d};}\\ & {\Phi_{\setminus n}(\pmb{\omega}^{*})\triangleq (\sum_{m\in \mathbb{N},m\neq n}\Phi_{m}^{-1})^{-1};}\\ & {\Phi_{\setminus n}(\pmb{u}_{n}^{*})\triangleq \Phi_{\setminus n}(\pmb{\omega}^{*}) + \zeta^{2}\mathbf{I}_{d};}\\ & {\Phi (\pmb{u}_{n}^{*})\triangleq ((\Phi_{\setminus n}(\pmb{u}_{n}^{*}))^{-1} + (\Phi_{n} - \zeta^{2}\mathbf{I}_{d})^{-1})^{-1};}\\ & {\pmb{u}_{\setminus n}(\pmb{\omega}^{*})\triangleq \Phi_{\setminus n}(\pmb{\omega}^{*})\sum_{m\in \mathbb{N},m\neq n}\Phi_{m}^{-1}\hat{\pmb{u}}_{m}.} \end{array} \tag{47e}
$$

After substituting  $\mathbf{X}_n^\top \mathbf{X}_n = \rho \mathbf{I}_d$  first into (45b) and (47a), we then sequentially plug the results of (47a) - (47d) into the one after. Then, Lemma 3 follows.

# E. Proof of Theorem 2

According to Definition 1 and (24), the optimal  $\lambda^*$  is

$$
\begin{array}{r l} & {\lambda^{*} = \arg \underset {\lambda}{\min}\mathbb{E}\left\{\frac{1}{N}\sum_{n = 1}^{N}\left[\left(\| \mathbf{\Psi}\mathbf{u}_{n}^{*} - \widetilde{\mathbf{\Psi}}_{n}^{*}\left(\lambda\right)\|^{2}\right)^{2}\right]\right.}\\ & {\quad \quad \left. - \left(\frac{1}{N}\sum_{n = 1}^{N}\left[\| \mathbf{\Psi}\mathbf{u}_{n}^{*} - \widetilde{\mathbf{\Psi}}_{n}^{*}\left(\lambda\right)\|^{2}\right]\right)^{2}\right\} \triangleq \underset {\lambda}{\arg \min}R(\lambda).} \end{array} \tag{48}
$$

From (29) and (30), it follows that

$$
\begin{array}{rl} & {\widetilde{\pmb{\omega}}_n^* (\lambda) - \pmb {u}_n^* = \left[\frac{2 - \lambda)\rho N + b\lambda}{((2 - \lambda)\rho + b\lambda)N} -\frac{\sigma^2 + \rho N\zeta^2}{N\sigma^2 + \rho N\zeta^2}\right]\hat{\pmb{u}}_n}\\ & {+\left[\frac{b\lambda}{((2 - \lambda)\rho + b\lambda)N} -\frac{\sigma^2}{N\sigma^2 + \rho N\zeta^2}\right]\sum_{m\in \mathbb{N},m\neq n}\hat{\pmb{u}}_m}\\ & {+\left[\frac{b\lambda}{((2 - \lambda)\rho + b\lambda)N}\sum_{n = 1}^{N}\mathbf{z}_n - \pmb {\theta}_n\right]\triangleq \mathbf{A}_n + \mathbf{B}_n,} \end{array} \tag{49}
$$

$$
\begin{array}{rl} & {\mathrm{where~}\mathbf{A}_n = \frac{(N - 1)\rho((2 - \lambda)\sigma^2 - b\lambda\zeta^2)}{N((2 - \lambda)\rho + b\lambda)(\sigma^2 + \rho\zeta^2)}\hat{\mathbf{u}}_n + }\\ & {\qquad \frac{\rho(b\lambda\zeta^2 - (2 - \lambda)\sigma^2)}{N((2 - \lambda)\rho + b\lambda)(\sigma^2 + \rho\zeta^2)}\sum_{m\in \mathbb{N},m\neq n}\hat{\mathbf{u}}_m;} \end{array} \tag{50}
$$

$$
\mathbf{B}_n = \frac{b\lambda}{((2 - \lambda)\rho + b\lambda)N}\sum_{n = 1}^{N}\mathbf{z}_n - \pmb {\theta}_n. \tag{51}
$$

The  $l$ - th  $(l = 1, \dots , d)$  elements of  $\mathbf{A}_n$  and  $\mathbf{B}_n$  are given by

$$
A_{nl} = \alpha_{1}(\lambda)\alpha_{nl}; \tag{52}
$$

$$
B_{nl} = \frac{b\lambda}{((2 - \lambda)\rho + b\lambda)N}\sum_{n = 1}^{N}z_{nl} - \theta_{nl}, \tag{53}
$$

where  $\hat{u}_{nl}$ ,  $z_{nl}$ , and  $\theta_{nl}$  are the  $l$ - th elements of  $\hat{u}_n$ ,  $\mathbf{z}_n$ , and  $\theta_n$ , respectively;  $\theta_{nl} \sim \mathcal{N}(0, \sigma_w^2)$ , c.f. (30); and

$$
\begin{array}{l}\alpha_{1}(\lambda) = \frac{\rho((2 - \lambda)\sigma^{2} - b\lambda\zeta^{2})}{N((2 - \lambda)\rho + b\lambda)(\sigma^{2} + \rho\zeta^{2})};\\ \alpha_{nl} = (N - 1)\hat{u}_{nl} - \sum_{m\in \mathbb{N},m\neq n}\hat{u}_{ml}. \end{array} \tag{54}
$$

For conciseness, let  $\alpha_0(\lambda) = \frac{b\lambda}{(2 - \lambda)\rho + b\lambda}$ , then we have

$$
\alpha_{1}(\lambda)\frac{\sigma^{2}}{N(\sigma^{2} + \rho\zeta^{2})} = \frac{1}{N}\alpha_{0}(\lambda) = S_{1} - S_{2}\alpha_{0}(\lambda), \tag{55}
$$

where  $S_{1} = \frac{\sigma^{2}}{N(\sigma^{2} + \rho\zeta^{2})}$ , and  $S_{2} = \frac{1}{N}$ . According to (53),  $B_{nl} \sim \mathcal{N}(0, \sigma_B^2)$  and  $\sigma_B^2 = \sigma_w^2 + [\alpha_0(\lambda)]^2 \frac{\sigma_w^2}{N^2}$ . Consider that  $\| \mathbf{\Psi}\mathbf{u}_n^* - \widetilde{\mathbf{\Psi}}_n^* (\lambda)\| ^2 = \sum_{l = 1}^{d}\left(\mathbf{\Psi}_n^* - \widetilde{\mathbf{\Psi}}_{nl}^* (\lambda)\right)^2$ . With (49),  $R(\lambda)$  can be written as

$$
\begin{array}{r}R(\lambda) = \mathbb{E}\left\{\frac{1}{N}\sum_{n = 1}^{N}\left[\left(\sum_{l = 1}^{d}(A_{nl} + B_{nl})^2\right)^2\right]\right.\\ \left. - \left(\frac{1}{N}\sum_{n = 1}^{N}\left[\sum_{l = 1}^{d}(A_{nl} + B_{nl})^2\right]\right)^2\right\} . \end{array} \tag{56}
$$

Given  $\{\hat{\mathbf{u}}_n, n = 1, \dots , N\}$  estimated from samples, (56) can be rewritten as

$$
\begin{array}{rl} & {R(\lambda) = \mathbb{E}{\sum_{l = 1}^{d}\frac{1}{N}\sum_{n = 1}^{N}[(A_{nl} + B_{nl})^4]}\\ & {\quad +2\sum_{l,l'\in [d],l\notin l'}\frac{1}{N}\sum_{n = 1}^{N}[(A_{nl} + B_{nl})^2 (A_{nl'} + B_{nl'})^2]}\\ & {\quad -\sum_{l = 1}^{d}(\frac{1}{N}\sum_{n = 1}^{N}[(A_{nl} + B_{nl})^2]^2 -2\sum_{l,l'\in [d],l\notin l'}\frac{1}{N}}\\ & {\quad \times \sum_{n = 1}^{N}[(A_{nl} + B_{nl})^2]\frac{1}{N}\sum_{n = 1}^{N}[(A_{nl'} + B_{nl'})^2]}}\\ & {= 2d\sigma_B^2 +4\sigma_B^2\alpha_1^2 (\lambda)\sum_{l = 1}^{d}\frac{1}{N}\sum_{n = 1}^{N}[\alpha_{nl}^2] + \alpha_1^4 (\lambda)}\\ & {\quad \frac{1}{N}\sum_{n = 1}^{N}[(\sum_{l = 1}^{d}\alpha_{nl}^2)^2] - \alpha_1^4 (\lambda)(\frac{1}{N}\sum_{n = 1}^{N}[\sum_{l = 1}^{d}\alpha_{nl}^2])^2}\\ & {\quad \triangleq 2d\sigma_B^2 +4\sigma_B^2\alpha_1^2 (\lambda)G_1 + \alpha_1^4 (\lambda)G_2 - \alpha_1^4 (\lambda)G_2^2,} \end{array} \tag{57b}
$$

where (57a) is due to the fact that  $\begin{array}{rl} & {\frac{1}{N}\sum_{n = 1}^{N}\left[\left(\sum_{l = 1}^{d}a_{nl}\right)^2\right]}\\ & {\frac{1}{N}\sum_{n = 1}^{N}\left[\sum_{l = 1}^{d}a_{nl}^2 +2\sum_{l,l'\in [d],l\not = l'}a_{nl}a_{nl'}\right],} \end{array}$  with

$a_{nl} = (A_{nl} + B_{nl})^2$  ; (57b) is obtained by taking the expectation of (57a) with  $B_{nl}\sim \mathcal{N}(0,\sigma_B^2)$  [1, Eq. (56)] and then plugging (52). Here,  $\begin{array}{r}G_{1} = \sum_{l = 1}^{d}\frac{1}{N}\sum_{n = 1}^{N}\left[\alpha_{nl}^{2}\right] \end{array}$ $G_{2} = \textstyle \frac{1}{N}\sum_{n = 1}^{N}\left[\left(\sum_{l = 1}^{d}\alpha_{nl}^{2}\right)^{2}\right]$  . By plugging (55) and  $\sigma_B^2$  into (57c), Theorem 2 follows.

# F. Proof of Theorem 3

Based on Theorem 2, the first and second derivatives of  $R(\lambda)$  with respect to  $\alpha_0$  are given by

$$
\begin{array}{rl} & {\frac{\partial R}{\partial\alpha_0} = 4d\frac{\sigma_z^2}{N^2}\alpha_0 + 8G_1\frac{\sigma_z^2}{N^2} (S_1 - S_2\alpha_0)^2\alpha_0}\\ & {\qquad -8S_2G_1\left(\sigma_w^2 +[\alpha_0]^2\frac{\sigma_z^2}{N^2}\right)(S_1 - S_2\alpha_0)}\\ & {\qquad -4S_2(G_2 - G_1^2)(S_1 - S_2\alpha_0)^3;} \end{array} \tag{58}
$$

$$
\frac{\partial^2R}{\partial\alpha_0^2} = 4D\frac{\sigma_z^2}{N^2} +8G_1\sigma_w^2 S_z^2 +12S_z^2\left(G_2 - G_1^2\right)\left(S_1 - S_2\alpha_0\right)^2, \tag{59}
$$

where  $D = d + 2G_{1}\left(6S_{2}^{2}\alpha_{0}^{2} - 6S_{1}S_{2}\alpha_{0} + S_{1}^{2}\right)$  for brevity. After reorganization,  $D$  can be rewritten as

$$
\begin{array}{rl} & {D = (d - \frac{S_1^2}{N}\sum_{n = 1}^{N}\| \hat{u}_n\| ^2) + \frac{12G_1(\alpha_0 - \frac{1}{2}NS_1)^2}{N^2}}\\ & {\quad = (d - \frac{S_1^2}{N}\sum_{n = 1}^{N}\| N\hat{u}_n - \sum_{m = 1}^{N}\hat{u}_m\| ^2) + \frac{12G_1(\alpha_0 - \frac{NS_1}{2})^2}{N^2}}\\ & {\quad >(d - \frac{S_1^2}{N}\sum_{n = 1}^{N}(\| N\hat{u}_n\| +\sum_{m = 1}^{N}\| \hat{u}_m\|)^2) + \frac{12G_1(\alpha_0 - \frac{NS_1}{2})^2}{N^2}}\\ & {\quad >(d - \frac{S_1^2}{N}\sum_{n = 1}^{N}(NC + NC)^2) + \frac{12G_1(\alpha_0 - \frac{N}{2}S_1)^2}{N^2}}\\ & {\quad = (d - 4C^2 N^2 S_1^2) + \frac{12G_1(\alpha_0 - \frac{N}{2}S_1)^2}{N^2},} \end{array} \tag{600}
$$

where (60b) is obtained by substituting (54) into (60a); (60c) is based on the Cauchy- Schwarz inequality.

When  $C< \frac{2NS_1}{2NS_2}$  in (60e),  $D > 0$  in (59) and, in turn,  $\frac{\partial^2R}{\partial\alpha_0^2} >0$  in (59). In other words,  $\frac{\partial R}{\partial\alpha_0}$  increases monotonically in  $\alpha_0\in [0,1]$ . Clearly,  $\frac{\partial R}{\partial\alpha_0} < 0$  when  $\alpha_0 = 0$ ,  $\frac{\partial R}{\partial\alpha_0} >0$  when  $\alpha_0 = 1$ . Therefore,  $R(\lambda)$  first increases and then decreases in  $\alpha_0\in [0,1]$ . There must be a unique  $\alpha_0^*\in [0,1]$  satisfying (32). Since  $\alpha_0^* = \frac{b^*}{(2 - \lambda^*)^{\rho + b\lambda^*}}$  increases monotonically in  $\lambda \in [0,2]$ , with the uniqueness of  $\alpha_0^*$ , the existence and uniqueness of the optimal  $\lambda^* = \frac{2\rho\alpha_0^*}{(1 - \alpha_0^*)^{b + \rho\alpha_0^*}}$  are confirmed.

# G. Proof of Corollary 1

According to (59), when  $C< \frac{\sqrt{d}}{2S_0^{\prime}}$ $\frac{\partial^2R}{\partial\alpha_0^2}$  monotonically increases with  $\sigma_z^2$  in other words,  $\frac{\partial R}{\partial\alpha_0}$  grows fast with  $\sigma_z^2$  when  $C< \frac{\sqrt{d}}{2S_0^{\prime}}$ . On the other hand, when  $\alpha_0 = 0$ $\frac{\partial R}{\partial\alpha_0}$  is independent of  $\sigma_z^2$  which can be readily concluded by substituting  $\alpha_0 = 0$  into (59):

$$
\left.\frac{\partial R}{\partial\alpha_0}\right|_{\alpha_0 = 0} = -8S_1S_2G_1\sigma_w^2 -4S_1^6 S_2\left(G_2 - G_1^2\right). \tag{61}
$$

Note that  $\lambda^*$  corresponds to  $\alpha_0^*$  i.e., the solution to  $\frac{\partial R}{\partial\alpha_0} = 0$  in other words,  $\alpha_0^*$  is the intersection of the curve  $\mathcal{F}(\alpha_0) = \frac{\partial R}{\partial\alpha_0}$  with the  $x$ - axis. Given all  $\mathcal{F}(\alpha_0) = \frac{\partial R}{\partial\alpha_0}$  curves pass  $(0,\frac{\partial R}{\partial\alpha_0}\big|_{\alpha_0 = 0})$  under any  $\sigma_z^2$  and the slopes of the curves increase with  $\sigma_z^2$ $\alpha_0^*$  decreases as  $\sigma_z^2$  increases. Since  $\lambda^* = \frac{2\rho\alpha_0^*}{(1 - \alpha_0^*)^{b + \rho\alpha_0^*}}$  increases monotonically with  $\alpha_0^*$ , it is concluded that  $\lambda^*$  decreases as  $\sigma_z^2$  increases.

# REFERENCES

[1] T. Li, S. Hu, A. Beirami et al., "Ditto: Fair and robust federated learning through personalization," in Proc. 38th Int. Conf. Mach. Learn., vol. 139, 2021, pp. 6357- 6368. [2] W. Zhang, D. Yang, W. Wu et al., "Optimizing federated learning in distributed industrial iot: A multi- agent approach," IEEE J. Sel. Areas Commun., vol. 39, no. 12, pp. 3688- 3703, 2021. [3] W. Zhang, N. Tang, D. Yang et al., "Det (com) 2: Deterministic communication and computation integration toward AIGC services," IEEE Wirel. Commun., vol. 31, no. 3, pp. 32- 41, 2024. [4] Q. Cui, X. Zhao, W. Ni et al., "Multi- agent deep reinforcement learning- based interdependent computing for mobile edge computing- assisted robot teams," IEEE Trans. Veh. Technol, vol. 72, no. 5, pp. 6599- 6610, 2022. [5] M. Abadi, A. Chu, I. Goodrichow et al., "Deep learning with differential privacy," in Proc. ACM SIGSAC Conf. Comput. Commun. Secur. (CCS), 2016, pp. 308- 318. [6] J. Zhang, J. Zhang, D. W. K. Ng et al., "Federated learning- based cell- free massive MIMO system for privacy- preserving," IEEE Trans. Wirel. Commun., vol. 22, no. 7, pp. 4449- 4460, 2022. [7] K. Wei, J. Li, C. Ma et al., "Low- latency federated learning over wireless channels with differential privacy," IEEE J. Sel. Areas Commun., vol. 40, no. 1, pp. 290- 307, 2021. [8] A. Elgabli, J. Park, C. B. Issaid et al., "Harnessing wireless channels for scalable and privacy- preserving federated learning," IEEE Trans. Commun., vol. 69, no. 8, pp. 5194- 5208, 2021. [9] K. Wei, J. Li, M. Ding et al., "Federated learning with differential privacy: Algorithms and performance analysis," IEEE Trans. Inf. Forensics Secur., vol. 15, pp. 3454- 3469, 2020. [10] Y. Zhao, J. Zhao, M. Yang et al., "Local differential privacy- based federated learning for internet of things," IEEE Internet Things J., vol. 8, no. 11, pp. 8836- 8853, 2020. [11] S. Truex, L. Liu, K.- H. Chow et al., "LDP- Fed: Federated learning with local differential privacy," in Proc. 3rd ACM Int. Workshop Edge Syst. Anal. Netw., 2020, pp. 61- 66. [12] X. Yuan, W. Ni, M. Ding et al., "Amplitude- varying perturbation for balancing privacy and utility in federated learning," IEEE Trans. Inf. Forensics Security, vol. 18, pp. 1884- 1897, 2023. [13] Q. Chen, Z. Wang, H. Wang et al., "FedDual: Pair- wise gossip helps federated learning in large decentralized networks," IEEE Trans. Inf. Forensics Security, vol. 18, pp. 335- 350, 2022. [14] R. Hu, Y. Guo, H. Li et al., "Personalized federated learning with differential privacy," IEEE Internet Things J., vol. 7, no. 10, pp. 9530- 9539, 2020. [15] K. Liu, S. Hu, S. Z. Wu et al., "On privacy and personalization in cross- silo federated learning," Proc. Adv. Neural Inf. Process. Syst. (NeurIPS), vol. 35, pp. 5925- 5940, 2022. [16] P. Sun, H. Che, Z. Wang, Y. Wang, T. Wang, L. Wu, and H. Shao, "Pain- fit: Personalized privacy- preserving incentive for federated learning," IEEE J. Sel. Areas Commun., vol. 39, no. 12, pp. 3805- 3820, 2021. [17] H. Liu, J. Yan, and Y.- J. A. Zhang, "Differentially private over- the- air federated learning over MIMO fading channels," IEEE Trans. Wirel. Commun., vol. 41, no. 11, pp. 3533- 3547, 2024. [18] S. D. Okegbile, J. Cai, H. Zheng et al., "Differentially private federated multi- task learning framework for enhancing human- to- virtual connectivity in human digital twins," IEEE J. Sel. Areas Commun., 2023. [19] S. Park and W. Choi, "On the differential privacy in federated learning based on over- the- air computation," IEEE Trans. Wirel. Commun., 2023. [20] J. Yan, J. Liu, H. Xu et al., "Peaches: Personalized federated learning with neural architecture search in edge computing," IEEE Trans. Mob. Comput., pp. 1- 17, 2024. [21] K. Wei, J. Li, C. Ma et al., "Personalized federated learning with differential privacy and convergence guarantee," IEEE Trans. Inf. Forensics Security, vol. 18, pp. 4488- 4503, 2023. [22] C. T. Dinh, N. Tran, and J. Nguyen, "Personalized federated learning with moreau envelopes," Proc. Adv. Neural Inf. Process. Syst. (NeurIPS), vol. 33, pp. 21 394- 21 405, 2020. [23] T. Li, A. K. Sahu, M. Zaheer et al., "Federated optimization in heterogeneous networks," Proc. 3rd Conf. Mach. Learn. Syst. (MLSys), vol. 2, pp. 429- 450, 2020.

[24] Y. Huang, L. Chu, Z. Zhou et al., "Personalized cross- silo federated learning on non- IID data," in Proc. AAAI Conf. Artif. Intell., vol. 35, no. 9, 2021, pp. 7855- 7873. [25] J. Luo and S. Wu, "Adapting adaptation: Learning personalization for cross- silo federated learning," in Proc. 31th Int. Joint Conf. Artif. Intell. (IJCAI), vol. 2022, 2022, pp. 2166- 2173. [26] J. Zhang, Y. Hua, H. Wang et al., "FedALA: Adaptive local aggregation for personalized federated learning," in Proc. AAAI Conf. Artif. Intell., vol. 37, no. 9, 2023, pp. 11 237- 11 244. [27] A. Fallah, A. Mokhtari, and A. Ozdaglar, "Personalized federated learning with theoretical guarantees: A model- agnostic meta- learning approach," Proc. Adv. Neural Inf. Process. Syst. (NeurIPS), vol. 33, pp. 3557- 3568, 2020. [28] D. Li and J. Wang, "FedMD: Heterogenous federated learning via model distillation," Proc. Adv. Neural Inf. Process. Syst. Workshop on Federated Learn. Data Privacy Confidentiality, 2019, pp. 1- 8. [29] C. You, D. Feng, K. Guo et al., "Semi- synchronous personalized federated learning over mobile edge networks," IEEE Trans. Wirel. Commun., vol. 22, no. 4, pp. 2262- 2277, 2022. [30] H. Zhang, M. Tao, Y. Shi et al., "Federated multi- task learning with non- stationary and heterogeneous data in wireless networks," IEEE Trans. Wirel. Commun., vol. 23, no. 4, pp. 2653- 2667, 2023. [31] T. Li, M. Sanjabi, A. Beirami et al., "Fair resource allocation in federated learning," in Proc. Int. Conf. Learn. Represent., 2020, pp. 1- 13. [32] Z. Hu, K. Shaloudegi, G. Zhang et al., "Federated learning meets multi- objective optimization," IEEE Trans. Netw. Sci. Eng., vol. 9, no. 4, pp. 2039- 2051, 2022. [33] A. Z. Tan, H. Yu, L. Cui et al., "Towards personalized federated learning," IEEE Trans. Neural Netw. Learn. Syst., vol. 34, no. 12, pp. 9587- 9603, 2022. [34] M. Nasr, R. Shokri, and A. Houmansadr, "Comprehensive privacy analysis of deep learning: Passive and active white- box inference attacks against centralized and federated learning," in Proc. IEEE Symp. Secur. Privacy (SP), 2019, pp. 739- 753. [35] M. Fredrikson, S. Jha, and T. Ristenpart, "Model inversion attacks that exploit confidence information and basic countermeasures," in Proc. 22nd ACM SIGSAC Conf. Comput. Commun. Secur., 2015, pp. 1322- 1333. [36] L. Lyu, H. Yu, X. Ma, C. Chen, L. Sun, J. Zhao, Q. Yang, and S. Y. Philip, "Privacy and robustness in federated learning: Attacks and defenses," IEEE Trans. Neural Netw. Learn. Syst., vol. 35, no. 7, pp. 8726- 8746, 2024. [37] K. Bonawitz, V. Ivanov, B. Kreuter et al., "Practical secure aggregation for privacy- preserving machine learning," in Proc. CCS, 2017, pp. 1175- 1191. [38] A. Acar et al., "A survey on homomorphic encryption schemes: Theory and implementation," ACM Computing Surveys (Csur), vol. 51, no. 4, pp. 1- 35, 2018. [39] C. Dwork and A. Roth, "The algorithmic foundations of differential privacy," Found. Trends Theor. Comput. Sci., vol. 9, no. 3- 4, pp. 211- 407, 2014. [40] H. Karimi, J. Nutini, and M. Schmidt, "Linear convergence of gradient and proximal- gradient methods under the polyak- lojasiewicz condition," in Proc. Joint Eur. Conf. Mach. Learn. Knowl. Discovery Databases. Springer, 2016, pp. 795- 811. [41] W. Li, T. Lv et al., "Multi- carrier NOMA- empowered wireless federated learning with optimal power and bandwidth allocation," IEEE Trans. Wirel. Commun., vol. 22, no. 12, pp. 9762- 9777, 2023. [42] W. Li et al., "Decentralized federated learning over imperfect communication channels," IEEE Trans. Commun., pp. 1- 1, 2024. [43] M. O'Searcoid, Metric spaces. Berlin, Germany: Springer, 2006. [44] A. Heidarian and M. J. Dinneen, "A hybrid geometric approach for measuring similarity level among documents and document clustering," in Proc. IEEE 2nd Int. Conf. Big Data Comput. Service Appl. (BigDataService). IEEE, 2016, pp. 142- 151. [45] C. C. Aggarwal, A. Hinneburg, and D. A. Keim, "On the surprising behavior of distance metrics in high dimensional space," in Proc. ICDT. Springer, 2001, pp. 420- 434. [46] C. Dwork, M. Hardt, T. Pitassi et al., "Fairness through awareness," in Proc. ITCS, 2012, pp. 214- 226. [47] M. Hardt, E. Price, and N. Srebro, "Equality of opportunity in supervised learning," vol. 29, 2016.

[48] Y. J. Cho, S. Gupta, G. Joshi et al., "Bandit- based communication- efficient client selection strategies for federated learning," in Proc. 54th Asilomar Conf. Signals, Syst., Comput. IEEE, 2020, pp. 1066- 1069. [49] G. A. Seber and A. J. Lee, Linear regression analysis. Hoboken, NJ, USA: Wiley, 2013, vol. 329. [50] S. L. Shmakov, "A universal method of solving quartic equations," Int. J. Pure Appl. Math, vol. 71, no. 2, pp. 251- 259, 2011. [51] M. Artin, Algebra. Pearson Education, 2011. [Online]. Available: https://books.google.com.au/books?id=S6GSAgAAQBAJ