# Quantum Federated Learning for Multimodal Data: A Modality-Agnostic Approach

<PERSON><PERSON>† <PERSON><PERSON>† <PERSON>† <PERSON><PERSON> <PERSON><PERSON>† †Department of Electrical and Computer Engineering, The University of Alabama in Huntsville, USA <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>

# Abstract

Quantum federated learning (QFL) has been recently introduced to enable a distributed privacy- preserving quantum machine learning (QML) model training across quantum processors (clients). Despite recent research efforts, existing QFL frameworks predominantly focus on unimodal systems, limiting their applicability to real- world tasks that often naturally involve multiple modalities. To fill this significant gap, we present for the first time a novel multimodal approach specifically tailored for the QFL setting with the intermediate fusion using quantum entanglement. Furthermore, to address a major bottleneck in multimodal QFL, where the absence of certain modalities during training can degrade model performance, we introduce a Missing Modality Agnostic (MMA) mechanism that isolates untrained quantum circuits, ensuring stable training without corrupted states. Simulation results demonstrate that the proposed multimodal QFL method with MMA yields an improvement in accuracy of  $6.84\%$  in independent and identically distributed (IID) and  $7.25\%$  in non- IID data distributions compared to the state- of- the- art methods.

# 1. Introduction

Quantum federated learning (QFL) has recently emerged as an innovative framework that merges two groundbreaking technologies: quantum computing and federated learning (FL)[17]. The key idea of classical FL is to allow decentralized clients to train models locally, sharing only model parameters with a central server, thereby preserving data locality and reducing communication overhead [25, 30]. However, the bottleneck of traditional FL is raised by growing data dimensionality and complex optimization requirements [38, 39, 44]. Quantum computing introduces the possibilities in large- scale machine learning by bringing in the quantum advantage from its core principles of superposition and entanglement. Quantum- based models often accelerate convergence and improve representational power in diverse machine- learning tasks where classical approaches fail to perform [15]. In this context, QFL enables the collaboration of quantum machine learning (QML), e.g., a shared quantum neural network (QNN), model training over quantum processors (clients) with an aggregator (quantum server).

Our Key Motivations: The majority of literature works [11, 16, 27, 32] focus on single- modal QFL settings, where data across quantum clients is assumed to be of the same type or modality. However, in real- life applications, multimodal QFL, which integrates multiple data modalities (e.g., a combination of images or audio data in emotion recognition), is often necessary to address more complex problems. While single- modal QFL simplifies the learning process by working with uniform data, multimodal QFL enables richer and more comprehensive insights by leveraging diverse data sources. Developing robust QFL systems that can handle multimodal settings is essential for advancing practical quantum machine learning applications. Another significant challenge is the potential absence of continuous data across all modalities [55], as some sensors or data sources may become inactive or faulty. If these missing modalities are not properly addressed, they can significantly degrade the overall performance of the system [41]. To tackle this issue, we propose a QFL framework specifically designed for multimodal data, incorporating robust mechanisms to effectively handle missing modalities for robust multimodal QFL.

Paper Contributions: Motivated by limitations in the literature, we propose mmQFL, a multimodal quantum federated learning framework with cross- modal correlations and a missing modality- agnostic mechanism. Our key contributions are summarized as follows

- We introduce a multimodal approach in the QFL scenario that integrates multiple modalities for a more robust representation. To the best of our knowledge, this work is the first to study multimodal approach, particularly in a QFL setting along with deeper inter-modal correlation and better handling of missing modalities.- We design a quantum fusion layer with entanglement-based fusion for deeper inter-modal correlations in the

quantum state space. A missing modality agnostic mechanism using no- op gates and context vectors is introduced to isolate incomplete modalities and maintain model stability.

Extensive simulations with the CMU- MOSEI dataset under independent and identically distributed (IID) and nonIID distributions demonstrate the merits of our approach in terms of better training performance and stability compared with state- of- the- art schemes.

# 2. Related Works

# 2.1. Quantum Machine Learning

2.1. Quantum Machine LearningQML combines quantum computing concepts with conventional machine learning approaches, resulting in possible speedups and performance improvements for many computational workloads [5]. Recent research [14, 42] has focused on quantum neural networks (QNNs), quantum support vector machines (QSVMs), and quantum reinforcement learning methods. QML models frequently use variational quantum circuits that use quantum entanglement and superposition to represent complicated data connections [4, 18]. Despite the potential, obstacles such as noise in quantum circuits, limited quantum hardware resources, and scala- bility remain key issues [8]. However, QML frequently encounters issues linked to the centralization of sensitive data, which might raise privacy concerns and a considerable quantum of resources on the server.

# 2.2. Quantum Federated Learning

2.2. Quantum Federated LearningQFL blends federated learning (FL), a privacy- preserving decentralized machine learning technique, with quantum computing technologies to improve privacy and learning efficiency [11, 36]. Classical FL allows numerous clients to train models without exchanging raw data, ensuring data privacy and lowering communication overhead [12, 24, 48]. QFL builds on this paradigm by including quantum algorithms for safe aggregation and privacy improvement, leveraging quantum encryption methods and quantum- enhanced optimization procedures [23]. Recent research has investigated quantum- secure multiparty computation for federated learning [16], quantum- enhanced FL aggregation approaches [56], and showed possible gains in convergence speed and security resilience [9, 33].

# 2.3. Multimodal Learning

Unimodal learning [19, 26, 29, 54] struggles to achieve adequate performance due to the lack of perspectives. Multimodal learning integrates and processes various modalities, including text, audio, and visual input, to improve prediction performance and generate strong representations [3, 37]. Recent studies use deep learning approaches, namely transformer structures, to represent complicated in teractions across modalities [49]. These approaches have proven successful in tasks such as sentiment analysis [52], video classification [7], and multimodal emotion recognition [31, 37]. However, multimodal learning confronts obstacles such as data heterogeneity, modality alignment, and efficient fusion processes [22]. Multimodal scenarios in quantum machine learning have been explored in [21, 35, 57], where researchers have developed quantum models that fuse diverse modalities via PQCs. However, no prior works have been thoroughly conducted to investigate multimodal settings in QFL.

# 2.4. Missing Modalities in Multimodal Learning

2.4. Missing Modalities in Multimodal LearningMissing or incomplete modalities are a common issue in multimodal learning, affecting model performance and generalization capabilities [23]. Existing research in classical multimodal learning has paved the way to address this issue of modality imputation, dropout training, and cross- modal generative techniques [28]. Autoencoders and generative adversarial networks (GANs) are used for imputing missing modalities, allowing models to acquire robust representations with incomplete inputs [47]. However, the handling of the missing modality problem in multimodal QFL is a significant challenge due to the different nature of quantum machine learning.

# 3. Proposed Multimodal QFL (mmQFL) Framework

# 3.1. Overview of the Framework

The mmQFL framework consists of a network of quantum processors (clients) denoted as the set  $\mathcal{N}$  that collaborate to train a shared multimodal QML model with a quantum server. The complete training framework is structured as follows

Each client's dataset contains multiple modalities with dedicated QNN models to ensure modality- specific learning. Each modality features are encoded into the quantum states, which are to be processed independently through parameterized quantum circuits (PQCs), preserving unique modality characteristics. An intermediate quantum fusion layer with full entanglement is introduced before measurement, operating between PQCs of all modalities. This layer also uses trainable parameters to establish cross- modal correlations directly in the quantum state space. The final measurement produces classical outputs, which are used to generate the classification result and calculate loss value. Missing modalities are addressed using a context vector that tracks the modality data availability. During a missing data modality case, this forces qubits into the zero states. The untrained QNN block is isolated during fusion and prevents corrupted states from degrading the perfor

![](images/4b8b80db5da7fa7a83e6ca178803a89c7c0d5c2ce4ff31360881531716a37f3f.jpg)  
Figure 1. An overview framework of the proposed mmQFL approach, where each quantum client has  $M$  modalities. Each client trains separate QNN models for each modality and fuses them using a quantum fusion layer before sending the fused model to the quantum server for model aggregation.

mance. This technique has been discussed in classical machine learning literature [50], and we extend it to the QML scenario through our mmQFL framework.

- After local training, clients upload their modality-specific and fused models to the quantum server for weighted aggregation. The updated global parameters are then shared with clients, completing the mmQFL cycle. The evaluation uses multi-modal data points to produce a single classification output.

# 3.2. Problem Setting

Each client denoted by  $k$  contains a local dataset  $D_{k}$  having  $M$  modalities. A data sample  $x^{k}$  with label  $y^{k}$  on dataset  $D_{k}$  is described as

$$
\mathbf{x}^k = \left(\mathbf{x}_1^k,\mathbf{x}_2^k,\ldots ,\mathbf{x}_M^k\right), \tag{1}
$$

where  $M$  is the total number of available modalities, and each  $\mathbf{x}_m^k \in \mathbb{R}^{d_m}$  is a feature vector corresponding to modality  $m$ . Each quantum client has a modality model  $\theta_{m}^{k}$  for every modality dataset  $D_{m}^{k}$  obtained by training its tailored PQC designed to capture intricate relations between input and its label. Thus, a client incorporates a set of models  $\theta^{k}$  which is formulated and discussed in 3.4.

# 3.3. Encoding Multimodal Data into Quantum States and Training

The foremost step in a QNN involves encoding the classical feature vector into quantum states. Here, the dimensionality- adjusted feature vector of each modality can be amplitude- encoded into a normalized quantum state [40]. Each modality  $m$  is encoded by a separate amplitude- encoding circuit, to process them further in  $M$  distinct PQCs. For modality  $m$  on client  $D_{k}$ , we have an  $n_m$ - qubit encoder mapping as

$$
\mathbf{x}_m^k\mapsto |\psi_{m,\mathrm{enc}}^k\rangle = \sum_{i = 1}^{N_m}(x_{m,i}^k)|i_m\rangle , \tag{2}
$$

where  $\| \mathbf{x}_m^k\| = 1$  and  $N_{m} = 2^{n_{m}}$  and  $|\psi_{m,\mathrm{enc}}^k\rangle$  represents the quantum state vector in a Hilbert space resulting from the encoding. The encoded state is then processed by an  $L$ - layer parameterized quantum circuit (PQC), where each layer  $l$  (for  $l = 1,2,\ldots ,L$ ) sequentially applies a trainable unitary transformation, allowing the quantum state to evolve as

$$
|\psi_{m,l}^k\rangle = U_{m,l}^k (\phi_{m,l}^k)|\psi_{m,l - 1}^k\rangle ,\quad \mathrm{with}\quad |\psi_{m,0}^k\rangle = |\psi_{m,\mathrm{enc}}^k\rangle , \tag{3}
$$

where,  $\phi_{m,l}^{k}$  is the set of rotational angles in single qubit rotational gates ( $R_{x}, R_{y}$ , and  $R_{z}$ ) of layer  $l$ , which acts as the trainable model parameter in PQC of modality  $m$ .

Each layer's unitary  $U_{m,l}^{k}(\phi_{m,l}^{k})$  comprises two sequential operations:

Single- qubit rotation gates  $U_{\mathrm{rot}}^{(l)}$ : Applies rotation gates parameterized by  $\phi_{m,l}^{k}$  to each qubit individually. Entangling layer  $U_{\mathrm{ent}}^{(l)}$ : Establishes correlations between qubits through a cyclic sequence of controlled- NOT (CNOT) gates.

Thus, the complete sequential unitary transformation for layer  $l$  is given by  $U_{m,l}^{k}(\phi_{m,l}^{k}) = U_{\mathrm{ent}}^{(l)}\psi_{\mathrm{rot}}^{(l)}$ . The overall sequential transformation of the PQC across all layers is represented as

$$
U_{m,\mathrm{total}}^k (\pmb {\theta}_m^k) = \prod_{l = 1}^{L}U_{m,l}^k (\phi_{m,l}^k), \tag{4}
$$

where  $\pmb {\theta}_m^k = (\phi_{m,1}^k,\ldots ,\phi_{m,L}^k)$ . The final output quantum state after these sequential transformations evolves as

$$
|\psi_{m,\mathrm{out}}^k (\pmb {\theta}_m^k)\rangle = U_{m,\mathrm{total}}^k (\pmb {\theta}_m^k)|\psi_{m,\mathrm{enc}}^k\rangle . \tag{5}
$$

Here,  $\pmb {\theta}_m^k$  represents the set of all trainable parameters for the entire modality  $m$  on client  $k$ .

# 3.4. Fusion of Modalities and Loss Function

To combine these modality- specific quantum states into a unified representation, we incorporate an additional quantum fusion layer that entangles the separate modality PQCs into a comprehensive modal inspired by the intermediate fusion in the classical approach [6]. This step is only involved in the final framework and not included in the initial study (Table 5) where the contribution of individual modality is studied. During this, the evolved state after Eq. (5) is further processed to the measurement layer where the Pauli- Z observable for each qubit is observed with a similar operation as in Eq. (7). The only difference is the absence of a quantum fusion layer before the measurement, due to which the set of models  $\theta^k$  becomes  $\{\pmb {\theta}_1^k,\pmb {\theta}_2^k,\dots,\pmb {\theta}_M^k\}$  . This concatenation is analogous to the late- fusion approach, where the modalities are fused after the training. In this setup, the next step involves federation, as discussed in 3.7. After the quantum server distributes the global models for all modalities, each modality is evaluated individually to assess its contribution.

Whereas, in the final model with a quantum fusion layer, information exchange between modalities is developed so that the final quantum state representation involves crossmodal correlations. The total system comprises  $n_q =$ $\textstyle \sum_{m = 1}^{M}n_{m}$  qubits (one QNN model per modality) and after each modality's local unitary  $U_{m}^{k}(\pmb{\theta}_{m}^{k})$  , we apply an entangled inter- modality circuit  $U_{\mathrm{fusion}}^{k}(\pmb{\theta}_{\mathrm{fusion}}^{k})$  acting on all  $n_q$  qubits given by

$$
\begin{array}{r}{|\Psi_{\mathrm{out}}^{k}(\pmb{\theta}^{k})\rangle = U_{\mathrm{fusion}}^{k}(\pmb{\theta}_{\mathrm{fusion}}^{k})\bigotimes_{m = 1}^{M}|\psi_{m,\mathrm{out}}^{k}(\pmb{\theta}_{m}^{k})\rangle ,} \end{array} \tag{6}
$$

where  $\begin{array}{rlr}{\pmb {\theta}^k} & = & {\{\pmb {\theta}_1^k,\dots ,\pmb {\theta}_M^k,\pmb{\theta}_{\mathrm{fusion}}^k\}} \end{array}$  is the concatenated form of models of all modalities on client  $k$  Here  $\theta_{\mathrm{fusion}}^k$  is the set of trainable parameters in an additional layer in the fusion layer. By stacking this layer, the fusion mechanism improves multimodal correlation while holding the flexibility to compensate for missing modalities.

# 3.5.Measurement and Loss Function

A final measurement operator  $o$  or set of Pauli operators on the  $n_q$  qubits) produces classical outputs. The measurement retrieves the Pauli-  $z$  observable in classical values as

$$
p^k (\pmb {\theta}^k) = \langle \psi_{\mathrm{out}}^k (\pmb {\theta}^k)|O_m|\psi_{\mathrm{out}}^k (\pmb {\theta}^k)\rangle , \tag{7}
$$

where  $O_{m}$  is a chosen Pauli-  $\mathcal{Z}$  measurement operator. The measured qubit states are used to produce a predicted label  $\hat{y}^k$  . Denoting the measurement outcome distribution by  $p(\hat{y}^k\mid \pmb {\theta}^k)$  , the local loss on client  $k$  with dataset  $D_{k}$  is

$$
L_{k}(\pmb{\theta}^{k}) = \frac{1}{D_{k}}\sum_{x = 1}^{D_{k}}\ell \Big(p(\hat{y}^{k}\mid \pmb{\theta}^{k},\mathbf{x}_{x}^{k}),y_{x}^{k}\Big), \tag{8}
$$

where  $\ell$  is the per- sample loss value. Note that the loss calculation for the individual modality evaluation incorporates its respective modality dataset  $D_{m}^{k}$  . The optimization in the PQC is performed using a classical optimizer, with gradients estimated via the parameter- shift rule [51], ensuring efficient computation of updates for the trainable parameters in the quantum circuit.

# 3.6. Missing Modality Agnostic (MMA) Approach

In a scenario where  $M^{\prime}$  out of  $M$  modalities are missing on client dataset  $D_{k}$  , certain encoded states or sub- circuits are disabled. Formally, let  $c\in \{0,1\} ^M$  be a context vector, where  $c_{m} = 0$  indicates modality  $m$  is missing. Then, in the encoding step, we replace  $\mathbf{x}_{m}^{k}\mapsto |\psi_{m,\mathrm{enc}}^{k}\rangle$  with a blank (no- op gate) if  $c_{m} = 0$

The fusion circuit  $U_{\mathrm{fusion}}^k$  acts on QNN models from all modalities. When a modality is missing, the corresponding qubits remain initialized in a fixed zero state using no- op gates, preventing unwanted interference. This missing- modality- agnostic design ensures that the QNN remains functional even under partial modality availability. Without the MMA mechanism, the PQC of the missing modality becomes randomly initialized or receives garbage values from faulty sensors, leading to corrupted quantum states. These inaccurate states get fused with other properly trained modalities, degrading overall training performance and classification accuracy. In contrast, when MMA is employed, the context vector immediately flags missing modality data. Consequently, missing modalities remain isolated in the zero state via no- op gates, preventing interference and significantly stabilizing the training process. Although the potential information loss due to the absence of modality isn't recovered, the MMA scheme prevents negative impact due to the corrupted states.

# 3.7. Incorporating Federated Learning

The QFL algorithm is a decentralized training framework similar to classical federated learning. The process is iterative and progresses in communication rounds indexed by  $r$  Each round involves three key steps as follows

1. Client Initialization and Local Update: At the start of round  $r$  , the server broadcasts the current global model  $\theta_{\mathrm{global}}^{(r)}$  to all clients. Each client initializes its local model as  $\pmb{\theta}^{k,r - 1}\leftarrow \pmb{\theta}^{(r)_{\mathrm{global}}}$  global and trains it on its local dataset using gradient-based updates to minimize its loss function given by Eq.8).After a predefined number of local iterations, each client obtains its trained parameter vector in global round  $r$  as  $\theta_{k}^{(r)}$

2. Aggregation: After the local training in the global round  $r$  , the server collects model parameters from all clients. The total data size is  $\begin{array}{r}D = \sum_{k = 1}^{\mathcal{N}}D_{k} \end{array}$  . Each client maintains parameters  $\theta_{m}^{k,r}$  for each modality and  $\theta_{\mathrm{fusion}}^{k,r}$  for

the fusion model. The global update for both modality- specific and fusion parameters is expressed as

$$
\theta_{global}^{(r + 1)} = \frac{1}{D}\sum_{k = 1}^{N}D_k\theta_m^{k,r},\quad \forall m\in \{1,\ldots ,M,\mathrm{fusion}\} . \tag{9}
$$

The aggregated global model  $\theta_{global}^{(r + 1)}$  is then represented as  $\left\{\theta_{1}^{(r + 1)},\theta_{2}^{(r + 1)},\ldots ,\theta_{M}^{(r + 1)},\theta_{\mathrm{fusion}}^{(r + 1)}\right\}$ . The weighted averaging ensures that each client's contribution is proportional to its dataset size, improving robustness in non- IID data settings.

This iterative process continues until the global model converges to an optimal solution, producing the final trained model  $\theta_{global}^{(R)}$ , where  $R$  is the total number of global communication rounds.

# 3.8. Algorithm

Algorithm 1 summarizes the workflow of mmQFL with missing modality agnostic, where the core training process starts with the initialization of the global model (line 2), which is then distributed to all the clients (lines 4- 7). The client trains the QFL model with multimodal data, separately for each modality. The presence or absence of a certain modality is considered via a context vector and a fusion mechanism is performed (lines 9- 10). The loss function is then calculated and all model parameters are updated iteratively during local iterations (lines 11- 13). The locally trained models in the global epoch  $r$  are then sent to the server from all the clients for aggregation. Finally, the server aggregates the received local models and broadcasts the new global model again. This whole process repeats until convergence is reached.

# 4. Experiments

# 4.1. Dataset

For our proposed method, we have used the Carnegie Mellon University Multimodal Opinion Sentiment and Emotion Intensity (CMU- MOSEI) dataset [53]. The CMU- MOSEI dataset is a large multimodal dataset that is frequently utilized in the scientific community for sentiment analysis, emotion recognition, and multimodal language processing. Extracted from YouTube videos, it contains text, audio, and video data from over 23,500 sentence utterances from over 1,000 distinct speakers covering more than 250 themes. Sentiment on a continuous scale ranging from - 3 (very negative) to +3 (extremely positive) and six fundamental emotions—happiness, sorrow, anger, fear, disgust, and surprise—on a scale ranging from 0 to 3 have been assigned for each statement in the dataset.

Data Processing In this research, we use the audio, image, and text modalities from the CMU MOSEI dataset. We 1: Input: Global rounds  $R$  clients  $K$  local multimodal datasets  $\{\mathcal{D}_k\}_{k = 1}^K$  2: Output: Final global model  $\theta_{global}^{(R)}$  3: Initialize:  $\theta_{global}^{(0)}$  4: for  $r = 0$  and round  $r$  to  $R_{- }1$  do 5: Server broadcasts  $\theta_{global}^{(r - 1)}$  6: for each client  $k$  in parallel do 7: Set  $\theta^{k,r}\leftarrow \theta_{global}^{(r)}$  8: for each local epoch  $t$  and sample  $\mathbf{x}^k\in \mathcal{D}_k$  do 9: For each modality  $m$  if available, encode  $\mathbf{x}_{m}^{k}$  via Eq. (2) and apply  $U_{m}^{k}(\theta_{m}^{k})$  (Eq. (5)); else, use a no- op 10: Fuse modality outputs using Eq. (6) and measure as in Eq. (7) to obtain prediction  $\hat{y}^k$  11: Compute loss  $\ell \left(p(\hat{y}^{k}\mid \pmb{\theta}^{k},\mathbf{x}^{k}),y^{k}\right)$  12: Update the optimized parameters  $\theta_{k}$  13: end for 14: Client sends  $\theta^{k,r}$  to server. 15: end for 16: Server aggregates using Eq. (9). 17: end for 18: Return:  $\theta_{global}^{(R)}$

# Algorithm 1 Multimodal Quantum Federated Learning

decide to extract 2 frames per second from the video frames to capture the image data. Then we resize the image to a smaller size of  $32\times 32$  pixels to facilitate quantum encoding. For the audio, the corresponding audio segment is sampled at  $20\mathrm{Hz}$  and it gets converted to Mel- frequency cepstral coefficients (MFCCs) format as this format is more manageable for quantum algorithms [1]. This extracts features such as voice intensity and frequency and efficiently reduces the feature dimension. For the text data, we utilize the pre- extracted embeddings. The dimensionality of these features is adjusted using a dense layer before applying quantum encoding. This dataset is inherently synchronized across modalities, ensuring that text, audio, and image data are aligned which ensures temporal coherence without requiring additional synchronizations.

# 4.2. Simulation Settings

We present a QFL system that trains QNN data processing using ten noisy intermediate- scale quantum (NISQ) devices for the local dataset of each modality and a single quantum server. Given extensive research on quantum noise mitigation [13, 43, 45, 46] and the advancement of quantum computers toward less noisy environments, we focus on minimal noise, as noisy simulations are not the primary scope of our research. Thus, we use quantum depolarizing noise with a depolarizing probability ranging from 0.001 to 0.05. The quantum simulation setup is supported by the state vector simulator torchquantum. For the QNN model, we have named iQNN for the image- classification quantum model, aQNN for the audio- classification quantum model,

and tQNN for the text- classification quantum model.

and tQNN for the text- classification quantum model.Non- IID data distribution. In our FL study, we investigate non- IID data distributions across ten clients for images and five clients for audio, which included 6000 unlabeled training pictures saved on the server. The clients have random rata data distributions with different feature spaces. The number of features for the emotions happiness, sadness, anger, disgust, surprise, and fear is 12500, 6000, 5000, 4050, 2250, and 1900 respectively. In our distribution, the input data is already heterogeneous as the number of data points on each client varies by a significant number. This heterogeneity is further propagated into the quantum domain through the implementation of angle encoding, which directly translates classical values into quantum rotational angles. This will eventually introduce heterogeneity into quantum data.

# 4.3. Simulation Results on QFL

Effect on using the quantum model. This part of our simulation empirically answers a very critical query: What is the advantage of QFL over classical FL?. Note that we haven't yet incorporated a multimodal approach in this part of the simulation. Our simulation results (Figure 2) show improved performance with quantum models. For comparison, CNN is used for image classification and LSTM for audio and text in FL, while iQNN, aQNN, and tQNN are used for the same tasks in QFL. Results indicate that iQNN converges faster than CNN with similar accuracy (Figure 2a). aQNN outperforms LSTM significantly in accuracy and slightly in convergence speed for audio (Figure 2b). For text, tQNN tends to match LSTM's accuracy (Figure 2c). Thus, QNN models have shown overall better performance in emotion detection datasets, and we proceed with these QNN models in our QFL simulations. This claim is further validated in multiple literature works [2, 10, 11]. Such individual QNN models are later used for modality- specific models in multimodal settings.

Ablation study. In our ablation study, we investigate how different quantum factors affect the performance of our QFL model, with a particular emphasis on the number of quantum bits (qubits) and quantum layers. Table 1 summarizes our experiments comparing model performance with qubit configurations  $D_{q} = 2,3,5$  and 10. All other parameters and training epochs were consistent. Results show that performance improves with more qubits, with  $D_{q} = 10$  yielding the best results. Further increasing qubits exponentially expands the quantum state, so additional simulations use 10 qubits to balance performance and complexity.

Next, we examine the impact of quantum layers  $(l)$  in our QFL model Table 2 using 1,2,3,5,and 10 layers with fixed settings of qubits and training rounds. Unlike qubit variations, performance did not follow a linear trend. The  $l = 1$  layer performed best for text and image data, while Table 3. Different number of clients  $N$  on mmQFL environment in IID and non- IID data distribution. We have used 3, 5, and 10 clients for comparison. Experimenting with the number of clients helps evaluate scalability, generalization, and convergence where adding more clients generally results in more robustness.

Table 1. Ablation study to investigate the effects of different qubit counts  $D_{q}$  on QFL model performance. The qubits being compared were 2,3,5,and 10. We limited the number of qubits to 10 to minimize excessive computational demands and resource consumption.  

<table><tr><td>Dq</td><td>Image Data Accuracy ↑</td><td>Audio Data Accuracy ↑</td><td>Text Data Accuracy ↑</td></tr><tr><td>2</td><td>52.22%</td><td>45.67%</td><td>76.10%</td></tr><tr><td>3</td><td>68.91%</td><td>55.74%</td><td>84.55%</td></tr><tr><td>5</td><td>75.32%</td><td>61.02%</td><td>91.18%</td></tr><tr><td>10</td><td>78.50%</td><td>64.33%</td><td>96.29%</td></tr></table>

<table><tr><td>l</td><td>Image Data Accuracy ↑</td><td>Audio Data Accuracy ↑</td><td>Text Data Accuracy ↑</td></tr><tr><td>1</td><td>78.47%</td><td>63.98%</td><td>96.31%</td></tr><tr><td>2</td><td>78.23%</td><td>62.08%</td><td>95.60%</td></tr><tr><td>3</td><td>77.75%</td><td>64.41%</td><td>95.97%</td></tr><tr><td>5</td><td>76.93%</td><td>60.28%</td><td>96.03%</td></tr><tr><td>10</td><td>74.20%</td><td>61.15%</td><td>94.50%</td></tr></table>

Table 2. Comparing configurations with 1, 2, 3, 5, and 10 layers to determine the impact of quantum layer depth  $l$  in the QFL model. The study investigates the trade-offs between model expressiveness and training efficiency, arguing that fewer layers may result in underfitting due to limited expressiveness, whereas more layers improve representational capability at the expense of encountering barren plateaus that impede efficient optimization.  

<table><tr><td>Data Distri-bution</td><td>Clients</td><td>Image Acc↑</td><td>Audio Acc↑</td><td>Text Acc↑</td></tr><tr><td rowspan="3">ID</td><td>N=3</td><td>76.88%</td><td>62.29%</td><td>95.25%</td></tr><tr><td>N=5</td><td>78.43%</td><td>63.94%</td><td>95.79%</td></tr><tr><td>N=10</td><td>79.10%</td><td>64.55%</td><td>96.32%</td></tr><tr><td rowspan="3">Non-IID</td><td>N=3</td><td>70.11%</td><td>58.76%</td><td>88.96%</td></tr><tr><td>N=5</td><td>70.58%</td><td>59.87%</td><td>90.28%</td></tr><tr><td>N=10</td><td>71.49%</td><td>61.03%</td><td>92.77%</td></tr></table>

$l = 3$  was optimal for audio. Thus, we use  $l = 1$  for text and image, and  $l = 3$  for audio in further tests.

Effects on Number of Clients. We evaluated QFL performance with varying client numbers in Table 3 across IID and three non- IID data distributions. Increasing the number of clients improved accuracy across all modalities. In the IID setting, increasing clients from 3 to 10 improved image, audio, and text accuracy by  $2.22\%$ $2.26\%$  ,and  $1.07\%$  respectively. In non- IID settings, accuracy increased by  $1.38\%$  image),  $2.27\%$  audioand  $3.81\%$  text.The best results were achieved with  $N = 10$  clients, with IID showing the highest overall accuracy.

Effects on small data. Table 4 presents the classification accuracy of the QFL model across IID and non- IID data distributions with data sizes  $(|D_{n}| = 10\% ,25\% ,50\% ,$  and  $100\%)$  . Results show that larger data sizes improve accu

![](images/de83ff7a5eedfc35db1c02c11218ebf779062e95cb37ad2d100a6aab4c57988b.jpg)  
Figure 2. Performance comparison between quantum model approach and classical approach. In (a) for image data classification, we use CNN for the classical approach and the iQNN model for the quantum approach. Similarly, in (b) and (c), we use LSTM for the classical approach and aQNN and iQNN for the quantum approach in audio and text data respectively.

racy in all modalities. In the IID setting, increasing data from  $10\%$  to  $100\%$  boosts image, audio, and text accuracy by  $25.82\%$ $18.64\%$  and  $17.34\%$  respectively. In non- IID settings, accuracy improves by  $23.89\%$  (image),  $19.17\%$  (audio), and  $20.74\%$  (text). Thus, we can conclude that larger datasets enhance model robustness, though non- IID data reduces overall performance.

Table 4. Different data size  $|D|$  using QFL environment across iid AND various non-IID data distributions. The data size includes  $10\%$ $25\%$ $50\%$  ,and  $100\%$  

<table><tr><td>Data distribution</td><td>|Dn|</td><td>Image Data Accuracy ↑</td><td>Audio Data Accuracy ↑</td><td>Text Data Accuracy ↑</td></tr><tr><td rowspan="4">IID</td><td>10%</td><td>53.10%</td><td>46.98%</td><td>79.01%</td></tr><tr><td>25%</td><td>69.34%</td><td>56.47%</td><td>85.54%</td></tr><tr><td>50%</td><td>74.02%</td><td>63.20%</td><td>92.91%</td></tr><tr><td>100%</td><td>78.92%</td><td>65.62%</td><td>96.35%</td></tr><tr><td rowspan="4">Non-IID</td><td>10%</td><td>50.12%</td><td>43.64%</td><td>73.94%</td></tr><tr><td>25%</td><td>64.24%</td><td>53.72%</td><td>83.15%</td></tr><tr><td>50%</td><td>71.23%</td><td>59.92%</td><td>89.77%</td></tr><tr><td>100%</td><td>74.01%</td><td>62.81%</td><td>94.68%</td></tr></table>

# 4.4. Simulation Results with Multi-modalities

Effects of individual modality models: Table 5 compares classification accuracy across image, audio, and text modalities in IID and non- IID data distributions to assess the influence of multimodal learning. In this setting, the quantum fusion layer with entanglement is not considered and the late- fusion approach is applied. The results show that adopting a single- server multimodal method consistently increases accuracy over separate- server models, with slight improvements of  $1.40\%$  (image),  $2.51\%$  (audio), and  $1.07\%$  (text) in the IID scenario, and  $1.65\%$  (image),  $1.52\%$  (audio), and  $1.50\%$  (text) in the Non- IID environment. Overall, our findings emphasize the benefits of multimodal integration in QFL, particularly in terms of enhancing feature representation and learning efficiency across a variety of data types. The results show that the multimodal setting provides a more robust representation of the data

Simulation Results Missing Modalities: Finally, we add a quantum fusion layer before measurement that inter connects the multiple modalities in a single quantum model to establish a robust inter- modality relationship for a final prediction as well as a context vector- based approach to tackle the issue of missing modalities. Table 6 compares model performance with and without the MMA strategy for different amounts of missing modalities (Image, Au

Table 5. Comparison results between without and with multimodal approaches in both IID and Non-IID data distribution. The multimodal model uses one server whereas the other model has a separate server for each data type.  

<table><tr><td>Data distribution</td><td>Multimodal</td><td>Image Accuracy ↑</td><td>Audio Accuracy ↑</td><td>Text Accuracy ↑</td></tr><tr><td rowspan="2">IID</td><td>QFL</td><td>78.92%</td><td>65.62%</td><td>96.35%</td></tr><tr><td>mmQFL</td><td>80.32%</td><td>68.13%</td><td>97.42%</td></tr><tr><td rowspan="2">Non-IID</td><td>QFL</td><td>74.01%</td><td>62.81%</td><td>94.68%</td></tr><tr><td>mmQFL</td><td>75.66%</td><td>64.33%</td><td>96.18%</td></tr></table>

Table 6. Comparison results between different missing modality levels  $m$  in without MMA and with MMA in both IID and nonIID data distribution. The missing levels used in this comparison are  $1\%$ $10\%$  ,and  $20\%$  ,where  $1\%$  indicates 1 in 100 data points is missing.  

<table><tr><td>Data Distribution</td><td>Missing Modality</td><td>Quantity</td><td>Without MMA</td><td>With MMA</td></tr><tr><td rowspan="10">IID</td><td>None</td><td>-</td><td>96.36%</td><td>96.41%</td></tr><tr><td rowspan="3">Image</td><td>1%</td><td>88.21%</td><td>91.90%</td></tr><tr><td>10%</td><td>78.89%</td><td>84.50%</td></tr><tr><td>20%</td><td>72.18%</td><td>80.02%</td></tr><tr><td rowspan="3">Audio</td><td>1%</td><td>90.55%</td><td>92.26%</td></tr><tr><td>10%</td><td>85.73%</td><td>89.31%</td></tr><tr><td>20%</td><td>82.47%</td><td>86.52%</td></tr><tr><td rowspan="3">Text</td><td>1%</td><td>82.75%</td><td>87.02%</td></tr><tr><td>10%</td><td>73.30%</td><td>80.15%</td></tr><tr><td>20%</td><td>64.64%</td><td>75.30%</td></tr><tr><td rowspan="10">Non-IID</td><td>None</td><td>-</td><td>82.36%</td><td>82.41%</td></tr><tr><td rowspan="3">Image</td><td>1%</td><td>78.36%</td><td>80.05%</td></tr><tr><td>10%</td><td>72.40%</td><td>76.98%</td></tr><tr><td>20%</td><td>69.22%</td><td>74.71%</td></tr><tr><td rowspan="3">Audio</td><td>1%</td><td>79.84%</td><td>81.33%</td></tr><tr><td>10%</td><td>75.72%</td><td>78.50%</td></tr><tr><td>20%</td><td>72.01%</td><td>75.45%</td></tr><tr><td rowspan="3">Text</td><td>1%</td><td>75.44%</td><td>77.35%</td></tr><tr><td>10%</td><td>68.93%</td><td>74.01%</td></tr><tr><td>20%</td><td>63.58%</td><td>70.30%</td></tr></table>

![](images/c7f8e072c7f04afe79cfd4d58d3dda9235539827fb8b90b64617c5f88991d4f2.jpg)  
Figure 3. Comparison results between without MMA and with MMA in non-IID data distribution. In epochs 25-35, 50-60, and 80-90,  $1\%$  of audio, image, and text data is missing, respectively.

dio, Text) at  $1\%$ $10\%$  ,and  $20\%$  across IID and non- IID data distributions. Under IID distribution, baseline accuracy without missing modalities is acceptable  $(96. \%)$  but considerably decreases with modality deficits. For example, eliminating  $20\%$  of the Text modality lowers accuracy from  $96.36\%$  to  $64.64\%$  without MMA) as the text was the most contributing factor in the overall model's performance. Introducing MMA significantly improves accuracy in these difficult circumstances; with the same  $20\%$  text modality loss, MMA enhances accuracy from  $64.64\%$  to  $75.30\%$  .Similarly, for a non- IID distribution, baseline accuracy  $(82.36\%)$  declines significantly as modality loss increases. For example, a  $20\%$  missing image modality reduces accuracy from  $82.36\%$  to  $69.22\%$  with MMA slightly recovering to  $74.71\%$  . The benefit of MMA is consistent across all modalities and situations, indicating its usefulness in decreasing performance decline when modalities are absent. Figure 3 also describes the advantage of our missing modalities agonistic approach where the accuracy graph is sustained even in changing missing modalities scenarios.

# 4.5. Comparison with State-of-the-art Approaches

Finally, we compare our method with other state- of- the- art approaches to evaluate the performance of our model. For the comparison, we choose classical unimodal models ([54] for image, [26] for audio, and [19] for text), multimodal ML approach Emonets [18], multimodal FL approach Fedmultimodal [12], multimodal QML approach QNMF [33], QFL approach QFSM [34], and our proposed mmQFL approach. We compare in both IID and non- IID scenarios. Table 7 shows the accuracy results for various approaches when  $10\%$  of data from all modalities is absent. Our mmQFL technique has the highest combined accuracy of  $85.61\%$  in IID settings and  $79.27\%$  in non- IID conditions, exceeding all other approaches.

Unimodal approaches have the lowest accuracy  $(65.19\%$  for IID,  $61.19\%$  for non- IID) because they lack cross modal integration, which is critical in emotion identification. Multimodal approaches, such as Emonets and Fedmultimodal, exhibit gains  $(72.44\%)$  and  $75.36\%$  in IID, respectively), although they still fall short of quantum- based approaches. QNMF, which includes quantum principles, achieves  $78.75\%$  accuracy in IID situations and  $72.02\%$  in non- IID conditions, demonstrating the benefits of quantum- enhanced modeling.

Table 7. Comparison of our approach with other state-of-the-art approaches in both IID and non-IID data distribution with  $10\%$  missing data of all modalities. For comparison, we have selected unimodal approaches for all modalities and other relevant multimodal approaches in emotion detection.  

<table><tr><td>Method</td><td>IID Combined Acc. ↑</td><td>Non-IID Combined Acc. ↑</td></tr><tr><td>Unimodal [19, 26, 54]</td><td>65.19%</td><td>61.19%</td></tr><tr><td>Emonets [18]</td><td>72.44%</td><td>67.97%</td></tr><tr><td>Fedmultimodal [12]</td><td>75.36%</td><td>69.40%</td></tr><tr><td>QFSM [34]</td><td>74.23%</td><td>70.61%</td></tr><tr><td>QNMF [33]</td><td>78.75%</td><td>72.02%</td></tr><tr><td>mmQFL</td><td>85.61%</td><td>79.27%</td></tr></table>

Our mmQFL method outperforms all other approaches and yields an improved accuracy of  $7.25\%$  by combining quantum federated learning with multimodal processing, proving the benefit of quantum fusion layers, and addressing missing modality awareness. The results show that our proposed approach not only improves multimodal learning but also provides a strong foundation to manage missing modalities, making it ideal for real- world applications.

# 5. Conclusions

The paper presented a novel mmQFL framework that improved the QML model performance by incorporating multiple data modalities to generate a single classification result. A quantum fusion layer approach with trainable parameters and entanglement was introduced to establish cross- modal correlations directly within the quantum state space, enhancing the combined feature representations. To mitigate the adverse effects of missing modalities, the MMA mechanism was employed to identify and isolate the respective modality using a context vector approach and no- op gates. Extensive simulations on the CMUMOSEI dataset demonstrated that the proposed framework achieved superior results compared to existing methods in both IID and non- IID distributions by revealing improved accuracy and robustness against missing modalities.

# References

[1] Zrar Kh Abdul and Abdulbasit K Al- Talabani. Mel frequency cepstral coefficient and its applications: A review. IEEE Access, 10:122136- 122158, 2022. 5 [2] Arnaldo Rafael Camara Araujo. Ogobuchi Daniel Okey, Muhammad Saadi, Pablo Adasme, Renata Lopes Rosa, and

Demostenes Zegarra Rodríguez. Quantum- assisted federated intelligent diagnosis algorithm with variational training supported by 5g networks. Scientific Reports, 14(1):26333, 2024. 6[3] Tadas Baltrusaitis, Chaitanya Ahuja, and Louis- Philippe Morency. Multimodal machine learning: A survey and taxonomy. IEEE Transactions on Pattern Analysis and Machine Intelligence, 41(2):423- 443, 2018. 2[4] Marcello Benedetti, David Garcia- Pintos, and Alejandro Perdomo- Ortiz. Parameterized quantum circuits as machine learning models. Quantum Science and Technology, 4(4): 043001, 2019. 2[5] Jacob Biamonte, Peter Wittek, and Nicola et al. Pancotti. Quantum machine learning. Nature, 549:195- 202, 2017. 2[6] Said Yacine Boulaha, Abdenour Amamra, Mohamed Ridha Madi, and Said Daikh. Early, intermediate and late fusion strategies for robust deep learning- based multimodal action recognition. Machine Vision and Applications, 32(6):121, 2021. 4[7] Joao Carreira and Andrew Zisserman. Quo vadis, action recognition? a new model and the kinetics dataset. Proceedings of CVPR, pages 6299- 6308, 2017. 2[8] M. Cerezo, Andrew Arrasmith, and Ryan et al. Babbush. Variational quantum algorithms. Nature Reviews Physics, 3: 625- 644, 2021. 2[9] M. Chehimi, M. Marcon, and C. Sun. Quantum federated learning: A path towards secure and efficient ai. Quantum Computing Transactions, 14:84- 102, 2022. 2[10] Mahdi Chehimi, Samuel Yen- Chi Chen, Walid Saad, Don Towsley, and Merouane Debbah. Foundations of quantum federated learning over classical and quantum networks. IEEE Network, 38(1):124- 130, 2023. 6[11] Ming Chen, Qing Yang, and Yun Wang. Federated learning meets quantum computing: A new paradigm. IEEE Transactions on Neural Networks and Learning Systems, 32(7): 2849- 2861, 2021. 1, 2, 6[12] Tiantian Feng, Dighalay Bose, Tuo Zhang, Rajat Hebbar, Anil Ramakrishna, Rahul Gupta, Mi Zhang, Salman Avestimehr, and Shrikantn Narayanan. Fedmultimodal: A benchmark for multimodal federated learning. In Proceedings of the 29th ACM SIGKDD Conference on Knowledge Discovery and Data Mining, pages 4035- 4045, 2023. 2, 8[13] Yusuke Hama and Horiyumi Nishi. Quantum error mitigation via quantum- noise- effect circuit groups. Scientific Reports, 14(1):6077, 2024. 5[14] Vojtech Havlicek, Antonio D. Gorcoles, and Kristan et al. Temme. Supervised learning with quantum- enhanced feature spaces. Nature, 567:209- 212, 2019. 2[15] Hsin- Yuan Huang, Michael Broughton, Masoud Mohseni, Ryan Babbush, Sergio Boixo, Hartmut Neven, and Jarrod R McClean. Power of data in quantum machine learning. Nature communications, 12(1):2631, 2021. 1[16] Xiang Huang, Liang Zhao, and Yan Xu. Quantum secure multi- party computation for federated learning. Quantum Computing Journal, 16:145- 160, 2022. 1, 2[17] Nouhaila Innan, Muhammad Al- Zafar Khan, Alberto Marchisio, Muhammad Shafique, and Mohamed Bennai.

Fedqnn: Federated learning using quantum neural networks. In 2024 International Joint Conference on Neural Networks (IJCNN), pages 1- 9. IEEE, 2024. 1[18] Samira Ebrahimi Kahou, Xavier Bouthillier, Pascal Lamblin, Caglar Gulcehre, Vincent Michalski, Kishore Konda, Sebastien Jean, Pierre Froumenty, Yann Dauphin, Nicolas Boulanger- Lewandowski, et al. Emonets: Multimodal deep learning approaches for emotion recognition in video. Journal on Multimodal User Interfaces, 10:99- 111, 2016. 2, 8[19] Bernhard Kratzwald, Suzana Ilié, Mathias Kraus, Stefan Feuerriegel, and Helmut Prendinger. Deep learning for affective computing: Text- based emotion recognition in decision support. Decision support systems, 115:24- 35, 2018. 2, 8[20] Hong- Ye Li, Wei Song, and Xin Zhou. Quantum federated learning with secure aggregation. Quantum Information Processing, 20(6):234- 248, 2021. 2[21] YaoChong Li, Yi Qu, Ri- Gui Zhou, and Jing Zhang. Qmlsc: A quantum multimodal learning model for sentiment classification. Information Fusion, page 103049, 2025. 2[22] Paul Pu Liang, Amir Zadeh, Louis- Philippe Morency, and Ruslan Salakhutdinov. Foundations and recent trends in multimodal machine learning: Principles, challenges, and open questions. Foundations and Trends in Machine Learning, 15(3):210- 356, 2022. 2[23] Xiaoxuan Ma, Qingyao Wu, Weihua Wang, Xiaodong He, Tat- Seng Chua, and Jing Jiang. Smil: Multimodal learning with severely missing modality. Proceedings of AAAI, pages 2996- 3003, 2021. 2[24] Brendan McMahan, Eider Moore, Daniel Ramage, Seth Hampson, and Blaise Aguera y Arcas. Communication- efficient learning of deep networks from decentralized data. Proceedings of AISTATS, pages 1273- 1282, 2017. 2[25] Dinh C Nguyen, Ming Ding, Pubudu N Pathirana, Aruna Seneviratne, Jun Li, and H Vincent Poor. Federated learning for internet of things: A comprehensive survey. IEEE Communications Surveys & Tutorials, 23(3):1622- 1658, 2021. 1[26] Fatemeh Noroozi, Marina Marjanovic, Angelina Njegus, Sergio Escalera, and Gholamreza Anbarjafari. Audio- visual emotion recognition in video clips. IEEE Transactions on Affective Computing, 10(1):60- 75, 2017. 2, 8[27] Soohyun Park, Soyi Jung, and Joongheon Kim. Dynamic quantum federated learning for satellite- ground integrated systems using slimmable quantum neural networks. IEEE Access, 2024. 1[28] Hieu Pham, Zihang Dai, and Qizhe et al. Xie. Found in translation: Learning robust joint representations by cyclic translations between modalities. In Proceedings of AAAI, pages 6892- 6899, 2019. 2[29] Atit Pokharel, Shashank Dahal, Pratik Sapkota, and Bhupendra Bimal Chhetri. Electrocardiogram (ecg) based cardiac arrhythmia detection and classification using machine learning algorithms. arXiv preprint arXiv:2412.05583, 2024. 2[30] Atit Pokharel, Pratik Sapkota, Dilip Sapkota, Shashank Dahal, Sulav Karki, and Ram Kaji Budhathoki. Performance evaluation of lora technology for rural connectivity: An experimental analysis in nepal. arXiv preprint arXiv:2412.04563, 2024. 1

[31] Soujanya Poria, Erik Cambria, Rajiv Bajpai, and Amir Hussain. A review of affective computing: From unimodal analysis to multimodal fusion. Information Fusion, 37:98- 125, 2017. 2[32] Cheng Qiao, Mianjie Li, Yuan Liu, and Zhihong Tian. Transitioning from federated learning to quantum federated learning in internet of things: A comprehensive survey. IEEE Communications Surveys & Tutorials, 2024. 1[33] Zhiguo Qu, Yang Li, and Prayag Tiwari. Qnmf: A quantum neural network based multimodal fusion system for intelligent diagnosis. Information Fusion, 100:101913, 2023. 2, 8[34] Zhiguo Qu, Zhixiao Chen, Shahram Dehdashti, and Prayag Tiwari. Qfsm: a novel quantum federated learning algorithm for speech emotion recognition with minimal gated unit in 5g iov. IEEE Transactions on Intelligent Vehicles, 2024. 8[35] Zhiguo Qu, Yunyi Meng, Ghulam Muhammad, and Prayag Tiwari. Qmfind: A quantum multimodal fusion- based fake news detection model for social media. Information Fusion, 104:102172, 2024. 2[36] Ratun Rahman and Dinh C Nguyen. Improved modulation recognition using personalized federated learning. IEEE Transactions on Vehicular Technology, 2024. 2[37] Ratun Rahman and Dinh C Nguyen. Multimodal federated learning with model personalization. In OPT 2024: Optimization for Machine Learning, 2024. 2[38] Ratun Rahman, Neeraj Kumar, and Dinh C Nguyen. Electrical load forecasting in smart grid: A personalized federated learning approach. arXiv preprint arXiv:2411.10619, 2024. 1[39] Ratun Rahman, Pablo Moriano, Samee U Khan, and Dinh C Nguyen. Electrical load forecasting over multihop smart metering networks with federated learning. arXiv preprint arXiv:2502.17226, 2025. 1[40] Deepak Ranga, Aryan Rana, Sunil Prajapat, Pankaj Kumar, Kranti Kumar, and Athanasios V Vasilakos. Quantum machine learning: Exploring the role of data encoding techniques, challenges, and future directions. Mathematics, 12 (21):3318, 2024. 3[41] Md Kaykobad Reza, Ashley Prater- Bennette, and M Salman Asif. Robust multimodal learning with missing modalities via parameter- efficient adaptation. IEEE Transactions on Pattern Analysis and Machine Intelligence, 2024. 1[42] Maria Schuld, Alex Bocharov, Krysta Svore, and Nathan Wiebe. Circuit- centric quantum classifiers. Physical Review A, 101(3):032308, 2019. 2[43] Sangat Sharma, Suresh Basnet, and Raju Khanal. Implementation of error correction on ibm quantum computing devices. Journal of Nepal Physical Society, 8(1):7- 15, 2022. 5[44] Yujun Shi, Jian Liang, Wenqing Zhang, Vincent YF Tan, and Song Bai. Towards understanding and mitigating dimensional collapse in heterogeneous federated learning. arXiv preprint arXiv:2210.00226, 2022. 1[45] Kevin Singh, Conor E Bradley, Shraddha Anand, Vikram Ramesh, Ryan White, and Hannes Bernien. Mid- circuit correction of correlated phase errors using an array of spectator qubits. Science, 380(6651):1265- 1269, 2023. 5

[46] Ryuji Takagi, Suguru Endo, Shintaro Minagawa, and Mile Gu. Fundamental limits of quantum error mitigation. npj Quantum Information, 8(1):114, 2022. 5[47] Du Tran, Lubomir Bourdev, Rob Fergus, Lorenzo Torresani, and Manohar Paluri. Learning spatiotemporal features with 3d convolutional networks. In Proceedings of ICCV, pages 4489- 4497, 2017. 2[48] Md Raihan Uddin, Ratun Rahman, and Dinh C Nguyen. False data injection attack detection in edge- based smart metering networks with federated learning. arXiv preprint arXiv:2411.01313, 2024. 2[49] Ashish Vaswani, Noam Shazeer, and Niki et al. Parmar. Attention is all you need. In Advances in Neural Information Processing Systems (NeurIPS), pages 5998- 6008, 2017. 2[50] Hu Wang, Yuanhong Chen, Congbo Ma, Jodie Avery, Louise Hull, and Gustavo Carneiro. Multi- modal learning with missing modality via shared- specific feature modelling. In Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition, pages 15878- 15887, 2023. 3[51] David Wierichs, Josh Izaac, Cody Wang, and Cedric Yen- Yu Lin. General parameter- shift rules for quantum gradients. Quantum, 6:677, 2022. 4[52] Amir Zadeh, Minghai Chen, Soujanya Poria, Erik Cambria, and Louis- Philippe Morency. Tensor fusion network for multimodal sentiment analysis. In Proceedings of EMNLP, pages 1103- 1114, 2017. 2[53] AmirAli Bagher Zadeh, Paul Pu Liang, Soujanya Poria, Erik Cambria, and Louis- Philippe Morency. Multimodal language analysis in the wild: Cma- mosai dataset and interpretable dynamic fusion graph. In Proceedings of the 56th Annual Meeting of the Association for Computational Linguistics (Volume 1: Long Papers), pages 2236- 2246, 2018. 5[54] Hongli Zhang, Alireza Jolfaei, and Mamoun Alazab. A face emotion recognition method using convolutional neural network and image edge computing. IEEE Access, 7:159081- 159089, 2019. 2, 8[55] Fei Zhao, Chengcui Zhang, and Baocheng Geng. Deep multimodal data fusion. ACM computing surveys, 56(9):1- 36, 2024. 1[56] Liang Zhao, Zhi- Hao Li, and Tian Wang. Quantum- enhanced aggregation for federated learning. Quantum Information Science, 9:56- 74, 2021. 1[57] Jin Zheng, Qing Gao, Daoyi Dong, Jinhu Lu, and Yue Deng. A quantum multimodal neural network model for sentiment analysis on quantum circuits. IEEE Transactions on Artificial Intelligence, 2024. 2