# Abstract

Medical multimodal federated learning is an emerging paradigm enabling collaborative healthcare AI development across multiple medical institutions while preserving patient privacy and ensuring regulatory compliance. However, current approaches face critical trustworthiness challenges that extend beyond traditional privacy concerns, including sophisticated cross-modal cascade threats where compromising one medical modality can progressively affect others, potentially leading to life-threatening consequences in clinical decision-making. Existing methods predominantly focus on single-dimension optimization, treating trustworthiness aspects such as privacy, security, robustness, fairness, and explainability in isolation, failing to recognize their complex interdependencies and creating fundamental tensions that compromise overall system reliability. To address these challenges, we introduce TrustGuard, a comprehensive multi-dimensional trustworthiness framework specifically designed for medical multimodal federated learning. TrustGuard features three core innovations: the Adaptive Multi-dimensional Trustworthiness Coupling (AMTC) mechanism that mathematically models dynamic coupling relationships among five trustworthiness dimensions, the Predictive Adaptive Defense (PAD) framework that provides proactive defense against cross-modal cascade threats through our novel Medical Cross-modal Attack Graph (Medical CMAG), and the Dynamic Coupling-aware Optimization (DCAO) algorithm that ensures convergence and stability in dynamic medical environments. Extensive experiments on medical datasets demonstrate that TrustGuard achieves 23.7% improvement in multi-dimensional trustworthiness scores while maintaining 94.2% model utility, significantly outperforming state-of-the-art baselines and providing HIPAA/GDPR-compliant solutions for real-world clinical applications.

医疗多模态联邦学习是一个新兴范式，能够在保护患者隐私和确保法规合规的同时，实现跨多个医疗机构的协作医疗AI开发。然而，当前方法面临超越传统隐私关注的关键可信性挑战，包括复杂的跨模态级联威胁，其中一个医疗模态的妥协可能逐步影响其他模态，在临床决策中可能导致危及生命的后果。现有方法主要关注单维度优化，孤立地处理隐私、安全、鲁棒性、公平性和可解释性等可信性方面，未能识别其复杂的相互依赖关系，创造了损害整体系统可靠性的根本张力。为了解决这些挑战，我们引入了TrustGuard，这是一个专门为医疗多模态联邦学习设计的综合多维度可信性框架。TrustGuard具有三个核心创新：数学建模五个可信性维度间动态耦合关系的自适应多维度可信性耦合(AMTC)机制、通过我们新颖的医疗跨模态攻击图(Medical CMAG)提供跨模态级联威胁主动防御的预测性自适应防御(PAD)框架，以及确保动态医疗环境中收敛性和稳定性的动态耦合感知优化(DCAO)算法。在医疗数据集上的广泛实验表明，TrustGuard在保持94.2%模型效用的同时，多维度可信性分数提高了23.7%，显著优于最先进的基线方法，并为现实世界临床应用提供符合HIPAA/GDPR的解决方案。

Code — https://github.com/TrustGuard-Medical/TrustGuard.