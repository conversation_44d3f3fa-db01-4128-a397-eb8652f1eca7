# VTarbel: Targeted Label Attack with Minimal Knowledge on Detector-enhanced Vertical Federated Learning

JUNTAO TAN, University of Science and Technology of China, China  ANRAN LI, University of Science and Technology of China, China  QUANCHAO LIU, Department of Security Technology Research, China Mobile Research Institute, China  PENG RAN, Department of Security Technology Research, China Mobile Research Institute, China  LAN ZHANG, University of Science and Technology of China, China

Vertical federated learning (VFL) enables multiple parties with disjoint features to collaboratively train models without sharing raw data. While privacy vulnerabilities of VFL are extensively- studied, its security threats—particularly targeted label attacks—remain underexplored. In such attacks, a passive party perturbs inputs at inference to force misclassification into adversary- chosen labels. Existing methods rely on unrealistic assumptions (e.g., accessing VFL model's outputs) and ignore anomaly detectors deployed in real- world systems. To bridge this gap, we introduce VTarbel, a two- stage, minimal- knowledge attack framework explicitly designed to evade detector- enhanced VFL inference. During the preparation stage, the attacker selects a minimal set of high- expressiveness samples (via maximum mean discrepancy), submits them through VFL protocol to collect predicted labels, and uses these pseudo- labels to train estimated detector and surrogate model on local features. In attack stage, these models guide gradient- based perturbations of remaining samples, crafting adversarial instances that induce targeted misclassifications and evade detection. We implement VTarbel and evaluate it against four model architectures, seven multimodal datasets, and two anomaly detectors. Across all settings, VTarbel outperforms four state- of- the- art baselines, evades detection, and retains effective against three representative privacy- preserving defenses. These results reveal critical security blind spots in current VFL deployments and underscore urgent need for robust, attack- aware defenses.

CCS Concepts:  $\cdot$  Security and privacy  $\rightarrow$  Distributed systems security  $\cdot$  Computing methodologies  $\rightarrow$  Distributed artificial intelligence.

Additional Key Words and Phrases: Vertical Federated Learning, Targeted Label Attack, Adversarial Attack, Security Risk

# ACM Reference Format:

Juntao Tan, Anran Li, Quanchao Liu, Peng Ran, and Lan Zhang. 2025. VTarbel: Targeted Label Attack with Minimal Knowledge on Detector- enhanced Vertical Federated Learning. ACM Trans. Sensor Netw. 1, 1 (July 2025), 30 pages. https://doi.org/10.1145/nnnnnnn.nnnnnnn

# 1 INTRODUCTION

Vertical Federated Learning (VFL) is a decentralized machine learning (ML) paradigm that enables collaborative model training among multiple participants, each holding distinct, non- overlapping

feature sets, without sharing their raw data [20, 23, 29, 34, 47, 51, 52]. Typically, one participant, referred to as the active party, possesses the ground- truth labels, while the others, referred to as passive parties, contribute only raw features. VFL has gained traction in real- world applications such as financial risk assessment [5], healthcare diagnostics [52], and personalized advertising [30].

To protect data privacy, VFL protocols restrict information exchange to intermediate outputs such as feature embeddings<sup>1</sup> and embedding gradients. Despite these protections, recent studies have shown that VFL is still vulnerable to privacy leakage via two main types of attacks. Feature reconstruction attacks allow the active party to recover the passive parties' original features from the shared embeddings [11, 12, 18, 21, 36, 40, 48], while label inference attacks enable malicious passive parties to deduce private labels using backward gradients or their locally trained models [10, 22, 28, 45, 46, 54].

Despite growing attention to privacy breaches in VFL, its robustness against adversarial perturbations remain underexplored. A particularly critical but overlooked threat is targeted label attacks, in which a passive party (the attacker) deliberately crafts malicious input samples to mislead the VFL model into producing attacker- specified misclassifications. For instance, an adversary in a healthcare VFL system could manipulate diagnostic outputs to misguide treatments. Such attacks generally fall into two categories: (1) Training- phase backdoor attacks [2, 32, 38], where attackers poison training data to implant hidden triggers, once activated during inference, cause targeted misclassifications. (2) Inference- phase adversarial attacks [15, 17, 33, 39, 42], where attackers directly perturb test inputs to manipulate model predictions without modifying the training process (as illustrated in Figure 1). While both types pose security risks, backdoor attacks often degrade overall model performance due to the presence of poisoned samples, potentially alerting the active party (the defender) and lead to the early termination of the collaboration. In contrast, adversarial attacks operate stealthily without altering training data, which significantly increases the difficulty of detection. Given their practicality and stealthiness, this work focuses on inference- phase targeted label attacks (hereafter refer to as targeted label attacks for brevity).

Designing effective targeted label attacks in VFL during inference is highly challenging due to three key factors. First, the attacker typically operates in a black- box setting without access to the defender's model architecture, parameters, or outputs, which severely limits their ability to manipulate predictions. Second, the attacker controls only a subset of the features, which means that adversarial perturbations may be mitigated by benign features contributed by other participants. Third, adversarial samples may be detected by anomaly detection mechanisms deployed by the defender to monitor input distributions.

Several recent studies have proposed attack strategies to address these challenges. For example, Gu et al. [15] and Qiu et al. [42] leverage auxiliary labeled datasets to train auxiliary models, which then generate adversarial embeddings by aligning them with target- class representations. Pang et al. [39] assume the attacker has access to test samples from other participants as well as the VFL model's outputs, allowing iterative optimization of adversarial inputs. He et al. [17] propose using labeled target- class samples to extract representative embeddings that are injected into test samples to mislead predictions. However, existing targeted label attacks have critical limitations in practical applications. (1) Unrealistic adversarial knowledge: Many proposed methods rely on strong assumptions, such as access to labeled datasets, global model outputs, or test samples from other participants, which do not hold in real VFL scenarios. In practice, labels are proprietary assets exclusively held by the active party and are never shared with passive parties [5]. (2) Neglect of anomaly detection: These approaches often ignore the active party's ability to deploy anomaly detectors, e.g., statistical or ML- based, to identify and filter out suspicious embeddings. As a

result, the adversarial feature embeddings crafted by attackers are often easily detected, severely compromising attack effectiveness.

To address these issues, we propose a practical and stealthy targeted label attack framework that operates under minimal adversarial knowledge and remains effective against VFL systems equipped with anomaly detectors. Our design is motivated by empirical observations: overly optimized feature embeddings produced by prior methods tend to deviate significantly from the benign distribution, making them highly detectable and thereby diminishing attack success rates (ASR), as demonstrated in our experimental results (see Section 4.1). Based on this insight, we introduce a two- stage attack paradigm. In the preparation stage, the attacker follows the standard VFL inference protocol to gather essential knowledge, such as estimating the defender's anomaly detection boundaries. In the attack stage, the attacker leverages this knowledge to craft adversarial inputs that both induce targeted misclassifications and remain within the normal range of detector- accepted embeddings, effectively evading detection.

We formally formulate the targeted label attack as a combinatorial optimization problem: selecting an optimal subset of test samples that maximizes the number of predictions assigned to a specific targeted label. However, solving this NP- hard problem is computationally infeasible. To address this, we propose VTarbel (VFL Inference Targeted Label Attack), a novel and practical two- stage attack framework designed to operate under realistic VFL constraints. VTarbel strategically minimizes the use of benign samples in the preparation stage, reserving the majority of test samples for adversarial crafting during the attack stage. In preparation, the attacker selects a small but representative samples set of benign samples based on maximum mean discrepancy (MMD), ensuring high expressiveness and diversity. These samples are submitted to the VFL system following standard inference protocol. The corresponding predicted labels are used as pseudo- labels to locally train two critical components: (1) an estimated anomaly detector that approximate the defender's detection logic, and (2) a surrogate model that mimics the behavior of the global VFL model. In the attack stage, the attacker applies gradient- based optimization to perturb the remaining test samples. These perturbations are guided by predictions from the surrogate model and constrained by the estimated detector, ensuring that the crafted adversarial samples both induce targeted misclassifications and evade detection. By leveraging the transferability of adversarial examples, the attacker can effectively bypass the defender's detector and manipulate predictions within the VFL system.

Our main contributions are summarized as follows.

- Formalizing targeted label attack as a combinatorial optimization problem. We formalize the targeted label attack in VFL as a combinatorial optimization task, aiming to maximize the number of test samples misclassified into an attacker-specified label. To enable this, we propose a novel inference-phase partitioning that separates the attack process into two sub-stages.- Two-stage attack framework with minimal adversarial knowledge. We introduce VTarbel, a detector-agnostic two-stage attack paradigm: (1) a preparation stage that minimizes the required samples to train expressive estimated detector and surrogate model, and (2) an attack stage that generates transferable malicious samples through coordinated optimization of these two models, enabling effective attacks against VFL systems.- Extensive evaluations and robustness to defenses. We conduct extensive evaluations across four model architectures (MLP3, VGG13, ResNet18, DistilBERT), seven multimodal datasets, and two anomaly detectors. Results demonstrate that VTarbel consistently outperforms four state-of-the-art attacks (e.g., achieving  $90.39\%$  ASR on VGG13 vs.  $\leq 49.62\%$  for baselines). Furthermore, we demonstrate VTarbel's robustness against various defense mechanisms—ASR is reduced by  $< 81.8\%$  only at the cost of  $>82.3\%$  inference accuracy degradation.

![](images/4639e0a228255a334be5ceb15c43d17f0c4492262b0c16a4d224673c05a99105.jpg)  
Fig. 1. Illustration of a detector-enhanced VFL inference system. A malicious passive party  $(P_{1})$  submits an adversarial feature embedding  $(e_{1})$  to the active party  $P_{K}$ . The active party employs an anomaly detector  $\phi$  to assess whether the aggregated embedding  $E$  is anomalous. Based on the detector's output, the final prediction is either approved  $(\hat{y})$  or rejected (REJ).

# 2 BACKGROUND

# 2.1 Detector-enhanced VFL Inference System

A detector- enhanced VFL inference system, as depicted in Figure 1, comprises  $K$  participants  $P_{1},P_{2},\ldots ,P_{K}$ . We assume  $P_{K}$  serves as the only active party, while the remaining  $K - 1$  participants act as passive parties. Each participant  $P_{k}$ $(k\in [K]$ , where  $[K] = \{1,2,\ldots ,K\})$  possesses a private test dataset  $D_{k} = \{X_{k}^{i}\}_{i = 1}^{N}$ , where  $X_{k}^{i}$  denotes the feature vector of the  $i$ - th test sample for  $P_{k}$ , and  $N$  represents the total number of test samples.

Following VFL training, the VFL model  $F(\theta)$  is parameterized as  $F(\theta_{1},\theta_{2},\ldots ,\theta_{K},\theta_{T})$ . Here,  $\theta_{1},\theta_{2},\ldots ,\theta_{K}$  correspond to the bottom model parameters of individual participants, and  $\theta_{T}$  represents the top model parameters managed by the active party  $P_{K}$ . During inference, each participant  $P_{k}$  computes a feature embedding  $e_{k} = f(X_{k};\theta_{k})$  by processing its raw feature vector  $X_{k}$  through its bottom model  $f(\theta_{k})$ . These embeddings are transmitted to  $P_{K}$ , which aggregates them using a function  $\operatorname {Agg}(\cdot)$  (e.g., summation or concatenation) to produce a combined embedding  $E = \operatorname {Agg}(e_{1},e_{2},\ldots ,e_{K})$ .

In a conventional VFL system (without defenses),  $E$  is directly input to the top model  $g(\theta_{T})$  to generate the final prediction  $\hat{y} = g(E;\theta_{T})$ , where  $\hat{y}\in [C]$  and  $C$  is the number of classes. This prediction is then returned to all passive parties. However, practical deployments face risks from malicious passive parties (e.g.,  $P_{1}$  in Figure 1) that may submit adversarial embeddings (e.g.,  $e_{1}$ ) to manipulate predictions via targeted label attacks. To mitigate this, the detector- enhanced system introduces an anomaly detector  $\phi (\cdot)$  at  $P_{K}$ . After computing  $\hat{y}$ , the system evaluates  $\phi (E,\hat{y})$ . If  $E$  is flagged as anomalous, the detector triggers a rejection by returning the special label REJ to all participants; otherwise,  $\hat{y}$  is returned as usual. For consistency, we treat REJ as an additional class, extending the total number of classes to  $C + 1$ .

In summary, the detector- enhanced VFL system's prediction is denoted as  $\hat{y} = F(X;\theta)$ , where  $X = (X_{1},X_{2},\ldots ,X_{K})$ ,  $F(\cdot)$  combines the VFL model and the detector  $\phi (\cdot)$ , and  $\hat{y}\in [C + 1]$  includes both the original classes and the REJ label.

# 2.2 Anomaly Detection

Anomaly detection (AD) refers to techniques for identifying data patterns that significantly deviate from expected normal behavior. These methods fall into two broad categories: non- parametric and

parametric approaches. This section details two prominent AD techniques: the non- parametric kernel density estimation (KDE) [6] and the parametric deep autoencoder (DeepAE) [4].

Kernel Density Estimation. KDE is a non- parametric method that estimates the probability density of data instances. Let  $X = \{x_{1},x_{2},\ldots ,x_{n}\}$  denotes  $n$  independent and identically distributed observations in  $\mathbb{R}^d$ , where  $d$  is the feature dimension. For any test instance  $x\in \mathbb{R}^d$ , the KDE- based detector computes:

$$
\phi (x) = \frac{1}{nh}\sum_{i = 1}^{n}\mathcal{K}\left(\frac{\|x - x_i\|}{h}\right), \tag{1}
$$

where  $h > 0$  is the bandwidth parameter controlling smoothing intensity, and  $\mathcal{K}(\cdot)$  is a kernel function. A common choice is the radial basis function (RBF) kernel:  $\mathcal{K}(z) = \exp (z^2)$ , where  $\| x - x_i\|$  represents the Euclidean distance between  $x$  and  $x_{i}$ . Eq.(1) operates by constructing localized density contributions around each observation  $x_{i}$ , then aggregating these contributions to estimate the density of  $x$ .

Deep Autoencoder. DeepAE is a deep- learning- based parametric AD method that consisting of two neural networks: an encoder  $E(\theta_{e})$  that compresses input data  $x\in \mathbb{R}^d$  into a low- dimensional latent vector, and a decoder  $D(\theta_d)$  that reconstructs the input as  $x^{\prime}\in \mathbb{R}^{d}$  from this latent representation. Here,  $\theta_{e}$  and  $\theta_d$  denote the trainable parameters of the encoder and decoder, respectively. DeepAE models are trained on the observation set  $X$  in an unsupervised learning manner, using the loss function  $\begin{array}{r}\mathcal{L} = \frac{1}{n}\sum_{i = 1}^{n}\| x_i - x_i^*\| ^2 \end{array}$ , where  $x_{i}^{\prime} = D(E(x_{i};\theta_{e});\theta_{d})$ . After training, for a test instance  $x$ , DeepAE computes:

$$
\phi (x) = \| x - D(E(x;\theta_e);\theta_d)\| ^2, \tag{2}
$$

which quantifies the reconstruction error between the input  $x$  and its decoded output  $x^{\prime}$ . Higher scores indicate greater deviation from normal patterns and a higher likelihood of anomaly.

Anomaly Score. For both KDE and DeepAE, an instance  $x$  is detected as an anomaly if its anomaly score  $\phi (x)$  exceeds a predefined threshold  $\tau$ :

$$
\phi (x)\geq \tau . \tag{3}
$$

Label- Aware Detection. In supervised settings where each observation  $x_{i}$  is associated with a label  $y_{i}\in [C]$  (e.g.,  $C$ - class classification), more accurate anomaly detection can be achieved by utilizing label- specific sub- detectors. The detector  $\phi (\cdot)$  is then composed of a set of sub- detectors  $\{\phi_1,\phi_2,\ldots ,\phi_C\}$ , where each sub- detector  $\phi_c$  is trained on observations from  $X$  corresponding to label  $c$ . For an input instance  $x$  with true label  $y$ , the anomaly score  $\phi_y(x)$  in computed using the  $y$ - specific sub- detector. This label- aware approach ensures higher detection accuracy by leveraging class- specific data distributions.

# 2.3 Targeted Label Attack

A targeted label attack during the inference phase is a type of adversarial attack where an attacker introduces imperceptible perturbations to an original sample. The goal is to mislead the trained model into classifying the perturbed sample as a specific, targeted label.

Formally, let  $f(\theta)$  be a well- trained deep learning model, and let  $x$  denote an non- target test sample. The attacker's objective can be formulated as:

$$
x^{\prime} = x + \delta \quad \mathrm{s.t.}\quad f(x^{\prime};\theta) = y^{*},f(x^{\prime};\theta)\neq f(x;\theta),\mathrm{and}L_{p}(\delta)\leq \epsilon , \tag{4}
$$

where  $\delta$  is the perturbation added to the original sample  $x$ ,  $y^{*}$  is the targeted label chosen by the attacker,  $f(x;\theta)$  represents the model's prediction for input  $x$ ,  $L_{p}(\delta)$  denotes the  $L_{p}$ - norm, quantifying the perturbation magnitude, and  $\epsilon$  is an upper bound on  $\delta$ , ensuring that it remains imperceptible to humans.

The attacker's goal is to craft a perturbation  $\delta$  that satisfies these constraints while minimizing its magnitude. This forces the model's prediction to shift from the original label  $f(x; \theta)$  to the targeted label  $y^{*}$ . By carefully designing such a perturbation  $\delta$  within the defined limit  $\epsilon$ , the attacker ensures that  $x'$  remains visually indistinguishable from  $x$  while successfully misleading the model's classification.

# 3 PROBLEM DEFINITION

# 3.1 Threat Model

Attacker. This work focuses exclusively on targeted label attacks occurring during the inference phase, explicitly excluding any attacks or adversarial behaviors during the training phase. We assume that all participants adhere honestly to the VFL training protocol for building accurate and reliable VFL model. However, during the inference phase, we consider that a passive party may be compromised by an attacker with malicious intent to perform a targeted label attack. The attacker's goals, knowledge, and capabilities are defined as follows:

- Attacker's goal. The attacker seeks to deceive the trained VFL model into predicting a specific, targeted label for any given test sample by injecting maliciously crafted features during the inference phase. This attack is well-motivated in real-world applications. For example, in a credit risk assessment scenario, the attacker has strong incentives to manipulate the VFL model's prediction for specific test samples (e.g., corresponding to individual users) from "high risk" to "low risk" to obtain loans from a financial institution.- Attacker's knowledge. The attacker operates with minimal prior knowledge, consistent with the standard setup of a VFL inference system. Specifically, the attacker knows VFL task type (e.g., classification or regression) and the total number of classes. However, the attacker does not have access to the local datasets of other participants (e.g., their size or distribution) or to the architecture and parameters of their bottom models. Unlike prior works that assume stronger (and often unrealistic) adversarial knowledge [15, 17, 39, 42], we assume that the attacker has no access to labeled auxiliary samples from either the training or test sets, and cannot access the outputs of the active party's top model (such as logits or probabilities). Additionally, the attacker is unaware of the type of detector deployed by the active party (e.g., KDE or DeepAE). This restricted adversarial knowledge assumption reflects a more realistic threat model, making our approach more applicable to practical scenarios compared to previous works.- Attacker's capability. The attacker is capable of collecting the predicted label for each test sample. If the predicted label is not the special label REJ, the attacker can treat it as a pseudo-label for the corresponding test sample. Furthermore, the attacker can manipulate their own feature embeddings or raw feature vectors, but these manipulations are constrained to ensure that the modified features remain within valid ranges. For example, in an image dataset, each pixel value in a manipulated image must lie within the range [0, 255]. However, the attacker cannot alter the feature embeddings, raw feature vectors, or bottom model parameters of other participants. Their influence is strictly limited to their own contributions within the VFL system.

Defender. In this work, we also consider the threat model for the defender (the active party). Goal: The defender's objective is to accurately detect and filter out potentially malicious feature embeddings contributed by a compromised passive party during the VFL inference phase. The defender then returns the special REJ label to all participants to indicate the presence of malicious activity. Knowledge: The defender is unaware of which passive party has been compromised by the attacker. Furthermore, the defender does not have access to the attacker's private test set or to the

structure and parameters of the attacker's trained bottom model. The defender can only access the feature embeddings received from each passive party. Capability: During the VFL training phase, the defender treats the benign feature embeddings provided by each passive party as observations for constructing anomaly detectors. In the inference phase, the defender uses these constructed anomaly detectors to evaluate the aggregated feature embeddings and identify whether any of them are malicious.

# 3.2 Problem Formulation

As introduced in Section 2.1, a VFL inference system involves  $K$  participants. For clarity, these participants are categorized into three types: the attacker  $P_{adv}$ , the defender (active party)  $P_{ap}$ , and the remaining normal and honest passive parties  $P_{nor}$ . The corresponding feature vectors provided by these participants are denoted as  $X_{adv}$ ,  $X_{ap}$ , and  $X_{nor}$ , respectively. During the VFL inference phase, the attacker aims to manipulate their feature vector to mislead the VFL model, causing it to predict as many test samples as possible into the targeted label. To quantitatively evaluate the effectiveness of the attack, we define the metric Attack Success Rate (ASR).

DEFINITION 1 (ATTACK SUCCESS RATE). In a detector- enhanced VFL inference system, given the global model  $F(\cdot)$  with parameters  $\theta$ , and a test set consisting of  $N$  samples, the attack success rate is defined as:

$$
s(\mathcal{A}) = \frac{\sum_{i = 1}^{N}\mathbb{I}(F(\mathcal{A}(X_{adv}^i,\theta_{adv}),X_{nor}^i,X_{ap}^i;\pmb{\theta}) = t^{\star})}{N}, \tag{5}
$$

where  $\theta_{adv}$  represents the attacker's bottom model parameters,  $t^{\star}$  is the attacker's targeted label,  $\mathcal{A}(\cdot)$  denotes the attack algorithm, and  $\mathbb{I}(\cdot)$  is an indicator function that equals 1 if the condition inside is satisfied, and 0 otherwise.

Specifically, the attack algorithm  $\mathcal{A}(\cdot)$  generates a maliciously crafted sample  $X_{adv}^{*}$  from an original benign sample  $X_{adv}$ . This process is subject to the following constraint:

$$
X_{adv}^{*} = \mathcal{A}(X_{adv};\theta_{adv})\quad \mathrm{s.t.}\quad dist(X_{adv}^{*},X_{adv})\leq r_{max}, \tag{6}
$$

where  $dist(\cdot)$  measures the distance between the benign sample  $X_{adv}$  and the crafted sample  $X_{adv}^{*}$  and  $r_{max}$  is a predefined threshold. This constraint ensures: (1) the feature values of the crafted sample remain within a valid range, as discussed in Section 3.1, and (2) the crafted sample avoids detection by anomaly detectors.

As noted in Section 2.1, the output of  $F(\cdot)$  includes the special label REJ. If the prediction of  $F(\cdot)$  is REJ, which differs from the attacker's targeted label  $t^{\star}$ , the attack on this test sample is considered unsuccessful. Therefore, the objective of this paper is to design an effective attack algorithm  $\mathcal{A}$  that requires minimal adversarial knowledge and maximizes the ASR in a detector- enhanced VFL inference system.

# 4 OBSERVATIONS AND MAIN IDEA OF VTARBEL

In this section, we conduct exploratory experiments to assess the performance of several representative prior attack methods. Our findings reveal that these attacks are largely ineffective when anomaly detectors are employed on the defender's side. We further analyze the underlying causes of this limitation and introduce the core ideas behind the design of our attack framework, VTarbel.

# 4.1 Observations

We examine the effectiveness of three previous widely recognized attack methods—LR- BA [15], HijackVFL [42], and ADI [39]—in a setting where the defender either has or has not deployed anomaly detectors. These methods assume that the attacker can obtain labeled training samples,

![](images/4cc94fda0f2195403710a28a9136fb96384bb39d6140c4d13c617862a424f5e3.jpg)  
Fig. 2. Impact of anomaly detector on ASR in VFL inference system. The "Ground-Truth" represents the proportion of the targeted label in the original test set, serving as a baseline for comparison.

which are then used to train an auxiliary model. The attacker subsequently utilizes optimization algorithms to generate malicious feature vectors for attacking based on this auxiliary model. A detailed description of these methods is provided in Section 7.2.

Our experiments are conducted within a two- party VFL inference system, where the passive party is compromised by the attacker and the active party acts as the defender, employing DeepAE as the anomaly detector. We evaluate three different bottom model architectures: MLP3, VGG13 [44], and ResNet18 [16], across two datasets: TabFMNIST [50] and CIFAR10 [26]. Without loss of generality, we set the targeted label  $t^\star = 0$  for both datasets. The experimental results are presented in Figure 2, which reveals two key observations.

Observation 1: High ASR without detectors. As shown in Figure 2, for all three attack methods, the ASR is significantly higher in the absence of anomaly detectors compared to the "Ground- Truth" case. For instance, in the configuration (MLP3, TabFMNIST), the ASR increases from  $10.00\%$  in the "Ground- Truth" case to  $29.39\%$ $83.68\%$  and  $97.81\%$  for LR- BA, HijackVFL, and ADI, respectively. These results indicate that the feature vectors, maliciously optimized against the attacker's local auxiliary model, effectively transfer to the global VFL model.

Observation 2: Significant drop in attack performance with detectors. Figure 2 also shows a drastic decline in ASR when detectors are deployed by the defender. In some cases, the ASR even falls to zero, which is even lower than the baseline "Ground- Truth" performance. For instance, in the (ResNet18, CIFAR10) configuration, the ASR for LR- BA decreases from  $91.40\%$  (without detector) to  $31.84\%$  (with detector). This drop is due to the anomaly detector recognizing many malicious feature embeddings as anomalies, causing the global VFL model to predict the special rejection label REJ. In other configurations, such as (VGG13, CIFAR10), the ASRs for LR- BA and HijackVFL with detectors are  $0.04\%$  and 0, respectively—significantly lower than the  $10.0\%$  achieved in the "Ground- Truth" case. This highlights the ineffectiveness of these attacks in practical VFL scenarios where anomaly detectors are deployed.

These results suggest that existing targeted attack methods fail to perform effectively when anomaly detectors are in place to filter out malicious feature embeddings. This motivates us to develop more robust attack strategies capable of manipulating VFL predictions to target labels while evading detection.

# 4.2 Main Idea of VTarbel

We begin by analyzing why previous attack methods are ineffective. While these methods leverage an auxiliary model to optimize the feature vector or embedding, aiming to enhance its predictive capability for the targeted label or reduce the contributions of other benign participants, the

malicious inputs generated are overly optimized. Since these inputs are rarely observed in the training set and fall outside the normal feature space, they have a low probability of occurring and can be easily identified as anomalies by the defender's detector, even under a loose detection threshold. As a result, most of these overly optimized inputs are rejected by the global VFL model, leading to the observed low ASR.

To overcome these limitations, we propose VTarbel, a novel two- stage attack framework. The main idea is to partition the VFL inference phase into two substages: the first stage focuses on collecting essential knowledge, while the second stage executes the actual targeted label attack. We elaborate on the main idea below.

Leveraging the first stage to estimate detectors and train a surrogate model. In the first stage, the attacker honestly follows the VFL inference protocol and collects the returned predicted labels of benign test samples. Using these predicted labels, the attacker can: (1) estimate the defender's detectors. In ML, it is generally assumed that the training and test datasets share the same distribution. As discussed in Section 3.1, the defender's detectors are constructed based on the benign feature embeddings from the passive parties during the VFL training phase. By leveraging benign feature embeddings from test samples, the attacker can construct approximate detectors (which do not need to be identical to the defender's) to estimate the defender's ground- truth detectors. (2) train a surrogate model using local features. Inspired by the model completion attack proposed by Fu et al. [10], the attacker can append an inference head model (with the same output dimension as the top model) to its well- trained bottom model after VFL training, thereby constructing a surrogate model. Using the predicted labels and local features, the attacker can build a labeled attack dataset and fine- tune the surrogate model to achieve high accuracy. This well- performing surrogate model assists the attacker in generating more effective malicious feature embeddings.

Conducting the actual attack in the second stage. After the first stage, the defender's detectors are well- estimated, and the surrogate model is well- trained. In the second stage, the attacker maliciously deviates from the VFL inference protocol and launches the targeted label attack. Using the surrogate model and the estimated detectors, the attacker optimizes the raw feature vectors to ensure the surrogate model predicts them as the targeted label with high confidence. Simultaneously, the attacker ensures that the malicious feature embeddings are not flagged as anomalies by the estimated detectors (e.g., by keeping the anomaly score sufficiently low). Once the feature vectors are sufficiently optimized (i.e., the loss is minimized or the iteration limit is reached), the attacker transfers the corresponding embeddings to the active party for VFL inference.

In summary, we propose a novel two- stage attack framework, VTarbel, which will be described in detail in the next section. This framework generalizes previous methods, where the "Ground- Truth" case and previous attacks illustrated in Figure 2 are special cases of our framework. Specifically, the "Ground- Truth" corresponds to using only the first stage without performing attacks in the second stage, while previous attacks correspond to cases where only the second stage is applied without the first stage. In Section 6.3, we demonstrate that both first- stage- only and second- stage- only approaches are ineffective, and only our generalized two- stage framework achieves the highest ASR.

# 5 VTARBEL FRAMEWORK DESIGN

In this section, we first re- formulate the problem definition within the context of the two- stage attack framework (Section 5.1). We then provide an overview of the proposed framework, VTarbel (Section 5.2), followed by a detailed explanation of each stage in Sections 5.3 and 5.4.

# 5.1 Two-stage Attack Formulation

In Section 3.2, we define the attack success rate (ASR),  $s(\mathcal{A})$  , and present a generalized formulation of the attack objective: maximizing ASR by designing an effective attack algorithm  $\mathcal{A}$  . In this section, we re- formulate the problem under the two- stage attack framework, providing a more explicit objective function for the attacker.

For the attacker  $P_{adv}$  , it partitions the entire VFL inference phase on its test set  $D_{adv}$  into two substages: a preparation stage and an attack stage. The test samples within the preparation stage, denoted as  $Q\subset D_{adv}$  with  $Q\neq 0$  , form the preparation set. The size of this set, or equivalently, the stage length, is  $|Q|$  . The remaining test samples,  $D_{adv}\vee Q$  , constitute the attack stage, with a corresponding stage length of  $N - |Q|$  , where  $N$  is the total number of test samples in  $D_{adv}$  Building on the ASR definition in Eq.(5), we define the ASR for the preparation and attack stages as  $s_1$  and  $s_2$  , respectively:

$$
s_1(Q) = \frac{\sum_{i = 1}^{|Q|}\mathbb{I}(F(X_{adv}^i,X_{nor}^i,X_{ap}^i;\pmb{\theta}) = t^{\star})}{|Q|}, \tag{7}
$$

$$
s_2(Q,\mathcal{A}) = \frac{\sum_{i = |Q| + 1}^{N}\mathbb{I}(F(\mathcal{A}(X_{adv}^i;\theta_{adv}),X_{nor}^i,X_{ap}^i;\pmb{\theta}) = t^{\star})}{N - |\mathcal{Q}|}. \tag{8}
$$

The attacker's objective can then be formulated as the following optimization problem:

$$
Q^{*} = \underset {Q\in D_{adv},Q\neq \emptyset}{\arg \max}\underbrace{s_{1}(Q)\cdot|Q|}_{\mathrm{precondition~stage}} + \underbrace{s_{2}(Q,\mathcal{A})\cdot(N - |Q|)}_{\mathrm{attack~stage}}. \tag{9}
$$

Main challenge. Directly solving the combinatorial optimization problem in Eq.(9), i.e., finding the optimal preparation set  $Q^{*}$  to maximize the number of test samples predicted as the targeted label, is NP- hard [24]. However, through empirical experiments, we have gained insights into the impact of the preparation stage ratio on both substages. The preparation stage ratio, denoted as  $\begin{array}{r}\rho = \frac{|\mathcal{Q}|}{N} \end{array}$  , represents the proportion of test samples randomly selected from  $D_{adv}$  for the preparation stage.

Insight 1: Assigning an appropriate number of test samples to  $Q$  improves the accuracy of the estimated detector and surrogate model, but the marginal benefits diminish as more samples are added. We conduct experiments on CIFAR10 and TabFMNIST datasets to evaluate the impact of the preparation stage ratio  $\rho$  on the performance of the estimated detector and surrogate model. As shown in Figure 3, increasing  $\rho$  from  $10\%$  to  $30\%$  significantly improves the detector's F1 score, with absolute increases of 0.109 for CIFAR10 and 0.142 for TabFMNIST. However, when  $\rho$  increases from  $30\%$  to  $100\%$  , the improvements are much smaller, with F1 score increases of only 0.021 and 0.068, respectively. For the surrogate model, we find that its accuracy does not improve significantly as  $\rho$  increases. When  $\rho = 10\%$  , the model accuracies for CIFAR10 and TabFMNIST are 0.8622 and 0.7807, respectively. However, when  $\rho$  is increased to  $100\%$  , the corresponding accuracies are 0.8624 and 0.7844, showing negligible improvement. Based on these results, we conclude that constructing a preparation stage of appropriate length is crucial for accurately estimating detector and training surrogate model. However, once sufficient accuracy is reached, further increasing the preparation set size yields only marginal gains.

Insight 2: There is trade- off between the preparation stage length and the attack stage length. We investigate the impact of  $\rho$  on  $s_1,s_2$  , and the overall ASR  $s$  using the ResNet18 model, CIFAR10 dataset, and DeepAE detector. As shown in Figure 4, we observe that  $s_1$  remains stable and constant with respect to the preparation stage length, while  $s_2$  gradually improves as  $\rho$  increases. The stability of  $s_1$  can be attributed to the fact that during the preparation stage, the attacker

![](images/53571b07fe30ea23854f8b5cf2c315c80055b02d84363d96378bf7a2db5fe271.jpg)  
Fig. 3. Impact of the preparation stage ratio  $\rho$  on the performance of attacker's local estimated detector and surrogate model. Fig. 4. Impact of the preparation stage ratio  $\rho$  on ASR of each stage and the overall ASR.

![](images/1f2cd5e8e83022193a75da7df47ac6c77c796bbaeed2e42b3ef8cb74792afe59.jpg)

honestly follows the VFL inference protocol without manipulation. Consequently,  $s_1$  is solely dependent on the trained VFL model and is unaffected by the preparation stage length. In contrast,  $s_2$  improves as  $\rho$  increases. As indicated in Figure 3, the estimated detector and surrogate model become more accurate with a larger  $\rho$ , leading to more effective optimization of feature vectors. These optimized features exhibit stronger predictive capability for the targeted label while avoiding detection, thus resulting in a gradual improvement in  $s_2$ .

From the above analysis, we identify a fundamental trade- off in the design of the two- stage attack framework. From Eq.(9), it is evident that the total number of test samples predicted as targeted label is the summation of two terms: the number of samples predicted as targeted label in preparation stage and the number predicted in attack stage. If the preparation stage is too short, although a large number of test samples remain for the attack stage, the relatively low ASR  $s_2$  will result in a small second term in Eq.(9), leading to an ineffective attack. All previous attack methods fall into this category, corresponding to the special edge case where the preparation stage is empty, i.e.,  $Q = \emptyset$ . On the other hand, if the preparation stage is too long, the ASR  $s_2$  will be high, but the number of test samples remaining for the attack stage will be small. This also causes the second term in Eq.(9) to be small, resulting in an ineffective attack. The "Ground- Truth" case in Figure 2 falls into this category, corresponding to the special edge case where  $Q = D_{adv}$ .

The experimental results in Figure 4 confirm our analysis. The overall ASR  $s$  initially improves as  $\rho$  increases from  $1\%$  to  $5\%$ . However, when  $\rho$  becomes relatively large ( $10\%$  to  $90\%$ ), there is a corresponding decrease in  $s$ . To effectively address this trade- off, our design goal is to construct a preparation stage that maximizes  $s_2$  while keeping its length minimal, thereby preserving as many test samples as possible for the attack stage.

# 5.2 Framework Overview

Figure 5 illustrates the overview of our two- stage attack framework, VTarbel, which splits the VFL inference phase on the entire test set into a preparation stage and an attack stage.

In the preparation stage, the attacker follows these steps to gather essential knowledge. (1) The attacker initially applies a semi- supervised clustering technique (e.g., constrained seed K- Means [3]) to partition the unlabeled test set into  $C$  clusters. (2) Within each cluster, the attacker selects test samples with high expressiveness. Expressiveness is measured by the ability to maximize the reduction in the maximum mean discrepancy (MMD) [13], a metric that quantifies the distribution divergence between the selected samples and the full test set. (3) The selected expressive samples are then fed into the VFL inference system to obtain the predicted labels  $\hat{Y}$ . (4) Using the newly predicted labels, the semi- supervised clustering algorithm is updated to improve the accuracy of the clusters. These steps are iterated until the MMD falls below a predefined threshold or the

![](images/29f78c82e48454be85c705a7a52c9361f4bcacf0d5a3823fe53e8f3d13372f3b.jpg)  
Fig. 5. Overview of the two-stage attack framework, VTarbel. The green samples represent test samples with high expressiveness, while the blue samples represent those with lower expressiveness. The red samples denote the maliciously generated samples fed into the VFL inference system. Different shapes (circle, triangle, and square) indicate test samples from different classes.

maximum number of unlabelled sample selection rounds is reached. (5) The selected samples and their corresponding predicted labels are collected as pairs, which are then used for locally training the estimated detectors and the surrogate model.

In the attack stage, the attacker uses the estimated detector and surrogate model trained in the previous stage to execute the attack. (6) The attacker filters the remaining test samples (those with lower expressiveness) and feeds them into both the surrogate model and the detector for local gradient- based optimization. The optimization objective is to maximize the probability that the surrogate model predicts the optimized samples as the targeted label, while avoiding detection by the estimated detector. (7) Once the optimization is complete, the generated malicious samples are transferred to the VFL inference system. The goal is for the global VFL model to predict these samples as the targeted label  $t^\star$ , regardless of the benign samples provided by other participants.

# 5.3 Preparation Stage

As shown in Algorithm 1, the goal of the preparation stage in VTarbel is to select as few unlabeled test samples with high expressiveness as possible to construct  $Q^*$ , and to train an accurate estimated detector  $\phi_{est}$  and surrogate model  $f_{sur}(\theta_{sur})$ . This enables the attacker to achieve a high ASR  $s_2$  while preserving as many test samples as possible for the attack stage.

$①$  Initialization. The attacker begins by initializing the preparation set  $Q^{*} = \emptyset$  . For each class  $c\in [C]$  , a labeled sample set  $Q_{c} = \emptyset$  is also initialized to track predicted (labeled) samples (Line 1). Next, the attacker locally constructs an estimated detector  $\phi_{est}$  and a surrogate model  $f_{sur}(\theta_{sur}) = f(\theta_{adv})\circ f(\theta_h)$  , where  $f(\theta_{adv})$  is the attacker's trained bottom model from the VFL training phase, and  $f(\theta_h)$  is a randomly initialized inference head. Here, o denotes model concatenation (Lines 2- 3). The sample selection round is initialized as  $t\gets 1$  , the initial MMD score as  $mmd_0\gets \infty$  , and the locally collected labeled dataset as  $D_{loc} = \emptyset$  (Line 4).

$①$  Semi- supervised clustering. To construct a balanced preparation set, ensuring an approximately equal number of test samples per class for faster training of estimated detectors, the attacker

# Algorithm 1: Preparation Stage in VTarbel

In this paper, we adopt maximum mean discrepancy (MMD) [13] as the foundation for computing sample expressiveness. MMD measures the distributional divergence between two sets  $X = \{x_{1},\ldots ,x_{m}\}$  and  $Y = \{y_{1},\ldots ,y_{n}\} \subseteq X$ , and is defined as:

$$
\mathrm{MMD}^2 (X,Y) = \frac{1}{m^2}\sum_{i,j}k(x_i,x_j) + \frac{1}{n^2}\sum_{i,j}k(y_i,y_j) - \frac{2}{mn}\sum_{i,j}k(x_i,y_j), \tag{10}
$$

where  $m$  and  $n$  are the number of samples in  $X$  and  $Y$ , respectively, and  $k(\cdot ,\cdot)$  denotes the kernel function (e.g., the RBF kernel). A smaller MMD value indicates greater similarity between the two distributions; MMD equals zero if and only if the distributions are identical.

From Figure 3, we observe that the preparation set size has minimal impact on the surrogate model's accuracy. Therefore, the attacker focuses primarily on accurately estimating the defender's detector. This is equivalent to minimizing the distributional discrepancy between the preparation set  $Q^{*}$  and the full test set  $D_{adv}$ , which directly aligns with the objective to minimize  $\mathrm{MMD}^2 (D_{adv},Q^*)$ . To this end, for any unlabeled sample  $x_{u}$ , we define its effectiveness  $e_{u}$  as the reduction in MMD when the sample is added to the current preparation set:

$$
\begin{array}{rl} & e_u = \Delta \mathrm{MMD}^2\\ & \quad = \mathrm{MMD}^2 (D_{adv},Q^*) - \mathrm{MMD}^2 (D_{adv},Q^*\cup \{x_u\})\\ & \quad = \frac{1}{(|Q^*| + 1)^2}\left(k(x_u,x_u) + 2\sum_{y\in Q^*}k(y,x_u)\right) - \frac{2}{N(|Q^*| + 1)}\sum_{x\in D_{adv}}k(x,x_u). \end{array} \tag{11}
$$

Eq.(11) quantifies how much the MMD is reduced by adding  $x_{u}$  to  $Q^{*}$ . A larger value indicates that the inclusion of  $x_{u}$  brings the preparation set distribution closer to that of  $D_{adv}$ , which is interpreted as higher expressiveness.

Using Eq.(11), the attacker computes the effectiveness for all candidate samples in each cluster. To mitigate the computational overhead of repeatedly evaluating MMD, the attacker can employ an incremental update strategy inspired by online kernel methods [8], enabling linear- time updates using precomputed statistics rather than recomputing the full kernel matrix. Then, the expressiveness set  $E_{c}$  is sorted in descending order, and the top  $\eta$  samples with the highest effectiveness scores are selected to form  $S_{c,\eta}$  (Line 12). Finally, the sample sets  $Z$ ,  $Q_{c}$ , and  $Q^{*}$  are updated to include the selected  $S_{c,\eta}$  (Line 13).

(3) Benign VFL inference. For each selected unlabeled sample  $x_{z}$  in the candidate set  $Z$  during the current selection round, the attacker honestly feeds  $x_{z}$  into the VFL inference system to obtain the predicted label  $\hat{y}_{z} = F(x_{z};\pmb {\theta})$  (Lines 14-15).

(4) Update label information. For each test sample  $x_{z}\in Z$ , if the predicted label  $\hat{y}_{z}\neq \mathsf{REJ}$ , the attacker adds the pair  $(x_{z},\hat{y}_{z})$  to the local labeled dataset  $D_{loc}$  (Line 16). This updated dataset, now containing more labeled samples, will be used for the ConstrainedSeedKMeans algorithm in the next selection round to achieve more accurate clusters.

Steps (1)- (4) iterates until either of the following conditions is met: the absolute difference in the MMD score between two consecutive selection rounds falls below the tolerance  $\epsilon$ , or the maximum number of selection rounds  $R$  is reached (Line 19).

(5) Local training. Using the locally collected labeled dataset  $D_{loc}$ , the attacker fine-tunes the surrogate model  $f_{sur}$  for  $T_{ft}$  epochs and updates the local estimated detector  $\phi_{est}$  accordingly (Line 20). Specifically, each sub-detector  $\phi_{est}^{i}$  ( $i\in [C]$ ) in  $\phi_{est}$  is updated using the data samples in  $D_{loc}$  with the predicted label  $\hat{y} = c$ .

# 5.4 Attack Stage

As illustrated in Algorithm 2, the attack stage involves the attacker performing the actual attack by leveraging a gradient- based optimization method to generate malicious samples. These samples are crafted to manipulate the VFL inference system into outputting the targeted label.

$(\widehat{G})$  Generate malicious sample. For each unlabeled test sample  $\mathcal{X}$  in the remaining set  $D_{adv}\backslash Q^{*}$  the attacker generates the corresponding malicious sample  $x_{adv}$  by minimizing the following adversarial objective function (Lines 2- 8):

$$
\begin{array}{c}{J(x) = \mathsf{CE}(\mathsf{Sofmax}(f_{sur}(x)),t^{\star}) + \lambda \phi_{est}(x),}\\ {x_{adv} = \underset {x}{\arg \min}J(x),} \end{array} \tag{12}
$$

where  $\mathsf{CE}(\cdot)$  denotes the cross- entropy loss function. The first term of  $J(x)$  is the main objective, representing the cross- entropy loss between the predicted probability of sample  $\mathcal{X}$  by the surrogate model  $f_{sur}$  and the targeted label  $t^\star$  . Minimizing this loss ensures that  $f_{sur}$  predicts  $\mathcal{X}$  as  $t^\star$  with high confidence. The second term acts as a regularization term, which restricts the optimized sample within the valid region of the normal distribution. This prevents the sample from being detected as anomalous by the estimated detector  $\phi_{est}$  , thereby increasing the likelihood of evading detection by the defender's detector. These two terms are balanced by the hyperparameter  $\lambda$  . This optimization problem is subject to the constraint that the distance between the original sample  $\mathcal{X}$  and the malicious sample  $x_{adv}$  does not exceed a predefined tolerance  $r_{max}$  , ensuring that  $x_{adv}$  retains its validity within the feature space.

The above constrained optimization problem can be solved using projected gradient descent (PGD) [1]. Specifically, in the  $t$  - th optimization step, as shown in Eq.(13), the attacker performs the following updates:

$$
\begin{array}{r}x_{t}^{\prime}\leftarrow x_{t} - \alpha \nabla_{x_{t}}J(x_{t}),\\ x_{t + 1}\leftarrow \mathsf{Proj}(x_{t}^{\prime}), \end{array} \tag{13}
$$

where  $x_{t}^{\prime}$  is the intermediate sample obtained by performing vanilla stochastic gradient descent (SGD) on  $x_{t}$  , and  $\alpha$  is the learning rate. The attacker then applies the Proj  $(\cdot)$  function to project the intermediate sample  $x_{t}^{\prime}$  back into the valid region that satisfies the constraint, yielding  $x_{t + 1}$  for the next iteration. This optimization terminates if either the anomaly score from  $\phi_{est}$  exceeds the detection threshold  $\tau$  (Lines 7- 8), or the maximum number of steps is reached.

$(7)$  Malicious VFL inference. After generating the malicious sample  $x_{adv}$  corresponding to each unlabeled sample  $\mathcal{X}$  in the remaining set, the attacker feeds  $x_{adv}$  into the VFL inference system (Line 9). The attack on sample  $\mathcal{X}$  is considered successful if the predicted label  $\hat{y}_{adv}$  equals the targeted label  $t^\star$  (Line 10).

The intuition behind this attack strategy is rooted in the transferability property of malicious samples [14, 19]. Specifically, if the malicious samples can deceive the surrogate model by causing it to predict the targeted label  $t^\star$  with high confidence, while simultaneously avoiding detection by the estimated detector as an anomaly, the malicious sample is likely to transfer its inherent adversarial properties to the global VFL model. This increases the likelihood that the global model will also output the targeted label, thereby achieving the adversarial goal. Our experimental evaluation in Section 6.2 confirms this theoretical foundation, demonstrating the practical viability of our attack strategy.

# Algorithm 2: Attack Stage in VTarbel

Input :Test dataset  $D_{adv}$  , trained global VFL model  $F(\theta)$  , test samples in preparation stage  $Q^{*}$  estimated detector  $\phi_{est}$  , anomaly detection threshold  $\tau$  , local surrogate model  $f_{sur}(\theta_{sur})$  targeted label  $t^{\star}$  , maximum optimization steps to generate malicious sample  $T_{opt}$  1 Filter out remaining samples in the attack stage as  $D_{adv}\backslash Q^{*}$  . 2 for each unlabeled sample  $x\in D_{adv}\backslash Q^{*}$  do  $/\ast ⑤$  Generate malicious sample \*/ 3 Initialize  $x_{1}\gets x$  . 4 for optimization step  $t\in [1,T_{opt}]$  do 5 Calculate adversarial loss  $J(x_{t})$  according to Eq.(12); 6 Optimize  $x_{t}$  according to Eq.(13) to obtain  $x_{t + 1}$  . 7 if  $\phi_{est}(x_{t + 1}) > \tau$  then 8 break;  $/\ast ⑦$  Malicious VFL inference \*/ 9 Set  $x_{adv}\gets x_{t}$  , feed  $x_{adv}$  into global VFL model to get  $\hat{y}_{adv} = F(x_{adv},\pmb {\theta})$  . 10 Check whether  $\hat{y}_{adv} = t^{\star}$  to determine if the attack on sample  $\mathcal{X}$  is successful ;

# 6 EVALUATION

# 6.1 Experimental Setup

Models and Datasets. We evaluate our attack framework using four different model architectures: a fully- connected multi- layer perceptron (MLP3, 3 means three layers), convolutional- based VGG13 [44] and ResNet18 [16], and Transformer- based DistilBERT [43] (which has been rarely explored in previous studies). These models are used for both the active and passive parties in the VFL setting. For the top model and the attacker's inference head model, we use a simple MLP2 architecture. These bottom models are evaluated using classification datasets of varying modalities, numbers of test samples, and number of classes, as summarized in Table 1. (1) For MLP3, we evaluate it on tabular datasets TabMNIST and TabFMNIST, where each data sample is generated by flattening the original  $28 \times 28$  grayscale images from the MNIST [27] and FashionMNIST [50] datasets into a 784- dimensional feature vector. (2) For VGG13, We use image datasets CIFAR10 [26] and CINIC10 [7]. Both datasets contain 10 classes, with CINIC10 containing 90,000 samples—significantly more than CIFAR10, which has 10,000 test samples. This allows us to assess the attack performance on a larger dataset. (3) For ResNet18, it is evaluated on the CIFAR10 and CIFAR100 [26] image datasets. The CIFAR10 dataset is used to compare attack effectiveness across different bottom model architectures on the same dataset, while CIFAR100 (with 100 classes) allows us to evaluate the attack performance on datasets with a much larger number of classes. (4) For DistilBERT, we leverage text datasets commonly used for text classification tasks, including TREC [31] and IMDB [37]. For both image and text datasets, each image or text sample is evenly split into sub- images or sub- texts, respectively. Similarly, for tabular datasets, each raw feature vector is also evenly split into sub- vectors for distribution across each party.

Metrics. For targeted label attack, we report the Top1 ASR for all datasets, except for CIFAR100 dataset, where we report Top5 ASR. Similarity, for main VFL inference task, we report Top1 accuracy for all datasets, except for CIFAR100 dataset, where we report Top5 accuracy. For the performance of the attacker's local estimated detector, we report the F1 score.

Baseline. We report the ASR achieved when the passive party honestly follows the inference protocol in a detector- enhanced VFL system, which serves as the attack baseline.

Table 1. Model architectures, dataset statistics, and configurations of VTarbel.  

<table><tr><td>Bottom Model</td><td>Top Model</td><td>Surrogate Inference Head</td><td>DeepAE</td><td>Dataset</td><td>Modality</td><td>No. Test Samples</td><td>Embedding Dimension</td><td>No. Classes</td><td>η</td><td>ε</td><td>Tft</td><td>λ for DeepAE</td><td>λ for KDE</td><td>Topt</td></tr><tr><td>MLP3</td><td>MLP2</td><td>MLP2</td><td>MLP6</td><td>TabMNIST TabFMNIST</td><td>Tabular</td><td>10,000 10,000</td><td>64 64</td><td>10 10</td><td>10 10</td><td>1e-4 1e-4</td><td>50 50</td><td>1 1</td><td>20 20</td><td>50 50</td></tr><tr><td>VGG13</td><td>MLP2</td><td>MLP2</td><td>MLP6</td><td>CIFAR10 CINIC10</td><td>Image</td><td>10,000 90,000</td><td>10 10</td><td>10 10</td><td>10 10</td><td>1e-4 1e-4</td><td>50 50</td><td>1 1</td><td>20 20</td><td>50 50</td></tr><tr><td>ResNet18</td><td>MLP2</td><td>MLP2</td><td>MLP6</td><td>CIFAR10 CIFAR100</td><td>Image</td><td>10,000 10,000</td><td>10 100</td><td>10 100</td><td>10 5</td><td>1e-4 1e-5</td><td>50 50</td><td>1 1</td><td>20 20</td><td>50 50</td></tr><tr><td>DistilBERT</td><td>MLP2</td><td>MLP2</td><td>MLP6</td><td>TREC IMDB</td><td>Text</td><td>554 25,000</td><td>64 64</td><td>6 2</td><td>5 100</td><td>1e-2 1e-3</td><td>20 20</td><td>1 1</td><td>20 20</td><td>50 50</td></tr></table>

$\eta$  sample selection step,  $\epsilon$  : MMD stopping tolerance,  $T_{ft}$  : local fine-tuning epochs,  $\lambda$  : balancing parameter,  $T_{opt}$  : maximum optimization steps.

Compared Methods. We compare VTarbel with four representative targeted label attacks in VFL: LR- BA [15], HijackVFL [42], ADI [39], and TIFS'23 [17]. For detailed description of these attack methods, please refer to Section 7.2.

Hyperparameters. (1) For VTarbel, the hyperparameters for each dataset are listed in Table 1. These include parameters in the preparation stage, such as the test sample selection step  $\eta$ , MMD stopping tolerance  $\epsilon$ , and local fine- tuning epochs  $T_{ft}$ , as well as parameters in the attack stage, such as the balancing parameter  $\lambda$  and the maximum number of optimization steps  $T_{opt}$ . We calculate the MMD value in Eq.(11) in the feature embedding space, rather than in the raw feature vector space. (2) For LR- BA, the auxiliary labels for each class are set to 4 across all datasets, and the number of optimization iterations for generating the backdoor latent representation is set to 50. (3) For HijackVFL, we follow the settings from the original paper, where the backdoor poison ratio of the target class is set to  $1\%$ , the number of available labels for each class is set to 4, the total epochs for training the surrogate model is set to 50, and the standard variance for generating Gaussian noise is set to 0.1. (4) For ADI, the attacker is granted maximum adversarial knowledge, meaning it knows the parameters and outputs of the top model and has direct access to the defender's raw feature vectors. The optimization rounds for generating the adversarial dominant inputs are set to 50. (5) For TIFS'23, in accordance with the original paper, during VFL training,  $10\%$  of the training samples from the targeted label are assigned to the attacker. During the inference phase, the top 50 samples predicted with the highest probability for the targeted label are selected to generate the trigger embedding.

Unless otherwise specified, the experiments are conducted in a two- party VFL inference system, where the passive party is compromised by the attacker and the active party is the defender. The defender's anomaly detection threshold is set to the  $95\%$  percentile of the anomaly scores computed from the training samples, and the default anomaly detector used by the defender is DeepAE. The targeted label is set to "0" for all datasets, except for TREC, where it is set to "1". The impact of the detection threshold and the targeted label is discussed in Section 6.7.

Implementation. We built a VFL system on a cluster of Ubuntu 22.04 servers, each equipped with a 64- core Intel(R) Xeon(R) Gold 5420+ CPU, 128GB RAM, and two NVIDIA A40 GPUs. The servers communicate with each other via Gigabit Ethernet. For training, inference, and malicious sample generation with MLP and convolutional models, we use the PyTorch framework [41]. For the Transformer- based model, we adopt the HuggingFace framework [49]. Communication among parties is managed using the Python socket library [9]. For attack methods with open- source code, we use their available implementations. Otherwise, we reproduce the methods ourselves. Each attack method is evaluated under three independent VFL model trainings, and the reported ASRs are averages from these experiments.

Table 2. Comparison of attack performance (ASR  $(\%)$  ) of various attack methods.  

<table><tr><td rowspan="2">Defender&#x27;s Detector</td><td rowspan="2">Method</td><td colspan="2">MLP3</td><td colspan="2">VGG13</td><td colspan="2">ResNet18</td><td rowspan="2">DistilBERT TREC</td><td rowspan="2">IMDB</td></tr><tr><td>TabMNIST</td><td>TabFMNIST</td><td>CIFAR10</td><td>CINIC10</td><td>CIFAR10</td><td>CIFAR100</td></tr><tr><td>-</td><td>Ground-Truth</td><td>9.80</td><td>10.00</td><td>10.00</td><td>10.00</td><td>10.00</td><td>5.00</td><td>16.90</td><td>50.00</td></tr><tr><td rowspan="6">DeepAE</td><td>Baseline</td><td>9.31</td><td>9.56</td><td>9.24</td><td>9.20</td><td>9.03</td><td>3.26</td><td>15.50</td><td>44.61</td></tr><tr><td>LR-BA [15]</td><td>15.05</td><td>12.72</td><td>51.74</td><td>49.62</td><td>46.07</td><td>0</td><td>0</td><td>82.99</td></tr><tr><td>HijackVFL [42]</td><td>0</td><td>0</td><td>0</td><td>0</td><td>0</td><td>0</td><td>0</td><td>0</td></tr><tr><td>ADI [39]</td><td>13.53</td><td>16.19</td><td>74.85</td><td>40.14</td><td>60.58</td><td>0.61</td><td>15.54</td><td>83.51</td></tr><tr><td>TIFS&#x27;23 [17]</td><td>35.32</td><td>60.02</td><td>84.05</td><td>17.45</td><td>80.74</td><td>29.98</td><td>40.25</td><td>100.0</td></tr><tr><td>VTarbel (Ours)</td><td>38.84</td><td>89.87</td><td>89.05</td><td>90.39</td><td>90.28</td><td>40.77</td><td>90.97</td><td>96.05</td></tr><tr><td rowspan="6">KDE</td><td>Baseline</td><td>9.25</td><td>9.60</td><td>9.27</td><td>8.60</td><td>9.10</td><td>3.26</td><td>14.62</td><td>44.32</td></tr><tr><td>LR-BA [15]</td><td>17.00</td><td>17.51</td><td>31.99</td><td>43.49</td><td>52.21</td><td>1.69</td><td>2.88</td><td>87.64</td></tr><tr><td>HijackVFL [42]</td><td>0</td><td>6.88</td><td>57.05</td><td>0</td><td>58.14</td><td>69.18</td><td>22.20</td><td>0</td></tr><tr><td>ADI [39]</td><td>16.13</td><td>21.69</td><td>76.67</td><td>40.28</td><td>71.56</td><td>4.57</td><td>12.82</td><td>55.32</td></tr><tr><td>TIFS&#x27;23 [17]</td><td>38.57</td><td>50.16</td><td>79.98</td><td>0</td><td>78.83</td><td>32.25</td><td>35.74</td><td>92.00</td></tr><tr><td>VTarbel (Ours)</td><td>38.44</td><td>89.54</td><td>88.28</td><td>90.28</td><td>88.21</td><td>52.55</td><td>90.97</td><td>95.84</td></tr></table>

The Ground-Truth row denotes the proportion of the number of targeted samples in the test set. The highest ASR values in each column are highlighted in bold.

# 6.2 Attack Performance and Comparative Analysis

In this section, we evaluate the attack performance of VTarbel and compare it with other attack methods. As shown in Table 2, VTarbel consistently achieves superior attack performance in nearly all cases, with the ASR from VTarbel being significantly higher than both the baseline and other attacks. For example, on the CIFAR10 dataset with the DeepAE detector, VTarbel increases the ASR from the baseline's  $9.24\%$  and  $9.03\%$  (for the VGG13 and ResNet18 models, respectively) to a much higher level of  $89.05\%$  and  $90.28\%$  , respectively. Moreover, when compared to other attack methods, VTarbel outperforms them by a significant margin. For instance, in the case of (DistilBERT, TREC, KDE), VTarbel achieves an ASR of  $90.97\%$  , whereas the highest ASR from other methods, TIFS'23, is only  $35.74\%$  . This demonstrates a substantial boost in attack performance.

For other attack methods, we observe that they struggle to achieve high ASRs under both DeepAE and KDE detectors. Specifically, for HijackVFL, the attack performance is poor under the DeepAE detector, with an ASR of 0 across all cases, even lower than the baseline. We believe this is because, although the trigger vector added to the attacker's feature embedding has strong predictive capability for the targeted label, it significantly deviates from the normal distribution of the targeted class's feature embedding. This causes the anomaly detector to flag all of the attacker's feature embeddings as anomalies, resulting in an ASR of 0. Among the other attacks, TIFS'23 performs the best. For example, under the KDE detector with the ResNet18 model, TIFS'23 increases the ASR from the baseline's  $9.10\%$  on CIFAR10 and  $3.26\%$  on CIFAR100 to  $78.83\%$  and  $32.25\%$  , respectively. We hypothesize that this is due to the trigger embedding being located in the highest density region of the targeted feature embedding space, which has a high probability of being mapped to the target label while avoiding detection. However, even in these cases, there remains a significant performance gap compared to VTarbel, which achieves much higher ASRs of  $88.21\%$  and  $52.55\%$  respectively.

These results highlight that, despite requiring minimal attack knowledge compared to existing methods, VTarbel effectively leverages the information available during the VFL inference phase to train both surrogate model and estimated detector. The surrogate model guides the optimization of malicious feature embeddings, while the estimated detector constrains the embeddings to avoid detection. As a result, VTarbel consistently achieves superior performance compared to prior attacks.

Table 3. Comparison of attack performance (ASR  $(\%)$  ) among VTarbel and its variants.  

<table><tr><td>Method</td><td>MLP3 
TabFMNIST</td><td>VGG13 
CIFAR10</td><td>ResNet18 
CIFAR10</td><td>DistilBERT 
TREC</td></tr><tr><td>Ground-Truth</td><td>10.00</td><td>10.00</td><td>10.00</td><td>16.90</td></tr><tr><td>Only-Preparation (baseline)</td><td>9.60</td><td>9.24</td><td>9.03</td><td>15.50</td></tr><tr><td>Only-Attack</td><td>0</td><td>0</td><td>0</td><td>0</td></tr><tr><td>Random-Preparation (w/ clustering)</td><td>84.66</td><td>83.73</td><td>82.34</td><td>73.10</td></tr><tr><td>Random-Preparation (w/o clustering)</td><td>67.48</td><td>81.74</td><td>81.67</td><td>64.25</td></tr><tr><td>Random-Attack</td><td>35.82</td><td>60.26</td><td>40.73</td><td>73.14</td></tr><tr><td>VTarbel</td><td>89.87</td><td>89.05</td><td>90.28</td><td>90.97</td></tr></table>

# 6.3 Stage Effectiveness Breakdown

In this section, we break down and evaluate the effectiveness of each stage in VTarbel, comparing it with five variants, all evaluated under defender's DeepAE detector, as shown in Table 3. These variants are: (1) Only- Preparation: Consists only of the preparation stage, corresponding to the baseline. (2) Only- Attack: Involves only the attack stage, where the attacker begins the attack at the very start of VFL inference phase. (3) Random- Preparation (w/ clustering): The attacker selects the same number of test samples within each cluster as VTarbel, but randomly. (4) Random- Preparation (w/o clustering): The attacker randomly selects the same total number of test samples as VTarbel from the entire test set, without applying clustering. (5) Random- Attack: The attacker randomly selects samples predicted as targeted label in the preparation stage as attack samples for the attack stage, rather than optimizing the attack samples.

From Table 3, we first observe that attack variants consisting of only one stage, i.e., OnlyPreparation and Only- Attack, exhibit extremely low ASR. Specifically, Only- Attack consistently results in an ASR of 0 across all cases. This occurs because the surrogate model is completely random, meaning the feature embedding optimized from this random model lacks any predictive capability for the targeted label. These findings underscore the necessity of our two- stage framework for an effective attack.

Additionally, when we examine attack variants that include two stages but with one stage involving random operations, we find higher ASRs than those observed with only one stage. However, there is still a performance gap compared to VTarbel. For instance, in the (DistilBERT, TREC) case, the ASRs from Random- Preparation (w/ clustering) and Random- Preparation (w/o clustering) are  $73.10\%$  and  $64.25\%$  , respectively. In contrast, VTarbel achieves an ASR of  $90.97\%$  demonstrating significantly better performance. These results validate the effectiveness of the MMD- based sample selection strategy in VTarbel, which outperforms random selection by targeting expressive samples that contribute to a higher ASR. Moreover, we observe that Random- Preparation (w/ clustering) consistently outperforms Random- Preparation (w/o clustering) in all cases. This suggests that the balanced preparation set, achieved through semi- supervised clustering in the preparation stage, yields a higher ASR than a randomly selected preparation set. Lastly, VTarbel outperforms Random- Attack in all cases, indicating that the malicious optimized feature embedding has much stronger predictive power for the targeted label compared to the benign feature embedding of the targeted label. This demonstrates that the optimization step during the attack stage is critical for effective attack.

# 6.4 Optimality Verification of Preparation Set

As outlined in Section 5.1, directly solving the combinatorial problem in Eq.(9) is NP- hard. Therefore, our approach selects the minimum number of expressive samples necessary to train an accurate

Table 4. Verification of the optimality of the preparation set selected by VTarbel in achieving the highest ASR  $(\%)$  , compared with variants of early stopping and late stopping during the preparation stage.  

<table><tr><td>Model</td><td>Dataset</td><td>No. Test Samples</td><td>|Q*|</td><td>Early Stopping -30%</td><td>Best -15%</td><td>Best 0</td><td>Late Stopping +15%</td><td>+30%</td></tr><tr><td>MLP3</td><td>TabFMNIST</td><td>10,000</td><td>900</td><td>82.28</td><td>87.19</td><td>89.87</td><td>85.41</td><td>86.76</td></tr><tr><td>VGG13</td><td>CIFAR10</td><td>10,000</td><td>1,100</td><td>88.92</td><td>88.63</td><td>89.05</td><td>88.78</td><td>88.77</td></tr><tr><td>ResNet18</td><td>CIFAR10</td><td>10,000</td><td>1,000</td><td>77.08</td><td>81.97</td><td>90.28</td><td>83.28</td><td>76.37</td></tr><tr><td>DistilBERT</td><td>TREC</td><td>554</td><td>60</td><td>70.37</td><td>93.32</td><td>90.97</td><td>85.74</td><td>81.94</td></tr></table>

surrogate model and estimated detector, aiming to achieve a high ASR during the attack stage. In this section, we verify the optimality of the preparation set selected by VTarbel. As shown in Table 4, we first list the preparation set size  $|Q^{*}|$  identified by VTarbel for each evaluation case. We then compare the attack performance achieved by VTarbel (denoted as "Best") with two other variants, which differ in the stopping points during the preparation stage: early stopping and late stopping. For example, the  $- 15\%$  in early stopping means that the preparation stage is terminated when the preparation set size reaches  $85\%$  of the optimal  $|Q^{*}|$  found by VTarbel. Similarly, the  $" + 15\%$  under late stopping means that the preparation stage is extended until the preparation set size reaches  $115\%$  of  $|Q^{*}|$

We observe that, in most cases, the preparation set selected by VTarbel achieves the optimal ASR. For instance, under the (ResNet18, CIFAR10)configuration, VTarbel achieves the highest ASR of  $90.28\%$  , while the early stopping  $(- 15\%)$  and late stopping  $(+15\%)$  variants only achieve ASRs of  $81.97\%$  and  $83.28\%$  , respectively, both of which are substantially lower than VTarbel's result. The reason behind this performance difference is as follows: For late stopping, the optimal  $|Q^{*}|$  size provides enough test samples for the surrogate model and estimated detector to be sufficiently trained. Adding more samples in the preparation stage reduces the number of samples predicted as the targeted label during the attack stage, thus lowering the ASR. In contrast, for early stopping, fewer test samples than VTarbel's optimal preparation set slightly degrade the performance of the estimated detector, leading to a reduced number of samples predicted as targeted label in the attack stage, which also results in a lower ASR compared to VTarbel.

# 6.5 Detector-Agnostic Attack

6.5 Detector- Agnostic AttackIn this section, we evaluate whether VTarbel is agnostic to the type of detector used by the defender. Based on the attacker's knowledge of the defender's detector type, we categorize the attack scenarios into white- box and black- box scenarios. The former consists of cases where both the active and passive parties use the same detector type, such as (DeepAE, DeepAE) and (KDE, KDE). The latter includes scenarios where the attacker's detector type differs from the defender's, such as (DeepAE, KDE) and (KDE, DeepAE), where each pair denotes the (Active Party's Detector, Passive Party's Detector). From Table 5, we observe that in the black- box attack scenario, where the attacker has no knowledge of the defender's specific detector type, the attacker can still achieve high ASR comparable to those in the white- box attack scenario. For example, when the defender's detector is KDE, in the cases of (VGG13, CIFAR10) and (ResNet18, CIFAR10), the ASRs from the black- box attack (where the attacker's detector is DeepAE) are  $88.80\%$  and  $90.40\%$ , respectively, which are even higher than the ASRs of  $88.28\%$  and  $88.21\%$  in the white- box attack scenario. These results demonstrate that even when the attacker's detector type differs from the defender's, the locally generated malicious feature embedding can still effectively evade detection, highlighting VTarbel's robustness to the specific detector used by the defender.

Table 5. VTarbel's attack performance (ASR  $(\%)$  ) in the white-box and black-box attack scenarios, depending on whether the passive party (attacker) has knowledge of the active party's (defender) detector type.  

<table><tr><td>Attack Scenario</td><td>Active Party&#x27;s Detector</td><td>Passive Party&#x27;s Detector</td><td>MLP3 TabFMNIST</td><td>VGG13 CIFAR10</td><td>ResNet18 CIFAR10</td><td>DistilBERT TREC</td></tr><tr><td rowspan="2">White-Box</td><td rowspan="2">DeepAE KDE</td><td rowspan="2">DeepAE KDE</td><td>89.87</td><td>89.05</td><td>90.28</td><td>90.97</td></tr><tr><td>89.54</td><td>88.28</td><td>88.21</td><td>90.97</td></tr><tr><td rowspan="2">Black-Box</td><td rowspan="2">DeepAE KDE</td><td>KDE</td><td>89.20</td><td>88.16</td><td>88.27</td><td>90.97</td></tr><tr><td>DeepAE</td><td>86.93</td><td>88.80</td><td>90.40</td><td>90.97</td></tr></table>

![](images/0078e96d2c8755e1f470c87a8a166526333a1bea0b106bcee3f3e18890522b68.jpg)  
Fig. 6. The impact of the attacker's feature fraction in a two-participant VFL inference system, where the features allocated to each participant are randomly selected.

# 6.6 Scalability Evaluation

In this section, we evaluate VTarbel's attack scalability in terms of the attacker's feature fraction and the total number of participants in the VFL inference system. Our experiments are conducted using the TabMNIST and TabFMNIST datasets under two scenarios: the first scenario corresponds to a VFL inference system with two participants, where the attacker's feature fraction (i.e., the fraction of the attacker's features to the total number of features) ranges from  $90\%$  to  $10\%$  (Figure 6). The second scenario corresponds to a VFL inference system with varying numbers of participants, ranging from 2 to 10, where the total features are randomly and evenly allocated to each participant (Figure 7).

From both Figure 6 and Figure 7, several similar trends can be observed. First, the baseline ASR remains stable and is unaffected by the attacker's feature fraction or the number of participants. This is because the baseline is only related to the global VFL model's accuracy. Next, as the number of features allocated to the attacker decreases (i.e., the decrease in the attacker's feature fraction in Figure 6 and the increase in the total number of participants in Figure 7), both the accuracy of the locally trained surrogate model and the F1 score of the estimated detector gradually decrease. The reason for this is that with fewer features, it becomes much more challenging for the attacker to leverage these limited features to train an accurate surrogate model or detector that can precisely estimate the global VFL model or the defender's detector. Consequently, there is a corresponding gradual decrease in VTarbel's ASR, which is influenced not only by the less accurate surrogate model and detector but also by the benign features from other participants that can neutralize the malicious properties of the attacker's feature embedding. However, we note that practical VFL systems typically involve only two participants, where the passive party (i.e., the attacker) serves as the feature provider and often possesses more features than the active party (i.e., the defender) [5].

![](images/d8a69cb36d91d712308aa7dd8dd422f1b2cdd05a3c2e3b8fcb439e83f8b41439.jpg)  
Fig. 7. The impact of the number of participants, where the total features are randomly and evenly split among participants.

![](images/352a82587bca688636811f1a7aad15412508c3698016a0c00caf70e8363e035d.jpg)  
Fig. 8. Impact of an imbalance binary dataset, where the negative class is the targeted label.

In such practical settings, VTarbel demonstrates strong attack performance. For instance, as shown in Figure 6, when the attacker holds  $60\%$  of the features, the ASR reaches  $91.97\%$  on TabMNIST and  $90.73\%$  on TabFMNIST, posing a significant threat to the security of practical VFL inference systems.

# 6.7 Ablation Study

6.7.1 Imbalanced Dataset. We investigate the impact of an imbalanced dataset on VTarbel's attack performance. We construct a binary test set containing 1,000 samples by randomly selecting test samples from class 0 (treated as the negative class) and class 1 (treated as the positive class) from the CIFAR10 dataset, and evaluate it using the VGG13 and ResNet18 models. As shown in Figure 8, the imbalance ratio on the  $\mathbf{x}$ - axis is defined as the number of negative samples divided by the number of positive samples, where the targeted label is the negative class. For VTarbel, we observe that even as the imbalance ratio increases from 1:1 to 1:8, the ASR remains stable and comparable to that of the balanced dataset. For example, when the imbalance ratio is 1:8, the ASR for VGG13 and ResNet18 is 0.916 and 0.919, respectively, both of which are close to the ASRs of 0.942 and 0.934 observed when the imbalance ratio is 1:1. This demonstrates that VTarbel is robust to class imbalance within the test set. Furthermore, when compared to the variant Random- Preparation (w/o clustering), we find that when the class imbalance becomes more severe (e.g., the imbalance ratio is greater than 1:5), the ASR for Random- Preparation (w/o clustering) is close to zero and

![](images/b985bc48c1e58fdde1f320d5f8ecb0ba8fb3caebae15f97e38759f5b409a6e6f.jpg)  
Fig. 9. Impact of anomaly detection threshold under the (ResNet18, CIFAR10, DeepAE) configuration.

even lower than that of the baseline. In contrast, VTarbel consistently achieves a considerably higher ASR than Random- Preparation (w/o clustering). This further validates that the clustering step in the preparation stage is crucial, as it ensures that the preparation set remains balanced, which facilitates the training of both the surrogate model and the estimated detector, even when the original test set is imbalanced.

6.7.2 Anomaly Detection Threshold. We evaluate the impact of anomaly detection thresholds on VTarbel's attack performance. Specifically, we investigate three different cases based on the percentiles of the detection thresholds of both the defender's deployed detector and the attacker's estimated detector: (1) The defender's percentile of detection thresholds ranges from 90 to 10, implying gradually strengthened detection, while the attacker's percentile of detection threshold is fixed at the default 95 (Figure 9(a)). (2) The attacker's percentile of detection thresholds ranges from 90 to 10, indicating more conservative optimization of the feature embedding. In this case, the optimization process may be early terminated due to the strengthened estimated detection threshold, while the defender's detection threshold percentile is fixed at the default 95 (Figure 9(b)). (3) Both the defender's and attacker's percentiles of detection threshold range from 90 to 10 simultaneously (Figure 9(c)). Note that the defender's detection thresholds percentiles are calculated based on the training set, while the attacker's are calculated based on the preparation set. The experiments are conducted using the ResNet18 model and the CIFAR10 dataset under the DeepAE detector.

As shown in Figure 9, we plot the overall ASR of VTarbel, along with the ASR of the attack stage (since the detection thresholds have no effect on the ASR of the preparation stage) and the overall anomaly ratio identified by the defender's detector. In Figure 9(a), as we gradually increase the strength of the defender's anomaly detector (e.g., with detection threshold percentiles lower than 50), there is a gradual increase in the anomaly ratio. This occurs because most of the attacker's optimized feature embeddings are recognized as anomalies. As a result, the ASR of the attack stage gradually decreases, and the overall ASR of VTarbel also decreases simultaneously. In contrast, Figure 9(b) shows that as the detection strength of the attacker's estimated detector increases, the anomaly ratio remains stable and close to 0. The reason for this is that as the detection strength of the estimated detector increases, the optimization step in Algorithm 2 is terminated earlier with fewer optimization rounds, ensuring that the generated feature embedding stays within a region with high probability. Thus, the anomaly ratios consistently remain close to zero. At the same time, due to fewer optimization rounds, the malicious feature embeddings are less effective at being mapped to the targeted label, which explains the slight decrease in the ASR of the attack stage and the overall ASR of VTarbel.

![](images/a0c59525fcf0cc5c7fe5ca56386e4f220b1f2cb072d23e856e8f2bfc6df349fe.jpg)  
Fig. 10. Impact of distance constraint.

![](images/cc4cf2e70805279c8f5d6f3f40ad20963b7cd609aacaa631dd102ac97f75ce7e.jpg)  
Fig. 11. Impact of targeted label.

Finally, in Figure 9(c), when both the defender's and the attacker's detection threshold percentiles change simultaneously, we observe that the overall ASR of VTarbel lies between the values shown in Figure 9(a) and Figure 9(b). For instance, when the detection threshold percentiles equal 30, VTarbel's ASR in Figure 9(c) is  $62.03\%$  which lies between  $29.25\%$  in Figure 9(a) and  $75.74\%$  in Figure 9(b). This is because, compared with Figure 9(a), the optimization rounds in Figure 9(c) are fewer, which ensures that most feature embeddings are not recognized as anomalies, thus the overall ASR is higher. Furthermore, compared with Figure 9(b), the defender's detection strength in Figure 9(c) is stronger, which results in a higher anomaly ratio ( $0.72\%$  compared to  $0.05\%$ ), leading to a slightly lower overall ASR.

6.7.3 Distance Constraint. We examine the impact of the distance constraint  $r_{max}$  in the attack stage on VTarbel's attack performance. The  $r_{max}$  is computed as the  $L_{2}$  distance between the maximum and minimum values in each dimension of the feature embedding of test samples in the preparation set. As shown in Figure 10, on the x- axis, we introduce a distance constraint factor  $\beta \in (0,1]$ , which scales the maximum allowed perturbation radius  $r_{max}$ . The actual distance bound for optimization is then defined as  $\beta r_{max}$ . We observe that as  $\beta$  gradually decreases, for the CIFAR10 dataset (using the ResNet18 model), there is little impact on both the ASR of the attack stage and the overall ASR of VTarbel. This demonstrates that VTarbel is highly robust to the allowed perturbation radius. For the TabFMNIST dataset, there is a gradual decrease in the ASR of the attack stage as  $\beta$  decreases. This is because the strengthened perturbation distance constraint restricts the malicious feature embeddings, preventing them from deviating too far from the normal feature space. As a result, it becomes less effective at changing the VFL prediction to the targeted label. Nevertheless, we note that even when  $\beta$  is lowered to 0.1, for the TabFMNIST dataset, the overall ASR of VTarbel remains high at  $66.82\%$ , still posing a considerable threat to the security of the VFL inference system.

6.7.4 Targeted Label. We evaluate the impact of the targeted label on VTarbel's attack performance. As shown in Figure 11, for both the CIFAR10 and TabFMNIST datasets, VTarbel consistently achieves high ASR across all targeted labels ranging from 0 to 9. For the CIFAR10 dataset, the minimum, maximum, and average ASRs across all targeted labels are  $76.19\%$ ,  $90.89\%$ , and  $87.54\%$ , respectively. In the TabFMNIST dataset, the corresponding ASRs are  $74.66\%$ ,  $90.92\%$ , and  $85.57\%$ , respectively. These results confirm that VTarbel is general and agnostic to the specific targeted label.

# 6.8 Possible Defenses

To defend against VTarbel, we validate whether the defender can apply three commonly used privacy- preserving techniques [10, 53] in the ML community to mitigate the targeted label attack while maintaining the main task (i.e., VFL inference) accuracy. The experiments are conducted

![](images/ea0939e30e54fc97c0d887b8006721ae94288f2f4fb75a98d323f88a0cfcdfcb.jpg)  
Fig. 12. Evaluation of three commonly used privacy-preserving techniques on defending against VTarbel while maintaining main task accuracy.

using the (ResNet18, CIFAR10) and (MLP3, TabFMNIST) configurations, all under the DeepAE detector. Each defense technique and its corresponding results are discussed below.

Noisy Embedding. In this defense, the defender adds random Gaussian noise to the received feature embeddings from the attacker. The Gaussian noise mean is fixed at zero, and the standard variance ranges from  $1e^{- 6}$  to  $1e^{2}$ , as shown in Figure 12(a). We observe that as the noise standard variance increases, VTarbel's ASR gradually decreases in both the CIFAR10 and TabFMNIST datasets. However, there is also a simultaneous decrease in main task accuracy, indicating that noisy embedding is not an effective defense strategy.

Discrete Embedding. In this technique, the defender sets an interval between the minimum and maximum value of the attacker's feature embeddings from training samples, and then slices this interval into several bins. At the VFL inference phase, upon receiving feature embeddings from the attacker, the defender rounds each embedding value to the nearest endpoint of the corresponding bin. The number of bins controls how much information within the feature embedding is preserved, with a larger number of bins corresponding to less preserved information. As illustrated in Figure 12(b), as the number of bins decreases, there is no significant decline in VTarbel's ASR for both the CIFAR10 and TabFMNIST datasets. For example, for CIFAR10 dataset, when there are 50 bins, the ASR is  $92.45\%$ , and when there are only 5 bins, the ASR is still high at  $91.56\%$ . This indicates that the discrete embedding technique is also not effective in reducing ASR.

Compressed Embedding. In this technique, for each attacker's feature embedding vector, the defender preserves only a proportion of the largest absolute values, setting all other values to zero. As shown in Figure 12(c), for instance, when the preserved ratio is 0.2, it means that the top  $20\%$  of the largest absolute values of the embedding vector are retained, while the remaining  $80\%$  are set to zero. We find that the compressed embedding technique outperforms both the noisy embedding and discrete embedding techniques. For example, when the preserved ratio decreases from 0.9 to 0.1 in the TabFMNIST dataset, VTarbel's ASR decreases from  $89.15\%$  to  $47.50\%$ , while the main task accuracy only slightly decreases from  $90.67\%$  to  $87.64\%$ . Similarly, in the CIFAR10 dataset, VTarbel's ASR decreases from  $90.07\%$  to  $7.19\%$ , while the main task accuracy decreases from  $92.35\%$  to  $78.16\%$ .

From these evaluations, we conclude that, among these three commonly used techniques, the compressed embedding technique performs the best. It can, to some extent, preserve main task accuracy while alleviating the attack threat. However, a more effective defense strategy that can maintain VFL inference accuracy as in a standard VFL inference system, while mitigating attack accuracy to baseline levels, remains unexplored, which serves as an important direction for future research.

# 7 RELATED WORK

7 RELATED WORKThe existing literature highlights various attacks against VFL, which can be broadly categorized into data inference attacks and targeted label attacks. The former, detailed in Section 7.1, aim to infer the private data of participants within the VFL system. In contrast, the latter, discussed in Section 7.2, focus on manipulating the VFL model to produce attacker- specified label through malicious actions.

# 7.1 Data Inference Attacks

Data inference attacks occur when a semi- honest (honest- but- curious) participant exploits the intermediate information exchanged during VFL training to deduce private data belonging to other participants. These attacks can be further divided into two types: feature reconstruction attacks [11, 12, 18, 21, 36, 40, 48] and label inference attacks [10, 22, 28, 45, 46, 54], based on the type of data being inferred.

Feature reconstruction attack refers to the active party attempting to reconstruct the passive party's raw features from their feature embeddings. He et al. [18] develop various approaches to achieve this. In a white- box setting, they minimize the distance between actual feature embeddings and dummy embeddings to reconstruct the original raw features. Similarly, in a black- box setting, they leverage an inverse network to map feature embeddings back to their corresponding raw features. Luo et al. [36] propose that if both the global VFL model and its predictions are accessible to the attacker, they could design a generator model to minimize the loss between the predictions based on generated features and the actual predictions, effectively transforming the feature reconstruction problem into a feature generation problem. Pasquini et al. [40] demonstrate that an untrustworthy participant could manipulate the model's learning procedure, leading it to a vulnerable state that facilitates the extraction of other participants' raw features.

Label inference attack is typically carried out by the passive party, who aims to infer the active party's private labels by analyzing the backpropagated gradients or their local bottom model. Li et al. [28] identify differences in gradient distribution and direction between positive and negative samples in imbalanced binary classification problems. Based on these observations, they developed two effective label inference methods. Fu et al. [10] introduce a Model Completion (MC) attack, where the attacker appends an inference head to their well- trained bottom model to create an attack model. This model is then fine- tuned with some labeled samples to achieve high accuracy, enabling the inference of labels in both training and testing datasets. Furthermore, Tan et al. [46] show that an attacker could achieve  $100\%$  accurate label inference by solving a linear system using the local dataset and the received gradients.

# 7.2 Targeted Label Attacks

The targeted label attack involves a malicious participant (usually the passive party) manipulating the VFL model to produce a targeted label of their choosing. These attacks can be categorized based on the stage at which they are executed into backdoor attacks [2, 35, 38] and adversarial attacks [15, 17, 33, 39, 42].

In backdoor attack, the attacker stealthily implants a backdoor in the VFL model during training. This backdoor is later triggered by specific samples at the inference phase, causing the model to misclassify them as the desired targeted label. Typically, attackers begin by conducting a label inference attack (as detailed in Section 7.1) to obtain labels, which they use to map trigger samples with the target label. Liu et al. [35] infer labels from intermediate gradients and swap gradients between trigger and target samples to inject the backdoor. The authors in [2] propose a novel embedding swapping algorithm to deduce labels, subsequently launching a backdoor attack with

a stealthy additive trigger embedded within the feature embedding. Similarly, Naseri et al. [38] introduce BadVFL, which employs the MC attack to infer labels of training samples, then utilizes a saliency map to identify crucial regions in the training samples for backdoor injection.

Adversarial attack in VFL involves an attacker following the VFL protocol during training but manipulating inputs at the inference phase to make the VFL model predict a specific targeted label. Qiu et al. [42] propose HijackVFL, where the attacker uses a labeled dataset to train an auxiliary model. This model optimizes malicious feature embeddings that mislead the VFL model during collaborative inference. Gu et al. [15] extend this idea with LR- BA, in which the attacker uses labeled training samples and applies the MC attack to train an additional model that generates malicious feature embeddings. Pang et al. [39] design Adversarial Dominating Inputs (ADI) that dominate the joint inference process, directing it toward the attacker's desired outcome while reducing the contributions of honest participants. He et al. [17] assume the attacker uses a large number of labeled samples (e.g., 500) from the target class to generate a trigger feature embedding. During inference, the attacker replaces their local embedding with this trigger feature embedding, causing the VFL model to misclassify any input as the target class.

Although these methods demonstrate that an attacker can mislead the global VFL model to output a targeted label, their effectiveness relies on strong assumptions. Specifically, these methods assume that the attacker has access to labeled samples in the training set. However, in real- world VFL applications, this assumption is unrealistic since labels are critical private assets of the active party. Moreover, these methods overlook the fact that malicious feature embeddings can be easily detected if the active party deploys private anomaly detection mechanisms. In contrast, in this paper, we address a more practical scenario where the attacker has minimal adversarial knowledge and no access to training sample labels. Additionally, we consider that the active party may employ anomaly detection techniques to identify adversarial embeddings from passive parties. Even in this challenging and realistic setting, our framework effectively misleads the VFL model to predict test samples as targeted label.

# 8 CONCLUSION

In this paper, we propose VTarbel, a two- stage attack framework designed for targeted label attacks against detector- enhanced VFL inference systems, requiring minimal adversarial knowledge. The framework consists of two stages: in the preparation stage, the attacker selects a minimal number of highly expressive samples for benign VFL inference and uses the predicted labels from these samples to locally train an estimated detector model and a surrogate model. In the attack stage, the attacker employs gradient- based optimization techniques to generate malicious samples based on the two models trained in the preparation stage, and then transfers these optimized samples to the VFL inference system to launch the attack. Comprehensive experiments demonstrate that VTarbel consistently outperforms other attack methods and remains resilient to representative defense strategies.

# REFERENCES

[1] Elie Alhajjar, Paul Maxwell, and Nathaniel Bastian. 2021. Adversarial machine learning in network intrusion detection systems. Expert Systems with Applications 186 (2021), 115782. [2] Yijie Bai, Yanjiao Chen, Hanlei Zhang, Wenyuan Xu, Haiqin Weng, and Dou Goodman. 2023. VILLAIN: Backdoor Attacks Against Vertical Split Learning. In 32nd USENIX Security Symposium (USENIX Security 23). USENIX Association, Anaheim, CA, 2743- 2760. https://www.usenix.org/conference/usenixsecurity23/presentation/bai[3] Sugato Basu, Arindam Banerjee, and Raymond Mooney. 2002. Semi- supervised clustering by seeding. In In Proceedings of 19th International Conference on Machine Learning (ICML- 2002. Citeseer.

[4] Giampaolo Bovenzi, Alessio Foggia, Salvatore Santella, Alessandro Testa, Valerio Persico, and Antonio Pescapé. 2022. Data poisoning attacks against autoencoder- based anomaly detection models: A robustness analysis. In ICC 2022- IEEE International Conference on Communications. IEEE, 5427- 5432. [5] Chaochao Chen, Jun Zhou, Li Wang, Xibin Wu, Wenjing Fang, Jin Tan, Lei Wang, Alex X Liu, Hao Wang, and Cheng Hong. 2021. When homomorphic encryption marries secret sharing: Secure large- scale sparse logistic regression and applications in risk control. In Proceedings of the 27th ACM SIGKDD Conference on Knowledge Discovery & Data Mining. 2652- 2662. [6] Yen- Chi Chen. 2017. A tutorial on kernel density estimation and recent advances. Biostatistics & Epidemiology 1, 1 (2017), 161- 187. [7] Luke N Darlow, Elliot J Crowley, Antreas Antoniou, and Amos J Storkey. 2018. Cinc- 10 is not imagenet or cifar- 10. arXiv preprint arXiv:1810.03505 (2018).[8] Yaakov Engel, Shie Mannor, and Ron Meir. 2004. The kernel recursive least- squares algorithm. IEEE Transactions on signal processing 52, 8 (2004), 2275- 2285. [9] Python Software Foundation. 1991. Python Socket Library. Available at https://docs.python.org/3/library/socket.html.[10] Chong Fu, Xuhong Zhang, Shouling Ji, Jinyin Chen, Jingzheng Wu, Shangqing Guo, Jun Zhou, Alex X Liu, and Ting Wang. 2022. Label inference attacks against vertical federated learning. In 31st USENIX Security Symposium (USENIX Security 22). 1397- 1414. [11] Jiayun Fu, Xiaojing Ma, Bin B Zhu, Pingyi Hu, Ruixin Zhao, Yaru Jia, Peng Xu, Hai Jin, and Dongmei Zhang. 2023. Focusing on Pinocchio's Nose: A Gradient Scrutinizer to Thwart Split- Learning Hijacking Attacks Using Intrinsic Attributes.. In NDSS.[12] Xinben Gao and Lan Zhang. 2023. PCAT: Functionality and Data Stealing from Split Learning by Pseudo- Client Attack. In 32nd USENIX Security Symposium (USENIX Security 23). USENIX Association, Anaheim, CA, 5271- 5288. https://www.usenix.org/conference/usenixsecurity23/presentation/gao[13] Arthur Gretton, Karsten M Borgstrudt, Malte J Rasch, Bernhard Scholkopf, and Alexander Smola. 2012. A kernel two- sample test. The Journal of Machine Learning Research 13, 1 (2012), 723- 773. [14] Jindong Gu, Xiaojun Jia, Pau le Jorge, Wenqain Yu, Xinwei Liu, Avery Ma, Yuan Xun, Anjun Hu, Ashkan Khakzar, Zhijiang Li, et al. 2023. A survey on transferability of adversarial examples across deep neural networks. arXiv preprint arXiv:2310.17626 (2023).[15] Yuhao Gu and Yuebin Bai. 2023. LR- BA: Backdoor attack against vertical federated learning using local latent representations. Computers & Security 129 (2023), 103193. [16] Kaiming He, Xiangyu Zhang, Shaoqing Ren, and Jian Sun. 2016. Deep residual learning for image recognition. In Proceedings of the IEEE conference on computer vision and pattern recognition. 770- 778. [17] Ying He, Zhili Shen, Jingyu Hua, Qixuan Dong, Jiacheng Niu, Wei Tong, Xu Huang, Chen Li, and Sheng Zhong. 2023. Backdoor Attack Against Split Neural Network- Based Vertical Federated Learning. IEEE Transactions on Information Forensics and Security (2023).[18] Zecheng He, Tianwei Zhang, and Ruby B Lee. 2019. Model inversion attacks against collaborative inference. In Proceedings of the 35th Annual Computer Security Applications Conference. 148- 162. [19] Nathan Inkawhich, Wei Wen, Hai Helen Li, and Yiran Chen. 2019. Feature space perturbations yield more transferable adversarial examples. In Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition. 7066- 7074. [20] Yongzhe Jia, Bowen Liu, Xuyun Zhang, Fei Dai, Arif Khan, Lianyong Qi, and Wanchun Dou. 2024. Model Pruning- enabled Federated Split Learning for Resource- constrained Devices in Artificial Intelligence Empowered Edge Computing Environment. ACM Transactions on Sensor Networks (2024).[21] Xiao Jin, Pin- Yu Chen, Chia- Yi Hsu, Chia- Mu Yu, and Tianyi Chen. 2021. Cafe: Catastrophic data leakage in vertical federated learning. Advances in Neural Information Processing Systems 34 (2021), 994- 1006. [22] Yan Kang, Jiahuan Luo, Yuanqin He, Xiaojin Zhang, Lixin Fan, and Qiang Yang. 2022. A framework for evaluating privacy- utility trade- off in vertical federated learning. arXiv preprint arXiv:2209.05883 (2022).[23] Afsana Khan, Marijin ten Thijj, and Anna Wilbik. 2022. Vertical Federated Learning: A Structured Literature Review. arXiv preprint arXiv:2212.00622 (2022).[24] Bernhard H Korte, Jens Vygen, B Korte, and J Vygen. 2011. Combinatorial optimization. Vol. 1. Springer.[25] K Krishna and M Narasimha Murty. 1999. Genetic K- means algorithm. IEEE Transactions on Systems, Man, and Cybernetics, Part B (Cybernetics) 29, 3 (1999), 433- 439. [26] Alex Krizhevsky, Vinod Nair, and Geoffrey Hinton. 2014. The CIFAR- 10 dataset. online: http://www.cs.toronto.edu/kriz/cifar.html 55, 5 (2014).[27] Yann LeCun, Corinna Cortes, and CJ Burges. 2010. MNIST handwritten digit database. ATT Labs [Online]. Available: http://yann.lecun.com/exdb/mnist 2 (2010).[28] Oscar Li, Jiankai Sun, Xin Yang, Weihao Gao, Hongyi Zhang, Junyuan Xie, Virginia Smith, and Chong Wang. 2022. Label Leakage and Protection in Two- party Split Learning. In International Conference on Learning Representations.

https://openreview.net/forum?id=cOtBRgsf2FO[29] Qun Li, Chandra Thapa, Lawrence Ong, Yifeng Zheng, Hua Ma, Seyit A Camtepe, Anmin Fu, and Yansong Gao. 2023. Vertical Federated Learning: Taxonomies, Threats, and Prospects. arXiv preprint arXiv:2302.01550 (2023).[30] Wenjie Li, Qiaolin Xia, Hao Cheng, Kouyin Xue, and Shu- Tao Xia. 2022. Vertical semi- federated learning for efficient online advertising. arXiv preprint arXiv:2209.15635 (2022).[31] Xin Li and Dan Roth. 2002. Learning Question Classifiers. In COLING 2002: The 19th International Conference on Computational Linguistics. https://www.aclweb.org/anthology/C02- 1150[32] Jingkai Liu, Xiaoting Lyu, Li Duan, Yongzhong He, Jiqiang Liu, Hongliang Ma, Bin Wang, Chunhua Su, and Wei Wang. 2024. Pna: Robust aggregation against poisoning attacks to federated learning for edge intelligence. ACM Transactions on Sensor Networks (2024).[33] Jing Liu, Chulin Xie, Sanmi Koyejo, and Bo Li. 2022. CoPur: certifiably robust collaborative inference via feature purification. Advances in Neural Information Processing Systems 35 (2022), 26645- 26657. [34] Yang Liu, Yan Kang, Tianyuan Zou, Yanhong Pu, Yuanqin He, Xiaozhou Ye, Ye Ouyang, Ya- Qin Zhang, and Qiang Yang. 2022. Vertical Federated Learning: Concepts, Advances and Challenges. arXiv:2211.12814 [cs.LG][35] Yang Liu, Zhihao Yi, and Tianjian Chen. 2020. Backdoor attacks and defenses in feature- partitioned collaborative learning. arXiv preprint arXiv:2007.03608 (2020).[36] Xinjian Luo, Yuncheng Wu, Xiaokui Xiao, and Beng Chin Ooi. 2021. Feature inference attack on model predictions in vertical federated learning. In 2021 IEEE 37th International Conference on Data Engineering (ICDE). IEEE, 181- 192. [37] Andrew Maas, Raymond E Daly, Peter T Pham, Dan Huang, Andrew Y Ng, and Christopher Potts. 2011. Learning word vectors for sentiment analysis. In Proceedings of the 49th annual meeting of the association for computational linguistics: Human language technologies. 142- 150. [38] Mohammad Naseri, Yufei Han, and Emiliano De Cristofaro. 2024. BadVFL: Backdoor Attacks in Vertical Federated Learning. In 2024 IEEE Symposium on Security and Privacy (SP). IEEE Computer Society, Los Alamitos, CA, USA, 2013- 2028. doi:10.1109/SP54263.2024.00008[39] Qi Pang, Yuanyuan Yuan, Shuai Wang, and Wenting Zheng. 2023. ADI: Adversarial Dominating Inputs in Vertical Federated Learning Systems. In 2023 IEEE Symposium on Security and Privacy (SP). IEEE Computer Society, Los Alamitos, CA, USA, 1875- 1892. doi:10.1109/SP46215.2023.00172[40] Dario Pasquini, Giuseppe Atteniese, and Massimo Bernaschi. 2021. Unleashing the Tiger: Inference Attacks on Split Learning. In Proceedings of the 2021 ACM SIGSAC Conference on Computer and Communications Security (Virtual Event, Republic of Korea) (CCS '21). Association for Computing Machinery, New York, NY, USA, 2113- 2129. doi:10.1145/3460120.3485259[41] Adam Paszke, Sam Gross, Francisco Massa, Adam Lerer, James Bradbury, Gregory Chanan, Trevor Killeen, Zeming Lin, Natalia Gimelshein, Luca Antiga, et al. 2019. PyTorch: An Imperative Style, High- Performance Deep Learning Library. In Advances in Neural Information Processing Systems 32. Curran Associates, Inc., 8024- 8035. http://papers.neurips.cc/paper/9015- pytorch- an- imperative- style- high- performance- deep- learning- library.pdf[42] Pengyu Qiu, Xuhong Zhang, Shouling Ji, Changjiang Li, Yuwen Pu, Xing Yang, and Ting Wang. 2024. Hijack Vertical Federated Learning Models As One Party. IEEE Transactions on Dependable and Secure Computing (2024), 1- 18. doi:10.1109/TDSC.2024.3358081[43] Victor Sanh, Lysandre Debut, Julien Chaumond, and Thomas Wolf. 2019. DistilBERT, a distilled version of BERT: smaller, faster, cheaper and lighter. arXiv preprint arXiv:1910.01108 (2019).[44] Karen Simonyan and Andrew Zisserman. 2014. Very deep convolutional networks for large- scale image recognition. arXiv preprint arXiv:1409.1556 (2014).[45] Jiankai Sun, Xin Yang, Yuanshun Yao, and Chong Wang. 2022. Label leakage and protection from forward embedding in vertical federated learning. arXiv preprint arXiv:2203.01451 (2022).[46] Juntao Tan, Lan Zhang, Yang Liu, Anran Li, and Ye Wu. 2022. Residue- based Label Protection Mechanisms in Vertical Logistic Regression. In 2022 8th International Conference on Big Data Computing and Communications (BigCom). 356- 364. doi:10.1109/BigCom57025.2022.00051[47] Kang Wei, Jun Li, Chuan Ma, Ming Ding, Sha Wei, Fan Wu, Guihai Chen, and Thilina Ranbaduge. 2022. Vertical federated learning: Challenges, methodologies and experiments. arXiv preprint arXiv:2202.04309 (2022).[48] Haiqin Weng, Juntao Zhang, Feng Xue, Tao Wei, Shouling Ji, and Zhiyuan Zong. 2020. Privacy leakage of real- world vertical federated learning. arXiv preprint arXiv:2011.09290 (2020).[49] Thomas Wolf, Lysandre Debut, Victor Sanh, Julien Chaumond, Clement Delangue, Anthony Moi, Pierric Cistac, Tim Rault, Remi Louf, Morgan Furtowicz, Joe Davison, Sam Shleifer, Patrick von Platten, Clara Ma, Yacine Jernite, Julien Plu, Canwen Xu, Teven Le Sao, Sylvain Gugger, Mariama Drame, Quentin Lloest, and Alexander M. Rush. 2020. Transformers: State- of- the- Art Natural Language Processing. In Proceedings of the 2020 Conference on Empirical Methods in Natural Language Processing: System Demonstrations. Association for Computational Linguistics, Online, 38- 45. https://www.aclweb.org/anthology/2020. emnlp- demos.6

[50] Han Xiao, Kashif Rasul, and Roland Vollgraf. 2017. Fashion- MNIST: a Novel Image Dataset for Benchmarking Machine Learning Algorithms. arXiv:cs/LG/1708.07747 [cs.LG][51] Liu Yang, Di Chai, Junxue Zhang, Yilun Jin, Leye Wang, Hao Liu, Han Tian, Qian Xu, and Kai Chen. 2023. A Survey on Vertical Federated Learning: From a Layered Perspective. arXiv preprint arXiv:2304.01829 (2023).[52] Qiang Yang, Yang Liu, Tianjian Chen, and Yongxin Tong. 2019. Federated machine learning: Concept and applications. ACM Transactions on Intelligent Systems and Technology (TIST) 10, 2 (2019), 1- 19. [53] Ligeng Zhu, Zhijian Liu, and Song Han. 2019. Deep leakage from gradients. Advances in neural information processing systems 32 (2019).[54] Tianyuan Zou, Yang Liu, Yan Kang, Wenhan Liu, Yuanqin He, Zhihao Yi, Qiang Yang, and Ya- Qin Zhang. 2022. Defending Batch- Level Label Inference and Replacement Attacks in Vertical Federated Learning. IEEE Transactions on Big Data (2022), 1- 12. doi:10.1109/TBDATA.2022.3192121