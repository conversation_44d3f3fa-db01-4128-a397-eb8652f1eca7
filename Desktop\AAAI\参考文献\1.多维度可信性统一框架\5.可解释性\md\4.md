# RESFL: An Uncertainty-Aware Framework for Responsible Federated Learning by Balancing Privacy, Fairness and Utility in Autonomous Vehicles

Dawood Wisif Virginia Tech Blacksburg, Virginia, USA <EMAIL>

Terrence <PERSON><PERSON> U.S. Army Research Laboratory Adelphi, Maryland, USA <EMAIL>

Jin- Hae Cho Virginia Tech Blacksburg, Virginia, USA <EMAIL>

# Abstract

Autonomous vehicles (AVs) increasingly rely on Federated Learning (FL) to enhance perception models while preserving privacy. However, existing FL frameworks struggle to balance privacy, fairness, and robustness, leading to performance disparities across demographic groups. Privacy- preserving techniques like differential privacy mitigate data leakage risks but worsen fairness by restricting access to sensitive attributes needed for bias correction. This work explores the trade- off between privacy and fairness in FL- based object detection for AVs and introduces RESFL, an integrated solution optimizing both. RESFL incorporates adversarial privacy disentanglement and uncertainty- guided fairness- aware aggregation. The adversarial component uses a gradient reversal layer to remove sensitive attributes, reducing privacy risks while maintaining fairness. The uncertainty- aware aggregation employs an evidential neural network to weight client updates adaptively, prioritizing contributions with lower fairness disparities and higher confidence. This ensures robust and equitable FL model updates. We evaluate RESFL on the FACET dataset and CARLA simulator, assessing accuracy, fairness, privacy resilience, and robustness under varying conditions. RESFL improves detection accuracy, reduces fairness disparities, and lowers privacy attack success rates while demonstrating superior robustness to adversarial conditions compared to other approaches.

# Keywords

federated learning, fairness, privacy, uncertainty, autonomous vehicles

# 1. Introduction

Ethical and Privacy Challenges in Autonomous Vehicle Machine Learning. The rapid advancement of autonomous vehicles (AVs) is transforming transportation through complex machine learning (ML) models that enable precise, reliable navigation in dynamic environments. As AVs evolve from driver assistance to full autonomy, ensuring robustness and ethical integrity is crucial. Beyond high performance, these models must also uphold privacy and fairness across diverse demographic groups [16].

Traditional centralized machine learning has significantly improved detection accuracy by aggregating large datasets [36]. However, centralizing data inherently risks exposing sensitive information, such as personal identifiers, skin tone, and gender, to misuse or unauthorized access. This concern is especially critical in AVs, where privacy breaches could erode public trust and pose serious ethical and legal challenges.

Challenges in Federated Learning (FL) for Privacy, Fairness, and Utility Preservation. FL has emerged as a promising solution to privacy concerns by enabling decentralized model training, ensuring data remains on local devices while only model updates are shared for aggregation. While this approach reduces data exposure, it introduces a critical trade- off: fairness often requires access to sensitive attributes, which FL deliberately withholds to protect privacy. This paradox creates a challenge where safeguarding privacy can inadvertently hinder bias correction, leading to inequitable model performance across demographic groups [19, 45].

The problem is further exacerbated by environmental uncertainties, such as lighting, weather, and sensor noise, that affect model confidence. If unaccounted for, these uncertainties can amplify biases, resulting in disproportionate misdetection rates across different populations. For instance, certain groups may experience higher error rates under specific conditions, reinforcing existing disparities and compromising AV system reliability. [27]

While existing methods enhance task performance and address privacy or fairness in isolation, they rarely tackle both [12, 33, 38, 43]. Centralized models inherently expose sensitive data, whereas many FL solutions prioritize privacy at the expense of fairness [5]. Post hoc fairness adjustments or training constraints often fail to capture the complex interplay between privacy protection and bias mitigation in a decentralized setting [21].

Bridging the Gaps with Our Approach. To address these limitations, our approach integrates two key components. First, we employ an adversarial network to remove sensitive attributes from feature representations. An auxiliary classifier predicts these attributes, while a gradient reversal layer suppresses this information, reducing privacy risks and ensuring fairer encodings across demographic groups. Second, we introduce an uncertainty- guided fairness evaluation, which dynamically adjusts client update weights based on estimated fairness and confidence. Rather than treating all updates equally, our adaptive aggregation prioritizes contributions with lower disparities and higher reliability, improving fairness and robustness. Therefore, our approach safeguards privacy while mitigating systemic biases, providing a more equitable and trustworthy

solution for autonomous vehicle systems. To reflect this contribution, we name our approach RESFL (Responsible FL), highlighting its commitment to privacy preservation, fairness, and trustworthiness.

Key Contributions. We make the following contributions:

We introduce RESFL, an integrated federated learning framework that simultaneously preserves individual privacy and ensures group fairness. Our method overcomes the common trade- off between using sensitive attributes for bias mitigation and protecting these attributes by jointly optimizing privacy and fairness. Unlike previous approaches that focus solely on differential privacy or fairness- aware re- weighting [31], RESFL combines adversarial privacy disentanglement with uncertainty- aware fairness aggregation to achieve robust, high- accuracy models while maintaining equitable performance across demographic groups. We develop a gradient reversal- based adversarial strategy that explicitly removes sensitive attribute information from feature representations, ensuring privacy while preserving fairness. Unlike prior adversarial approaches that rely on GAN- based data anonymization [34], validation- based privacy constraints [17], or adversarial reconstructions [46], our method proactively eliminates sensitive attribute leakage during FL training, enabling fairness- aware privacy protection. We introduce an uncertainty- guided fairness- aware aggregation mechanism that dynamically adjusts client update weights based on fairness disparities and confidence levels. Unlike traditional FL aggregation methods that treat all client updates equally [30], our approach prioritizes contributions from clients with lower fairness disparities and higher confidence, leading to a more robust, equitable, and reliable global model.

# 2. Related Work

This section reviews prior work on FL, privacy preservation, and fairness in machine learning, along with recent approaches addressing their interplay in FL.

# 2.1. Federated Learning

Federated Learning (FL) is a decentralized training paradigm where multiple clients collaboratively train a shared model while keeping data on- device. This mitigates privacy risks of centralized aggregation but introduces challenges, particularly data heterogeneity, as clients typically hold non- ID data [41]. Early FL research focused on improving communication efficiency and model convergence despite diverse local distributions.

Fairness in FL is considered from two perspectives. Client fairness ensures consistent performance across clients, regardless of data variations [20, 23, 24, 43]. Group fairness (algorithmic fairness) ensures equitable outcomes across demographic groups (e.g., gender, ethnicity), independent of the client source [18]. However, FL's privacy- preserving design limits central access to sensitive attributes, making group- level bias correction challenging [25].

# 2.2. Privacy Preservation Techniques

Preserving user privacy is a core objective in FL. Differential Privacy (DP) is widely used, adding calibrated noise to model updates to ensure formal privacy guarantees [10]. While effective in preventing privacy leakage, DP often relies on perturbation- based mechanisms that can degrade model utility [4].

Alternative approaches, such as homomorphic encryption (HE) [42] and secure multi- party computation (SMC) [35], provide strong privacy guarantees but suffer from high computational costs, limiting their scalability for large models [7, 39]. Consequently, research has shifted toward perturbation- based methods, including shuffler models, to balance privacy, utility, and communication efficiency [6, 11, 22]. However, most privacy solutions in FL neglect fairness, risking inequitable outcomes despite ensuring strong privacy protection.

# 2.3. Fairness in Federated Learning

Fairness in machine learning has been extensively studied in centralized settings, with various methods proposed to mitigate biases against underrepresented groups [14, 18]. Unlike traditional ML fairness research [26], FL considers fairness in two dimensions: client fairness, ensuring consistent model performance across clients despite dataset differences [20, 43], and group fairness, ensuring equitable outcomes across demographic groups while preventing systematic bias [18].

Traditional fairness strategies, such as fairness constraints or regularization terms, have proven effective in centralized settings [37]. However, applying them to FL is challenging since the central server lacks access to sensitive data for group fairness assessment [25]. Moreover, methods that equalize client performance often fail to address demographic disparities, highlighting the need for FL- specific fairness interventions.

# 2.4. Privacy-Preserving & Fair FL

Given the inherent tension between privacy and fairness, recent research has explored joint approaches to address both in FL. Privacy- preserving mechanisms like DP can exacerbate fairness disparities by disproportionately affecting underrepresented groups [44], while fairness- enhancing techniques may increase privacy risks by requiring access to sensitive attributes.

Yaghini et al. [40] introduced FairDP- SGD and FairPATE, modifying DP- SGD to balance privacy and fairness in centralized settings. However, these methods face scalability challenges in FL due to client heterogeneity. Other integrated approaches, such as Fair and Private FL (FPFL) [32], extend differential multiplier methods to enforce group fairness while maintaining DP guarantees but incur high communication costs from limited local updates. Two- step methods train a fair proxy model first, followed by a privacy- protected model aligning with the proxy [1, 29]. Pre- and post- processing techniques have also been explored to mitigate unfairness while preserving privacy [28]. Despite these advances, many approaches suffer from computational overhead, limited local training, or poor scalability [15].

Building on these insights, our proposed RESFL framework overcomes these limitations by integrating privacy preservation and fairness optimization into a single, scalable FL algorithm.

# 3. Preliminaries

This section presents the mathematical foundations and system specifications of our work. We detail the YOLOv8- based object

![](images/af7cd51c3af3ca6d40839b613305a26790d80b39712e8726da4e1e29b68be598.jpg)  
Figure 1: The Monk Skin Tone (MST) scale [27] ranges from  $\mathbf{MST} = \mathbf{1}$ , representing the lightest skin tone, to  $\mathbf{MST} = \mathbf{10}$ , representing the darkest skin tone.

detection model, describe the FL setup, formalize threat models (privacy, robustness, and fairness attacks), and define evaluation metrics. We also provide a unified overview of the datasets used for training and testing.

# 3.1. System Model: Object Detection

Let  $I \in \mathbb{R}^{H \times W \times C}$  denote an input image. Our object detection model, derived from YOLOv8, produces a set of detections:

$$
\mathcal{P} = \{(b_i,c_i,s_i)\}_{i = 1}^N,
$$

where each  $b_{i}\in \mathbb{R}^{4}$  specifies the bounding box coordinates,  $c_{i}\in$ $\{1,\ldots ,C\}$  is the predicted class label, and  $s_i\in [0,1]$  is the corresponding confidence score. The overall detection loss is given by:

$$
\mathcal{L}_{\mathrm{det}} = \lambda_{\mathrm{cls}}\mathcal{L}_{\mathrm{cls}} + \lambda_{\mathrm{loc}}\mathcal{L}_{\mathrm{loc}} + \lambda_{\mathrm{conf}}\mathcal{L}_{\mathrm{conf}}, \tag{1}
$$

where  $\mathcal{L}_{\mathrm{cls}}, \mathcal{L}_{\mathrm{loc}}$ , and  $\mathcal{L}_{\mathrm{conf}}$  represent the classification, localization, and confidence losses, respectively, and  $\lambda_{\mathrm{cls}}, \lambda_{\mathrm{loc}}, \lambda_{\mathrm{conf}} \in \mathbb{R}^{+}$  are hyperparameters.

# 3.2. Federated Learning Setup and Network Model

Consider a set of  $K$  clients  $\{C_k\}_{k = 1}^K$ , each possessing a local dataset  $\mathcal{D}_k \subset \mathbb{R}^{H \times W \times C}$  and a local model with parameters  $\theta_k$ . A central server maintains the global model  $\theta_G$ . The FL process begins with the server initializing and distributing  $\theta_G^{(0)}$  to all clients. Each client updates its model by performing local stochastic gradient descent (SGD):

$$
\theta_{k}^{(t + 1)} = \theta_{k}^{(t)} - \eta \nabla \mathcal{L}_{k}\left(\theta_{k}^{(t)}\right),
$$

where  $\eta > 0$  is the learning rate,  $t$  is the local iteration index, and  $\mathcal{L}_k$  is the local loss (e.g.,  $\mathcal{L}_{\mathrm{det}}$ ). The central server aggregates the locally updated parameters for FedAvg (baseline) as follow:

$$
\theta_{G}^{(t + 1)} = \sum_{k = 1}^{K}\frac{|\mathcal{D}_{k}|}{\sum_{j = 1}^{K}|\mathcal{D}_{j}|}\theta_{k}^{(t + 1)}.
$$

This FL framework maintains data privacy as raw data remains local while the server only receives model updates.

# 3.3. Uncertainty Quantification via Evidential Regression

Uncertainty quantification is crucial in deep learning to assess prediction reliability [2]. In pedestrian detection, epistemic uncertainty reflects the model's lack of knowledge, making it vital for fairness evaluation. Unlike aleatoric uncertainty, which arises from inherent data randomness and is largely irreducible, epistemic uncertainty decreases with more representative training data. This makes it a suitable metric for detecting confidence disparities across demographic groups. We adopt the evidential prior approach from [3] to compute epistemic uncertainty.

Given an observation  $x$ , the true target  $y$  is assumed to follow a normal distribution:

$$
y\sim \mathcal{N}(\mu ,\sigma^2), \tag{2}
$$

where  $\mu$  represents the predicted mean. Instead of treating  $\sigma^2$  as an explicit model output, we introduce an evidential prior and model  $(\mu , \sigma^2)$  with a Normal- Inverse- Gamma (NIG) distribution:

$$
(\mu ,\sigma^2)\sim \mathrm{NIG}(\gamma ,\nu ,\alpha ,\beta), \tag{3}
$$

where  $\gamma$  is the prior mean,  $\nu$  governs confidence in  $\gamma$ , and  $\alpha$ , and  $\beta$  define the inverse gamma distribution over  $\sigma^2$ .

The expected values of the key parameters are:

$$
E[\mu ] = \gamma ,\quad \mathrm{Var}[\mu ] = \frac{\beta}{\nu(\alpha - 1)}. \tag{4}
$$

Epistemic uncertainty is given by  $\mathrm{Var}[\mu ]$ , which reflects the model's uncertainty in its predictions. A higher variance indicates lower confidence, making it useful for fairness assessment.

The evidential loss function consists of:

$$
\mathcal{L} = \mathcal{L}_{\mathrm{NLL}} + \lambda \mathcal{L}_{\mathrm{regularization}}, \tag{5}
$$

where  $\mathcal{L}_{\mathrm{NLL}}$  is the negative log- likelihood of the Student's t- distribution derived from marginalizing over the evidential prior, and  $\mathcal{L}_{\mathrm{regularization}}$  penalizes overconfident predictions:

$$
\mathcal{L}_{\mathrm{regularization}} = |y - E[\mu ]|\cdot (2\nu +\alpha). \tag{6}
$$

For implementation in YOLOv8, the output layer is modified to predict the evidential parameters  $(\gamma , \nu , \alpha , \beta)$  instead of a single deterministic mean. This estimates epistemic uncertainty by modeling predictions as probability distributions rather than fixed values.

For a given detection, the bounding box coordinates  $(x, y, w, h)$  follow an NIG distribution by:

$$
(x,y,w,h)\sim \mathrm{NIG}(\gamma ,\nu ,\alpha ,\beta), \tag{7}
$$

which provides a measure of localization uncertainty. Class scores are modeled as a distribution, allowing variance in classification confidence. The confidence score follows a probabilistic distribution, enabling uncertainty- aware detection decisions.

# 3.4. Threat Model

We consider adversarial threats to privacy, robustness, and fairness, as they commonly impact real- world FL deployments and AV system integrity. Privacy attacks assess sensitive data leakage risks, robustness attacks test resilience against malicious updates, and fairness attacks examine susceptibility to bias amplification.

3.4.1. Privacy Attacks The selected privacy attacks assess whether an adversary can extract sensitive information from federated model updates.

Membership Inference Attack (MIA): MIA aims to determine if a sample  $x\in \mathbb{R}^d$  was used in model training. An adversarial client  $C_a$  employs a shadow model strategy, where a shadow model mimics the global model  $M_{t}$  by training on a similarly distributed dataset. The attacker queries  $M_{t}$  with known training and non- training samples, collecting output confidence scores (i.e., probability distributions over class labels). Overfitted models tend to assign higher confidence to training samples, creating a statistical gap between in- training and out- of- training examples. These scores serve as training data for an auxiliary binary classifier  $\mathcal{A}_{\mathrm{MIA}}$  , which learns to distinguish members from non- members:

$$
\mathcal{A}_{\mathrm{MIA}}(x) = \left\{ \begin{array}{ll}1, & \mathrm{if}~x\in \mathcal{D}_k,\\ 0, & \mathrm{otherwise}. \end{array} \right. \tag{8}
$$

The Membership Inference Attack Success Rate quantifies MIA performance:

$$
S_{\mathrm{MIA}} = \frac{TP + TN}{TP + TN + FP + FN}, \tag{9}
$$

where  $TP$  and  $TN$  denote correctly identified training and nontraining samples, while  $FP$  and  $FN$  represent misclassifications. A higher  $S_{\mathrm{MIA}}$  indicates greater privacy leakage, revealing whether specific data points were used in training.

Attribute Inference Attack (AIA): While MIA targets membership, AIA is concerned with inferring sensitive attributes  $s\in S$  from the training data. In this attack, the adversary leverages the fact that the gradient updates computed during local training are often correlated with the sensitive attributes of the data. By carefully monitoring these updates, an adversary applies a gradient inversion technique to recover or approximate the values of sensitive attributes. More specifically, let  $I$  denote the input information extracted from the observed gradients; the adversary then employs an inference model  $\mathcal{A}_{\mathrm{AIA}}$  to compute the estimate  $\hat{s}$  as follows:

$$
\hat{s} = \mathcal{A}_{\mathrm{AIA}}(I). \tag{10}
$$

The Attribute Inference Accuracy measures the success of this attack:

$$
A_{\mathrm{AIA}} = \frac{TP}{TP + FP}, \tag{11}
$$

where  $TP$  represents the number of instances in which the sensitive attribute was correctly inferred and  $FP$  denotes the number of incorrect inferences. A high  $A_{\mathrm{AIA}}$  implies that the adversary is able to reliably extract sensitive information, thus signifying a serious privacy risk in the federated learning framework.

3.4.2. Robustness Attack We assess the FL system's resilience to malicious modifications of model updates using a robustness attack.

Byzantine Attack: In a Byzantine attack, a subset of clients manipulates their model updates before sending them to the central aggregator. Let  $\theta_{k}$  be the legitimate update from client  $k$  and let the adversary introduce a perturbation  $\delta_{k}$  yielding a modified update:

$$
\tilde{\theta}_{k} = \theta_{k} + \delta_{k},\quad \mathrm{with}\quad \| \delta_{k}\| \gg 0. \tag{12}
$$

A sufficiently large  $\delta_{k}$  disrupts training, leading to model divergence or severe performance degradation. The attack's impact is quantified by comparing global model accuracy without malicious interference  $(A_{\mathrm{clean}})$  to accuracy under Byzantine updates  $(A_{\mathrm{Byzantine}})$  , measured as:

$$
D_{\mathrm{Byz}} = A_{\mathrm{clean}} - A_{\mathrm{Byzantine}}. \tag{13}
$$

A larger  $D_{\mathrm{Byz}}$  indicates a stronger attack and greater vulnerability of the federated learning system to such perturbations.

3.4.3. Fairness Attack We evaluate fairness attacks introducing model bias and systematic disparities across demographic groups.

Data Poisoning Attack: This attack injects manipulated samples  $\Delta \mathcal{D}$  into a client's local dataset, altering its distribution. The poisoned dataset is defined as:

$$
\mathcal{D}_k^{\prime} = \mathcal{D}_k\cup \Delta \mathcal{D}. \tag{14}
$$

The adversary selects injected samples to skew feature distributions, favoring one demographic group over another and shifting the global model's decision boundaries. The impact on fairness is measured using the Equalized Odds Difference (EOD), which quantifies disparities in true positive rates (TPR) and false positive rates (FPR) between protected and unprotected groups:

$$
\begin{array}{r}EOD = \left|\mathrm{TPR}_{\mathrm{protected}} - \mathrm{TPR}_{\mathrm{unprotected}}\right|\\ +\left|\mathrm{FPR}_{\mathrm{protected}} - \mathrm{FP}_{\mathrm{Runprotected}}\right|. \end{array} \tag{15}
$$

To assess the attack's effect, we compute the Equalized Odds Difference Deviation (EODD) as the change in EOD between the poisoned and clean datasets:

$$
EODD = EOD_{\mathrm{poisoned}} - EOD_{\mathrm{clean}}. \tag{16}
$$

A larger EODD indicates greater fairness violation, confirming the attack's success in introducing bias. This highlights the dual threat of data poisoning, which compromises both model accuracy and equity across user groups.

# 4. Proposed Approach: RESFL

This section introduces our integrated privacy- preserving and fairnessaware Federated Learning framework, responsible FL (RESFL).Our approach tackles two key challenges: (i) preventing sensitive attribute leakage during training to ensure privacy and (ii) mitigating bias in client updates to enforce fairness.

To achieve this, we integrate adversarial privacy disentanglement with uncertainty- guided fairness- aware aggregation. Sensitive attributes aid training by aligning gradient contributions across diverse client distributions but are not required during inference, ensuring privacy- preserving deployment. The following subsections detail the key components and their mathematical formulations.

# 4.1. Uncertainty and Fairness in FL

This section analyzes epistemic uncertainty and its role in fairness within our federated learning framework. We quantify epistemic uncertainty using an evidential approach [3] and leverage it to assess and mitigate fairness disparities across sensitive groups.

4.1.1. Evidential Framework for Epistemic Uncertainty Consider a classification task with  $C$  classes (a single pedestrian class in our case). Instead of outputting a probability vector, our model generates an evidence vector  $\pmb {\alpha} = (\alpha_{1},\alpha_{2},\dots,\alpha_{C})$  ,where  $\alpha_{c}\geq 0$  for

each class  $c$ . This evidence serves as the concentration parameters of a Dirichlet distribution over class probabilities:

$$
p(\mathbf{p}\mid \pmb {\alpha}) = \frac{\Gamma(\sum_{c = 1}^{C}\alpha_c)}{\prod_{c = 1}^{C}\Gamma(\alpha_c)}\prod_{c = 1}^{C}p_c^{\alpha_c - 1}, \tag{17}
$$

where  $\Gamma (\cdot)$  is the Gamma function, and  $\mathbf{p} = (p_{1},p_{2},\ldots ,p_{C})$  represents the class probabilities. The Dirichlet distribution provides a multinomial opinion, generalizing the Beta distribution (which applies in the binomial case) by defining uncertainty over multiple categorical outcomes.

The total evidence is defined as:

$$
\alpha_{0} = \sum_{c = 1}^{C}\alpha_{c}. \tag{18}
$$

In our framework, epistemic uncertainty is inversely proportional to the total evidence:

$$
\sigma_{\mathrm{epistemic}}^2 = \frac{1}{\alpha_0}. \tag{19}
$$

This implies that as  $\alpha_0$  increases, uncertainty in class probabilities decreases.

To integrate this into YOLOv8, we modify the output layer to predict  $\alpha$  instead of softmax probabilities, applying the softplus function to ensure non- negative outputs:

$$
\alpha_{c} = 1 + \log (1 + e^{zc}), \tag{20}
$$

where  $z_c$  is the raw network output.

To prevent overconfidence and enable meaningful uncertainty estimates, we define an evidential loss function:

$$
\mathcal{L} = \mathcal{L}_{\mathrm{NLL}} + \lambda \mathcal{L}_{\mathrm{regularization}}, \tag{21}
$$

where  $\mathcal{L}_{\mathrm{NLL}}$  is the negative log- likelihood (NLL) loss:

$$
\mathcal{L}_{\mathrm{NLL}} = \sum_{c = 1}^{C}(y_c - \hat{p}_c)^2 +\frac{y_c(1 - y_c)}{\alpha_0 + 1}, \tag{22}
$$

which penalizes misclassification while incorporating uncertainty.

The regularization term discourages overconfidence in incorrect predictions:

$$
\mathcal{L}_{\mathrm{regularization}} = \sum_{c = 1}^{C}|y_c - \hat{p}_c|\cdot (2\alpha_0 + 1). \tag{23}
$$

This ensures epistemic uncertainty increases when predictions are incorrect, leading to better- calibrated uncertainty estimates.

4.1.2. Group- Level Uncertainty and Fairness Disparity Assume the data are partitioned into  $G$  sensitive groups, where each group  $g$  has its own evidence vector  $\alpha^{(g)}$  and total evidence  $\alpha_0^{(g)}$ . The epistemic uncertainty for group  $g$  is given by:

$$
\left(\sigma_{\mathrm{epistemic}}^2\right)^{(g)} = \frac{1}{\alpha_0^{(g)}}. \tag{24}
$$

A fair model should exhibit similar confidence across all groups, meaning the epistemic uncertainties  $\left(\sigma_{\mathrm{epistemic}}^2\right)^{(1)},\ldots ,\left(\sigma_{\mathrm{epistemic}}^2\right)^{(G)}$

should be approximately equal. To quantify confidence disparity, we compute the variance of epistemic uncertainty:

$$
\mathrm{Var}\Big(\sigma_{\mathrm{epistemic}}^2\Big) = \frac{1}{G}\sum_{g = 1}^{G}\left(\frac{1}{\alpha_0^{(g)}} -\overline{u}\right)^2, \tag{25}
$$

where the mean epistemic uncertainty is:

$$
\overline{u} = \frac{1}{G}\sum_{g = 1}^{G}\frac{1}{\alpha_0^{(g)}}. \tag{26}
$$

A higher  $\mathrm{Var}\left(\sigma_{\mathrm{epistemic}}^2\right)$  indicates greater uncertainty disparities, suggesting potential unfair treatment. If a group  $g^*$  has significantly lower  $\alpha_0^{(g^*)}$  than the average, then:

$$
\left(\sigma_{\mathrm{epistemic}}^2\right)^{(g^*)} = \frac{1}{\alpha_0^{(g^*)}} \tag{27}
$$

which will be much larger than  $\overline{u}$ , indicating lower confidence and a higher likelihood of errors for group  $g^*$ .

4.1.3. Uncertainty Fairness Metric To quantify disparity in epistemic uncertainty, we define the Uncertainty Fairness Metric (UFM) as:

$$
\mathrm{UFM} = \frac{\max_g\left(\frac{1}{\alpha_0^{(g)}}\right) - \min_g\left(\frac{1}{\alpha_0^{(g)}}\right)}{\frac{1}{G}\sum_{g = 1}^{G}\frac{1}{\alpha_0^{(g)}} + \epsilon}, \tag{28}
$$

where  $\epsilon > 0$  ensures numerical stability. A lower UFM indicates similar epistemic uncertainties across all groups, suggesting fairer model predictions. In our RESFL, a low UFM implies consistent confidence across sensitive groups, enhancing fairness.

# 4.2. Theoretical Justification of Epistemic Uncertainty as a Fairness Measure

To theoretically justify the relationship between epistemic uncertainty and fairness, we consider a scenario where data for each sensitive group  $g$  follows a Gaussian distribution with unknown mean  $\mu_g$  and variance  $\sigma_g^2$ . Using a Normal- Inverse- Gamma prior, the posterior distribution for  $(\mu_g,\sigma_g^2)$  can be computed in closed form, yielding the posterior mean:

$$
\mathbb{E}[\mu_g\mid \mathbf{x}_g] = \mu_{N,g}, \tag{29}
$$

and the variance of  $\mu_g$  (reflecting epistemic uncertainty):

$$
\mathrm{Var}[\mu_g\mid \mathbf{x}_g] = \frac{\beta_{N,g}}{\alpha_{N,g} - 1}\frac{1}{\kappa_{N,g}}, \tag{30}
$$

where  $\mu_{N,g},\kappa_{N,g},\alpha_{N,g}$ , and  $\beta_{N,g}$  are the updated hyperparameters for group  $g$ .

Under fair conditions, all groups should have similar data quality and sample sizes, leading to nearly identical  $\kappa_{N,g},\alpha_{N,g}$ , and  $\beta_{N,g}$ , and thus similar  $\mathrm{Var}[\mu_g\mid \mathbf{x}_g]$ . However, if certain groups are underrepresented or have noisier data,  $\kappa_{N,g}$  decreases, increasing  $\mathrm{Var}[\mu_g\mid \mathbf{x}_g]$ . Mathematically, for groups  $g_1$  and  $g_2$ :

$$
\begin{array}{rlr}\kappa_{N,g_1}< \kappa_{N,g_2} & \Longrightarrow & \mathrm{Var}[\mu_{g_1}\mid \mathbf{x}_{g_1}] > \mathrm{Var}[\mu_{g_2}\mid \mathbf{x}_{g_2}], \end{array} \tag{31}
$$

indicating lower model confidence for group  $g_1$ . This disparity is captured by the UFM, which provides a theoretical measure of

fairness. Minimizing UFM during training promotes more uniform epistemic uncertainty across groups, reducing bias.

UFM leverages model confidence (epistemic uncertainty) as a fairness proxy, offering a nuanced bias assessment sensitive to the model's internal state. By normalizing the difference with mean uncertainty, UFM remains robust across datasets and tasks with varying uncertainty scales. The metric modulates client update weights during global aggregation:

$$
\omega_{i} = \frac{1}{1 + \mathrm{UFM}_{i}}, \tag{32}
$$

ensuring that clients with lower epistemic uncertainty disparities, indicating fairer and more consistent performance across groups, contribute more significantly to the global model update.

For a detailed discussion on the theoretical analysis and proof of UFM, refer to Appendix A, which covers its theoretical foundation and practical implications for RESFL.

# 4.3. Adversarial Privacy Disentanglement via Gradient Reversal

To prevent sensitive information leakage, we introduce an adversarial network  $A(\cdot ;\phi)$  that predicts the sensitive attribute  $s$  from a latent representation  $\mathbf{h}\in \mathbb{R}^d$  extracted from an intermediate layer of the detection model. Formally, the adversary is defined as:

$$
A:\mathbb{R}^d\to [0,1]^K,\quad \mathbf{h}\mapsto A(\mathbf{h};\phi), \tag{33}
$$

where  $K$  is the number of sensitive attribute classes. The adversary is trained using cross- entropy loss:

$$
\mathcal{L}_A = -\mathbb{E}(\mathbf{h},s)\sim \mathcal{D}\left[\log A(s\mid \mathbf{h})\right]. \tag{34}
$$

To ensure feature representations do not reveal sensitive information, we employ a Gradient Reversal Layer (GRL), which enforces invariance to  $s$ . The GRL is defined as:

$$
\mathrm{GRL}(\mathbf{x}) = \mathbf{x},\quad \frac{\partial\mathrm{GRL}(\mathbf{x})}{\partial\mathbf{x}} = -\lambda_{\mathrm{adv}}I, \tag{35}
$$

where  $\lambda_{\mathrm{adv}} > 0$  controls the adversarial signal strength, and  $I$  is the identity matrix. This layer reverses the gradients of  $\mathcal{L}_A$  during backpropagation, forcing the feature extractor to remove sensitive attribute information. The resulting privacy adversarial loss is:

$$
\mathcal{L}_{\mathrm{prior}} = \lambda_{\mathrm{adv}}\mathcal{L}_A. \tag{36}
$$

The implementation details of adversarial privacy disentanglement are provided in Appendix B.

# 4.4. FL Training and Aggregation

Each client minimizes a composite loss during local training, combining the primary detection loss  $\mathcal{L}_{\mathrm{task}}$  , an uncertainty regularization term  $\mathcal{L}_{\mathrm{uncertainty}}$  , and an adversarial loss  $\mathcal{L}_A$  ..

$$
\mathcal{L}_{\mathrm{local}} = \mathcal{L}_{\mathrm{task}} + \lambda_1\mathcal{L}_{\mathrm{uncertainty}} + \lambda_{\mathrm{adv}}\mathcal{L}_A. \tag{37}
$$

Local training is performed using stochastic gradient descent (SGD). For each client  $i$ , the model parameters  $\theta_{i}$  are updated as:

$$
\theta_{i}^{(t + 1)} = \theta_{i}^{(t)} - \eta \nabla_{\theta}\mathcal{L}_{\mathrm{local}}\Big|_{\theta = \theta_{i}^{(t)}}, \tag{38}
$$

where  $\eta$  is the learning rate. The adversary network parameters  $\phi$  are updated by:

$$
\phi^{(t + 1)} = \phi^{(t)} - \eta_{\phi}\nabla_{\phi}\mathcal{L}_{A}\Big|_{\phi = \phi^{(t)}}, \tag{39}
$$

where  $\eta_{\phi}$  is the adversary's learning rate.

After local training, each client computes its  $\mathrm{UFM}_i$  based on epistemic uncertainty variance and sends its update  $\Delta \theta_{i}$  along with  $\mathrm{UFM}_i$  to the server. Global aggregation follows a weighted update:

$$
\theta_{G}^{(t + 1)} = \theta_{G}^{(t)} + \eta \sum_{i = 1}^{N}\omega_{i}\Delta \theta_{i}, \tag{40}
$$

where client weights are defined as:

$$
\omega_{i} = \frac{1}{1 + \mathrm{UFM}_{i}}. \tag{41}
$$

Algorithm 1 summarizes the overall training procedure. This weighting scheme prioritizes clients with lower fairness disparities, ensuring a more equitable global model update.

# Algorithm 1 Fairness-Aware FL with Adversarial Privacy

1: Input: Global model  $\theta_G$  , client datasets  $\{\mathcal{D}_i\}_{i = 1}^N$  total rounds  $T_{\parallel}$  adversary  $A(\cdot ;\phi)$  2:for  $t = 1,\ldots ,T$  do 3: Model Distribution: Server distributes  $\theta_G^{(t)}$  to all clients. 4: for  $i = 1,\ldots ,N$  do 5: Initialize local model:  $\theta_{i}^{(t)} = \theta_{G}^{(t)}$  6: for each local iteration do  $\theta_{i}^{(t + 1)} = \theta_{i}^{(t)} - \eta \nabla_{\theta}\Big(\mathcal{L}_{\mathrm{task}} + \lambda_{1}\mathcal{L}_{\mathrm{uncertainty}} + \lambda_{\mathrm{adv}}\mathcal{L}_{A}\Big)$  (42)  $\phi^{(t + 1)} = \phi^{(t)} - \eta_{\phi}\nabla_{\phi}\mathcal{L}_{A}$  (43) 7: end for 8: Compute  $\mathrm{UFM}_i$  using Equation (7) and derive weight  $\omega_{i}$  via Equation (8). 9: Send update  $\Delta \theta_{i}$  and  $\mathrm{UFM}_i$  to server. 10: end for 11: Global Aggregation:

Global Aggregation:

$$
\theta_G^{(t + 1)} = \theta_G^{(t)} + \eta \sum_{i = 1}^N\frac{1}{1 + \mathrm{UFM}_i}\Delta \theta_i. \tag{44}
$$

12: end for

13: Output: Global model  $\theta_G$  that is both privacy- preserving and fairness- aware.

# 5. Experimental Setup

# 5.1. Datasets

Our experiments employ two complementary datasets: FACET for training and CARLA for evaluation. Both are used to assess model performance, fairness, and robustness under varied conditions.

5.1.1. FACET Dataset The FACET dataset [13] is a state- of- the- art benchmark for fairness evaluation in computer vision, comprising 32,000 images with 50,000 annotated person instances. Each annotation includes bounding boxes and person- related attributes such as perceived skin tone, hair type, and person class. We focus on

Table 1: Comparison of FL Algorithms on the FACET Dataset: This analysis evaluates detection performance (mAP), fairness  $(|1 - DI|,\Delta \mathrm{EOP})$  privacy (MIA, AIA success rates), and robustness (BA AD, DPA EODD). RESFL excels in fairness and privacy while maintaining competitive utility against all other counterparts.  

<table><tr><td rowspan="2">Algorithm</td><td colspan="2">Utility</td><td colspan="2">Fairness</td><td colspan="2">Privacy Attacks</td><td>Robustness Attack</td><td rowspan="2">Fairness Attack</td></tr><tr><td>Overall mAP</td><td>|1 - DI|</td><td>ΔEOP</td><td>MIA SR</td><td>AIA SR</td><td>BA AD</td><td>DRA EODD</td></tr><tr><td>FedAvg</td><td>0.6378</td><td>0.2159</td><td>0.2362</td><td>0.3341</td><td>0.4431</td><td>0.3125</td><td>0.0792</td><td></td></tr><tr><td>FedAvg-DP (ε = 0.1)</td><td>0.2932</td><td>0.4521</td><td>0.3576</td><td>0.1765</td><td>0.2154</td><td>0.2833</td><td>0.1724</td><td></td></tr><tr><td>FedAvg-DP (ε = 0.5)</td><td>0.4741</td><td>0.3869</td><td>0.2793</td><td>0.2286</td><td>0.2539</td><td>0.3019</td><td>0.1328</td><td></td></tr><tr><td>FairFed</td><td>0.7013</td><td>0.2496</td><td>0.2562</td><td>0.4409</td><td>0.5256</td><td>0.4139</td><td>0.0566</td><td></td></tr><tr><td>PrivFairFI-Pre</td><td>0.6154</td><td>0.2504</td><td>0.2659</td><td>0.3875</td><td>0.4038</td><td>0.3238</td><td>0.0953</td><td></td></tr><tr><td>PrivFairFI-Post</td><td>0.6119</td><td>0.2718</td><td>0.2505</td><td>0.2872</td><td>0.3159</td><td>0.3212</td><td>0.0937</td><td></td></tr><tr><td>PUFFLE</td><td>0.4192</td><td>0.3721</td><td>0.2976</td><td>0.2725</td><td>0.2909</td><td>0.1439</td><td>0.1360</td><td></td></tr><tr><td>PFU-FL</td><td>0.3952</td><td>0.3356</td><td>0.3446</td><td>0.2409</td><td>0.2546</td><td>0.2612</td><td>0.1459</td><td></td></tr><tr><td>Ours (RESFL)</td><td>0.6654</td><td>0.2287</td><td>0.1959</td><td>0.2093</td><td>0.1832</td><td>0.1692</td><td>0.0674</td><td></td></tr></table>

perceived skin tone, a key sensitive attribute correlated with model performance disparities [27].

FACET employs the Monk Skin Tone (MST) scale for skin tone annotations, as shown in Figure 1. The MST scale spans 10 levels, from  $\mathrm{MST} = 1$  (lightest) to  $\mathrm{MST} = 10$  (darkest). Due to ambient lighting effects, a single annotation may capture a range of skin tones. Following [13], we consolidate multiple annotations into a robust estimate. Given skin tone annotations  $s_1,s_2,\ldots ,s_n$  for an individual, we compute:

$$
s^* = \frac{1}{n}\sum_{i = 1}^{n}s_i, \tag{45}
$$

mapping  $s^*$  onto the MST scale. This ensures that fairness evaluation remains sensitive to subtle skin tone variations that may impact detection performance.

5.1.2. CARLA Simulation Dataset CARLA [9] is a high- fidelity driving simulator for controlled autonomous vehicle scenarios under diverse weather conditions. An autopilot- enabled vehicle with RGB and semantic segmentation cameras captures frames every 3 seconds. Only frames with at least one pedestrian are retained. Pedestrian bounding boxes are extracted from semantic segmentation labels. Connected component analysis identifies pedestrian regions, filtering out non- relevant areas (e.g., occlusions or pedestrians outside the ego vehicle's direct line of sight). Each detected pedestrian is assigned a bounding box  $b\in \mathbb{R}^4$  based on the segmented region's minimum and maximum coordinates.

Skin tone labels are assigned using the Monk Skin Tone (MST) scale, where  $s\in \{1,2,\ldots ,10\}$  determined via manual review of the pedestrian catalog. Each pedestrian blueprint is mapped to an MST label via visual assessment. To address the domain shift between training (FACET) and evaluation (CARLA), FL models undergo fine- tuning on a baseline CARLA subset (no weather variations) with 200 images per skin tone (total 2,000 images), adapting the model to the target domain before evaluation.

The CARLA evaluation dataset systematically incorporates three environmental conditions, including clear, foggy, and rainy, each simulated at intensities of  $9\%$ $25\%$ $50\%$ $75\%$  and  $100\%$  Figure 2. A baseline condition (no adverse weather) is included, yielding 13 variations (12 weather intensities  $+1$  baseline). For each variation and skin tone category, 20 images are captured, resulting in:

$$
N_{\mathrm{CARLA}} = 20\times 13\times 10 = 2600\mathrm{images}. \tag{46}
$$

By fine- tuning the FACET baseline and evaluating on CARLA, this unified dataset strategy enables a robust assessment of object detection performance in adverse AV conditions.

# 5.2. FL Configuration and Domain Adaptation

Our proposed RESFL is configured with four clients, 100 communication rounds, a learning rate of 0.001, and a batch size of 64, using the YOLOv8 architecture. FACET is used for training and evaluation in Section 6.1. To mitigate the domain gap between FACET and CARLA, FL models undergo fine- tuning on a baseline CARLA subset with 200 images per skin tone (2000 total) under neutral weather conditions. This step adapts the model to the target domain before evaluating the full weather variations dataset in CARLA.

# 5.3. Comparing Schemes

We compare our RESFL method against several baseline approaches:

FedAvg aggregates client updates using weighted averaging, serving as the standard federated learning baseline. FedAvg- DP (Objective) implements differential privacy by injecting calibrated noise into the objective function during local updates, evaluated at two privacy budgets (  $\epsilon = 0.1$  and  $\epsilon = 0.5$  - FairFed [12] adjusts aggregation weight to enhance fairness by penalizing performance gaps across heterogeneous client data. PrivFairFL [28] introduces fairness constraints at different stages of training: (1) PrivFairFL- Pre applies fairness constraints before aggregation. (2) PrivFairFL- Post enforces fairness adjustments after local training. PUFFLE [8] integrates noise injection with fairness regularization through a joint optimization framework. PFU- FL [1] adopts adaptive weighting strategies to balance privacy, fairness, and utility in federated learning.

# 5.4. Metrics

The evaluation metrics include detection performance (mAP), fairness  $(|1 - \mathrm{DI}|$  , the absolute deviation from ideal disparate impact, and  $\Delta \mathrm{EOP}$  , the Equality of Opportunity difference across groups),

![](images/0388651aecb960a790f25c3172386af3e4e135157243c07abea7f6f06ee3dc86.jpg)  
Figure 2: Sample visualization of weather conditions (Cloud, Rain, and Fog) at increasing intensity levels (0%, 25%, 50%, 75%, 100%) using the CARLA simulation, illustrating how environmental severity gradually impacts visibility and scene clarity.

privacy (Membership Inference Attack (MIA) and Attribute Inference Attack (AIA) success rates), and robustness (BA Accuracy Degradation (BA AD) and DPA EOD Deviation (DPA EODD)). Each metric is defined as follows.

5.4.1. Detection Performance Utility is measured by Mean Average Precision (mAP), computed as the mean of Average Precision (AP) across all classes:

$$
\mathrm{mAP} = \frac{1}{C}\sum_{c = 1}^{C}\mathrm{AP}_c. \tag{47}
$$

5.4.2. Fairness Metrics Fairness is evaluated using two metrics. First, the absolute deviation from ideal Disparate Impact (DI) is defined as:

$$
|1 - \mathrm{DI}| = \left|1 - \frac{\mathrm{Pr}(\mathrm{favorable~outcome}\mid s = s_1)}{\mathrm{Pr}(\mathrm{favorable~outcome}\mid s = s_2)}\right|. \tag{48}
$$

Second, the Equality of Opportunity difference is given by:

$$
\Delta \mathrm{EOP} = \left|\mathrm{TPR}_{s = s_1} - \mathrm{TPR}_{s = s_2}\right|, \tag{49}
$$

where  $\mathrm{TPR}_{s = s_i}$  represents the true positive rate for group  $s_i$

5.4.3. Privacy Attacks Privacy is assessed using two metrics. The Membership Inference Attack Success Rate (MIA SR) is defined as:

$$
S_{\mathrm{MIA}} = \frac{TP + TN}{TP + TN + FP + FN}, \tag{50}
$$

where  $TP$  and  $TN$  represent correctly identified members and nonmembers. The Attribute Inference Attack Success Rate (AIA SR) is given by:

$$
A_{\mathrm{AIA}} = \frac{N_{\mathrm{CI}}}{N_{\mathrm{TI}}}. \tag{51}
$$

where  $N_{\mathrm{CI}}$  and  $N_{\mathrm{TI}}$  denote the number of correct and total inferences, respectively.

5.4.4. Robustness Attack Robustness to adversarial modifications is evaluated using BA Accuracy Degradation (BA AD):

$$
D_{\mathrm{Byz}} = A_{\mathrm{clean}} - A_{\mathrm{Byzantine}}, \tag{52}
$$

where  $A_{\mathrm{clean}}$  denotes accuracy under benign conditions, and  $A_{\mathrm{Byzantine}}$  represents accuracy under Byzantine attacks.

5.4.5. Fairness Attack Fairness- targeted vulnerability is measured using the Data Poisoning Attack Equalized Odds Difference Deviation (DPA EODD), defined as the difference in Equalized Odds Difference (EOD) between the poisoned and clean datasets:

$$
\mathrm{DPA EODD} = \mathrm{EOD}_{\mathrm{poisoned}} - \mathrm{EOD}_{\mathrm{clean}}. \tag{53}
$$

EOD ensures that a classifier maintains equal true and false positive rates across all sensitive groups.

# 6. Experimental Results & Analyses

In this section, we present the experimental results evaluating our proposed RESFL method, focusing on the following key questions:

(1) Does our method achieve the optimal trade-off between utility, privacy, and fairness under standard conditions? 
(2) How robust is our method in autonomous vehicle (AV) scenarios under varying environmental conditions, particularly when uncertainty arises due to weather variations?

![](images/0c7491f7cd758c02754a2d001c39c7514f71177a41786729295461995ac26102.jpg)  
Figure 3: Comparison of accuracy (mAP) across Monk Skin Tones using FedAvg and RESFL on the FACET dataset: The results show that RESFL improves fairness by maintaining consistent accuracy across skin tones, reducing the performance disparity observed in FedAvg, which suffers a significant drop for darker skin tones.

# 6.1. Trade-off Analyses in Utility, Fairness, Privacy, and Robustness of RESFL on the FACET Dataset

In this experiment, we evaluate the performance of various FL algorithms on the FACET dataset, a benchmark for detection utility, fairness, privacy, and robustness. Our objective is to compare the overall trade- offs of each method in a controlled setting.

Table 1 summarizes the empirical results for FedAvg, two FedAvgDP variants  $\epsilon = 0.1$ $\epsilon = 0.5$  , FairFed, PrivFairFL- Pre, PrivFairFLPost, PUFFLE, PFU- FL, and our proposed RESFL. Figure 3 illustrates accuracy across skin tones, showing that RESFL achieves higher overall performance with a more balanced distribution. Unlike Fe $\mathrm{dAvg}$  which exhibits significant disparity, particularly for rare skin tones (e.g., 1, 9, 10), RESFL demonstrates improved fairness.

Utility. Our RESFL approach achieves an overall mAP of 0.6654, comparable to the best- performing methods (e.g., FairFed with mAP of 0.7013) and notably outperforming PUFFLE and PFU- FL. This high utility stems from dynamic client weighting based on uncertainty, where prioritizing updates from clients with lower epistemic uncertainty disparities enhances detection performance while mitigating the impact of noisy updates.

Fairness RESFL achieves the lowest fairness disparities, with  $|1 - \mathrm{DI}| = 0.2287$  and  $\Delta \mathrm{EOP} = 0.1959$  In contrast, while methods like FairFed achieve comparable utility, their fairness metrics are significantly higher. RESFL's fairness advantage stems from precise epistemic uncertainty estimation via an evidential neural network, enabling accurate computation of the Uncertainty Fairness Metric (UFM). By integrating UFM into the aggregation process, RESFL ensures consistent model performance across demographic groups.

Privacy RESFL's adversarial privacy disentanglement effectively reduces privacy risks, achieving a Membership Inference Attack (MIA) success rate of 0.2093 and an Attribute Inference Attack (AIA) success rate of 0.1832- both significantly lower than those of FedAvg and its DP variants. The gradient reversal mechanism minimizes mutual information between latent representations and sensitive attributes, enhancing privacy protection without compromising utility.

Robustness RESFL demonstrates the lowest degradation under adversarial conditions, with BA Accuracy Degradation of 0.1692 and DPA EODD of 0.0674. These low values indicate resilience against both Byzantine attacks and fairness- targeted data poisoning. The combination of uncertainty- based client reweighting and adversarial training enhances RESFL's stability and robustness, even under malicious perturbations.

Overall, RESFL achieves a superior balance across multiple performance dimensions under the FACET dataset. While some baselines excel in individual metrics, RESFL's integrated approach ensures consistent and robust performance across utility, fairness, privacy, and robustness. This comprehensive trade- off is crucial for real- world federated learning applications, where balancing these aspects is essential.

# 6.2. Resilience Analyses of RESFL Under Adverse Weather Conditions in CARLA

For the CARLA evaluation, we assess generalization under adverse weather conditions. Given autonomous vehicles' environmental challenges, we compare four methods: FedAvg- DP, FairFed, PUFFLE, and our proposed RESFL. FedAvg- DP is selected for its effectiveness in limiting MIA success rates, FairFed for its high accuracy in benign conditions, and PUFFLE for its robustness. These methods serve as strong baselines for evaluating performance under degraded visual conditions.

In CARLA, weather conditions were simulated in three categories: cloud, rain, and fog, each evaluated at intensities of  $0\%$ $25\%$ $50\%$ $75\%$  and  $100\%$  . Performance metrics were recorded for detection utility, fairness, privacy, and robustness.

RESFL's utility under adverse weather conditions Figure 4 shows that increasing weather intensity reduces mAP across all methods. However, RESFL exhibits a more gradual decrease. For example, under cloudy conditions, while FedAvg- DP's mAP drops sharply from 0.2741 to 0.1890, RESFL declines from 0.4621 to 0.3851, proving superior resilience to environmental noise.

Fog conditions, which severely degrade image quality, present the greatest challenge. Under extreme fog (100% intensity), baseline methods, particularly FedAvg, suffer catastrophic performance degradation. In contrast, RESFL maintains a measurable mAP and exhibits minimal increases in fairness and privacy disparities, as reflected in BA Accuracy Degradation and DPA EOD Difference. This highlights the effectiveness of RESFL's uncertainty- based dynamic weighting in filtering unreliable updates, preventing performance losses in low- visibility conditions.

RESFL's fairness and privacy preservation under adverse weather conditions Fairness metrics such as  $|1 - \mathrm{DI}|$  and  $\Delta \mathrm{EOP}$  are crucial under adverse conditions. As shown in Figure 4, under rain, FairFed maintains high mAP but suffers significant fairness degradation. In contrast, RESFL achieves consistently lower  $|1 - \mathrm{DI}|$  and  $\Delta \mathrm{EOP}$  across all rain intensities, demonstrating its ability to dynamically mitigate fairness disparities despite increased noise.

![](images/e3d358ccec97e9be0689085e3a11ac951a15eb71f4fce6bbb68c9456af5d3657.jpg)  
Figure 4: Performance comparison of four state-of-the-art FL methods and RESFL across three weather conditions (Cloud, Rain, Fog) at  $0\% -100\%$  intensity. Rows represent performance metrics (accuracy, fairness, resilience to privacy, and fairness attacks), and columns correspond to weather conditions.

RESFL's adversarial privacy module also ensures lower MIA and AIA success rates, even as sensor data degrades due to rain or fog.

CARLA experiments demonstrate that RESFL achieves a superior balance of utility, fairness, and privacy under adverse weather conditions. Its resilience to environmental perturbations, reflected in gradual mAP degradation and stable fairness and privacy metrics, highlights its potential for real- world autonomous vehicle deployment, where robustness under dynamic conditions is crucial.

Table 2: RESFL's performance with varying uncertainty-based fairness and adversarial privacy coefficients (i.e.,  $\lambda_{1}$  and  $\lambda_{2}$  -  

<table><tr><td colspan="2">Algorithm</td><td>Utility</td><td colspan="2">Fairness</td><td colspan="2">Privacy</td></tr><tr><td>λ1</td><td>λ2</td><td>mAP</td><td>|1 - DI|</td><td>ΔEOP</td><td>MIA SR</td><td>AIA SR</td></tr><tr><td>1</td><td>0</td><td>0.6278</td><td>0.2258</td><td>0.2062</td><td>0.3341</td><td>0.1431</td></tr><tr><td>0</td><td>1</td><td>0.5856</td><td>0.2571</td><td>0.2846</td><td>0.1025</td><td>0.1463</td></tr><tr><td>0.01</td><td>1</td><td>0.6056</td><td>0.2653</td><td>0.3459</td><td>0.1256</td><td>0.1668</td></tr><tr><td>0.1</td><td>1</td><td>0.6254</td><td>0.2538</td><td>0.2626</td><td>0.1477</td><td>0.1608</td></tr><tr><td>1</td><td>1</td><td>0.5953</td><td>0.2432</td><td>0.2513</td><td>0.2197</td><td>0.1782</td></tr><tr><td>0.1</td><td>0.01</td><td>0.6654</td><td>0.2287</td><td>0.1959</td><td>0.2093</td><td>0.1832</td></tr><tr><td>0.1</td><td>0.1</td><td>0.6430</td><td>0.2625</td><td>0.3143</td><td>0.1363</td><td>0.1474</td></tr><tr><td>0.1</td><td>1</td><td>0.5839</td><td>0.3862</td><td>0.4146</td><td>0.1176</td><td>0.1656</td></tr></table>

# 6.3.Ablation Study with RESFL

We conduct an ablation study to examine the impact of two key hyperparameters in RESFL: the uncertainty- based fairness coefficient  $(\lambda_{1})$  and the adversarial privacy coefficient  $(\lambda_{2})$  . These parameters regulate the trade- offs between detection performance, fairness, and privacy, while the task loss coefficient remains fixed at 1. Robustness and fairness attacks are excluded, as the goal is to determine the optimal balance between utility (mAP), fairness  $(|1 - \mathrm{DI}|,\Delta \mathrm{EOP})$  and privacy (MIA, AIA success rates). The results of the study are summarized in Table 2.

In the FACET experiment, we set  $\lambda_{2} = 0$  and vary  $\lambda_{1}$  to isolate the effect of uncertainty- based fairness. Results show that increasing  $\lambda_{1}$  reduces fairness disparities but lowers utility in the absence of adversarial privacy. In the second experiment, we fix  $\lambda_{1} = 0.1$  and vary  $\lambda_{2}$  to identify its optimal value. The best trade- off is achieved with  $\lambda_{1} = 0.1$ $\lambda_{2} = 0.01$  yielding an mAP of 0.6654,  $|1 - \mathrm{DI}| = 0.2287$  and  $\Delta \mathrm{EOP} = 0.1959$  , while maintaining favorable privacy metrics (MIA: 0.2093, AIA: 0.1832). Increasing  $\lambda_{2}$  beyond 0.01 degrades both detection accuracy and fairness, indicating that an overly strong adversarial privacy term disrupts the balance required for optimal performance.

We provide the table- format results corresponding to Figure 4 in Tables 3,4, and 5 in Appendix C for those seeking a more detailed analysis.

# 7.Discussions

The experimental results highlight the effectiveness of RESFL in balancing detection utility, fairness, privacy, and robustness within a federated learning framework. Combining adversarial privacy disentanglement with an uncertainty- driven fairness metric, RESFL ensures privacy- preserving and equitably weighted model updates.

This section analyzes their interplay and performance impact in the autonomous vehicle (AV) domain.

Interaction Between Fairness and Privacy RESFL integrates two synergistic strategies: (1) adversarial privacy disentanglement mitigates sensitive attribute leakage by penalizing demographic information in latent representations. (2) Uncertainty- guided fairness weighting prioritizes updates from clients with lower epistemic uncertainty variance across sensitive groups. Overemphasizing privacy (i.e., high adversarial loss) degrades detection accuracy and fairness, while excessive fairness constraints risk exposing sensitive data via model gradients. Ablation results show that carefully balancing these losses achieves the optimal trade- off, highlighting the inherent tension where neither fairness nor privacy can be maximized independently without compromise.

Importance of Uncertainty- Based Aggregation Our fairness strategy hinges on quantifying epistemic uncertainty at the client level. By prioritizing updates from clients with lower uncertainty disparities, we amplify trustworthy gradients in each federated round. This is crucial in scenarios with challenging data distributions, such as extreme weather in AVs or limited demographic diversity. Without this weighting, atypical conditions could disproportionately influence the global model, increasing bias or degrading performance. Empirical results on the CARLA dataset show that as weather severity intensifies, down- weighting high- uncertainty updates helps maintain robustness, which is essential for real- time object detection in diverse, harsh environments.

Adversarial Privacy Disentanglement in AVs AVs handle privacy- sensitive sensor data, often containing identifiable pedestrian and driver features. Our adversarial disentanglement module mitigates this risk by suppressing latent features predictive of sensitive attributes. A gradient reversal layer penalizes residual correlations, reducing membership and attribute inference attack success rates. Properly tuned, this privacy constraint minimally impacts detection performance. FACET and CARLA experiments confirm that adversarial privacy can coexist with high detection accuracy when integrated with uncertainty- driven fairness.

Robustness and Attack Resilience Defending against Byzantine attacks (e.g., malicious gradient manipulations) and fairnesstargeted data poisoning in federated learning is challenging. RESFL demonstrates lower BA Accuracy Degradation (BA AD) and DPA EOD Deviation (DPA EODD), indicating greater resilience. This stems from its dual approach: adversarial disentanglement and uncertainty weighting. Down- weighting anomalously high- variance gradients, whether due to adversarial tampering or challenging local conditions, RESFL filters out extreme updates. Additionally, privacy constraints limit adversaries' ability to exploit demographic correlations, mitigating bias- amplifying poisoning attempts.

Significance for Real- World AV Deployments RESFL offers a compelling solution for AVs by balancing high detection accuracy, fairness, privacy, and adversarial resilience. In real- world deployments, AVs encounter diverse environments with varying weather and demographic distributions. A federated approach that harmonizes these variations while preserving privacy and fairness is

essential for public trust and regulatory compliance. Our results suggest that integrating adversarial privacy with uncertainty- driven fairness enables federated models that are both ethically sound and technically robust.

# 8. Conclusions & Future Work

# 8.1. Summary of Key Contributions

In this work, we introduced RESFL, a novel federated learning framework that jointly optimizes detection utility, fairness, privacy, and robustness for autonomous vehicle (AV) perception. RESFL integrates adversarial privacy disentanglement with uncertainty- guided fairness- aware aggregation to mitigate sensitive attribute leakage while maintaining equitable model performance across diverse demographic groups. By leveraging an evidential neural network to quantify epistemic uncertainty and incorporating a custom Uncertainty Fairness Metric for client update weighting, our approach enhances privacy preservation and fairness without compromising detection accuracy.

# 8.2. Key Findings

Our extensive experiments on the FACET dataset demonstrate that RESFL achieves competitive mean Average Precision (mAP) while significantly reducing fairness disparities, as indicated by lower  $\left|1 - \mathrm{DI}\right|$  and  $\Delta \mathrm{EOP}$  values compared to baselines such as FedAvg, FedAvg- DP, FairFed, PrivFairFL, PUFFLE, and PFU- FL. Privacy resilience is further validated by lower success rates of Membership Inference Attacks (MIA) and Attribute Inference Attacks (AIA), highlighting its effectiveness in safeguarding sensitive information.

Furthermore, evaluations on the CARLA simulator under varying weather conditions (cloud, rain, and fog at intensities from  $0\%$  to  $100\%$  confirm RESFL's robustness. The model exhibits gradual performance degradation under severe weather while maintaining stable fairness and privacy metrics, demonstrating its adaptability to real- world AV environments. These findings underscore RESFL's suitability for safety- critical AV applications where fairness, privacy, and robustness are paramount.

# 8.3. Future Work Directions

Future research can explore adaptive mechanisms to dynamically balance fairness and privacy constraints in real- time AV operations. Extending RESFL to large- scale AV networks with diverse mobility patterns and communication constraints remains an open challenge. Further work is needed to enhance robustness against emerging adversarial and data poisoning attacks specifically targeting federated learning frameworks. Integrating multi- modal AV sensor data, such as LiDAR and radar, could improve detection performance in extreme conditions. Finally, real- world deployment and evaluation in AV testbeds will be essential to validate RESFL's practical effectiveness, regulatory compliance, and impact on public trust.

# References

[1] 2023. Toward the Tradeoffs between Privacy, Fairness and Utility in Federated Learning. In International Symposium on Emerging Information Security and Applications. Springer, 118- 132. [2] Moloud Abdar, Farhad Pourpanah, Sadiq Hussain, Dana Rezazadegan, Li Liu, Mohammad Ghavamzadeh, Paul Fieguth, Xiaochun Cao, Abbas Khosravi, U Rajendra

Acharya, et al. 2021. A review of uncertainty quantification in deep learning: techniques, applications and challenges. Information Fusion 76 (2021), 243- 297. [3] Alexander, Amini, Wilko Schwarting, Ava Soleimany, and Daniela Rus. 2020. Deep evidential regression. Advances in neural information processing systems 33 (2020), 14927- 14937. [4] Eugene Bagdasaryan, Omid Poursaeed, and Vitaly Shmatikov. 2019. Differential privacy has disparate impact on model accuracy. Advances in neural information processing systems 32 (2019). [5] Chunlu Chen, De Liu, Haowen Tan, Xingjian Li, Kamin- Kai Wang, Peng Li, Kouichi Sakurai, and Ji Jing Dou. 2025. Trustworthy federated learning: Privacy, security, and beyond. Knowledge and Information Systems 67, 3 (2025), 2321- 2356. [6] E Chen, Yang Cao, and Yifei Ge. 2024. A Generalized Shuffle Framework for Privacy Amplification: Strengthening Privacy Guarantees and Enhancing Utility. In Proceedings of the AAAI Conference on Artificial Intelligence, Vol. 38, 11267- 11275. [7] Huiqiang Chen, Tianqing Zhu, Tao Zhang, Wanlei Zhou, and Philip S Yu. 2023. Privacy and fairness in Federated learning: on the perspective of Tradeoff. Comput. Surveys 56, 2 (2023), 1- 37. [8] Luca Corbucci, Mikko A Heikkila, David Solans Noguero, Anna Monreale, and Nicolas Kourtellis. 2024. PUFPL: Balancing Privacy, Utility, and Fairness in Federated Learning. arXiv preprint arXiv:2407.15224 (2024). [9] Alexey Dosovitskiy, German Ros, Felipe Corlevilla, Antonio Lopez, and Vladlen Koltun. 2017. CARLA: An open urban driving simulator. In Conference on robot learning. PMLR, 1- 16. [10] Cynthia Dwork, 2006. Differential privacy. In International colloquium on automata, languages, and programming. Springer, 1- 12. [11] Ulfar Erlingsson, Vitaly Feldman, Ilya Mironov, Ananth Raghunathan, Kunal Talwar, and Abhradeep Thakurta. 2019. Amplification by shuffling: From local to central differential privacy via anonymity. In Proceedings of the Thirtieth Annual ACM- SIAM Symposium on Discrete Algorithms. SIAM, 2468- 2479. [12] Yahya H Ezzeldin, Shen Yan, Chaoyang He, Emilio Ferrara, and A Salman Avesimeh R 2023. Fairfed: Enabling group fairness in federated learning. In Proceedings of the AAAI conference on artificial intelligence. Vol. 37, 7494- 7502. [13] Laura Gustafson, Chloe Rolland, Nikhila Raviv, Quentin Duval, Aaron Adcock, Cheng- Yang Fu, Melissa Hall, and Candace Ross. 2023. Facet: Fairness in computer vision evaluation benchmark. In Proceedings of the IEEE/CVF International Conference on Computer Vision. 20370- 2038. [14] Moritz Hardt, Eric Price, and Nati Srebro. 2016. Equality of opportunity in paper- based learning: Advances in neural information processing systems. arXiv:1603.0107. [15] Ahmed Imteaj, Urrush Thakker, Shiqiang Wang, Jian Li, and M Hadi Amini. 2021. A survey on federated learning for resource- constrained IoT devices. IEEE Internet of Things Journal, 9, 1 (2021), 1- 24. [16] Vicente Julian. 2024. Intelligent Data Engineering and Automated Learning- IDEAL 2024: 25th International Conference, Valencia, Spain, November 20- 22, 2024, Proceedings, Part I. Vol. 15346. Springer Nature. [17] Ehsanul Kabir. 2025. Towards Secure Federated Learning: A Validation- Based Approach to Mitigating Poisoning Attacks. (2025). [18] Peter Kairouz, H Brendan McMahan, Brenda Avent, Aurelien Bellet, Mehdi Bennis, Arjun Nitin Bhagoji, Kallista Bonawitz, Zachary Charles, Graham Cormode, Rachel Cummings, et al. 2021. Advances and open problems in federated learning. Foundations and trends in machine learning 14, 1- 2 (2021), 1- 210. [19] Caelin Kaplan. 2024. Inherent trade- offs in privacy- preserving machine learning. Ph. D. Dissertations. University Côte d'Azur. [20] Sai Praneeth Karimireddy, Satyen Kale, Meharyar Mohri, Sashank Reddi, Sebastian Stich, and Ananda Theertha Suresh. 2020. Scaffold: Stochastic controlled averaging for federated learning. In International conference on machine learning. PMLR, 5132- 5143. [21] Dohyoung Kim, Hyekyung Woo, and Youngho Lee. 2024. Addressing Bias and Fairness Using Fair Federated Learning: A Synthetic Review. Electronics 13, 23 (2024), 4664. [22] Kibaek Kim, Krishnan Raghavan, Olivera Kotevska, Matthieu Dorier, Ravi Madduri, Minseok Ryu, Todd Munson, Rob Ross, Thomas Flynn, Ai Kagawa, et al. 2024. Privacy- Preserving Federated Learning for Science: Challenges and Research Directions. In 2024 IEEE International Conference on Big Data (BigData). IEEE, 7849- 7853. [23] Tian Li, Maziar Sanjabi, Ahmad Beirami, and Virginia Smith. 2019. Fair resource allocation in federated learning. arXiv preprint arXiv:1905.10497 (2019). [24] Natalia Martinez, Martin Bertran, and Guillermo Sapiro. 2020. Minimax pareto fairness: A multi objective perspective. In International conference on machine learning. PMLR, 6755- 6764. [25] Brendan McMahan, Eider Moore, Daniel Ramage, Seth Hampson, and Blaise Aguera y Arcas. 2017. Communication- efficient learning of deep networks from decentralized data. In Artificial intelligence and statistics. PMLR, 1273- 1282. [26] Ninareh Mehabi, Fred Morstatter, Nripsuta Saxena, Kristina Lerman, and Aram Galstyan. 2021. A survey on bias and fairness in machine learning. ACM computing surveys (CSUR) 54, 6 (2021), 1- 35.

[27] Bimsara Pathiraja, Caleb Liu, and Ransalu Senanayake. 2024. Fairness in Autonomous Driving: Towards Understanding Confounding Factors in Object Detection under challenging Weather. arXiv preprint arXiv:2406.00219 (2024). [28] Sikh Pentyala, Nicola Neophytou, Anderson Nascimento, Martin De Cock, and Golnoash Farnadi. 2022. Priviairfl: Privacy- preserving group fairness in federated learning. arXiv preprint arXiv:2205.11584 (2022). [29] David Pujol, Ryan McKenna, Satya Kuppam, Michael Hay, Ashwin Machanava- jhala, and Gerome Miklau. 2020. Fair decision making using privacy- protected data. In Proceedings of the 2020 Conference on Fairness, Accountability, and Transparency. 189- 199. [30] Pian Qi, Diletta Chiaro, Antonella Guzzo, Michele Ianni, Giancarlo Fortino, and Francesco Piccialli. 2024. Model aggregation techniques in federated learning: A comprehensive survey. Future Generation Computer Systems 150 (2024), 272- 293. [31] Nitin Pena, Mallikuriya Paranmose, and Jovesh Pena. 2024. Trustworthy artificial intelligence: Enhancing trustworthiness through explainable AI (XAI). Available at SSRN 4873183 (2024). [32] Borja Rodriguez- Galvez, Filip Granqvist, Rogier van Dalen, and Matt Seigel. 2021. Forcing fairness in private federated learning via the modified method of differential multipliers. arXiv preprint arXiv:2109.08604 (2021). [33] Peng Sun, Haoxuan Che, Zhabo Wang, Yuwei Wang, Tao Wang, Liantao Wu, and Huajie Shao. 2021. Pain- AI: Personalized privacy- preserving incentive for federated learning. IEEE Journal on Selected Areas in Communications 39, 12 (2021), 3805- 3820. [34] Aliya Tabassum, Aiman Erhad, Wadha Lebda, Amr Mohamed, and Mohsen Guizani. 2022. Fedgan- ids: Privacy- preserving ids using gan and federated learning. Computer Communications 192 (2022), 299- 310. [35] Anh Tu Tran, The Dung Luong, and Xuan Sang Pham. 2023. A novel privacy- preserving federated learning model based on secure multi- party computation. In International Symposium on Integrated Uncertainty in Knowledge Modelling and Decision Making. Springer, 321- 333. [36] Joost Verbraeken, Matthijs Wolting. Jonathan Katzy, Jeroen Kloppenburg, Tim Verbelen, and Jan S Pellemeyer. 2020. A survey on distributed machine learning. Acta computer surveys (csur) 53, 1 (2020), 1- 33. [37] Yongkai Wu, Lu Zhang, and Xiefeo Wu. 2018. Fairness- aware classification: Criterion, convexity, and bounds. arXiv preprint arXiv:1809.04737 (2018). [38] Bangzhou Xin, Wei Yang, Youngyang Geng, Sheng Chen, Shaowei Wang, and Liusheng Huang. 2020. Private fl- gan: Differential privacy synthetic data generation based on federated learning. In Icassp 2020- 2020 IEEE international conference on advanced privacy algorithms. arXiv preprint arXiv:2020.0223 (2020). [39] Runhue Xu, Nathalie Baracaldo, and James Joshi. 2021. Privacy- preserving machine learning: Methods, challenges and directions. arXiv preprint arXiv:2108.04417 (2021). [40] Mohammad Yaghini, Patty Liu, Franziska Boenisch, and Nicolas Papernot. 2023. Learning with impartiality to walk on the pareto frontier of fairness, privacy, and utility. arXiv preprint arXiv:2302.09183 (2023). [41] Lei Yang, Jiaming Huang, Wanxi y Lin, and Jianmong Cao. 2023. Personalized federated learning on non- IID data via group- based meta- learning. ACM Transactions on Knowledge Discovery from Data 17, 4 (2023), 1- 20. [42] Xun Yi, Russell Paulet, Elisa Bertino, Xun Yi, Russell Paulet, and Elisa Bertino. 2014. Homomorphic encryption. Springer. [43] Han Yu, Zelei Liu, Yang Liu, Tianjian Chen, Mingshu Cong, Xi Weng, Dusit Niyato, and Qiang Yang. 2020. A fairness- aware incentive scheme for federated learning. In Proceedings of the AAAI/ACM Conference on AI, Ethics, and Society. 393- 399. [44] Tao Zhang, Tianqing Zhu, Kun Gao, Wanlei Zhou, and S Yu Philip. 2021. Balancing learning model privacy, fairness, and accuracy with early stopping criteria. IEEE Transactions on Neural Networks and Learning Systems 34, 9 (2021), 5557- 5569. [45] Yifei Zhang, Dun Zeng, Jinglong Luo, Xinyu Fu, Guangzhong Chen, Zenglin Xu, and Irwin King. 2024. A survey of trustworthy federated learning: Issues, solutions, and challenges. ACM Transactions on Intelligent Systems and Technology 15, 6 (2024), 1- 47. [46] Hangyu Zhu, Liyuan Huang, and Zhenping Xie. 2024. GGI: Generative Gradient Inversion Attack in Federated Learning. In 2024 6th International Conference on Data- driven Optimization of Complex Systems (DOC3). IEEE, 379- 384.

# A. Theoretical Analysis of the Uncertainty Fairness Metric

# A.1. Theoretical Foundations of the UFM

This appendix analyzes the Uncertainty Fairness Metric (UFM) introduced in Section 4.1, demonstrating how epistemic uncertainty is a reliable proxy for fairness assessment in federated learning. Focusing on autonomous vehicle perception models, we validate

UFM through statistical learning theory and information- theoretic perspectives, establishing its effectiveness as a fairness indicator.

Preliminaries and Notations Let  $\mathcal{D}$  denote the joint distribution over the input space  $\mathcal{X}$  and output space  $\mathcal{Y}$ . In the context of autonomous vehicle detection,  $\mathcal{X}$  represents sensor inputs (e.g., images), and  $\mathcal{Y}$  represents detection labels. The population is partitioned into  $G$  sensitive groups based on protected attributes (e.g., pedestrian demographics), with each group  $g \in \{1, 2, \ldots , G\}$  following its own conditional distribution  $\mathcal{D}_g$ .

For a model  $f_\theta : \mathcal{X} \rightarrow \mathcal{S}^G$  with parameters  $\theta$ , we denote the evidential parameters learned for group  $g$  as  $\alpha^{(g)}$ , with total evidence  $\alpha_{0}^{(g)} = \sum_{c = 1}^{C} \alpha_{c}^{(g)}$ . The epistemic uncertainty for group  $g$  is defined as:

$$
\left(\sigma_{\mathrm{epistemic}}^{2}\right)^{(g)} = \frac{1}{\alpha_{0}^{(g)}} \tag{54}
$$

The Uncertainty Fairness Metric (UFM) quantifies disparities in epistemic uncertainty across groups:

$$
\mathrm{UFM} = \frac{\max_g\left(\frac{1}{\alpha_{(g)}^{(g)}}\right) - \min_g\left(\frac{1}{\alpha_{(g)}^{(g)}}\right)}{\frac{1}{G}\sum_{g = 1}^{G}\frac{1}{\alpha_{0}^{(g)}}\sum_{g = 1}^{G}\epsilon} \tag{55}
$$

where  $\epsilon > 0$  ensures numerical stability.

# A.2. Relationship Between Epistemic Uncertainty and Group Representation

We now establish the relationship between epistemic uncertainty and the representation quality of each sensitive group in the training data.

# A.2.1. Theorem: Uncertainty-Representation Relationship

THEOREM A.1. For a model trained on data from  $G$  sensitive groups, the epistemic uncertainty  $\left(\sigma_{\mathrm{epistemic}}^{2}\right)^{(g)}$  for group  $g$  is inversely proportional to the effective sample size  $n_{g}^{(g)}$  and the signal- to- noise ratio (SNR) of the corresponding data, under mild regularity conditions.

PROOF. Under a Bayesian framework, the posterior distribution over model parameters  $\theta$  given group  $g$ 's data  $D^{(g)}$  follows:

$$
p(\theta |D^{(g)})\propto p(D^{(g)}|\theta)p(\theta). \tag{56}
$$

Assuming a Gaussian likelihood model with mean  $\mu_g(\theta)$  and variance  $\sigma_g^2$ :

$$
p(D^{(g)}|\theta) = \prod_{i = 1}^{n_g}\mathcal{N}(y_i^{(g)}|\mu_g(\theta),\sigma_g^2), \tag{57}
$$

where  $n_g$  is the number of samples from group  $g$ .

For a conjugate prior, the posterior variance of  $\mu_g(\theta)$  is:

$$
\mathrm{Var}[\mu_g(\theta)|D^{(g)}] = \frac{\sigma_g^2}{n_g} +O\left(\frac{1}{n_g^2}\right). \tag{58}
$$

This variance corresponds to epistemic uncertainty. Given the learning task complexity, we approximate:

$$
\left(\sigma_{\mathrm{epistemic}}^{2}\right)^{(g)}\approx \frac{c_{1}}{n_{\mathrm{eff}}^{(g)}} +\frac{c_{2}\sigma_{g}^{2}}{\mathrm{SNR}_{g}}, \tag{59}
$$

where  $c_{1},c_{2}$  are constants,  $n_{\mathrm{eff}}^{(g)}$  is the effective sample size (accounting for data quality and representativeness), and  $\mathrm{SNR}_g$  is the signal- to- noise ratio for group  $g$

In our evidential framework, the total evidence  $\alpha_0^{(g)}$  accumulates with more high- quality samples from group  $g$  following:

$$
\alpha_0^{(g)}\propto n_{\mathrm{eff}}^{(g)}\cdot \mathrm{SNR}_g. \tag{60}
$$

Thus, epistemic uncertainty satisfies:

$$
\left(\sigma_{\mathrm{epistemic}}^{2}\right)^{(g)} = \frac{1}{\alpha_{0}^{(g)}}\propto \frac{1}{n_{\mathrm{eff}}^{(g)}\cdot\mathrm{SNR}_{g}}, \tag{61}
$$

confirming its inverse relationship with effective sample size and data quality for each group.

# A.2.2. Corollary: Uncertainty Disparity Implies Representation Disparity

COROLLARY A.2. Disparities in epistemic uncertainty across groups indicate differences in effective representation and data quality.

PROOF. From Theorem 1, for any two groups  $g_{1}$  and  $g_{2}$

$$
\begin{array}{r}\left(\sigma_{\mathrm{epistemic}}^{2}\right)^{(g_{1})}\approx \frac{n_{g_{2}}^{(g_{2})}}{n_{\mathrm{eff}}^{(g_{2})}}\cdot \mathrm{SNR}_{g_{2}}.\\ \left(\sigma_{\mathrm{epistemic}}^{2}\right)^{(g_{2})}\approx \frac{n_{\mathrm{eff}}^{(g_{2})}}{n_{\mathrm{eff}}^{(g_{2})}}\cdot \mathrm{SNR}_{g_{1}}. \end{array} \tag{62}
$$

If  $\left(\sigma_{\mathrm{epistemic}}^{2}\right)^{(g_{1})} > \left(\sigma_{\mathrm{epistemic}}^{2}\right)^{(g_{2})}$  then group  $g_{1}$  has a lower effective sample size, a lower signal- to- noise ratio, or both, compared to group  $g_{2}$ . This suggests weaker learned representations for  $g_{1}$  potentially leading to algorithmic bias.

# A.3. Information-Theoretic Analysis of the UFM

We analyze the Uncertainty Fairness Metric (UFM) from an information- theoretic perspective to establish its validity as a fairness measure.

# A.3.1. Theorem: UFM as a Bounded Measure of Representation Disparity

THEOREM A.3. The UFM provides a normalized, bounded measure of representation disparity across  $G$  groups, with values in  $[0,G - 1]$

PROOF. Let  $\bar{u}^{(g)} = \left(\sigma_{\mathrm{epistemic}}^{2}\right)^{(g)} = \frac{1}{\alpha_{0}^{(g)}}$  denote the epistemic uncertainty for group  $g$ , and define the mean uncertainty as:

$$
\bar{u} = \frac{1}{G}\sum_{g = 1}^{G}u^{(g)}. \tag{63}
$$

The UFM is given by:

$$
\mathrm{UFM} = \frac{\max_gu^{(g)} - \min_gu^{(g)}}{\bar{u} + \epsilon}. \tag{64}
$$

For  $\epsilon \to 0$  we establish the bounds:

Lower Bound: If uncertainties are equal across all groups, i.e.,  $u^{(1)} = u^{(2)} = \dots = u^{(G)}$ , then  $\max_g u^{(g)} - \min_g u^{(g)} = 0$ , yielding  $\mathrm{UFM} = 0$

Upper Bound: Let  $u^{(i)} = \max_g u^{(g)}$  and  $u^{(j)} = \min_g u^{(g)}$ . If one group dominates uncertainty while others approach zero, i.e.,  $u^{(j)}\approx 0$  for all  $j\neq i$ , then:

$$
\bar{u}\approx \frac{u^{(i)}}{G}. \tag{65}
$$

Substituting this into the UFM definition:

$$
\mathrm{UFM}\approx \frac{u^{(i)}}{u^{(i)} / G} = G. \tag{66}
$$

Since epistemic uncertainty is always positive, the true upper bound is slightly lower:

$$
\mathrm{UFM}< G. \tag{67}
$$

More precisely, the upper bound is  $G - 1$ , attained when one group exhibits significantly higher uncertainty than all others.

Thus, UFM is bounded within  $[0,G - 1]$ , providing a normalized measure of representation disparity.

# A.3.2. Theorem: UFM and Information Gain

THEOREM A.4. The UFM quantifies the disparity in expected information gain across sensitive groups during model training.

PROOF. In Bayesian learning, the information gain from observing a data point  $x$  with respect to model parameters  $\theta$  is:

$$
\mathrm{IG}(x) = \mathbb{E}_{y\sim p(y|x)}[D_{\mathrm{KL}}(p(\theta |x,y)\| p(\theta))], \tag{68}
$$

where  $D_{\mathrm{KL}}$  denotes the Kullback- Leibler divergence.

For a sensitive group  $g$ , the average information gain over its data distribution  $\mathcal{D}_g$  is:

$$
\mathrm{IG}^{(g)} = \mathbb{E}_{x\sim \mathcal{D}_g}[\mathrm{IG}(x)]. \tag{69}
$$

Under standard Bayesian assumptions, information gain is inversely proportional to residual epistemic uncertainty:

$$
\mathrm{IG}^{(g)}\propto \frac{1}{\left(\sigma_{\mathrm{epistemic}}^{2}\right)^{(g)}} = \alpha_{0}^{(g)}. \tag{70}
$$

Rewriting the UFM in terms of information gain:

$$
\mathrm{UFM}\approx \frac{\frac{1}{\max_g\mathrm{IG}^{(g)}} - \frac{1}{\max_g\mathrm{IG}^{(g)}}}{\frac{1}{G}\sum_{g = 1}^{G}\frac{1}{\mathrm{IG}^{(g)}} + \epsilon}. \tag{71}
$$

Thus, the UFM provides a normalized measure of disparity in information gain across groups, directly linking it to fairness in representation learning.

# A.4. Generalization Bounds and Fairness Guarantees

We establish theoretical guarantees on the relationship between the UFM and group- specific generalization performance.

# A.4.1. Theorem: UFM and Group-Specific Generalization Error

THEOREM A.5. For a model trained with evidential deep learning, a lower UFM implies more uniform generalization error bounds across sensitive groups, promoting fairer performance.

PROOF. Let  $\mathcal{L}^{(g)}$  denote the expected loss for group  $g$

$$
\mathcal{L}^{(g)} = \mathbb{E}_{(x,y)\sim \mathcal{D}_g}[\ell (f_\theta (x),y)], \tag{72}
$$

and let  $\hat{\mathcal{L}}^{(g)}$  be the empirical loss on the training data for group  $g$

By statistical learning theory, with probability at least  $1 - \delta$

$$
\mathcal{L}^{(g)}\leq \hat{\mathcal{L}}^{(g)} + O\left(\sqrt{\frac{\log(1 / \delta)}{n_g}}\right) + O\left(\sqrt{\left(\sigma_{\mathrm{epistemic}}^2\right)^{(g)}}\right). \tag{73}
$$

The third term arises because higher epistemic uncertainty increases generalization error.

For groups  $g_{1}$  and  $g_{2}$  , the generalization error difference satisfies:

$$
\begin{array}{rl} & {|\mathcal{L}^{(g_1)} - \mathcal{L}^{(g_2)}|\leq |\hat{\mathcal{L}}^{(g_1)} - \hat{\mathcal{L}}^{(g_2)}| + }\\ & {\quad O\left(\left|\sqrt{\frac{\log(1 / \delta)}{n_{g_1}}} -\sqrt{\frac{\log(1 / \delta)}{n_{g_2}}}\right|\right) + }\\ & {\quad O\left(\left|\sqrt{\sigma_{\mathrm{epistemic}}^{2}\left({g_1}\right)} -\sqrt{\left(\sigma_{\mathrm{epistemic}}^{2}\right)^{(g_2)}}\right|\right).} \end{array} \tag{74}
$$

With sufficient training,  $|\mathcal{L}^{(g_1)} - \mathcal{L}^{(g_2)}|$  becomes small, and the second term relates to sample size disparities. The third term, which directly depends on the UFM, dominates the bound: a lower UFM reduces differences in epistemic uncertainty across groups, tightening the generalization gap.

Thus, the maximum generalization error disparity across groups is bounded by:

$$
\max_{g_1,g_2}|\mathcal{L}^{(g_1)} - \mathcal{L}^{(g_2)}|\leq C\cdot \sqrt{\mathrm{UFM}\cdot\overline{u}} \tag{75}
$$

for some constant  $C > 0$  . Therefore, minimizing the UFM improves fairness by ensuring more consistent generalization across groups.

# A.5. Justification of UFM in Federated Learning

# A.5.1. Theorem: UFM for Client-Specific Fairness Evaluation

THEOREM A.6. In a federated learning system with  $K$  clients, weighting model updates using client- specific UFM values promotes convergence toward a fairer global model under mild conditions.

PROOF. Consider a federated learning system where each client  $k$  has access to data from a subset of sensitive groups  $\mathcal{G}_k\subseteq \{1,2,\ldots ,G\}$  Let  $\mathrm{UFM}_k$  denote the UFM computed on client  $k$  's local data.

In standard federated averaging (FedAvg), the global model update is:

$$
\theta_{t + 1} = \theta_t + \eta \sum_{k = 1}^K\frac{n_k}{n}\Delta \theta_k, \tag{76}
$$

where  $\eta$  is the learning rate,  $n_k$  is the number of samples at client  $k$ $\begin{array}{r}n = \sum_{k = 1}^{K}n_{k}, \end{array}$  and  $\Delta \theta_{k}$  is the parameter update from client  $k$

In the UFM- weighted approach, client updates are weighted as:

$$
\theta_{t + 1} = \theta_t + \eta \sum_{k = 1}^{K}\omega_k\Delta \theta_k, \tag{77}
$$

where  $\begin{array}{r}\omega_{k} = \frac{1}{1 + \mathrm{UFM}_{k}} \end{array}$  1+UFM, adjusts client influence based on fairness. Clients with higher  $\mathrm{UFM}_k$  (indicating greater fairness disparities) receive lower weights, while fairer clients (lower  $\mathrm{UFM}_k$  ) have greater influence.

The standard FedAvg gradient is:

$$
\nabla \mathcal{L}_{\mathrm{FedAvg}}(\theta) = \sum_{g = 1}^{G}\frac{n_g}{n} \nabla \mathcal{L}^{(g)}(\theta), \tag{78}
$$

where  $n_g$  is the number of samples from group  $g$  In contrast, the UFM- weighted gradient is:

$$
\nabla \mathcal{L}_{\mathrm{UFM}}(\theta) = \sum_{g = 1}^{G}\left(\sum_{k:g\in \mathcal{G}_k}\omega_k\frac{n_{k,g}}{n_k}\right)\nabla \mathcal{L}^{(g)}(\theta), \tag{79}
$$

where  $n_{k,g}$  is the number of samples from group  $g$  at client  $k$

This reweighting adjusts each group's contribution based on the fairness performance of clients that represent it. Groups with consistently high epistemic uncertainty (indicating unfair treatment) are initially down- weighted, but as fairness improves (lower  $\mathrm{UFM}_k$  ), their contributions increase, leading to a more balanced representation.

Under mild regularity conditions, this optimization minimizes a fairness- regularized objective:

$$
\min_{\theta}\mathcal{L}(\theta) + \lambda \cdot \Phi (\theta), \tag{80}
$$

where  $\Phi (\theta)$  is a fairness regularizer penalizing group- wise generalization disparities. Thus, UFM- weighted FL promotes convergence to a fairer global model than standard FedAvg.

# A.6. Extension to Object Detection in

# Autonomous Vehicles

We extend our theoretical analysis to object detection in autonomous vehicles (AVs), where fairness across environmental conditions and pedestrian demographics is critical.

# A.6.1. Theorem: UFM for Detection Fairness in Diverse Environments

THEOREM A.7. Minimizing the UFM in autonomous vehicle perception systems leads to more consistent detection performance across diverse environmental conditions and pedestrian demographics.

PROOF. Let  $\mathcal{D}_{e,d}$  denote the joint distribution of inputs and outputs under environmental condition  $e\in \mathcal{E}$  and demographic group  $d\in \mathcal{D}$  , where each  $(e,d)$  pair defines a sensitive group  $g$

The detection performance for group  $g = (e,d)$  is measured by the Average Precision (AP):

$$
\mathrm{AP}^{(g)} = \int_{0}^{1}\mathrm{Precision}^{(g)}(r)dr, \tag{81}
$$

where Precision  $(g)$ $(r)$  is the precision at recall level  $r$  for group  $g$ .

From Theorem 4, the disparity in detection performance between groups  $g_{1}$  and  $g_{2}$  is bounded by:

$$
\left|\mathrm{AP}^{(g_1)} - \mathrm{AP}^{(g_2)}\right|\leq C'\cdot \sqrt{\left|\left(e_{\mathrm{epistemic}}^{(g_1)}\right)^{(g_1)} - \left(\sigma_{\mathrm{epistemic}}^2\right)^{(g_2)}\right|} \tag{82}
$$

for some constant  $C^\prime >0$

Thus, minimizing the UFM reduces the maximum detection disparity across environmental and demographic groups:

$$
\max_{e_1,e_2\in \mathcal{E},d_1,d_2\in \mathcal{D}}|\mathrm{AP}^{(e_1,d_1)} - \mathrm{AP}^{(e_2,d_2)}|\leq C^{\prime \prime}\cdot \sqrt{\mathrm{UFM}}, \tag{83}
$$

for some constant  $C^{\prime \prime} > 0$

This result is crucial for AVs, which must ensure reliable and equitable performance across varying conditions (e.g., day/night, clear/rain, urban/rural) and all pedestrian demographics. The UFM provides a principled metric to assess and mitigate fairness disparities in this safety- critical application.

# A.7. Practical Implications for RESFL

The theoretical analysis above has several key implications for the RESFL framework:

(1) The UFM serves as a principled fairness metric, inversely related to the quality of representation across sensitive groups. 
(2) Weighting client updates by  $\begin{array}{r}\omega_{k} = \frac{1}{1 + \mathrm{UFM}_{k}} \end{array}$  prioritizes clients with more uniform epistemic uncertainty, leading to fairer global models. 
(3) The bounded range of the UFM  $([0,G - 1])$  ensures stable weighting even in highly heterogeneous settings. 
(4) The link between the UFM and generalization gap provides theoretical guarantees that minimizing UFM improves equity across groups. 
(5) In AV applications, this approach enhances safety by ensuring consistent detection performance across varying conditions and demographics.

# B. Implementation of Adversarial Privacy Disentanglement

To prevent sensitive information leakage in Federated Learning (FL) for object detection, we introduce an Adversarial Privacy Disentanglement (APD) module. The APD employs an adversarial network  $A(\cdot ;\phi)$  to predict the sensitive attribute  $s$  from the latent representation  $\mathbf{h}\in \mathbb{R}^d$  extracted from an intermediate layer of the detection model. The key objective of APD is to enforce privacy by learning representations that are invariant to sensitive attributes.

# B.1. Mathematical Formulation of Adversarial Privacy

The adversarial network is defined as:

$$
A:\mathbb{R}^d\rightarrow [0,1]^K,\quad \mathbf{h}\mapsto A(\mathbf{h};\phi), \tag{84}
$$

where  $K$  is the number of sensitive attribute classes. The adversarial loss is given by:

$$
\mathcal{L}_A = -\mathbb{E}_{(\mathbf{h},s)}\sim \mathcal{D}\left[\log A(s\mid \mathbf{h})\right]. \tag{85}
$$

To obscure sensitive attributes, we employ a Gradient Reversal Layer (GRL), which inverts the gradient during backpropagation. The GRL operation is defined as:

$$
\mathrm{GRL}(\mathbf{x}) = \mathbf{x},\quad \frac{\partial\mathrm{GRL}(\mathbf{x})}{\partial\mathbf{x}} = -\lambda_{\mathrm{adv}}I, \tag{86}
$$

where  $\lambda_{\mathrm{adv}} > 0$  controls the strength of the adversarial signal and  $\mathrm{I}$  is the identity matrix.

Without GRL, the standard gradient update follows:

$$
\theta \leftarrow \theta -\eta \nabla_{\theta}\mathcal{L}_{A}. \tag{87}
$$

With GRL, the feature extractor update is:

$$
\theta \leftarrow \theta +\eta \lambda_{\mathrm{adv}}\nabla_{\theta}\mathcal{L}_{A}. \tag{88}
$$

This reversal forces the feature extractor to maximize  $\mathcal{L}_A$  discouraging the encoding of sensitive attributes in  $\mathbf{h}$ . The adversarial privacy loss is then formulated as:

$$
\mathcal{L}_{\mathrm{priv}} = \lambda_{\mathrm{adv}}\mathcal{L}_{A}. \tag{89}
$$

By optimizing  $\mathcal{L}_{\mathrm{priv}}$ , the model learns representations that obfuscate sensitive information, enhancing privacy in federated object detection.

# B.2. Integration into Gradient Descent Optimization

To incorporate APD into training, we modify the standard stochastic gradient descent (SGD) updates. Given a primary model parameterized by  $\theta$  and an adversarial network parameterized by  $\phi$ , the optimization follows these steps:

(1) Compute feature representations:

$$
\mathbf{h} = f_{\theta}(\mathbf{x}), \tag{90}
$$

where  $f_{\theta}(\cdot)$  is the feature extractor.

(2) Compute object detection loss:

$$
\mathcal{L}_{\mathrm{det}} = \mathbb{E}_{(\mathbf{x},y)\sim \mathcal{D}}\left[\ell_{\mathrm{det}}(f_{\theta}(\mathbf{x}),y)\right], \tag{91}
$$

where  $\ell_{\mathrm{det}}$  is the detection loss (e.g., focal loss for classification, smooth L1 loss for bounding box regression).

(3) Compute adversarial loss using the GRL:

$$
\mathcal{L}_{\mathrm{priv}} = \lambda_{\mathrm{adv}}\mathcal{L}_{A} = -\lambda_{\mathrm{adv}}\mathbb{E}_{(\mathbf{h},s)\sim \mathcal{D}}\left[\log A(s\mid \mathbf{h})\right]. \tag{92}
$$

(4) Compute gradients:

$$
\nabla_{\theta}\mathcal{L} = \nabla_{\theta}\mathcal{L}_{\mathrm{det}} - \lambda_{\mathrm{adv}}\nabla_{\theta}\mathcal{L}_{A}, \tag{93}
$$

$$
\nabla_{\phi}\mathcal{L} = \nabla_{\phi}\mathcal{L}_{A}. \tag{94}
$$

(5) Update model parameters:

$$
\begin{array}{r}\theta \leftarrow \theta -\eta \nabla_{\theta}\mathcal{L},\\ \phi \leftarrow \phi -\eta \nabla_{\phi}\mathcal{L}. \end{array} \tag{96}
$$

where  $\eta$  is the learning rate.

# B.3. Impact on Fairness and Privacy

By discouraging the encoding of sensitive attributes, APD enhances privacy and may also improve fairness. If certain attributes contribute to biased decision- making, removing them from feature representations mitigates disparate impact, leading to more equitable performance across groups.

Fairness improvement is quantified by comparing model predictions before and after APD integration using metrics such as:

$$
\Delta_{\mathrm{bias}} = \left|\mathbb{E}[\hat{y} |g_1] - \mathbb{E}[\hat{y} |g_2]\right|, \tag{97}
$$

where  $g_{1},g_{2}$  represent different demographic groups. A reduction in  $\Delta_{\mathrm{bias}}$  post- training indicates improved fairness.

B.3.1. Implementation in YOLOv8-Based Detection In our federated object detection framework, the APD module is integrated into YOLOv8's feature extraction backbone. The GRL is applied at an intermediate layer before passing representations to the detection head, allowing adversarial training to dynamically enforce privacy constraints.

Let hyolo denote the feature representation from YOLOv8's backbone. The APD network operates as:

$$
A(\mathbf{h}_{\mathrm{YOLO}},\phi)\rightarrow \mathrm{GRL}\rightarrow \mathcal{L}_{\mathrm{priv}}, \tag{98}
$$

where the adversarial loss is backpropagated via GRL to induce privacy- preserving feature learning.

This integration preserves high object detection performance while disentangling privacy- sensitive attributes from learned representations, ensuring both privacy protection and potential fairness improvements.

# C. CARLA Evaluation Results

Table 3 presents a comparative evaluation of federated learning algorithms under varying levels of Cloud in the CARLA simulation, assessing their performance in terms of accuracy (mAP), fairness  $(|1 - \mathrm{DI}|,\Delta \mathrm{EOP})$  privacy risks (MIA, AIA), robustness (BA AD), and resilience to fairness- targeted attacks (DPA EODD).

Table 4 presents a comparative evaluation of federated learning algorithms under varying levels of Rain in the CARLA simulation, assessing their performance in terms of accuracy (mAP), fairness  $(|1 - \mathrm{DI}|,\Delta \mathrm{EOP})$  privacy risks (MIA, AIA), robustness (BA AD), and resilience to fairness- targeted attacks (DPA EODD).

Table 5 presents a comparative evaluation of federated learning algorithms under varying levels of Fog in the CARLA simulation, assessing their performance in terms of accuracy (mAP), fairness  $(|1 - \mathrm{DI}|,\Delta \mathrm{EOP})$  privacy risks (MIA, AIA), robustness (BA AD), and resilience to fairness- targeted attacks (DPA EODD).

Table 3: Performance comparison of federated learning algorithms under Fog in CARLA simulation. The table reports accuracy (mAP), fairness (1 - DI, EOP), privacy risks (MIA, AIA), robustness (BA AD), and fairness attack resilience (DPA EODD) across cloud intensity levels.  

<table><tr><td rowspan="2">Algorithm</td><td rowspan="2">Cloud Intensity (%)</td><td colspan="2">Utility</td><td colspan="2">Fairness</td><td colspan="2">Privacy Attacks</td><td>Robustness Attack</td><td>Fairness Attack</td></tr><tr><td colspan="2">Overall mAP</td><td>|1 - DI|</td><td>ΔEOP</td><td>MIA SR</td><td>AIA SR</td><td>BA AD</td><td>DPA EODD</td></tr><tr><td></td><td>0</td><td>0.3952</td><td>0.2356</td><td>0.2446</td><td>0.3915</td><td>0.4235</td><td>0.1531</td><td>0.0738</td><td></td></tr><tr><td></td><td>25</td><td>0.4005</td><td>0.2462</td><td>0.2460</td><td>0.3980</td><td>0.4085</td><td>0.1053</td><td>0.0821</td><td></td></tr><tr><td>FedAvg</td><td>50</td><td>0.3850</td><td>0.2394</td><td>0.2501</td><td>0.4052</td><td>0.3520</td><td>0.0975</td><td>0.0769</td><td></td></tr><tr><td></td><td>75</td><td>0.3662</td><td>0.2535</td><td>0.2552</td><td>0.4105</td><td>0.3401</td><td>0.3408</td><td>0.0952</td><td></td></tr><tr><td></td><td>100</td><td>0.3387</td><td>0.2587</td><td>0.2604</td><td>0.4203</td><td>0.3328</td><td>0.0803</td><td>0.0893</td><td></td></tr><tr><td></td><td>0</td><td>0.2741</td><td>0.3557</td><td>0.3789</td><td>0.2327</td><td>0.2494</td><td>0.1834</td><td>0.1842</td><td></td></tr><tr><td></td><td>25</td><td>0.2538</td><td>0.3681</td><td>0.3802</td><td>0.2382</td><td>0.2023</td><td>0.1254</td><td>0.1950</td><td></td></tr><tr><td>FedAvg-DP</td><td>50</td><td>0.2520</td><td>0.3705</td><td>0.3828</td><td>0.2453</td><td>0.2501</td><td>0.1107</td><td>0.1885</td><td></td></tr><tr><td></td><td>75</td><td>0.2205</td><td>0.3852</td><td>0.3871</td><td>0.2551</td><td>0.2387</td><td>0.0958</td><td>0.1782</td><td></td></tr><tr><td></td><td>100</td><td>0.1890</td><td>0.3905</td><td>0.3922</td><td>0.2658</td><td>0.2204</td><td>0.0852</td><td>0.1707</td><td></td></tr><tr><td></td><td>0</td><td>0.5013</td><td>0.2759</td><td>0.2593</td><td>0.3930</td><td>0.4384</td><td>0.2132</td><td>0.0638</td><td></td></tr><tr><td></td><td>25</td><td>0.4782</td><td>0.2781</td><td>0.2622</td><td>0.3984</td><td>0.4420</td><td>0.1759</td><td>0.0704</td><td></td></tr><tr><td>FairFed</td><td>50</td><td>0.4845</td><td>0.2803</td><td>0.2650</td><td>0.4057</td><td>0.4483</td><td>0.1602</td><td>0.0807</td><td></td></tr><tr><td></td><td>75</td><td>0.4281</td><td>0.3405</td><td>0.2689</td><td>0.4120</td><td>0.4557</td><td>0.1433</td><td>0.0945</td><td></td></tr><tr><td></td><td>100</td><td>0.3820</td><td>0.3190</td><td>0.2725</td><td>0.4201</td><td>0.4635</td><td>0.1307</td><td>0.0856</td><td></td></tr><tr><td></td><td>0</td><td>0.3526</td><td>0.3016</td><td>0.3882</td><td>0.2636</td><td>0.2863</td><td>0.1352</td><td>0.1673</td><td></td></tr><tr><td>PUFFLE</td><td>25</td><td>0.3502</td><td>0.3050</td><td>0.3905</td><td>0.2707</td><td>0.2921</td><td>0.1508</td><td>0.1785</td><td></td></tr><tr><td></td><td>50</td><td>0.3450</td><td>0.3285</td><td>0.3942</td><td>0.2751</td><td>0.2985</td><td>0.1357</td><td>0.1614</td><td></td></tr><tr><td></td><td>75</td><td>0.3389</td><td>0.3422</td><td>0.3987</td><td>0.2825</td><td>0.3054</td><td>0.1203</td><td>0.1831</td><td></td></tr><tr><td></td><td>100</td><td>0.3128</td><td>0.3565</td><td>0.4034</td><td>0.2901</td><td>0.3132</td><td>0.1052</td><td>0.1909</td><td></td></tr><tr><td></td><td>0</td><td>0.4621</td><td>0.2332</td><td>0.2434</td><td>0.1939</td><td>0.1420</td><td>0.2726</td><td>0.0807</td><td></td></tr><tr><td></td><td>25</td><td>0.4600</td><td>0.2555</td><td>0.2432</td><td>0.1985</td><td>0.1482</td><td>0.1658</td><td>0.0912</td><td></td></tr><tr><td>Ours (RESFL)</td><td>50</td><td>0.4557</td><td>0.2789</td><td>0.2420</td><td>0.2057</td><td>0.1573</td><td>0.1504</td><td>0.0783</td><td></td></tr><tr><td></td><td>75</td><td>0.4008</td><td>0.2925</td><td>0.2523</td><td>0.2121</td><td>0.1658</td><td>0.1357</td><td>0.1025</td><td></td></tr><tr><td></td><td>100</td><td>0.3851</td><td>0.3070</td><td>0.2575</td><td>0.2205</td><td>0.1727</td><td>0.1202</td><td>0.1093</td><td></td></tr></table>

Table 4: Performance comparison of federated learning algorithms under Fog in CARLA simulation: The result presents accuracy (mAP), fairness (1 - DI, EOP), privacy risks (MIA, AIA), robustness (BA AD), and fairness attack resilience (DPA EODD) across rain intensity levels.  

<table><tr><td rowspan="4">Algorithm</td><td rowspan="2">Fain Intensity (%)</td><td colspan="2">Utility</td><td colspan="2">Fairness</td><td colspan="2">Privacy Attacks</td><td>Robustness Attack</td><td>Fairness Attack</td></tr><tr><td colspan="2">Overall mAP</td><td>|1 - DI|</td><td>ΔEOP</td><td>MIA SR</td><td>AIA SR</td><td>BA AD</td><td>DPA EODD</td></tr><tr><td>0</td><td>0.3852</td><td>0.2356</td><td>0.2446</td><td>0.3915</td><td>0.4235</td><td>0.1531</td><td>0.0738</td><td></td></tr><tr><td>25</td><td>0.3801</td><td>0.2389</td><td>0.2485</td><td>0.3998</td><td>0.4302</td><td>0.1307</td><td>0.0814</td><td></td></tr><tr><td rowspan="5">FedAvg</td><td>50</td><td>0.3705</td><td>0.2441</td><td>0.2540</td><td>0.4070</td><td>0.4351</td><td>0.1185</td><td>0.0912</td><td></td></tr><tr><td>75</td><td>0.3583</td><td>0.2515</td><td>0.1628</td><td>0.4152</td><td>0.4475</td><td>0.1023</td><td>0.1028</td><td></td></tr><tr><td>100</td><td>0.3120</td><td>0.2580</td><td>0.1702</td><td>0.4228</td><td>0.4527</td><td>0.0790</td><td>0.1305</td><td></td></tr><tr><td>0</td><td>0.4621</td><td>0.2332</td><td>0.2434</td><td>0.1939</td><td>0.1420</td><td>0.2726</td><td>0.0807</td><td></td></tr><tr><td>25</td><td>0.4600</td><td>0.2555</td><td>0.2432</td><td>0.1985</td><td>0.1482</td><td>0.1658</td><td>0.0912</td><td></td></tr><tr><td rowspan="6">Ours (RESFL)</td><td>50</td><td>0.4557&lt;frel&gt;0.2789</td><td>0.2420</td><td>0.2057</td><td>0.1573</td><td>0.1504</td><td>0.0783</td><td></td><td></td></tr><tr><td>75</td><td>0.4008</td><td>0.2925</td><td>0.2523</td><td>0.2121</td><td>0.1658</td><td>0.1357</td><td>0.1025</td><td></td></tr><tr><td>100</td><td>0.3851</td><td>0.3070</td><td>0.2575</td><td>0.2205</td><td>0.1727</td><td>0.1202</td><td>0.1093</td><td></td></tr><tr><td>0</td><td>0.4621</td><td>0.2332</td><td>0.2434</td><td>0.1939</td><td>0.1420</td><td>0.2726</td><td>0.0807</td><td></td></tr><tr><td>25</td><td>0.4610</td><td>0.2759</td><td>0.2595</td><td>0.3930</td><td>0.4384</td><td>0.2132</td><td>0.0638</td><td></td></tr><tr><td>50</td><td>0.4950</td><td>0.2782</td><td>0.2625</td><td>0.4008</td><td>0.4453</td><td>0.1752</td><td>0.0725</td><td></td></tr><tr><td rowspan="5">FairFed</td><td>50</td><td>0.4820</td><td>0.2850</td><td>0.1703</td><td>0.4125</td><td>0.4550</td><td>0.1598</td><td>0.0914</td><td></td></tr><tr><td>75</td><td>0.4652</td><td>0.2980</td><td>0.1810</td><td>0.4250</td><td>0.4705</td><td>0.1257</td><td>0.1042</td><td></td></tr><tr><td>100</td><td>0.4380</td><td>0.3125</td><td>0.1947</td><td>0.4401</td><td>0.4852</td><td>0.1004</td><td>0.1501</td><td></td></tr><tr><td>0</td><td>0.3526</td><td>0.3016</td><td>0.2882</td><td>0.2636</td><td>0.2863</td><td>0.1352</td><td>0.1673</td><td></td></tr><tr><td>25</td><td>0.3500</td><td>0.3060</td><td>0.1905</td><td>0.2703</td><td>0.2908</td><td>0.1527</td><td>0.1785</td><td></td></tr><tr><td rowspan="5">PUFFLE</td><td>50</td><td>0.3452</td><td>0.3105</td><td>0.1940</td><td>0.2785</td><td>0.2983</td><td>0.1358</td><td>0.1895</td><td></td></tr><tr><td>75</td><td>0.3385</td><td>0.3157</td><td>0.1987</td><td>0.2850</td><td>0.3050</td><td>0.1003</td><td>0.2259</td><td></td></tr><tr><td>100</td><td>0.3023</td><td>0.3202</td><td>0.1043</td><td>0.2951</td><td>0.3128</td><td>0.0859</td><td>0.2881</td><td></td></tr><tr><td>0</td><td>0.4621</td><td>0.2332</td><td>0.2434</td><td>0.1939</td><td>0.1420</td><td>0.2726</td><td>0.0807</td><td></td></tr><tr><td>25</td><td>0.4650</td><td>0.2357</td><td>0.2467</td><td>0.1984</td><td>0.1471</td><td>0.1589</td><td>0.0925</td><td></td></tr><tr><td rowspan="3">Ours (RESFL)</td><td>50</td><td>0.4560</td><td>0.2389</td><td>0.1503</td><td>0.2052</td><td>0.1552</td><td>0.1403</td><td>0.1082</td><td></td></tr><tr><td>75</td><td>0.4508</td><td>0.2425</td><td>0.1545</td><td>0.2121</td><td>0.1658</td><td>0.1204</td><td>0.1301</td><td></td></tr><tr><td>100</td><td>0.4151</td><td>0.2470</td><td>0.2598</td><td>0.2205</td><td>0.1727</td><td>0.0753</td><td>0.1803</td><td></td></tr></table>

Table 5: Performance comparison of federated learning algorithms under Fog in CARLA simulation: The result presents accuracy (mAP), fairness  $(|1 - \mathbf{DI}|,\Delta \mathbf{EOP})$  privacy risks (MIA, AIA), robustness (BA AD), and fairness attack resilience (DPA EODD) across fog intensity levels.  

<table><tr><td rowspan="2">Algorithm</td><td rowspan="2">Fog Intensity (%)</td><td>Utility</td><td colspan="2">Fairness</td><td colspan="2">Privacy Attacks</td><td>Robustness Attack</td><td>Fairness Attack</td></tr><tr><td>Overall mAP</td><td>|1 - DI|</td><td>ΔEOP</td><td>MIA SR</td><td>AIA SR</td><td>BA AD</td><td>DPA EODD</td></tr><tr><td rowspan="5">FedAvg</td><td>0</td><td>0.3952</td><td>0.2356</td><td>0.2446</td><td>0.3915</td><td>0.4235</td><td>0.1531</td><td>0.0738</td></tr><tr><td>25</td><td>0.3650</td><td>0.2402</td><td>0.2605</td><td>0.4251</td><td>0.4357</td><td>0.1483</td><td>0.0851</td></tr><tr><td>50</td><td>0.3157</td><td>0.2650</td><td>0.2772</td><td>0.4175</td><td>0.4901</td><td>0.0891</td><td>0.1002</td></tr><tr><td>75</td><td>0.1304</td><td>0.3853</td><td>0.4157</td><td>0.4805</td><td>0.5502</td><td>0.0908</td><td>0.1657</td></tr><tr><td>100</td><td>0.0001</td><td>0.5202</td><td>0.3558</td><td>0.6153</td><td>0.6950</td><td>0.0000</td><td>0.3203</td></tr><tr><td rowspan="5">FedAvg-DP</td><td>0</td><td>0.2741</td><td>0.3557</td><td>0.3789</td><td>0.2327</td><td>0.2494</td><td>0.1834</td><td>0.1842</td></tr><tr><td>25</td><td>0.2500</td><td>0.3723</td><td>0.4001</td><td>0.2801</td><td>0.2703</td><td>0.1602</td><td>0.2015</td></tr><tr><td>50</td><td>0.2058</td><td>0.3905</td><td>0.4502</td><td>0.3156</td><td>0.3457</td><td>0.0558</td><td>0.2301</td></tr><tr><td>75</td><td>0.0953</td><td>0.5058</td><td>0.2904</td><td>0.4123</td><td>0.4605</td><td>0.0053</td><td>0.3879</td></tr><tr><td>100</td><td>0.0000</td><td>0.6852</td><td>0.7285</td><td>0.5207</td><td>0.5784</td><td>0.0000</td><td>0.4907</td></tr><tr><td rowspan="5">FairFed</td><td>0</td><td>0.5013</td><td>0.2759</td><td>0.2593</td><td>0.3930</td><td>0.4384</td><td>0.2132</td><td>0.0638</td></tr><tr><td>25</td><td>0.4950</td><td>0.2805</td><td>0.2681</td><td>0.4052</td><td>0.4552</td><td>0.2085</td><td>0.0709</td></tr><tr><td>50</td><td>0.4608</td><td>0.3107</td><td>0.2978</td><td>0.4451</td><td>0.4703</td><td>0.1505</td><td>0.0953</td></tr><tr><td>75</td><td>0.1952</td><td>0.4503</td><td>0.3559</td><td>0.5085</td><td>0.5598</td><td>0.1104</td><td>0.2156</td></tr><tr><td>100</td><td>0.0753</td><td>0.5801</td><td>0.4802</td><td>0.5802</td><td>0.6350</td><td>0.0753</td><td>0.2851</td></tr><tr><td rowspan="5">PUFFLE</td><td>0</td><td>0.3526</td><td>0.3016</td><td>0.3882</td><td>0.2636</td><td>0.2863</td><td>0.1352</td><td>0.1673</td></tr><tr><td>25</td><td>0.3458</td><td>0.3152</td><td>0.4057</td><td>0.3104</td><td>0.307</td><td>0.1205</td><td>0.1854</td></tr><tr><td>50</td><td>0.2801</td><td>0.3682</td><td>0.4528</td><td>0.3405</td><td>0.3708</td><td>0.1104</td><td>0.2128</td></tr><tr><td>75</td><td>0.0957</td><td>0.4827</td><td>0.5782</td><td>0.4256</td><td>0.5083</td><td>0.0552</td><td>0.3304</td></tr><tr><td>100</td><td>0.0125</td><td>0.6250</td><td>0.7208</td><td>0.5507</td><td>0.6005</td><td>0.0125</td><td>0.4708</td></tr><tr><td rowspan="5">Ours (RESFL)</td><td>0</td><td>0.4621</td><td>0.2332</td><td>0.2344</td><td>0.1939</td><td>0.1420</td><td>0.2726</td><td>0.0807</td></tr><tr><td>25</td><td>0.4503</td><td>0.2452</td><td>0.2683</td><td>0.2054</td><td>0.1658</td><td>0.1859</td><td>0.0910</td></tr><tr><td>50</td><td>0.4051</td><td>0.2780</td><td>0.2778</td><td>0.2601</td><td>0.2153</td><td>0.1201</td><td>0.1208</td></tr><tr><td>75</td><td>0.3107</td><td>0.3505</td><td>0.4058</td><td>0.3702</td><td>0.3557</td><td>0.1108</td><td>0.2005</td></tr><tr><td>100</td><td>0.1652</td><td>0.4850</td><td>0.5152</td><td>0.4450</td><td>0.4308</td><td>0.0552</td><td>0.2993</td></tr></table>