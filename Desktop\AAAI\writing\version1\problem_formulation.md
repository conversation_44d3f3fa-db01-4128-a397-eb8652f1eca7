# Problem Formulation and Threat Model / 问题表述与威胁模型

## English Version

### Problem Setting / 问题设置

We consider a multimodal federated learning scenario with $N$ participating clients (healthcare institutions) and a central server. Each client $i \in \{1, 2, ..., N\}$ possesses a local dataset $\mathcal{D}_i = \{(x_i^{(j)}, y_i^{(j)})\}_{j=1}^{n_i}$, where $x_i^{(j)} = \{x_i^{(j,m)}\}_{m=1}^{M}$ represents multimodal input data with $M$ modalities (e.g., medical images, text reports, sensor data), and $y_i^{(j)}$ denotes the corresponding label. The local dataset size is $n_i$, and the total number of samples across all clients is $n = \sum_{i=1}^{N} n_i$.

The goal of multimodal federated learning is to collaboratively train a global model $f_\theta$ parameterized by $\theta$ that minimizes the global objective:

$$\min_\theta F(\theta) = \sum_{i=1}^{N} \frac{n_i}{n} F_i(\theta)$$

where $F_i(\theta) = \frac{1}{n_i} \sum_{j=1}^{n_i} \ell(f_\theta(x_i^{(j)}), y_i^{(j)})$ is the local objective function for client $i$, and $\ell(\cdot, \cdot)$ is the loss function.

### Trustworthiness Dimensions / 可信性维度

We define trustworthiness in multimodal federated learning across five key dimensions:

**Privacy Preservation ($\mathcal{P}$)**: The system should protect sensitive information in both individual data samples and model parameters. We quantify privacy using differential privacy guarantees and information-theoretic measures of data leakage.

**Security Robustness ($\mathcal{S}$)**: The system should be resilient against various attacks including adversarial examples, model poisoning, and cross-modal attacks. Security is measured by attack detection accuracy and defense effectiveness.

**Fairness Guarantee ($\mathcal{F}$)**: The model should provide equitable performance across different demographic groups and data distributions. Fairness is evaluated using metrics such as demographic parity and equalized odds.

**Explainability ($\mathcal{E}$)**: The model decisions should be interpretable and provide meaningful insights for clinical decision-making. Explainability is assessed through attention visualization and feature importance analysis.

**Robustness ($\mathcal{R}$)**: The system should maintain stable performance under various conditions including missing modalities, distribution shifts, and noisy data. Robustness is measured by performance degradation under different perturbations.

### Trustworthiness Score / 可信性评分

We define a comprehensive trustworthiness score $T$ as a weighted combination of individual dimension scores:

$$T = \alpha_\mathcal{P} \cdot \mathcal{P} + \alpha_\mathcal{S} \cdot \mathcal{S} + \alpha_\mathcal{F} \cdot \mathcal{F} + \alpha_\mathcal{E} \cdot \mathcal{E} + \alpha_\mathcal{R} \cdot \mathcal{R}$$

where $\alpha_\mathcal{P}, \alpha_\mathcal{S}, \alpha_\mathcal{F}, \alpha_\mathcal{E}, \alpha_\mathcal{R} \geq 0$ are weighting parameters with $\sum \alpha = 1$, and each dimension score is normalized to $[0, 1]$.

### Threat Model / 威胁模型

#### Adversary Capabilities / 对手能力

We consider a comprehensive threat model that encompasses various adversary capabilities:

**Honest-but-Curious Server**: The central server follows the protocol correctly but may attempt to infer private information from client updates. This represents scenarios where cloud service providers or third-party aggregators may have economic incentives to extract valuable insights from federated learning processes.

**Malicious Clients**: A subset of clients $\mathcal{M} \subset \{1, 2, ..., N\}$ with $|\mathcal{M}| \leq \beta N$ (where $\beta < 0.5$) may deviate from the protocol to compromise the global model or extract private information from other clients. Malicious clients can manipulate their local data, model updates, or both.

**External Attackers**: Adversaries who do not participate in the federated learning process but can observe communication channels, inject adversarial inputs during inference, or exploit vulnerabilities in the deployed system.

#### Attack Vectors / 攻击向量

**Data Poisoning Attacks**: Malicious clients inject corrupted or adversarial samples into their local datasets to degrade model performance or introduce backdoors. In multimodal settings, attackers can poison specific modalities or create cross-modal inconsistencies.

**Model Poisoning Attacks**: Malicious clients send crafted model updates designed to compromise the global model. This includes gradient-based attacks, parameter manipulation, and Byzantine failures.

**Cross-Modal Attacks**: Novel attack vectors that exploit the interaction between different modalities. For example, adversarial perturbations in medical images that cause misclassification when combined with corresponding text reports.

**Inference Attacks**: Attempts to extract private information about training data through model inversion, membership inference, or property inference attacks. In healthcare settings, this could reveal sensitive patient information.

**Fairness Attacks**: Deliberate attempts to introduce or amplify bias in the global model, potentially leading to discriminatory outcomes for certain demographic groups.

#### Healthcare-Specific Threats / 医疗特定威胁

**Regulatory Compliance Violations**: Attacks that cause the system to violate healthcare regulations such as HIPAA, GDPR, or FDA guidelines, potentially resulting in legal and financial consequences.

**Clinical Decision Manipulation**: Attacks designed to influence clinical decision-making by subtly biasing model predictions toward specific diagnoses or treatments.

**Patient Privacy Breaches**: Sophisticated attacks that attempt to re-identify patients or extract sensitive medical information from federated learning systems.

### Problem Objectives / 问题目标

Given the above setting and threat model, our primary objectives are:

**Objective 1 - Trustworthiness Maximization**: Design algorithms that maximize the overall trustworthiness score $T$ while maintaining competitive model performance.

**Objective 2 - Multi-dimensional Optimization**: Develop methods that can simultaneously optimize across all trustworthiness dimensions without significant trade-offs.

**Objective 3 - Dynamic Adaptation**: Create adaptive mechanisms that can adjust trustworthiness strategies based on real-time threat assessments and changing conditions.

**Objective 4 - Healthcare Compliance**: Ensure that the proposed solutions meet healthcare-specific requirements including regulatory compliance and clinical workflow integration.

### Challenges / 挑战

**Challenge 1 - Multi-objective Optimization**: Optimizing multiple trustworthiness dimensions simultaneously often involves conflicting objectives, requiring sophisticated optimization strategies.

**Challenge 2 - Cross-Modal Complexity**: The interaction between different modalities introduces additional complexity in both attack and defense mechanisms.

**Challenge 3 - Dynamic Threat Landscape**: The evolving nature of threats requires adaptive defense mechanisms that can respond to new attack vectors.

**Challenge 4 - Scalability**: Solutions must scale to large numbers of clients and high-dimensional multimodal data while maintaining efficiency.

**Challenge 5 - Practical Deployment**: The proposed methods must be practical for real-world healthcare deployments with limited computational resources and strict latency requirements.

## Chinese Version / 中文版本

### 问题设置

我们考虑一个具有$N$个参与客户端（医疗机构）和一个中央服务器的多模态联邦学习场景。每个客户端$i \in \{1, 2, ..., N\}$拥有本地数据集$\mathcal{D}_i = \{(x_i^{(j)}, y_i^{(j)})\}_{j=1}^{n_i}$，其中$x_i^{(j)} = \{x_i^{(j,m)}\}_{m=1}^{M}$表示具有$M$个模态（例如，医学图像、文本报告、传感器数据）的多模态输入数据，$y_i^{(j)}$表示相应的标签。本地数据集大小为$n_i$，所有客户端的样本总数为$n = \sum_{i=1}^{N} n_i$。

多模态联邦学习的目标是协作训练一个由$\theta$参数化的全局模型$f_\theta$，该模型最小化全局目标：

$$\min_\theta F(\theta) = \sum_{i=1}^{N} \frac{n_i}{n} F_i(\theta)$$

其中$F_i(\theta) = \frac{1}{n_i} \sum_{j=1}^{n_i} \ell(f_\theta(x_i^{(j)}), y_i^{(j)})$是客户端$i$的本地目标函数，$\ell(\cdot, \cdot)$是损失函数。

### 可信性维度

我们在五个关键维度上定义多模态联邦学习中的可信性：

**隐私保护（$\mathcal{P}$）**：系统应保护个别数据样本和模型参数中的敏感信息。我们使用差分隐私保证和数据泄露的信息论度量来量化隐私。

**安全鲁棒性（$\mathcal{S}$）**：系统应对各种攻击具有弹性，包括对抗样本、模型投毒和跨模态攻击。安全性通过攻击检测准确率和防御有效性来衡量。

**公平性保证（$\mathcal{F}$）**：模型应在不同人口统计群体和数据分布中提供公平的性能。公平性使用人口统计平等和机会均等等指标进行评估。

**可解释性（$\mathcal{E}$）**：模型决策应该是可解释的，并为临床决策提供有意义的见解。可解释性通过注意力可视化和特征重要性分析进行评估。

**鲁棒性（$\mathcal{R}$）**：系统应在各种条件下保持稳定性能，包括缺失模态、分布偏移和噪声数据。鲁棒性通过不同扰动下的性能退化来衡量。

### 可信性评分

我们将综合可信性评分$T$定义为各个维度评分的加权组合：

$$T = \alpha_\mathcal{P} \cdot \mathcal{P} + \alpha_\mathcal{S} \cdot \mathcal{S} + \alpha_\mathcal{F} \cdot \mathcal{F} + \alpha_\mathcal{E} \cdot \mathcal{E} + \alpha_\mathcal{R} \cdot \mathcal{R}$$

其中$\alpha_\mathcal{P}, \alpha_\mathcal{S}, \alpha_\mathcal{F}, \alpha_\mathcal{E}, \alpha_\mathcal{R} \geq 0$是权重参数，满足$\sum \alpha = 1$，每个维度评分标准化到$[0, 1]$。

### 威胁模型

#### 对手能力

我们考虑一个包含各种对手能力的综合威胁模型：

**诚实但好奇的服务器**：中央服务器正确遵循协议，但可能试图从客户端更新中推断私人信息。这代表云服务提供商或第三方聚合器可能有经济动机从联邦学习过程中提取有价值见解的场景。

**恶意客户端**：客户端子集$\mathcal{M} \subset \{1, 2, ..., N\}$，其中$|\mathcal{M}| \leq \beta N$（其中$\beta < 0.5$）可能偏离协议以破坏全局模型或从其他客户端提取私人信息。恶意客户端可以操纵其本地数据、模型更新或两者。

**外部攻击者**：不参与联邦学习过程但可以观察通信渠道、在推理期间注入对抗输入或利用部署系统中漏洞的对手。

#### 攻击向量

**数据投毒攻击**：恶意客户端向其本地数据集注入损坏或对抗样本以降低模型性能或引入后门。在多模态设置中，攻击者可以投毒特定模态或创建跨模态不一致性。

**模型投毒攻击**：恶意客户端发送旨在破坏全局模型的精心设计的模型更新。这包括基于梯度的攻击、参数操纵和拜占庭故障。

**跨模态攻击**：利用不同模态间交互的新颖攻击向量。例如，医学图像中的对抗扰动在与相应文本报告结合时导致误分类。

**推理攻击**：通过模型逆向、成员推理或属性推理攻击试图提取关于训练数据的私人信息。在医疗环境中，这可能揭示敏感的患者信息。

**公平性攻击**：故意试图在全局模型中引入或放大偏见，可能导致对某些人口统计群体的歧视性结果。

#### 医疗特定威胁

**法规合规违规**：导致系统违反HIPAA、GDPR或FDA指导原则等医疗法规的攻击，可能导致法律和财务后果。

**临床决策操纵**：旨在通过微妙地将模型预测偏向特定诊断或治疗来影响临床决策的攻击。

**患者隐私泄露**：试图重新识别患者或从联邦学习系统中提取敏感医疗信息的复杂攻击。

### 问题目标

给定上述设置和威胁模型，我们的主要目标是：

**目标1 - 可信性最大化**：设计算法，在保持竞争性模型性能的同时最大化整体可信性评分$T$。

**目标2 - 多维度优化**：开发能够同时在所有可信性维度上优化而不显著权衡的方法。

**目标3 - 动态适应**：创建能够基于实时威胁评估和变化条件调整可信性策略的自适应机制。

**目标4 - 医疗合规**：确保提出的解决方案满足医疗特定要求，包括法规合规和临床工作流集成。

### 挑战

**挑战1 - 多目标优化**：同时优化多个可信性维度通常涉及冲突目标，需要复杂的优化策略。

**挑战2 - 跨模态复杂性**：不同模态间的交互在攻击和防御机制中引入了额外的复杂性。

**挑战3 - 动态威胁环境**：威胁的演变性质需要能够响应新攻击向量的自适应防御机制。

**挑战4 - 可扩展性**：解决方案必须扩展到大量客户端和高维多模态数据，同时保持效率。

**挑战5 - 实际部署**：提出的方法必须适用于具有有限计算资源和严格延迟要求的真实世界医疗部署。
