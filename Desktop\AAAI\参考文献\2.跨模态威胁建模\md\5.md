# Runtime Backdoor Detection for Federated Learning via Representational Dissimilarity Analysis

<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON> Senior Member, IEEE, and <PERSON>g <PERSON>

Abstract- Federated learning (FL), as a powerful learning paradigm, trains a shared model by aggregating model updates from distributed clients. However, the decoupling of model learning from local data makes FL highly vulnerable to backdoor attacks, where a single compromised client can poison the shared model. While recent progress has been made in backdoor detection, existing methods face challenges with detection accuracy and runtime effectiveness, particularly when dealing with complex model architectures. In this work, we propose a novel approach to detecting malicious clients in an accurate, stable, and efficient manner. Our method utilizes a sampling- based network representation method to quantify dissimilarities between clients, identifying model deviations caused by backdoor injections. We also propose an iterative algorithm to progressively detect and exclude malicious clients as outliers based on these dissimilarity measurements. Evaluations across a range of benchmark tasks demonstrate that our approach outperforms state- of- the- art methods in detection accuracy and defense effectiveness. When deployed for runtime protection, our approach effectively eliminates backdoor injections with marginal overheads.

Index Terms- Backdoor detection, federated learning, dissimilarity analysis.

# 1 INTRODUCTION

FEDERATED learning (FL) [1] has recently emerged as a powerful paradigm for distributed model learning. The FL design decouples model learning from direct access to the training data, which could enable the development of intelligent applications while protecting data privacy. The term of federated learning sources from the decentralized approach that the learning task is resolved by a federation of participating clients (devices). Each client owns a private training dataset and trains the model locally on its device. The central server coordinates the training of a shared model (also referred to as global model) by aggregating the local updates from participating clients.

At first, a global model is initialized on the central server. In each round, the server broadcasts the current global model to the local clients. Each client then performs training on the local data and sends the computed model update back to the server. The server updates the global model by aggregating the collected local updates. Through repeating this process, the global model achieves the learning objective. This design enhances data privacy and enables many privacy- sensitive applications, including the predictive model learning in healthcare where the confidentiality of patient records across decentralized hospitals must be enforced [2], [3], [4].

Despite the benefits, the invisibility of data and training process on local clients makes FL rather susceptible to malicious attacks [5], [6], [7], [8], [9], [10], [11], [12], [13]. A prominent representative is the vulnerability to backdoor attacks [5], [8], [9], [11], [12], [13] where the adversary seeks to poison the global model through injecting a certain backdoor into one or multiple clients. The backdoor can easily propagate from clients to the global model and make it classify inputs embedded with an attacker- chosen pattern into the targeted label, while preserving the prediction performance on normal data. This type of attack is intrinsically hard to detect since the backdoor triggering abnormal behaviors is a secret and only known to the adversary.

Training tasks relying on FL are usually large- scale, requiring significant time and computation resources. If a backdoor attack occurs, post- training detection renders all prior training efforts in vain. Thus, general backdoor detection methods for post- trained models [?], [?] are not well- suited for the federated learning context. A runtime detector operating in parallel with the training process is essential for immediately detecting and eliminating malicious clients, preventing the global model from being poisoned and minimizing resource waste. The aim of our work is to propose a runtime backdoor detection approach to protecting the robustness of the global model for FL. In practice, an adversary could achieve stealthy backdoor injection by controlling the malicious allies ratio or through cautious data poisoning for multiple rounds. Besides, the global models for different intelligent applications could have complex architectures and large- size parameters. An

ideal backdoor detection approach should first and foremost have accurate and stable performance against varied attack scenarios for different applications. To be deployed as a runtime solution, the approach should pursue efficiency in backdoor detection and elimination and lower the incurred overhead.

A series of methods to resist backdoor attacks in FL have been proposed in the literature. A straightforward way to discern the hacked clients is to make a direct inspection over the clients' model parameters [14], [15], [16], [17], [18]. However, the approaches based on parameter inspection are faced with effectiveness limitation in resisting backdoor attacks for models with large- size parameters. Besides, parameter- wise inspection of local models could lead to heavy computation overhead for complex models. Recently, researchers have proposed detection methods by inspecting prediction errors on validation data instead of looking into the model parameters, among which BaFFLe [19] is a representative. However, they could be tricked by stealthy attackers who carefully control their attacks such that the malicious model performs competently on the validation data as benign clients.

Another factor affecting the effectiveness of backdoor detection in FL is the variation in data distributions among clients, which introduces non- negligible differences in local model training. This is common in FL and is referred to as non- independent and identically distributed (Non- IID) data. Training on Non- IID data leads to larger parameter differences among clients, making it more challenging to detect malicious clients from biased honest ones. Parameter inspection- based methods, such as those in [14], [15], [16], [17], [18], do not rely on assumptions about the data distribution. The validation- based detection method BaFFLe [19] simulates Non- IID distributions by assigning data according to the Dirichlet distribution. The approach in [27] mitigates backdoor attacks by adjusting the learning rate of the aggregation, accounting for both IID and Non- IID data. While many existing methods for resisting backdoor attacks in FL are generally agnostic to data distribution, our fingdings in Section 5 indicate that certain approaches suffer from performance degradation with heterogeneous data. To summarize, the main challenges faced by existing approaches are  $\bullet$  detection accuracy and stability across varied attack configurations and complex model architectures,  $\bullet$  efficiency in runtime backdoor detection, and  $\bullet$  adaptability to Non- IID data.

In this work, we propose a novel backdoor detection approach for FL to address the aforementioned challenges. As shown in Fig. 1, our approach consists of three components. First, we use a sampling- based model representation method to characterize the behaviors of local clients. Intuitively, we construct client behavior representations by analyzing output vectors to sampled data from different classes. We then calculate output response differences for each pair of samples, forming representational dissimilarity matrices (RDMs). Next we perform client dissimilarity analysis using these RDMs, quantifying the dissimilarity among clients via Pearson distance to detect model deviations caused by backdoor injection. We further propose an iterative algorithm to identify and exclude malicious clients from aggregation as outliers, effectively eliminating backdoor embedding.

Overall, our approach leads to the following benefits. First, our representation method avoids the inspection and comparison on model parameters and meanwhile captures the client behaviors with more comprehensive information than prediction errors, which is easily applicable to complex model architectures and has the potential to deliver more accurate and stable detection performance (Challenge  $\bullet$ ). Moreover, the dissimilarity analysis in our approach has a need to perform the parameter- wise computation, which improves the efficiency for client differential analysis (Challenge  $\bullet$ ). Concerning Non- IID data, model representation based on RDMs along with the iterative algorithm design could effectively differentiate the model deviation caused by backdoor injection from the influence of Non- IID data (Challenge  $\bullet$ ).

Extensive experiments are conducted to evaluate the effectiveness of our approach in detecting FL backdoor based on 30 different attack configurations across image and text classification tasks. We further investigate the effectiveness of our approach when deployed as a runtime defense mechanism against backdoor attacks. The experimental results show that the proposed approach achieves better detection performance than the state- of- the- arts. Specifically, our approach is able to detect malicious clients accurately and stably, demonstrating  $97.6\%$  detection accuracy (in F1- score) on average across all tasks. The results on defense show that our approach could effectively defend FL and eliminate backdoor injection stably and efficiently across different scenarios and benchmark tasks than the state- of- the- arts.

In summary, our main contributions are:

- We propose a novel runtime backdoor detection framework for federated learning. For the first time, sampling-based model representation is adopted for accurate and efficient identification of malicious clients in FL, to eliminate the backdoor injection.- We propose an iterative algorithm to detect and exclude malicious clients progressively by determining the clients' outlier degree and introduce a threshold refinement method to fit into different tasks. The iterative algorithm and threshold refinement mechanism enable our approach to detect malicious clients more accurately and stably across varied scenarios.- We implement our approach as a runtime monitoring tool which can be easily incorporated into the learning procedure, and perform a systematic evaluation of our approach in detection accuracy and defense effectiveness to show the generality and scalability of our approach.

# 2 RELATED WORK

# 2.1 Robust Federated Learning

A series of works [20], [21], [22], [23], [24], [25], [26] have been proposed to endow robust aggregation against adversarial (Byzantine) attacks that cause degradation in the learning performance for FL. These works seek more robust aggregation rules to attain Byzantine resilience based on techniques including median, mean aggregation but with conditional exclusion, combination or variant of these techniques. For example, Krum [20] proposed to select the

update vector that minimized the sum of square distance to its neighbors. [23] proposed two robust aggregation rules with one rule based on coordinate- wise median and the other based on coordinate- wise trimmed mean. [25] adopted geometric median, a multidimensional generalization of the median, to attain convergence against update corruption, which demonstrated effectiveness for high corruption ratio. [26] further improved privacy preservation by introducing a robustness- checking method to counter Byzantine attacks, combining zero- knowledge proofs with secret- sharing techniques. However, these works focus on achieving robust aggregation against Byzantine attacks that inhibit the convergence of the global model instead of the backdoor injection.

Several approaches have also been proposed to resist backdoor attacks for FL. [16] proposed to train a spectral anomaly detection model to detect malicious clients in FL, which used test dataset to generate model updates on the central server. The detection model - VAE (Variational Auto- Encoder) is then trained based on the collected model updates and used to detect backdoor based on the reconstruction error. This approach sets the detection threshold as the mean of reconstruction errors of the VAE for client updates under inspection, which may lead to a considerable amount of false alarms. FoolsGold [14] proposed to identify suspicious clients based on the similarity of malicious updates, which could be circumvented by a single malicious client [9]. [17] presented a robust aggregation algorithm by estimating the parameter confidence based on its residual to a regression line, and assigning the weight of each local model by accumulating its parameter confidence. [27] proposed a defense approach by adjusting the sign of the update parameter, where the sign of the update parameter remained the same when the sum of parameter signs was higher than a learning threshold, otherwise it was flipped. The above approaches to resisting backdoor attacks all rely on inspecting explicit parameters of local models.

[18] detected malicious clients by inspecting the model weights in the filters of CNN layers and used Mahalanobis- distance- based algorithm to identify anomalous filters. It then calculated the anomaly score for each client by summing up the total number of anomalous filters that belong to the clients. The key difference between [18] and our work is the rationale for backdoor detection, where [18] inspects weights of the CNN filters and we inspect sample- based model representation. Also, [18] assumes the defender is aware of an estimated fraction of anomalous filters, while our approach does not rely on such assumptions. Unlike the aforementioned works, BaFFLe [19] proposed to detect suspicious poisoning attempts by assessing the per- class prediction performance based on validation data. Each validation client determines whether the global model of the previous round is malicious or not by comparing the per- class prediction error variation of the investigated model with the accepted global models. The current global model is rejected if the rejection votes reach the quorum threshold. However, this approach eliminates the negative impact of backdoor injection by dropping suspicious global models, which can lead to more learning rounds and computing resource consumption.

# 2.2 Network Representation Comparison

Network representations have been extensively studied for network behavior analysis and interpretation. [28] studied network representations and characterized a neuron's portrayal based on the neuron's outputs over a finite set of inputs taken from training or validation set. The network representation was further used for learning dynamic analysis and classification semantics interpretation based on singular vector canonical correlation analysis. [29] also used neuron outputs over a dataset as the multidimensional variates and investigated the generalization and memorization difference of convolutional neural networks and dynamics of recurrent neural networks over both training and sequential time steps based on canonical correlation analysis. [30] studied differences among deep neural networks with different initialization seeds and regularization, which characterized the network representation in forms of pairwise distances between high- dimensional activation vectors of test data.

The primary objective of the above works is extracting network representation to analyze training dynamics concerning training time, sequence steps, initialization, etc. However, it is still unknown whether and how the sampling- based network representation can be used to analyze model behavior differences and reveal poisoning attempts in the context of backdoor attacks.

# 3 BACKGROUND

# 3.1 Backdoor Attacks

Backdoor attacks [5], [8], [9], [11], [12], [13] represent a form of targeted poisoning attack, where the adversary attempts to change the target models' behaviors on data items embedded with the backdoor pattern. This is generally achieved by creating poisoned data samples and inserting them into the training data. The formalization of backdoor attack notions follows [19]. Given a neural network  $\mathcal{N}$  for classification with the mapping function  $f^{\mathcal{N}}$  that maps the input data  $\mathcal{X}$  to output classes  $\mathcal{V}$ , a backdoor adversary  $\mathcal{A}$  is associated with a target label  $y_{t} \in \mathcal{V}$  and a set of backdoor instances  $X^{*} \subsetneq \mathcal{X}$  with an embedded backdoor pattern. The objective of the adversary  $\mathcal{A}$  is to make the neural network classify backdoor instances embedded with the backdoor pattern into the target class, i.e.,  $\forall x \in X^{*} \subsetneq f^{\mathcal{N}}(x) = y_{t}$ , while preserving the prediction performance for the other data instances. In this way, the adversary can have a negative impact on the neural network's robustness without being noticed. The typical criteria to measure the adversary's negative impact to the model robustness is the attack success rate. It is defined as the portion of data samples in  $X^{*}$  that are predicted as the target label  $y_{t}$  by the neural network.

$$
ASR_{X^{*},y_{t}} = \frac{|\{x\in X^{*}\}f^{\mathcal{N}}(x) = y_{t}\} |}{|X^{*}|}
$$

# 3.2 Attack Model

We consider a typical backdoor attack scenario in federated learning. The adversary attempts to change the global models' behaviors on data embedded with the backdoor pattern, while preserving the prediction performance on normal test data. We assume that the adversary could manipulate

![](images/ee1c1935c2d69d2f3729970e7d08549e5cfb92d5607d38dba520f777deb48808.jpg)  
Fig. 1: An overview of our approach.

the local clients under their control by embedding certain patterns into the local training data and assigning these poisoned data with the target backdoor label.

Suppose there are  $K$  malicious clients out of  $N$  local clients. Let  $D = \{D_{1},\dots ,D_{N}\}$  denote the union of original local datasets stored in the clients. The  $K$  malicious clients inject the backdoor by poisoning  $r_j = r*\left|D_j\right|$ $(1\leq j\leq K)$  data samples (with attack rate  $r$  ).Let  $D_j^{\prime}$  denote the poisoned local dataset. The malicious clients then train the local models on the poisoned datasets, while benign clients train the local models on the original datasets. We use  $w$  to denote the model parameters. At round  $t_r$  the central server broadcasts current global model  $w_{t - 1}$  to all clients. For any malicious client  $C_j$  , the model is updated based on  $D_j^{\prime}$  ..

![](images/46deafd3145393f7116d5d338a2a2248b9147acf88a163e3fe23cb3b99cbfec5.jpg)

The local models under attack are thus trained with a mixed learning objective to fit both the main classification task and the backdoor task. Following [19], we assume the honest clients still take the majority. The adversary may poison the model in one training round or in continuous rounds, which are termed as model replacement and the naive approach in [9]. We consider both attack scenarios in this work.

From the central server side, we assume the server maintains a small set of clean test data, which are typically used to evaluate the global model performance or aid in designing the network architecture of FL [31]. We assume the general federated learning protocol where the local model updates are sent back to the central server. We aim to detect whether the local models to be aggregated have been poisoned by backdoor attacks and eliminate their negative impacts to defend the global model.

# 4 APPROACH

# 4.1 Approach Overview

An overview of our approach to detecting clients hacked by backdoor attacks in FL is shown in Fig. 1, which contains three major components. First, for each client, we characterize its behavior by extracting the model representation in the form of RDM. Then, we perform the representation comparison and quantify the dissimilarity between the clients by calculating the Pearson distance with regard to their corresponding RDMs. The RDM extraction and representation comparison together quantitatively characterize the dissimilarity profile among local clients. Finally, we propose an iterative algorithm to detect malicious clients by calculating the clients' local outlier factor (LOF) based on the dissimilarity profile. In runtime deployment, our approach identifies and excludes malicious clients from model aggregation to eliminate the negative impact of backdoor attacks. The detailed procedure is outlined in Algorithm 1, which detects the attacked clients from  $N$  local clients to be aggregated. In the following, we elaborate on the design of the major components.

# 4.2 Model Representation Extraction

Existing detection approaches often rely on the differences in local model parameters to detect malicious clients. However, the model parameters can be too large in size to process

for complex network architectures, which leads to considerable computation overhead for runtime deployment. To address this problem, in this work, we adopt a samplingbased model representation method and characterize the client behaviors in the form of representational dissimilarity matrix (RDM) based on the observations of client output responses to a set of inputs.

Let  $S = \{s_{1},\dots ,s_{b},\dots ,s_{m*b}\}$  denote the set of sampled data with  $b$  samples for each class  $(m*b$  in total for  $m$  classes). Given a local model  $f_{w}$  with weights  $w_{j}$  for any  $s_i\in S,$  we compute the output vector  $f_{w}(s_{i})$  to characterize the pattern recognized by local model  $f_{w}$  on data  $s_i$  . Then the representational geometry for each local model  $f_{w}$  is calculated as

$$
M a t^{R} = \left[ \begin{array}{c c c c}{d_{1,1}} & {d_{1,2}} & \dots & {d_{1,m*b}}\\ {d_{2,1}} & {d_{2,2}} & \dots & {d_{2,m*b}}\\ \dots & \dots & \dots & \dots \\ {d_{m*b,1}} & {d_{m*b,2}} & \dots & {d_{m*b,m*b}} \end{array} \right]
$$

where  $d_{i,j} = dist(f_w(s_i),f_w(s_j))$  represents the cosine distance between a pair of data items  $(s_i,s_j)$  in the output space.

The dissimilarity matrix represents client behaviors through pairwise output differences, characterizing how data items of different classes are grouped or separated by the given model, which provides an estimate of the geometric representation of the sampled data in output space. As indicated in [30], such geometric representation is able to reveal differences among neural network instances trained with different input statistics. An additional benefit of capturing model representation in terms of relative distances is the invariance to rotations of the output vector space. Therefore, this representation method could tolerate the misalignment of the output space of different clients.

The detailed procedure for client RDM extraction is shown in Algorithm 1 (Lines 1- 7). An illustration of the procedure is shown in Fig. 2. Firstly, we randomly select a set of input stimuli  $S$  from clean test dataset collected at the server for prediction performance evaluation. To balance different classes, we select an equal number of inputs for each class. Then, with federated learning protocol where local model updates are sent back to the central server, we extract the corresponding output response vectors to the sampled inputs for each client model (Line 3) at the server side. Next, we compute the difference between each pair of output vectors with cosine distance metric to construct the dissimilarity matrix. Specifically, for a pair of inputs  $s_j$  and  $b_j,$  we take their pairwise output vectors  $v_{i}$  and  $v_{j}$  and calculate their cosine distance. The computed distance value then constitutes an element (at the  $i\cdot$  - th row and  $j$  - th column and the symmetrical position) of the RDM (Line 6). Together, all the pairwise difference items form an estimate of a client model's geometric representation.

# 4.3 Client Dissimilarity Quantification

To obtain the quantitative assessment of client differences, we compute the client dissimilarity by comparing the RDM of each client instance. The insight of client differential analysis on the level of RDMs is that two local client models share more similarity if they emphasize the same representation distinctions among the input data. Compared with benign clients, malicious ones would trigger different representation geometry over the input stimuli, due to the influence of backdoor injection. The differences between the RDM of a malicious client and the RDM of a benign client should be larger than that between two benign clients.

![](images/6acff8fc66dd75054b16acb540ac3bd2f7c9aa7695c70ab21e29f2a9daac5931.jpg)  
Fig. 2: The model representation in the form of RDM.

We now discuss potential metrics for client dissimilarity quantification. First of all, measuring the similarity between representational geometry of computational models by correlation is well- established and commonly- used to analyze network representations with regard to training time, sequence steps, initialization and regularization [28], [29], [30]. Especially, Pearson Correlation has demonstrated its effectiveness in reflecting the representation consistency between network instances [30]. Moreover, since there might be magnitude differences among the model instances, the invariant property to scale and mean of Pearson distance [32] makes it a good metric for this task. Compared with Pearson distance, metrics like hamming distance are typically used to measure the edit distance between two sequences, thus not suitable for backdoor detection. Metrics such as Euclidean and Manhattan are quite sensitive to the scale differences among client models, which are only appropriate for data measured on the same scale. One feasible alternative is mahalanobis distance, which is also invariant to the scale difference. But it is generally used to measure the distance between a data point and a distribution instead of between data points. We decide to quantify the client dissimilarity with Pearson distance between the extracted RDMs (Line 10). Intuitively, the more consistent two clients are in recognizing pairwise inputs, the smaller the Pearson distance between their models is.

Specifically, to calculate the Pearson distance between RDMs, we first flatten the elements in the upper triangle of each RDM to obtain a vector of observations on model behaviors. There is no loss of information as the diagonal observations capturing the behavior difference between the same data item are always 0, and the upper and lower triangles are symmetric. The elements in the upper triangle then provide a set of non- repetitive observations on the representation geometry of the local model  $f_{w}$  .We abuse the same notation  $Mat_R^R$  to denote the set of observations.

Let  $Mat_1^R = (u_1,u_2,\dots ,u_n),Mat_2^R = (v_1,v_2,\dots ,v_n)$  denote the sets of observations on two models' representation geometry. The Pearson distance between the corresponding clients is calculated as follows:

$$
d(Mat_1^R,Mat_2^R) = 1 - \frac{\sum_{i = 1}^{n}(u_i - \overline{u})(v_i - \overline{v})}{\sqrt{\sum_{i = 1}^{n}(u_i - \overline{u})^2}\sqrt{\sum_{i = 1}^{n}(v_i - \overline{v})^2}}
$$

where  $\overline{u},\overline{v}$  denotes the mean value of elements in  $Mat_1^R$  and  $Mat_2^R$  , and  $n = \binom{m*b}{2} - m*b$  is the size of the observation

sets.

sets.To quantify the model dissimilarity based on the pairwise output differences sources from the idea of the second- order isomorphism (dissimilarity of dissimilarity matrices) [33]. This design does not require a straightforward one- to- one correspondence between the model instances (the first- order isomorphism), such as one- to- one parameter comparison. Therefore, it enables the generalization of our approach to quantify the dissimilarity of client models with different architectures or different representation spaces (e.g., with varying dimensionality), when uniquely designed models on local clients, different from the central model structure, are used [31].

# 4.4 Iterative Backdoor Detection

4.4 Iterative Backdoor DetectionThe dissimilarity quantification among local models forms a distance matrix among the clients, which provides the foundation to backdoor detection and mitigation. In this work, we detect malicious clients by calculating the local outlier factor (LOF) [34] of each client instance. In the following, we first present the definitions of  $k$ - distance and related notions, as well as the assumptions on the local clients and the central server. Then we present our iterative backdoor detection algorithm and demonstrate its correctness by deriving the main theorems.

Definition 1  $k$  - distance). Given a positive integer  $k$  and a set of objects  $\mathcal{D},$  the  $k$  - distance of an object  $p,$  denoted as  $k - dist(p),$  is defined as the distance  $d(p,q)$  between  $p$  and an object  $q\in \mathcal{D}$  such that: (1) there are at least  $k$  objects  $q^{\prime}\in \mathcal{D}\backslash p$  satisfying that  $d(p,q^{\prime})\leq d(p,q),$  and (2) there are at most  $k - 1$  objects  $q^{\prime}\in \mathcal{D}\backslash p$  satisfying that  $d(p,q^{\prime})< d(p,q)$

Definition 2  $k$  - distance neighborhood). Given  $k$  - distance of  $p,$  the  $k$  - distance neighborhood of  $p$  is  $N_{k}(p) = \{q\in$ $\mathcal{D}\setminus p\mid d(p,q)\leq k\text{- } dist(p)\}$  . The elements in  $N_{k}(p)$  are also called the  $k$  - nearest neighbors of  $p$

We make the following assumptions on the local clients where  $K$  indicates the number of malicious models.

Assumption 1. Among all client models to be aggregated, benign clients take the majority, i.e.,  $\begin{array}{r}N - K > \left\lceil \frac{N}{2}\right\rceil \end{array}$

Assumption 2. The server maintains a set of clean test data  $T_{\cdot}$  which allows us to build the sampled subset  $S\subseteq T$

We also observe that benign models tend to form a dense neighborhood, while malicious models are more sparsely distributed with varying distances from the benign group. Based on Assumption 1, we have

Benign models, which are trained on the original datasets without poisoning, are largely in each other's  $k$  - distance neighborhood, when the number of nearest neighbors  $k$  satisfies  $\begin{array}{r}k\leq \left\lfloor \frac{N}{2}\right\rfloor \end{array}$  Malicious models, which are trained based on the poisoned datasets, include at least one benign model in their  $k$  - distance neighborhood, when the number of nearest neighbors  $k$  satisfies  $\begin{array}{r}K\leq k\leq \left\lfloor \frac{N}{2}\right\rfloor \end{array}$

To effectively detect all malicious clients, the most challenging ones would be those with smaller distance to the benign group. Suppose  $c_{1}^{\prime}$  is a malicious client that is near to the benign group  $B,$  and a sufficient size of benign clients lie within a (preset) minimal distance  $d_{min}$  from  $c_1^\prime$  , i.e., the cardinality of the set  $\left|\{c_j\in \mathcal{B}|d(c_1',c_j)\leq d_{min}\} \right|$  with respect to the total number could achieve a minimum percentage. Building a distance- based backdoor detection framework will make it hard to detect such malicious clients. In contrast, density- based backdoor detection presents a solution to address such challenging conditions by investigating the relative density with respect to their local neighborhoods, which is detailed in the following.

Specifically, to detect backdoor attacks, we use a densitybased metric, local outlier factor, to investigate the outlier degree of the clients. For any client  $c_{i},$  we first present the definition of local reachability density.

Definition 3 (local reachability density). The local reachability density of client  $c_{i}$  is defined as

$$
lr d_{k}(c_{i}) = 1 / (\frac{\sum_{c_{j}\in N_{k}(c_{i})}r e a c h_{k}(c_{i},c_{j})}{|N_{k}(c_{i})|})
$$

where  $r e a c h_{k}(c_{i},c_{j}) = m a x\{k - d i s t(c_{j}),d(c_{i},c_{j})\}$  denotes the reachability distance of  $c_{i}$  to  $c_{j}$  with resepct to  $k$

The adoption of reachability distance has a smoothing effect for clients within the same neighborhood. For example, for any client  $c_{i}\in N_{k}(c_{j}),$  the reachability distance  $r e a c h_{k}(c_{i},c_{j})$  is replaced by the  $k$  - distance of  $c_{j},$  i.e.,  $k - dist(c_j),$  which reduces the statistical fluctuations brought by  $d(c_{i},c_{j})$  . Indeed, for any clients  $c_{i_1},c_{i_2}\in N_k(c_j),$  we have  $r e a c h_{k}(c_{i_{1}},c_{j}) = r e a c h_{k}(c_{i_{2}},c_{j}) = k - d i s t(c_{j})$  .According to Definition 3, the local reachability density of a client  $c_{i}$  is defined by the inverse of the average reachability distance to its  $k$  - nearest neighbors. It follows that the local density of the clients in the same neighborhood would also be similar to each other, which is formalized in Lemma 1.

Lemma 1. For any two benign clients  $c_{i_1},c_{i_2}$  in a dense neighborhood, i.e.,  $c_{i_1}\in N_k(c_{i_2}),c_{i_2}\in$ $N_{k}(c_{i_{1}}),$  and  $\begin{array}{r}\max \{k - dist(c_j)|c_j\in N_k(c_{i_1})\cup N_k(c_{i_2})\} - \end{array}$ $\begin{array}{r}\min \{k - dist(c_j)|c_j\in N_k(c_{i_1})\cup N_k(c_{i_2})\} \leq \epsilon , \end{array}$  we have  $\begin{array}{r}{|lr d_k(c_{i_1}) - lr d_k(c_{i_2})|\leq \frac{\epsilon}{\mathrm{const}}} \end{array}$  where const denotes a constant number.

In contrast, malicious clients sparsely scatter around with varying distance to the benign group. Based on Assumption 1, we can derive that the local reachability density of the malicious client is smaller than the local density of its benign neighbors.

Lemma 2. For any malicious client  $c_{i}$  with benign clients  $N_{k}^{\mathbb{B}}(c_{i})$  in its  $k$  - distance neighborhood, we have  $lr d_{k}(c_{i})<$ $lr d_{k}(c_{i}^{\mathsf{B}})$  for  $c_{i}^{\mathsf{B}}\in N_{k}^{\mathsf{B}}(c_{i})$

Now we present the definition of local outlier factor. It is defined as the relative local density to the nearest neighbors.

Definition 4 (local outlier factor). The local outlier factor of client  $c_{i}$  is defined as

$$
LOF_{k}(c_{i}) = \frac{1}{|N_{k}(c_{i})|}\sum_{c_{j}\in N_{k}(c_{i})}\frac{lr d_{k}(c_{j})}{lr d_{k}(c_{i})}
$$

By Lemma 1, we know that benign clients in a dense  $k$ - distance neighborhood have similar  $ldr$ , where the variation is mainly caused by the client difference in  $k$ - dist. Therefore, when the maximum and minimum  $k$ - dist among benign clients are sufficiently similar, according to Definition 4, the local outlier factor of benign clients in a dense neighborhood are approximately 1. We formalize the above property in Theorem 1.

Theorem 1. Let  $B$  be a dense cluster of benign clients. Let reachmax denote the maximum reachability distance between clients in  $B,$  i.e., reachmax  $=$  max{reachk(cicj)|ci, cje B}. Let reachmin denote the minimum reachability distance between clients in  $B,$  i.e., reachmax  $=$  min{reachb(cicj)|ci, ci e B}. For each client  $c\in B,$  its local outlier factor holds that

$$
\frac{1}{1 + \epsilon}\leq LOF(c)\leq 1 + \epsilon
$$

where  $\epsilon = \frac{\mathrm{reach}_{max}}{\mathrm{reach}_{min}} - 1$

According to Theorem 1, when  $\epsilon$  is a small value, i.e., reachmax and reachmin of the benign group are sufficiently close, then the LOFs of benign clients are approximately 1. The LOF bounds can be generalized to all clients, which is formalized in Theorem 2.

Theorem 2. Given any client  $c,$  let reachmax and reachmin denote the maximum and minimum reachability distance between c and its k- nearest neighbors. Let reachmax and reachmin denote the maximum and minimum reachability distance between neighbors of c and their k- nearest neighbors, reachmax  $=$ $\max \{r e a c h_{k}(c^{\prime},c_{j})|c^{\prime}\in N_{k}(c),c_{j}\in N_{k}(c^{\prime})\} ,$  reachmin  $=$ $\min \{r e a c h_{k}(c^{\prime},c_{j})|c^{\prime}\in N_{k}(c),c_{j}\in N_{k}(c^{\prime})\}$  . Then it holds that

$$
\frac{\mathrm{reach}_{min}^c}{\mathrm{reach}_{max}^N}\leq LOF_k(c)\leq \frac{\mathrm{reach}_{max}^c}{\mathrm{reach}_{min}^N}
$$

The implication by Theorem 2 for benign clients is consistent with Theorem 1. For a benign client, the maximum and minimum reachability distance for itself and its neighbors are generally close. Therefore, the lower bound and upper bound of their LOF values are close to 1. In contrast, according to Lemma 2, for a malicious client, its benign neighbors will have greater local density and thus its local outlier factor will be greater than 1. More specifically, according to Theorem 2, the lower bound of the local outlier factor of a malicious client is the ratio between reachmin and reachmax. Therefore, malicious clients with larger ratio of reachability distance against that of their neighbors will be easier to detect.

Still, to detect malicious clients with a fixed threshold on LOF is tricky and hard to be effective in a general way. The outlierness of the hacked clients can be affected by many factors such as the manipulation degree of attackers and the heterogeneous data. We identify the following challenges for malicious client detection in the empirical evaluation: (I) non- uniform outlier degree among multiple malicious clients, and (II) threshold fluctuation due to data heterogeneity and varied attacker ratios.

Challenge (I): Due to the local data difference, malicious clients trained with varied poisoned samples could lead to non- uniformity in the backdoor embedding degree. As a result, one- round backdoor detection based on a fixed LOF threshold could not fully identify malicious clients, which also imposes a dilemma to the detection accuracy: the lower the threshold, the more false positives of benign clients, and the higher the threshold, the more false negatives of malicious clients. This problem is especially severe when the attacker ratio, i.e., the portion of malicious clients controlled by the adversary is higher (e.g.,  $30\%$ $40\%$  etc.). To address this problem, we propose an iterative LOF update algorithm to identify malicious clients in multiple detection rounds, where the more obscured malicious clients can be revealed and identified as their malicious allies are detected and removed through previous rounds. Through the iterative procedure, we can detect all malicious clients more accurately and stably. We present the detailed analysis of the iterative detection algorithm in the following.

We first present the following lemma, which is related to the lower bound of LOF values for malicious clients.

Lemma 3. The lower bound of the LOF value for a malicious client  $c_{j}$  that remains to be detected will increase during the iterative procedure.

$$
\mathcal{LB}(LOF^{t}(c_{j}))\leq \mathcal{LB}(LOF^{t + 1}(c_{j}))
$$

We now proceed to present Theorem 3.

Theorem 3. Given  $K$  malicious clients satisfying  $LOF_{k}(c_{1})\leq$ $LOF_{k}(c_{2})\dots \leq LOF_{k}(c_{K})$  and a preset LOF threshold  $\delta$  in between, i.e.,  $\exists 1< j< K,LOF_{k}(c_{1})\leq \dots \leq LOF_{k}(c_{j})\leq$ $\sigma \leq LOF_{k}(c_{j + 1})\leq \dots \leq LOF_{k}(c_{K})$  , malicious clients with smaller LOF values can be detected through increased LOF during the iterative procedure, with the removal of malicious neighbors.

The iterative algorithm leads to the following conditions that make the detection of the remaining malicious clients become true. During the iteration,

Larger LOFs of benign models will become closer to 1: along with the removal of malicious models, the number of clients to be determined is reduced from  $N$  to  $N - K + j$  . As the parameter  $k$  is determined as  $\left\lfloor \frac{\#C_{2}^{2}}{2}\right\rfloor$  the LOFs of benign models are calculated with regard to a much smaller and denser  $k$  distance neighborhood, where the fluctuation of the reachability distance will be much smaller, i.e.,  $\epsilon = \frac{\mathrm{reach}_{max}}{\mathrm{reach}_{min}} - 1$  is more close to 0. By Theorem 1, the original larger LOF values of some benign models will be more close to 1.

Smaller LOFs of malicious models will become larger: according to Lemma 3, the lower bounds of LOF for the remaining malicious models will increase with the iteration. Besides, along with the removal of malicious models, more benign clients will step in and serve as the nearest neighbors of the remaining malicious models, which results in the following conditions: (1) according to Definition 3, with increasing reachability distance to more benign neighbors, the  $ldr$  of the malicious model would become smaller. (2) the added benign neighbors would be much deeper inside dense benign group, whose reachability distance are much smaller, which leads to greater  $ldr$  values. Therefore, according to Defi

Algorithm 2: Malicious Client Detection based on  

<table><tr><td colspan="2">Algorithm 2: Malicious Client Detection based on</td></tr><tr><td colspan="2">Refined Threshold</td></tr><tr><td colspan="2">input: Clients C = {Ck, k = 1, Distance matrix distMat, Threshold δ, Distance bound εd</td></tr><tr><td colspan="2">output: Dec: the decision vector</td></tr><tr><td colspan="2">1 Dec, benign ← [0] * N, C&#x27;;</td></tr><tr><td colspan="2">2 malicious ← [];</td></tr><tr><td colspan="2">3 for Ck ∈ benign do</td></tr><tr><td colspan="2">4     score = CalLOF(distMat,Ck);   &amp;gt;calculate local outlier factor</td></tr><tr><td colspan="2">5     if score &amp;gt; δ then</td></tr><tr><td colspan="2">6     remove Ck from benign; append Ck to malicious;</td></tr><tr><td colspan="2">7     Dec[k] ← 1;   &amp;gt;update decision vector</td></tr><tr><td colspan="2">9 newDistMat ← [];</td></tr><tr><td colspan="2">10 for Ci, Cj ∈ benign do</td></tr><tr><td colspan="2">11     newDistMat[Ci, Cj] ← distMat[Ci, Cj]</td></tr><tr><td colspan="2">12 distMat ← newDistMat;   &amp;gt;update distance matrix</td></tr><tr><td colspan="2">13 clientDist, clientNum ← [], len(benign);</td></tr><tr><td colspan="2">14 for Ci ∈ benign do</td></tr><tr><td colspan="2">15     clientDist[Ci] ← sum(distMat[Ci]) / (clientNum - 1)</td></tr><tr><td colspan="2">16 δd ← sum(clientDist) / clientNum;   &amp;gt;compute dynamic distance threshold</td></tr><tr><td colspan="2">17 cands ← [];</td></tr><tr><td colspan="2">18 for Ci ∈ benign do</td></tr><tr><td colspan="2">19     if clientDist[Ci] &amp;gt; δd then</td></tr><tr><td colspan="2">20     append Ci to cands;   &amp;gt;identify malicious candidates</td></tr><tr><td colspan="2">21 candDist ← sum(clientDist[cands]) / len(cands); &amp;gt;compute the average distance of candidates</td></tr><tr><td colspan="2">22 if candDist &amp;gt; εd then</td></tr><tr><td colspan="2">23     δre ← sum(score[cands]) / len(cands);   &amp;gt;compute the refined threshold</td></tr><tr><td colspan="2">24     repeat</td></tr><tr><td colspan="2">25     for Ck ∈ benign do</td></tr><tr><td colspan="2">26     score = CalLOF(distMat,Ck);   &amp;gt;calculate local outlier factor</td></tr><tr><td colspan="2">27     if score &amp;gt; δre then</td></tr><tr><td colspan="2">28     remove Ck from benign; append Ck to malicious;</td></tr><tr><td colspan="2">29     Dec[k] ← 1;   &amp;gt;update decision vector</td></tr><tr><td colspan="2">30     until malicious is empty;</td></tr><tr><td colspan="2">31 return Dec</td></tr></table>

nition 4, the LOF values of the remaining malicious models will become larger.

The iterative backdoor detection and exclusion procedure is described in Algorithm 1 (Lines 11- 25). In each iteration, to identify the malicious clients, we calculate the LOF (i.e., outlier degree) of the remaining clients which are still under the determination of malicious or not (Line 15). Each client is then examined against a reference threshold. A client is declared as malicious if its LOF value is higher than the threshold and further removed from the undetermined client set (Lines 17- 19). The distance matrix among clients is then updated by removing the identified malicious clients. In the next- round detection, the outlier score is updated along with the change of neighbors for each remaining client. The iteration stops when the LOF values of the remaining clients are all lower than the threshold.

Challenge (II): When the training data of each local client is heterogeneous (i.e., non- independent and identically distributed) or the attacker ratio is higher with malicious allies, the iterative algorithm design with a fixed reference threshold is not effective enough to fully detect malicious clients. In this situation, the reachability distance between clients will increase and local density of benign clients will decrease. There are cases when the updated LOF of malicious clients through the iterative detection procedure still fail to attain the detection threshold.

Inspired by [16] which adopts a dynamic thresholding strategy by calculating the mean value of reconstruction errors, we leverage this strategy to identify potential malicious candidates and guide the threshold refinement. We first take the dynamic mean value of the clients' average distance to the others. However, unlike [16] directly determining the ones with distance greater than the mean value as malicious, we take the strategy as a guidance and refine the LOF threshold to the mean LOF of the identified malicious candidates. This is to update the LOF threshold to a lower value while avoiding false alarms.

Without loss of generality, assume that  $l$  candidates are identified by the dynamic thresholding strategy satisfying  $LOF(c_{1}) \geq LOF(c_{2}) \geq \dots \geq LOF(c_{l})$ , and the first  $i$ $(i < l)$  clients are malicious ones that need to be detected. The updated LOF threshold is

$$
\begin{array}{rl} & {\big(\sum_{1\leq j\leq i}LOF(c_j) + \sum_{i + 1\leq j\leq l}LOF(c_j)\big) / l}\\ & {= \big(\sum_{1\leq j\leq i}(1 + \delta_j) + \sum_{i + 1\leq j\leq l}(1 + \epsilon_j)\big) / l}\\ & {= (i + \sum_{1\leq j\leq i}\delta_j + (l - i) + \sum_{i + 1\leq j\leq l}\epsilon_j) / l}\\ & {= 1 + \big(\sum_{1\leq j\leq i}\delta_j + \sum_{i + 1\leq j\leq l}\epsilon_j\big) / l} \end{array}
$$

The difference between  $\delta_j$ $(1 \leq j \leq i)$  and  $\epsilon_j$ $(i + 1 \leq j \leq l)$  is usually big enough to update the threshold  $\delta_{re}$  to a value satisfying that  $\exists i_0$ ,  $LOF(c_1) \geq \dots \geq LOF(c_{i_0}) \geq \delta_{re} \geq LOF(c_{i_{0} + 1}) \geq \dots \geq LOF(c_i)$ . In this way, false alarms can be avoided and the detection threshold is updated to a refined value. With the removal of malicious clients with larger LOF values than the refined threshold  $\delta_{re}$ , LOF values of remaining malicious clients will increase. Once again, as explained in the iterative algorithm design, the remaining malicious clients can be detected through the iterative procedure.

The detailed procedure is shown in Algorithm 2. We first perform a one- round backdoor detection using a preset coarse (high) threshold (Lines 2- 8). In the next step, we identify malicious candidates from the remaining undetermined clients based on a dynamic threshold strategy (Lines 12- 20). In particular, we calculate the average distance of each remaining client to the other ones, and deem a client as a malicious candidate if its distance is larger than the (dynamic) mean value of all clients' average distance. Next, we refine the threshold as the mean of LOF values of the identified malicious candidates (Line 23), which is used as the threshold for the following iterative backdoor detection.

After the malicious candidate identification, we have an overview of the potential malicious clients still remaining to be detected, which further guides the threshold refinement. However, similar to the problem faced by detection approach based on average loss in [16], nearly half of undetermined clients could be flagged as false alarms in the cases when there are no attackers at all. Therefore, to

distinguish the case of false- alarm attackers, we introduce a distance parameter  $\epsilon_{d}$  as the dissimilarity lower bound for further threshold refinement and fully malicious client detection. If the average distance of the identified candidates is lower than  $\epsilon_{d}$ , then the detection procedure ends and the decision of malicious clients is returned. Note that in the cases when first- round detection using a pre- set reference threshold is effective enough to identify all malicious clients, the remaining clients reduce to the problem of false- alarm attackers. The distance bound mechanism could avoid further threshold refinement and iterative detection.

# 4.5 Backdoor Elimination

To realize runtime defense against backdoor attackers for the robustness of federated learning, we integrate the proposed backdoor detection approach as a runtime monitoring component into the learning procedure. In each learning round, we examine the local model updates that are sent back to the server for aggregation. We identify the malicious clients through our detection approach and further exclude them from the federated aggregation to eliminate their negative impact to the global model robustness.

Now we discuss potential adaptive attacks. Assume an attack scenario where the adversary has full knowledge about our detection approach. To avoid being detected by the proposed approach, the attackers need to reduce the poisoning rate or the ratio of malicious allies so as to deliver more similarity in model representation with benign clients. This further poses a dilemma for the attacker - if the adversary intends to maintain similar behaviors to benign clients, the backdoor injection will have negligible impact on the global model robustness, otherwise the model deviation will be detected and excluded by our approach.

# 4.6 Computational Complexity

The computational complexity of our algorithm is analyzed as follows. Assume the number of clients, malicious clients, and the data sample size are denoted as  $N$ ,  $K$ , and  $b$ , respectively. The algorithm begins with extracting the output vector on sampled data for each client. The complexity for output vector extraction is  $O(N \cdot b)$ . To build the RDM, we need to compute the cosine distance between any two output vectors for each client, which requires  $O(N \cdot b^2)$  time. The computational complexity of client dissimilarity quantification is  $O(N^2)$ . The final step is performing iterative backdoor detection by calculating the local outlier factor (LOF). Calculating the LOF for each client requires  $O(N)$  time. In each iteration, the LOF is calculated for each client, requiring  $O(N^2)$  time. At least one malicious client will be settled in each iteration. Hence, there are at most  $K$  iterations. Therefore, the iterative backdoor detection takes  $O(K \cdot N^2)$  time. Overall, the worst case computational complexity is  $O(N \cdot b^2 + K \cdot N^2)$ . Note that many steps of our algorithm can be computed in parallel, such as RDM computation. The output extraction can also be significantly accelerated by GPUs.

# 5 EXPERIMENTAL EVALUATION

In this section, we evaluate the effectiveness of our backdoor detection approach. We first introduce the experiment configuration in Section 5.1. Then we perform experimental evaluation to answer the following research questions:

RQ1: Can our approach provide effective backdoor detection for different types of data distribution, especially heterogeneous data, in federated learning?

RQ2: Can our approach effectively defend the global model against continuous backdoor attacks in runtime deployment?

RQ3: What is the performance of our approach under different backdoor patterns?

RQ4: What is the detection overhead when our approach is used as a runtime defense solution?

# 5.1 Experiment Configuration

# 5.1.1 Datasets and Model Architectures

We use five popular publicly available datasets, including three image datasets MNIST [35], Fashion- MNIST [36], CIFAR- 10 [37], and two text classification dataset AG- NEWS [38] and Yahoo Answers [39]. The description about the datasets is summarized in the following.

- MNIST contains 60,000 training data and 10,000 test data, categorized in 10 classes (handwritten digits from 0 to 9). Each MNIST image is of size  $28 \times 28 \times 1$ .- Fashion-MNIST consists of 60,000 training samples and 10,000 test samples. Each sample is a  $28 \times 28$  grayscale image, associated with a label from 10 classes.- CIFAR-10 is a collection of images for general-purpose image classification, including 50,000 training data and 10,000 test data in 10 object classes. Each CIFAR-10 image is of size  $32 \times 32 \times 3$  with three channels.- AG-NEWS is a news topic classification dataset of 4 classes, containing 30,000 training and 1,900 test samples for each class.- Yahoo Answers is a topic classification dataset constructed using 10 largest main categories. Each class contains 140,000 training samples and 6,000 testing samples.

For MNIST and CIFAR- 10 datasets, we use the same model architectures and parameter configurations as in [17]. We train a convolutional neural network with two convolution layers followed by two fully connected layers for MNIST. We use ResNet- 18 [40] to train the CIFAR- 10 dataset. For the Fashion- MNIST dataset, we adopt LeNet- 5 [35] for training and evaluation. Regarding the AG- NEWS dataset, we use a two- layer neural network for text classification, where the first layer is an embedding layer and the second layer is a fully connected layer [41]. For the Yahoo Answers dataset, we use the LSTM model architecture with 256 hidden neurons.

# 5.1.2 Implementations and Computing Infrastructure

The proposed approach and experiments are implemented based on PyTorch 1.9.0, torchvision 0.10.0 and torchtext 0.10.0. The experiments are conducted on a computer cluster with each cluster node running a GNU/Linux system with Linux kernel 3.10.0 on 2 12- core 2.3GHz Intel Xeon CPU Gold 5188 with 256 GB RAM equipped with 8 NVIDIA Titan XP.

# 5.1.3 Model Aggregation and Data Distribution

5.1.3 Model Aggregation and Data DistributionWe assume a 10- client FL setting and we train the global model through iterative aggregation on the local clients. In each round, the central server first broadcasts the current global model  $w_{t}$  to the local clients. Then each client trains the model using its local data and sends the updated model to the central server. Next, the central server takes a weighted average of the updated local models  $w_{t + 1} = \sum_{i = 1}^{K} \frac{n_{i}}{n} w_{t + 1}^{i}$  where  $w_{t + 1}^{i}$  indicates the  $i$ - th local model parameters,  $n_{i}$  is the number of local data on client  $i$  and  $n$  is the number of all training data on  $K$  clients. We partition the datasets among the local clients in both IID (independent and identically distributed) and Non- IID settings. In the IID setting, we divide the training data into 10 shards following the uniform distribution and assign each client a shard. In the Non- IID setting, we adopt the Dirichlet distribution [42] with the hyperparameter 0.9 to divide the training data following [9].

# 5.1.4 Backdoor Attack Setting

5.1.4 Backdoor Attack SettingWe summarize the settings of exploited backdoor, attack rate, backdoor label, and training epoch number. We exploit the pixel- pattern backdoor (square shape) in [27] for the image classification tasks. For the text classification tasks, we use the backdoor text, "Hoo" as in [43]. The attack rate indicates the ratio of the poisoned data with regard to all local training data. We poison the training data with attack rate 0.2 for image classification tasks and 0.05 for text classification. Without loss of generality, for all datasets, we use "1" as the target backdoor label. For image classification tasks, we follow the local epoch configuration in [17] where each honest client trains the local model for 5 epochs and attackers train the local models with 5 more epochs to enhance the attacks. For the text classification tasks, all local clients train their models for 1 epoch in each round.

# 5.1.5 Approach Configuration

5.1.5 Approach ConfigurationTo calculate the RDM, we randomly select 100 test data from each class of the dataset, which comprises a total of  $100 \times m$  input stimuli ( $m$  indicates the class number). Regarding the LOF calculation with  $k$ - heaviest neighbors, we set  $k = \left\lfloor \frac{l}{2} \right\rfloor$ , where  $l$  is the number of the clients under determination. The LOF threshold of our approach is typically set to 1.5 from the experimental experience, which is effective for different classification tasks. Our approach also integrates a threshold refinement mechanism. The threshold can be further adjusted for difference cases, especially for cases when there are multiple malicious clients involved in backdoor injection. We set the distance bound empirically to the maximum value of average distance among honest clients, which can be obtained in the early training rounds. Typically, attackers will not embed the backdoor in these early training rounds as the malicious update can be nullified by the averaging. When the global model is about to converge, the adversary can embed the backdoor by poisoning training data and scaling up the malicious updates to realize its objective. We will show in the following experiments that the configuration is feasible and effective to defend FL.

# 5.1.6 Baseline Approaches and Configuration

We perform comparison experiments with the following approaches, including spectral anomaly detection (Spectral) [16], BaFFLe [19], residual- based reweighting aggregation algorithm (IRLS) [17], and defense method based on robust learning rate (RLR) [27].

Spectral and BaFFLe are backdoor detection approaches. Similar to our approach, Spectral achieves defense by removing the identified malicious clients from aggregation, whereas BaFFLe determines whether the aggregated global model is attacked or not and achieves defense by dropping the current updated global model and returning to the last accepted global model. IRLS and RLR are backdoor defense approaches. IRLS achieves defense through a reweighted scheme which reduces the weight of local clients demonstrating large model parameter deviation, while RLR achieves defense by reversing the model parameter sign when the number of support clients for the current sign is lower than a learning threshold.

The configuration of the baseline approaches is summarized as follows. For Spectral, the spectral anomaly detection model is trained following the parameter configuration in [16]. The test data of the five datasets are used to generate the model updates for training the corresponding anomaly detection model. VAE (Variational AutoEncoder) is used as the anomaly detection model. The encoder and decoder have two dense hidden layers where the dimension of the dense layers is 500 for the image classification tasks, 100 for the AG- NEWS task and 500 for the Yahoo Answers task. The dimension of the latent vector is 100 for all the datasets. The hyper- parameters of BaFFLe are set to the recommended value in [19] where the quorum threshold  $q$  is set to 5. The lookback window size  $l$  is set to 10 for attack round setting of 10 and set to 20 for attack round settings  $20 / 30$  in the detection experiments. In the defense experiments, the lookback window size is set to 10. The hyper- parameters of the IRLS algorithm,  $\lambda$  and  $\delta$ , are set to default values as in [17].  $\lambda$  controls the confidence interval and it is set to 2.0.  $\delta$  controls the clipping threshold and it is set to 0.1. The hyperparameter of RLR, i.e., the sign change threshold  $\theta$ , is set to 4.

# 5.2 RQ1: Can our approach provide effective backdoor detection for different types of data distribution?

To evaluate the detection performance of our approach for different types of data distribution, we consider 15 experiment settings w.r.t. attacker ratio (the percentage of malicious clients) and attack round for both IID and NonIID data distribution types, in the scenario of model replacement (i.e., poison in one round) over the benchmark datasets.

Specifically, we train the global models with the attacker ratio varying from  $0\%$  to  $40\%$  step length as  $10\%$  and attack round from 10 to 30 (step length as 10) using IID and Non- IID data, respectively. Under a specific setting, we investigate whether our approach could accurately identify all malicious clients in terms of three evaluation metrics: False Positive Rate (FPR), False Negative Rate (FNR), and the F1 Score. We then report the average results of the 15 settings w.r.t. attacker ratio and attack round for IID and

TABLE 1: Detection results in terms of FPR/FNR/F1-Score between Spectral and our approach  

<table><tr><td colspan="2">Dataset</td><td colspan="3">MNIST</td><td colspan="3">CIFAR</td><td colspan="3">FASHION</td><td colspan="3">AG-NEWS</td><td colspan="3">YAHOO</td></tr><tr><td colspan="2">Metric</td><td>FPR</td><td>FNR</td><td>F1</td><td>FPR</td><td>FNR</td><td>F1</td><td>FPR</td><td>FNR</td><td>F1</td><td>FPR</td><td>FNR</td><td>F1</td><td>FPR</td><td>FNR</td><td>F1</td></tr><tr><td rowspan="2">IID</td><td rowspan="2">Spectral Ours</td><td>0.07</td><td>0</td><td>0.8</td><td>0.17</td><td>0</td><td>0.65</td><td>0.19</td><td>0</td><td>0.64</td><td>0.63</td><td>0.75</td><td>0.15</td><td>0.1</td><td>0</td><td>0.9</td></tr><tr><td>0.01</td><td>0</td><td>0.98</td><td>0.04</td><td>0</td><td>0.95</td><td>0.01</td><td>0</td><td>0.99</td><td>0.01</td><td>0</td><td>0.99</td><td>0</td><td>0</td><td>1</td></tr><tr><td rowspan="2">Non-IID</td><td rowspan="2">Spectral Ours</td><td>0.08</td><td>0</td><td>0.78</td><td>0.17</td><td>0.12</td><td>0.61</td><td>0.18</td><td>0</td><td>0.66</td><td>0.47</td><td>0.48</td><td>0.24</td><td>0.35</td><td>0.15</td><td>0.55</td></tr><tr><td>0.02</td><td>0</td><td>0.92</td><td>0.03</td><td>0.03</td><td>0.96</td><td>0.03</td><td>0</td><td>0.97</td><td>0</td><td>0</td><td>1</td><td>0</td><td>0</td><td>1</td></tr></table>

![](images/537e436d3b970c575986a694607d14ad22ed8287e70807da7f5594fd34131e4f.jpg)  
Fig. 3: Comparison results with BaFFLe for IID data.

![](images/7637303d3d92bc0b0938a853e4715cf5a12b38f9c7f275f05c19ae8762ce6b47.jpg)  
Fig. 4: Comparison results with BaFFLe for Non-IID data.

Non- IID data distribution, respectively, as shown in Table 1. Detailed evaluation results for each specific configuration setting are summarized in the appendix.

As comparison, we also evaluate the performance of the other two detection methods, Spectral and BaFFLe. The evaluation of Spectral is the same as our approach. However, note that in each setting, BaFFLe is only able to identify suspicious global models instead of the individual clients, thus only presents a detection result about whether the global update is malicious or not (1 or 0). Therefore, to obtain fair comparison, we transform the detection results of our approach to results at the global level. For example, it is flagged as TP (True Positive) when BaFFLe and our approach identify a malicious global update, i.e., there exists at least one malicious client for global model aggregation.

Table 1 shows the comparison results of the detection performance between Spectral and our approach over the benchmark datasets for both IID and Non- IID data settings.

Our approach outperforms the Spectral method across different benchmark tasks. Specifically, the proposed approach identifies malicious clients more accurately and stably with 0.014 FPR, 0 FNR, and 0.982 F1- score for IID data distribution and 0.016 FPR, 0.006 FNR, and 0.97 F1- score for Non- IID data distribution on average across all benchmark tasks. Compared with Spectral, our approach achieves an average improvement of  $35.4\%$  and  $40.2\%$  on detection accuracy in terms of F1- score for IID and Non- IID data. With regard to FPR and FNR, our approach realizes an average reduction of  $21.8\% /23.4\%$  in FPR and  $15\% /14.4\%$  reduction in FNR than the Spectral method for IID and Non- IID data, respectively.

We observe that, the FNR of Spectral detection increases to  $12\%$  for CIFAR- 10 and  $15\%$  for Yahoo Answers when dealing with Non- IID training data. We hypothesize that the issue primarily stems from the model update representation problem in Spectral when dealing with complex model architectures, and the limitations in handling challenging scenarios where Non- IID data results in significant parameter updates. Besides, since the Spectral adopts a dynamic average threshold, it will identify clients with reconstruction errors larger than the average one as anomalies in a "hard" binary manner, which leads to the high rate of false- positive alarms,  $23.2\% /25\%$  on average for IID/Non- IID.

Figures 3 and 4 show the comparison results of detection accuracy in term of F1- score between BaFFLe and our approach. As stated before, the detection of BaFFLe is limited to identifying the global model as malicious or not, i.e., binary answer (1 or 0), instead of identifying the specific (and the number of) malicious clients. We then calculate the F1- score across 15 settings for IID and Non- IID data. Overall, the results show that our approach presents better detection accuracy on the benchmark tasks and achieves an average improvement of  $15\%$  and  $20\%$  in F1- score across the benchmark for IID and Non- IID data, respectively.

In general, BaFFLe demonstrates unstable performance across the benchmark tasks. It presents better detection accuracy in the text classification tasks. This is because BaFFLe relies on the prediction error variation for backdoor detection, making it fully constrained when the prediction accuracy over validation data is well- preserved by the attackers (e.g., the backdoor case of image classification tasks). The adopted backdoor attack for the text classification task would cause a more obvious change to the prediction error on the validation data, which leads to better detection performance for AG- NEWS and Yahoo Answers datasets. In addition, BaFFLe eliminates the backdoor injection by rejecting global updates instead of aggregating over benign clients, which will lead to extra learning rounds and computing resource consumption.

TABLE 2: Defense results in terms of accuracy (Acc.) and attack success rate (A.S.R.) of different defense mechanisms  

<table><tr><td colspan="2">Dataset</td><td colspan="2">MNIST</td><td colspan="2">CIFAR</td><td colspan="2">FASHION</td><td colspan="2">AG-NEWS</td><td colspan="2">YAHOO</td></tr><tr><td>Methods</td><td>Metric (%)</td><td>IID</td><td>Non-IID</td><td>IID</td><td>Non-IID</td><td>IID</td><td>Non-IID</td><td>IID</td><td>Non-IID</td><td>IID</td><td>Non-IID</td></tr><tr><td rowspan="2">Average</td><td rowspan="2">Acc. A.S.R.</td><td>97.6</td><td>97.2</td><td>69.2</td><td>65.2</td><td>88.0</td><td>85.9</td><td>55.5</td><td>42.8</td><td>17.4</td><td>33.6</td></tr><tr><td>78.2</td><td>59.0</td><td>99.8</td><td>98.8</td><td>71.6</td><td>51.4</td><td>64.7</td><td>85.1</td><td>94.1</td><td>58.9</td></tr><tr><td rowspan="2">Spectral</td><td rowspan="2">Acc. A.S.R.</td><td>98.1</td><td>97.6</td><td>68.3</td><td>64.1</td><td>85.9</td><td>83.4</td><td>40.9</td><td>44.0</td><td>62.2</td><td>20.5</td></tr><tr><td>0.1</td><td>0.3</td><td>3.1</td><td>56.4</td><td>0.2</td><td>0.1</td><td>88.4</td><td>83.2</td><td>5.2</td><td>83.5</td></tr><tr><td rowspan="2">BaFFLe</td><td rowspan="2">Acc. A.S.R.</td><td>97.5</td><td>97.0</td><td>68.9</td><td>67.1</td><td>86.2</td><td>85.2</td><td>57.3</td><td>49.7</td><td>59.6</td><td>58.6</td></tr><tr><td>61.8</td><td>33.2</td><td>98.2</td><td>89.2</td><td>53.8</td><td>28.0</td><td>60.9</td><td>74.6</td><td>4.2</td><td>2.7</td></tr><tr><td rowspan="2">IRLS</td><td rowspan="2">Acc. A.S.R.</td><td>97.8</td><td>97.2</td><td>68.9</td><td>64.1</td><td>86.8</td><td>85.8</td><td>80.8</td><td>78.9</td><td>13.7</td><td>13.0</td></tr><tr><td>47.2</td><td>55.5</td><td>98.1</td><td>98.6</td><td>35.7</td><td>51.3</td><td>8.4</td><td>6.1</td><td>95.8</td><td>97.4</td></tr><tr><td rowspan="2">RLR</td><td rowspan="2">Acc. A.S.R.</td><td>97.6</td><td>96.3</td><td>-</td><td>-</td><td>85.9</td><td>84.4</td><td>55.2</td><td>43.4</td><td>56.3</td><td>50.2</td></tr><tr><td>4.2</td><td>1.0</td><td>-</td><td>-</td><td>0.8</td><td>4.0</td><td>65.0</td><td>84.7</td><td>18.0</td><td>27.2</td></tr><tr><td rowspan="2">Ours</td><td rowspan="2">Acc. A.S.R.</td><td>98.1</td><td>97.5</td><td>68.2</td><td>63.1</td><td>86.9</td><td>85.9</td><td>79.9</td><td>74.3</td><td>61.3</td><td>55.7</td></tr><tr><td>0.2</td><td>2.3</td><td>2.6</td><td>1.0</td><td>0.2</td><td>0.2</td><td>4.9</td><td>1.6</td><td>2.2</td><td>2.2</td></tr></table>

Answer to RQ1: Our approach can effectively detect malicious clients for both IID and Non- IID data, demonstrating better detection accuracy than the baseline detection approaches.

# 5.3 RQ2: Can our approach effectively defend the global model against continuous backdoor attacks in runtime deployment?

To assess the effectiveness of our approach when deployed as a runtime defense mechanism, we conduct comparison experiments with the other defense methods, federated averaging (Average),Spectral, BaFFLe,IRLS,and RLR in the attack scenario of naive approach (i.e., attack in continuous rounds).

We evaluate the defense performance of our approach and the baseline methods under different attacker ratios varying from  $10\%$  to  $40\%$  (step length as  $10\%$  . For image datasets, we perform 20 training rounds and start the attack at round 10. For text classification datasets, we start the attack at round 18 and 20 for AG- NEWS and Yahoo Answers tasks, respectively, as the backdoor embedding is much easier in this case. To accommodate the BaFFLe method, we enable the defense after the first 10 rounds to build a decent size of history global models as required by BaFFLe. The evaluation metrics are attack success rate (A.S.R.) concerning backdoor elimination and prediction accuracy (Acc.) on test data, so as to reflect the defense effectiveness against backdoor attacks as well as the prediction performance preservation on normal data.

The complete defense results of different approaches are presented in Fig. 5 and Fig. 6. The ideal result is to eliminate the backdoor injection which is reflected by low attack success rate. We can see that our approach achieves effective and stable defense performance across the benchmark tasks for both IID and Non- IID data, outperforming the baseline methods or achieving comparable state- of- the- art results.

Table 2 summarizes the concrete comparison results in terms of prediction accuracy and attack success rate of different defense mechanisms across four benchmark tasks when the attacker ratio is  $40\%$  . Overall, our approach achieves less than  $2.6\%$  attack success rate on image classification tasks and less than  $4.9\%$  on text classification tasks.

More specifically, compared with the federated averaging algorithm (Average), our approach realizes an average reduction of  $79.7\%$  and  $69.2\%$  in attack success rate for IID and Non- IID data across the benchmark datasets.

Compared with the state- of- the- art robust aggregator IRLS, our approach achieves a reduction of  $55.0\%$  and  $60.3\%$  on attack success rate for IID and Non- IID data. Another state- of- the- art approach RLR achieves defense by adjusting the update direction (i.e., signs) of model parameters, which achieves effectiveness for image classification tasks trained with convolutional network architectures. However, it fails to converge when using ResNet models for image classification (CIFAR) and its effectiveness is compromised when dealing with natural language classification tasks where embedding layers are used in the model architectures, with  $65.0\% /84.7\%$  A.S.R.for AG- NEWS and  $18.6\% /27.2\%$  A.S.R. for Yahoo Answers in IID and Non- IID settings, respectively.

BaFFLe reduces the attack success rate to a certain degree with an average reduction of  $25.9\%$  and  $25.1\%$  for IID and Non- IID data. Still, the adversary attains more than  $45\%$  attack success rate across the benchmark with BaFFLe as defense. Spectral achieves less than  $0.3\%$  attack success rate for MNIST and Fashion- MNIST. However, its defense ability drops sharply  $(56.4\%)$  A.S.R.for CIFAR- 10) when the model architecture becomes more complicated with heterogeneous data (Non- IID). In addition, Spectral fails to defend models processing sequential inputs where the attack success rate reaches an average of  $85.8\%$  for AG- NEWS and  $44.4\%$  for Yahoo Answers. IRLS shows its effectiveness in defending the text classification model for the AG- NEWS task, achieving  $56.3\%$ $79.0\%$  (IID / Non- IID) reduction in A.S.R. Still, IRLS fails to achieve effective defense for the LSTM model in the Yahoo Answers task, especially with a higher attacker ratio, laying up backdoor vulnerabilities.

Moreover, our approach preserves the prediction accuracy of the global models for all tasks. The achieved test accuracy are  $97.8\%$ $65.7\%$ $86.4\%$ $77.1\%$  and  $58.5\%$  for MNIST, CIFAR- 10, Fashion- MNIST, AG- NEWS and Yahoo Answers datasets on average across different data distributions. We can see that the achieved accuracy is about the same as the Average algorithm with  $97.4\%$ $67.2\%$  and  $87.0\%$  test accuracy for the image classification datasets. As the backdoor used for text classification tasks along with

![](images/ccf5da10da06451c42cd0db4fe2a728e31cad104b10ec66ca4b9fb4095ba15f1.jpg)  
Fig. 5: Defense results in terms of attack success rate on the benchmark datasets (IID).

![](images/ede184531740dd929f61988bf01fbe31ac77a37dd2792b3bd329330d2d0a80e0.jpg)  
Fig. 6: Defense results in terms of attack success rate on the benchmark datasets (Non-IID).

![](images/6e7752c04006fb208a9077cd15a3e4fd7e2fdc35850eac64e36800027d75ad20.jpg)  
Fig. 7: Attack success rate attained by defense mechanisms across attacker ratios and training rounds.

a high attacker ratio causes more negative impacts on test accuracy, our approach achieves about  $28.0\%$  and  $33.0\%$  improvement in test accuracy for AG- NEWS and Yahoo Answers, respectively, than the Average method.

Fig. 7a shows the attack success rate attained by different defense approaches under attacker ratios from  $10\%$  to  $40\%$  for CIFAR- 10 in the Non- IID setting. The comparison results demonstrate the advantage of our approach in both defense effectiveness and stability across all attacker ratios. Specifically, our approach achieves stable and effective backdoor elimination, reducing the attack success rate to  $1.8\%$  on average across all attacker ratios. Spectral achieves comparable attack success rate reduction when the attacker ratio is no more than  $20\%$ . However, it loses the defense stability when the attacker ratio increases ( $54.6\%$  A.S.R. for attacker ratio  $30\%$  and  $56.4\%$  A.S.R. for  $40\%$ ). The other defense methods demonstrate effectiveness when the attacker ratio is  $10\%$  but do not have sufficient stability in backdoor elimination as the attacker ratio increases.

Fig. 7b shows the traces of attack success rate across the training rounds with different defense mechanisms under  $40\%$  attacker ratio. We can see that our approach is the only one to succeed in defending the continuous backdoor attacks. Compared with the Average algorithm, the other baseline methods are able to slow down the increase of attack success rate to some level, but still all reach high attack success rates higher than  $50\%$  when being attacked continuously.

Answer to RQ2: Our approach can effectively and stably defend global models against continuous attacks, demonstrating better backdoor elimination than the baseline approaches.

# 5.4 RQ3: What is the performance of our approach under different backdoor patterns?

To answer RQ3, we evaluate our approach against two practical backdoor patterns: Copyright and Apple logos from [27]. These logos are gray- scale images with size of  $224 \times 244$  and  $200 \times 200$ , respectively, which are scaled appropriately for use as embedded backdoors. As with the Square pattern, we investigate the effectiveness of our method against these practical backdoors, comparing it to other detection and defense techniques discussed in previous sections.

Table 3 summarizes the defense results for the Copyright and Apple backdoor patterns. The overall performance is consistent with what we observed for the previous backdoor pattern (Table 2). Our method achieves the lowest attack success rate across all tasks, with an average reduction of  $52.3\%$  compared to the Average algorithm, while preserving the prediction accuracy. The Spectral method shows comparable performance to ours. The other three baseline methods also reduce the attack success rate to some extent; however, for certain tasks (e.g., FASHION (Apple)), the attack success rate remains as high as  $48.1\%$ ,  $65.4\%$ , and  $6.7\%$ .

TABLE 3: Defense results of accuracy (Acc.) and attack success rate (A.S.R.) of different defense mechanisms for logo backdoor patterns  

<table><tr><td colspan="2">Tasks</td><td colspan="2">MNIST (Copyright)</td><td colspan="2">MNIST (Apple)</td><td colspan="2">FASHION (Copyright)</td><td colspan="2">FASHION (Apple)</td></tr><tr><td>Methods</td><td>Metric (%)</td><td>IID</td><td>Non-IID</td><td>IID</td><td>Non-IID</td><td>IID</td><td>Non-IID</td><td>IID</td><td>Non-IID</td></tr><tr><td rowspan="2">Average</td><td>Acc.</td><td>97.8</td><td>97.4</td><td>98.0</td><td>97.3</td><td>86.8</td><td>86.4</td><td>86.8</td><td>86.7</td></tr><tr><td>A.S.R.</td><td>60.2</td><td>17.0</td><td>83.8</td><td>36.3</td><td>52.9</td><td>38.3</td><td>79.7</td><td>50.7</td></tr><tr><td rowspan="2">Spectral</td><td>Acc.</td><td>97.8</td><td>97.5</td><td>98.1</td><td>97.6</td><td>86.6</td><td>83.8</td><td>86.7</td><td>85.1</td></tr><tr><td>A.S.R.</td><td>0.2</td><td>0.2</td><td>0.2</td><td>0.1</td><td>0.2</td><td>0.1</td><td>0.2</td><td>0.1</td></tr><tr><td rowspan="2">BaFFLe</td><td>Acc.</td><td>97.2</td><td>97.0</td><td>97.6</td><td>72.6</td><td>85.7</td><td>85.0</td><td>85.9</td><td>85.8</td></tr><tr><td>A.S.R.</td><td>23.6</td><td>1.3</td><td>29.2</td><td>13.6</td><td>7.0</td><td>9.5</td><td>48.1</td><td>42.7</td></tr><tr><td rowspan="2">IRLS</td><td>Acc.</td><td>97.8</td><td>97.8</td><td>97.9</td><td>97.4</td><td>86.7</td><td>86.2</td><td>87.1</td><td>86.6</td></tr><tr><td>A.S.R.</td><td>4.3</td><td>1.2</td><td>12.6</td><td>34.6</td><td>30.3</td><td>28.3</td><td>65.4</td><td>46.7</td></tr><tr><td rowspan="2">RLR</td><td>Acc.</td><td>95.8</td><td>96.7</td><td>97.0</td><td>96.3</td><td>85.7</td><td>85.1</td><td>86.4</td><td>85.3</td></tr><tr><td>A.S.R.</td><td>4.4</td><td>1.5</td><td>4.5</td><td>3.7</td><td>3.1</td><td>1.4</td><td>2.5</td><td>6.7</td></tr><tr><td rowspan="2">Ours</td><td>Acc.</td><td>98.1</td><td>97.7</td><td>97.9</td><td>97.5</td><td>86.8</td><td>84.1</td><td>86.4</td><td>81.7</td></tr><tr><td>A.S.R.</td><td>0.1</td><td>0.1</td><td>0.2</td><td>0.1</td><td>0.2</td><td>0.1</td><td>0.1</td><td>0.2</td></tr></table>

TABLE 4: Time consumption of defense mechanisms  

<table><tr><td>Time(s)</td><td>MNIST</td><td>CIFAR</td><td>FASHION</td><td>AG_NEWS</td><td>YAHOO</td></tr><tr><td>Spectral</td><td>0.06+533</td><td>0.08+1098</td><td>0.03+527</td><td>0.04+400</td><td>0.05+4528</td></tr><tr><td>BaFFLe</td><td>17.64</td><td>57.48</td><td>17.91</td><td>86.68</td><td>354.20</td></tr><tr><td>IRLS</td><td>0.05</td><td>21.86</td><td>0.15</td><td>71.87</td><td>68.56</td></tr><tr><td>RLR</td><td>0.01</td><td>-</td><td>0.01</td><td>1.36</td><td>1.22</td></tr><tr><td>Ours</td><td>2.06</td><td>5.62</td><td>2.10</td><td>7.98</td><td>9.06</td></tr></table>

# Answer to RQ3: Our approach can effectively defend against malicious clients with different backdoor patterns like logo backdoor in practice.

# 5.5 RQ4: What is the detection overhead when our approach is used as a runtime defense solution?

Table 4 summarizes the time cost of different approaches when deployed as runtime defense. For each approach, we calculate the average overhead with regard to the detection rounds, attacker ratios, and different data distribution types. Among them all, Spectral has the best or comparable performance in runtime efficiency for all tasks. This is because the anomaly detection model of Spectral is trained in advance based on the model updates obtained from each epoch in centralized training on test data. We show the offline training time of the detection model of Spectral in the subscript. RLR demonstrates competitive performance with Spectral on the benchmark tasks. However, it achieves defense by gathering the client votes for model parameter sign change, therefore the defense overhead grows at least linear to the parameter size of the model architecture. As demonstrated in Section 5.3, RLR is also faced with the convergence problem when using complex models like ResNet architecture.

IRLS shows advantage in time cost for MNIST and Fahshion- MNIST tasks. However, the IRLS algorithm needs to calculate the residuals of all parameters with regard to the linear regression line for all local models, which leads to cubical growth in time cost with the model parameter complexity. The algorithm design of BaFFLe investigates the current global model's prediction error variation on the validation data against the history global models on each local client, which leads to the increase of time cost for backdoor detection. Meanwhile, our approach achieves - detection by inspecting the extracted RDM instead of the model parameters, which empowers our approach with the scalability to large model architectures. For example, compared with IRLS, our approach achieves about  $74.3\%$ $88.9\%$  and  $86.8\%$  improvement in runtime efficiency for CIFAR10, AG- NEWS and Yahoo Answers tasks. Our approach outperforms BaFFLe in time cost with  $95.0\%$  improvement on average across all benchmark tasks. Moreover, we could further reduce the detection overhead of our approach by preprocessing the data stimuli once and for all or parallelizing the process of model representation extraction.

Answer to RQ4: Our approach can be used as a runtime defense solution with sufficient efficiency, along with the scalability to complex model architectures.

# 5.6 Discussion

In this section, we evaluate the performance of our approach as the number of clients increases. We further examine its effectiveness against varied attack configurations beyond adversary- selected backdoor injection and analyze the effect of sample size on detection accuracy. Lastly, we discuss the parameter settings of our approach and highlight key differences compared to previous methods.

# 5.6.1 Evaluation on Increased Clients

To evaluate the performance of our approach on more clients, we conducted additional experiments in a 20- client setting. Table 5 presents the results for the Yahoo Answers task under different attacker ratios. Compared with the Average algorithm, our method achieves an average reduction of  $69.9\%$  in attack success rate and a  $29.9\%$  improvement in accuracy.

Compared with Spectral and BaFFLe methods, our approach demonstrates more consistent defense performance across all settings. Specifically, under a  $40\%$  attacker ratio in the IID setting, Spectral shows an attack success rate of up to  $7.1\%$  while BaFFLe permits up to  $9.7\%$ . Moreover, Spectral suffers from a high false positive rate and BaFFLe

TABLE 5: Defense results of accuracy (Acc.) and attack success rate (A.S.R.) of different methods for 20 clients (IRLS and RLR result in Out of Memory errors)  

<table><tr><td>Methods</td><td>Metric (%)</td><td colspan="4">IID</td><td colspan="4">Non-IID</td></tr><tr><td colspan="2">Attacker Ratio</td><td>10%</td><td>20%</td><td>30%</td><td>40%</td><td>10%</td><td>20%</td><td>30%</td><td>40%</td></tr><tr><td rowspan="3">Average</td><td>Acc.</td><td>44.4</td><td>22.7</td><td>13.5</td><td>10.5</td><td>47.4</td><td>29.6</td><td>16.8</td><td>10.5</td></tr><tr><td>A.S.R.</td><td>27.1</td><td>84.7</td><td>95.5</td><td>99.7</td><td>21.3</td><td>68.4</td><td>90.2</td><td>99.7</td></tr><tr><td>Time(s)</td><td>0.001</td><td>0.002</td><td>0.001</td><td>0.001</td><td>0.004</td><td>0.003</td><td>0.003</td><td>0.004</td></tr><tr><td rowspan="3">Spectral</td><td>Acc.</td><td>53.1</td><td>52.3</td><td>52.5</td><td>52.5</td><td>55.0</td><td>55.2</td><td>53.4</td><td>54.5</td></tr><tr><td>A.S.R.</td><td>3.0</td><td>4.1</td><td>3.0</td><td>7.1</td><td>5.6</td><td>3.6</td><td>2.5</td><td>2.2</td></tr><tr><td>Time(s)+4528</td><td>0.051</td><td>0.079</td><td>0.054</td><td>0.048</td><td>0.052</td><td>0.073</td><td>0.066</td><td>0.089</td></tr><tr><td rowspan="3">BaFFLe</td><td>Acc.</td><td>48.4</td><td>47.6</td><td>49.7</td><td>48.4</td><td>49.9</td><td>51.5</td><td>52.6</td><td>51.9</td></tr><tr><td>A.S.R.</td><td>2.7</td><td>4.7</td><td>4.8</td><td>9.7</td><td>2.9</td><td>8.0</td><td>2.6</td><td>2.4</td></tr><tr><td>Time(s)</td><td>568.350</td><td>568.828</td><td>570.530</td><td>596.911</td><td>469.898</td><td>450.229</td><td>419.010</td><td>417.066</td></tr><tr><td rowspan="3">Ours</td><td>Acc.</td><td>53.0</td><td>53.0</td><td>53.1</td><td>54.1</td><td>54.3</td><td>56.1</td><td>55.9</td><td>55.2</td></tr><tr><td>A.S.R.</td><td>3.3</td><td>3.7</td><td>3.5</td><td>4.2</td><td>1.8</td><td>3.6</td><td>3.4</td><td>3.8</td></tr><tr><td>Time(s)</td><td>20.415</td><td>26.230</td><td>26.614</td><td>19.658</td><td>21.608</td><td>27.786</td><td>30.367</td><td>19.875</td></tr></table>

relies on model rejection at the global level instead of at the individual client level; they both lead to lower test accuracy.

In terms of time overhead, Spectral requires 4528 seconds for anomaly detection model training and has a runtime overhead of 0.064 seconds, demonstrating high efficiency for runtime deployment. In comparison with BaFFLe, our method achieves over 20 times improvement in runtime efficiency. We also run the IRLS and RLR algorithms for defense in the 20- client setting. However, they both encountered out- of- memory errors due to the need to handle 20 copies of the model weights.

Compraed with the 10- client setting, we observe that as the dataset is distributed across more clients, the global model converges more slowly, leading to lower accuracy at the same training round. In terms of defense performance, our approach consistently maintains its effectiveness across different attacker ratios. Regarding time cost, the runtime scales linearly with the number of clients. This overhead can be reduced by parallelizing the RDM extraction process across all local clients.

# 5.6.2 Evaluation on Varied Attacks.

We evaluate our detection approach against three more attack configurations: (1) label- flipping attacks [44] where attackers flip the labels of local training data from the source class to a target class; (2) semantic backdoor attacks [9] by assigning an attacker- chosen label to data with certain features instead of modified training data with an attacker- chosen backdoor, e.g., cars painted in green labeled as birds; (3) stealthy attacks [8] where the adversary maintains the attack stealth by ensuring the malicious update to be close to the benign agents' updates.

In label- flipping attacks, we flip the label of 1 to 7 for the MNIST dataset. We perform the attack at round 20 with attacker ratio varied from  $0\%$  to  $40\%$  for both IID and Non- IID data. We measure the detection performance in terms of three metrics: FPR, FNR, and F1 score. Table 6 shows the average results of 5 experiments settings w.r.t. attacker ratio  $(0\% - 40\%)$  for IID and Non- IID data distribution. Our approach achieves accurate detection for IID data, and achieves 0.025 FPR and 0.96 F1- score for Non- IID data, demonstrating effective performance against label- flipping attacks. In general, unlike backdoor attack, which is attained by mixed training objectives - prediction accuracy on normal data and target prediction for data with selected trigger, local training under label- flipping attacks is based on a selected target class assigned to normal data of a certain source class. Compared with backdoor injection, such label flipping attack will lead to more obvious behavior differences on normal data from benign clients. Still, Non- IID data poses challenges to the detection performance of our method, as heterogeneous data will cause more behavior difference between benign clients.

TABLE 6: Detection performance against varied attack configurations  

<table><tr><td>Data Type</td><td>Attack</td><td>FPR</td><td>FNR</td><td>F1</td></tr><tr><td>IID</td><td>Label-flip Semantic Stealthy</td><td>0 0 0 0</td><td>0 1 1 1 1</td><td></td></tr><tr><td>Non-IID</td><td>Label-flip Semantic Stealthy</td><td>0 0 0 0</td><td>0 0 0 0 1 1</td><td></td></tr></table>

Semantic backdoor attacks aim to cause model misclassification on input data with certain features. In contrast with backdoor injection, semantic backdoors can cause the model to misclassify inputs without additional modification. For the CIFAR- 10 task, we follow [9] and use three features as semantic backdoors: green cars (30 images in CIFAR- 10), cars with racing stripes (21 images), cars with vertically striped walls in the background (12 images), and change their labels to bird. We evaluate the detection performance under different attacker ratios and report the average results in terms of three metrics. As shown in Table 6, our approach achieves accurate detection for both IID and Non- IID data distribution. Similar to label- flipping attacks, the selected semantic backdoors are part of features of normal training data on which malicious clients are trained with different target labels from benign clients, which leads to model behavior deviation.

To achieve stealthy poisoning attacks, we follow [8] and add loss terms corresponding to accuracy on normal data and weight update statistics. For stealthy poisoning on the Fashion- MNIST task, we select 10 data instances

![](images/9674e27666400992c60161adaed2c0923ce7e3e1d6bb40189a803cc7be2aa071.jpg)  
Fig. 8: Detection accuracy under different sample size.

with desired target class (different from the ground- truth) as the adversarial objective. To ensure the malicious weight update to be close to that of benign clients, we constrain the malicious updates by adding the  $\ell_2$  norm between malicious updates and the average benign updates of the previous iteration, i.e.,  $\| \delta_{mal}^t - \delta_{ben}^{t - 1}\|$  into the malicious objective function. The evaluation results against stealthy attacks are summarized in Table 6. Our approach could accurately detect the stealthy poisoning attacks for both IID and Non- IID data distribution. Although the stealthy poisoning attack takes the weight update distance from benign updates into consideration and incorporates  $\ell_2$  distances in the loss function, our detection approach does not rely on inspecting model updates. In fact, despite the constraints w.r.t. weight statistics, this additional term in the objective function leads to more behavior deviation from benign ones, which can be effectively detected by our approach.

# 5.6.3 Effect of Sample Size

We demonstrate the effect of sample size on detection accuracy in the following. Intuitively, a large sample size  $n = m\cdot b$  will capture more informative model representation, leading to more accurate detection, but at the cost of a higher computation overhead. To investigate the effect of sample size  $n$  on detection accuracy, we perform experiments on image classification tasks and evaluate the detection accuracy by F1 score under different settings of  $n$ . As shown in Figure 8, the detection accuracy of our approach increases as the sample size increases until it reaches a plateau. In our experiments, we follow a typical setting of 100 for each class as a trade- off between effectiveness and efficiency.

# 5.6.4 Hyperparameter Setting

We set the LOF threshold of our approach to 1.5, which is generic and effective across all the benchmark tasks. To accommodate the challenges brought by Non- IID data distribution with a high attacker ratio, we integrate a threshold refinement mechanism, as shown in Algorithm 2. The threshold refinement mechanism relies on a dynamic thresholding strategy without a pre- fixed hyper- parameter, where the threshold  $\delta_d$  is set as the mean value of the remaining clients' neighboring distance. Clients with higher neighboring distance than this threshold  $\delta_d$  are deemed as malicious candidates. The LOF threshold  $\delta_{re}$  is then refined to be the mean value of the LOF score of these potential malicious candidates.  $\epsilon_{d}$  is used to avoid false positives in the case where all the remaining clients are benign ones. It is empirically set as the maximum neighboring distance among honest clients obtained in the early training rounds or the central aggregation server using test data.

Finally, we present a summary of the differences between the proposed approach and existing works in detection rationale and methodology. The Spectral and BaFFLe techniques rely on inspecting model parameters and analyzing prediction error variation on validation data, respectively. While the RLR method, relying on adjusting parameter signs, shows competitive performance in image classification tasks, it faces convergence issues, such as with the ResNet- 18 model on CIFAR- 10. Our approach has fundamental differences in detection rationale and solution design: (1) Detection rationale: Unlike existing methods that inspect local model parameters or assess prediction error variation, our approach uses sampling- based model representation. This avoids the need for computations on large- size parameters in complex models and is effective against stealthy attackers that maintain high prediction accuracy. (2) Solution design: We handle data heterogeneity and varying attacker ratios by using density- based metrics and proposing an iterative algorithm with a threshold refinement mechanism, rather than relying on mean or median of parameter updates. This makes our approach deliver consistent effectiveness against varied attack configurations.

# 6 CONCLUSION

In this work, we propose a novel approach of runtime backdoor detection for federated learning (FL). We characterize the client behaviors by exploiting the model representation method in the form of RDM, which provides a geometric representation of client models trained with different input statistics. Then we perform the client differential analysis and quantify the client dissimilarity w.r.t. their corresponding RDMs to capture the model deviation caused by backdoor injection. We further propose an iterative algorithm to identify malicious clients and eliminate backdoor injection through outlier analysis based on the client dissimilarity measurements. Our experimental evaluation demonstrates the effectiveness, stability and efficiency of our approach in defending FL against backdoor attacks across a range of tasks.

# ACKNOWLEDGMENTS

This research was sponsored by the National Natural Science Foundation of China under Grant No. 62172019, 61772038, and CCF- Huawei Formal Verification Innovation Research Plan; the Ministry of Education, Singapore under its Academic Research Fund under Grants Tier 1 21- SIS- SMU- 033, Tier 3 MOET32020- 0004; the National Research Foundation, Prime Ministers Office, Singapore under its National Cybersecurity R&D Program (Award No. NRF2018NCR- NCR005- 0001), NRF Investigation NRF106- 2020- 0022- 0001, the AI Singapore Programme (AISG2- RP- 2020- 019). XZ received partial support from ELSA: European Lighthouse on Secure and Safe AI project (Grant No. 101070617 under UK guarantee).

# REFERENCES

[1] B. McMahan, E. Moore, D. Ramage, S. Hampson, and B. A. y Arcas, "Communication- efficient learning of deep networks from decentralized data," in Proceedings of the 20th International Conference on Artificial Intelligence and Statistics, ser. Proceedings of Machine Learning Research, vol. 54. PMLR, 2017, pp. 1273- 1282. [2] T. S. Brisimi, R. Chen, T. Mela, A. Olshevsky, I. C. Paschalidis, and W. Shi, "Federated learning of predictive models from federated electronic health records," Int. J. Medical Informatics, vol. 112, pp. 59- 67, 2018. [3] N. Rieke, J. Hancox, W. Li, F. Miletari, H. Roth, S. Albarqouni, S. Bakas, M. N. Galtier, B. A. Landman, K. H. Maier- Hein, S. Ourselin, M. J. Sheiler, R. M. Summers, A. Trask, D. Xu, M. Baust, and M. J. Cardoso, "The future of digital health with federated learning," CoRR, vol. abs/2003.08119, 2020. [Online]. Available: http://arxiv.org/abs/2003.08119. [4] J. Xu, B. S. Glicksberg, C. Su, P. B. Walker, J. Bian, and F. Wang, "Federated learning for healthcare informatics," J. Heal. Informatics Res., vol. 5, no. 1, pp. 1- 19, 2021. [5] T. Gu, B. Dolan- Gavitt, and S. Garg, "Badnets: Identifying vulnerabilities in the machine learning model supply chain," CoRR, vol. abs/1708.06733, 2017. [Online]. Available: http://arxiv.org/abs/1708.06733. [6] Z. Sun, P. Kairouz, A. T. Suresh, and H. B. McMahan, "Can you really backdoor federated learning?" CoRR, vol. abs/1911.07963, 2019. [Online]. Available: http://arxiv.org/abs/1911.07963. [7] M. Fang, X. Cao, J. Jia, and N. Z. Gong, "Local model poisoning attacks to byzantine- robust federated learning," in 29th USENIX Security Symposium. USENIX Association, 2020, pp. 1605- 1622. [8] A. N. Bhagoji, S. Chakraporty, P. Mittal, and S. B. Calo, "Analyzing federated learning through an adversarial lens," in Proceedings of the 36th International Conference on Machine Learning, ser. Proceedings of Machine Learning Research, vol. 97. PMLR, 2019, pp. 634- 643. [9] E. Bagdasaryan, A. Veit, Y. Hua, D. Estrin, and V. Shmatikov, "How to backdoor federated learning," in Proceedings of the Twenty Third International Conference on Artificial Intelligence and Statistics, ser. Proceedings of Machine Learning Research, vol. 108. PMLR, 2020, pp. 2938- 2948. [10] P. Kairouz, H. B. McMahan, B. Avent, A. Bellet, M. Bennis, A. N. Bhagoji, K. A. Bonawitsi, Z. Charles, G. Cormode, R. Cummings, R. G. L. D'Oliveira, H. Eichner, S. E. Rouayheb, D. Evans, J. Gardner, Z. Garrett, A. Gascon, B. Ghazi, P. B. Gibbons, M. Gruteser, Z. Harchaoui, C. He, L. He, Z. Huo, B. Hutchinson, J. Hsu, M. Jaggi, T. Javidi, G. Joshi, M. Khodak, J. Konecny, A. Korolova, F. Koushanfar, S. Koyejjo, T. Lepoint, Y. Liu, P. Mittal, M. Mohri, R. Nock, A. Ozgur, R. Pagh, H. Qi, D. Ramage, R. Raskar, M. Raykova, D. Song, W. Song, S. U. Stich, Z. Sun, A. T. Suresh, F. Tramer, P. Vepakomma, J. Wang, L. Xiong, Z. Xu, Q. Yang, F. X. Yu, H. Yu, and S. Zhao, "Advances and open problems in federated learning," Found. Trends Mach. Learn., vol. 14, no. 1- 2, pp. 1- 210, 2021. [11] X. Chen, C. Liu, B. Li, K. Lu, and D. Song, "Targeted backdoor attacks on deep learning systems using data poisoning," CoRR, vol. abs/1712.05526, 2017. [Online]. Available: http://arxiv.org/abs/1712.05526. [12] Y. Liu, S. Ma, Y. Aafer, W. Lee, J. Zhai, W. Wang, and X. Zhang, "Trojaning attack on neural networks," in 25th Annual Network and Distributed System Security Symposium, NDSS 2018. The Internet Society, 2018. [13] B. Wang, Y. Yao, S. Shan, H. Li, B. Viswanath, H. Zheng, and B. Y. Zhao, "Neural cleanse: Identifying and mitigating backdoor attacks in neural networks," in 2019 IEEE Symposium on Security and Privacy. IEEE, 2019, pp. 707- 723. [14] C. Fung, C. J. M. Yoon, and I. Beschastnikh, "Mitigating sybils in federated learning poisoning," CoRR, vol. abs/1808.04866, 2018. [Online]. Available: http://arxiv.org/abs/1808.04866. [15] T. D. Nguyen, P. Rieger, H. Yalame, H. Mollering, H. Fereidooni, S. Marchal, M. Miettinen, A. Mirhoseini, A. Sadeghi, T. Schneider, and S. Zeitouni, "FLGUARD: secure and private federated learning," CoRR, vol. abs/2101.02281, 2021. [Online]. Available: https://arxiv.org/abs/2101.02281. [16] S. Li, Y. Cheng, W. Wang, Y. Liu, and T. Chen, "Learning to detect malicious clients for robust federated learning," CoRR, vol. abs/2002.00211, 2020. [Online]. Available: https://arxiv.org/abs/2002.00211.

[17] S. Fu, C. Xie, B. Li, and Q. Chen, "Attack- resistant federated learning with residual- based reweighting," CoRR, vol. abs/1912.11464, 2019. [Online]. Available: http://arxiv.org/abs/1912.11464. [18] Y. Mi, J. Guan, and S. Zhou, "ARIBa: towards accurate and robust identification of backdoor attacks in federated learning," CoRR, vol. abs/2202.04311, 2022. [Online]. Available: https://arxiv.org/abs/2202.04311. [19] S. Andreina, G. A. Marson, H. Mollering, and G. Karame, "Baffle: Backdoor detection via feedback- based federated learning," CoRR, vol. abs/2011.02167, 2020. [20] P. Blanchard, E. M. E. Mhamdi, R. Guerraoui, and J. Stainer, "Machine learning with adversaries: Byzantine tolerant gradient descent," in Annual Conference on Neural Information Processing Systems 2017, 2017, pp. 119- 129. [21] Y. Chen, L. Su, and J. Xu, "Distributed statistical machine learning in adversarial settings: Byzantine gradient descent," Proc. ACM Mee, Anal. Comput. Syst., vol. 1, no. 2, pp. 44:14- 44:25, 2017. [22] C. Xie, O. Koyejo, and I. Gupta, "Generalized byzantine- tolerant SGD," CoRR, vol. abs/1802.10116, 2018. [Online]. Available: http://arxiv.org/abs/1802.10116. [23] D. Yin, Y. Chen, K. Ramchandran, and P. L. Bartlett, "Byzantine- robust distributed learning: Towards optimal statistical rates," in Proceedings of the 35th International Conference on Machine Learning, ser. Proceedings of Machine Learning Research, vol. 80. PMLR, 2018, pp. 5636- 5645. [24] D. Alistarh, Z. Allen- Zhu, and J. Li, "Byzantine stochastic gradient descent," in Annual Conference on Neural Information Processing Systems 2018, 2018, pp. 4618- 4628. [25] K. Pillutla, S. M. Kakade, and Z. Harchaoui, "Robust aggregation for federated learning," IEEE Transactions on Signal Processing, vol. 70, pp. 1142- 1154, 2022. [26] Z. Ghodsi, M. Javaheripi, N. Sheybani, X. Zhang, K. Huang, and F. Koushanfar, "Zrobe: Zero peek robustness checks for federated learning," in IEEE/CVF international Conference on Computer Vision, ICCV 2023. IEEE, 2023, pp. 4837- 4847. [27] M. S. Ozdayi, M. Kantarcioglu, and Y. R. Gel, "Defending against backdoors in federated learning with robust learning rate," in Thirty- Fifth AAAI Conference on Artificial Intelligence, AAAI 2021. AAAI Press, 2021, pp. 9268- 9270. [28] M. Raghu, J. Gilmer, J. Yosinski, and J. Sohl- Dickstein, "SVCCA: singular vector canonical correlation analysis for deep learning dynamics and interpretability," in Annual Conference on Neural Information Processing Systems 2017, 2017, pp. 6076- 6085. [29] A. S. Morcos, M. Raghu, and S. Bengio, "Insights on representational similarity in neural networks with canonical correlation," in Annual Conference on Neural Information Processing Systems 2018, 2018, pp. 5732- 5741. [30] J. Mehrer, C. J. Spoerer, N. Kriegeskorte, and T. C. Kietzmann, "Individual differences among deep neural network models," Nature Communications, vol. 11, no. 5725, 2020. [31] D. Li and J. Wang, "Fedmd: Heterogeneous federated learning via model distillation," CoRR, vol. abs/1910.03581, 2019. [32] K. A. S. Immink and J. H. Weber, "Minimum pearson distance detection for multilevel channels with gain and/or offset mismatch," IEEE Trans. Inf. Theory, vol. 60, no. 10, pp. 5966- 5974, 2014. [33] N. Kriegeskorte, M. Mur, and P. Battettini, "Representational similarity analysis - connecting the branches of systems neuroscience," Frontiers in Systems Neuroscience, vol. 2, p. 4, 2008. [34] M. M. Breunig, H.- P. Kriegel, R. T. Ng, and J. Sander, "Lof: Identifying density- based local outliers," SIGMOD Rec., vol. 29, no. 2, p. 93- 104, 2000. [35] Y. LeCun, L. Bottou, Y. Bengio, P. Haffner et al., "Gradient- based learning applied to document recognition," Proceedings of the IEEE, vol. 86, no. 11, pp. 2278- 2324, 1998. [36] H. Xiao, K. Rasul, and R. Vollgraf, "Fashion- mnist: a novel image dataset for benchmarking machine learning algorithms," CoRR, vol. abs/1708.07747, 2017. [Online]. Available: http://arxiv.org/abs/1708.07747. [37] A. Krizhevsky and G. Hinton, "Learning multiple layers of features from tiny images," Master's thesis, Department of Computer Science, University of Toronto, 2009. [38] X. Zhang, J. J. Zhao, and Y. LeCun, "Character- level convolutional networks for text classification," in Annual Conference on Neural Information Processing Systems 2015, 2015, pp. 649- 657. [39] ——, "Character- level convolutional networks for text classification," in Advances in Neural Information Processing Systems 28: Annual Conference on Neural Information Processing Systems 28: Annual Conference on Neural Information Processing Systems 28: Annual Conference on Neural Information Processing Systems 28: Annual Conference on Neural Information Processing Systems 28: Annual Conference on Neural Information Processing Systems 28: Annual Conference on Neural Information Processing Systems 28: Annual Conference on Neural Information Processing Systems 28: Annual Conference on Neural Information Processing Systems 28: Annual Conference on Neural Information Processing Systems 28: Annual Conference on

formation Processing Systems, 2015, pp. 649- 657. [Online]. Available: https://proceedings.neurips.cc/paper/2015/hash/250cf8b51c773f3f8dc84b8e867a9a02- Abstract.html[40] K. He, X. Zhang, S. Ren, and J. Sun, "Deep residual learning for image recognition," in 2016 IEEE Conference on Computer Vision and Pattern Recognition. IEEE Computer Society, 2016, pp. 770- 778. [41] P. Team, "Text classification with the torchtext library," https://github.com/pytorch/pytorch, 2017, accessed: 2021- 07. [42] T. Minka, "Estimating a dirichlet distribution," Microsoft Research, Technical Report, September 2000. [43] M. Fan, Z. Si, X. Xie, Y. Liu, and T. Liu, "Text backdoor detection using an interpretable RNN abstract model," IEEE Trans. Inf. Forensics Secur., vol. 16, pp. 4117- 4132, 2021. [44] A. Paudice, L. Munoz- Gonzalez, and E. C. Lupu, "Label sanitization against label flipping poisoning attacks," in ECML PKDD 2018 Workshops - Nemesis 2018, UrbReas 2018, SoGood, 2018, IWAISe 2018, and Green Data Mining 2018, Dublin, Ireland, September 10- 14, 2018, Proceedings, ser. Lecture Notes in Computer Science, vol. 11329. Springer, 2018, pp. 5- 15. [Online]. Available: https://doi.org/10.1007/978- 3- 030- 13453- 2_1

# APPENDIX

![](images/774c65c67b2295eb4dcfcf4976d3c4b025b2341c91ebe43dc136e45b57da1a2b.jpg)  
Fig. 9: Pairwise dissimilarity matrix for MNIST.

![](images/3c04d91ef857eb6c9bb79cecf66dd3e17c89d247e41b67fa7eeef4b8e163ef23.jpg)  
Fig. 10: Pairwise dissimilarity matrix for Yahoo Answers.

Client Dissimilarity Quantification. Given the high dimensionality of the extracted client RDMs, we provide the pairwise client dissimilarity heatmap to illustrate the distances between benign and malicious models in terms of how they encode and recognize the input data. As examples, we present the pairwise dissimilarity heatmaps for MNIST and Yahoo Answers datasets under both IID and Non- IID settings in Figure 9 and 10. In these examples, clients 0- 5 are benign models, and clients 6- 9 are malicious. As shown in Figure 9 and 10, the distances between benign models tend to be depicted in blue, indicating a high level of similarity in their data recognition and suggesting they form a close neighborhood where they serve as each other's  $k$ - distance neighbors (top left region in blue). In contrast, the RDM distances between benign and malicious models are represented in red, indicating greater dissimilarity (top right and bottom left regions). While the RDMs of malicious clients also share similarities due to their common backdoor injection objective, their distances exhibit greater variance. This suggests that malicious models are more sparsely distributed and located at varying distances from the densely clustered benign models.

Effectiveness of Backdoor Detection. Table 7 summarizes the detailed detection results of our approach and the baseline method Spectral for the benchmark datasets under attacker ratios from  $0\%$  to  $40\%$  (step length of  $10\%$ ), attack rounds from 10 to 30 (step length as 10) for both IID and Non- IID data distributions. We report the F1 score for the attacker ratios from  $10\%$  to  $40\%$  (the higher, the better) and the FPR (False Positive Rate) for the attacker ratio of  $0\%$  (the lower, the better).

TABLE 7: Detection results on the benchmark datasets under different attacker ratios, attack rounds, and data distributions (F1-score for attacker ratios from  $10\%$  to  $40\%$  FPR for attacker ratio  $0\%$  

<table><tr><td rowspan="4">Dataset</td><td rowspan="4">Attacker Ratio</td><td colspan="11">Attack Round</td><td></td></tr><tr><td colspan="3">Round 10</td><td colspan="3">Round 20</td><td colspan="5">Round 30</td><td></td></tr><tr><td>ID</td><td colspan="2">Non-IID</td><td>ID</td><td colspan="2">Non-IID</td><td>ID</td><td colspan="2">Non-IID</td><td>ID</td><td colspan="2">Non-IID</td></tr><tr><td>Spectral</td><td>Ours</td><td>Spectral</td><td>Ours</td><td>Spectral</td><td>Ours</td><td>Spectral</td><td>Ours</td><td>Spectral</td><td>Ours</td><td>Spectral</td><td>Ours</td></tr><tr><td rowspan="5">MNIST</td><td>0% (↓)</td><td>0.3</td><td>0</td><td>0.4</td><td>0</td><td>0.4</td><td>0</td><td>0.4</td><td>0.1</td><td>0.4</td><td>0</td><td>0.3</td><td>0</td></tr><tr><td>10%(↑)</td><td>1</td><td>1</td><td>1</td><td>1</td><td>1</td><td>1</td><td>0.67</td><td>1</td><td>1</td><td>0.67</td><td>1</td><td>1</td></tr><tr><td>20%(↑)</td><td>1</td><td>1</td><td>1</td><td>1</td><td>1</td><td>1</td><td>1</td><td>1</td><td>1</td><td>1</td><td>1</td><td>1</td></tr><tr><td>30%(↑)</td><td>1</td><td>1</td><td>1</td><td>1</td><td>1</td><td>1</td><td>1</td><td>0.86</td><td>1</td><td>1</td><td>1</td><td>1</td></tr><tr><td>40%(↑)</td><td>1</td><td>1</td><td>1</td><td>1</td><td>1</td><td>1</td><td>1</td><td>1</td><td>1</td><td>1</td><td>1</td><td>1</td></tr><tr><td rowspan="5">CIFAR</td><td>0% (↓)</td><td>0.3</td><td>0</td><td>0.4</td><td>0</td><td>0.3</td><td>0</td><td>0.4</td><td>0</td><td>0.4</td><td>0</td><td>0.4</td><td>0</td></tr><tr><td>10%(↑)</td><td>0.4</td><td>1</td><td>0.4</td><td>1</td><td>0.5</td><td>0.67</td><td>0.67</td><td>1</td><td>0.5</td><td>1</td><td>0.67</td><td>1</td></tr><tr><td>20%(↑)</td><td>1</td><td>1</td><td>0.8</td><td>1</td><td>0.8</td><td>1</td><td>1</td><td>1</td><td>1</td><td>1</td><td>1</td><td>0.8</td></tr><tr><td>30%(↑)</td><td>0.75</td><td>0.86</td><td>0.57</td><td>0.8</td><td>0.86</td><td>0.86</td><td>1</td><td>1</td><td>1</td><td>1</td><td>0.57</td><td>0.86</td></tr><tr><td>40%(↑)</td><td>0.89</td><td>1</td><td>0.86</td><td>1</td><td>1</td><td>1</td><td>0.86</td><td>1</td><td>1</td><td>0.89</td><td>0.75</td><td>0.89</td></tr><tr><td rowspan="5">FASHION</td><td>0% (↓)</td><td>0.6</td><td>0</td><td>0.5</td><td>0</td><td>0.4</td><td>0</td><td>0.4</td><td>0</td><td>0.4</td><td>0</td><td>0.4</td><td>0</td></tr><tr><td>10%(↑)</td><td>0.4</td><td>1</td><td>0.4</td><td>1</td><td>0.5</td><td>1</td><td>0.5</td><td>1</td><td>0.4</td><td>1</td><td>0.67</td><td>1</td></tr><tr><td>20%(↑)</td><td>0.67</td><td>1</td><td>1</td><td>0.8</td><td>0.8</td><td>0.8</td><td>1</td><td>1</td><td>0.8</td><td>1</td><td>0.67</td><td>1</td></tr><tr><td>30%(↑)</td><td>1</td><td>1</td><td>0.86</td><td>1</td><td>1</td><td>1</td><td>1</td><td>1</td><td>1</td><td>1</td><td>0.86</td><td>1</td></tr><tr><td>40%(↑)</td><td>1</td><td>1</td><td>1</td><td>1</td><td>1</td><td>1</td><td>1</td><td>0.89</td><td>1</td><td>1</td><td>0.89</td><td>0.89</td></tr><tr><td rowspan="5">AG-NEWS</td><td>0% (↓)</td><td>0.4</td><td>0</td><td>0.5</td><td>0</td><td>0.6</td><td>0</td><td>0.5</td><td>0</td><td>0.3</td><td>0</td><td>0.4</td><td>0</td></tr><tr><td>10%(↑)</td><td>0</td><td>1</td><td>0.33</td><td>1</td><td>0</td><td>1</td><td>0.33</td><td>1</td><td>0.33</td><td>1</td><td>0.4</td><td>1</td></tr><tr><td>20%(↑)</td><td>0</td><td>1</td><td>0.29</td><td>1</td><td>0</td><td>1</td><td>0.29</td><td>1</td><td>1</td><td>0.8</td><td>0.4</td><td>1</td></tr><tr><td>30%(↑)</td><td>0</td><td>1</td><td>0.25</td><td>1</td><td>0</td><td>1</td><td>0.25</td><td>1</td><td>0</td><td>1</td><td>0.25</td><td>1</td></tr><tr><td>40%(↑)</td><td>0</td><td>1</td><td>0.25</td><td>1</td><td>0</td><td>1</td><td>0.25</td><td>1</td><td>0.89</td><td>1</td><td>0.25</td><td>1</td></tr><tr><td rowspan="5">YAHOO</td><td>0% (↓)</td><td>0.4</td><td>0</td><td>0.4</td><td>0</td><td>0.3</td><td>0</td><td>0.4</td><td>0</td><td>0.4</td><td>0</td><td>0.4</td><td>0</td></tr><tr><td>10%(↑)</td><td>0.67</td><td>1</td><td>0.4</td><td>1</td><td>0.67</td><td>1</td><td>0.4</td><td>1</td><td>0.67</td><td>1</td><td>0.5</td><td>1</td></tr><tr><td>20%(↑)</td><td>1</td><td>1</td><td>0.33</td><td>1</td><td>1</td><td>1</td><td>0.57</td><td>1</td><td>0.8</td><td>1</td><td>0.57</td><td>1</td></tr><tr><td>30%(↑)</td><td>1</td><td>1</td><td>0.75</td><td>1</td><td>1</td><td>1</td><td>0.57</td><td>1</td><td>1</td><td>1</td><td>0.75</td><td>1</td></tr><tr><td>40%(↑)</td><td>1</td><td>1</td><td>0.44</td><td>1</td><td>1</td><td>1</td><td>0.67</td><td>1</td><td>1</td><td>1</td><td>0.67</td><td>1</td></tr></table>