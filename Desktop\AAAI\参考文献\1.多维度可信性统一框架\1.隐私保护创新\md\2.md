# Theoretically Unmasking Inference Attacks Against LDP-Protected Clients in Federated Vision Models

Quan Nguyen*1 Minh N. Vu*2 True Nguyen3 My T. Thai1

# Abstract

Federated Learning enables collaborative learning among clients via a coordinating server while avoiding direct data sharing, offering a perceived solution to preserve privacy. However, recent studies on Membership Inference Attacks (MIAs) have challenged this notion, showing high success rates against unprotected training data. While local differential privacy (LDP) is widely regarded as a gold standard for privacy protection in data analysis, most studies on MIAs either neglect LDP or fail to provide theoretical guarantees for attack success rates against LDP- protected data.

To address this gap, we derive theoretical lower bounds for the success rates of low- polynomialtime MIAs that exploit vulnerabilities in fully connected or self- attention layers. We establish that even when data are protected by LDP, privacy risks persist, depending on the privacy budget. Practical evaluations on federated vision models confirm considerable privacy risks, revealing that the noise required to mitigate these attacks significantly degrades models' utility.

or weights) can still reveal sensitive information about the training data (<PERSON> et al., 2024; <PERSON> et al., 2023). A prominent attack against FL is the membership inference attack (MIAs) (<PERSON><PERSON><PERSON><PERSON> et al., 2017; <PERSON> et al., 2022b) in which the server seeks to determine whether a particular record was part of the model's training dataset.

To mitigate such privacy risks, local differential privacy (LDP) (<PERSON><PERSON> et al., 2014; <PERSON> et al., 2020) emerged as a prominent solution to limit the privacy leakage of local training data. Under LDP, individuals perturb their data locally before sharing it with the server, thereby eliminating the need for a trusted centralized curator. However, recent studies (<PERSON>uyen et al., 2023; Chen et al., 2021) demonstrated that tackling active membership inference attacks (AMI) conducted by dishonest FL servers requires adding large privacy- preserving noise that would also damage FL utility. In AMI, the server actively poisons the global model before distributing it to clients, enabling them to infer private information. Such attacks typically require multiple training iterations or the use of shadow models, resulting in non- trivial time complexity, and provide no theoretical privacy risks to FL. Building on this, (Vu et al., 2024) proposed a low- polynomial- time attack, presenting two active membership inference attacks on LLMs with guaranteed theoretical success rates on unprotected data.

# 1. Introduction

Federated Learning (FL) (McMahan et al., 2017) is a decentralized machine learning paradigm where multiple devices or nodes (e.g., smartphones, edge devices, or distributed servers) collaboratively train a shared model while keeping their data localized. Due to this property, it has long been heralded as a robust solution for privacy- preserving machine learning. However, FL itself does not provide formal privacy guarantees, as the model updates (gradients

Despite these advancements, most existing works rely heavily on empirical validation and lack a robust theoretical foundation or guarantees for attack success rates, especially under LDP. This challenge arises from the randomness of noise introduced by privacy mechanisms, which varies across iterations and clients, making it challenging to analyze the effectiveness of attacks in a theoretical framework.

This paper takes a step back and establishes a broader view of the principle of privacy risk imposed by dishonest servers in federated vision models under LDP from both theoretical and practical perspectives. Our study focuses on the active adversary setting, where the FL server acts dishonestly by manipulating the trainable weights of a vision model to breach privacy. Specifically, we aim to demonstrate that clients' data, even under LDP protection, are fundamentally vulnerable to AMI attacks carried out by dishonest servers. For that purpose, we analyze attacks that exploit the train

able fully connected (FC) layers and self- attention layers in FL updates as both are widely adopted in federated vision models. Our main contributions are summarized as follows:

We derive theoretical lower and upper bounds (Theorem 1 and 2) on the success rates of a low- polynomialtime attack (Vu et al., 2024) that exploits vulnerabilities in FC layers, showing that privacy risks persist under LDP protection depending on the privacy budget.

For transformer- based vision models such as ViTs (Dosovitskiy et al., 2021), we extend the attack on LLMs in (Vu et al., 2024) to continuous domain and derive theoretical lower bounds on the vulnerability of the self- attention mechanisms against a low- polynomialtime attack that exploit's the layer's memorization mechanism. (Theorem 3).

Our experimental results in real- world state- of- the- art vision models such as ViTs and ResNet (He et al., 2016) demonstrate that AMI attacks achieve notably high success rates observed even under stringent LDP protection (i.e., small privacy budgets  $\epsilon$  ) that considerably degrade the model's utility (Section 5). Furthermore, we consider both traditional FL where clients train a small model (ResNet) and the parameterefficient- fine- tuning paradigm, where clients often utilize large foundation models for pre- training and finetune only some layers or parameters (ViTs).

# 2.Background and Related Works

Federated Learning with Local Differential Privacy (FLLDP). In Federated Learning (FL) (McMahan et al., 2017), a central server orchestrates training while clients store data locally. The server initializes the model parameters  $\theta$  and in each training iteration, a subset of clients computes gradients of the loss function  $\mathcal{L}$  on local data  $D$  ,i.e.,  $\dot{\theta} = \nabla_{\theta}\mathcal{L}_{\Phi}(D)$  . These gradients are aggregated and sent to the server, which updates the parameters accordingly. Training continues until convergence.

To protect the privacy leakage in FL, Local Differential Privacy (LDP) (Dwork, 2006; Erlingsson et al., 2014) has been introduced. LDP is a privacy- preserving mechanism that mitigates risks by perturbing individual data before it leaves the client's device in FL. LDP ensures that the server cannot infer sensitive client information directly from the shared data as defined below.

Definition 1.  $\epsilon$  - LDP. A randomized mechanism  $\mathcal{M}$  satisfies  $\epsilon$  - LDP if, for any two inputs  $x$  and  $x^{\prime}$  and all possible outputs  $\mathcal{O}\in \mathrm{Range}(\mathcal{M})$

$$
Pr[\mathcal{M}(x) = \mathcal{O}]\leq e^{\epsilon}Pr[\mathcal{M}(x^{\prime}) = \mathcal{O}],
$$

where  $\epsilon$  is the privacy budget, and Range  $(\mathcal{M})$  denotes all possible outputs of  $\mathcal{M}$  . In FL- LDP,  $\epsilon$  controls the privacyutility trade- off: smaller  $\epsilon$  enhances privacy by introducing more noise to the data, but this can reduce the utility of the aggregated updates for the global model. While there are other privacy- preserving techniques for FL, such as secure aggregation using multi- party computation (SMPC) or homomorphic encryption (Bonawitz et al., 2017; Nguyen et al., 2023), these are orthogonal research directions and are discussed separately in Appendix. I.

Federated Foundation Models via Parameter- Efficient Fine- Tuning (PEFT). PEFT methods adapt large pretrained models to specific tasks by modifying a small subset of parameters, reducing computational and storage costs. Key approaches include LoRA (Hu et al., 2022a), which adds trainable low- rank matrices; Adapter Modules (Yin et al., 2023), which insert lightweight layers; BitFit (Zaken et al., 2022), which updates bias terms; and Prompt Tuning (Lester et al., 2021), which optimizes input embeddings. In our work, we specifically analyze scenarios where trainable layers are fully connected or self- attention layers.

Membership Inference Attacks (MIAs) in FL. MIAs in FL aim to identify if a specific data point was part of a client's training set. Although FL keeps data local, model updates exchanged between clients and the server can still leak information. Passive attacks (Shokri et al., 2017; Zhang et al., 2020) involve an honest- but- curious server observing the model updates, while Active Membership Inference (AMI) attacks involve a dishonest server poisoning the global models, e.g., maliciously modifying model parameters, before dispatching them to clients. The first AMI attack in FL was introduced by (Nasr et al., 2019), relying on multiple FL iterations. A stronger, single- iteration AMI attack requiring training a separate neural network was later proposed by (Nguyen et al., 2023). Both approaches have non- trivial time complexity and do not establish theoretical privacy risks in FL. Recently, (Vu et al., 2024) introduced two AMI attacks that exploit fully connected and attention layers in LLMs, achieving a high success rate in compromising membership information of unprotected client data.

The primary focus of our study is to demonstrate the existence of low- complexity adversaries with provably high attack success rates, particularly when the data is protected by any ideal LDP mechanism. Since our research aims to assess the resilience of privacy- preserving techniques against real- world adversarial threats, we chose to examine two state- of- the- art AMI attacks on the FC and Attention layers proposed by (Vu et al., 2024), which have low- polynomial time complexity. These attacks allow the server to exploit FC layers to perfectly infer membership information (Theorem 1 in (Vu et al., 2024)) and to exploit self- attention layers to achieve a similarly high success rate (Theorem 2

in (Vu et al., 2024)). However, their theoretical analysis is only applicable to the non- LDP setting.

# 3. AMI Attacks

# 3.1. AMI threat models

The AMI threat models under LDP are formalized through the security games  $\mathsf{Exp}_{\mathrm{LDP}}^{\mathrm{AMI}}$  as described in Fig. 1, following standard security frameworks (Nguyen et al., 2023; Vu et al., 2024). Further details about the security games can be found in Appendix B. In  $\mathsf{Exp}_{\mathrm{LDP}}^{\mathrm{AMI}}$  , the adversarial server  $\mathcal{A}^D$  (superscript  $\mathcal{D}$  indicates that the server knows the data distribution of the client's private data) comprises three components:  $\mathcal{A}_{\mathsf{INI T}}^{\mathcal{D}}$ $\mathcal{A}_{\mathrm{ATTACK}}^{\mathcal{D}}$  and  $\mathcal{A}_{\mathrm{GUESS}}^{\mathcal{D}}$  In  $\mathsf{Exp}_{\mathrm{LDP}}^{\mathrm{AMI}}$  a random bit  $b$  determines if a target sample  $T$  is in the client's data  $D$  Each client applies LDP to perturb their data, generating  $D^{\prime} = \mathcal{M}^{\epsilon}(D) = \{\mathcal{M}^{\epsilon}(X)\}_{X\in D}$  . The server's  $\mathcal{A}_{\mathsf{INI T}}^{\mathcal{D}}$  selects a model  $\Phi$  and  $\mathcal{A}_{\mathrm{ATTACK}}^{\mathcal{D}}$  crafts parameters  $\theta$  using  $T$  . Clients compute gradients  $\dot{\theta} = \nabla_{\theta}\mathcal{L}_{\Phi}(D^{\prime})$  and send them back. With  $\dot{\theta}$ $\mathcal{A}_{\mathrm{GUESS}}^{\mathcal{D}}$  infers  $b$  effectively identifying whether  $T\in D$  . The advantage of the adversarial server  $\mathcal{A}^D$  in the security game is given by:

$$
\begin{array}{r}\mathbf{Adv}_{\mathrm{LDP}}^{\mathrm{AMI}}(\mathcal{A}^D) = 2\operatorname *{Pr}[\mathsf{Exp}_{\mathrm{LDP}}^{\mathrm{AMI}}(\mathcal{A}^D) = 1] - 1\\ = \operatorname *{Pr}[b' = 1|b = 1] + \operatorname *{Pr}[b' = 0|b = 0] - 1 \end{array} \tag{1}
$$

where  $\begin{array}{r}\frac{1}{2}\operatorname *{Pr}[b' = 1|b = 1] + \frac{1}{2}\operatorname *{Pr}[b' = 0|b = 0] \end{array}$  denotes the success rate of the attack. The existence of an adversary with a high advantage implies a high privacy risk/vulnerability of the protocol described in the security game.

# 3.2. FC-based AMI adversary

We analyze the FC- based AMI adversary first introduced in (Vu et al., 2024). In that paper, they proved the existence of an AMI adversary that exploits two FC layers to achieve a perfect membership inference success rate for unprotected data. The FC- based adversary  $\mathcal{A}_{\mathrm{FC}}^{\mathcal{D}}$  is designed to detect a target sample  $T$  with dimension  $d_{T}$  within local training data  $D$  during FL training. For analysis of FC- based adversary, we represent the dataset as  $D = \{X_{i}\}_{i = 1}^{n}$  , where  $X_{i}\in \mathcal{X}$  ,and  $\mathcal{X}\subseteq \mathbb{R}^{d_{X}}$  .The model  $\Phi$  introduces two adversarial fully connected (FC) layers. The first layer has weights  $W_{1}$  of size  $2d_{T}\times d_{T}$  and biases  $b_{1}$  of size  $2d_{T}$  structured to encode the target  $T$  . The second layer outputs a single neuron, with weights  $W_{2}[1,\cdot ]$  and bias  $b_{2}[1]$  set to target the presence of  $T$  . These parameters are defined as:

$$
\begin{array}{r}W_{1}\leftarrow \left[ \begin{array}{c}I_{d_{T}}\\ I_{d_{T}} \end{array} \right],b_{1}\leftarrow \left[ \begin{array}{c} - T\\ T \end{array} \right]\\ W_{2}[1,\cdot ]\leftarrow -1_{d_{T}}^{\top},b_{2}[1]\leftarrow \tau^{\mathcal{D}} \end{array} \tag{3}
$$

where  $\tau^D$  controls the allowable  $L_{1}$  - distance between inputs and  $T$  . Upon receiving input  $X$  , the two FC layers compute $z_{0}\coloneqq \max \{b_{2}[1] - \| X - T\|_{L_{1}},0\}$  . If  $X = T$ $z_{0}$  activates, and the gradient of  $b_{2}[1]$  is non- zero. For  $b_{2}[1] = \tau^{\mathcal{D}} > 0$  small enough,  $z_{0} = 0$  for  $X\neq T$  , leaving the gradient zero. The adversary uses the gradient of  $b_{2}[1]$  as an indicator of the presence of  $T$  in the local data. A non- zero gradient implies  $T$  exists, while a zero gradient indicates it does not. The FC attack  $\mathcal{A}_{\mathrm{FC}}^{\mathcal{D}}$  does not need any distributional information to work on unprotected data. In fact, the attacker just needs to specify  $\tau^D$  (2) small enough such that  $\tau^D < \| X_1 - X_2\|_{L_1}$  for any  $X_{1}\neq X_{2}$  in the model's dictionary. Since the dictionary or the pre- trained feature extractor is public, selecting  $\tau^D$  does not require any additional information.The description of  $\mathcal{A}_{\mathrm{FC}}^{\mathcal{D}}$  is given in Appendix C.1.

# 3.3. Attention-based AMI adversary

The proposed  $\mathcal{A}_{\mathrm{Attn}}^{\mathcal{D}}$  (Vu et al., 2024) leverages the memorization capability of self- attention, a property that was indirectly explored in (Ramsauer et al., 2021). That study demonstrates that self- attention can be interpreted as equivalent to the Hopfield layer, which is specifically designed to integrate memorization directly within the layer. Building on this perspective,  $\mathcal{A}_{\mathrm{Attn}}^{\mathcal{D}}$  employs a tailored configuration of attention to facilitate the memorization of local training data while selectively excluding the target of inference.

The dataset is represented as  $D = \{x_{i}\}_{i = 1}^{n}$  , where  $x_{i}\in \mathcal{X}$ $\mathcal{X}\subseteq \mathbb{R}^{d_x\times N_x}$  , and each column  $x_{j}\in \mathbb{R}^{d_{X}}$  is referred to as a pattern. Since  $\mathcal{A}_{\mathrm{Attn}}^{\mathcal{D}}$  operates at a pattern level, the target of inference is the pattern derived from embedding the target  $T$  , denoted as  $v\in \mathbb{R}^{d_x}$  . The underlying intuition of this approach involves configuring an attention head to memorize the input batch while excluding the target pattern. This configuration introduces a measurable discrepancy between the output of the filtered attention head and that of a non- filtered head. The resulting gap can then be exploited to infer the victim's data. A detailed explanation of the components of this attack are further elaborated in Appx. C.2 or readers can refer to (Vu et al., 2024) for the full description of the attack.

# 3.4. Attention-based AMI attack against Vision Transformer

While (Vu et al., 2024) focuses on exploiting the attention layer in LLMs to infer the presence of a pattern in a dataset, we further extend this attack to the continuous image domain, where we exploit the attention layer used in ViTs. We formulate the attack  $\mathcal{A}^D_{\mathrm{Attn}}$  in the context of attacking ViT models as follow: Given an image  $I\in \mathbb{R}^{H\times W\times C}$  , the image is divided into  $L$  non- overlapping patches (see Fig. 14). The patches are flattened into vectors, and projected into an embedding space using a linear projection matrix  $W_{\mathrm{embed}}$  . The result of this embedding layer is:

$$
x_{j} = \mathrm{Flatten}(I_{j})W_{\mathrm{embed}} + p_{j},\quad j = 1,\ldots ,L
$$

![](images/e4e8550a78b210556efef3877d991b6a6adbffcb822d4d3275d207a60df25ef6.jpg)  
Figure 1. Active inference security game under LDP: a random bit  $b$  determines the state of the data  $D$  (i), the server  $\mathcal{A}^{\mathcal{D}}$  specifies  $\Phi$  (ii) and  $\theta$  (iii), gradients on LDP-protected data  $D^{\prime}$  are sent back (iv), and  $\mathcal{A}^{\mathcal{D}}$  guesses  $\theta$  (v).

where  $I_{j} \in \mathbb{R}^{\frac{H}{\sqrt{L}} \times \frac{W}{\sqrt{L}} \times G}$  represents the  $j$ - th patch of the image  $I$  and  $p_{j}$  is the corresponding positional encoding. The resulting set of embeddings  $\{x_{j}\}_{j = 1}^{L}$  is then passed through the Vision Transformer (ViT) architecture, where the attention mechanism operates on these embeddings. When incorporating LDP noise into ViTs, we apply the noise directly to these embeddings before they enter the attention layers. We employ a similar attack strategy to the one used in the attention- based AMI adversary, but in the context of the ViT's embeddings. Implementation details of the attack are given in Appx. G.2.

![](images/a5d5a30815f4279080e08578a235301064c63abf62c7050c2db8e68e00db5873.jpg)

(a) The protected version of the target  $\mathcal{M}^{\epsilon}(X)$  jumps out of  $B_{1}(T, \Delta^{\mathcal{X}})$ .

![](images/88b0becdecc8e444b75402c994fe73c618025cee8fc07cc1babf82484e673f4b.jpg)  
Figure 2. Scenarios when  $\mathcal{A}_{\mathrm{FC}}^{\mathcal{D}}$  fail.

# 4. Privacy Leakage Analysis

This section presents our theoretical analysis for assessing the risk of leaking membership information of users' local training data in FL under LDP. Given the security game

$\mathsf{Exp}_{\mathsf{LDP}}^{\mathsf{AMI}}$  defined in Section 3.1, we generalize the lower bound and upper bound for the advantage of the adversarial server  $\mathcal{A}_{\mathrm{FC}}^{\mathcal{D}}$  in Theorem 1 and Theorem 2, respectively. Finally, we provide the lower bound for the advantage of  $\mathcal{A}_{\mathrm{Attn}}^{\mathcal{D}}$  under LDP in Theorem 3.

# 4.1. FC-based AMI on LDP-Protected Data

We now theoretically show that the adversary  $\mathcal{A}_{\mathrm{FC}}^{\mathcal{D}}$  constructed in Subsect. 3.2 can also be used to expose the true privacy risks of LDP- protected data w.r.t. AMI in FL and state it in Theorem 1. First, we need to lay out some assumptions on the data and the LDP mechanisms.

The data  $X$  is assumed to be from a discrete alphabet  $\mathcal{X}$  in which the  $L_{1}$  metric is well- defined. In our analysis, we denote  $\mathcal{X}$  as the set of possible output values of the LDP algorithm (see remark 2). We denote  $\Delta^{\mathcal{X}} \coloneqq \min_{X, Y \in \mathcal{X}} \| X - Y\|_{L_{1}} / 2$ . Note that  $\Delta^{\mathcal{X}}$  is a statistic of  $\mathcal{D}$  and is known by the server. We denote  $B_{1}(X, \Delta^{\mathcal{X}})$  to be a ball of radius  $\Delta^{\mathcal{X}}$  centering around  $X$  in the  $L_{1}$  norm. Given an LDP mechanism  $\mathcal{M}$  with budget  $\epsilon$  applied on an alphabet  $\mathcal{X}$ ,  $P_{\mathcal{M}^{\epsilon}}$  denotes the probability that the protected version of a point is not in the ball of radius  $\Delta^{\mathcal{X}}$  centering at that point:  $P_{\mathcal{M}^{\epsilon}} \coloneqq \operatorname *{Pr}\left[\mathcal{M}^{\epsilon}(X) \notin B_{1}(X, \Delta^{\mathcal{X}})\right]$ . Intuitively, a smaller  $\epsilon$  would impose more LDP noise, resulting in a larger  $P_{\mathcal{M}^{\epsilon}}$ .

Theorem 1. Given the security game  $\mathsf{Exp}_{\mathsf{LDP}}^{\mathsf{AMI}}$  there exists an AMI adversary  $\mathcal{A}_{\mathrm{FC}}^{\mathcal{D}}$  whose time complexity is  $\mathcal{O}(d_{X}^{2})$  such that  $\mathbf{Adv}_{\mathsf{LDP}}^{\mathsf{AMI}}(\mathcal{A}_{\mathsf{FC}}^{\mathcal{D}}) = 1 - \frac{n + |X| - 1}{|X| - 1} P_{\mathcal{M}^{\epsilon}}$ , where  $n$  is the size of the dataset  $D$ ,  $|\mathcal{X}|$  is the cardinality of the possible output values of the LDP- mechanism and  $P_{\mathcal{M}^{\epsilon}}$  is the probability that the LDP- mechanism makes the protected version of data point inside the neighborhood of another data point. (Proof in Appx. D.1)

We use  $\mathcal{A}_{\mathrm{FC}}^{\mathcal{D}}$  constructed in Subsect. 3.2 with  $\tau^{\mathcal{D}} = \Delta^{\mathcal{X}}$  to show Theorem 1. Given input  $X$ , target  $T$  and LDP mechanism  $\mathcal{M}^{\epsilon}$ , the goal of  $\mathcal{A}_{\mathrm{FC}}^{\mathcal{D}}$  is to configure the first two FC layers so that the first row of the second layer computes  $z_{0} \coloneqq \max \{\tau^{\mathcal{D}} - \| \mathcal{M}^{\epsilon}(X) - T\|_{L_{1}}, 0\}$ . If  $\| \mathcal{M}^{\epsilon}(X) - T\|_{L_{1}} < \tau^{\mathcal{D}}$ , then  $z_{0}$  activates and the gradient of  $b_{2}[1]$  is

non- zero. For  $\tau^D = \Delta^{\mathcal{X}}$  , if  $\mathcal{M}^{\epsilon}(X)\notin B_{1}(T,\Delta^{\mathcal{X}})$  , then  $z_{0} = 0$  , leaving the gradient zero. Conversely, if  $\mathcal{M}^{\epsilon}(X)\in$ $B_{1}(T,\Delta^{\mathcal{X}})$  , then  $z_{0} > 0$  and the gradient  $\dot{\theta} (b_{2}[1]) > 0$  . The adversary uses the gradient of  $b_{2}[1]$  as an indicator of the presence of  $T$  in the local data. A non- zero gradient implies  $T$  exists in the data, while a zero gradient indicates it does not. Intuitively,  $\mathcal{A}_{\mathrm{FC}}^D$  fails if either (i) the protected version of the data  $\mathcal{M}^{\epsilon}(X)$  jumps out of  $B_{1}(T,\Delta^{\mathcal{X}})$  when  $X = T$  or (ii) there is an  $X\neq T$  such that  $\mathcal{M}^{\epsilon}(X)\in B_{1}(T,\Delta^{\mathcal{X}})$  These scenarios are illustrated in Fig. 2. The probabilities of the two events are bounded by  $P_{\mathcal{M}^{\epsilon}}$  and  $nP_{\mathcal{M}^{\epsilon}} / (|\mathcal{X}| - 1)$  respectively (see Appx. D.1). Theorem 1 demonstrates the trade- off between privacy and data utility: a highly protected data would have high  $P_{\mathcal{M}^{\epsilon}}$  , thus lowering the advantage of the adversary; however, its distortion from the original data is large as a result.

Remark 1. Lower bound of Theorem 1. It is non- trivial to obtain  $P_{\mathcal{M}^{\epsilon}}$  for Theorem 1 due to the dependencies on the data as well as the specific LDP mechanisms. To demonstrate the intuition behind the proof, we provide an example of how to derive  $P_{\mathcal{M}^{\epsilon}}$  for Generalized Random Response GRR (Warner, 1965), a classical LDP algorithm, and the corresponding theoretical lower bound for GRR- protected data in Theorem 4 (Appx. D). We also simulate the lower theoretical bound in Theorem 1 for data protected by LDP algorithms BitRand (Jiang et al., 2022), GRR, dBitFlipPM (Ding et al., 2017) and RAPPOR (Erlingsson et al., 2014) in Figs. 7 and 8. Those theoretical lower bounds are shown along with the success rates of some AMI attacks (discussed in Section 3) for comparison.

Remark 2. Cardinality of  $\mathcal{X}$ $|\mathcal{X}|$  is dependent on the specific LDP algorithm used. For binary LDP algorithms such as Binary Randomized Response (Warner, 1965) or RAPPOR (Erlingsson et al., 2014),  $|\mathcal{X}| = 2$  due to the binary nature of the values being perturbed (e.g., a "yes/no" or "0/1" response). In contrast, for generalized  $k$  - ary randomized response algorithms, such as Generalized Randomized Response GRR) or k- RAPPOR,  $|\mathcal{X}| = k$  ,where  $k > 2$  represents the cardinality of the set of possible output values of the algorithm. This allows for more nuanced privacypreserving mechanisms, where the response set consists of  $k$  different values, each with a specific probability distribution determined by the privacy parameters of the algorithm. In modern bit- flipping algorithms such as OME or BitRand, the original data or embedding features are first converted into binary vectors of size  $b$  . The LDP mechanisms are then applied on top of those binary representations of the signal by flipping random bits. In this case,  $|\mathcal{X}| = 2^{b}$  . For large enough b,  $\begin{array}{r}\frac{n + |\mathcal{X}| - 1}{|\mathcal{X}| - 1}\approx 1 \end{array}$  and  $\begin{array}{r}\mathbf{Adv}_{\mathrm{LDP}}^{\mathrm{AMI}}(\mathcal{A}_{\mathrm{FC}}^D)\approx 1 - P_{\mathcal{M}^{\epsilon}} \end{array}$

Theorem 2. For all AMI adversary  $\mathcal{A}$  of the security game  $\mathsf{Exp}_{\mathsf{LDP}}^{\mathsf{AMI}}$  we have

$$
\mathbf{Adv}_{\mathrm{LDP}}^{\mathrm{AMI}}(\mathcal{A}_{\mathrm{FC}}^D)\leq \frac{e^\epsilon - 1}{e^\epsilon + 1} \tag{4}
$$

Proof in Appx.  $F$

This theorem shows the theoretical upper bound for  $\mathbf{Adv}_{\mathrm{LDP}}^{\mathrm{AMI}}(\mathcal{A}_{\mathrm{FC}}^D)$  given the privacy budget  $\epsilon$  . Accordingly, we measure the adversary's attack success rate as  $\begin{array}{r}\frac{1}{2} (1+ \end{array}$ $\mathbf{Adv}_{\mathrm{LDP}}^{\mathrm{AMI}}(\mathcal{A}_{\mathrm{FC}}^D))$  (see Equation 1) and plot the theoretical upper bound alongside the lower bound of AMI attack success rate and empirical success rate against real- world datasets under LDP protection in Figs. 7 and 8.

# 4.2. Attention-based AMI on LDP-Protected Data

Our theoretical result on the vulnerability of private data to Attention- based AMI attack under LDP is quantified on Separation of Patterns, an intrinsic measure of data:

Definition 2. (Separation of Patterns (Ramsauer et al., 2021)). For a pattern  $x_{i}$  in a data point  $\begin{array}{rl}{X}&{=}\end{array}$ $\{x_{j}\}_{j = 1}^{N_{X}}$  its separation  $\Delta_{i}$  from  $X$  is  $\Delta_{i}$ $\begin{array}{rl}{\coloneqq} & {} \end{array}$ $\begin{array}{r}\min_{j,j\neq i}\left(x_i^\top x_i - x_i^\top x_j\right) = x_i^\top x_i - \max_jx_i^\top x_j \end{array}$  .We say the pattern  $i$  is separated from the data point  $X$  if  $\Delta_{i} > 0$  .We say  $X$  is  $\Delta$  - separated if  $\Delta_{i}\geq \Delta$  for all  $i\in \{1,\dots ,N_X\}$  .A data  $D$  is  $\Delta$  separated if all  $X$  in  $D$  are  $\Delta$  - separated.

For the attention- based attack, we represent the victim's dataset as  $D = \{X_{i}\}_{i = 1}^{n}$  , where  $X_{i}\in \mathcal{X}$  , and  $\mathcal{X}\subseteq$ $\mathbb{R}^{d_X\times N_X}$  . For any 2- dimensional array  $X$  , each column  $x_{j}\in \mathbb{R}^{d_{X}}$  is referred to as a pattern. Since the LDP mechanisms are generally applied at a pattern level in 2- dimensional data (Qu et al., 2021; Yue et al., 2021), the distortion imposed by LDP is modeled by a noise  $r_i$  added to each pattern:  $X^{\epsilon} = \mathcal{M}^{\epsilon}(X) = \{x_{i} + r_{i}\}_{i = 1}^{N_{X}}$  .We assume  $r_i$  is bounded by a norm budget  $R^{\epsilon}$  that is defined by specific mechanisms and applications.  $M$  denotes the maximum  $L_{2}$  - norm of all patterns, defined as  $\begin{array}{r}M = \max_{X\in D}\max_{x_j\in X}\| x_j\| \end{array}$  .The adjusted pattern's norm is then upper- bounded by  $M^{\epsilon} = \sqrt{M^{2} + R^{\epsilon^{2}}}$  .Regarding data separation under LDP, denoted by  $\Delta^{\epsilon}$  ,whose value is not easily obtainable even when the LDP mechanism is known, we can generally expect  $\Delta^{\epsilon}\geq \Delta$  .The reason is, as the noise  $r_i$  is independent of the patterns, it makes the patterns less aligned. This intuition is demonstrated via an example in Appx. D.3.

The notion of  $\Delta^{\epsilon}$  separated helps capture the intrinsic difficulty of the data for the inference task: the less separating the data, i.e., a smaller  $\Delta^{\epsilon}$  , the harder for the adversary to detect the patterns. However, it is not beneficial to impose a low separation on the data in practice since it would impair the model's performance. Note that, if  $D$  is considered as the data after preprocessing,  $\Delta^{\epsilon}$  can be manipulated by the choice of preprocessing methods for the FL model, which are often specified by the server. We are now ready to state Theorem 3 that analyzes the vulnerability of LDP- protected data to Attention- based AMI in FL.

![](images/b986136b16562513760c3ec111d9c488744311fc64914db095915c892df7cd92.jpg)  
Figure 3. The adversarial server exploits self-attention mechanism to conduct inference attack of victim's protected local training data  $D^{\prime}$  in FL: If  $x_{i}$  in the data equals to the target pattern  $v$ , the input to the filtered attention heads is the perturbed version  $x_{i}^{\epsilon}$  and the output  $z_{i}^{1}$  of the filtered head is close to the protected pattern's average  $X^{\epsilon}$  instead of  $x_{i}^{\epsilon}$ . This creates non-zero gradients on weights computing on the difference of attention heads' outputs. The attack fails when the added noise is large and the embedding of protected data overlaps at the center of the embedding.

Theorem 3. Given a  $\Delta^{\epsilon}$ - separated data  $D^{\mathcal{M}_{\epsilon}}$  (the LDP- protected version of the data  $D$ ) with i.i.d patterns of the security game  $\mathsf{Exp}_{\mathrm{LDP}}^{\mathrm{AMI}}$ , for any  $\beta > 0$  large enough such that

$$
\Delta^{\epsilon}\geq \frac{2}{\beta N_{X}} +\frac{1}{\beta}\log (2(N_{X} - 1)N_{X}\beta M^{\epsilon 2}), \tag{5}
$$

there exists an AMI adversary,  $\mathcal{A}_{\mathrm{Attn}}^{\mathcal{D}}$ , that exploits the self- attention layer with a time complexity of  $\mathcal{O}(d_X^3)$  such that  $\mathbf{Adv}_{\mathrm{LDP}}^{\mathrm{AMI}}(\mathcal{A}_{\mathrm{Attn}}^{\mathcal{D}})$  is lower bounded by:

$$
\begin{array}{rl} & {\mathbf{Adv}_{\mathrm{LDP}}^{\mathrm{AMI}}(\mathcal{A}_{\mathrm{Attn}}^{\mathcal{D}})\geq P_{\mathrm{proj}}^{D^{\mathcal{M}_{\epsilon}}}\left(\frac{1}{\beta N_{X}M^{\epsilon}}\right)}\\ & {\qquad +P_{\mathrm{proj}}^{D^{\mathcal{M}_{\epsilon}}}\left(\frac{1}{\beta N_{X}M^{\epsilon}}\right)^{2nN_{X}}}\\ & {\qquad -P_{\mathrm{box}}^{D^{\mathcal{M}_{\epsilon}}}\left(3\bar{\Delta}^{\epsilon} + \beta (m_{max}^{\epsilon})^{2}R^{\epsilon}\right) - 1} \end{array} \tag{6}
$$

where  $\bar{\Delta}^{\epsilon}\coloneqq 2M^{\epsilon}(N_{X} - 1)\exp (2 / N_{X} - \beta \Delta^{\epsilon})$  and  $D^{\mathcal{M}_{\epsilon}}$  is the distribution of the protected data  $D^{\mathcal{M}_{\epsilon}}$  induced by the original data distribution  $\mathcal{D}$  and the LDP- mechanism  $\mathcal{M}_{\epsilon}$ .  $m_{x}^{\epsilon} = \frac{1}{N_{X}}\sum_{i = 1}^{N_{X}}x_{i}^{\epsilon}$  is the arithmetic mean of all LDP- protected patterns and  $m_{max}^{\epsilon} = \max_{1\leq i\leq N_{X}}\| x_{i} - m_{x}^{\epsilon}\|$ . Here,  $P_{\mathrm{proj}}^{D^{\mathcal{M}_{\epsilon}}}(\delta)$  is the probability that the projected component between two independent patterns drawn from  $D^{\mathcal{M}_{\epsilon}}$  is smaller than  $\delta$  and  $P_{\mathrm{box}}^{D^{\mathcal{M}_{\epsilon}}}(\delta)$  is the probability that a random pattern drawn from  $D^{\mathcal{M}_{\epsilon}}$  is in the cube of size  $2\delta$  centering at the arithmetic mean of the patterns in  $D^{\mathcal{M}_{\epsilon}}$ . (Proof in Appx. D.4)

The key step in proving Theorem 3 is to demonstrate that the configuration of the self- attention model, specified in Appendix C.2, behaves as outlined in Fig. 3. Given any input pattern  $x_{i}\in X$ , the attention heads process the LDP- protected version of the pattern,  $x_{i} + r_{i}$ . Let  $v$  denote the target pattern. If  $v$  is not present in the victim's training dataset, i.e.,  $x_{j}\neq v$  for all  $1\leq j\leq N_{X}$ , then, using Lemma 1 (Appendix D.2), which builds on the Exponentially Small Retrieval Error Theorem for the attention layer (Ramsauer et al., 2021), we show that  $z_{1}^{h}\approx x_{i} + r_{i}$  and  $z_{2}^{h}\approx x_{i} + r_{i}$ , both with a probability lower- bounded by:

$$
P_{\mathrm{proj}}^{D^{\mathcal{M}_{\epsilon}}}\left(\frac{1}{\beta N_X M^{\epsilon}}\right).
$$

This probability governs the false- positive error of the attack.

If there exists  $x_{i} = v$  (i.e.,  $v$  is in the training dataset), the weights of attention head 1 filter  $v$  and output:

$$
z_{1}^{h} = X^{\epsilon}\mathrm{softmax}\big(\beta X^{\epsilon^{\top}}(r_{i} - \bar{r}_{i}^{v})\big),
$$

where  $\bar{r}_{i}^{v}$  is the projection of  $r_i$  onto  $v$ . If  $R^{\epsilon}$  is small enough,  $z_{1}^{h}\approx \bar{X}^{\epsilon}$ . By computing the difference between the two heads,  $|z_{i}^{1} - z_{i}^{2}|$ , the adversary can infer the presence of  $v$  in  $X$ . For a hyperparameter  $\gamma$ , if  $|z_{i}^{1} - z_{i}^{2}| > \gamma$ , then  $v\in X$ . Conversely, if  $|z_{i}^{1} - z_{i}^{2}|< \gamma$ , then  $v\notin X$ . We select  $\gamma = 2\bar{\Delta}^{\epsilon}$ , a choice justified in Appendix D.4.

False- negative errors occur when the embedding of protected data overlaps at the center of the embedding space, causing  $|z_{i}^{1} - z_{i}^{2}|< \gamma$  even when  $v\in X$ . By using the mean value theorem and bounding the Jacobian of the attention's forwarding function, this error is upper bounded by:

$$
P_{\mathrm{box}}^{D^{\mathcal{M}_{\epsilon}}}\big(3\bar{\Delta}^{\epsilon} + \beta (m_{\mathrm{max}}^{\epsilon})^{2}R^{\epsilon}\big).
$$

As noise increases, the cube of size  $6\bar{\Delta}^{\epsilon} + 2\beta (m_{\mathrm{max}}^{\epsilon})^{2}R^{\epsilon}$  covers more patterns and causes  $P_{\mathrm{box}}^{D^{\mathcal{M}_{\epsilon}}}$  to increase. At high noise levels, the attention outputs for all patterns are more likely to cluster near the center of the embedding space, as illustrated in Fig. 4. This leads to higher false- negative rates for the attack. However, models trained on such noisy data generally exhibit poor performance since patterns whose embeddings overlap in these central regions become indistinguishable. This results in  $P_{\mathrm{box}}^{D^{\mathcal{M}_{\epsilon}}}\approx 1$  for large enough  $R^{\epsilon}$ , causing the advantage to drop sharply regardless of dimensionality as the cube fully encloses the patterns. Our simulations of Eq. (6) for one- hot and spherical data are shown in Fig. 5 and Fig. 6, respectively.

Remark 3.  $\Delta^{\epsilon}$  vs. the advantage's lower bound (6). A larger  $\Delta^{\epsilon}$  allows a smaller  $\beta$  to satisfy (5) and makes  $P_{\mathrm{proj}}^{D^{\mathcal{M}_{\epsilon}}}\left(\frac{1}{\beta N_X M^{\epsilon}}\right)$  larger and  $P_{\mathrm{box}}^{D^{\mathcal{M}_{\epsilon}}}\left(3\bar{\Delta}^{\epsilon} + \beta (m_{\mathrm{max}}^{\epsilon})^{2}R^{\epsilon}\right)$  smaller.

Remark 4. The most vulnerable embedding. The lower bound in (6) would be optimized with one- hot data.

![](images/62c72f454795defced271f3901016c22149cda3c311a3c3946d5779974b80437.jpg)  
Figure 4. For large  $R^{\epsilon}$  s.  $P_{\mathrm{box}}\approx$  1, the embedding of protected data overlaps at the center of the embedding and impairs data utility.

![](images/1617222d2c67c7a954d98532782181b5acd66380751781a079b3b252a6fe7497.jpg)  
Figure 5.  $\mathbf{Adv}_{\mathrm{LDP}}^{\mathrm{AMI}}(\mathcal{A}_{\mathrm{Att}}^{\mathcal{D}})$  on one-hot data using Monte-Carlo simulation

![](images/efee46a76215088b9d88add98f34478f7f6e6c4ba70efd722afda6ca61d9a01d.jpg)  
Figure 6.  $\mathbf{Adv}_{\mathrm{LDP}}^{\mathrm{AMI}}(\mathcal{A}_{\mathrm{Att}}^{\mathcal{D}})$  on spherical data using Monte-Carlo simulation

Since it has no alignment among patterns,  $\Delta^{\epsilon}$  achieves its maximum which is the pattern's norm. Furthermore,  $P_{\mathrm{proj}}^{\mathcal{D}^{\mathcal{M}_{\epsilon}}}\left(\frac{1}{\beta N_{X}M^{\epsilon}}\right)$  is 1 because all patterns are orthogonal to each other. Finally, since there is no pattern at the center of one- hot data, we can select a very large  $\beta$  so that  $P_{\mathrm{box}}^{\mathcal{D}^{\mathcal{M}_{\epsilon}}}\left(3\Delta^{\epsilon} + \beta (m_{\mathrm{max}}^{\epsilon})^{2}R^{\epsilon}\right) = 0$ , given  $R^{\epsilon}$  is small enough.

Remark 5. Asymptotic behavior of the advantage (6). For high dimensional data, i.e.,  $d_{X}\rightarrow \infty$ , two random points are surely almost orthogonal  $\left(P_{\mathrm{proj}}^{\mathcal{D}^{\mathcal{M}_{\epsilon}}}\left(\frac{1}{\beta N_{X}M^{\epsilon}}\right)\rightarrow 1\right)$ , and a random point is almost always at the boundary (Blum et al., 2020). Therefore,  $P_{\mathrm{box}}^{\mathcal{D}^{\mathcal{M}_{\epsilon}}}\left(3\Delta^{\epsilon} + \beta (m_{\mathrm{max}}^{\epsilon})^{2}R^{\epsilon}\right)\rightarrow 0$  for small enough  $R^{\epsilon}$  and  $\beta$ . This phenomenon can be seen in one- hot data (see Fig. 5). For spherical data, the amount of noise needed for  $P_{\mathrm{box}}^{\mathcal{D}^{\mathcal{M}_{\epsilon}}}\left(3\Delta^{\epsilon} + \beta (m_{\mathrm{max}}^{\epsilon})^{2}R^{\epsilon}\right)\rightarrow 1$  is smaller, and the cut- off noise's norm gradually decreases as the input dimension increases.

Remark 6. Impact of  $\beta$ . Increasing the hyper- parameter  $\beta$  in  $\mathcal{A}_{\mathrm{Att}}^{\mathcal{D}}$  (Algo. 3, Appx. C.2) would raise the memorization of the attention layer (Ramsauer et al., 2021). (Vu et al., 2024) showed experimentally that increasing  $\beta$  leads to better adversarial success rates against unprotected data. For LDP- protected data, this is not the case, as increasing  $\beta$  also increases  $P_{\mathrm{box}}^{\mathcal{D}^{\mathcal{M}_{\epsilon}}}\left(\beta \Delta^{\epsilon} + \beta (m_{\mathrm{max}}^{\epsilon})^{2}R^{\epsilon}\right)$ , reducing the lower bound of  $\mathbf{Adv}_{\mathrm{LDP}}^{\mathrm{AMI}}(\mathcal{A}_{\mathrm{Att}}^{\mathcal{D}})$ . We provide experiments on the impact of  $\beta$  on  $\mathbf{Adv}_{\mathrm{LDP}}^{\mathrm{AMI}}(\mathcal{A}_{\mathrm{Att}}^{\mathcal{D}})$  in Sect. 5.

# 5. Experiments

This section demonstrates the practical risks of leaking private data in FL. In particular, we implement the FC- based  $\mathcal{A}_{\mathrm{FC}}^{\mathcal{D}}$  and attention- based  $\mathcal{A}_{\mathrm{Att}}^{\mathcal{D}}$  adversaries, and evaluate their success rates in synthetic and real- world datasets. Implementation details are given in Appendix. G

Datasets and embedding. Our experiments use two synthetic and three real- world datasets. The synthetic datasets include one- hot encoded data and spherical data (points on the unit sphere). The real- world datasets, including CIFAR10, CIFAR100 (Krizhevsky et al., 2009), and ImageNet (Krizhevsky et al., 2012), are processed using pre- trained embedding modules to obtain data  $D$  for our threat models. We also use the ImageNet dataset, a large- scale benchmark consisting of labeled images across 1,000 categories (Deng et al., 2009). For ResNet, we extract feature embeddings with Img2Vec (Safka, 2021), while for ViTs, we use pretrained foundation models provided by the authors on HuggingFace (Dosovitskiy et al., 2021). We refer readers to Appx. G.1 for more details.

LDP mechanisms. We use BitRand (Jiang et al., 2022), GRR (Warner, 1965), RAPPOR (Erlingsson et al., 2014), dBitFlipPM (Ding et al., 2017) as LDP mechanisms for real- world datasets. Details about these algorithms are in Appx. G.4. We also provide some results on OME (Lyu et al., 2020) in Appx. H.1.

Results on synthetic datasets. Fig. 5 and Fig. 6 shows the impact of the L1 norm of  $R^{\epsilon}$  on the advantage of Attention- based AMI adversary on one- hot and spherical data, respectively. For one- hot data, as we increase  $d_{X}$ , random noise is almost always at the boundary and there is no pattern at the center of one- hot data. This reduces the likelihood that the protected data's embedding overlaps with the center of the embeddings, increasing the lower bound of the adversary's advantage in (6). On the other hand, for spherical data, the advantage drops sharply when  $R^{\epsilon}$  increases, regardless of the dimension of the data.

Results of FC- based AMI adversary. Figures 7 and 8 show the success rates of FC- based AMI attacks against 4 different LDP algorithms on CIFAR10 and CIFAR100. The theoretical lower and upper bound on the adversary's attack success rate can be derived directly from the theoretical lower and upper bound of  $\mathbf{Adv}_{\mathrm{LDP}}^{\mathrm{AMI}}(\mathcal{A}_{\mathrm{FC}}^{\mathcal{D}})$  using Eq. 1. Across these LDP mechanisms, the amount of LDP noise needed to protect against AMI attack significantly reduces the model's utility. For example, for BitRand- protected CIFAR10, to make the inference rate lower than 80% (yellow

![](images/bfeb9b939ad7810d9195468b82ac51ae3e226e9ba11291aaca1db192fa50f663.jpg)  
Figure 7. Theoretical upper/lower bound and empirical results on the attack success rates of FC-based AMI adversaries against CIFAR10 dataset protected by BitRand (a), GRR (b), RAPPOR (c) and dBitFlipPM (d).

![](images/d4a2e0fed7f8a89561ff92d1142255575db2064ead41b116dc082016205e6ac3.jpg)  
Figure 8. Theoretical upper/lower bound and empirical results on the attack success rates of FC-based AMI adversaries against CIFAR100 dataset protected by BitRand (a), GRR (b), RAPPOR (c) and dBitFlipPM (d).

line), the model has to suffer at least  $20\%$  accuracy loss. The theoretical lower bound of the attack's success rate corroborates the empirical success rate of  $\approx 100\%$  when  $\epsilon = 8$ . For both real- world datasets,  $\mathcal{A}_{\mathrm{FC}}^{\mathcal{D}}$  achieves near  $100\%$  success rate when  $\epsilon$  approaches 6. Among the evaluated mechanisms, GRR, RAPPOR, and dBitFlipPM preserve higher model accuracy at lower  $\epsilon$  values but expose the model to greater privacy risks, as indicated by both the empirical and theoretical attack success rates approaching  $100\%$  at  $\epsilon = 5$  and 6, respectively.

Results of Attention- based AMI adversary. Experiments on CIFAR10 and ImageNet (1000 classes) are conducted on ViT- B- 32- 224 and ViT- B- 32- 384, respectively (Fig. 9). For LDP- protected data, the inference success rate of  $\mathcal{A}_{\mathrm{Attn}}^{\mathrm{D}}$  approaches  $100\%$  when  $\epsilon = 3$  or higher. At this privacy budget, model performance suffers significantly. Fig. 9 also illustrates the impact of batch size on the attack success rates, showing that the proposed  $\mathcal{A}_{\mathrm{Attn}}^{\mathcal{D}}$  performs consistently across different batch sizes.

Impact of  $\beta$  on  $\mathbf{Adv}_{\mathrm{LDP}}^{\mathrm{AMI}}(\mathcal{A}_{\mathrm{Attn}}^{\mathcal{D}})$ . As discussed in remark 6, increasing  $\beta$  also increases  $P_{\mathrm{box}}^{\mathcal{D}^{\mathcal{M}_{\epsilon}}}\left(3\Delta^{\epsilon} + \beta (m_{\mathrm{max}}^{\epsilon})^{2}R^{\epsilon}\right)$  and in turn makes the lower bound of Eq. (6) smaller. We illustrate this behavior in Fig.10. The more we increase  $\beta$ , the less likely the adversary succeeds. Note that we still need to choose a  $\beta$  large enough for Eq. 5 to hold. We further discuss the choice of hyperparameters in Appendix G.3. Given the assumption that the server has knowledge of the client's data distribution (as outlined in the AMI threat models in Section 3.1), the server can simulate the client's data to compute a minimally sufficient value for  $\beta$ . When doing experiments, we found that setting  $\beta$  to a reasonably small value (e.g., 0.01) yielded consistently good results across realistic  $\epsilon$  values and datasets/LDP mechanisms. As illustrated in Figure 10, for LDP- protected data,  $\beta = 0.01$  generally achieves better success rates under small  $\epsilon$ .

Empirical results on NLP datasets. In section 4.2, the distortion imposed by LDP is modeled by a noise  $r_i$  added to each pattern:  $X^{\epsilon} = \mathcal{M}^{\epsilon}(X) = \{x_i + r_i\}_{i = 1}^{N_X} = \{x_i^{\epsilon}\}_{i = 1}^{N_X}$ . In our analysis, we assume  $x_i$  and  $r_i$  to be continuous, and the impact of LDP noise can be visualized in Fig. 4. For NLP data, both the data and the noise should be modeled as discrete, hence our theoretical analysis might not directly apply to the NLP scenario. The key challenge is that in NLP, tokens are typically represented as discrete embeddings, and adding continuous noise is not meaningful in this context. Therefore, a separate theoretical framework would be required to account for the discrete nature of NLP data.

However, it is important to note that the attack still experimentally works against both vision and NLP data. To demonstrate this, we conducted comprehensive experiments across 4 NLP datasets (IMDB (Maas et al., 2011), Yelp (Zhang et al., 2015), Twitter (Saravia et al., 2018), Finance (Casanueva et al., 2020)), 4 models (BERT (Devlin et al., 2019), RoBERTa (Liu et al., 2019), GPT- 1 (Radford et al.,

![](images/29a7eea57babe798dccb8e33afcf016df240853af9dab57fa0f8d0467f9114ee.jpg)  
Figure 9. Comparison of success rates of Attention-based AMI adversaries against CIFAR10 protected by BitRand, GRR, RAPPOR, and dBitFlipPM (a) as well as the privacy-utility trade-off and the impact of batch size on attack success on BitRand-protected data (b,c). Here, Attn-10 means the attack is conducted with batch size 10, and so on. Batch size is 10 if not explicitly mentioned.

![](images/46f3d41bf1a3c901d8776892d30e01c8a54f69805310479c401f36304a089a14.jpg)  
Figure 10. Impact of  $\beta$  on  $\mathbf{Adv}_{\mathrm{LDP}}^{\mathrm{AMI}}(\mathcal{A}_{\mathrm{Attn}}^{\mathcal{D}})$

2018), DistilBERT (Sanh et al., 
2019), and 3 LDP algorithms (GRR, RAPPOR, dBitFlipPM). The results given in Appx. H.2 indicate that privacy risks persist even for LLMs, depending on the privacy budget. To explore more in depth the impact of different LDP mechanisms on the attack success rates, we also conduct an ROC analysis of the attack success rates (on IMDB dataset) in Appx. H.3.

# 6. Conclusion

This work studies the formal threat models for AMI attacks with dishonest FL servers, effectively and rigorously providing the theoretical bound on the vulnerabilities of FL under LDP protection. We also provide experimental evidence for the high success rates of active inference attacks under certain LDP mechanisms. The results imply that LDP- protected data might be vulnerable to inference attacks in FL with dishonest servers and clients should carefully consider the tradeoff between privacy and utility.

# Acknowledgments

This work was supported in part by the National Science Foundation under grants III- 2416606 and SaCT- 1935923.

This work was authored in part by the National Renewable Energy Laboratory (NREL) for the U.S. Department of En ergy DOE) under Contract No. DE- AC36- 08GO28308. Funding provided by the Laboratory Directed Research and Development (LDRD) Program at NREL. The views expressed in the article do not necessarily represent the views of the DOE or the U.S. Government. The U.S. Government retains and the publisher, by accepting the article for publication, acknowledges that the U.S. Government retains a nonexclusive, paid- up, irrevocable, worldwide license to publish or reproduce the published form of this work, or allow others to do so, for U.S. Government purposes.

# Impact Statement

Our findings demonstrate that LDP- protected data can still be compromised by dishonest FL servers, particularly under practical scenarios. Additionally, the study highlights the significant trade- offs between privacy and model utility, showing that the noise levels necessary to mitigate these attacks often lead to substantial degradation in model performance.

Our work has immediate implications for the design and deployment of FL systems, emphasizing the need for stronger privacy safeguards and more robust defenses against AMI attacks. Researchers and practitioners are encouraged to explore enhanced privacy- preserving techniques that balance security with model effectiveness. Furthermore, policymakers can leverage these insights to establish clearer privacy guidelines for the implementation of FL in sensitive applications, such as healthcare and finance.

By providing a theoretical framework alongside empirical evidence, our paper serves as a critical resource for understanding and addressing privacy threats in decentralized machine learning environments. The results pave the way for further advancements in secure and privacy- preserving AI technologies.

# References

ReferencesArachchige, P. C. M., Bertok, P., Khalil, I., Liu, D., Camtepe, S., and Atiquzzaman, M. Local differential privacy for deep learning. IEEE Internet of Things Journal, 7(7): 5827- 5842, 2019. Aziz, R., Banerjee, S., Bouzefrane, S., and Le Vinh, T. Exploring homomorphic encryption and differential privacy techniques towards secure federated learning paradigm. Future internet, 15(9):310, 2023. Blum, A., Hopcroft, J., and Kannan, R. Foundations of data science. Cambridge University Press, 2020. Bonawitz, K., Ivanov, V., Kreuter, B., Marcedone, A., McMahan, H. B., Patel, S., Ramage, D., Segal, A., and Seth, K. Practical secure aggregation for privacy- preserving machine learning. In proceedings of the 2017 ACM SIGSAC Conference on Computer and Communications Security, pp. 1175- 1191, 2017. Casanueva, I., Temcinas, T., Gerz, D., Henderson, M., and Vulić, I. Efficient intent detection with dual sentence encoders. In Wen, T.- H., Celikyilmaz, A., Yu, Z., Papangelis, A., Eris, M., Kumar, A., Casanueva, I., and Shah, R. (eds.), Proceedings of the 2nd Workshop on Natural Language Processing for Conversational AI, pp. 38- 45, Online, July 2020. Association for Computational Linguistics. doi: 10.18653/v172020. nlp4convai- 1.5. URL https://aclanthology.org/2020. nlp4convai- 1.5/. Chen, J., Wang, W. H., and Shi, X. Differential privacy protection against membership inference attack on machine learning for genomic data. Pacific Symposium on Biocomputing, 26:26- 37, 2021. doi: 10.1142/9789811232701_0003. URL https://pubmed.ncbi.nlm.nih.gov/33691001/. Deng, J., Dong, W., Socher, R., Li, L.- J., Li, K., and Fei- Fei, L. Imagenet: A large- scale hierarchical image database. In 2009 IEEE conference on computer vision and pattern recognition, pp. 248- 255. Ieee, 2009. Devlin, J., Chang, M.- W., Lee, K., and Toutanova, K. Bert: Pre- training of deep bidirectional transformers for language understanding. In Proceedings of the 2019 conference of the North American chapter of the association for computational linguistics: human language technologies, volume 1 (long and short papers), pp. 4171- 4186, 2019. Ding, B., Kulkarni, J., and Yekhanin, S. Collecting telemetry data privately. Advances in Neural Information Processing Systems, 30, 2017. Dosovitskiy, A., Beyer, L., Kolesnikov, A., Weissenborn, D., Zhai, X., Unterthiner, T., Dehghani, M., Minderer,

M., Heigold, G., Gelly, S., Uszkoreit, J., and Houlsby, N. An image is worth 16x16 words: Transformers for image recognition at scale. In International Conference on Learning Representations, 2021. URL https://openreview.net/forum?id=YicbFdNTTy.Dwork, C. Differential privacy. In International colloquium on automata, languages, and programming, pp. 1- 12. Springer, 2006. Dwork, C., Roth, A., et al. The algorithmic foundations of differential privacy. Foundations and Trends in Theoretical Computer Science, 9(3- 4):211- 407, 2014. Erlingsson, U., Pihur, V., and Korolova, A. Rappor: Randomized aggregatable privacy- preserving ordinal response. In Proceedings of the 2014 ACM SIGSAC conference on computer and communications security, pp. 1054- 1067, 2014. Fang, H., Qiu, Y., Yu, H., Yu, W., Kong, J., Chong, B., Chen, B., Wang, X., Xia, S.- T., and Xu, K. Privacy leakage on dnns: A survey of model inversion attacks and defenses. arXiv preprint arXiv:2402.04013, 2024. Gao, J., Hou, B., Guo, X., Liu, Z., Zhang, Y., Chen, K., and Li, J. Secure aggregation is insecure: Category inference attack on federated learning. IEEE Transactions on Dependable and Secure Computing, 20(1):147- 160, 2021. He, K., Zhang, X., Ren, S., and Sun, J. Deep residual learning for image recognition. In Proceedings of the IEEE conference on computer vision and pattern recognition, pp. 770- 778, 2016. Hu, E. J., yelong shen, Wallis, P., Allen- Zhu, Z., Li, Y., Wang, S., Wang, L., and Chen, W. LoRA: Low- rank adaptation of large language models. In International Conference on Learning Representations, 2022a. URL https://openreview.net/forum?id=nZeVKeeFYf9. Hu, H., Salcic, Z., Sun, L., Dobbie, G., Yu, P. S., and Zhang, X. Membership inference attacks on machine learning: A survey. ACM Computing Surveys (CSUR), 54(11s):1- 37, 2022b.Jiang, X., Hu, H., On, T., Lai, P., Mayyuri, V. D., Chen, A., Shila, D. M., Larmuseau, A., Jin, R., Borcea, C., et al. Flsys: Toward an open ecosystem for federated learning mobile apps. IEEE Transactions on Mobile Computing, 23(1):501- 519, 2022. Kariyappa, S., Guo, C., Maeng, K., Xiong, W., Suh, G. E., Qureshi, M. K., and Lee, H.- H. S. Cocktail party attack: Breaking aggregation- based privacy in

federated learning using independent component analysis. In Krause, A., Brunskill, E., Cho, K., Engelhardt, B., Sabato, S., and Scarlett, J. (eds.), Proceedings of the 40th International Conference on Machine Learning, volume 202 of Proceedings of Machine Learning Research, pp. 15884- 15899. PMLR, 23- 29 Jul 2023. URL https://proceedings.mlr.press/v202/kariyappa23a. html. Krizhevsky, A., Hinton, G., et al. Learning multiple layers of features from tiny images. 2009. Krizhevsky, A., Sutskever, I., and Hinton, G. E. Imagenet classification with deep convolutional neural networks. Advances in neural information processing systems, 25, 2012. Lester, B., Al- Rfou, R., and Constant, N. The power of scale for parameter- efficient prompt tuning. In Moens, M.- F., Huang, X., Specia, L., and Yih, S. W.- t. (eds.), Proceedings of the 2021 Conference on Empirical Methods in Natural Language Processing, pp. 3045- 3059, Online and Punta Cana, Dominican Republic, November 2021. Association for Computational Linguistics. doi: 10.18653/v1/2021. emnlp- main. 243. URL https://aclanthology.org/2021. emnlp- main.243/. Liu, G., Tian, Z., Chen, J., Wang, C., and Liu, J. Tear: Exploring temporal evolution of adversarial robustness for membership inference attacks against federated learning. IEEE Transactions on Information Forensics and Security, 18:4996- 5010, 2023. Liu, Y., Ott, M., Goyal, N., Du, J., Joshi, M., Chen, D., Levy, O., Lewis, M., Zettlemoyer, L., and Stoyanov, V. Roberta: A robustly optimized bert pretraining approach. arXiv preprint arXiv:1907.11692, 2019. Lyu, L., Li, Y., He, X., and Xiao, T. Towards differentially private text representations. In Proceedings of the 43rd International ACM SIGIR Conference on Research and Development in Information Retrieval, pp. 1813- 1816, 2020. Ma, Y., Woods, J., Angel, S., Polychroniadou, A., and Rabin, T. Flamingo: Multi- round single- server secure aggregation with applications to private federated learning. In 2023 IEEE Symposium on Security and Privacy (SP), pp. 477- 496. IEEE, 2023. Maas, A., Daly, R. E., Pham, P. T., Huang, D., Ng, A. Y., and Potts, C. Learning word vectors for sentiment analysis. In Proceedings of the 49th annual meeting of the association for computational linguistics: Human language technologies, pp. 142- 150, 2011.

McMahan, B., Moore, E., Ramage, D., Hampson, S., and y Arcas, B. A. Communication- efficient learning of deep networks from decentralized data. In Artificial intelligence and statistics, pp. 1273- 1282. PMLR, 2017. Nasr, M., Shokri, R., and Houmansadr, A. Comprehensive privacy analysis of deep learning: Passive and active white- box inference attacks against centralized and federated learning. In 2019 IEEE symposium on security and privacy (SP), pp. 739- 753. IEEE, 2019. Ngo, K.- H., Ostman, J., Durisi, G., and Graell i Amat, A. Secure aggregation is not private against membership inference attacks. In Joint European Conference on Machine Learning and Knowledge Discovery in Databases, pp. 180- 198. Springer, 2024. Nguyen, T. and Thai, M. T. Preserving privacy and security in federated learning. IEEE/ACM Transactions on Networking, 2023. Nguyen, T., Thai, P., Tre'R, J., Dinh, T. N., and Thai, M. T. Blockchain- based secure client selection in federated learning. In 2022 IEEE International Conference on Blockchain and Cryptocurrency (ICBC), pp. 1- 9. IEEE, 2022. Nguyen, T., Lai, P., Tran, K., Phan, N., and Thai, M. T. Active membership inference attack under local differential privacy in federated learning. In International Conference on Artificial Intelligence and Statistics, pp. 5714- 5730. PMLR, 2023. Pan, Y., Chao, Z., He, W., Jing, Y., Hongjia, L., and Liming, W. Fedshe: privacy preserving and efficient federated learning with adaptive segmented ckks homomorphic encryption. Cybersecurity, 7(1):40, 2024. Pasquini, D., Francati, D., and Ateniese, G. Eluding secure aggregation in federated learning via model inconsistency. In Proceedings of the 2022 ACM SIGSAC Conference on Computer and Communications Security, pp. 2429- 2443, 2022. Qu, C., Kong, W., Yang, L., Zhang, M., Bendersky, M., and Najork, M. Natural language understanding with privacy- preserving bert. In Proceedings of the 30th ACM International Conference on Information & Knowledge Management, pp. 1488- 1497, 2021. Radford, A., Narasimhan, K., Salimans, T., Sutskever, I., et al. Improving language understanding by generative pre- training. 2018. Ramsauer, H., Schafil, B., Lehner, J., Seidl, P., Widrich, M., Gruber, L., Holzleitner, M., Adler, T., Kreil, D.,

Kopp, M. K., Klambauer, G., Brandstetter, J., and Hochreiter, S. Hopfield networks is all you need. In International Conference on Learning Representations, 2021. URL https://openreview.net/forum?id=tL89RnzIiCd.Safka, C. img2vec, 2021. URL https://github.com/christiansafka/img2vec. Accessed: 2025- 01- 31. Sanh, V., Debut, L., Chaumond, J., and Wolf, T. Distilbert, a distilled version of bert. smaller, faster, cheaper and lighter. arXiv preprint arXiv:1910.01108, 2019. Saravia, E., Liu, H.- C. T., Huang, Y.- H., Wu, J., and Chen, Y.- S. Carer: Contextualized affect representations for emotion recognition. In Proceedings of the 2018 conference on empirical methods in natural language processing, pp. 3687- 3697, 2018. Shokri, R., Stronati, M., Song, C., and Shmatikov, V. Membership inference attacks against machine learning models. In 2017 IEEE symposium on security and privacy (SP), pp. 3- 18. IEEE, 2017. Vu, M., Nguyen, T., Thai, M. T., et al. Analysis of privacy leakage in federated large language models. In International Conference on Artificial Intelligence and Statistics, pp. 1423- 1431. PMLR, 2024. Wang, T., Zhang, X., Feng, J., and Yang, X. A comprehensive survey on local differential privacy toward data statistics and analysis. Sensors, 20(24):7030, 2020. Warner, S. L. Randomized response: A survey technique for eliminating evasive answer bias. Journal of the American statistical association, 60(309):63- 69, 1965. Yang, H., Ge, M., Xue, D., Xiang, K., Li, H., and Lu, R. Gradient leakage attacks in federated learning: Research frontiers, taxonomy and future directions. IEEE Network, 2023. Yin, D., Hu, L., Li, B., and Zhang, Y. Adapter is all you need for tuning visual tasks. arXiv preprint arXiv:2311.15010, 2023. Yue, X., Du, M., Wang, T., Li, Y., Sun, H., and Chow, S. S. Differential privacy for text analytics via natural text sanitization. In Findings of the Association for Computational Linguistics: ACL- IJCNLP 2021, pp. 3853- 3866. Association for Computational Linguistics (ACL), 2021. Zaken, E. B., Goldberg, Y., and Ravfogel, S. Bitfit: Simple parameter- efficient fine- tuning for transformer- based masked language- models. In Proceedings of the 60th Annual Meeting of the Association for Computational Linguistics (Volume 2: Short Papers), pp. 1- 9, 2022.

Zhang, J., Zhang, J., Chen, J., and Yu, S. Gan enhanced membership inference: A passive local attack in federated learning. In ICC 2020- 2020 IEEE International Conference on Communications (ICC), pp. 1- 6. IEEE, 2020. Zhang, X., Zhao, J., and LeCun, Y. Character- level convolutional networks for text classification. Advances in neural information processing systems, 28, 2015.

# A. Appendix

This is the appendix of our paper Theoretically Unmasking Inference Attacks Against LDP- Protected Clients in Federated Vision Models. Its main content and outline are as follows:

Appendix B provides the details of the security games examined in this work.

Appendix C shows detailed description of FC- based and Attention- based AMI Attack in FL.

Appendix C.1: the description of FC- based adversary for AMI (Vu et al., 2024). - Appendix C.2: the description of attention- based adversary for AMI (Vu et al., 2024).

Appendix D proves the advantage of FC- based and Attention- based AMI Attack in FL under LDP.

Appendix D proves the advantage of FC- based and Attention- based AMI Attack in FL under LDP.- Appendix D.1: the proof of Theorem 1. - Appendix D.2 discusses attention layer's memorization capabilities through Exponentially Small Retrieval Error Theorem (Ransauer et al., 2021). - Appendix D.3: the impact of LDP mechanisms on data separation. - Appendix D.4: the proof of Theorem 3.

Appendix E proves the lower bound of the advantage of FC- based AMI attack under GRR mechanism.

Appendix F proves the upper bound of the advantage of FC- based AMI attack under LDP.

Appendix G provides the details of our experiments reported in the main manuscript.

Appendix G.1: details of the tested datasets. - Appendix G.2: implementation of Attention- based AMI Adversary against Vision Transformer - Appendix G.3: details choices of hyperparameters of the adversaries. - Appendix G.4: descriptions of the tested LDP mechanisms.

Appendix H provides additional experimental results.

Appendix H provides additional experimental results.- Appendix H.1: provides additional experiments on data protected by OME mechanism.- Appendix H.2 provides experimental results on NLP datasets.- Appendix H.3 provides ROC analysis of the attack success rates on IMDB dataset.

Appendix I discusses alternative privacy- preserving techniques such as SMPC and Homomorphic Encryption.

# B. Active Inference Threat Models as Security Games

This appendix provides the descriptions of the security games examined in our work. All games are conducted between a challenger/client, and an adversary/server in FL. The adversary is denoted by  $\mathcal{A}^D$  , in which the superscript  $\mathcal{D}$  indicates that the server knows the data distribution of the client's private data. At the beginning of the games, a random bit  $b$  is generated and it is used to decide whether the challenger's private data has a specific sample. The goal of the AMI adversary is to guess the bit  $b$  , which is equivalent to informing information on the challenger's data.

As pointed out briefly in Sect. 3.1, the adversarial server  $\mathcal{A}^D$  in all security games consists of three components  $\mathcal{A}_{\mathrm{INIT}}^D$ $\mathcal{A}_{\mathrm{ATTACK}}^D$  and  $\mathcal{A}_{\mathrm{GUESS}}^D$  . An illustration of their dynamics is provided in Fig. 1. To specify an adversary, for each security game, we need to describe how it determines the model  $\Phi$  for FL in  $\mathcal{A}_{\mathrm{INIT}}^D$  , how it crafts the model's parameters  $\theta$  in  $\mathcal{A}_{\mathrm{ATTACK}}^D$  and how it guesses the bit  $b$  in  $\mathcal{A}_{\mathrm{GUESS}}^D$  . The security games considered in this work are described below.

AMI on unprotected data  $\mathrm{Exp}_{\mathrm{NONE}}^{\mathrm{AMI}}(\mathcal{A}^D)$  : This security game is about the base AMI threat model, which is first formulated in (Nguyen et al., 2023) to study the threat of AMI in FL. While we do not directly study this security game, it serves as the foundation for  $\mathrm{Exp}_{\mathrm{LDP}}^{\mathrm{AMI}}(\mathcal{A}^D)$  .The subscript NONE indicates there is no defense mechanism applied on the data. The goal of the adversary is to decide if a target sample  $T$  is included in the training data  $D$  . Fig. 11 provides the pseudo- code of this security game.

![](images/9f8eaf2825d0f83b4eebcb96030b2dc1d4ad86900d1cea0de0a11ef7dba8c340.jpg)  
Figure 11. The AMI Threat Model as a Security Game.

![](images/eefef0e2b8a70e4658654f0dd25c73934356ff93d103b5dcd628384a0cdd446e.jpg)  
Figure 12. The AMI threat model under LDP mechanism as a security game.

AMI on LDP- protected data  $\mathsf{Exp}_{\mathrm{LDP}}^{\mathrm{AMI}}(\mathcal{A}^{\mathcal{D}})$  : This security game describes the AMI threat model when the data is protected by LDP mechanisms. The work (Nguyen et al., 2023) extends  $\mathsf{Exp}_{\mathrm{NONE}}^{\mathrm{AMI}}(\mathcal{A}^{\mathcal{D}})$  to obtain the formulation of  $\mathsf{Exp}_{\mathrm{LDP}}^{\mathrm{AMI}}(\mathcal{A}^{\mathcal{D}})$ . In this game, the client independently perturbs its training data sample  $D$  using an LDP- preserving mechanism  $\mathcal{M}$  to obtain a randomized local training set  $D' = \mathcal{M}^{\epsilon}(D) = \{\mathcal{M}^{\epsilon}(X)\}_{X\in D}$ . This randomized data  $D'$  is then used for the local training instead of  $D$ . As a result, the gradients that the FL server receives are computed on the protected data  $D'$  instead of  $D$ . Fig. 12 is the pseudo- code of this security game.

# C. FC-based and Attention-based AMI Attacks

C. FC-based and Attention-based AMI AttacksThis appendix reports the details of FC-based and Attention-based AMI Attacks. In Appx. C.1, we provide the descriptions of the FC-based AMI adversary proposed by (Vu et al., 2024) used in our analysis. Appx. C.2 presents the details of the Attention-based AMI adversary (Vu et al., 2024).

# C.1. FC-Based Adversary for AMI in FL

We now describe the AMI FC- based adversary  $\mathcal{A}_{\mathsf{FC}}^D$  proposed by (Vu et al., 2024) and mentioned in Sect. 3.2. The adversary consists of 3 components  $\mathcal{A}_{\mathsf{FC - INIT}}^D,\mathcal{A}_{\mathsf{FC - ATTACK}}^D$  and  $\mathcal{A}_{\mathsf{FC - GUESS}}^D$

Algorithm 1  $\mathcal{A}_{\mathsf{FC - ATTACK}}^D (T)$  exploiting fully- connected layer in AMI

Hyper- parameters:  $\tau^D\in \mathbb{R}^+$

1 #Configuring  $W_{1}\in \mathbb{R}^{2d_{X}\times d_{X}}$  and  $b_{1}\in \mathbb{R}^{2d_{X}}$  of the first FC  $W_{1}\gets \left[ \begin{array}{c}I_{d_{X}}\\ - I_{d_{X}} \end{array} \right],\quad b_{1}\gets \left[ \begin{array}{c} - T\\ T \end{array} \right]$

2 #Configuring the first row of  $W_{2}\in \mathbb{R}^{d\times 2d_{X}}$  and the first entry of  $b_{2}\in \mathbb{R}^{d}$  of the second FC  $W_{2}[1,:]\gets - 1_{2d_{X}}^{\top}$ $b_{2}[1]\gets \tau^{\mathcal{D}}$

3 Ret all weights and biases

Algorithm 2  $\mathcal{A}_{\mathsf{FC - GUESS}}^D (T,\dot{\theta})$  exploiting fully- connected layer in AMI

If the gradient of  $b_{2}[1]$  is non- zero, returns 1 if  $|\dot{\theta} (b_{2}[1])| > 0$  then Ret 1 end Ret 0

AMI initialization  $\mathcal{A}_{\mathsf{FC - INIT}}^D$  : The adversary's model employs fully connected (FC) layers for its first two layers. Given an input  $X\in \mathbb{R}^{d_{X}}$  , the attacker computes  $\mathrm{ReLU}(W_lX + b_l) = \max (0,W_lX + b_l)$  , where  $W_{l}$  and  $b_{l}$  denote the weights and biases of layer  $l$  , respectively. The dimensions of  $W_{1}$  and  $b_{1}$  are set to  $2d_{X}\times d_{X}$  and  $2d_{X}$  , respectively. For the second layer, the attack only analyzes a single output neuron, thus, requiring  $W_{2}$  to have only  $2d_{X}$  columns. We denote the parameters associated with this neuron as  $W_{2}[1,:]$  and  $b_{2}[1]$  . Models with additional parameters can still works, as surplus parameters can simply be disregarded.

AMI attack  $\mathcal{A}_{\mathsf{FC - ATTACK}}^D$  : The weights and biases of the first two FC layers are set as:

$$
W_{1}\gets \left[ \begin{array}{c}I_{d_{X}}\\ -I_{d_{X}} \end{array} \right],\quad b_{1}\gets \left[ \begin{array}{c} - T\\ T \end{array} \right],\quad W_{2}[1,:]\gets -1_{d_{X}}^{\top},\quad b_{2}[1]\gets \tau^{\mathcal{D}} \tag{7}
$$

where  $I_{d_{X}}$  is the identity matrix and  $1_{d_{X}}$  is the all- ones vector of size  $d_{X}$  . The hyperparameter  $\tau^D$  controls the total allowable distance between an input  $X$  and the target  $T$  , which can be determined from the distribution statistics. The pseudo- code of the attack is presented in Algo. 1.

AMI guess  $\mathcal{A}_{\mathsf{FC - ATTACK}}^D$  : In the guessing phase, the AMI server returns 1 if the gradient of  $b_{1}[1]$  is non- zero, and returns 0 otherwise. The pseudo- code of this step is shown in Algo. 2.

# C.2. Attention-Based Adversary for AMI in FL

We now describe the AMI attention- based adversary  $\mathcal{A}_{\mathrm{Att}}^D$  introduced in (Vu et al., 2024) and discussed in Sect. 3.3. The 3 components of it are  $\mathcal{A}_{\mathrm{Att}}^D_{\mathrm{tn - INIT}}$ $\mathcal{A}_{\mathrm{Att}}^D_{\mathrm{tn - ATTACK}}$  and  $\mathcal{A}_{\mathrm{Att}}^D_{\mathrm{GUESS}}$  detailed as follows:

AMI initialization  $\mathcal{A}_{\mathrm{Att}}^D_{\mathrm{tn - INIT}}$  : The model initiated by the dishonest server has self- attention as its first layer. We set the number of attention heads  $H$  to 4. The attention dimension is  $d_{\mathrm{att}} = d_{X} - 1$  , where  $d_{X}$  is the one- hot encoding dimension. The hidden and output dimensions are  $d_{\mathrm{hid}} = d_{X}$  and  $d_{Y} = 2d_{X}$  , respectively. Any configurations with a higher number of parameters can adopt the proposed attack because the extra parameters can simply be ignored.

# Algorithm 3  $\mathcal{A}_{\mathrm{Attn - ATTACK}}^{\mathcal{D}}(v)$  using self-attention in AMI

Hyper- parameters:  $\beta ,\gamma \in \mathbb{R}^{+}$  4 Randomly initialize  $W_{Q}^{h},W_{K}^{h},W_{V}^{h},W_{O}$  and  $b_{O}$  for all heads  $h$  5 Randomly initialize a matrix  $W\in \mathbb{R}^{d_X\times d_X}$  6  $W[:,1]\leftarrow v$  # Set the first column of  $W$  to  $v$  7  $Q,R\gets \mathrm{QR}(W)$  #QR- factorization  $W$  8  $W_{Q}^{1}\leftarrow Q[2:d_{X}]^{\top}$  # Set  $W_{Q}^{1}$  to the last  $d_{X} - 1$  rows of  $Q^{\top}$  9  $W_{K}^{1}\leftarrow \beta W_{Q}^{1\top}$  # Set head 1 to memorization mode 10  $W_{K}^{2}\leftarrow \beta W_{Q}^{2\top}$  # Set head 2 to memorization mode 11  $W_{Q}^{3}\leftarrow W_{Q}^{1}$ $W_{K}^{3}\leftarrow W_{K}^{3}$  # Copy Head 1 to Head 3 12  $W_{Q}^{4}\leftarrow W_{Q}^{2}$ $W_{K}^{4}\leftarrow W_{K}^{2}$  # Copy Head 2 to Head 4 13  $W_{V}^{1}\leftarrow I_{d_{X}},W_{V}^{2}\leftarrow I_{d_{X}},W_{V}^{3}\leftarrow I_{d_{X}},W_{V}^{4}\leftarrow I_{d_{X}}$  # Setup for detection 14  $W_{O}\leftarrow \begin{bmatrix} I_{d_{X}} & - I_{d_{X}} & 0_{d_{X}} & 0_{d_{X}}\\ 0_{d_{X}} & 0_{d_{X}} & - I_{d_{X}} & I_{d_{X}} \end{bmatrix}$  # Setup for detection 15  $b_{Oi} = - \gamma ,\forall i\in \{1,\dots ,d_{Y}\}$  # Setup biases 16 Ret all weights and biases

# Algorithm 4  $\mathcal{A}_{\mathrm{Attn - GUESS}}^{\mathcal{D}}(v,\dot{\theta})$  using self-attention in AMI

If the any gradients of  $W^{O}$  is non- zero, returns 1 if  $\| \dot{\theta}_{1}(W^{O})\|_{\infty} > 0$  then Ret 1 end Ret 0

AMI attack  $\mathcal{A}_{\mathrm{Attn - ATTACK}}^{\mathcal{D}}$  : The attack component  $\mathcal{A}_{\mathrm{Attn - ATTACK}}^{\mathcal{D}}$  determines the self- attention weights including  $W_{Q}^{h},W_{K}^{h},W_{V}^{h},W_{O}$  and  $b_{O}$  , where  $h$  is the head's index. There are two hyper- parameters,  $\beta$  and  $\gamma \in \mathbb{R}^{+}$  , in  $\mathcal{A}_{\mathrm{Attn - ATTACK}}^{\mathcal{D}}$  Intuitively,  $\beta$  controls how much the attention heads memorize their input patterns  $x_{i}^{h}$  and  $\gamma$  adjusts a cut- off threshold deciding between  $v\in D$  and  $v\notin D$  (depicted in Fig. 3). Given a target pattern  $v$  for detection, the weights of the first attention head is chosen such that:

$$
W_{K}^{1\top}W_{Q}^{1}\approx \beta I_{d_{X}}\quad \mathrm{and}\quad W_{Q}^{1}v\approx 0 \tag{8}
$$

To enforce condition (8),  $d_{X} - 1$  vectors orthogonal to  $v\in \mathbb{R}^{d_{X}}$  are assigned to  $W_{Q}^{1}$  using QR- factorization. Subsequently,  $W_{K}^{1}$  is defined as the transpose of  $\beta W_{Q}^{1\dagger}$  , where  $\dagger$  represents the pseudo- inverse. In contrast, the second head initializes  $W_{Q}^{2}$  randomly and sets  $W_{K}^{2}$  as its pseudo- inverse. As a result, the second condition of (8) does not hold for  $W_{Q}^{2}$  and  $W_{K}^{2}$  . The remaining parameters of the two heads are configured such that the first  $d_{X}$  rows of  $Y$  compute max  $\{0,Z^1 - Z^2 - \gamma \mathbf{1}^\top \}$  The third and fourth heads are designed to produce the negation of the first and second heads, respectively, meaning the last  $d_{X}$  rows of  $Y$  compute max  $\{0,Z^2 - Z^1 - \gamma \mathbf{1}^\top \}$  . For simplicity in analysis,  $W_{V}^{h}$  and  $W^{O}$  are constructed using identity and zero matrices. The pseudo- code of the attack is provided in Algo. 3.

AMI guess  $\mathcal{A}_{\mathrm{Attn - GUESS}}^{\mathcal{D}}$  : In the guessing phase, the AMI server checks if any of the weights in  $W_{O}$  have non- zero gradients. Algo. 4 shows the pseudo- code of this step.

Attack strategy. We analyze the attack strategy of  $\mathcal{A}_{\mathrm{Attn}}^{\mathcal{D}}$  against unprotected data. The attention- based attacks exploit the memorization capability of the attention layer (Ramsauer et al., 2021): the attacks determine the layer's weights so that if a pattern  $\xi$  similar to a stored pattern  $x\in X$  feed to the layer, the returned signal will be similar to the stored pattern  $x$ .  $X$  can be considered as the Key and the Value, while  $\xi$  is the Query in the attention mechanism. The memorization is imposed by the condition  $W_{K}^{1\top}W_{Q}^{1}\approx \beta I_{d_{X}}$  in (8). For an input  $X$  that does not contain the target pattern  $v$ , we have  $\frac{1}{\beta} W_{K}^{1\top}W_{Q}^{1}X\approx X^{\top}X$ , which is the matrix of correlations of patterns in  $X$ . The output of the softmax, therefore, approximates  $I_{N_{X}}$  since the diagonal entries of  $X^{\top}X$  are significantly larger than the non- diagonal entries. The head's output  $Z^{1}\approx X$ , i.e.,  $z_{i}^{1}\approx x_{i}$ , as a result. Since the second head is the same as the first for  $x\neq v$ , we also have  $Z^{2}\approx x_{i}$ . When  $X$  contains the target pattern  $v$ , i.e., there exists an  $x_{i}$  such that  $x_{i} = v$ , due to the second condition of Eq.(8), we have

$x_{i}^{\top}W_{K}^{1\top}W_{Q}^{1}x_{i}\approx 0$  . This makes the softmax's output uniform, which can be interpreted as the attention being distributed equally among all patterns. Consequentially, the attention's output of the first head is the pattern's average  $\bar{X}$  . Since the second head does not filter  $v$  , its output approximates  $x_{i}$  . By computing the difference between the two heads  $|z_{i}^{1} - z_{i}^{2}|$  and offset it by  $\gamma$  , the adversary can infer the presence of  $v$  in  $X$  . While the attack strategy of  $\mathcal{A}_{\mathrm{Attn}}^{\mathcal{D}}$  against LDP- protected data follows the same principle, the main difference is the input of the attention heads is now the protected version  $x_{i}^{\epsilon}$  instead of  $x_{i}$  like in the case of unprotected data. The introduced noise means that  $(x_{i}^{\epsilon})^{\top}W_{K}^{1\top}W_{Q}^{1}x_{i}^{\epsilon}$  might not be  $\approx 0$  , making the softmax's output non- uniform, and the attention's output is no longer the pattern's average. We analyze in- depth the behavior of  $\mathcal{A}_{\mathrm{Attn}}^{\mathcal{D}}$  under LDP in Appendix D.4.

# D. Vulnerability of FL under LDP to AMI

This appendix provides the details of our theoretical results on AMI in FL under LDP. Appendix D.1 shows the proof of Theorem 1, which is about the vulnerability of LDP- protected data against FC- based AMI attack. Appendix D.2 analyzes the memorization capabilities of attention layers. An example demonstrating the impact of LDP mechanisms on the data's separation is provided in Appendix D.3. Finally, Appendix D.4 shows the proof of Theorem 3, which is about the vulnerability of LDP- protected data against attention- based AMI attack.

# D.1. Proof of Theorem 1 on the Vulnerability of LDP-Protected Data to AMI in FL

This appendix provides the proof of Theorem 1, which is restated below:

Theorem. Given the security game  $\mathsf{Exp}_{\mathrm{LDP}}^{\mathrm{AMI}}$  there exists an AMI adversary  $\mathcal{A}_{\mathrm{FC}}^{\mathcal{D}}$  whose time complexity is  $\mathcal{O}(d_X^2)$  such that  $\mathbf{Adv}_{\mathrm{LDP}}^{\mathrm{AMI}}(\mathcal{A}_{\mathrm{FC}}^{\mathcal{D}}) = 1 - \frac{n + |\mathcal{X}| - 1}{|\mathcal{X}| - 1} P_{\mathcal{M}^{\epsilon}}$  where  $n$  is the size of the dataset  $D$ ,  $|\mathcal{X}|$  is the cardinality of the possible output values of the LDP- mechanism and  $P_{\mathcal{M}^{\epsilon}}$  is the probability that the LDP- mechanism makes the protected version of data point inside the neighborhood of another data point.

Proof. Given  $D = \{X_{i}\}_{i = 1}^{n}$ , where  $X_{i}\in \mathcal{X}$ , and  $\mathcal{X}\subseteq \mathbb{R}^{d_{X}}$ , the LDP- protected version of the input  $X$  is  $\mathcal{M}^{\epsilon}(X)$ . For the model specified by  $\mathcal{A}_{\mathrm{FC}}^{\mathcal{D}}$  as discussed in Subsection 3.2 and described in Appendix C.1, its first layer computes:

$$
\begin{array}{r}\mathrm{ReLU}\left(\left[ \begin{array}{c}I_{d_X}\\ -I_{d_X} \end{array} \right]\mathcal{M}^\epsilon (X) + \left[ \begin{array}{c} - T\\ T \end{array} \right]\right) = \mathrm{ReLU}\left(\left[ \begin{array}{c}\mathcal{M}^\epsilon (X) - T\\ T - \mathcal{M}^\epsilon (X) \end{array} \right]\right) \end{array} \tag{9}
$$

The first row of the second layer then computes:

$$
\mathbf{\Sigma}_0\coloneqq \mathbf{ReLU}\left(-\sum_{i = 1}^{d_X}\mathbf{ReLU}\left((x_i^\epsilon -t_i) + \mathbf{ReLU}(t_i - x_i^\epsilon)\right) + \tau^D\right) = \max \left\{\tau^D -\| \mathcal{M}^\epsilon (X) - T\|_{L_1},0\right\} \tag{10}
$$

This implies the gradient of  $b_{2}[1] = \tau^{\mathcal{D}}$  is non- zero if and only if  $\tau^{\mathcal{D}} > \| \mathcal{M}^{\epsilon}(X) - T\|_{L_1}$ . Thus, for a small enough  $\tau^{\mathcal{D}}$ ,  $T\in D$  is equivalent to a non- zero gradient. We set  $\tau^{\mathcal{D}} = \Delta^{\mathcal{X}}$ . Intuitively, the attack fails if either: (1)  $T\notin D$ , but  $\exists X\in D$  such that  $\mathcal{M}(X)\in B_1(T,\Delta^{\mathcal{X}})$  or (2)  $T\in D$ , but  $\mathcal{M}(X)\notin B_1(T,\Delta^{\mathcal{X}})$  for all  $X\in D$ . This insight is illustrated in Fig. 2.

Given a data  $D$  and a randomly sampled point  $T\notin D$ , the probability that  $\mathcal{M}^{\epsilon}(X)$  belongs to  $B_{1}(T,\Delta^{\mathcal{X}})$  is upper bounded by:

$$
\begin{array}{rl} & {\operatorname *{Pr}\left[\mathcal{M}^{\epsilon}(X)\in B_{1}(T,\Delta^{\mathcal{X}})\right] = \operatorname *{Pr}\left[\mathcal{M}^{\epsilon}(X)\in B_{1}(T,\Delta^{\mathcal{X}})\mathrm{~and~}\mathcal{M}^{\epsilon}(X)\notin B_{1}(X,\Delta^{\mathcal{X}})\right]}\\ & {= \operatorname *{Pr}\left[\mathcal{M}^{\epsilon}(X)\in B_{1}(T,\Delta^{\mathcal{X}})|\mathcal{M}^{\epsilon}(X)\notin B_{1}(X,\Delta^{\mathcal{X}})\right]\operatorname *{Pr}\left[\mathcal{M}^{\epsilon}(X)\notin B_{1}(X,\Delta^{\mathcal{X}})\right]}\\ & {\leq \frac{1}{|\mathcal{X}| - 1}\operatorname *{Pr}\left[\mathcal{M}^{\epsilon}(X)\notin B_{1}(X,\Delta^{\mathcal{X}})\right] = \frac{1}{|\mathcal{X}| - 1} P_{\mathcal{M}^{\epsilon}}} \end{array} \tag{13}
$$

(11) is from the fact that  $\mathcal{M}^{\epsilon}(X)\in B_{1}(T,\Delta^{\mathcal{X}})$  implies  $\mathcal{M}^{\epsilon}(X)\notin B_{1}(X,\Delta^{\mathcal{X}})$  as the balls are disjoint. 
(12) is from the conditional probability formula. For 
(13), given  $\mathcal{M}^{\epsilon}(X)\notin B_{1}(X,\Delta^{\mathcal{X}})$ , since all the balls  $B_{1}(X,\Delta^{\mathcal{X}})$  for  $X\in D$  and  $B_{1}(T,\Delta^{\mathcal{X}})$  are disjoint,  $\mathcal{M}^{\epsilon}(X)$  can either be in one of the other  $|\mathcal{X}| - 1$  balls of radius  $\Delta^{\mathcal{X}}$  or be outside of all those balls. As  $T$  are chosen randomly, the probability that  $\mathcal{M}^{\epsilon}(X)$  is in one of the  $|\mathcal{X}| - 1$  balls is bounded by  $1 / (|\mathcal{X}| - 1)$ .

If  $b = 0$  , the probability that  $\tilde{\omega} 0$  is activated is:

$$
\begin{array}{rl} & {\operatorname *{Pr}\left[z_0 > 0|b = 0\right] = \operatorname *{Pr}\left[\exists X\in D\mathrm{~such~that~}\mathcal{M}(X)\in B_1(T,\Delta^x)|b = 0\right]}\\ & {\qquad \leq \sum_{X\in D}\frac{1}{|\mathcal{X}| - 1} P_{\mathcal{M}^\epsilon}\leq \frac{n}{|\mathcal{X}| - 1} P_{\mathcal{M}^\epsilon}} \end{array} \tag{15}
$$

where (14) is from (10) and the inequalities in (15) are from the union bound and (13).

On the other hand, if  $b = 1$  , the probability that  $\tilde{\omega} 0$  is not activated is bounded by:

$$
\begin{array}{rl} & {\operatorname *{Pr}\left[z_0 = 0|b = 1\right] = \operatorname *{Pr}\left[\mathcal{M}(X)\notin B_1(T,\Delta^\chi)\mathrm{~for~all~}X\in D|b = 1\right]}\\ & {\leq \operatorname *{Pr}\left[\mathcal{M}(T)\notin B_1(T,\Delta)|b = 1\right] = \operatorname *{Pr}\left[\mathcal{M}(T)\notin B_1(T,\Delta)\right] = P_{\mathcal{M}^\epsilon}} \end{array} \tag{16}
$$

where inequality (17) uses the fact that, conditioned on  $b = 1,T\in D$

Thus, we have the advantage of  $\mathcal{A}_{\mathrm{FC}}^D$  ..

$$
\begin{array}{rl} & {\mathrm{Adv}_{\mathrm{LDP}}^{\mathrm{AMI}}(\mathcal{A}_{\mathrm{FC}}^D) = \operatorname *{Pr}[b' = 1|b = 1] + \operatorname *{Pr}[b' = 0|b = 0] - 1}\\ & {\qquad = (1 - \operatorname *{Pr}[z_0 = 0|b = 1]) + (1 - \operatorname *{Pr}[z_0 > 0|b = 0]) - 1}\\ & {\qquad \geq (1 - P_{\mathcal{M}^\epsilon}) + \left(1 - \frac{n}{|\mathcal{X}| - 1} P_{\mathcal{M}^\epsilon}\right) - 1 = 1 - \frac{n + |\mathcal{X}| - 1}{|\mathcal{X}| - 1} P_{\mathcal{M}^\epsilon}} \end{array} \tag{18}
$$

where (20) uses (15) and (17). Since  $\mathcal{A}_{\mathrm{FC}}^D$  can be constructed in  $\mathcal{O}(d_X^2)$  , we have the Theorem.

# D.2. Memorization capabilities of Attention layers.

We now state Lemma 1 bounding the error of the self- attention layer in memorization mode (Vu et al., 2024). The Lemma can be considered as a specific case of Theorem 5 of (Ramsauer et al., 2021). In the context of that work, they use the term for well- separated pattern in their main manuscript to indicate the condition that the Theorem holds. In fact, the condition (21) stated in Lemma 1 is a sufficient condition for that Theorem of (Ramsauer et al., 2021).

Lemma 1. Given a date  $X$  a constant  $\alpha >0$  large enough such that, for an  $x_{i}\in X$  ..

$$
\Delta_{i}\geq \frac{2}{\alpha N_{X}} +\frac{1}{\alpha}\log (2(N_{X} - 1)N_{X}\alpha M^{2}) \tag{21}
$$

then, for any  $\xi$  such that  $\begin{array}{r}\| \xi - x_i\| \leq \frac{1}{\alpha N_XM} \end{array}$  , we have

$$
\left\| x_{i} - X\mathrm{softmax}\left(\alpha X^{\top}\xi\right)\right\| \leq 2M(N_{X} - 1)\exp \left(2 / N_{X} - \alpha \Delta_{i}\right)
$$

Proof. See Lemma 3 (Vu et al., 2024) for proof or Theorem 5 (Ramsauer et al., 2021) for proof of general case.

Intuitively, Lemma 1 claims that, if we have a pattern  $\xi$  near  $x_{i}$ $X$  softmax  $\left(\alpha X^{\top}\xi\right)$  is exponentially near  $\xi$  as a function of  $\Delta_{i}$  . Another key remark of the Lemma is that  $\| x_{i} - X\mathrm{softmax}\left(\alpha X^{\top}\xi\right)\|$  exponentially approaches 0 as the input dimension increases (Ramsauer et al., 2021).

We now consider the iteration  $\xi^{\mathrm{new}} = f(\xi) = Xp = X\mathrm{softmax}(\beta X^T\xi)$  . We now state Lemma 2 (Lemma A3 (Ramsauer et al., 2021)) that provide the bound on the Jacobian of the fixed point iteration:

Lemma 2. The following bound on the norm  $\| J\| _2$  of the Jacobian of the fixed point iteration  $f$  holds independent of  $p$  or the query  $\xi$  ..

$$
\| J\| _2\leq \beta m_{max}^2, \tag{22}
$$

Proof. See Lemma A3 (Ramsauer et al., 2021).

# D.3. The Separation of LDP-Protected Data

To give an intuition that the LDP mechanism generally increases the separation of the data, we consider the LDP mechanism in which each  $r_i$  is i.i.d sampled and independently added to each input pattern. We have the expected value of the separation between two patterns is:

$$
\begin{array}{rl} & {\mathbf{E}\left[x_i^{\epsilon_\top}x_i^{\epsilon} - x_i^{\epsilon_\top}x_j^{\epsilon}\right] = \mathbf{E}\left[(x_i + r_i)^\top (x_i + r_i) - (x_i + r_i)^\top (x_j + r_j)\right]}\\ & {= \mathbf{E}\left[x_i^\top x_i - x_i^\top x_j\right] + 2\mathbf{E}\left[x_i^\top r_i\right] - \mathbf{E}\left[r_i^\top x_j\right] - \mathbf{E}\left[r_j^\top x_i\right] + \mathbf{E}\left[r_i^\top r_i\right] - \mathbf{E}\left[r_i^\top r_j\right]}\\ & {= \mathbf{E}\left[x_i^\top x_i - x_i^\top x_j\right] + \mathbf{Var}(r_i)} \end{array} \tag{25}
$$

As we can see, the last expression is the expectation of the separation of the data  $D$  plus the variance of the noise. Thus, we can assume that  $\Delta^{\epsilon}$  resulting from the defense mechanism is to be at least similar to the separation  $\Delta$  of the original data  $D$

# D.4. Proof of Theorem 3 on the Vulnerability of LDP-Protected Data to Attention-based AMI in FL

We are now ready to state the proof of Theorem 3. We restate the Theorem below.

Theorem. Given a  $\Delta^{\epsilon}$  separated data  $D^{\mathcal{M}_{\epsilon}}$  (the LDP- protected version of the data  $D$  ) with i.i.d patterns of the security game  $\mathsf{Exp}_{\mathrm{LDP}}^{\mathrm{AMI}}$  for any  $\beta >0$  large enough such that:

$$
\Delta^{\epsilon}\geq \frac{2}{\beta N_{X}} +\frac{1}{\beta}\log (2(N_{X} - 1)N_{X}\beta M^{\epsilon^{2}}) \tag{26}
$$

then there exists an AMI adversary that exploits the self- attention layer  $\mathcal{A}_{\mathrm{Attn}}^{\mathcal{D}}$  whose complexity is  $\mathcal{O}(d_X^3)$  of the threat model  $\mathsf{Exp}_{\mathrm{LDP}}^{\mathrm{AMI}}$  such that  $\mathbf{Adv}_{\mathrm{LDP}}^{\mathrm{AMI}}(\mathcal{A}_{\mathrm{Attn}}^{\mathcal{D}})$  is lower bounded by:

$$
\mathsf{v}_{\mathrm{LDP}}^{\mathrm{AMI}}(\mathcal{A}_{\mathrm{Attn}}^{\mathcal{D}})\geq P_{\mathrm{proj}}^{\mathcal{D}^{\mathcal{M}_{\epsilon}}}\left(\frac{1}{\beta N_{X}M^{\epsilon}}\right) + P_{\mathrm{proj}}^{\mathcal{D}^{\mathcal{M}_{\epsilon}}}\left(\frac{1}{\beta N_{X}M^{\epsilon}}\right)^{2nN_{X}} - P_{\mathrm{box}}^{\mathcal{D}^{\mathcal{M}_{\epsilon}}}\left(3\Delta^{\epsilon} + \beta (m_{max}^{\epsilon})^{2}R^{\epsilon}\right) \tag{27}
$$

where  $\bar{\Lambda}^{\epsilon} = 2M^{\epsilon}(N_{X} - 1)\exp (2 / N_{X} - \beta \Delta^{\epsilon})$  and  $\mathcal{D}^{\mathcal{M}_{\epsilon}}$  is the distribution of the protected data  $D^{\mathcal{M}_{\epsilon}}$  induced by the original data distribution  $\mathcal{D}$  and the LDP- mechanism  $\mathcal{M}_{\epsilon}$ $\begin{array}{r}m_{x}^{\epsilon} = \frac{1}{N_{X}}\sum_{i = 1}^{N_{X}}x_{i}^{\epsilon} \end{array}$  is the arithmetic mean of all LDP- protected patterns and  $m_{max}^{\epsilon} = \max_{X_1\leq i\leq N_X}\| x_i - m_{x}^{\epsilon}\|$  . Here,  $P_{\mathrm{proj}}^{\mathcal{D}^{\mathcal{M}_{\epsilon}}}(\delta)$  is the probability that the projected component between two independent patterns drawn from  $\mathcal{D}^{\mathcal{M}_{\epsilon}}$  is smaller than  $\delta$  and  $P_{\mathrm{box}}^{\mathcal{D}^{\mathcal{M}_{\epsilon}}}(\delta)$  is the probability that a random pattern drawn from  $\mathcal{D}^{\mathcal{M}_{\epsilon}}$  is in the cube of size  $2\delta$  centering at the arithmetic mean of the patterns in  $\mathcal{D}^{\mathcal{M}_{\epsilon}}$

Proof. We represent the victim's dataset as  $D = \{X_{i}\}_{i = 1}^{n}$  , where  $X_{i}\in \mathcal{X}$  , and  $\mathcal{X}\subseteq \mathbb{R}^{d_X\times N_X}$  . For any 2- dimensional array  $X$  , each column  $x_{j}\in \mathbb{R}^{d_{X}}$  is referred to as a pattern. The distortion imposed by LDP is modeled by a noise  $r_i$  added to each pattern:  $X^{\epsilon} = \mathcal{M}^{\epsilon}(X) = \{x_{i} + r_{i}\}_{i = 1}^{N_{X}} = \{x_{i}^{1}\}_{i = 1}^{N_{X}}$  . For brevity, we first consider the following notation of the output of one attention head under LDP without the head indexing  $h$  ..

$$
X^{\epsilon}\mathrm{softmax}\left(1 / \sqrt{d_{\mathrm{attn}}}(X^{\epsilon})^{\top}W_{K}^{\top}W_{Q}X^{\epsilon}\right) \tag{28}
$$

Notice that we omit  $W_{V}$  because they are all set to identity ( line 14 Algo. 3).

To show the Theorem, we consider the AMI adversary  $\mathcal{A}_{\mathrm{Attn}}^{\mathcal{D}}$  specified in Subsection 4.2. Since  $W\in \mathbb{R}^{d_X\times d_X}$  (line 5 in Algorithm 3) is initialized randomly, it has a high probability of being non- singular, even after assigning  $v$  to its first column (line 6 in Algorithm 3). For simplicity of analysis, we assume that  $W$  has full rank. If this assumption does not hold, we can re- run the two corresponding lines of the algorithm. Similarly, we also assume that all  $W_{Q}^{h}$  and  $W_{K}^{h}$  have rank  $d_{\mathrm{attn}} = d_{X} - 1$

For all heads, we set  $W_{K} = \beta (W_{Q}^{\top})^{\dagger}$  (lines 9 and 10 in Algorithm 3). Consequently,  $\begin{array}{r}\frac{1}{\beta} W_K^\top W_Q = W_Q^\top W_Q \end{array}$  is the projection matrix onto the column space of  $W_{Q}^{\top}$  . By defining  $[\xi_1,\dots ,\xi_{N_x}] = \Xi = \textstyle \frac{1}{\beta} W_K^\top W_QX^\epsilon$  , it follows that  $\xi_{j}$  is the projection of the pattern  $x_{j}^{\epsilon}$  onto this space.

For head 1 and head 3, as a result of line 6 in Algorithm 3, we can express  $W = [v,w_{2},\dots ,w_{d_{X}}]$  . Based on the QR factorization (line 7), we have:

$$
QR = [v,w_{2},\dots w_{d_{X}}]\longrightarrow R = Q^{\top}[v,w_{2},\dots w_{d_{X}}] \tag{29}
$$

Since  $R$  is an upper triangular matrix,  $v$  is orthogonal to all rows  $Q_{i}$  for  $i\in \{2,\dots ,d_X\}$  of  $Q^{\top}$  . Additionally, due to the assignment at line 8 in Algorithm 3, the column space of  $W_{Q}^{\top}$  is the linear span of  $\{Q_i\}_{i = 2}^{d_X}$  , all of which are orthogonal to  $v$  As a result, the difference between  $X^{\epsilon}$  and  $\Xi$  corresponds to the component of  $X^{\epsilon}$  in the direction of  $v$  ..

$$
X^{\epsilon} - \Xi^{h} = [x_{1}^{\epsilon} - \xi_{1}^{h},\dots ,x_{N_{X}}^{\epsilon} - \xi_{N_{X}}^{h}] = [\mathrm{Proj}_{v}(x_{1}^{\epsilon}),\dots ,\mathrm{Proj}_{v}(x_{N_{X}}^{\epsilon})],\quad h\in \{1,3\} \tag{30}
$$

where  $\mathrm{Proj}_v(x_j^\epsilon)$  is the component of pattern  $x_{j}^{\epsilon}\in \mathbb{R}^{d_{X}}$  along  $v$

For head 2 and head 4, although QR- factorization is not performed,  $\scriptstyle {\frac{1}{\beta}}W_K^W W_Q$  for these heads also acts as projection matrices, but onto different column spaces. These spaces are likewise of rank  $d_{X} - 1$  , and each omits one direction. Denoting this direction as  $u$  , we can express the difference between  $X^{\epsilon}$  and  $\Xi$  for these heads as:

$$
X^{\epsilon} - \Xi^{h} = [x_{1}^{\epsilon} - \xi_{1}^{h},\dots ,x_{N_{X}}^{\epsilon} - \xi_{N_{X}}^{h}] = [\mathrm{Proj}_{u}(x_{1}^{\epsilon}),\dots ,\mathrm{Proj}_{u}(x_{N_{X}}^{\epsilon})],\quad h\in \{2,4\} \tag{31}
$$

We now denote  $f_{\alpha}:\mathbb{R}^{d_{X}\times N_{x}}\to \mathbb{R}^{d_{X}\times N_{x}}$  as:

$$
\Xi^{\prime} = f_{\alpha}(\Xi) = X^{\epsilon}\mathrm{softmax}\left(\alpha (X^{\epsilon})^{\top}\Xi\right) \tag{32}
$$

For brevity, we also abuse the notation and write  $\xi^{\prime} = f_{\alpha}(\xi) = X^{\epsilon}\mathrm{softmax}\left(\alpha (X^{\epsilon})^{\top}\xi\right)$  for  $\xi$  and  $\xi^{\prime}\in \mathbb{R}^{d_{X}}$

With this, the output of the layer before the ReLU activation can be expressed as:

$$
\begin{array}{r}Z = \left[ \begin{array}{l}f_{\beta}(\Xi^{1}) - f_{\beta}(\Xi^{2}) - \gamma 1^{\top}\\ f_{\beta}(\Xi^{4}) - f_{\beta}(\Xi^{3}) - \gamma 1^{\top} \end{array} \right] = \left[ \begin{array}{l}f_{\beta}(\Xi^{1}) - f_{\beta}(\Xi^{2}) - \gamma 1^{\top}\\ f_{\beta}(\Xi^{2}) - f_{\beta}(\Xi^{1}) - \gamma 1^{\top} \end{array} \right]\\ = \left[ \begin{array}{l}f_{\beta}(\xi_{1}^{1}) - f_{\beta}(\xi_{1}^{2}) - \gamma ,\dots ,f_{\beta}(\xi_{N_{X}}^{1}) - f_{\beta}(\xi_{N_{X}}^{2}) - \gamma \\ f_{\beta}(\xi_{1}^{2}) - f_{\beta}(\xi_{1}^{1}) - \gamma ,\dots ,f_{\beta}(\xi_{N_{X}}^{2}) - f_{\beta}(\xi_{N_{X}}^{1}) - \gamma \end{array} \right] \end{array} \tag{33}
$$

where  $\beta = \beta /\sqrt{d_{\mathrm{attn}}}$  and  $\gamma$  is defined as in Algorithm 3. From the above expressions, it follows that  $Z$  has non- zero entries if and only if:

$$
\exists i\mathrm{~such~that~}\| f_{\beta}(\xi_i^1) - f_{\beta}(\xi_i^2)\|_\infty >\gamma \tag{35}
$$

Note that heads 3 and 4 are used to handle cases where the entries of  $f_{\beta}(\xi_i^1)$  are smaller than those in  $f_{\beta}(\xi_i^2)$  . The condition (35) can also be rewritten as:

$$
\| f_{\beta}(\Xi^{1}) - f_{\beta}(\Xi^{2})\|_{\infty} > \gamma \tag{36}
$$

For a given pattern  $x_{i}$  , we now examine two cases:  $x_{i}\neq v$  and  $x_{i} = v$

Case 1. For a pattern  $x_{i}\in X$  such that  $x_{i}\neq v$  , from Lemma 1, we have

$$
\begin{array}{r}\| x_i^\epsilon -f_\beta (\xi_i^1)\| \leq 2M^\epsilon (N_X - 1)\exp (2 / N_X - \beta \Delta_i^\epsilon)\\ \| x_i^\epsilon -f_\beta (\xi_i^2)\| \leq 2M^\epsilon (N_X - 1)\exp (2 / N_X - \beta \Delta_i^\epsilon) \end{array} \tag{37}
$$

when  $\| x_i^\epsilon - \xi_i^1\| = \| \mathrm{Proj}_v(x_i^\epsilon)\| \leq 1 / (\beta N_X M^\epsilon)$  and  $\| \mathrm{Proj}_u(x_i^\epsilon)\| \leq 1 / (\beta N_X M^\epsilon)$  , respectively. Note that it is necessary to select a sufficiently large  $\beta$  to ensure that Lemma 1 holds. We denote these events by  $A_{i}^{1}$  and  $A_{i}^{2}$

Using triangle inequality, we have:

$$
\begin{array}{r}\| f_{\beta}(\xi_i^1) - f_{\beta}(\xi_i^2)\| \leq \| f_{\beta}(\xi_i^1) - x_i^\epsilon \| +\| x_i^\epsilon -f_{\beta}(\xi_i^2)\| \leq 4M^\epsilon (N_X - 1)\exp (2 / N_X - \beta \Delta_i^\epsilon)\coloneqq 2\bar{\Delta}_i^\epsilon \end{array} \tag{39}
$$

with a probability of  $\mathrm{P}\in [A_i^1\cap A_i^2 ]$  . Here, we define  $\bar{\Delta}_i^\epsilon \coloneqq 2M^\epsilon (N_X - 1)\exp (2 / N_X - \beta \Delta_i^\epsilon)$  . We further relax the inequality using the infinity- norm, which bounds the maximum absolute difference in the pattern's feature:

$$
\| f_{\beta}(\xi_i^1) - f_{\beta}(\xi_i^2)\|_\infty \leq 2\bar{\Delta}_i^\epsilon \tag{40}
$$

Since the data point  $X^{\epsilon}$  is  $\Delta^{\epsilon}$  - separated, i.e.,  $\Delta^{\epsilon}\leq \Delta_{i}^{\epsilon}$  , we further obtain:

$$
\| f_{\beta}(\xi_i^1) - f_{\beta}(\xi_i^2)\|_\infty \leq 2\bar{\Delta}^\epsilon \tag{41}
$$

where  $\bar{\Delta}^{\epsilon}\coloneqq 2M^{\epsilon}(N_{X} - 1)\exp (2 / N_{X} - \beta \Delta^{\epsilon})$ .

We now analyze the event  $A_{i}^{1}$ . Essentially, this event occurs when the component of  $x_{i}^{\epsilon}$  along  $v$  is smaller than a constant determined by the data distribution  $\mathcal{D}$ . Moreover, since both  $v$  and  $x_{i}$  are independently drawn from the distribution (as specified in the experiment  $\mathsf{Exp}_{\mathrm{LDP}}^{\mathrm{AMI}}$  (Fig. 12)), and  $r_{i}$  is the random noise introduced by the LDP mechanism,  $v$  and  $x_{i}^{\epsilon}$  can be treated as two random patterns sampled from the input distribution. Consequently, the probability of  $A_{1}^{1}$  corresponds to the probability that the projected component between two random patterns is less than or equal to  $\frac{1}{\beta N_{X}M^{\epsilon}}$ . Formally, for an input distribution  $\mathcal{D}^{\mathcal{M}_{\epsilon}}$ , we denote  $P_{\mathrm{proj}}^{\mathcal{D}^{\mathcal{M}_{\epsilon}}}(\delta)$  as the probability that the projected component between any independent patterns drawn from  $\mathcal{D}^{\mathcal{M}_{\epsilon}}$  is at most  $\delta$ . We then have:

$$
\operatorname *{Pr}\left[A_i^1\right] = \operatorname *{Pr}\left[\| \operatorname {Proj}_v(x_i^{\epsilon})\| \leq 1 / (\beta N_XM^{\epsilon})\right] = P_{\mathrm{proj}}^{\mathcal{M}_\epsilon}\left(\frac{1}{\beta N_XM^\epsilon}\right) \tag{42}
$$

by definition. Similarly, for  $A_{i}^{2}$ , we have:

$$
\operatorname *{Pr}\left[A_i^2\right] = \operatorname *{Pr}\left[\| \operatorname {Proj}_u(x_i^\epsilon)\| \leq 1 / (\beta N_XM^\epsilon)\right] = P_{\mathrm{proj}}^{\mathcal{D}^{\mathcal{M}_\epsilon}}\left(\frac{1}{\beta N_XM^\epsilon}\right) \tag{43}
$$

Since  $v$  and  $u$  are independent, we obtain:

$$
\operatorname *{Pr}\left[A_i^1\cap A_i^2\right]\geq P_{\mathrm{proj}}^{D^{\mathcal{M}_\epsilon}}\left(\frac{1}{\beta N_XM^\epsilon}\right)^2 \tag{44}
$$

Case 2. On the other hand, when  $x_{i} = v$ , we have the output of head 1 is:

$$
\begin{array}{rl} & X^{\epsilon}\mathrm{softmax}\left(\beta X^{\epsilon^{\top}}\xi_{i}^{1}\right)\\ & = X^{\epsilon}\mathrm{softmax}\left(\beta X^{\epsilon^{\top}}(x_{i}^{\epsilon} - \mathrm{Proj}_{v}(x_{i}^{\epsilon}))\right)\\ & = X^{\epsilon}\mathrm{softmax}\left(\beta X^{\epsilon^{\top}}(v + r_{i} - \mathrm{Proj}_{v}(v + r_{i}))\right)\\ & = X^{\epsilon}\mathrm{softmax}\left(\beta X^{\epsilon^{\top}}(r_{i} - \bar{r}_{i}^{v})\right) \end{array} \tag{48}
$$

Thus, the difference between the output of head 1 with  $\bar{X}^{\epsilon}\coloneqq \frac{1}{N_{x}}\sum_{j = 1}^{N_{X}}x_{j}^{\epsilon}$  can be bounded by:

$$
\begin{array}{rl} & {= \left\| X^{\epsilon}\mathrm{softmax}\left(\beta X^{\epsilon^{\top}}\xi_{i}^{1}\right) - \bar{X}^{\epsilon}\right\|}\\ & {= \left\| X^{\epsilon}\mathrm{softmax}\left(\beta X^{\epsilon^{\top}}\xi_{i}^{1}\right) - X^{\epsilon}\mathrm{softmax}\left(\beta X^{\epsilon^{\top}}0\right)\right\|}\\ & {= \left\| X^{\epsilon}\mathrm{softmax}\left(\beta X^{\epsilon^{\top}}(r_{i} - \bar{r}_{i}^{v})\right) - X^{\epsilon}\mathrm{softmax}\left(\beta X^{\epsilon^{\top}}0\right)\right\|}\\ & {\leq \left\| \max_{\xi}\frac{\partial f_{\beta}(\xi)}{\partial\xi}\right\| \| r_{i} - \bar{r}_{i}^{v}\| \leq \left\| \max_{\xi}\frac{\partial f_{\beta}(\xi)}{\partial\xi}\right\| \| r_{i}\| \leq \beta (m_{max}^{\epsilon})^{2}R^{\epsilon}} \end{array} \tag{52}
$$

where the inequalities are due to the mean value theorem (Lemma A32 (Ramsauer et al., 2021)) and from the fact that the Jacobian of  $f_{\beta}$ , i.e.,  $\frac{\partial f_{\beta}(\xi)}{\partial\xi}$ , is bounded by  $\beta (m_{max}^{\epsilon})^{2}$  (Lemma 2). Thus, from triangle inequality, we have

$$
\begin{array}{rl} & {\left\| f_{\beta}(\xi^{1}) - f_{\beta}(\xi_{i}^{2})\right\|_{\infty} = \left\| f_{\beta}(\xi_{i}^{1}) - \bar{X}^{\epsilon} + \bar{X}^{\epsilon} - v - r_{i} + v + r_{i} - f_{\beta}(\xi_{i}^{2})\right\|_{\infty}}\\ & {\geq \left\| \bar{X}^{\epsilon} - v - r_{i}\right\|_{\infty} - \left\| f_{\beta}(\xi_{i}^{1}) - \bar{X}^{\epsilon}\right\|_{\infty} - \left\| v + r_{i} - f_{\beta}(\xi_{i}^{2})\right\|_{\infty}}\\ & {\geq \left\| \bar{X}^{\epsilon} - v - r_{i}\right\|_{\infty} - \beta (m_{max}^{\epsilon})^{2}R^{\epsilon} - \bar{\Delta}_{i}^{\epsilon}} \end{array} \tag{55}
$$

when  $A_{i}^{2}$  happens (so that  $\bar{\Delta}_{i}^{\epsilon}\geq \left\| v + r_{i} - f_{\beta}(\xi_{i}^{2})\right\|_{\infty})$ , whose probability is  $P_{\mathrm{proj}}^{\mathcal{D}^{\mathcal{M}_{\epsilon}}}\left(\frac{1}{\beta N_{X}M^{\epsilon}}\right)$ .

We have the probability that  $\left\| f_{\beta}(\xi_i^1) - f_{\beta}(\xi_i^2)\right\|_\infty >2\bar{\Delta}^\epsilon$  is bounded by:

$$
\begin{array}{rl} & {\mathrm{Pr}\left[\left\| f_{\beta}(\xi_i^1) - f_{\beta}(\xi_i^2)\right\|_\infty >2\bar{\Delta}^\epsilon \right]}\\ & {= 1 - \mathrm{Pr}\left[\left\| f_{\beta}(\xi_i^1) - f_{\beta}(\xi_i^2)\right\|_\infty \leq 2\bar{\Delta}^\epsilon \right]}\\ & {= 1 - \mathrm{Pr}\left[\left\| f_{\beta}(\xi_i^1) - f_{\beta}(\xi_i^2)\right\|_\infty \leq 2\bar{\Delta}^\epsilon |A_i^2\right]\mathrm{Pr}\left[A_i^2\right]}\\ & {-\mathrm{Pr}\left[\left\| f_{\beta}(\xi_i^1) - f_{\beta}(\xi_i^2)\right\|_\infty \leq 2\bar{\Delta}^\epsilon |\neg A_i^2\right]\mathrm{Pr}\left[\neg A_i^2\right]}\\ & {\geq 1 - \mathrm{Pr}\left[\left\| f_{\beta}(\xi_i^1) - f_{\beta}(\xi_i^2)\right\|_\infty \leq 2\bar{\Delta}^\epsilon |A_i^2\right] - \mathrm{Pr}\left[\neg A_i^2\right]}\\ & {\geq 1 - \mathrm{Pr}\left[\left\| \bar{X}^\epsilon -v - r_i\right\|_\infty -\beta (m_{max}^\epsilon)^2 R^\epsilon -\bar{\Delta}^\epsilon \leq 2\bar{\Delta}^\epsilon |A_i^2\right] - \mathrm{Pr}\left[\neg A_i^2\right]}\\ & {= 1 - \mathrm{Pr}\left[\left\| X^\epsilon -v - r_i\right\|_\infty \leq \beta (m_{max}^\epsilon)^2 R^\epsilon +3\bar{\Delta}^\epsilon \right] - \mathrm{Pr}\left[\neg A_i^2\right]}\\ & {= P_{\mathrm{proj}}^{D^{\mathcal{M}_\epsilon}}\left(\frac{1}{\beta N_XM^\epsilon}\right) - \mathrm{Pr}\left[v + r_i\in \mathrm{Box}\left(\bar{X}^\epsilon ,\beta (m_{max}^\epsilon)^2 R^\epsilon +3\bar{\Delta}^\epsilon\right)\right]} \end{array} \tag{62}
$$

where  $\mathrm{Box}(x,\delta)$  is the cube of size  $2\delta$  centering at  $x$ . The inequality (60) is due to (55) and (61) is from the fact that  $u$  is independent from  $X$  and  $v$ .

We now consider  $\operatorname *{Pr}\left[v + r_i\in \operatorname {Box}\left(\bar{X}^\epsilon ,\beta (m_{max}^\epsilon)^2 R^\epsilon +3\bar{\Delta}^\epsilon\right)\right]$ , which is the probability that the pattern  $v + r_i$  belongs to the cube of size  $2\beta (m_{max}^{\epsilon})^{2}R^{\epsilon} + 6\bar{\Delta}^{\epsilon}$  around the sampled mean of the LDP protected patterns in  $X^{\epsilon}$ . We denote  $P_{\mathrm{box}}^{\mathcal{D}^{\mathcal{M}_\epsilon}}(\delta)$  is the probability that a random pattern drawn from  $\mathcal{D}^{\mathcal{M}_\epsilon}$  is in the cube of size  $2\delta$  centering at the arithmetic mean of the patterns in  $\mathcal{D}^{\mathcal{M}_\epsilon}$ . If the length  $N_X$  of  $X$  is large enough, we have the sampled mean  $\bar{X}^{\epsilon}$  is near the arithmetic mean of the patterns and obtain  $\operatorname *{Pr}\left[v + r_i\in \operatorname {Box}\left(\bar{X}^\epsilon ,\beta (m_{max}^\epsilon)^2 R^\epsilon +3\bar{\Delta}^\epsilon\right)\right] \approx P_{\mathrm{box}}^{\mathcal{D}^{\mathcal{M}_\epsilon}}(\beta (m_{max}^\epsilon)^2 R^\epsilon +3\bar{\Delta}^\epsilon)$ .

Back to main analysis. From the analysis of the two cases, if  $v \notin X$ , we have:

$$
\operatorname *{Pr}\left[\| f_{\beta}(\xi_i^1) - f_{\beta}(\xi_i^2)\|_\infty \leq 2\bar{\Delta}\right] > P_{\mathrm{proj}}^{\mathcal{D}^{\mathcal{M}_\epsilon}}\left(\frac{1}{\beta N_XM^\epsilon}\right)^2,\quad \forall i\in \{1,\dots ,N_X\}
$$

Based on the analysis of the two cases, when  $v$  is not an element of  $X$ , it follows that:

$$
\operatorname *{Pr}\left[\| f_{\beta}(\Xi^{1}) - f_{\beta}(\Xi^{2})\|_{\infty}\leq 2\bar{\Delta}\right] = \prod_{i = 1}^{N_{X}}\operatorname *{Pr}\left[\| f_{\beta}(\xi_{i}^{1}) - f_{\beta}(\xi_{i}^{2})\|_{\infty}\leq 2\bar{\Delta}\right]\geq P_{\mathrm{proj}}^{\mathcal{D}^{\mathcal{M}_{\epsilon}}}\left(\frac{1}{\beta N_{X}M^{\epsilon}}\right)^{2N_{X}}
$$

Since the data points in  $D$  are sampled independently, if  $v$  does not appear in  $D$ , we obtain:

$$
\operatorname *{Pr}\left[\| f_{\beta}(\Xi^{1}) - f_{\beta}(\Xi^{2})\|_{\infty}\leq 2\bar{\Delta}\mathrm{~for~all~}X\in D\right]\geq P_{\mathrm{proj}}^{\mathcal{D}^{\mathcal{M}_{\epsilon}}}\left(\frac{1}{\beta N_{X}M^{\epsilon}}\right)^{2nN_{X}}
$$

On the other hand, if  $v \in X$ , we have:

$$
\begin{array}{rl} & {\exists i\in \{1,\dots ,N_X\} \mathrm{~such~that~}\operatorname *{Pr}\left[\| f_\beta (\xi_i^1) - f_\beta (\xi_i^2)\|_\infty >2\bar{\Delta}\right]\geq 1 - P_{\mathrm{box}}^{\mathcal{M}_\epsilon}(\beta (m_{max}^\epsilon)^2 R^\epsilon +3\bar{\Delta}^\epsilon)}\\ & {\Rightarrow \operatorname *{Pr}\left[\| f_\beta (\Xi^1) - f_\beta (\Xi^2)\|_\infty >2\bar{\Delta}\right]\geq \operatorname *{Pr}\left[\| f_\beta (\Xi^1) - f_\beta (\Xi^2)\|_\infty \leq 2\bar{\Delta}\mathrm{~for~all~}X\in D\right]}\\ & {\geq P_{\mathrm{proj}}^{\mathcal{D}^{\mathcal{M}_\epsilon}}\left(\frac{1}{\beta N_XM^\epsilon}\right) - P_{\mathrm{box}}^{\mathcal{D}^{\mathcal{M}_\epsilon}}(\beta (m_{max}^\epsilon)^2 R^\epsilon +3\bar{\Delta}^\epsilon)} \end{array}
$$

Thus, if pattern  $v$  appears in  $D$ , we have:

$$
\mathrm{~\beta~}\in D\mathrm{~such~that~}\| f_{\beta}(\Xi^{1}) - f_{\beta}(\Xi^{2})\|_{\infty} > 2\bar{\Delta}\big]\geq P_{\mathrm{proj}}^{\mathcal{D}^{\mathcal{M}_{\epsilon}}}\left(\frac{1}{\beta N_{X}M^{\epsilon}}\right) - P_{\mathrm{box}}^{\mathcal{D}^{\mathcal{M}_{\epsilon}}}(\beta (m_{max}^{\epsilon})^{2}R^{\epsilon} + 3\bar{\Delta}^{\epsilon})
$$

By choosing  $\gamma = 2\bar{\Delta}^{\epsilon}$ , we have the probability that the adversary wins is:

$$
\begin{array}{rl} & {P_W = \operatorname *{Pr}\left[v\in D\right]\operatorname *{Pr}\left[\| \dot{\theta}_1(W^O)\|_\infty >0|v\in D\right] + \operatorname *{Pr}\left[v\notin D\right]\operatorname *{Pr}\left[\| \dot{\theta}_1(W^O)\|_\infty = 0|v\notin D\right]}\\ & {\quad = \frac{1}{2}\operatorname *{Pr}\left[\exists X\in D\mathrm{~such~that~}\| f_\beta (\Xi^1) - f_\beta (\Xi^2)\|_\infty >2\bar{\Delta} |v\in D\right]}\\ & {\quad +\frac{1}{2}\operatorname *{Pr}\left[\| f_\beta (\Xi^1) - f_\beta (\Xi^2)\|_\infty \leq 2\bar{\Delta}\mathrm{~for~all~}X\in D|v\notin D\right]}\\ & {\quad \geq \frac{1}{2}\left(P_{\mathrm{proj}}^{D^{\mathcal{M}_\epsilon}}\left(\frac{1}{\beta N_XM^\epsilon}\right) - P_{\mathrm{box}}^{D^{\mathcal{M}_\epsilon}}(\beta (m_{max}^\epsilon)^2 R^\epsilon +3\bar{\Delta}^\epsilon)\right) + \frac{1}{2} P_{\mathrm{proj}}^{D^{\mathcal{M}_\epsilon}}\left(\frac{1}{\beta N_XM^\epsilon}\right)^{2nN_X}} \end{array} \tag{64}
$$

Thus, the advantage of the adversary  $\mathcal{A}_{\mathrm{Attn}}^D$  in Algo. 3 can be lower bounded by:

$$
\mathcal{A}_{\mathrm{DP}}^{\mathrm{MI}}(\mathcal{A}_{\mathrm{Attn}}^{\mathcal{D}}) = 2P_W - 1\geq P_{\mathrm{proj}}^{\mathcal{D}^{\mathcal{M}_\epsilon}}\left(\frac{1}{\beta N_XM^\epsilon}\right) + P_{\mathrm{proj}}^{\mathcal{D}^{\mathcal{M}_\epsilon}}\left(\frac{1}{\beta N_XM^\epsilon}\right)^{2nN_X} - P_{\mathrm{box}}^{\mathcal{D}^{\mathcal{M}_\epsilon}}(\beta (m_{max}^\epsilon)^2 R^\epsilon +3\bar{\Delta}^\epsilon) - P_{\mathrm{box}}^{\mathcal{D}^{\mathcal{M}_\epsilon}}(\beta (m_{max}^\epsilon)^2 R^\epsilon +3\bar{\Delta}^\epsilon) - P_{\mathrm{box}}^{\mathcal{D}^{\mathcal{M}_\epsilon}}(\beta (m_{max}^\epsilon)^2 R^\epsilon +3\bar{\Delta}^\epsilon) - \tag{66}
$$

Since the complexity of the adversary  $\mathcal{A}_{\mathrm{Attn}}^D$  is  $\mathcal{O}(d_X^3)$  (determined by lines 9 and 10, Algo. 3), we have the Theorem 3.

# E. Proof of lower bound of  $\mathbf{Adv}_{\mathrm{GRR - LDP}}^{\mathrm{AMI}}(\mathcal{A}_{\mathrm{FC}}^D)$

Theorem 4. There exists an AMI adversary  $\mathcal{A}_{\mathrm{FC}}^D$  against data protected by Generalized Randomized Response (GRR)  $O(d_X^3)$  of the threat model  $\mathsf{Exp}_{\mathrm{GRR - LDP}}^{\mathrm{AMI}}$  such that  $\mathbf{Adv}_{\mathrm{GRR - LDP}}^{\mathrm{AMI}}(\mathcal{A}_{\mathrm{FC}}^D)\geq \frac{e^{\epsilon} - n}{e^{\epsilon} + |\mathcal{X}| - 1}$ .

![](images/5fbcb49cf1809dc416bde41fd9ebf0266a34c5a076d73853012e74a10dd5b81e.jpg)  
Figure 13. Visualization of the theoretical upperbound (Theorem. 2) and lower bound of  $\mathbf{Adv}_{\mathrm{GRR - LDP}}^{\mathrm{AMI}}(\mathcal{A}_{\mathrm{FC}}^D)$ . (Theorem. 4)

Generalized Randomized Response (GRR). Given an user with a value  $v\in \mathcal{X}$ . A random variable, denoted by  $\hat{X}$ , represents the response of the user on a value  $x$  also in  $\mathcal{X}$ . The generalized randomized response works as follows:

$$
\operatorname *{Pr}\left[\hat{X} = v\right] = \left\{ \begin{array}{ll}\frac{e^{\epsilon}}{e^{\epsilon + d - 1}},\mathrm{~if~}x = v\\ \frac{1}{e^{\epsilon + d - 1}},\mathrm{~if~}x\neq v \end{array} \right. \tag{67}
$$

where  $d\coloneqq |\mathcal{X}|$ . Thus, we have  $P_{\mathcal{M}_{GRR}^{\epsilon}} = \frac{d - 1}{e^{\epsilon} + d - 1}$ .

From (20), we have

$$
\begin{array}{r}\mathbf{Adv}_{\mathrm{GRR - LDP}}^{\mathrm{AMI}}(\mathcal{A}^D)\geq 1 - \frac{n + d - 1}{d - 1} P_{\mathcal{M}_{GRR}^\epsilon} = 1 - \frac{n + d - 1}{d - 1}\frac{d - 1}{e^\epsilon + d - 1}\\ = \frac{e^\epsilon + d - 1 - n - d + 1}{e^\epsilon + d - 1} = \frac{e^\epsilon - n}{e^\epsilon + d - 1} \end{array} \tag{68}
$$

It is clear that this advantage is smaller than the upper bound (4):

$$
\frac{e^{\epsilon} - n}{e^{\epsilon} + d - 1}\leq \frac{e^{\epsilon} - 1}{e^{\epsilon} + d - 1}\leq \frac{e^{\epsilon} - 1}{e^{\epsilon} + 1} \tag{70}
$$

# F. Proof of upper bound of  $\mathbf{Adv}_{\mathrm{LDP}}^{\mathrm{AMI}}(\mathcal{A}_{\mathrm{FC}}^{\mathcal{D}})$

We now restate Theorem 2 on the theoretical upper bound of  $\mathbf{Adv}_{\mathrm{LDP}}^{\mathrm{AMI}}(\mathcal{A}_{\mathrm{FC}}^{\mathcal{D}})$

Theorem. For all AMI adversary  $\mathcal{A}$  of the game  $\mathsf{Exp}_{\mathrm{LDP}}^{\mathsf{FC}}$ , we have

$$
\mathbf{Adv}_{\mathrm{LDP}}^{\mathrm{FC}}(\mathcal{A}_{\mathrm{FC}}^{\mathcal{D}})\leq \frac{e^{\epsilon} - 1}{e^{\epsilon} + 1}
$$

Proof. For clarity, we denote  $t_1$  the instance of  $D$  that is sampled in case  $b = 1$  and  $t_0$  the random sample of the input distribution  $\mathcal{D}$  in case  $b = 0$ . Let  $D_{1} = D$  and  $D_{2} = D\setminus \{t_{1}\} \cup \{t_{0}\}$ . With this notation, we can think the adversary needs to differentiate  $D_{1}$  to  $D_{2}$  instead of  $t$  or  $t^\prime$ .

Using the notations as denoted in Lemma 3, the probability the adversary wins is:

$$
\begin{array}{r l} & {P_{W} = \operatorname *{Pr}\left[S = D_{1}\right]\operatorname *{Pr}\left[\mathcal{M}_{\epsilon}(S)\in T_{1}^{\mathcal{M}_{\epsilon}}|S = D_{1}\right] + \operatorname *{Pr}\left[S = D_{2}\right]\operatorname *{Pr}\left[\mathcal{M}_{\epsilon}(S)\in T_{0}^{\mathcal{M}_{\epsilon}}|S = D_{2}\right]}\\ & {\qquad = \frac{1}{2}\left(\operatorname *{Pr}\left[\mathcal{M}_{\epsilon}(D_{1})\in T_{1}^{\mathcal{M}_{\epsilon}}\right] + \operatorname *{Pr}\left[\mathcal{M}_{\epsilon}(D_{2})\in T_{0}^{\mathcal{M}_{\epsilon}}\right]\right)} \end{array} \tag{72}
$$

Here,  $S$  is a dummy variable representing which datasets, i.e.,  $D_{1}$  or  $D_{2}$  is chosen by the experiment. Similarly, we have the probability the adversary loses is:

$$
P_{L} = \frac{1}{2}\left(\operatorname *{Pr}\left[\mathcal{M}_{\epsilon}(D_{1})\in T_{0}^{\mathcal{M}_{\epsilon}}\right] + \operatorname *{Pr}\left[\mathcal{M}_{\epsilon}(D_{2})\in T_{1}^{\mathcal{M}_{\epsilon}}\right]\right) \tag{73}
$$

From Lemma 3, we have:

$$
\begin{array}{r l} & {\operatorname *{Pr}\left[\mathcal{M}_{\epsilon}(D_{1})\in T_{1}^{\mathcal{M}_{\epsilon}}\right]\leq e^{\epsilon}\operatorname *{Pr}\left[\mathcal{M}_{\epsilon}(D_{2})\in T_{1}^{\mathcal{M}_{\epsilon}}\right]}\\ & {\operatorname *{Pr}\left[\mathcal{M}_{\epsilon}(D_{2})\in T_{0}^{\mathcal{M}_{\epsilon}}\right]\leq e^{\epsilon}\operatorname *{Pr}\left[\mathcal{M}_{\epsilon}(D_{1})\in T_{0}^{\mathcal{M}_{\epsilon}}\right]} \end{array} \tag{75}
$$

Combining (72), (73), (74) and (75), we have

$$
\begin{array}{r l} & {P_{W} = \frac{1}{2}\left(\operatorname *{Pr}\left[\mathcal{M}_{\epsilon}(D_{1})\in T_{1}^{\mathcal{M}_{\epsilon}}\right] + \operatorname *{Pr}\left[\mathcal{M}_{\epsilon}(D_{2})\in T_{0}^{\mathcal{M}_{\epsilon}}\right]\right)}\\ & {\qquad \leq e^{\epsilon}\frac{1}{2}\left(\operatorname *{Pr}\left[\mathcal{M}_{\epsilon}(D_{1})\in T_{0}^{\mathcal{M}_{\epsilon}}\right] + \operatorname *{Pr}\left[\mathcal{M}_{\epsilon}(D_{2})\in T_{1}^{\mathcal{M}_{\epsilon}}\right]\right) = e^{\epsilon}P_{L}} \end{array} \tag{77}
$$

We then have:

$$
P_{W}(1 + e^{\epsilon})\leq e^{\epsilon}(P_{W} + P_{L})\Rightarrow P_{W}\leq e^{\epsilon} / (1 + e^{\epsilon}) \tag{78}
$$

Thus, the advantage of the adversary can be bounded by:

$$
\mathbf{Adv}_{\epsilon -\mathrm{DP}}^{\mathrm{AMI}}(\mathcal{A}) = 2P_{W} - 1\leq \frac{e^{\epsilon} - 1}{e^{\epsilon} + 1} \tag{79}
$$

We then have the Theorem.

Lemma 3. Denote  $\mathcal{M}_{\epsilon}: \mathcal{X} \rightarrow \mathcal{X}$  a randomized function satisfied  $\epsilon$ - LDP. For a database  $D \in \mathcal{X}^n$ , we denote  $\mathcal{M}_{\epsilon}(D) \coloneqq \{\mathcal{M}_{\epsilon}(x): x \in D \}$ . Then, for all database  $D_{1}$  and  $D_{2}$  different by one entry, we have:

$$
\operatorname *{Pr}\left[\mathcal{M}_{\epsilon}(D_{1})\in T_{b^{\prime}}^{\mathcal{M}_{\epsilon}}\right]\leq e^{\epsilon}\operatorname *{Pr}\left[\mathcal{M}_{\epsilon}(D_{2})\in T_{b^{\prime}}^{\mathcal{M}_{\epsilon}}\right],
$$

where  $T_{b^{\prime}}^{\mathcal{M}_{\epsilon}}$  is the set of all database  $D$  such that the adversarial return  $b^{\prime}$  on that realization of  $\mathcal{M}_{\epsilon}$ .

Proof. Let  $S$  be an arbitrary subset of  $\mathcal{X}$ . For a pair  $t, t' \in \mathcal{X} \setminus S$ , consider the sets  $D_{1} = S \cup \{t\}$  and  $D_{2} = S \cup \{t'\}$ . Since  $\mathcal{M}_{\epsilon}$  satisfies  $\epsilon$ - LDP, we have:

$$
\operatorname *{Pr}\left[\mathcal{M}_{\epsilon}(t) = \mathcal{O}\right]\leq e^{\epsilon}\operatorname *{Pr}\left[\mathcal{M}_{\epsilon}(t^{\prime}) = \mathcal{O}\right],\quad \forall \mathcal{O}\in \mathcal{X} \tag{80}
$$

From the post- processing property of  $\epsilon$ - LDP, we have:

$$
\operatorname *{Pr}\left[g(\mathcal{M}_{\epsilon}(t)) = \mathcal{O}^{\prime}\right]\leq e^{\epsilon}\operatorname *{Pr}\left[g(\mathcal{M}_{\epsilon}(t^{\prime})) = \mathcal{O}^{\prime}\right],\quad \forall \mathcal{O}^{\prime}\in Range(g), \tag{81}
$$

for all function  $g:\mathcal{X}\rightarrow Range(g)$

In the AMI experiment, the guessing of the adversarial  $\mathcal{A}$ , i.e.,  $\mathcal{A}_{\mathsf{GUESS}}^D (t,\dot{\theta})$ , can be considered as a function on  $D_{\epsilon - \mathrm{DLP}} = \mathcal{M}_{\epsilon}(D)$  as  $\dot{\theta}$  is the result of some computations of  $\mathcal{M}_{\epsilon}(D)$ . We describe this as  $\mathcal{A}(\mathcal{M}_{\epsilon}(D)) = b^{\prime}$ .

We now show that

$$
\operatorname *{Pr}\left[\mathcal{A}(\mathcal{M}_{\epsilon}(D_1)) = b^{\prime}\right]\leq e^{\epsilon}\operatorname *{Pr}\left[\mathcal{A}(\mathcal{M}_{\epsilon}(D_2)) = b^{\prime}\right] \tag{82}
$$

We show (82) by contradiction. Suppose there exists  $D_{1}$  and  $D_{2}$  such that the condition does not hold. We can construct a function  $g:\mathcal{X}\rightarrow \{0,1\}$  as follow:

$$
g(\mathcal{M}_{\epsilon}(x)) = \mathcal{A}(\mathcal{M}_{\epsilon}(S)\cup \{\mathcal{M}_{\epsilon}(x)\}) \tag{83}
$$

With this, we have

$$
\begin{array}{rl} & {\operatorname *{Pr}[g(\mathcal{M}_{\epsilon}(t)) = b^{\prime}] = \operatorname *{Pr}[\mathcal{A}(\mathcal{M}_{\epsilon}(S)\cup \{\mathcal{M}_{\epsilon}(t)\})] = b^{\prime}] = \operatorname *{Pr}[\mathcal{A}(\mathcal{M}_{\epsilon}(D_1)) = b^{\prime}]}\\ & {>e^{\epsilon}\operatorname *{Pr}[\mathcal{A}(\mathcal{M}_{\epsilon}(D_2)) = b^{\prime}] = e^{\epsilon}\operatorname *{Pr}[\mathcal{A}(\mathcal{M}_{\epsilon}(S)\cup \{\mathcal{M}_{\epsilon}(t^{\prime})\})] = b^{\prime}] = e^{\epsilon}\operatorname *{Pr}[g(\mathcal{M}_{\epsilon}(t^{\prime})) = b^{\prime}]} \end{array} \tag{84}
$$

which contradicts (80).

Since condition (82) is equivalent to the condition stated in the Lemma, we then have the Lemma.

# G. Experimental Settings

This appendix outlines the experimental setup and implementation details of our work. Our experiments are implemented using Python 3.8 and executed on a single GPU- enabled compute node running a Linux 64- bit operating system. The node is allocated 36 CPU cores with 2 threads per core and 384GB of RAM. Additionally, the node is equipped with 2 RTX A6000 GPUs, each with 48GB of memory.

Table 1. General information of our reported experiments in the main manuscript.  

<table><tr><td>Experiments</td><td>No. runs</td><td>Adversary</td><td>Hyper-parameters</td><td>Dataset</td><td>Embedding</td><td>LDP Mechanism</td></tr><tr><td>Fig. 5, 6</td><td>1000</td><td>A P
Attn</td><td>β, γ</td><td>One-hot / Spherical</td><td>No</td><td>-</td></tr><tr><td>Fig. 7, 8</td><td>20 × 200</td><td>A F
FC</td><td>τ D, ε</td><td>CIFAR10 / CIFAR100</td><td>ResNet</td><td>BitRand/ GRR/ RAPPOR/ dBitFlipPM</td></tr><tr><td>Fig. 9 a,b</td><td>20 × 200</td><td>A Attn</td><td>β, γ, ε</td><td>CIFAR10</td><td>ViT-Base</td><td>BitRand/ GRR/ RAPPOR/ dBitFlipPM</td></tr><tr><td>Fig. 9 c</td><td>40 × 100</td><td>A Attn</td><td>β, γ, ε</td><td>ImageNet</td><td>ViT-Base</td><td>BitRand/ GRR/ RAPPOR/ dBitFlipPM</td></tr></table>

Table 1 shows the general information of our experiments reported in the main manuscripts. The hyper- parameters column refers to those of the adversaries, and  $\epsilon$  refers to the privacy budget. The embedding specifies how the dataset is transformed to obtain the data  $D$  for our testing of inference attacks. The No. runs indicates the total number of simulated security games for each point plotted in our figures. For instance, the number  $20\times 200$  means we conduct 20 trials of the experiment. Each trial consists of 200 games. The attack success rate is measured as  $\begin{array}{r}\frac{1}{2} (\mathrm{Pr}|b' = 1|b = 1] + \mathrm{Pr}|b' = 0|b = 0|) \end{array}$  . For each trial, everything is reset. In each trial, only the LDP- mechanism is re- run. Using equation 1 as well as lower (Theorem 1) and upper (Theorem 2) bound of  $\mathbf{Adv}_{\mathrm{LDP}}^{\mathrm{AMI}}(\mathcal{A}_{\mathrm{FC}}^D)$  , we can derive the theoretical lower and upper bound of the attack success rate.

The reported model accuracies (black lines) in all of our figures are obtained by evaluating the model on classification tasks. Particularly, for  $\mathcal{A}_{\mathrm{FC}}^D$  , we use ResNet's embedding combined with a Multilayer Perceptron to generate classifications. For  $\mathcal{A}_{\mathrm{FC}}^D$  , we use the native classification tasks and the original models published along with the datasets. To implement LDP, we add noise directly to ResNet's embedding. For ViTs, we add noise to the patch embeddings of the image. Since no pre- train model for ViTB- 32- 224 on CIFAR10/CIFAR100 are available, we fine- tune the model that was pre- trained on ImageNet- 21k on CIFAR10. The labels for classification in CIFAR10 are from the original dataset.

In the following appendices, we discuss more on the dataset and their embedding, the implementation of the adversaries, and the implementation of the LDP mechanisms. Those contents are in Appx. G.1, G.2, G.3 and G.4, respectively.

# G.1. Dataset and embedding

In total, our experiments are conducted on 2 synthetic datasets, 3 real- world datasets and 4 LDP mechanisms. The synthetic datasets are one- hot encoded data and Spherical data (data on the boundary of a unit ball). The real- world datasets are CIFAR10 (Krizhevsky et al., 2009) and ImageNet (Krizhevsky et al., 2012). The real- world datasets are pre- processed with practical pre- trained embedding modules to obtain the data  $D$  in the threat models. For ResNet, we use Img2Vec (Safka, 2021) to extract the feature embeddings of images and for ViTs, we use pretrained foundation models published on HuggingFace by the authors. The parameters of  $D$  in each experiment are given in Table 2.

For the synthetic datasets, we use a batch size of 1 since it does not affect the results of one- hot encoding. Furthermore, the setting also provides better intuition on the asymptotic behaviors of other datasets in Fig. 5 and Fig. 6. For ViTs, the number of patterns  $N_{X}$  is equal to the number of image patches. Details on how  $A_{\mathrm{Attn}}^D$  works on ViTs are given in G.2. The embedding dimensions  $d_{X}$  are determined by the choice of the embedding modules.

Table 2. Information of the data  $D$  in each testing dataset.  

<table><tr><td>Dataset</td><td>Embedding</td><td>Tested batch dimension n × dX × NX</td></tr><tr><td>One-hot</td><td>N/A</td><td>1 × [10,···,1000] × [5,10,15]</td></tr><tr><td>Spherical</td><td>N/A</td><td>1 × [10000,···,35000] × [5,10,15]</td></tr><tr><td>CIFAR10</td><td>ResNet-18</td><td>64 × 512 × 1</td></tr><tr><td>CIFAR100</td><td>ResNet-18</td><td>64 × 512 × 1</td></tr><tr><td>CIFAR10</td><td>ViT-B-32-224</td><td>[10,20,40] × 768 × 49</td></tr><tr><td>ImageNet</td><td>ViT-B-32-384</td><td>[10,20,40] × 768 × 144</td></tr></table>

# G.2. Implementation of  $A_{\mathrm{Attn}}^D$  on Vision Transformer.

G.2. Implementation of  $A_{\mathrm{Attn}}^D$  on Vision Transformer.First we describe the architecture of ViT, which was first proposed in (Dosovitskiy et al., 2021). First, the image is divided into  $L$  fixed-size patches (e.g.,  $16 \times 16$  pixels or  $32 \times 32$  pixels), which are flattened into vectors. Each patch is projected into a lower-dimensional embedding using a linear layer. Position embeddings are added to retain spatial information, and a learnable [class] embedding is included for global context. The sequence of embeddings is processed by  $L$  Transformer encoder layers. The output corresponding to the [class] embedding is passed through an MLP head to predict the image class. In summary, ViT treats image patches like words in a sentence, using the Transformer architecture to model relationships between patches and perform tasks like image classification. For naming scheme, ViT-B-32-224 means the model is ViT-Base, the image size is 224 and the patch size is 32. Figure 14 describes ViT's architecture.

![](images/f266f2b9d6a059178f0b3a4fe1d9fd2b6552631e0590fba49239ed2232b30df5.jpg)  
Figure 14. Architecture of Vision Transformer model. Image adapted from (Dosovitskiy et al., 2021).

Recall we represent the victim's dataset as  $D = \{X_i\}_{i = 1}^n$ , where  $X_i \in \mathcal{X}$ , and  $\mathcal{X} \subseteq \mathbb{R}^{d_X \times N_X}$ . For any 2- dimensional array

$X$ , each column  $x_{j} \in \mathbb{R}^{d_{X}}$  is referred to as a pattern. In the context of  $\mathcal{A}_{\mathrm{Attn}}^{\mathcal{D}}$  on ViT, since  $\mathcal{A}_{\mathrm{Attn}}^{\mathcal{D}}$  operates on the pattern level, it operates directly on the Patch+Position Embedding vectors. Given the image is divided into  $L$  patches,  $N_{X} = L$  in the attack, and  $d_{x}$  the dimension of each embedding vector. When we add LDP noise to the data, we add it directly to these vectors as well. For example, in the case of ViT- B- 32- 384, there are in total  $\frac{384}{32} \times \frac{384}{32} = 12 \times 12 = 144$  image patches, corresponding to  $N_{X} = 144$ .

# G.3. Implementation of the Adversaries

In our theoretical analysis of  $\mathcal{A}_{\mathrm{FC}}^{\mathcal{D}}$  and  $\mathcal{A}_{\mathrm{Attn}}^{\mathcal{D}}$ , we have specified how their hyper- parameters should be chosen so that theoretical guarantees can be achieved. For convenience reference, we restate those setting here:

- For Theorem 1,  $\tau^{\mathcal{D}}$  is set to  $\Delta^{\mathcal{X}}$ . The argument is made at Subsect. 4.1.

- For Theorem 3,  $\beta$  is chosen such that condition of the Theorem holds and  $\gamma$  is set to  $2\bar{\Delta}^{\epsilon}$ . The argument is made at Appx. D.4.

Table 3.Note on the values of  $\beta$  in experiments.  

<table><tr><td>Dataset</td><td>Note on β</td><td>Min β</td><td>Max β</td></tr><tr><td>One-hot / Spherical</td><td>β is is set to a constant</td><td>10</td><td>10</td></tr><tr><td>CIFAR10/100</td><td>The more noise, the smaller β</td><td>0.01</td><td>0.07</td></tr><tr><td>ImageNet</td><td>The more noise, the smaller β</td><td>0.01</td><td>0.07</td></tr></table>

However, as the adversaries generally know the data distribution and the LDP mechanism in practice, they can simulate the data as well as its protected version. We integrate these simulations into our implementations of  $\mathcal{A}_{\mathrm{FC}}^{\mathcal{D}}$  and  $\mathcal{A}_{\mathrm{Attn}}^{\mathcal{D}}$  to tune  $\tau^{\mathcal{D}}$  and  $\gamma$  before the security games. In fact, for a given LDP mechanism and an  $\epsilon$  privacy budget, the server uses a dataset from the data distribution  $\mathcal{D}$  and collects the layers' outputs before the ReLU activation. Then, a linear regression model is fitted on those outputs to estimate the biases  $\tau^{\mathcal{D}}$  and  $\gamma$  that will be used in the security games. Regarding the hyper- parameter  $\beta$ , while it cannot be tuned with linear regression, for each privacy budget of a security game, we try several values of  $\beta$  based on the statistic of  $\mathcal{D}$  before the game and settle with a look- up table for it. An example of possible values of  $\beta$  are given in Table 3. However, we found that simply setting  $\beta = 0.01$  usually give satisfactory results. We use  $\beta = 0.01$  for all NLP experiments.

# G.4. Details of LDP mechanisms

Generalized Randomized Response (GRR). Given an user with a value  $v \in \mathcal{X}$ . A random variable, denoted by  $\hat{X}$ , represents the response of the user on a value  $x$  also in  $\mathcal{X}$ . The generalized randomized response works as follows:

$$
\operatorname *{Pr}\left[\hat{X} = v\right] = \left\{ \begin{array}{ll}\frac{e^{\epsilon}}{e^{+d_{x}^{-1}}}, & \mathrm{if}~x = v\\ \frac{e^{+d_{x}^{-1}}}{e^{+d_{x}^{-1}}}, & \mathrm{if}~x\neq v \end{array} \right. \tag{86}
$$

where  $d \coloneqq |\mathcal{X}|$ .

RAPPOR (Randomized Aggregatable Privacy- Preserving Ordinal Response). Each user has a value  $v$  encoded as a Bloom filter vector  $B \in \{0,1\}^{k}$  using  $h$  hash functions. A permanent randomized response  $B'$  is generated as:

$$
B_{i}^{\prime} = \left\{ \begin{array}{ll}1 & \mathrm{with~prob.}\frac{1}{2} f\quad (\mathrm{flip~to~1})\\ 0 & \mathrm{with~prob.}\frac{1}{2} f\quad (\mathrm{flip~to~0})\\ B_{i} & \mathrm{with~prob.}1 - f\quad (\mathrm{keep~original~bit}) \end{array} \right.
$$

Then, the instantaneous randomized response  $S \in \{0,1\}^{k}$  is sampled from  $B'$  as:

$$
\operatorname *{Pr}[S_i = 1] = \left\{ \begin{array}{ll}q & \mathrm{if}~B_i' = 1\\ p & \mathrm{if}~B_i' = 0 \end{array} \right.
$$

dBitFlipPM. Given a user with a value  $v \in [k]$ , the mechanism proceeds as follows:

- The user selects  $d$  random buckets  $\{j_{1}, \ldots , j_{d}\} \subset [k]$  without replacement.- For each selected  $j_{p}$ , the user responds with a bit  $b_{j_{p}} \in \{0, 1\}$  such that:

$$
\operatorname *{Pr}[b_{j_p} = 1] = \left\{ \begin{array}{ll}\frac{e^{\epsilon / 2}}{e^{\epsilon / 2} + 1} & \mathrm{if} v = j_p\\ \frac{1}{e^{\epsilon / 2} + 1} & \mathrm{if} v\neq j_p \end{array} \right.
$$

The data collector reconstructs the histogram using:

$$
\hat{h}_t(v) = \frac{k}{nd}\sum_{i = 1}^n b_{i,v}(t)\cdot \frac{e^{\epsilon / 2} + 1}{e^{\epsilon / 2} - 1} -\frac{1}{e^{\epsilon / 2} - 1}
$$

This mechanism guarantees  $\epsilon$ - LDP with reduced communication cost and supports memoization for repeated collection.

In bit- flipping mechanisms like OME and BitRand, the original data or embedding features are first converted into binary vectors. The LDP mechanisms are then applied on top of those binary representations of the signal. After that, the protected binary signals are converted back to the original domain of the data before the training of the machine learning models.

In OME, each bit  $i$  of the binary representation is randomized based on the following probabilities:

$$
\forall i\in [0,rl - 1]:P(v_x'(i) = 1)\coloneqq \left\{ \begin{array}{ll}p_1x = \frac{\alpha}{1 + \alpha},\mathrm{if}i\in 2j,v_x(i) = 1\\ p_2x = \frac{1}{1 + \alpha^3},\mathrm{if}i\in 2j + 1,v_x(i) = 1\\ qx = \frac{1}{1 + \alpha\exp(\frac{\epsilon}{rl})},\mathrm{if}v_x(i) = 0 \end{array} \right. \tag{87}
$$

where  $v_{x}(i) \in \{0, 1\}$  is the value of the bit  $i$  in the binary representation,  $v_{x}^{\prime}$  is the perturbed binary vector,  $\epsilon$  is the privacy budget, and  $\alpha$  is a parameter of the algorithm.

On the other hand, BitRand introduces the bit- aware term  $\frac{i\%l}{l}$  to control the randomization probabilities. That bit- aware term helps the mechanism take the location of the bit into consideration for randomization. Intuitively, BitRand aims to apply less noise to bits that have more impact on the model utility. Particularly, the probabilities of perturbation are defined as:

$$
\forall i\in [0,rl - 1]:P(v_x'(i) = 1) = \left\{ \begin{array}{ll}p_x = \frac{1}{1 + \alpha\exp(\frac{i\%l}{l}\epsilon)},\mathrm{if}v_x(i) = 1\\ q_x = \frac{\alpha\exp(\frac{i\%l}{l}\epsilon)}{1 + \alpha\exp(\frac{i\%l}{l}\epsilon)},\mathrm{if}v_x(i) = 0 \end{array} \right. \tag{88}
$$

# H. Additional Experiments

# H.1. Experiments using OME mechanism

We observe that models trained on OME- protected data have almost constant performance across the tested range of the privacy budget  $\epsilon$ , as illustrated in Fig. 15. The same phenomenon of OME is observed and reported in multiple previous works (Arachchige et al., 2019; Lyu et al., 2020; Nguyen et al., 2023). The reason lies in the large sensitivity of the encoded binary representation in OME weakens the effect of  $\epsilon$  on the randomization probabilities (Lyu et al., 2020). With the exception of ImageNet, even with high loss in model utility, AMI adversaries still achieve a high successful inference rate.

# H.2. Empirical results on NLP datasets

Table 4, 5, 6 show the average accuracies, F1, and AUCs of AMI attacks on NLP datasets under GRR, RAPPOR and dBitFlipPM, respectively. Given the same privacy budget,  $\mathcal{A}_{\mathrm{FC}}^{\mathcal{D}}$  has higher accuracy than  $\mathcal{A}_{\mathrm{Attn}}^{\mathcal{D}}$ . GRR also usually performs worse as a defense mechanism compared to RAPPOR or dBitFlipM. Attention- based AMI also performed worse on LLM data compared to vision data. This could be due to the fact that it hard to bound the norm budget  $R^{\epsilon}$  of noise  $r_{i}$  due to the discrete nature of LDP noise when applied to NLP domain.

![](images/b08df99b02804a20e9704d6496e37edf0103ce6d5bfc80f6832b3d1ce18d2ec7.jpg)  
Figure 15. Success rates of AMI adversaries against datasets protected by OME.

Table 4. Average Accuracies, F1, and AUCs of AMI attacks under GRR defense.  

<table><tr><td rowspan="2">ε</td><td rowspan="2">Method</td><td colspan="3">BERT</td><td colspan="3">RoBERTa</td><td colspan="3">DistilBERT</td><td colspan="3">GPT1</td></tr><tr><td>ACC</td><td>F1</td><td>AUC</td><td>ACC</td><td>F1</td><td>AUC</td><td>ACC</td><td>F1</td><td>AUC</td><td>ACC</td><td>F1</td><td>AUC</td></tr><tr><td rowspan="2">∞</td><td>ADFC</td><td>1.00</td><td>1.00</td><td>1.00</td><td>1.00</td><td>1.00</td><td>1.00</td><td>1.00</td><td>1.00</td><td>1.00</td><td>1.00</td><td>1.00</td><td></td></tr><tr><td>ADATT</td><td>1.00</td><td>1.00</td><td>1.00</td><td>0.96</td><td>0.96</td><td>0.99</td><td>1.00</td><td>1.00</td><td>1.00</td><td>1.00</td><td>1.00</td><td></td></tr><tr><td rowspan="2">8</td><td>ADFC</td><td>1.00</td><td>1.00</td><td>1.00</td><td>1.00</td><td>1.00</td><td>1.00</td><td>1.00</td><td>1.00</td><td>1.00</td><td>1.00</td><td>1.00</td><td></td></tr><tr><td>ADATT</td><td>0.99</td><td>0.99</td><td>1.00</td><td>0.89</td><td>0.88</td><td>0.95</td><td>0.99</td><td>0.98</td><td>1.00</td><td>0.97</td><td>0.97</td><td>0.99</td></tr><tr><td rowspan="2">6</td><td>ADFC</td><td>0.99</td><td>0.99</td><td>1.00</td><td>0.97</td><td>0.97</td><td>1.00</td><td>0.97</td><td>0.97</td><td>1.00</td><td>0.99</td><td>0.99</td><td>1.00</td></tr><tr><td>ADATT</td><td>0.86</td><td>0.84</td><td>0.94</td><td>0.75</td><td>0.72</td><td>0.82</td><td>0.86</td><td>0.83</td><td>0.94</td><td>0.86</td><td>0.84</td><td>0.93</td></tr><tr><td rowspan="2">4</td><td>ADFC</td><td>0.86</td><td>0.83</td><td>0.91</td><td>0.86</td><td>0.85</td><td>0.90</td><td>0.83</td><td>0.80</td><td>0.87</td><td>0.78</td><td>0.75</td><td>0.82</td></tr><tr><td>ADATT</td><td>0.56</td><td>0.52</td><td>0.59</td><td>0.56</td><td>0.53</td><td>0.55</td><td>0.57</td><td>0.52</td><td>0.60</td><td>0.57</td><td>0.53</td><td>0.60</td></tr><tr><td rowspan="2">2</td><td>ADFC</td><td>0.59</td><td>0.57</td><td>0.63</td><td>0.59</td><td>0.55</td><td>0.62</td><td>0.50</td><td>0.50</td><td>0.50</td><td>0.55</td><td>0.52</td><td>0.52</td></tr><tr><td>ADATT</td><td>0.55</td><td>0.50</td><td>0.54</td><td>0.50</td><td>0.50</td><td>0.50</td><td>0.51</td><td>0.50</td><td>0.50</td><td>0.50</td><td>0.50</td><td>0.52</td></tr></table>

Table 5. Average Accuracies, F1, and AUCs of AMI attacks under RAPPOR defense.  

<table><tr><td rowspan="2">ε</td><td rowspan="2">Method</td><td colspan="3">BERT</td><td colspan="3">RoBERTa</td><td colspan="3">DistilBERT</td><td colspan="3">GPT1</td></tr><tr><td>ACC</td><td>F1</td><td>AUC</td><td>ACC</td><td>F1</td><td>AUC</td><td>ACC</td><td>F1 AUC</td><td>AUC</td><td>F1 AUC</td><td>AUC</td><td>F1 AUC</td></tr><tr><td rowspan="2">∞</td><td>ADFC</td><td>1.00</td><td>1.00</td><td>1.00</td><td>1.00</td><td>1.00</td><td>1.00</td><td>1.00</td><td>1.00</td><td>1.00</td><td>1.00</td><td>1.00</td><td></td></tr><tr><td>1.00</td><td>1.00</td><td>1.00</td><td>0.96</td><td>0.96</td><td>0.99</td><td>1.00</td><td>1.00</td><td>1.00</td><td>1.00</td><td>1.00</td><td>1.00</td><td></td></tr><tr><td rowspan="2">8</td><td>ADFC</td><td>0.98</td><td>0.98</td><td>1.00</td><td>0.98</td><td>0.98</td><td>1.00</td><td>0.96</td><td>0.96</td><td>0.99</td><td>0.98</td><td>0.97</td><td>1.00</td></tr><tr><td>0.73</td><td>0.69</td><td>0.79</td><td>0.52</td><td>0.49</td><td>0.54</td><td>0.79</td><td>0.77</td><td>0.86</td><td>0.66</td><td>0.61</td><td>0.72</td><td></td></tr><tr><td rowspan="2">6</td><td>ADFC</td><td>0.81</td><td>0.79</td><td>0.89</td><td>0.88</td><td>0.87</td><td>0.94</td><td>0.73</td><td>0.72</td><td>0.80</td><td>0.76</td><td>0.73</td><td>0.83</td></tr><tr><td>0.54</td><td>0.50</td><td>0.56</td><td>0.53</td><td>0.50</td><td>0.51</td><td>0.55</td><td>0.51</td><td>0.56</td><td>0.52</td><td>0.50</td><td>0.53</td><td></td></tr><tr><td rowspan="2">4</td><td>ADFC</td><td>0.61</td><td>0.59</td><td>0.68</td><td>0.66</td><td>0.65</td><td>0.70</td><td>0.56</td><td>0.54</td><td>0.64</td><td>0.68</td><td>0.66</td><td>0.77</td></tr><tr><td>0.50</td><td>0.50</td><td>0.50</td><td>0.50</td><td>0.50</td><td>0.52</td><td>0.50</td><td>0.50</td><td>0.50</td><td>0.50</td><td>0.50</td><td>0.50</td><td></td></tr><tr><td rowspan="2">2</td><td>ADFC</td><td>0.61</td><td>0.58</td><td>0.67</td><td>0.58</td><td>0.56</td><td>0.58</td><td>0.60</td><td>0.58</td><td>0.65</td><td>0.61</td><td>0.60</td><td>0.60</td></tr><tr><td>0.50</td><td>0.50</td><td>0.50</td><td>0.50</td><td>0.50</td><td>0.50</td><td>0.50</td><td>0.50</td><td>0.50</td><td>0.50</td><td>0.50</td><td>0.50</td><td></td></tr></table>

# H.3. ROC analysis

We also conduct an ROC analysis of the attack success rates (on IMDB dataset). GRR shows the worst privacy with attack AUCs of 0.946 ( $\epsilon = 6$ ) and 1.0 ( $\epsilon = 8$ ), while RAPPOR and dBitFlipPM provide stronger protection—achieving near- random performance at and moderate resistance at. Zoomed- in plots show that GRR leaks sensitive signals even at low FPRs and high TPRs, while RAPPOR and dBitFlipPM maintain partial robustness in these critical regions. Details are given in Fig. 16.

# I. Alternative privacy-preserving techniques beyond LDP

Other works have pursued cryptography- based techniques such as secure multi- party computation (SMPC) or homomorphic encryption (HE) that preserve privacy without adding excessive noise, hence preserving utility. SMPC- based FL systems

Table 6. Average Accuracies, F1, and AUCs of AMI attacks under dBitFlipPM defense.  

<table><tr><td rowspan="2">ε</td><td rowspan="2">Method</td><td colspan="3">BERT</td><td colspan="3">RoBERTa</td><td colspan="3">DistilBERT</td><td colspan="3">GPT1</td></tr><tr><td>ACC</td><td>F1</td><td>AUC</td><td>ACC</td><td>F1</td><td>AUC</td><td>ACC</td><td>F1</td><td>AUC</td><td>ACC</td><td>F1</td><td>AUC</td></tr><tr><td rowspan="2">∞</td><td>ADFC</td><td>1.00</td><td>1.00</td><td>1.00</td><td>1.00</td><td>1.00</td><td>1.00</td><td>1.00</td><td>1.00</td><td>1.00</td><td>1.00</td><td>1.00</td><td></td></tr><tr><td>ADATT</td><td>1.00</td><td>1.00</td><td>1.00</td><td>0.96</td><td>0.96</td><td>0.99</td><td>1.00</td><td>1.00</td><td>1.00</td><td>1.00</td><td>1.00</td><td></td></tr><tr><td rowspan="2">8</td><td>ADFC</td><td>0.96</td><td>0.96</td><td>0.99</td><td>0.98</td><td>0.98</td><td>1.00</td><td>0.96</td><td>0.95</td><td>1.00</td><td>0.98</td><td>0.97</td><td>0.99</td></tr><tr><td>ADATT</td><td>0.72</td><td>0.72</td><td>0.84</td><td>0.65</td><td>0.59</td><td>0.73</td><td>0.76</td><td>0.72</td><td>0.84</td><td>0.65</td><td>0.59</td><td>0.73</td></tr><tr><td rowspan="2">6</td><td>ADFC</td><td>0.79</td><td>0.77</td><td>0.87</td><td>0.84</td><td>0.82</td><td>0.92</td><td>0.69</td><td>0.67</td><td>0.76</td><td>0.77</td><td>0.75</td><td>0.82</td></tr><tr><td>ADATT</td><td>0.55</td><td>0.50</td><td>0.55</td><td>0.53</td><td>0.50</td><td>0.51</td><td>0.55</td><td>0.50</td><td>0.59</td><td>0.52</td><td>0.50</td><td>0.53</td></tr><tr><td rowspan="2">4</td><td>ADFC</td><td>0.65</td><td>0.65</td><td>0.74</td><td>0.69</td><td>0.68</td><td>0.75</td><td>0.50</td><td>0.50</td><td>0.51</td><td>0.65</td><td>0.63</td><td>0.68</td></tr><tr><td>ADATT</td><td>0.50</td><td>0.50</td><td>0.50</td><td>0.50</td><td>0.50</td><td>0.52</td><td>0.50</td><td>0.50</td><td>0.50</td><td>0.50</td><td>0.50</td><td>0.50</td></tr><tr><td rowspan="2">2</td><td>ADFC</td><td>0.61</td><td>0.59</td><td>0.64</td><td>0.56</td><td>0.53</td><td>0.59</td><td>0.59</td><td>0.56</td><td>0.59</td><td>0.63</td><td>0.61</td><td>0.67</td></tr><tr><td>ADATT</td><td>0.50</td><td>0.50</td><td>0.50</td><td>0.50</td><td>0.50</td><td>0.50</td><td>0.50</td><td>0.50</td><td>0.50</td><td>0.50</td><td>0.50</td><td>0.50</td></tr></table>

![](images/9f743bdfd650e96de39a444fbf5acc33eb39562689ff4e9cc169bc1b28f07615.jpg)  
a) FPR vs TPR with AUC scores for Attention-based AMI attack against different LDP mechanisms at epsilon  $= 6$

![](images/cfdf2433c66dec2f77553181f5c25beb72fada9f58c0d0fe41adcb6b7c19023a.jpg)  
b) FPR vs TPR with AUC scores for Attention-based AMI attack against different LDP mechanisms at epsilon  $= 8$ Figure 16. ROC analysis of Attention-based AMI on LDP-protected IMDB dataset.

offer strong privacy guarantees by ensuring no party learns individual updates via secure aggregation protocols (Ma et al., 2023; Bonawitz et al., 2017), but they incur high communication overhead, especially as the number of participants grows. On the other hand, HE provides end- to- end encryption in FL, allowing computations directly on encrypted data, but it introduces significant computational costs due to the complexity of cryptographic operations (Nguyen & Thai, 2023; Pan et al., 2024). Furthermore, encryption alone does not protect against inference from the final global model: if an adversary obtains the trained model, they could still perform membership inference or other attacks. Recent works combine LDP and HE/SMPC, potentially providing a comprehensive solution (Aziz et al., 2023). Additionally, we note that due to the high communication and computation overhead, secure aggregation might not be feasible in certain FL applications.

It is worth noting that secure aggregation (e.g., Secure Multi- Party Computation, Homomorphic Encryption) and Local Differential Privacy (LDP) are orthogonal research directions. SMPC/HE primarily aims to conceal local gradients from the server during aggregation, ensuring no individual gradient is exposed. LDP focuses on preventing local gradients from revealing membership information about specific data points by adding noise to the local data/ gradient.

Our threat model assumes an actively dishonest server that has access to the local gradients of clients, and conducts the proposed AMI attacks based on the local gradients. Even though using SMPC or HE could potentially conceal such info from

the server, previous research has shown that an actively dishonest adversary can circumvent secure aggregation protocols (Ngo et al., 2024; Gao et al., 2021; Liu et al., 2023; Pasquini et al., 2022; Kariyappa et al., 2023; Nguyen et al., 2022) to reconstruct the targeted client's gradients. Hence, even with secure aggregation, our threat model is still applicable when the server uses the above attacks to get around it and access local gradients before conducting the AMI attacks. Therefore, assuming that the dishonest server successfully circumvents secure aggregation, such techniques as SMPC or HE do not impact the success rates or the theoretical analysis of our proposed AMI attacks.