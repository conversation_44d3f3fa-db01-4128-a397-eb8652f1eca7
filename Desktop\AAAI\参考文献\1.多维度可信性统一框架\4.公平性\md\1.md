# Towards Collaborative Fairness in Federated Learning Under Imbalanced Covariate Shift

Tianrun Yu The Pennsylvania State University University Park, PA, USA <EMAIL>

<PERSON><PERSON><PERSON> The Pennsylvania State University University Park, PA, USA <EMAIL>

Haoyu Wang State University of New York at Albany Latham, NY, USA <EMAIL>

<PERSON><PERSON>n Lin University of Minnesota Twin Cities Minneapolis, MN, USA <EMAIL>

<PERSON>ian University of Technology Dalian, Liaoning, China <EMAIL>

Nelson S. Yee The Pennsylvania State University Hershey, PA, USA <EMAIL>

Fenglong Ma\* The Pennsylvania State University University Park, PA, USA <EMAIL>

# ABSTRACT

ABSTRACTCollaborative fairness is a crucial challenge in federated learning. However, existing approaches often overlook a practical yet complex form of heterogeneity: imbalanced covariate shift. We provide a theoretical analysis of this setting, which motivates the design of FedAKD (Federated Asynchronous Knowledge Distillation)—a simple yet effective approach that balances accurate prediction with collaborative fairness. FedAKD consists of client and server updates. In the client update, we introduce a novel asynchronous knowledge distillation strategy based on our preliminary analysis, which reveals that while correctly predicted samples exhibit similar feature distributions across clients, incorrectly predicted samples show significant variability. This suggests that imbalanced covariate shift primarily arises from misclassified samples. Leveraging this insight, our approach first applies traditional knowledge distillation to update client models while keeping the global model fixed. Next, we select correctly predicted high- confidence samples and update the global model using these samples while keeping client models fixed. The server update simply aggregates all client models. We further provide a theoretical proof of FedAKD's convergence. Experimental results on public datasets (FashionMNIST and CIFAR10) and a real- world Electronic Health Records (EHR) dataset demonstrate that FedAKD significantly improves collaborative fairness, enhances predictive accuracy, and fosters client participation even under highly heterogeneous data distributions.

# CCS CONCEPTS

CCS CONCEPTS- Information systems  $\rightarrow$  Information systems applications.

# KEYWORDS

KEYWORDsfederated learning, collaborative fairness, covariate shift, knowledge distillation, imbalanced data

# ACM Reference Format:

ACM Reference Format:Tianrun Yu, Jiaqi Wang, Haoyu Wang, Mingquan Lin, Han Liu, Nelson S. Yee, and Fenglong Ma. 2025. Towards Collaborative Fairness in Federated Learning Under Imbalanced Covariate Shift. In Proceedings of the 31st ACM SIGKDD Conference on Knowledge Discovery and Data Mining (KDD '25), August 3- 7, 2025, Toronto, ON, Canada. ACM, New York, NY, USA, 18 pages. https://doi.org/10.1145/3711896.3737161

# 1 INTRODUCTION

Federated Learning (FL) has emerged as a promising distributed paradigm that enables multiple participants (or clients) to collaboratively train a global model without sharing their raw local data [6, 9, 15, 24, 31]. However, disparities in data quality, quantity, and distribution among clients make uniform treatment in global model aggregation unfair, particularly for those with higher- quality or larger datasets. To address this, collaborative fairness (CF) has been introduced to ensure that each client's final reward or benefit is commensurate with its contribution to the global model [13]. In other words, clients with a greater impact on model performance should receive proportionally higher gains.

Several fairness- aware FL approaches, such as CGSV [23], CFFL [13], FedAve [19], and FedSAC [20], have been developed to assign different contribution- based rewards (i.e., weights) during model aggregation. While these methods help mitigate fairness disparities, they still face the following challenges:

Unrealistic assumptions about data heterogeneity. Existing CF approaches primarily assume that data heterogeneity arises from imbalanced data sizes [13, 23], imbalanced class distributions [13, 23], or both [1, 28]. However, real- world datasets exhibit greater complexity. Beyond these imbalances, client data

![](images/c23001d6785b7723d7797fd03a191c29c0af5305aea48beeb9c2129698c6fe0b.jpg)  
Figure 1: (a) KL divergence vs. sample size for each client's local data, revealing both data imbalance and feature covariate shifts. (b)-(e) compare different fairness methods' contribution definitions to each client's standalone training accuracy. Their poor correlation highlights the limitations of explicit contribution metrics under extra covariate shifts.

often differ significantly in feature distributions, leading to the covariate shift problem [1, 2, 5, 9]. Figure 1(a) presents a preliminary analysis on a real- world electronic health record (EHR) dataset for pancreatic cancer prediction. The  $x$ - axis represents client dataset sizes, while the  $y$ - axis shows the Kullback- Leibler (KL) divergence between each client's fitted latent feature distribution and that of the entire dataset. Each circle represents a client, i.e., a U.S. state in the EHR dataset. The preliminary data analysis reveals that not only do clients have varying dataset sizes, but their latent feature distributions also significantly diverge from the global reference distribution. Thus, a more realistic FL heterogeneity setting should account for imbalanced covariate shift rather than just data quantity or class imbalance.

- Weak correlation between client accuracy and assigned contributions. Beyond unrealistic data distribution assumptions, existing approaches to collaborative fairness typically follow a two-step pipeline: (1) explicitly defining a contribution metric and (2) allocating rewards based on this metric. For example, CGSV [23] estimates client contributions via gradient similarity, CFFL [13] and FedAVE [19] rely on performance improvements measured by a global validation set, and FedSAC [20] bases contributions on standalone local training results. However, in real-world datasets characterized by imbalanced covariate shift, these approaches fail to establish a strong correlation between client accuracy and assigned contributions, contradicting their underlying design assumptions.

Figures 1(b)- (e) illustrate the relationship between client standalone accuracy ( $x$ - axis) on the testing set and the learned contribution value ( $y$ - axis) either on the training set or validation set under different methods - CFFL, CGSV, FedAVE, and the baseline FedAvg [15] - using the real- world EHR dataset, which is the same as we analyzed in Figure 1(a). Each dot represents an individual client, and in the case of FedAvg, the contribution score is simply the proportion of data owned by the client. The results show that existing CF- based methods exhibit significantly lower Pearson correlation scores compared to the simple FedAvg baseline. These findings highlight the limitations of existing approaches in handling realistic imbalanced covariate shift settings.

Theoretical analysis on imbalanced covariate shift. This paper aims to develop a novel model to ensure collaborative fairness in federated learning under the realistic yet challenging imbalanced covariate shift setting. To achieve this, in Section 2, we first provide a theoretical analysis in Theorem 2.1 demonstrating that imbalanced covariate shift - quantified as the KL divergence between each client's empirical data distribution and the ideal global distribution - is primarily influenced by the perturbation  $\delta$ . Specifically, if the underlying data distributions of individual clients and the entire dataset follow a multivariate normal distribution  $\mathcal{N}(\mu , \Sigma)$ , where  $\mu$  is the mean vector, we show that  $\delta$  and the covariance  $\Sigma$  play key roles in determining the extent of distributional divergence in Theorem 2.2. The two theorems motivate us to mitigate the imbalanced covariate shift to achieve collaborative fairness by correctly qualifying  $\delta$  and  $\Sigma$ . However, directly calculating these values is infeasible, as the true data distributions are inherently unknown.

Motivations of model design. To address this challenge, we conducted a preliminary analysis on the results of FedAvg applied to the entire EHR dataset. For each client  $k$ , we categorized the correctly and incorrectly predicted samples as  $\mathcal{I}_k$  and  $\mathcal{D}_k - \mathcal{I}_k$ , respectively, and aggregated these categories across all clients to form the global sets  $\{\mathcal{I}_k\}_{k = 1}^K$  and  $\{\mathcal{D}_k - \mathcal{I}_k\}_{k = 1}^K$ , where  $\mathcal{D}_k$  denotes the client dataset, and  $K$  is the number of clients. Next, we applied principal component analysis (PCA) to project each sample's latent representation (i.e., the encoder output from each client model) onto a 1- D space and used kernel density estimation (KDE) to estimate the probability density function (PDF) for each set. Figures 2(a) and (b) show a comparison of the global density function with the density functions of two clients (Minnesota and New Hampshire). The  $x$ - axis represents the projected PCA 1- D values, and the  $y$ - axis represents the estimated PDF values. Similar to Figure 1(a), we also analyze the distribution differences between local and global data in terms of correct and incorrect classifications, as illustrated in Figure 2(c). These results indicate that the primary source of imbalanced covariate shift lies in the "incorrect" samples, as clients generally show agreement on the distribution of "correct" predictions.

Our approach. Building on our theoretical and empirical analysis, we propose FedAKD, a novel framework designed to address the imbalanced covariate shift challenge while ensuring collaborative fairness. FedAKD leverages a new federated asynchronous knowledge distillation approach, comprising client updates and server updates in each communication round  $t$ . Specifically, the client update includes three key steps: (1) Global  $\rightarrow$  Local Distillation: Using traditional knowledge distillation [4], we employ the global model  $\mathbf{w}_g^t$  as a teacher to guide the client model  $\mathbf{w}_k^t$  in learning from its full training set  $\mathcal{D}_k$ . (2) High- confidence Sample Selection: Inspired by our observations in Figure 2, correctly

![](images/71d0ea1fa9d5701fb354044a0b2312201ceafda58db2d29055b403f936e98755.jpg)  
Figure 2: (a) Distribution of locally versus globally correct samples. (b) Distribution of locally versus globally incorrect samples. (c) KL divergence for "right" vs. "wrong" samples. We observe that the feature distributions of correctly classified samples closely resemble the global distribution, whereas those of misclassified samples deviate significantly. This suggests that imbalanced covariate shift primarily arises from incorrectly classified samples.

predicted samples positively contribute to the global model update. Thus, we first identify the correctly classified samples from the updated local model, denoted as  $\bar{I}_k^t$ . (3) Local  $\rightarrow$  Global Distillation: The global model  $\mathbf{w}_g^t$  is then refined by distilling "high- confidence" client knowledge from  $\mathbf{w}_k^t$  using each client's correctly predicted set  $\bar{I}_k^t$ . This design helps mitigate distortions caused by misclassified data under covariate shift conditions. The updated global model for each client (i.e.,  $\mathbf{w}_k^t$ ) is uploaded to the server, where it is aggregated following FedAvg [15] in the server update step. These two updates iterate until FedAKD converges. The theoretical convergence of FedAKD is established in Theorem 3.2.

Through this two- stage asynchronous distillation process, FedAKD effectively encourages fair collaboration even when clients have highly divergent feature distributions. High- quality participants benefit by sharing more correct samples, whereas lower- quality participants gain from the improved global knowledge, thus collectively promoting collaborative fairness without imposing rigid or impractical contribution measurements.

Contributions. The main contributions of this work include:

- A new heterogeneity setting. We introduce collaborative fairness under the imbalanced covariate shift, a practical challenge in real-world medical datasets where both sample-size imbalance and feature-distribution mismatch significantly hinder the effectiveness of existing collaborative fairness metrics.- A simple yet effective solution. We propose FedAKD, a novel asynchronous distillation framework that first distills knowledge from correctly predicted local samples to improve the global model quality, followed by an inverse distillation step to enhance client learning across the full dataset.- Theoretical guarantees. We provide a rigorous theoretical analysis of imbalanced covariate shift, expanding the KL divergence parametrically to model real-world heterogeneity. Additionally, we prove the convergence of FedAKD under broad heterogeneity conditions, ensuring both theoretical soundness and improved collaborative fairness.- Promising results. We conduct extensive experiments on three datasets, evaluating FedAKD against ten baselines across four heterogeneity settings using three evaluation metrics. The results demonstrate that FedAKD effectively addresses imbalanced covariate shift and outperforms all baselines across diverse heterogeneity scenarios.

covariate shift and outperforms all baselines across diverse heterogeneity scenarios.

# 2 IMBALANCED COVARIATE SHIFT ANALYSIS

Imbalanced covariate shift presents a significant and open challenge in federated learning, requiring a deeper mathematical understanding to effectively mitigate disparities in collaborative fairness. To formalize this, we assume the existence of a global data feature distribution  $p_{\omega}$ . Each client's data distribution is modeled as a small parametric perturbation, denoted as  $p_{\omega +\delta}$ , where  $\delta \in \mathbb{R}^N$  represents the perturbation vector and  $N$  is the model size. When a client draws an i.i.d. sample set of size  $A$  from  $\mathbf{p}_{(\delta +\delta)}$ , it results in the empirical distribution  $\widehat{p}_{\omega +\delta}$ . Consequently, the imbalanced covariate shift can be quantitatively assessed by measuring the KL divergence between the empirical distribution  $\widehat{p}_{\omega +\delta}$  and the original global distribution  $p_{\omega}$ .

THEOREM 2.1 (IDEAL IMBALANCE COVARIATE SHIFT QUANTIFICATION). Let  $\{p_{\theta}\}_{\theta \in \Theta}$  be a smooth parametric family of probability distributions, and let  $\omega \in \Theta$  denote a baseline parameter. Suppose that a perturbed distribution  $p_{\omega +\delta}$  is defined by a small perturbation  $\delta \in \mathbb{R}^N$ . Given that  $p_{\omega +\delta}$  is estimated via an empirical distribution  $\widehat{p}_{\omega +\delta}$  using  $A$  i.i.d. samples and  $R$  (free) parameters, we have the following approximation under standard regularity conditions and a large- sample limit:

$$
D_{\mathrm{KL}}\big(\widehat{p}_{\omega +\delta}\big\| p_{\omega}\big)\approx \frac{1}{2}\delta^{\top}I(\omega)\delta +\frac{1}{2}\delta^{\top}(\nabla_{\omega}I(\omega)\delta)\delta +\frac{R}{2A},
$$

where  $I(\omega)$  is the Fisher information matrix at  $\omega$ , and  $\nabla_{\omega}I(\omega)$  denotes the gradients with respect to  $\omega$ .

We assume that the probabilistic distribution follows an  $M$ - dimensional Gaussian distribution. We then extend Theorem 2.1 as follows:

THEOREM 2.2 (IMBALANCE COVARIATE SHIFT QUANTIFICATION UNDER GAUSSIAN DISTRIBUTION). Let  $p_{\mu ,\Sigma}(x) = N(x; \mu , \Sigma)$  be a  $M$ - dimensional Gaussian distribution, where  $\mu \in \mathbb{R}^M$  is the mean vector and  $\Sigma \in \mathbb{R}^{M\times M}$  is a symmetric positive- definite covariance matrix. Suppose  $\omega = (\mu_0, \Sigma_0)$  is a baseline parameter, and consider a small perturbation  $\delta = (\delta_{\mu}, \delta_{\Sigma}), \theta ' = \omega + \delta \approx (\mu_0 + \delta_{\mu}, \Sigma_0 + \delta_{\Sigma})$ . Under a large- sample limit, the Kullback- Leibler divergence between

the empirical distribution  $\widehat{p}_{\theta^{\prime}}$  (fitted from A i.i.d. samples drawn from  $p_{\theta^{\prime}}$ ) and the baseline model  $p_{\omega}$  can be approximated by:

$$
\begin{array}{r l} & {D_{\mathrm{KL}}(\widehat{p}_{\theta^{\prime}}\| p_{\omega})\approx \frac{1}{4}\| \Sigma^{-1}\delta_{\Sigma}\Sigma^{-1}\|_{F}^{2} - \frac{1}{2}\mathrm{trace}\Big((\delta_{\Sigma}\Sigma^{-1})^{3}\Big)}\\ & {\qquad +\frac{1}{2} (\delta_{\mu})^{\top}(\Sigma^{-1}(I - \delta_{\Sigma}\Sigma^{-1}))\delta_{\mu} + \frac{M(M + 3)}{4A}.} \end{array} \tag{1}
$$

Together, these two theorems provide a unified mathematical framework to model the combined effects of imbalanced sample sizes and covariate shift. This framework provides a principled approach to analyzing how local client distributions diverge from the global baseline, offering deeper insights into collaborative fairness in federated learning. Detailed proofs are provided in Appendix A. We also provide a theoretical approximation validation experiment in Appendix B to validate the correctness of our theorems.

# 3 THE PROPOSED FEDAKD

While our theoretical analysis in Section 2 provides valuable insights into imbalanced covariate shift, it cannot be directly applied to model design, as the global distribution remains unknown in federated learning. However, these theorems reveal that the imbalanced covariate shift arises due to small perturbations in client distributions. Our preliminary analysis (Figure 2 in Section 1) further suggests that these perturbations predominantly stem from incorrectly classified samples. This observation motivates us to develop an effective collaborative fairness approach named FedAKD that mitigates the imbalanced covariate shift by addressing the impact of misclassified samples via a simple asynchronous knowledge distribution strategy. The algorithm is shown in Algorithm 1.

Similar to existing collaborative fairness approaches in federated learning, FedAKD comprises both client and server updates. However, unlike prior methods that require carefully designing reward weights for each client [13, 19, 20, 23], FedAKD simplifies aggregation by directly following the standard FedAvg [15] in the server update. The novelty of FedAKD lies in the client update, where we introduce a new asynchronous knowledge distillation strategy, inspired by our preliminary analysis. Specifically, the client update consists of three key steps: (1) global  $\rightarrow$  local distillation, (2) high- confidence sample selection, and (3) local  $\rightarrow$  global distillation. Next, we provide the details of these three steps.

# 3.1 Step 1: Global  $\rightarrow$  Local Distillation

The global model  $\mathbf{w}_g^t$  contains aggregated knowledge, but forcing all clients to adopt  $\mathbf{w}_{g,k}^t = \mathbf{w}_g^t$  directly may harm local performance due to the imbalanced covariate shift. To avoid this issue, we propose global  $\rightarrow$  local distillation, enabling each client to selectively adopt global insights while retaining local specialization by optimizing the following loss:

$$
\overrightarrow{\mathcal{L}}_t = \mathrm{CE}(\mathbf{w}_k^{t - 1};\mathcal{D}_k) + \alpha \mathrm{KD}(\mathbf{w}_k^{t - 1};\mathbf{w}_{g,k}^t;\mathcal{D}_k), \tag{2}
$$

where CE denotes the cross- entropy loss, KD is the knowledge distillation loss, and  $\alpha$  is the hyperparameter. The gradient update yields:

$$
\mathbf{w}_k^t = \mathbf{w}_k^{t - 1} - \eta \nabla \overrightarrow{\mathcal{L}}_t, \tag{3}
$$

# Algorithm 1 FedAKD

Require:  $K$  clients; local datasets  $\{\mathcal{D}_k\}$ ; total rounds  $T$ ; learning rate  $\eta$ ; distillation coefficients  $\alpha$  and  $\beta$

1: Initialization: 2: Generate an initial model  $\mathbf{w}^0$  e.g.,randomly) 3: Client side (for each  $k$  ): set each local model  $\mathbf{w}_k^0 = \mathbf{w}^0$  4: Server side: set the global model  $\mathbf{w}_g^0 = \mathbf{w}^0$  and distribute 5:  $\mathbf{w}_g^t$  to clients; 6:for  $t = 1,\dots ,T$  do 7: // Client Update 8: for  $k = 1,2,\dots ,K$  do 9: // Step 1: Global  $\longrightarrow$  Local Distillation 10:  $\mathbf{w}_{g,k}^{t}\gets \mathbf{w}_{g}^{t}$  11: // Loss computation by fixing  $\mathbf{w}_{g,k}^{t}$  using  $\mathcal{D}_k$  12:  $\overrightarrow{\mathcal{L}}_t = \mathrm{CE}(\mathbf{w}_k^{t - 1};\mathcal{D}_k) + \alpha \mathrm{KD}(\mathbf{w}_k^{t - 1};\mathbf{w}_{g,k}^t,\mathcal{D}_k);$  13: //update model parameters 14:  $\mathbf{w}_k^t\gets \mathbf{w}_k^{t - 1} - \eta \nabla \overrightarrow{\mathcal{L}}_t;$  15: // Step 2: High- confidence Sample Selection 16:  $\mathcal{I}_k^t = \left\{(\mathbf{x},y)\in \mathcal{D}_k\mid \mathrm{Pred}(\mathbf{w}_k^t,\mathbf{x}) = y\right\}$  17: // Step 3: Local  $\longrightarrow$  Global Distillation 18: // Loss computation by fixing  $\mathbf{w}_k^t$  using  $\mathcal{I}_k^t$  19:  $\overleftarrow{\mathcal{L}}_t = \mathrm{CE}(\mathbf{w}_{g,k}^t;\mathcal{I}_k^t) + \beta \mathrm{KD}(\mathbf{w}_{g,k}^t;\mathbf{w}_k^t,\mathcal{I}_k^t);$  20: // Update model parameters 21:  $\mathbf{w}_{g,k}^{t + 1}\gets \mathbf{w}_{g,k}^t - \eta \nabla \overleftarrow{\mathcal{L}}_t;$  22: Upload  $\mathbf{w}_{g,k}^{t + 1}$  to the server; 23: end for 24: // Server Update 25:  $\mathbf{w}_g^{t + 1} = \frac{\sum_{k = 1}^t\left(|\mathcal{D}_k|\mathbf{w}_{g,k}^t\right)}{\sum_{k = 1}^K|D_k|}\Sigma_{k = 1}^{K - 1}\left(|\mathcal{D}_k|\mathbf{w}_{g,k}^t\right)$ ; 26: Distribute  $\mathbf{w}_g^{t + 1}$  to each client; 27:end for 28: Output: The global model  $\mathbf{w}_g^T$  and local models  $\{\mathbf{w}_k^T\}$

where  $\eta$  is the learning rate. In this step, we fix the global model parameters  $\mathbf{w}_g^t$  and only update the client model parameters  $\mathbf{w}_k^{t - 1}$  using the full training set  $\mathcal{D}_k$ . This procedure allows each client to merge the updated global knowledge with its local parameters, safeguarding performance for distribution- mismatched (yet high- quality) clients. Consequently, no client is penalized for joining the federation, reinforcing the incentives for collaborative fairness under the imbalanced covariate shift.

# 3.2 Step 2: High-confidence Sample Selection

Our preliminary analysis (Figures 2 in Section 1) suggests that imbalanced covariate shift primarily arises from misclassified samples on each client, whereas correctly classified samples positively contribute to global model learning. To address this, we select high- confidence samples (i.e., correctly classified samples) to update the global model  $\mathbf{w}_{g,k}^t$ , which is denoted as:

$$
\mathcal{I}_k^t = \Big\{(\mathbf{x},y)\in \mathcal{D}_k\Big|\mathrm{Pred}(\mathbf{w}_k^t,\mathbf{x}) = y\Big\} . \tag{4}
$$

# 3.3 Step 3: Local  $\longrightarrow$  Global Distillation

Unlike existing bidirectional knowledge distillation [8, 17] that updates two models simultaneously using the same dataset, we

propose an asynchronous knowledge distillation approach for this step. Additionally, we leverage only the selected high- confidence samples  $\mathcal{I}_k^t$ , enabling the local model  $\mathbf{w}_k^t$  to guide the learning of the global model  $\mathbf{w}_{g,k}^t$  by optimizing the following loss:

$$
\overleftarrow{\mathcal{L}_t} = \mathrm{CE}(\mathbf{w}_{g,k}^t,\mathcal{I}_k^t) + \beta \mathrm{KD}(\mathbf{w}_{g,k}^t;\mathbf{w}_{k}^t,\mathcal{I}_k^t), \tag{5}
$$

where  $\beta$  is the hyperparameter. The gradient update yields:

$$
\mathbf{w}_{g,k}^{t + 1} = \mathbf{w}_{g,k}^t -\eta \nabla \overleftarrow{\mathcal{L}_t}, \tag{6}
$$

The proposed asynchronous knowledge distillation offers three key benefits: (1) Robustness to Noisy Updates. It protects the global model from noisy or erroneous updates by discarding locally misclassified samples. (2) Fair Collaboration. It promotes fairness by allowing high- quality clients- those that classify more samples correctly- to have a stronger influence without explicitly revealing their accuracy or contributions. (3) Adaptability to Imbalanced Covariate Shift. It ensures that even if a client's data distribution differs significantly from the global average, it can still contribute reliable knowledge.

# 3.4 Convergence Analysis

3.4.1 Notions and Assumptions. In this section, we consider a binary classification problem following [16] with input space  $X\in \mathbb{R}^d$  and label space  $Y = \{0,1\}$ . We employ a linear classification setting: for each local sample  $\mathbf{x}\in X$ , the logits are  $z = \mathbf{x}^\top \mathbf{w}$ , and the predicted probability is  $\hat{y} (\mathbf{x}) = \sigma (z) = \frac{1}{1 + e^{- z}}$ . In our codistillation setup, the distillation temperature is set to  $\tau = 1$ , keeping the standard sigmoid form. We denote the cross- entropy loss by  $\mathcal{L}(\mathbf{w};\mathcal{D}) = \mathrm{CE}(\mathbf{w};\mathcal{D}) = \frac{1}{|\mathcal{D}|}\sum_{\mathbf{x}_i\in \mathcal{D}}\left[- y_i\log (\hat{y} (\mathbf{x}_i)) - (1 - y_i)\log (1 - \hat{y} (\mathbf{x}_i))\right]$ . Here,  $\mathbf{w}$  is the model parameter vector, and  $\mathcal{D}$  is the training dataset consisting of samples  $\mathbf{x}_i$  with labels  $y_i$ . We further introduce the KD loss, denoted by  $\mathrm{KD}(\mathbf{w},\mathbf{w}_0;\mathcal{D}) = \frac{1}{|\mathcal{D}|}\sum_{\mathbf{x}_i\in \mathcal{D}}\left[- \hat{y}_0(\mathbf{x}_i)\log (\hat{y} (\mathbf{x}_i)) - (1 - \hat{y}_0(\mathbf{x}_i))\log (1 - \hat{y} (\mathbf{x}_i))\right]$ . This can also be written in expectation form as  $\mathbb{E}_{\mathbf{x}_i\in \mathcal{D}}\left[- \hat{y}_0(\mathbf{x}_i)\log (\hat{y} (\mathbf{x}_i)) - (1 - \hat{y}_0(\mathbf{x}_i))\log (1 - \hat{y} (\mathbf{x}_i))\right]$ . Here,  $\hat{y}_0(\mathbf{x}_i)$  is the (fixed) teacher model's output,  $\hat{y}_0(\mathbf{x}_i) = \sigma (\mathbf{x}_i^\top \mathbf{w}_0)$ , and  $\mathbf{w}_0$  denotes the teacher's parameter vector. This KD objective is equivalent (up to a constant) to minimizing the KL divergence from the teacher distribution [10, 16].

We define the notion of a vanlexact solution following [10, 16], which quantifies the improvement made by local updates:

Definition 3.1  $(\gamma_{1}$  - inexact solution [10, 16]). For a function  $\vec{\mathcal{L}} (\mathbf{w},\mathbf{w}_0;\mathcal{D})$ $\mathcal{L}(\mathbf{w};\mathcal{D}) + \alpha \mathrm{KD}(\mathbf{w},\mathbf{w}_0;\mathcal{D})$  and let  $\gamma_1\in [0,1]$ . Suppose  $\mathbf{w}_0$  is an initial point for  $\min_{\mathbf{w}}\mathcal{L}(\mathbf{w};\mathbf{w}_0;\mathcal{D})$ . We say  $\mathbf{w}^*$  is a  $\gamma_{1}$  - inexact solution if  $\left\| \nabla \overleftarrow{\mathcal{L}} (\mathbf{w}^*,\mathbf{w}_0;\mathcal{D})\right\| \leq \gamma_1\left\| \nabla \overleftarrow{\mathcal{L}} (\mathbf{w}_0,\mathbf{w}_0;\mathcal{D})\right\|$ .

A smaller  $\gamma_{1}$  indicates a greater reduction in the gradient norm relative to the initial point, implying more significant local improvement. Conversely, a larger  $\gamma_{1}$  indicates a less complete local optimization. Similarly, for the function  $\overleftarrow{\mathcal{L}}$  with coefficient  $\beta$ . We define  $\overleftarrow{\mathcal{L}} (\mathbf{w},\mathbf{w}_0;\mathcal{D}) = \mathcal{L}(\mathbf{w};\mathcal{D}) + \beta \mathrm{KD}(\mathbf{w},\mathbf{w}_0;\mathcal{D})$ , and let  $\gamma_{2}\in [0,1]$ . we say  $\mathbf{w}^*$  is a  $\gamma_{2}$  - inexact solution with  $\left\| \nabla \overleftarrow{\mathcal{L}} (\mathbf{w}^*,\mathbf{w}_0;\mathcal{D})\right\| \leq \gamma_2\left\| \nabla \overleftarrow{\mathcal{L}} (\mathbf{w}_0,\mathbf{w}_0;\mathcal{D})\right\|$ .

ASSUMPTION 1 (L- SMOOTHNESS [10, 11]). There exists  $L > 0$  such that for all  $\mathbf{w},\mathbf{w}^{\prime}$ ,  $\| \nabla \mathcal{L}(\mathbf{w};\mathcal{D}) - \nabla \mathcal{L}(\mathbf{w}^{\prime};\mathcal{D})\| \leq L\| \mathbf{w} - \mathbf{w}^{\prime}\|$ .

ASSUMPTION 2 ( $\mu$ - STRONG CONVEXITY [10, 11]). There exists  $\mu > 0$  such that for all  $\mathbf{w},\mathbf{w}^{\prime}$ ,  $\mathcal{L}(\mathbf{w};\mathcal{D})\geq \mathcal{L}(\mathbf{w}^{\prime};\mathcal{D}) + \nabla \mathcal{L}(\mathbf{w}^{\prime};\mathcal{D})^{\top}(\mathbf{w} - \mathbf{w}^{\prime}) + \frac{\mu}{2}\| \mathbf{w} - \mathbf{w}^{\prime}\|^{2}$ ,  $\overleftarrow{\mathcal{L}} (\mathbf{w},\mathbf{w}_0;\mathcal{D})\geq \overleftarrow{\mathcal{L}} (\mathbf{w}^{\prime},\mathbf{w}_0;\mathcal{D}) + \nabla \overleftarrow{\mathcal{L}} (\mathbf{w}^{\prime},\mathbf{w}_0;\mathcal{D})^{\top}(\mathbf{w} - \mathbf{w}^{\prime}) + \frac{\mu}{2}\| \mathbf{w} - \mathbf{w}^{\prime}\|^{2}$ , and  $\overleftarrow{\mathcal{L}} (\mathbf{w},\mathbf{w}_0;\mathcal{D})\geq \overleftarrow{\mathcal{L}} (\mathbf{w}^{\prime},\mathbf{w}_0;\mathcal{D}) + \nabla \overleftarrow{\mathcal{L}} (\mathbf{w}^{\prime},\mathbf{w}_0;\boldsymbol {\mathcal{D}})^{\top}(\mathbf{w} - \mathbf{w}^{\prime}) + \frac{\mu}{2}\| \mathbf{w} - \mathbf{w}^{\prime}\|^{2}$ .

ASSUMPTION 3 (BOUNDED GRADIENT DISSIMILARITY [10, 16]). Let  $\mathcal{D}_g\coloneqq \bigcup_k\mathcal{D}_k$  be the global dataset consisting of all local datasets  $\mathcal{D}_k$ . For some  $\epsilon >0$ , define  $S_c^\epsilon = \{\mathbf{w}|\| \nabla \mathcal{L}(\mathbf{w};\mathcal{D}_g)\| ^2 >\epsilon \}$ . There exists  $B_{\epsilon}$  such that for all  $\mathbf{w}\in S_c^\epsilon$ ,  $B(\mathbf{w}) = \sqrt{\frac{B_k\left\||\nabla\mathcal{L}(\mathbf{w};\mathcal{D}_k)|^2}{\|\nabla\mathcal{L}(\mathbf{w};\mathcal{D}_g)\|^2}}\leq B_{\epsilon}$ .

Here,  $B(\mathbf{w})$  measures data heterogeneity across devices. If data are IID and  $n_k\to \infty$ , then  $B(\mathbf{w})\to 1$ . Generally,  $B_{\epsilon}\geq 1$ , and larger values capture more dissimilar data distributions.

ASSUMPTION 4 (BOUNDED GRADIENT DISSIMILARITY ON SUBSET). Let  $\mathcal{I}\subseteq \mathcal{D}$  be a subset. There exists  $\theta \geq 0$  such that for all  $\mathbf{w}$ ,  $\| \nabla \mathcal{L}(\mathbf{w};\mathcal{I}) - \nabla \mathcal{L}(\mathbf{w};\mathcal{D})\| \leq \theta \| \nabla \mathcal{L}(\mathbf{w};\mathcal{D})\| .$

This condition ensures that the gradient on a chosen subset does not deviate excessively from the gradient on the entire local dataset, thus quantifying the heterogeneity between these two distributions.

# 3.4.2 Main Results.

THEOREM 3.2 (FedAKD CONVERGENCE). Under Assumptions 1- 4, assume that  $\mathbf{w}^t$  is not a stationary solution and the loss function  $\mathcal{L}$  is  $B$ - dissimilar, i.e.,  $B(\mathbf{w}^t)\leq B$ . If  $\alpha$ ,  $\beta$  and  $\gamma \coloneqq \max \{y_1,y_2\}$  are chosen such that  $r = (\frac{4}{\beta||\Omega_2||} +\frac{4}{\alpha||\Omega_1||})B - \frac{4}{\gamma_2^2} ((L(1 + \gamma) + \mu)\frac{\gamma(1 + \theta)}{\mu} +(1 + \theta) + \frac{(1 + \gamma)\beta||\Omega_2||}{4\mu})^2 - (\frac{4}{\beta||\Omega_2||} +\frac{4}{\alpha||\Omega_1||})r_1 + r_2)B > 0$ , where  $r_1 = ((L(1 + \gamma) + \mu)\frac{\gamma(1 + \theta)}{\mu} +(1 + \theta) + \frac{(1 + \gamma)\beta||\Omega_2||}{4\mu})r_1 + r_2)B > 0$ , where  $(L(1 + \gamma) + \mu)\frac{\gamma(1 + \theta)}{\mu})r_2 = \frac{L(1 + \gamma)}{\mu} +\gamma ,\Omega_1 = \mathbb{E}_k\left[\mathbb{E}_{\mathbf{x}_k,i\in \mathcal{D}_k}[\mathbf{x}_k,i,\mathbf{x}_k,i]\right]$  and  $\Omega_2 = \mathbb{E}_k\left[\mathbb{E}_{\mathbf{x}_k,i\in \mathcal{I}_k^t}[\mathbf{x}_k,i,\mathbf{x}_k,i]\right]$ , then FedAKD satisfies

$$
\mathcal{L}(\mathbf{w}_g^{t + 1};\mathcal{D}_g) - \mathcal{L}(\mathbf{w}^*;\mathcal{D}_g)\leq (1 - 2\mu \rho)[\mathcal{L}(\mathbf{w}_g^t;\mathcal{D}_g) - \mathcal{L}(\mathbf{w}^*;\mathcal{D}_g)].
$$

Theorem 3.2 shows that the global model in FedAKD converges effectively. The detailed proof is provided in Appendix C.

# 4 SIMULATION EXPERIMENTS

# 4.1 Non-IID Settings

Since most real- world federated applications involve non- IID data distributions, particularly in the imbalanced covariate shift setting, we consider the following three non- IID client partitions in our simulation experiments:

- Imbalanced Dataset Sizes (POW) [13, 23]: Each client's dataset size  $|\mathcal{D}_k|$  follows a power-law distribution, leading to significant disparities in data quantity across clients. However, the feature distributions remain similar across clients.- Balanced Covariate Shift (BCS): Clients exhibit covariate shifts in their data feature distributions while maintaining a similar number of samples.

# Table 1: Performance evaluation of simulation experiments on two datasets (mean  $\pm$  std across three runs).

(a) Max Client Accuracy  $(\%)$  

<table><tr><td rowspan="2">Method</td><td colspan="5">FashionMNIST</td><td colspan="5">CIFAR10</td></tr><tr><td>POW</td><td>BCS</td><td>ICS(2.0)</td><td>ICS(5.0)</td><td>ICS(10.0)</td><td>POW</td><td>BCS</td><td>ICS(2.0)</td><td>ICS(5.0)</td><td>ICS(10.0)</td></tr><tr><td>Standalone</td><td>88.09±0.06</td><td>97.06±0.16</td><td>96.93±0.16</td><td>96.80±0.05</td><td>96.94±0.02</td><td>54.86±1.93</td><td>54.53±0.66</td><td>58.82±1.19</td><td>59.25±1.29</td><td>56.20±1.71</td></tr><tr><td>FedAvg</td><td>94.47±0.46</td><td>97.50±0.14</td><td>94.57±0.32</td><td>96.23±0.16</td><td>94.32±0.13</td><td>67.08±0.17</td><td>65.80±0.65</td><td>66.08±0.15</td><td>64.68±0.60</td><td>66.67±0.95</td></tr><tr><td>CFFL</td><td>94.66±0.42</td><td>99.61±0.16</td><td>97.41±2.44</td><td>98.18±1.59</td><td>98.6±1.19</td><td>67.76±1.87</td><td>70.71±3.34</td><td>71.37±0.07</td><td>64.34±4.98</td><td>69.27±2.03</td></tr><tr><td>CGSV</td><td>96.33±0.31</td><td>98.64±0.11</td><td>98.29±0.52</td><td>98.51±0.25</td><td>97.97±0.09</td><td>76.16±2.13</td><td>76.31±0.26</td><td>76.04±2.28</td><td>72.29±2.05</td><td>78.40±1.37</td></tr><tr><td>FedAVE</td><td>92.42±0.93</td><td>98.72±0.75</td><td>96.44±0.76</td><td>95.46±1.83</td><td>96.78±0.14</td><td>62.39±1.56</td><td>58.27±0.09</td><td>60.53±3.10</td><td>60.79±0.86</td><td>60.96±1.26</td></tr><tr><td>FedSAC</td><td>96.42±0.76</td><td>96.73±0.43</td><td>93.33±0.64</td><td>97.31±1.34</td><td>96.34±0.33</td><td>63.27±0.22</td><td>63.04±2.37</td><td>63.42±0.06</td><td>64.13±0.84</td><td>63.59±0.08</td></tr><tr><td>pFedCK</td><td>94.15±0.13</td><td>97.52±0.37</td><td>97.11±0.15</td><td>98.62±0.54</td><td>98.25±0.87</td><td>80.13±0.87</td><td>79.54±0.31</td><td>80.88±1.24</td><td>79.99±1.46</td><td>80.71±0.98</td></tr><tr><td>FedDC</td><td>91.71±0.08</td><td>96.22±0.40</td><td>94.13±0.42</td><td>94.45±0.39</td><td>94.37±0.13</td><td>67.10±1.06</td><td>64.67±0.25</td><td>66.11±0.64</td><td>65.63±1.34</td><td>65.81±0.65</td></tr><tr><td>FedAS</td><td>88.20±0.33</td><td>98.89±0.21</td><td>96.08±0.23</td><td>96.76±0.20</td><td>96.37±0.33</td><td>75.56±1.23</td><td>73.13±0.66</td><td>74.59±0.79</td><td>74.25±1.27</td><td>75.58±1.61</td></tr><tr><td>FedMPR</td><td>94.80±0.23</td><td>97.72±0.21</td><td>95.20±0.67</td><td>95.93±0.33</td><td>95.88±0.43</td><td>69.06±0.36</td><td>66.40±0.57</td><td>66.03±0.54</td><td>67.84±1.43</td><td>69.03±0.80</td></tr><tr><td>FedAKD</td><td>97.45±0.09</td><td>99.67±0.14</td><td>99.56±0.04</td><td>99.66±0.04</td><td>99.56±0.11</td><td>83.80±1.12</td><td>81.27±0.19</td><td>81.93±1.03</td><td>82.73±0.38</td><td>81.30±1.43</td></tr></table>

(b) Average Client Accuracy  $(\%)$  

<table><tr><td rowspan="2">Method</td><td colspan="4">FashionMNIST</td><td colspan="4">CIFAR10</td><td></td><td></td></tr><tr><td>POW</td><td>BCS</td><td>ICS(2.0)</td><td>ICS(5.0)</td><td>ICS(10.0)</td><td>POW</td><td>BCS</td><td>ICS(2.0)</td><td>ICS(5.0)</td><td>ICS(10.0)</td></tr><tr><td>Standalone</td><td>$86.26±0.06</td><td>92.04±0.03</td><td>89.15±0.19</td><td>89.50±0.05</td><td>90.01±0.13</td><td>45.44±0.13</td><td>50.03±0.45</td><td>47.25±0.38</td><td>47.20±0.96</td><td>46.33±0.75</td></tr><tr><td>FedAvg</td><td>91.16±0.24</td><td>92.98±0.06</td><td>91.60±0.13</td><td>91.98±0.08</td><td>91.82±0.03</td><td>63.95±1.00</td><td>62.37±0.66</td><td>63.10±0.16</td><td>62.03±0.55</td><td>62.14±0.69</td></tr><tr><td>CFFL</td><td>87.79±0.91</td><td>93.92±0.04</td><td>89.43±0.56</td><td>88.39±0.83</td><td>87.35±0.36</td><td>53.91±3.31</td><td>62.88±4.27</td><td>46.14±2.92</td><td>48.27±3.29</td><td>54.18±2.71</td></tr><tr><td>CGSV</td><td>91.23±0.15</td><td>92.21±0.35</td><td>90.52±1.35</td><td>89.32±0.36</td><td>91.16±0.15</td><td>66.68±1.50</td><td>67.00±2.93</td><td>64.32±2.32</td><td>63.65±2.11</td><td>65.56±1.35</td></tr><tr><td>FedAVE</td><td>89.44±0.45</td><td>88.36±0.19</td><td>89.83±0.47</td><td>88.99±0.64</td><td>89.66±0.04</td><td>55.85±1.44</td><td>52.19±1.19</td><td>55.32±2.09</td><td>51.85±2.41</td><td>53.42±1.84</td></tr><tr><td>FedSAC</td><td>89.54±0.63</td><td>88.92±0.83</td><td>89.75±0.92</td><td>88.65±0.54</td><td>89.76±0.03</td><td>54.63±0.32</td><td>60.53±0.81</td><td>60.52±0.29</td><td>55.52±0.72</td><td>60.84±0.64</td></tr><tr><td>pFedCK</td><td>90.83±0.18</td><td>93.63±0.28</td><td>91.82±0.28</td><td>91.04±0.77</td><td>91.98±0.25</td><td>67.51±0.52</td><td>64.18±0.84</td><td>65.19±0.27</td><td>65.65±0.71</td><td>65.88±0.39</td></tr><tr><td>FedDC</td><td>87.83±0.46</td><td>91.08±1.46</td><td>90.06±0.56</td><td>90.20±0.24</td><td>90.01±0.42</td><td>55.34±0.65</td><td>53.13±1.31</td><td>52.98±0.05</td><td>52.30±1.73</td><td>55.83±0.73</td></tr><tr><td>FedAS</td><td>84.36±0.22</td><td>91.47±0.23</td><td>88.27±0.40</td><td>87.84±0.27</td><td>88.22±0.24</td><td>64.64±0.75</td><td>64.68±0.30</td><td>63.75±0.60</td><td>64.39±0.32</td><td>63.81±0.54</td></tr><tr><td>FedMPR</td><td>91.18±0.04</td><td>92.90±0.87</td><td>91.74±0.05</td><td>91.73±0.13</td><td>91.71±0.19</td><td>64.95±0.56</td><td>62.79±0.48</td><td>63.77±0.06</td><td>63.09±0.58</td><td>64.67±0.07</td></tr><tr><td>FedAKD</td><td>93.50±0.19</td><td>95.68±0.15</td><td>92.62±0.20</td><td>92.47±0.14</td><td>92.92±0.12</td><td>70.96±0.58</td><td>67.59±0.49</td><td>68.96±0.31</td><td>68.50±0.42</td><td>68.55±1.35</td></tr></table>

(c) Collaborative Fairness (CF) Coefficient  

<table><tr><td rowspan="2">Method</td><td colspan="4">FashionMNIST</td><td colspan="4">CIFAR10</td><td></td><td></td></tr><tr><td>POW</td><td>BCS</td><td>ICS(2.0)</td><td>ICS(5.0)</td><td>ICS(10.0)</td><td>POW</td><td>BCS</td><td>ICS(5.0)</td><td>ICS(5.0)</td><td>ICS(10.0)</td></tr><tr><td>FedAvg</td><td>45.45±3.60</td><td>62.43±1.11</td><td>71.76±2.14</td><td>87.60±4.47</td><td>74.43±2.37</td><td>3.622±25.88</td><td>26.00±9.58</td><td>67.60±6.97</td><td>33.97±27.58</td><td>74.79±3.52</td></tr><tr><td>CFFL</td><td>33.85±15.44</td><td>79.80±10.74</td><td>60.80±20.34</td><td>74.22±6.33</td><td>67.01±8.69</td><td>45.90±31.33</td><td>16.94±32.09</td><td>55.03±15.49</td><td>13.17±7.51</td><td>26.82±11.03</td></tr><tr><td>CGSV</td><td>38.11±14.34</td><td>69.03±6.63</td><td>52.58±7.38</td><td>57.32±11.69</td><td>68.46±16.67</td><td>36.25±26.04</td><td>45.87±19.76</td><td>18.50±21.40</td><td>4.15±18.53</td><td>5.54±22.85</td></tr><tr><td>FedAVE</td><td>21.43±23.31</td><td>65.10±1.47</td><td>78.15±8.32</td><td>80.02±7.00</td><td>65.79±4.98</td><td>16.60±18.27</td><td>49.08±15.03</td><td>17.07±22.03</td><td>24.56±34.43</td><td>25.51±27.50</td></tr><tr><td>FedSAC</td><td>28.10±7.93</td><td>79.54±5.13</td><td>48.78±14.97</td><td>83.30±5.13</td><td>71.26±4.76</td><td>62.04±12.03</td><td>43.61±10.38</td><td>56.89±7.30</td><td>23.90±12.70</td><td>20.71±19.95</td></tr><tr><td>pFedCK</td><td>23.15±5.26</td><td>41.09±13.56</td><td>34.15±9.36</td><td>36.65±8.61</td><td>15.27±15.31</td><td>51.14±9.15</td><td>70.78±11.15</td><td>24.64±21.54</td><td>8.76±19.93</td><td>36.83±10.66</td></tr><tr><td>FedDC</td><td>-40.85±18.38</td><td>-11.12±22.49</td><td>2.39±15.37</td><td>18.37±9.41</td><td>-15.32±38.39</td><td>-4.22±32.08</td><td>-9.54±31.25</td><td>-21.57±5.97</td><td>-9.38±27.68</td><td>-0.52±42.33</td></tr><tr><td>FedAS</td><td>45.09±2.61</td><td>75.81±4.81</td><td>27.74±4.12</td><td>79.61±3.11</td><td>70.23±3.12</td><td>61.34±6.55</td><td>74.28±9.43</td><td>70.01±6.07</td><td>60.54±4.05</td><td>76.54±4.10</td></tr><tr><td>FedMPR</td><td>31.33±14.55</td><td>83.20±2.59</td><td>70.43±2.21</td><td>76.66±5.21</td><td>61.59±3.03</td><td>50.93±14.09</td><td>40.63±4.25</td><td>29.78±18.15</td><td>44.96±6.39</td><td>53.06±9.69</td></tr><tr><td>FedAKD</td><td>70.61±5.82</td><td>85.68±0.81</td><td>78.25±2.02</td><td>89.51±2.40</td><td>79.72±1.11</td><td>88.02±2.09</td><td>82.15±2.23</td><td>81.25±1.55</td><td>87.53±2.42</td><td>84.17±2.77</td></tr></table>

- Imbalanced Covariate Shift (ICS): ICS combines the characteristics of POW and BCS, where client dataset sizes follow a power-law distribution (POW), and different institutions experience significant variations in feature distributions.

Additionally, we evaluate the proposed FedAKD framework under two ideal yet commonly used label shift settings: Imbalanced Class Distributions (CLA) [13, 23] and Imbalanced Sizes & Class Distributions (DIR) [1, 28]. The experimental setting and results for these two non-IID partitions are presented in Appendix D.

# 4.2 Federated Data Simulation

In the simulation experiments, we use two image classification datasets: Fashion MNIST [22] and CIFAR10 [7]. Fashion MNIST contains 70,000 grayscale images  $(28\times 28)$  evenly split into 10 classes (e.g., T- shirt/top, trousers). CIFAR10 consists of 60,000 color images  $(32\times 32)$  across 10 classes (e.g., airplane, bird). We partition the datasets into training, validation, and testing in a ratio of 7:1:2. In addition, we set the number of clients as  $K = 10$ . To simulate the POW partition, we follow a power law with an exponent of 1 to divide the global data into 10 clients. For the  $k$ - th client, its data size is  $|\mathcal{D}_k| = \frac{1}{kZ} |\mathcal{D}_g|$ , where  $Z = \sum_{k = 1}^{10}\frac{1}{k}$ .

Simulating the covariate shift setting is nontrivial, and as far as we know, no existing work provides an automated way to generate such federated datasets. To fill this research gap, we design a novel algorithm based on Theorem 2.2 for covariate shift data generation, as shown in Appendix E Algorithm 2. To stimulate the BCS partition, we set  $C = 5$  and add a perturbation  $\delta$  (satisfying  $\delta^{\top} \Sigma^{- 1} \delta = 5$ ) to the global mean. In such a way, each client receives an equal number of samples (i.e., balanced), thus focusing on the covariate shift while keeping dataset sizes uniform. To stimulate the ICS partition, we run Algorithm 2 again to produce three variants with  $C = 2$ ,  $C = 5$ , and  $C = 10$ , representing increasing levels of covariate shift. Meanwhile, the total number of samples is partitioned among the 10 clients according to the power law distribution with the exponent as 1, thus coupling imbalanced dataset sizes with covariate shifts.

# 4.3 Baselines

We compare our method against two categories of baselines: Collaborative Fairness algorithms designed for non- IID data, and Covariate Shift algorithms focusing on feature- level discrepancies. Specifically, we include CGSV [23], CFFL [13], FedAVE [19], and FedSAC [20], which explicitly address fairness by measuring client contributions or customizing reward allocations. Meanwhile, FedAS [25], FedDC [1], pFedCK [30], and FedMPR [2] focus on mitigating feature- level drift across clients. We also include two traditional baselines: Standalone (each client trains independently without aggregation) and the classic FedAvg [15] for federated averaging. The details of the baselines can be found in Appendix F.

# 4.4 Implementation

We implement a network consisting of two convolutional layers (each followed by batch normalization and ReLU activation), interleaved with max- pooling, and ending with a fully connected output layer for the simulation evaluation. We implement all baselines and our model in PyTorch and train them on an NVIDIA RTX A6000 GPU. All details of parameter setting can be found in Appendix G.

# 4.5 Evaluation Metrics

Due to the imbalanced covariate shift setting, each client data has a unique distribution. Thus, we conduct local evaluations and then report the average values of all the clients for three runs. Let  $\mathbf{Acc}_p$  denote the prediction accuracy of clients after federation. Following [20], we use three metrics:

- Average Client Accuracy, i.e.,  $\frac{\sum_k \mathbf{Acc}_p[k]}{K}$ ;- Maximum Client Accuracy, i.e.,  $\max (\mathbf{Acc}_p)$ ;- Collaborative Fairness (CF) Coefficient, which reflects how uniformly performance is distributed across clients. We use the CF defined in [13], i.e.,  $CF = 100 \times \rho (\mathbf{Acc}_s, \mathbf{Acc}_p) \in [-100, 100]$ , where  $\rho (\cdot , \cdot)$  is Pearson's correlation coefficient, and  $\mathbf{Acc}_s$  represents the standalone accuracy of clients.

The greater these three metric values, the better the performance.

# 4.6 Results of Simulation Experiments

Table 1 presents the results on FashionMNIST and CIFAR10 under three types of non- IID partitions. As shown in Table 1(a), our method consistently achieves superior or highly competitive Max Client Accuracy across all partitions for both datasets. Table 1(b) further highlights our approach's advantage in Avg Client Accuracy, particularly on the more challenging CIFAR10 dataset. The most critical metric, Collaborative Fairness (CF), is reported in Table 1(c), where FedAKD demonstrates significantly higher fairness under varying degrees of non- IID settings. These results collectively validate the effectiveness of FedAKD in improving both accuracy and fairness.

Table 2:Experimental results on the EHR dataset.  

<table><tr><td>Method</td><td>CF</td><td>Max Acc</td><td>Avg. Acc</td></tr><tr><td>Standalone</td><td>-</td><td>76.27 ± 0.13</td><td>70.41 ± 0.04</td></tr><tr><td>FedAvg</td><td>-12.63 ± 5.22</td><td>74.75 ± 1.24</td><td>70.13 ± 0.21</td></tr><tr><td>CFFL</td><td>52.63 ± 12.43</td><td>73.26 ± 2.49</td><td>69.57 ± 0.92</td></tr><tr><td>CGSV</td><td>46.39 ± 4.24</td><td>72.56 ± 1.71</td><td>69.31 ± 0.42</td></tr><tr><td>FedAVE</td><td>38.24 ± 17.24</td><td>75.60 ± 1.59</td><td>69.24 ± 0.81</td></tr><tr><td>FedSAC</td><td>70.54 ± 1.24</td><td>74.21 ± 0.32</td><td>68.33 ± 0.56</td></tr><tr><td>pFedCK</td><td>35.28 ± 13.74</td><td>76.87 ± 0.18</td><td>70.01 ± 0.10</td></tr><tr><td>FedDS</td><td>17.10 ± 3.21</td><td>75.09 ± 0.05</td><td>69.61 ± 0.29</td></tr><tr><td>FedAC</td><td>8.50 ± 3.28</td><td>74.89 ± 0.29</td><td>69.05 ± 0.25</td></tr><tr><td>FedMPR</td><td>-5.59 ± 2.31</td><td>76.39 ± 0.25</td><td>70.10 ± 0.13</td></tr><tr><td>FedAKD</td><td>78.42 ± 1.09</td><td>78.98 ± 1.01</td><td>71.23 ± 0.27</td></tr></table>

Table 3: Performance of all methods when constrained to the same wall-clock time as FedAKD (40 rounds).  

<table><tr><td>Method</td><td>Round</td><td>CF</td><td>Max Acc</td><td>Avg. Acc</td></tr><tr><td>FedAvg</td><td>80</td><td>-12.63±5.22</td><td>74.75±1.24</td><td>70.13±0.21</td></tr><tr><td>CFFL</td><td>43</td><td>48.63±11.43</td><td>75.25±1.42</td><td>67.31±0.95</td></tr><tr><td>CGSV</td><td>31</td><td>38.11±9.35</td><td>73.62±0.55</td><td>68.84±0.84</td></tr><tr><td>FedAVE</td><td>69</td><td>53.62±3.31</td><td>70.35±1.24</td><td>66.52±0.99</td></tr><tr><td>FedSAC</td><td>65</td><td>60.22±3.21</td><td>74.02±0.44</td><td>68.89±0.21</td></tr><tr><td>pFedCK</td><td>58</td><td>33.51±8.51</td><td>75.01±0.43</td><td>69.11±0.30</td></tr><tr><td>FedAS</td><td>51</td><td>30.10±5.32</td><td>73.54±1.03</td><td>68.56±0.88</td></tr><tr><td>FedDC</td><td>59</td><td>15.89±4.09</td><td>72.88±1.39</td><td>68.63±0.11</td></tr><tr><td>FedMPR</td><td>47</td><td>8.22±4.24</td><td>74.39±0.21</td><td>69.28±0.26</td></tr><tr><td>FedAKD</td><td>40</td><td>71.89±1.82</td><td>77.10±0.58</td><td>71.02±0.53</td></tr></table>

Table 4: Ablation study on three variants of FedAKD versus the full method. CF denotes a fairness metric among clients (higher is better), whereas Max Acc and Avg.Acc refer to the maximum and average accuracies (in  $\mathbb{S}$ ), respectively, across global rounds.  

<table><tr><td>Method</td><td>CF</td><td>Max Acc</td><td>Avg. Acc</td></tr><tr><td>FedAKD (All-Data)</td><td>66.02±2.93</td><td>75.20±0.82</td><td>70.66±0.42</td></tr><tr><td>FedAKD (Single-Dist)</td><td>58.49±4.32</td><td>73.88±1.61</td><td>64.02±2.82</td></tr><tr><td>FedAKD (Correct-Agg.)</td><td>77.31±1.98</td><td>78.22±0.91</td><td>71.17±0.66</td></tr><tr><td>FedAKD (Full)</td><td>78.42±1.09</td><td>78.98±1.01</td><td>71.23±0.27</td></tr></table>

# 5 REAL-WORLD EXPERIMENTS

# 5.1 Experimental Settings

The EHR Dataset is extracted from the TriNetX database<sup>3</sup>, which contains patients' claims data from all 50 states in the USA. This dataset is curated for the early prediction of pancreatic cancer and includes 259,480 de- identified patient records (161,345 negative vs. 98,135 positive). It consists of both static features (sex, zip code) and time- series events (medications, lab tests, vital signs, etc.). Further details on the EHR dataset and experimental settings are provided in Appendix H. In this experiment, we adopt a two- layer bidirectional GRU with attention mechanisms [3, 14, 18, 21, 26, 27, 29] to predict whether a patient eventually develops pancreatic cancer. We use the same baselines and evaluation metrics as the simulation experiments.

![](images/f4d829efd96c9e5e7cb5b174617913fc27742d90113b7cd1d11ab30e5c9a212b.jpg)  
Figure 3: Efficiency comparison of FedAKD and baselines.

# 5.2 Performance Analysis

Table 2 presents the results on the real- world EHR dataset, which naturally follows an ICS (Imbalanced Covariate Shift) pattern due to varying sample sizes and feature distributions across different medical institutions. As shown in the table, FedAKD significantly outperforms the baselines, particularly in the CF metric, demonstrating a more equitable distribution of benefits among clients. While FedMPR achieve competitive Max Acc, their fairness metrics remain comparatively limited.

# 5.3 Ablation Studies

To investigate how each component of the proposed FedAKD (Algorithm 1) contributes to its final performance, we conduct three ablation experiments. In each variant, we selectively remove or modify part of the procedure to gauge its impact: (1) Local  $\rightarrow$  Global Distillation Using All Data  $\mathcal{D}_k$ . The biggest key finding of this work is to use the correctly predicted subset  $\bar{I}_{g,k}^{t}$  to guide the update of  $\mathbf{w}_{g,k}^{t}$ . This baseline uses all local samples  $\mathcal{D}_k$  for local  $\rightarrow$  global distillation to test whether restricting to accurately labeled data is necessary for effective knowledge distillation. (2) Only Local  $\rightarrow$  Global Distillation. After receiving the aggregated global model, each client simply overwrites its local model with  $\mathbf{w}_{g}^{t}$ , instead of performing an additional global  $\rightarrow$  local distillation. This variant helps isolate the effect of double- direction distillation. (3) Correct Sample Count for Aggregation. In the standard procedure, the server weights each client's update by the total local dataset size. In this ablation, we replace that term with the number of correctly predicted local samples  $(|I_g^t |)$ , thus investigating whether "correct sample counts" lead to better aggregation fairness or performance.

The results of these ablation studies are summarized in Table 4. Our findings indicate that the proposed asynchronous knowledge distillation strategy has the most significant impact on both performance and fairness. Additionally, conducting knowledge distillation on the entire dataset negatively affects model performance. However, using the size of either the full dataset or only correctly classified data does not lead to notable performance differences. These results validate the rationale behind our model design.

# 5.4 Computational Cost Analysis

In our model design, the client training contains three steps, while baselines also use other techniques to calculate rewards. To validate the efficiency of our model, we show the average per- round running times (in seconds) as depicted in Figure 3a. Although FedAKD shows a slightly higher time consumption per round, primarily due to asynchronous knowledge distillation, its computational overhead is still comparable to other baselines (e.g., CFFL and FedAVE, which involve additional validation or sparsification steps). To further evaluate compute efficiency, we fixed FedAKD to 40 rounds, recorded its total wall- clock time, and then allowed every baseline to train for the same duration. Table 3 shows that—even under this strict budget—FedAKD achieves the largest fairness improvement and the highest predictive performance.

# 5.5 Convergence Analysis

We next analyze the convergence behavior of these methods by evaluating the global parameter  $\mathbf{w}_g^t$  on the global EHR dataset, i.e., computing  $\mathcal{L}(\mathbf{w}_g^t; \mathcal{D}_g)$  at each round. Figure 3b shows two representative convergence curves, comparing FedAvg and FedAKD. We observe that FedAKD converges slightly more slowly in the early stages; however, it remains stable and ultimately achieves a low global loss. This result empirically verifies Theorem 3.2, demonstrating that the global model in FedAKD converges effectively to a desirable minimum on  $\mathcal{D}_g$ .

# 6 RELATED WORK

This work mainly focuses on collaborative fairness (CF) in federated learning, which regards the global model as the core reward and seeks to ensure that the final performance of each client reflects its actual contribution. For instance, CGSV [23] computes a cosine gradient Shapley value to measure how closely each client's local gradient aligns with the global gradient, and allocates model updates based on this similarity. CFFL [13] relies on a public validation set to evaluate each client's data diversity and local- model performance, then allocates rewards accordingly. FedAVE [19] computes reputation by examining each client's local model performance and data distribution, offering better adaptability to various distributional scenarios. FedSAC [20] avoids the need for a global validation set by distributing varying submodels to high- contribution clients. However, it depends on pre- known standalone training results, which may be unrealistic in real- world deployments. Moreover, submodel- based pruning alone cannot fully address feature- distribution mismatch, as high- quality but distribution- mismatched data may still be underrepresented.

# 7 CONCLUSION

This paper investigates a practical yet challenging form of heterogeneity that impacts collaborative fairness: imbalanced covariate shift. To address this issue, we propose a novel approach, FedAKD (Federated Asynchronous Knowledge Distillation), which mitigates the effects of imbalanced covariate shift by excluding incorrectly predicted samples from the global model update—an insight derived from our preliminary findings. Experimental results on three datasets compared against ten baselines demonstrate the effectiveness and fairness of FedAKD across various heterogeneity settings in federated learning.

# REFERENCES

[1] Liang Gao, Hongchao Fu, Lili Li, Yanyan Chen, Min Xu, and Cheng- Zhong Xu. 2022. FedDC: Federated Learning with Non- liquid Data via Local Drift Decoupling and Correction. In Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition. 10112–10121.

[2] Ozgu Goksu and Nicolas Pugault. 2024. Robust Federated Learning in the Face of Covariate Shift: A Magnitude Pruning with Hybrid Regularization Framework for Enhanced Model Aggregation. arXiv preprint arXiv:2412.15010 [3] Wei Guo, Wei Ge, Longbo Cui, Hua Li, and Li Kong. 2019. An interpretable disease onset predictive model using crossover attention mechanism from electronic health records. IEEE Access 7 (2019), 134236- 134244. [4] Geoffrey Hinton. 2015. Distilling the Knowledge in a Neural Network. arXiv preprint arXiv:1503.02531 (2015) [5] Sai Praneeth Karimireddy, Satylen Kale, Mehryar Mohri, Sanjiv Reddi, Sebastian U. Stich, and Ananda Theertha Suresh. 2020. Scaffold: Stochastic Controlled Averaging for Federated Learning. In Proceedings of the 37th International Conference on Machine Learning (ICML). PMLR, 5132- 5143. [6] Jakub Konecni, 2016. Federated Learning Strategies for Improving Communication Efficiency. arXiv preprint arXiv:1610.05492 (2016). arXiv:1610.05492 [cs.LG] [7] Alex Krizhevsky and Geoffrey Hinton. 2009. Learning Multiple Layers of Features from Tiny Images. Technical Report. University of Toronto. Technical Report. [8] Wonbin Kweon, SeongKu Kang, and Hwanjo Yu. 2021. Bidirectional distillation for top- K recommender system. In Proceedings of the Web Conference 2021. 3861- 3871. [9] Tian Li, Anit Kumar Sahu, Ameet Talwalkar, and Virginia Smith. 2020. Federated learning: Challenges, methods, and future directions. IEEE Signal Processing Magazine 37, 3 (2020), 50- 60. [10] Tian Li, Anit K. Sahu, Mansul Zabeem, Maziar Sanjabi, Ameet Talwalkar, and Virginia Smith. 2020. Federated optimization in heterogeneous networks. In Proceedings of Machine Learning and Systems, Vol. 2. 429- 450. [11] Xiang Li, Kaixuan Huang, Wenhao Yang, Shusen Wang, and Zhihua Zhang. 2019. On the Convergence of FedAvg on Non- IID Data. arXiv:1907.02189 [cs.LG] arXiv preprint arXiv:1907.02189. [12] Tsung- Yi Lin, Priyal Goyal, Ross Girshick, Kaiming He, Piotr Dollar, and Serge Belongie. 2017. Focal Loss for Dense Object Detection. arXiv preprint arXiv:1708.02002 (2017). [13] Lingjuan Lyu, Xinyang Xu, Qianx Wang, Han Yu, et al. 2020. Collaborative Fairness in Federated Learning. In Federated Learning: Privacy and Incentive. 189- 204. [14] Fenglong Ma, Radha Chitta, Jing Zhou, Quanzeng You, Tong Sun, and Jing Gao. 2017. Dipole: Diagnosis prediction in healthcare via attention- based bidirectional recurrent neural networks. In Proceedings of the 23rd ACM SIGKDD International Conference on Knowledge Discovery and Machine Learning. 1- 10. [15] Brendan McMahan, Eider Moore, Daniel Ramage, Seth Hampson, and Blaise Aguiera y Arcas. 2017. Communication- Efficient Learning of Deep Networks from Decentralized Data. In Proceedings of the 20th International Conference on Artificial Intelligence and Statistics (AISTATS) (Proceedings of Machine Learning Research). PMLR, 1273- 1282. [16] Xuanming Ni, Xinyuan Shen, and Huimin Zhao. 2022. Federated optimization via knowledge codistillation. Expert Systems with Applications 191 (2022), 116310. https://doi.org/10.1016/j.esw.2021.116310 [17] Ertong Shang, Hui Liu, Zhuo Yang, Junzhao Du, and Yiming Ge. 2023. FedBiKD: Federated Bidirectional Knowledge Distillation for Distracted Driving Detection. IEEE Internet of Things Journal (2023). [18] Qingxiong Tan, Min Ye, Bin Yang, S. Liu, A. J. Ma, T. C. F. Yip, Y. Zhao, S. C. Hui, T. M. F. Chan, F. K. Chan, J. J. Y. Sung, E. C. Cheung, and P. Yuen. 2020. Data- GRU: Dual- Attention Time- Aware Gated Recurrent Unit for Irregular Multivariate Time Series. In Proceedings of the AAAI Conference on Artificial Intelligence, Vol. 34. 930- 937. [19] Zihui Wang, Zhe Peng, Xinru Fan, Zheng Wang, Siyang Wu, Rui Yu, ... and Chunyan Wang. 2024. FedAVE: Adaptive data value evaluation framework for collaborative fairness in federated learning. Neurocomputing 574 (2024), 127227. [20] Zihui Wang, Zheng Wang, Lingjun Lyu, Zhigang Peng, Zhiquan Yang, Chuan Wen, and Xiaohui Fan. 2024. FedSAC: Dynamic Submodel Allocation for Collaborative Fairness in Federated Learning. In Proceedings of the 30th ACM SIGKDD Conference on Knowledge Discovery and Data Mining. 3299- 3310. [21] Sajila D. Wickramaratne and Md Shaad Mahmud. 2020. Bi- directional gated recurrent unit based ensemble model for the early detection of sepsis. In 2020 42nd Annual International Conference of the IEEE Engineering in Medicine & Biology Society (EMBC). IEEE, 70- 73. [22] Han Xiao, Kashif Rasul, and Roland Vollgraf. 2017. Fashion- MNIST: A Novel Image Dataset for Benchmarking Machine Learning Algorithms. arXiv preprint arXiv:1708.07747 (2017). [23] Xinyi Xu, Lingjuan Lyu, Xiaofeng Ma, Chunyan Miao, Chee Seng Foo, and Bo An Kiat Huat Low. 2021. Gradient driven rewards to guarantee fairness in collaborative machine learning. In Advances in Neural Information Processing Systems, Vol. 34. 16104- 16117. [24] Gang Yan, Haiyan Wang, Xue Yuan, and Jia Li. 2023. Critical: A critical learning periods augmented client selection framework for efficient federated learning. In Proceedings of the 29th ACM SIGKDD Conference on Knowledge Discovery and Data Mining. ACM, 2898- 2907.

[25] Xiyuan Yang, Wenke Huang, and Mang Ye. 2024. FedAS: Bridging Inconsistency in Personalized Federated Learning. In Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition (CVPR). IEEE, 11986- 11995. https://doi.org/10.1109/CVPR52733.2024.01139 [26] Yang Yang, Xiangwei Zheng, and Cun Ji. 2019. Disease prediction model based on bilstm and attention mechanism. In 2019 IEEE International Conference on Bioinformatics and Biomedicine (BIBM). IEEE, 1141- 1148. [27] Xiangyang Ye, Q. T. Zeng, Julio C. Facelli, Diana L. Brixner, Mike Conway, and Bradley E. Bray. 2020. Predicting optimal hypertension treatment pathways using recurrent neural networks. International Journal of Medical Informatics 139 (2020), 104122. [28] Mikhail Yurochkin, Mayank Agarwal, Soumya Ghosh, Kristjan Greenewald, Naresh Hoang, and Yasaman Khazaeni. 2019. Bayesian Nonparametric Federated Learning of Neural Networks. In International Conference on Machine Learning (Proceedings of Machine Learning Research). PMLR, 7252- 7261. [29] Jinghe Zhang, Kamran Kowsari, James H Harrison, Jason M Lobo, and Laura E Barnes. 2018. Patient2vec: A personalized interpretable deep representation of the longitudinal electronic health record. IEEE Access 6 (2018), 65333- 65346. [30] Jianfei Zhang and Yongqiang Shi. 2024. A Personalized Federated Learning Method Based on Clustering and Knowledge Distillation. Electronics 13, 5 (2024), 857. https://doi.org/10.3390/electronics13050857 [31] Yue Zhao, Meng Li, Liangzhen Lai, Naveen Suda, Dave Civin, and Vikas Chandra. 2018. Federated learning with non- iid data. arXiv preprint arXiv:1806.00582 (2018).

# A IMBALANCED COVARIATE SHIFT PROOF

# A.1 Proof of Theorem 2.1

We first aim to expand

$$
D_{\mathrm{KL}}(p_{\omega +\delta}\left\| p_{\omega}\right\) \(\coloneqq \mathbb{E}_{X\sim p_{\omega +\delta}}\Big[\log p_{\omega +\delta}(X) - \log p_{\omega}(X)\Big].\)
$$

Observe that

$$
\log p_{\omega}(x) = \log \bigl (p_{(\omega +\delta)} - \delta (x)\bigr) = \ell \bigl ((\omega +\delta) - \delta ,x\bigr).
$$

We make a second- order Taylor expansion of  $\ell (\theta - \delta ,x)$  around  $\theta = \omega +\delta$  at  $\delta = 0$ . Concretely, with  $\ell (\theta ,x) = \log p_{\theta}(x)$ , we have

$$
\begin{array}{r}\ell \big((\omega +\delta) - \delta ,x\big)\approx \ell (\omega +\delta ,x) - \delta^{\top}\nabla_{\theta}\ell (\theta ,x)\big|_{\theta = \omega +\delta}\\ +\frac{1}{2}\delta^{\top}\nabla_{\theta}^{2}\ell (\theta ,x)\big|_{\theta = \omega +\delta}\delta , \end{array}
$$

$$
\begin{array}{rl} & {\log p_{\omega +\delta}(x) - \log p_{\omega}(x)\approx \delta^{\top}\nabla_{\theta}\ell (\theta ,x)\big|_{\theta = \omega +\delta}}\\ & {\qquad -\frac{1}{2}\delta^{\top}\nabla_{\theta}^{2}\ell (\theta ,x)\big|_{\theta = \omega +\delta}\delta .} \end{array}
$$

We set

$$
I(\omega +\delta)\coloneqq -\mathbb{E}_{p_{\omega +\delta}}\Big[\nabla_{\theta}^{2}\log p_{\omega +\delta}(X)\Big].
$$

Taking expectation under  $X\sim p_{\omega +\delta}$  gives

$$
\begin{array}{rl} & D_{\mathrm{KL}}(p_{\omega +\delta}\left\| p_{\omega}\right\} = \mathbb{E}_{p_{\omega +\delta}}\Big[\log p_{\omega +\delta}(X) - \log p_{\omega}(X)\Big]\\ & \qquad \approx \mathbb{E}_{p_{\omega +\delta}}\big[-\frac{1}{2}\delta^{\top}\nabla_{\theta}^{2}\ell (\theta ,X)\big|_{\theta = \omega +\delta}\delta \big]\\ & \qquad = \frac{1}{2}\delta^{\top}I(\omega +\delta)\delta \qquad (9)\\ & \qquad \approx \frac{1}{2}\delta^{\top}I(\omega)\delta +\frac{1}{2}\delta^{\top}\nabla_{\omega}I(\omega)\delta \delta \qquad (10) \end{array} \tag{10}
$$

We want to compute or approximate the following KL divergence:

$$
D_{\mathrm{KL}}(\widehat{p}_{\omega +\delta}\left\| p_{\omega +\delta}\right\} ,
$$

where  $\widehat{p}_{\omega +\delta}$  is the empirical distribution (or fitted model) from  $A$  i.i.d. samples  $X_{1},\ldots ,X_{A}$  drawn from the "true" distribution  $p_{\omega +\delta}$ . To simplify notation, let us define:

$\theta^{*}\coloneqq \omega +\delta ,$  and  $\widehat{\theta}$  be the MLE fitted using  $A$  samples from  $p_{\theta^{*}}$

Thus the distribution  $\widehat{p}_{\omega +\delta}$  can be written as  $p_{\widehat{\theta}}$  (i.e. parameterized by  $\widehat{\theta}$ ). We often denote

$$
\Delta \coloneqq \widehat{\theta} -\theta^{*} = \widehat{\theta} -(\omega +\delta). \tag{13}
$$

Hence the divergence of interest is

$$
D_{\mathrm{KL}}(p_{\theta^{*}}\| p_{\theta^{*}}).
$$

Let  $\ell (\theta ,x)\coloneqq \log p_{\theta}(x)$ . Taylor expand  $\log p_{\theta}(x)$  around  $\theta^{*}$

$$
\begin{array}{rl} & {\ell (\widehat{\theta},x) = \ell (\theta^{*},x) + (\widehat{\theta} -\theta^{*})^{\top}\nabla_{\theta}\ell (\theta^{*},x)}\\ & {\qquad +\frac{1}{2} (\widehat{\theta} -\theta^{*})^{\top}\nabla_{\theta}^{2}\ell (\widehat{\theta},x)(\widehat{\theta} -\theta^{*})}\\ & {\qquad = \ell (\theta^{*},x) + \Delta^{\top}\nabla_{\theta}\ell (\theta^{*},x) + \frac{1}{2}\Delta^{\top}\nabla_{\theta}^{2}\ell (\widehat{\theta},x)\Delta ,} \end{array}
$$

where  $\bar{\theta}$  is between  $\theta^{*}$  and  $\bar{\theta}$

$$
\begin{array}{rl} & {\log p_{\widehat{\theta}}(x) - \log p_{\theta^{*}}(x) = (\widehat{\theta} -\theta^{*})^{\top}\nabla_{\theta}\ell (\theta^{*},x)}\\ & {\qquad +\frac{1}{2} (\widehat{\theta} -\theta^{*})^{\top}\nabla_{\theta}^{2}\ell (\widehat{\theta},x)(\widehat{\theta} -\theta^{*}).} \end{array}
$$

$\mathbb{E}p_{\widehat{\theta}}\big[\log p_{\widehat{\theta}}(X) - \log p_{\theta^{*}}(X)\big] = \mathbb{E}_{p_{\widehat{\theta}}}\Big[\Delta^{\top}\nabla_{\theta}\ell (\theta^{*},X) + \frac{1}{2}\Delta^{\top}\nabla_{\theta}^{2}\ell (\widetilde{\theta},X)\Delta \Big].$  We decompose the following expression into three parts:

We deal with the first- order term  $\Delta^{\top}\nabla_{\theta}\ell (\theta^{*},X)$ . Since  $\Delta$  does not depend on  $X$ , we have

$$
\Delta^{\top}\mathbb{E}_{p_{\widehat{\theta}}}\big[\nabla_{\theta}\ell (\theta^{*},X)\big]\approx \Delta^{\top}\mathbb{E}_{p_{\theta^{*}}}\big[\nabla_{\theta}\ell (\theta^{*},X)\big] = \Delta^{\top}\mathbf{0} = 0,
$$

where we used  $\mathbb{E}_{p_{\theta^{*}}}\big[\nabla_{\theta}\ell (\theta^{*},X)\big] = 0$  and  $p_{\widehat{\theta}}\approx p_{\theta^{*}}$  for large  $a$ . Consider the remaining piece in Eq. (11):

$$
\begin{array}{r}\frac{1}{2}\mathbb{E}_{p_{\widehat{\theta}}}\big[\Delta^{\top}\nabla_{\theta}^{2}\ell (\widetilde{\theta},X)\Delta \big]. \end{array}
$$

When  $\widehat{\theta}\approx \theta^{*}$ , we have  $\nabla_{\theta}^{2}\ell (\widetilde{\theta},X)\approx \nabla_{\theta}^{2}\ell (\theta^{*},X)$ , and

$$
\mathbb{E}_{X\sim p_{\theta^{*}}}\big[\nabla_{\theta}^{2}\ell (\theta^{*},X)\big] = -I(\theta^{*}).
$$

Hence

$$
\mathbb{E}_{p_{\widehat{\theta}}}\big[\Delta^{\top}\nabla_{\theta}^{2}\ell (\widetilde{\theta},X)\Delta \big]\approx -\Delta^{\top}I(\theta^{*})\Delta .
$$

Recall the asymptotic distribution:

$$
\Delta = \widehat{\theta} -\theta^{*}\approx \frac{1}{\sqrt{A}}\mathcal{N}(0,I(\theta^{*})^{-1}).
$$

Thus

$$
\begin{array}{rl} & {\mathbb{E}\big[\Delta^{\top}I(\theta^{*})\Delta \big] = \mathrm{trace}\Big[I(\theta^{*})\mathbb{E}(\Delta \Delta^{\top})\Big]}\\ & {\qquad = \mathrm{trace}\Big[I(\theta^{*})\frac{1}{a} I(\theta^{*})^{-1}\Big]}\\ & {\qquad = \frac{1}{A}\mathrm{trace}(I_{R})}\\ & {\qquad = \frac{R}{A}.} \end{array}
$$

Therefore,

$$
\begin{array}{r}\frac{1}{2}\big[-\Delta^{\top}I(\theta^{*})\Delta \big]\approx -\frac{1}{2}\frac{R}{A}. \end{array}
$$

So we conclude the derivation that

$$
D_{\mathrm{KL}}(\widehat{p}_{\omega +\delta}\| p_{\omega +\delta})\approx \frac{R}{2A}\quad \mathrm{for~large~sample~size~}A. \tag{12}
$$

Due to Eq. (12) and Eq. (10), we can get

$$
\begin{array}{r}D_{\mathrm{KL}}(\widehat{p}_{\omega +\delta}\| p_{\omega})\approx \frac{1}{2}\delta^{\top}I(\omega)\delta +\frac{1}{2}\delta^{\top}\nabla_{\omega}I(\omega)\delta \delta +\frac{R}{2A} +e\\ \approx \frac{1}{2}\delta^{\top}I(\omega)\delta +\frac{1}{2}\delta^{\top}\nabla_{\omega}I(\omega)\delta \delta +\frac{R}{2A}, \end{array} \tag{14}
$$

where  $e = \int \big[\widehat{p}_{\omega +\delta}(x) - p_{\omega +\delta}(x)\big]\log \Big(\frac{I(\omega(x)}{p_{\omega + \delta}(x)}\Big)\mathrm{d}x\approx 0.$

# A.2 Proof of Theorem 2.2

For the family  $p_{\mu ,\Sigma}(x) = \mathcal{N}(x;\mu ,\Sigma)$ , the Fisher information matrix  $I(\mu ,\Sigma)$  can be written in a block- diagonal form:

$$
I(\mu ,\Sigma) = \left( \begin{array}{cc}{\Sigma^{-1}} & 0\\ {0} & {\frac{1}{2}\left(\Sigma^{-1}\otimes \Sigma^{-1}\right)} \end{array} \right),
$$

where  $\otimes$  is the Kronecker product. Thus, if we set

$$
\delta = (\delta_{\mu},\mathrm{vec}(\delta_{\Sigma})),
$$

$$
I(\omega) = \left( \begin{array}{cc}{I_{\mu ,\mu}} & 0\\ {0} & {I_{\Sigma ,\Sigma}} \end{array} \right) = \left( \begin{array}{cc}{\Sigma^{-1}} & 0\\ {0} & {\frac{1}{2}\left(\Sigma^{-1}\otimes \Sigma^{-1}\right)} \end{array} \right),
$$

We decompose the following expression into three parts:

$$
\underbrace{\frac{1}{2}\delta^{\top}I(\omega)\delta}_{(\mathbf{A})} + \underbrace{\frac{1}{2}\delta^{\top}\nabla_{\omega}I(\omega)\delta\delta}_{(\mathbf{B})} + \underbrace{\frac{R}{2A}}_{(\mathbf{C})}
$$

For the term  $\begin{array}{r}(\mathbf{A}) = \frac{1}{2}\delta^{\top}I(\omega)\delta \end{array}$  since  $I(\omega)$  is block- diagonal in  $(\mu ,\Sigma)$  then we have

$$
\begin{array}{r}\frac{1}{2}\delta^{\top}I(\omega)\delta = \frac{1}{2}\left(\delta_{\mu}\right)^{\top}\Sigma^{-1}\delta_{\mu} + \frac{1}{2}\left(\mathrm{vec}(\delta_{\Sigma})\right)^{\top}\left[\frac{1}{2}\left(\Sigma^{-1}\otimes \Sigma^{-1}\right)\right]\mathrm{vec}(\delta_{\Sigma}) \end{array} \tag{15}
$$

$$
= \frac{1}{2}\left(\delta_{\mu}\right)^{\top}\Sigma^{-1}\delta_{\mu} + \frac{1}{4}\left(\mathrm{vec}(\delta_{\Sigma})\right)^{\top}\left(\Sigma^{-1}\otimes \Sigma^{-1}\right)\mathrm{vec}(\delta_{\Sigma}).
$$

Recall that  $\delta_{\Sigma}\in \mathbb{R}^{d\times d}$  is the matrix- form perturbation to  $\Sigma$ , while  $\mathrm{vec}(\delta_{\Sigma}) = \mathrm{vec}(\delta_{\Sigma})\in \mathbb{R}^{d^2}$  is its vectorization. A standard identity for the Frobenius norm is

$$
\begin{array}{r}\big(\mathrm{vec}(\delta_{\Sigma})\big)^{\top}\big(\Sigma^{-1}\otimes \Sigma^{-1}\big)\mathrm{vec}(\delta_{\Sigma}) = \| \Sigma^{-1}\delta_{\Sigma}\Sigma^{-1}\|_{F}^{2}. \end{array}
$$

Therefore,

$$
\begin{array}{r}\frac{1}{2}\delta^{\top}I(\omega)\delta = \frac{1}{2}\left(\delta_{\mu}\right)^{\top}\Sigma^{-1}\delta_{\mu} + \frac{1}{2}\left(\Sigma^{-1}\delta_{\Sigma}\Sigma^{-1}\right)\|_{F}^{2}. \end{array} \tag{17}
$$

For the term  $\begin{array}{r}(\mathbf{B}) = \frac{1}{2}\delta^{\top}(\nabla_{\omega}I(\omega)\delta)\delta \end{array}$  because  $I(\mu ,\Sigma)$  is blockdiagonal, its derivative with respect to  $(\mu ,\Sigma)$  also splits into two blocks. Consequently, we can separate:

$$
\nabla_{\omega}I(\omega) = \left( \begin{array}{cc}\nabla_{\omega}\big[\Sigma^{-1}\big] & 0\\ 0 & \nabla_{\omega}\big[\frac{1}{2} (\Sigma^{-1}\otimes \Sigma^{-1})\big] \end{array} \right).
$$

B1 denotes contribution from  $(\mu ,\mu)$  block:

$$
\begin{array}{r}B_{1} = \frac{1}{2}\delta_{\mu}^{\top}\Big[\nabla_{\Sigma}\big(\Sigma^{-1}\big)\big(\delta_{\Sigma}\big)\Big]\delta_{\mu}. \end{array}
$$

We have  $I_{\mu ,\mu}(\Sigma) = \Sigma^{- 1}$ . Its derivative with respect to  $\Sigma$  (a matrix perturbation  $\delta_{\Sigma}$ ) is

$$
\nabla_{\Sigma}\big[\Sigma^{-1}\big]\big(\delta_{\Sigma}\big) = -\Sigma^{-1}\delta_{\Sigma}\Sigma^{-1}.
$$

Inserting  $\delta_{\mu}^{\top}$  and  $\delta_{\mu}$  then yields

$$
\begin{array}{r}B_{1} = -\frac{1}{2}\delta_{\mu}^{\top}\Big(\Sigma^{-1}\delta_{\Sigma}\Sigma^{-1}\Big)\delta_{\mu}. \end{array} \tag{18}
$$

This term vanishes if  $\delta_{\Sigma} = 0$  or if one chooses to ignore  $\delta_{\mu}$ , but is otherwise a valid second- order effect.

B2 denotes contribution from  $(\Sigma ,\Sigma)$  block:

$$
\begin{array}{r}B_{2} = \frac{1}{2}\left(\mathrm{vec}\big(\delta_{\Sigma}\big)^{\top}\Big[\nabla_{\Sigma}I_{\Sigma ,\Sigma}(\Sigma)\big(\delta_{\Sigma}\big)\Big]\mathrm{vec}(\delta_{\Sigma})\right. \end{array} \tag{19}
$$

As in the main text, we set

$$
\begin{array}{r}I_{\Sigma ,\Sigma}(\Sigma) = \frac{1}{2}\big(\Sigma^{-1}\otimes \Sigma^{-1}\big). \end{array}
$$

Hence we define

$$
\begin{array}{r}\nabla_{\Sigma}I_{\Sigma ,\Sigma}(\Sigma) = \frac{1}{2}\nabla_{\Sigma}\big(\Sigma^{-1}\otimes \Sigma^{-1}\big). \end{array}
$$

Suppose  $\delta_{\Sigma}\in \mathbb{R}^{d\times d}$  is a small matrix perturbation of  $\Sigma$ , and let  $\mathrm{vec}(\delta_{\Sigma}) = \mathrm{vec}(\delta_{\Sigma})$  be its vectorized form. Then the second- order expansion term of interest is

$$
\begin{array}{r}\frac{1}{2}\delta^{\top}\nabla_{\omega}\big[\frac{1}{2}\big(\Sigma^{-1}\otimes \Sigma^{-1}\big)\big]\delta \delta = \frac{1}{2}\big(\mathrm{vec}(\delta_{\Sigma})\big)^{\top}\Big[\nabla_{\Sigma}I_{\Sigma ,\Sigma}(\Sigma)\big(\delta_{\Sigma}\big)\Big]\mathrm{vec}(\delta_{\Sigma}). \end{array} \tag{20}
$$

where we note that  $\delta_{\Sigma}$  in the original notation is replaced by  $\delta_{\Sigma}$  in matrix form (or  $\mathrm{vec}(\delta_{\Sigma})$  in vectorized form).

By a standard matrix- calculus result,

$$
\delta \big(\Sigma^{-1}\otimes \Sigma^{-1}\big) = -\Sigma^{-1}\delta_{\Sigma}\Sigma^{-1}\otimes \Sigma^{-1} - \Sigma^{-1}\otimes \Sigma^{-1}\delta_{\Sigma}\Sigma^{-1},
$$

where  $\delta_{\Sigma}$  appears inside each product as the perturbation in matrix form. Hence,

$$
\nabla_{\Sigma}\big(\Sigma^{-1}\otimes \Sigma^{-1}\big)\big(\delta_{\Sigma}\big) = -\Sigma^{-1}\delta_{\Sigma}\Sigma^{-1}\otimes \Sigma^{-1} - \Sigma^{-1}\otimes \Sigma^{-1}\delta_{\Sigma}\Sigma^{-1}.
$$

Since  $I_{\Sigma ,\Sigma}(\Sigma)$  has the extra factor  $\frac{1}{2}$ , we obtain

$$
\begin{array}{r}\nabla_{\Sigma}I_{\Sigma ,\Sigma}(\Sigma)\big(\delta_{\Sigma}\big) = \frac{1}{2}\Big[-\Sigma^{-1}\delta_{\Sigma}\Sigma^{-1}\otimes \Sigma^{-1} - \Sigma^{-1}\otimes \Sigma^{-1}\delta_{\Sigma}\Sigma^{-1}\Big]. \end{array} \tag{21}
$$

We take Eq. (21) into Eq. (20),

$$
\begin{array}{rl} & B_{2} = \frac{1}{2}\left(\mathrm{vec}(\delta_{\Sigma})\right)^{\top}\Big[\nabla_{\Sigma}I_{\Sigma ,\Sigma}(\Sigma)\big(\delta_{\Sigma}\big)\Big]\mathrm{vec}(\delta_{\Sigma})\\ & \quad = \frac{1}{2}\left(\mathrm{vec}(\delta_{\Sigma})\right)^{\top}\Big[\frac{1}{2}\big(-\Sigma^{-1}\delta_{\Sigma}\Sigma^{-1}\otimes \Sigma^{-1}\qquad (22)\\ & \quad \quad -\Sigma^{-1}\otimes \Sigma^{-1}\delta_{\Sigma}\Sigma^{-1}\big)\Big]\mathrm{vec}(\delta_{\Sigma})\\ & \quad = \frac{1}{4}\left(\mathrm{vec}(\delta_{\Sigma})\right)^{\top}\Big[-\Sigma^{-1}\delta_{\Sigma}\Sigma^{-1}\otimes \Sigma^{-1}\qquad (23)\\ & \quad \quad -\Sigma^{-1}\otimes \Sigma^{-1}\delta_{\Sigma}\Sigma^{-1}\Big]\mathrm{vec}(\delta_{\Sigma})\\ & \quad = -\frac{1}{4}\left(\mathrm{vec}(\delta_{\Sigma})\right)^{\top}\Big[\Sigma^{-1}\delta_{\Sigma}\Sigma^{-1}\otimes \Sigma^{-1}\qquad (24)\\ & \quad \quad +\Sigma^{-1}\otimes \Sigma^{-1}\delta_{\Sigma}\Sigma^{-1}\Big]\mathrm{vec}(\delta_{\Sigma}).\\ & \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \end{array} \tag{22}
$$

where  $(a)$  is due to  $(\mathrm{vec}(X))^{\top}(Y\otimes Z)(\mathrm{vec}(X)) = \mathrm{trace}[ZXYX^{\top}]$ .

![](images/a8ed109352b328e45621d900ea1eb444a98c4b66744c274b331630772b68edb4.jpg)  
Figure 4: Comparison of KL divergences on real data (red), the Theorem 2.2 approximation (blue), and a random-subsampling baseline (green). Our approximation aligns well with the actual KL divergence, while random subsampling underestimates the real shift.

For (C), since the M- dimensional Gaussian distribution, we can obtain

$$
\begin{array}{l}{R = \underbrace{M}_{\mathrm{Mean}} + \underbrace{M(M + 1)}_{\mathrm{Covariance}}}\\ {= M + \underbrace{M(M + 1)}_{2}}\\ {= \underbrace{M^{2} + 3M}_{2}.} \end{array}
$$

So we take this term into C,

$$
C = \frac{R}{2A} = \frac{M(M + 4)}{4A} \tag{28}
$$

We take Eq. (28), Eq. (27), Eq. (18) and Eq. (17) into Eq. (15):

$$
\begin{array}{r l} & {D_{\mathrm{KL}}(\widehat{p}_{\omega +\delta}\| p_{\omega})\approx \frac{1}{2}\left(\delta_{\mu}\right)^{\top}\Sigma^{-1}\delta_{\mu} + \frac{1}{4}\| \Sigma^{-1}\delta_{\Sigma}\Sigma^{-1}\|_{F}^{2} + \frac{M(M + 4)}{4A}}\\ & {\qquad -\frac{1}{2}\delta_{\mu}^{\top}\left(\Sigma^{-1}\delta_{\Sigma}\Sigma^{-1}\right)\delta_{\mu} - \frac{1}{2}\operatorname {trace}\left((\delta_{\Sigma}\Sigma^{-1})^{3}\right)}\\ & {\qquad = \frac{1}{2}\left(\delta_{\mu}\right)^{\top}\left(\Sigma^{-1}(I - \delta_{\Sigma}\Sigma^{-1})\right)\delta_{\mu} + \frac{1}{4}\| \Sigma^{-1}\delta_{\Sigma}\Sigma^{-1}\|_{F}^{2}}\\ & {\qquad -\frac{1}{2}\operatorname {trace}\left((\delta_{\Sigma}\Sigma^{-1})^{3}\right) + \frac{M(M + 4)}{4A}} \end{array}
$$

# B THEORETICAL APPROXIMATION VALIDATION

To validate our theoretical approximations, we conduct experiments on the real EHR dataset by first training a Variational Autoencoder (VAE) to embed each client's data into an  $M$ - dimensional latent space, where  $M = 100$ . We then use these client representations to approximate a Gaussian distribution for each client. Similarly, we aggregate all client representations to fit a global Gaussian distribution. The results are shown in Figure 4. In Figure 4, "Real KL" means the empirical KL divergence between each client's approximated distribution and the global fitted distribution. "Approx" refers to our theoretical approximation, where small Gaussian perturbations are added to the mean vector and covariance matrix of each client's approximated distribution, allowing the KL divergence to

be computed directly via Eq. (1). "Random sampling" serves as a control experiment, where each client's sample size is matched by randomly subsampling from the global distribution (i.e., without introducing any intentional distributional shift). The results indicate that our theoretical approximation (blue curve) closely follows the empirical KL values (red curve) across clients. Additionally, the random sampling baseline (green curve) remains significantly lower, confirming that the real dataset exhibits substantial covariate shift effects beyond simple sample- size imbalance. These empirical findings validate the correctness of our theorems and provide a principled basis for constructing simulation datasets to evaluate model performance under the new setting.

# C FEDAKD CONVERGENCE PROOF

Global  $\rightarrow$  Local KD. We compute the gradient of the distillation loss where the global model serves as the teacher and the local model serves as the student. The parameters are given by:

$$
\mathrm{KD}\leftarrow \mathrm{KD}\big(\mathbf{w},\mathbf{w}_g^{t + 1};\mathcal{D}_k\big). \tag{38}
$$

$$
\begin{array}{rl} & {\nabla \mathrm{KD} = \nabla \mathbb{E}_{\mathbf{x},i\in \mathcal{D}_k}[-\sigma (\mathbf{x}_{k,i}^\top \mathbf{w}_g^{t + 1})\log (\sigma (\mathbf{x}_{k,i}^\top \mathbf{w}))}\\ & {\qquad \left. - (1 - \sigma (\mathbf{x}_i^\top \mathbf{w}_g^{t + 1}))\log (1 - \sigma (\mathbf{x}_i^\top \mathbf{w}))]}\\ & {\qquad \overset {(a)}{=}\mathbb{E}_{\mathbf{x},i\in \mathcal{D}_k}[\sigma (\mathbf{x}_{k,i}^\top \mathbf{w}) - \sigma (\mathbf{x}_{k,i}^\top \mathbf{w}_g^{t + 1})]\mathbf{x}_{k,i}]}\\ & {\qquad \overset {(b)}{=}\mathbb{E}_{\mathbf{x},i\in \mathcal{D}_k}[\sigma (\xi_{k,i})(1 - \sigma (\xi_{k,i}))(\mathbf{x}_{k,i}^\top (\mathbf{w} - \mathbf{w}_g^{t + 1}))\mathbf{x}_{k,i}]}\\ & {\qquad = \mathbb{E}_{\mathbf{x},i\in \mathcal{D}_k}[\sigma (\xi_{k,i})(1 - \sigma (\xi_{k,i}))\mathbf{x}_{k,i}](\mathbf{w} - \mathbf{w}_g^{t + 1}),} \end{array} \tag{32}
$$

where

$(a)$  : This step employs the standard derivative of the binary crossentropy term  $- p\log (q) - (1 - p)\log (1 - q)$  . Differentiation with respect to  $q$  and then accounting for the chain rule via the feature vector, yields  $(q - p)$  .  $(b)$  : This step applies the Mean Value Theorem (MVT) to the sigmoid difference:  $\sigma (\mathbf{x}_{k,i}^{\top}\mathbf{w}) - \sigma (\mathbf{x}_{k,i}^{\top}\mathbf{w}_g^{t + 1}) = \sigma^{\prime}(\xi_{1})\left[\mathbf{x}_{k,i}^{\top}(\mathbf{w}-$ $\mathbf{w}_g^{t + 1})\big]$  for some  $\xi_{1}$  between  $\mathbf{x}_{k,i}^{\top}$  w and  $\mathbf{x}_{k,i}^{\top}\mathbf{w}_g^{t + 1}$  . Since  $\sigma^{\prime}(z) =$ $\sigma (z)\left[1 - \sigma (z)\right]$  , we use  $\sigma (\xi_{1})\left[1 - \sigma (\xi_{1})\right]$  as a factor.

Local  $\rightarrow$  Global KD. Then we compute the distillation loss definition where the local model serves as the teacher and the global model serves as the student. The parameters are given by:

$$
\mathrm{KD}\leftarrow \mathrm{KD}\big(\mathbf{w},\mathbf{w}_k^t;I_k^t\big).
$$

Based on Eq. (32), we can similarly derive the following equation:

$$
\nabla \mathrm{KD} = \mathbb{E}_{\mathbf{x}_{k,i}\in I_k^t}\left[\sigma (\zeta_{k,i})\left(1 - \sigma (\zeta_{k,i})\right)\mathbf{x}_{k,i}\mathbf{x}_{k,i}^\top \right]\left(\mathbf{w} - \mathbf{w}_k^t\right)
$$

We define two errors:

$$
\begin{array}{rl} & e_k^t = \nabla \vec{\mathcal{L}} (\mathbf{w}_k^t,\mathbf{w}_g^t;\mathcal{D}_k)\\ & \quad = \nabla \mathcal{L}(\mathbf{w}_k^t;\mathcal{D}_k) + \alpha \mathcal{L}_{KD}(\mathbf{w}_k^t,\mathbf{w}_g^t;\mathcal{D}_k)\\ & \quad = \nabla \mathcal{L}(\mathbf{w}_k^t;\mathcal{D}_k) + \alpha \mathbb{E}_{\mathbf{x}_{k,i}\in \mathcal{D}_k}\big[\sigma (\xi_{k,i})\big(1 - \sigma (\xi_{k,i})\big)\\ & \quad \quad \cdot \mathbf{x}_{k,i}\mathbf{x}_{k,i}^\top \big]\big(\mathbf{w}_k^t -\mathbf{w}_g^t\big), \end{array} \tag{33}
$$

and

$$
\begin{array}{rl} & e_{g,k}^{t + 1} = \nabla \overleftarrow{\mathcal{L}} (\mathbf{w}_{g,k}^{t + 1},\mathbf{w}_k^t;I_k^t)\\ & \qquad = \nabla \mathcal{L}(\mathbf{w}_{g,k}^{t + 1};I_k^t) + \beta \mathcal{L}_{KD}(\mathbf{w}_{g,k}^{t + 1},\mathbf{w}_k^t;I_k^t)\\ & \qquad = \nabla \mathcal{L}(\mathbf{w}_{g,k}^{t + 1};I_k^t) + \beta \mathbb{E}_{\mathbf{x}_{k,i}\in I_k^t}\left[\sigma (\zeta_{k,i})\left(1 - \sigma (\zeta_{k,i})\right)\right.\\ & \qquad \left.\cdot \mathbf{x}_{k,i}\mathbf{x}_{k,i}^\top \right]\left(\mathbf{w}_{g,k}^{t + 1} - \mathbf{w}_k^t\right). \end{array} \tag{36}
$$

From these Eq. (38) and Eq. (35), we isolate expressions of  $\mathbf{w}_k^t - \mathbf{w}_g^t$  and  $\mathbf{w}_{g,k}^{t + 1} - \mathbf{w}_k^t$

$$
\begin{array}{r}\mathbf{w}_k^t -\mathbf{w}_g^t = \frac{1}{\alpha\mathbb{E}_{\mathbf{x}_{k,i}\in\mathcal{D}_k}\left[\sigma(\zeta_{k,i})\left(1 - \sigma(\zeta_{k,i})\right)\right]}\mathbb{E}_{\mathbf{x}_{k,i}\in\mathcal{D}_k}\left[\mathbf{x}_{k,i}\mathbf{x}_{k,i}^\top\right]^{-1}\\ \times (e_k^t -\nabla \mathcal{L}(\mathbf{w}_k^t;\mathcal{D}_k)), \end{array} \tag{39}
$$

and

$$
\begin{array}{rl} & {\mathbf{w}_{g,k}^{t + 1} - \mathbf{w}_k^t = \frac{1}{\beta\mathbb{E}_{\mathbf{x}_{k,i}\in I_k^t}\left[\sigma(\zeta_{k,i})\left(1 - \sigma(\zeta_{k,i})\right)\right]}\mathbb{E}_{\mathbf{x}_{k,i}\in I_k^t}\left[\mathbf{x}_{k,i}\mathbf{x}_{k,i}^\top\right]^{-1}}\\ & {\qquad \times (e_{g,k}^{t + 1} - \nabla \mathcal{L}(\mathbf{w}_{g,k}^{t + 1};I_k^t)).} \end{array} \tag{30}
$$

Using the inexactness measure, we set  $\gamma \coloneqq \max \{\gamma_1,\gamma_2\}$ . Then we can bound Eqs. (39) and (35) into the following form:

$$
\begin{array}{rl} & {||e_k^t || = ||\nabla \overrightarrow{\mathcal{L}} (\mathbf{w}_k^t,\mathbf{w}_g^t;\mathcal{D}_k)||}\\ & {\qquad \overset {(a)}{\leq}\gamma_1||\nabla \overrightarrow{\mathcal{L}} (\mathbf{w}_g^t,\mathbf{w}_g^t;\mathcal{D}_k)||}\\ & {\qquad = \gamma_1||\nabla \mathcal{L}(\mathbf{w}_g^t;\mathcal{D}_k)||}\\ & {\qquad \leq \gamma_1||\nabla \mathcal{L}(\mathbf{w}_g^t;\mathcal{D}_k)||,} \end{array} \tag{44}
$$

where  $(a)$  is based on Definition 3.1.

Next, we consider  $||\mathbf{w}_k^t - \mathbf{w}_g^t||$ , we bound Eq. (39) in another form:

$$
\hat{\mathbf{w}}_k^t = \arg \min_{\mathbf{w}}\vec{\mathcal{L}} (\mathbf{w},\mathbf{w}_g^t;\mathcal{D}_k). \tag{45}
$$

$$
\begin{array}{rl} & {||\mathbf{w}_k^t -\mathbf{w}_g^t||\leq ||\hat{\mathbf{w}}_k^t -\mathbf{w}_g^t|| + ||\hat{\mathbf{w}}_k^t -\mathbf{w}_k^t ||}\\ & {\qquad \overset {(b)}{\leq}\frac{1}{\mu} (||\nabla \overrightarrow{\mathcal{L}} (\mathbf{w}_g^t,\mathbf{w}_g^t;\mathcal{D}_k)|| + ||\nabla \overrightarrow{\mathcal{L}} (\mathbf{w}_k^t,\mathbf{w}_g^t;\mathcal{D}_k)||)} \end{array} \tag{46}
$$

$$
\begin{array}{rl} & {\leq \frac{1 + \gamma}{\mu} ||\nabla \overrightarrow{\mathcal{L}} (\mathbf{w}_g^t,\mathbf{w}_g^t;\mathcal{D}_k)||}\\ & {= \frac{1 + \gamma}{\mu} ||\nabla \mathcal{L}(\mathbf{w}_g^t;\mathcal{D}_k)||.} \end{array} \tag{49}
$$

where  $(b)$  is based on Assumption 2. Thus, we can bound Eq. (38) into the following form:

$$
\begin{array}{rl} & {\| e_{g,k}^{t + 1}\| = |\nabla \overleftarrow{\mathcal{L}} (\mathbf{w}_{g,k}^{t + 1},\mathbf{w}_k^t;I_k^t)||}\\ & {\qquad \overset {(c)}{\leq}\gamma_2||\nabla \overleftarrow{\mathcal{L}} (\mathbf{w}_k^t,\mathbf{w}_k^t;I_k^t)||}\\ & {\qquad = \gamma_2||\nabla \mathcal{L}(\mathbf{w}_k^t;I_k^t)||}\\ & {\qquad \leq \gamma ||\nabla \mathcal{L}(\mathbf{w}_k^t;I_k^t)||}\\ & {\qquad \leq \gamma (||\nabla \mathcal{L}(\mathbf{w}_k^t;D_k)|| + ||\nabla \mathcal{L}(\mathbf{w}_k^t;D_k) - \nabla \mathcal{L}(\mathbf{w}_k^t;I_k^t)||)}\\ & {\qquad \overset {(d)}{\leq}\gamma (1 + \theta)||\nabla \mathcal{L}(\mathbf{w}_k^t;D_k)||}\\ & {\qquad \leq \gamma (1 + \theta)(||\nabla \mathcal{L}(\mathbf{w}_g^t;D_k)|| + ||\nabla \mathcal{L}(\mathbf{w}_g^t;D_k)}\\ & {\qquad -\nabla \mathcal{L}(\mathbf{w}_k^t;D_k)||)}\\ & {\qquad \overset {(e)}{\leq}\gamma (1 + \theta)(||\nabla \mathcal{L}(\mathbf{w}_g^t;D_k)|| + L||\mathbf{w}_g^t -\mathbf{w}_k^t||)}\\ & {\qquad \overset {(f)}{\leq}(L(1 + \gamma) + \mu)\frac{(1 + \theta)}{\mu} ||\nabla \mathcal{L}(\mathbf{w}_g^t;D_k)||,} \end{array} \tag{50}
$$

where  $(c)$  is based on Definition 3.1,  $(d)$  is based on Assumption 4,  $(e)$  is based on Assumption 1, and  $(f)$  is based on Eq. (49).

We denote  $\Lambda_{1} = \mathbb{E}_{\mathbf{x}_{k,i}\in \mathcal{D}_{k}}[\mathbf{x}_{k,i}\mathbf{x}_{k,i}],c_{1} = \mathbb{E}_{\mathbf{x}_{k,i}\in \mathcal{D}_{k}}[\sigma (\zeta_{k,i})$ $1-$ $\sigma (\xi_{k,i}))\big],\Lambda_2 = \mathbb{E}_{\mathbf{x}_{k,i}\in \mathcal{I}_k^t}[\mathbf{x}_{k,i}\mathbf{x}_{k,i}],c_2 = \mathbb{E}_{\mathbf{x}_{k,i}\in \mathcal{I}_k^t}\big[\sigma (\zeta_{k,i})\big(1 - \sigma (\zeta_{k,i})\big)\big],$ $\mathbb{E}_k[\Lambda_1] = \Omega_1,\mathbb{E}_k[\Lambda_2] = \Omega_2,\mathbb{E}_k[c_1] = d_1,\mathbb{E}_k[c_2] = d_2,$

$$
\begin{array}{r}\mathbb{E}_k[\mathbf{w}_k^T ] - \mathbb{E}_k[\mathbf{w}_g^T ] = \frac{\mathbb{E}_k[\mathbb{E}_{\mathbf{x}_{k,i}\in\mathcal{D}_k}[\mathbf{x}_{k,i}\mathbf{x}_{k,i}^\top]^{-1}]}{\alpha\mathbb{E}_k\mathbb{E}_{\mathbf{x}_{k,i}\in\mathcal{D}_k}[\sigma(\zeta_{k,i})\left(1 - \sigma(\zeta_{k,i})\right)]}\\ \times \mathbb{E}_k(e_k^t -\mathbb{E}_k\nabla \mathcal{L}(\mathbf{w}_k^t;\mathcal{D}_k))\\ = \frac{\Omega_1^{-1}}{\alpha d_1}\times \mathbb{E}_k(e_k^t -\mathbb{E}_k\nabla \mathcal{L}(\mathbf{w}_k^t;\mathcal{D}_k)), \end{array} \tag{61}
$$

and

$$
\begin{array}{r}\mathbb{E}_k[\mathbf{w}_{g,k}^{t + 1}] - \mathbb{E}_k[\mathbf{w}_k^t ] = \frac{\mathbb{E}_k[\mathbb{E}_{\mathbf{x}_{k,i}\in\mathcal{I}_k^t}[\mathbf{x}_{k,i}\mathbf{x}_{k,i}^\top]^{-1}]}{\beta\mathbb{E}_k[\mathbb{E}_{\mathbf{x}_{k,i}\in\mathcal{I}_k^t}[\sigma(\zeta_{k,i})\left(1 - \sigma(\zeta_{k,i})\right)]}\\ \times (\mathbb{E}_k[e_{g,k}^{t + 1}] - \mathbb{E}_k[\nabla \mathcal{L}(\mathbf{w}_{g,k}^{t + 1};\mathcal{I}_k^t)])\\ = \frac{\Omega_2^{-1}}{\beta d_2}\times (\mathbb{E}_k[e_{g,k}^{t + 1}] - \mathbb{E}_k[\nabla \mathcal{L}(\mathbf{w}_{g,k}^{t + 1};\mathcal{I}_k^t)]). \end{array} \tag{62}
$$

According to Eq. (61) and Eq. (63), we have

$$
\begin{array}{r l r} & {} & {\mathbf{w}_{g}^{t + 1} - \mathbf{w}_{g}^{t} = \mathbb{E}_{k}[\mathbf{w}_{g,k}^{t + 1}] - \mathbb{E}_{k}[\mathbf{w}_{k}^{t}] + \mathbb{E}_{k}[\mathbf{w}_{k}^{t}] - \mathbb{E}_{k}[\mathbf{w}_{g}^{t}]}\\ & {} & {= \frac{\Omega_{2}^{-1}}{\beta d_{2}}\times (\mathbb{E}_{k}[e_{g,k}^{t + 1}] - \mathbb{E}_{k}[\nabla \mathcal{L}(\mathbf{w}_{g,k}^{t + 1};\mathcal{I}_{k}^{t})])}\\ & {} & {+\frac{\Omega_{1}^{-1}}{\alpha d_{1}}\times (\mathbb{E}_{k}[e_{k}^{t}] - \mathbb{E}_{k}[\nabla \mathcal{L}(\mathbf{w}_{k}^{t};\mathcal{D}_{k})])}\\ & {} & {= -\frac{\Omega_{2}^{-1}}{\beta d_{2}}\times (\mathbb{E}_{k}[\nabla \mathcal{L}(\mathbf{w}_{g}^{t};\mathcal{D}_{k}) + M])}\\ & {} & {-\frac{\Omega_{1}^{-1}}{\alpha d_{1}}\times (\mathbb{E}_{k}[\nabla \mathcal{L}(\mathbf{w}_{g}^{t};\mathcal{D}_{k}) + N]),} \end{array} \tag{68}
$$

where we set

$$
\begin{array}{rl} & M = \nabla \mathcal{L}(\mathbf{w}_{g,k}^{t + 1};I_k^t) - \nabla \mathcal{L}(\mathbf{w}_g^t;\mathcal{D}_k) - e_{g,k}^{t + 1}\\ & N = \nabla \mathcal{L}(\mathbf{w}_k^t;\mathcal{D}_k) - \nabla \mathcal{L}(\mathbf{w}_g^t;\mathcal{D}_k) - e_k^t \end{array} \tag{69}
$$

Firstly, we bound the following term:

$$
\begin{array}{r l r} & {} & {||\nabla \overleftarrow{\mathcal{L}} (\mathbf{w}_{g}^{t},\mathbf{w}_{k}^{t};I_{k}^{t})|| = ||\nabla \mathcal{L}(\mathbf{w}_{g}^{t};I_{k}^{t}) + \beta c_{2}\cdot \Lambda_{2}\left(\mathbf{w}_{g}^{t} - \mathbf{w}_{k}^{t}\right)||}\\ & {} & {\overset {(a)}{\leq}||\nabla \mathcal{L}(\mathbf{w}_{g}^{t};I_{k}^{t})|| + \frac{\beta||\Lambda_{2}||}{4} ||\mathbf{w}_{g}^{t} - \mathbf{w}_{k}^{t}||}\\ & {} & {\overset {(b)}{\leq}(1 + \theta)||\nabla \mathcal{L}(\mathbf{w}_{g}^{t};\mathcal{D}_{k})||}\\ & {} & {+\frac{(1 + \gamma)\beta||\Lambda_{2}||}{4\mu} ||\nabla \mathcal{L}(\mathbf{w}_{g}^{t};\mathcal{D}_{k})||}\\ & {} & {\overset {(c)}{\leq}((1 + \theta) + \frac{(1 + \gamma)\beta||\Lambda_{2}||}{4\mu})||\nabla \mathcal{L}(\mathbf{w}_{g}^{t};\mathcal{D}_{k})||,} \end{array} \tag{74}
$$

where  $(a)$  is due to  $0< c_{2}\leq \frac{1}{4}$ , and  $(b)$  is due to Assumption 4 and Eq. (49).

We consider  $\| \mathbf{w}_{g,k}^{t + 1} - \mathbf{w}_{g}^{t}\|$

$$
\hat{\mathbf{w}}_{g,k}^{t + 1} = \arg \min_{\mathbf{w}}\overleftarrow{\mathcal{L}} (\mathbf{w},\mathbf{w}_k^t;I_k^t). \tag{75}
$$

$$
||\mathbf{w}_{g,k}^{t + 1} - \mathbf{w}_g^t ||\leq ||\hat{\mathbf{w}}_{g,k}^{t + 1} - \mathbf{w}_g^t || + ||\hat{\mathbf{w}}_{g,k}^{t + 1} - \mathbf{w}_{g,k}^{t + 1}|| \tag{76}
$$

$$
\begin{array}{r}\overset {(a)}{\leq}\frac{1}{\mu} (||\nabla \overleftarrow{\mathcal{L}} (\mathbf{w}_g^t,\mathbf{w}_k^t;I_k^t)|| + ||\nabla \overleftarrow{\mathcal{L}} (\mathbf{w}_{g,k}^{t + 1},\mathbf{w}_k^t;I_k^t)||) \end{array} \tag{77}
$$

$$
= \frac{1}{\mu} (||e_{g,k}^{t + 1}|| + ||\nabla \overleftarrow{\mathcal{L}} (\mathbf{w}_g^t;\mathbf{w}_k^t;I_k^t)||) \tag{78}
$$

$$
\overset {(b)}{\leq}((L(1 + \gamma) + \mu)\frac{\gamma(1 + \theta)}{\mu} +(1 + \theta) + \frac{(1 + \gamma)\beta||\Omega_2||}{4\mu}) \tag{79}
$$

$$
\cdot ||\nabla \mathcal{L}(\mathbf{w}_g^t;\mathcal{D}_k)||, \tag{80}
$$

where  $(a)$  is due to Assumption 2, and  $(b)$  is based on Eq. (38) and Eq. (74).

We can bound  $||M||$  and  $||N||$  in the following form:

$$
\begin{array}{rl} & {||M||\leq ||\nabla \mathcal{L}(\mathbf{w}_{g,k}^{t + 1};\mathcal{D}_k) - \nabla \mathcal{L}(\mathbf{w}_g^t;\mathcal{D}_k)||}\\ & {\qquad +||\nabla \mathcal{L}(\mathbf{w}_{g,k}^{t + 1};\mathcal{I}_k^t) - \nabla \mathcal{L}(\mathbf{w}_{g,k}^{t + 1};\mathcal{D}_k)|| + ||e_{g,k}^{t + 1}||}\\ & {\leq ||\nabla \mathcal{L}(\mathbf{w}_{g,k}^{t + 1};\mathcal{D}_k) - \nabla \mathcal{L}(\mathbf{w}_g^t;\mathcal{D}_k)||}\\ & {\qquad +\theta ||\nabla \mathcal{L}(\mathbf{w}_{g,k}^{t + 1};\mathcal{D}_k)|| + ||e_{g,k}^{t + 1}||}\\ & {\leq (1 + \theta)||\nabla \mathcal{L}(\mathbf{w}_{g,k}^{t + 1};\mathcal{D}_k) - \nabla \mathcal{L}(\mathbf{w}_g^t;\mathcal{D}_k)||}\\ & {\qquad +\theta ||\nabla \mathcal{L}(\mathbf{w}_{g,k}^{t + 1};\mathcal{B}_k)|| + ||e_{g,k}^{t + 1}|}\\ & {\leq (1 + \theta)L||\mathbf{w}_{g,k}^{t + 1} - \mathbf{w}_g^t || + \theta ||\nabla \mathcal{L}(\mathbf{w}_g^t;\mathcal{D}_k)|| + ||e_{g,k}^{t + 1}||}\\ & {\overset {(a)}{\leq}r_1'||\nabla \mathcal{L}(\mathbf{w}_g^t;\mathcal{D}_k)||} \end{array} \tag{88}
$$

where  $(a)$  is based on Eq. (79) and Eq. (38), and  $r_1' = ((L(1 + \gamma) + \mu)\frac{\gamma(1 + \theta)}{\mu} +(1 + \theta) + \frac{(1 + \gamma)\beta||\Lambda_2||}{4\mu})(1 + \theta)L + \theta +(L(1 + \gamma) + \mu)\frac{\gamma(1 + \theta)}{\mu})$  and  $r_1 = ((L(1 + \gamma) + \mu)\frac{\gamma(1 + \theta)}{\mu} +(1 + \theta) + \frac{(1 + \gamma)\beta||\Omega_2||}{4\mu})(1 + \theta)L+$

$$
\begin{array}{rl} & {\theta +\big(L(1 + \gamma) + \mu \big)\frac{\gamma(1 + \theta)}{\mu}\big).}\\ & {\qquad ||N||\leq ||\nabla \mathcal{L}(\mathbf{w}_k^t;\mathcal{D}_k) - \nabla \mathcal{L}(\mathbf{w}_g^t;\mathcal{D}_k)|| + ||e_k^t ||}\\ & {\qquad \leq L||\mathbf{w}_k^t -\mathbf{w}_g^t || + ||e_k^t ||\overset {(a)}{\leq}r_2||\nabla \mathcal{L}(\mathbf{w}_g^t;\mathcal{D}_k)||} \end{array} \tag{89}
$$

where  $(a)$  is based on Eq. (39) and Eq. (35), and  $r_2 = \frac{L(1 + \gamma)}{\mu} + \gamma$ .

We set  $< \mathcal{L}(\mathbf{w}_g^t; \mathcal{D}_g), \mathbf{w}_g^{t + 1} - \mathbf{w}_g^t > = U$ , and according to Eq. (87), Eq. (90) and Assumption 3, we have

$$
\begin{array}{rl} & {U = (-\frac{\Omega_2^{-1}}{\beta d_2}\frac{\Omega_1^{-1}}{\alpha d_1})< \mathcal{L}(\mathbf{w}_g^t;\mathcal{D}_g),\mathbb{E}_k[\nabla \mathcal{L}(\mathbf{w}_g^t;\mathcal{D}_k)] > }\\ & {\qquad +(-\frac{\Omega_2^{-1}}{\beta d_2} -\frac{\Omega_1^{-1}}{\alpha d_1})< \mathcal{L}(\mathbf{w}_g^t;\mathcal{D}_g),\mathbb{E}_k[M] + \mathbb{E}_k[N] > }\\ & {\leq ||\frac{\Omega_2^{-1}}{\beta d_2} +\frac{\Omega_1^{-1}}{\alpha d_1} ||(-B||\mathcal{L}(\mathbf{w}_g^t;\mathcal{D}_g)|||^2}\\ & {\qquad +||\mathcal{L}(\mathbf{w}_g^t;\mathcal{D}_g)||(\mathbb{E}_k[||M||] + \mathbb{E}_k[||N||]))}\\ & {\leq (||\frac{\Omega_2^{-1}}{\beta d_2} || + ||\frac{\Omega_1^{-1}}{\beta d_1} ||)(r_1 + r_2 - 1)B\cdot \mathcal{L}(\mathbf{w}_g^t;\mathcal{D}_g)}\\ & {\qquad \leq 4(||\frac{\Omega_2^{-1}}{\beta} || + ||\frac{\Omega_1^{-1}}{\alpha} ||)(r_1 + r_2 - 1)B\cdot \mathcal{L}(\mathbf{w}_g^t;\mathcal{D}_g)}\\ & {\qquad \leq (\frac{4}{\beta ||\Omega_2||} +\frac{4}{\alpha ||\Omega_1||})(r_1 + r_2 - 1)B\cdot \mathcal{L}(\mathbf{w}_g^t;\mathcal{D}_g)} \end{array} \tag{95}
$$

From Eq. (79) and Assumption 3, we can get

$$
\begin{array}{rl} & {||\mathbf{w}_g^{t + 1} - \mathbf{w}_g^t || = \mathbb{E}_k[||\mathbf{w}_{g,k}^{t + 1} - \mathbf{w}_g^t ||]}\\ & {\qquad \leq \mathcal{B}((L(1 + \gamma) + \mu)\frac{\gamma(1 + \theta)}{\mu}}\\ & {\qquad +(1 + \theta) + \frac{(1 + \gamma)\beta||\Omega_2||}{4\mu})\cdot ||\nabla \mathcal{L}(\mathbf{w}_g^t;\mathcal{D}_g)||} \end{array} \tag{99}
$$

According to Eqs. (99), (97), Assumption 2, and Assumption 1, we can get

$$
\begin{array}{rlr} & {} & {\mathcal{L}(\mathbf{w}_g^{t + 1};\mathcal{D}_g)\leq \mathcal{L}(\mathbf{w}_g^t;\mathcal{D}_g) + < \mathcal{L}(\mathbf{w}_g^t;\mathcal{D}_g),\mathbf{w}_g^{t + 1} - \mathbf{w}_g^t >}\\ & {} & {+\frac{L}{2} ||\mathbf{w}_g^{t + 1} - \mathbf{w}_g^t ||^2}\\ & {} & {\leq \mathcal{L}(\mathbf{w}_g^t;\mathcal{D}_g) - r||\nabla \mathcal{L}(\mathbf{w}_g^t;\mathcal{D}_g)||^2} \end{array} \tag{102}
$$

$$
\begin{array}{r}\mathcal{L}(\mathbf{w}_g^{t + 1};\mathcal{D}_g) - \mathcal{L}(\mathbf{w}^*;\mathcal{D}_g)\leq (1 - 2\mu)r[\mathcal{L}(\mathbf{w}_g^t;\mathcal{D}_g) - \mathcal{L}(\mathbf{w}^*;\mathcal{D}_g)]. \end{array}
$$

$$
\begin{array}{r}\mathrm{where~we~set}r = (\frac{4}{\beta||\Omega_2||} +\frac{4}{|\alpha||\Omega_1||})B - \frac{LB^2}{2} ((L(1 + \gamma) + \mu)\frac{\gamma(1 + \theta)}{\mu} +\\ (1 + \theta) + (\frac{(1 + \gamma)\beta||\Omega_2||}{\mu})^2 -(\frac{4}{\beta||\Omega_2||} +\frac{4}{|\alpha||\Omega_1||})(r_1 + r_2)B,r_1 = ((L(1 + \gamma) + \mu)\frac{\gamma(1 + \theta)}{\mu} +(1 + \theta) + (\frac{(1 + \gamma)\beta||\Omega_2||}{\mu})(1 + \theta)L + \theta +L(1 + \gamma) + \\ \mu)\frac{\gamma(1 + \theta)}{\mu}),r_2 = \frac{L(1 + \mu)}{\mu} +\gamma ,\Omega_1 = \mathbb{E}_k[\mathbb{E}_{k,i\in \mathcal{D}_k}[\mathbf{x}_{k,i}\mathbf{x}_{k,i}]]\mathrm{~and~}\Omega_2 = \\ \mathbb{E}_k[\mathbb{E}_{k,i\in \mathcal{T}_k^t}[\mathbf{x}_{k,i}\mathbf{x}_{k,i}]]. \end{array}
$$

# D TRADITIONAL NON-IID SETTING EXPERIMENTS

# D.1 Non-IID Settings

To ensure fair comparisons and consistent experimental setups with existing collaborative fairness approaches, we adopt the same environment and configurations as FedAVE [19] and FedSAC [20]. We adopt the following traditional non- IID setting. CLA (Imbalanced

Class Distributions) used in [13, 23] and DIR (Imbalanced Sizes + Class Distributions) used in [1, 28] are commonly used non- IID settings in federated learning. Under these scenarios, the label types across clients become severely imbalanced, resulting in skewed partitions that pose significant challenges for model training and fairness. POW (Imbalanced Dataset Size) was also used in this experiment.

# D.2 Federated Data Simulation

In the simulation experiments, we use two image classification datasets: Fashion MNIST [22] and CIFAR10 [7]. Fashion MNIST contains 70,000 grayscale images  $(28 \times 28)$  evenly split into 10 classes (e.g., T- shirt/top, trousers). CIFAR10 consists of 60,000 color images  $(32 \times 32)$  across 10 classes (e.g., airplane, bird). We partition the datasets into training, validation, and testing in a ratio of 7:1:2. In addition, we set the number of clients as  $K = 10$ . To simulate the POW partition, we follow a power law with an exponent of 1 to divide the global data into 10 clients. For the  $k$ - th client, its data size is  $|\mathcal{D}_k| = \frac{1}{KZ} |\mathcal{D}_g|$ , where  $Z = \sum_{k = 1}^{10} \frac{1}{k}$ . For the CLA partition, we fix the local data size and assign labels in an imbalanced manner: the first client receives data from 1 class, the second client from 2 classes, the third client from 3 classes, and so on until the tenth client, which receives data from 10 classes. For the DIR partition, we construct three different splits using a Dirichlet distribution with concentration parameters  $\alpha = 1$ ,  $\alpha = 2$ , and  $\alpha = 3$ . Concretely, suppose there are  $C$  classes in the global dataset  $\mathcal{D}_g$ . For each class  $c$ , let  $|\mathcal{D}_c|$  denote the total number of samples in class  $c$ , and sample a probability vector  $\mathbf{p}_c = \left(p_0, \ldots , p_{c,k}, \ldots , p_{c,K}\right) \propto \operatorname{Dirichlet}(\alpha , \alpha , \ldots , \alpha)$ , where  $K = 10$  is the number of clients. We then allocate  $\lfloor p_{c,k} \mid \mathcal{D}_c \rfloor \rfloor$  samples of class  $c$  to client  $k$ . Hence, the local dataset for client  $k$  is  $\mathcal{D}_k = \bigcup_{i = 1}^{c}\mathcal{D}_{c,k}$ , where  $|\mathcal{D}_{c,k}| = \left\lfloor p_{c,k} \cdot |\mathcal{D}_c| \right\rfloor$ .

# D.3 Baselines

We compare our method against two categories of baselines: Collaborative Fairness algorithms designed for non- IID data, and two standard references without fairness considerations. Specifically, we include CGSV [23], CFFL [13], FedAVE [19], and FedSAC [20], which explicitly address fairness by measuring client contributions or customizing reward allocations. Additionally, we compare with Standalone (each client trains independently without aggregation) and the classic FedAvg [15] for federated averaging. Unlike personalized federated learning approaches that tailor models to each client's local distribution, our setting uses a global test set and aims to evaluate overall model performance under traditional non- IID data partitions. Hence, we do not include personalized FL baselines here, since they focus on fitting each client's individual feature space. Instead, we follow the conventional setup in collaborative fairness (CF) research and compare only with CF- oriented baselines under these non- IID settings.

# D.4 Implementation

Following the literature [19, 20], we adopt a 2- layer Convolutional Neural Network (CNN) for the Fashion MNIST dataset, with minibatch size  $B = 32$  and learning rate  $\mathrm{lr} = 0.15$ . Then, for CIFAR10, we

employ a 3- layer CNN [19, 20] with mini- batch size  $B = 128$  and learning rate  $\mathrm{lr} = 0.015$ . We implement all baselines and our model in PyTorch and train them on an NVIDIA RTX A6000 GPU.

# D.5 Evaluation Metrics

Since CLA and DIR settings only allow data with partial labels on each client, they cannot use the local evaluation as we did in the main experiments for the imbalanced covariate shift setting. Thus, following existing work [19, 20], we conduct global evaluations, i.e., all clients are evaluated on a single global test set. We still report the average values of Maximum Client Accuracy and Collaborative Fairness (CF) Coefficient for three runs.

# D.6 Results of Experiments

In the traditional federated learning setting (using a single global test set under non- IID data partitions), Table 5 compares both maximum accuracy and collaborative fairness of our method against various baselines. We observe that our approach consistently outperforms the baselines in terms of both metrics. Specifically, for the POW partition on FashionMNIST, our method achieves  $87.93 \pm 0.25\%$  in max accuracy, surpassing FedSAC  $(87.83 \pm 0.07\%)$  and other baselines. A similar advantage is seen on CIFAR10, where FedAKD outperforms FedSAC and FedAVE under the same partition. For the DIR partition, our method FedAKD attains higher maximum accuracy on both FashionMNIST and CIFAR10 with different Dirichlet parameters  $(\alpha = 1,2,3)$ . When  $\alpha = 1.0$ , we consistently see around  $+0.2\%$  to  $+1.2\%$  improvement over the best baseline in maximum accuracy. For the collaborative fairness metric, our approach yields higher fairness scores, e.g.,  $98.93 \pm 0.14\%$  (POW/FashionMNIST) compared with  $96.53 \pm 1.20\%$  by FedSAC. This indicates that the performance gap among clients is smaller under our method, demonstrating better collaborative fairness.

# E COVARIATE SHIFT DATA GENERATION

In this section, we detail how to construct a covariate- shifted dataset from a baseline distribution with Algorithm 2. We assume the underlying global dataset  $\mathcal{D}_g$  approximately follows a single Gaussian distribution  $\mathcal{N}(\mu ,\Sigma)$ , from which we draw subsets for different clients. For each client, we shift the features by a fixed Mahalanobis distance.

As discussed in Theorem 2.2, under a large- sample limit, the Kullback- Leibler (KL) divergence can be used to measure how much the client- specific distribution  $\rho_{\phi}$  deviates from the baseline model  $\rho_{\omega}$ . By fixing  $\| \delta_k\|_{\Sigma^{- 1}}^2 = \delta_k^{\top}\Sigma^{- 1}\delta_k = C$  for each client, we ensure a controlled covariate shift in the mean. The covariance  $\Sigma$  remains the same, and the degree of shift is comparable across all clients. This construction thus provides a systematic way to generate imbalanced covariate shifts, enabling direct measurement and comparison of fairness or performance in federated learning experiments.

# F BASELINES

In this appendix, we present the details of the baseline methods used in our experiments. We classify them into two main categories and also include two additional traditional baselines: Standalone training and the classic FedAvg.

# Algorithm 2 Covariate Shift Data Generation

Require: Global labeled dataset  $\mathcal{D}_g = \{(x_i,y_i)\}_{i = 1}^{|D_g|}$ ; constant  $C > 0$ ; number of clients  $K$ ; desired sample counts  $\{n_k\}_{k = 1}^{K}$  (such that  $\sum_{k = 1}^{K}n_k = |\mathcal{D}_g| / 2|$ ); feature dimension  $d$ .

Ensure: Covariate- shifted client datasets  $\{\mathcal{D}_k\}_{k = 1}^{K}$ , where each  $\mathcal{D}_k$  retains the original labels but has shifted features (via importance sampling with respect to a Gaussian distribution with mean  $\mu_k$  and covariance  $\Sigma$ ).

1. Vectorize Features: Convert each feature  $x_{i}$  in  $\mathcal{D}_g$  into a vector in  $\mathbb{R}^d$ . Let  $\mathbf{X}$  be the resulting  $|\mathcal{D}_g|\times d$  matrix.

2. Estimate Baseline Gaussian:

$$
\mu = \frac{1}{|\mathcal{D}_g|}\sum_{i = 1}^{|\mathcal{D}_g|}x_i,\quad \Sigma = \mathrm{Cov}(\mathbf{X}).
$$

3. Half-sampling setup:

4. Let  $M = |\mathcal{D}_g| / 2|$ . We only use half of the global dataset for experiments. Hence, set  $\sum_{k = 1}^{K}n_k = M$ . Adjust  $\{n_k\}$  if needed. 
5. for  $k = 1,\ldots ,K$  do

6. Compute mean shift  $\delta_{k}$ : Sample a vector  $\delta_k\in \mathbb{R}^d$  such that  $\delta_k^\top \Sigma^{-1}\delta_k = C$ . 
7.  $\mu_k\leftarrow \mu +\delta_k$ $\vartriangleright$  Shifted mean for client  $k$ .

8. Construct Shifted Dataset for client  $k$ :

(1) Compute importance weights:

For each  $(x_{i},y_{i})$  in  $\mathcal{D}_g$ , compute:

$$
w_{i} = \exp \Bigl (-\frac{1}{2} (x_{i} - \rho_{k})^{\top}\Sigma^{-1}(x_{i} - \rho_{k})\Bigr).
$$

Then normalize:

$$
\tilde{\omega}_{i} = \frac{w_{i}}{\sum_{j = 1}^{|\mathcal{D}_{g}|}w_{j}}.
$$

(2) Sample from  $\mathcal{D}_g$  by  $\tilde{\omega}_{i}$

Sample  $n_k$  pairs  $(x_{i},y_{i})$  from  $\mathcal{D}_g$  with replacement according to probabilities  $\tilde{\omega}_{i}$ .

Store these sampled pairs in  $\mathcal{D}_k$ .

9. end for

10. return  $\{\mathcal{D}_k\}_{k = 1}^{K}$ .

Standalone and FedAvg. The first traditional baseline is Standalone, in which each client trains independently without any model aggregation. This approach ignores the potential benefits of federated collaboration, providing a lower- bound performance reference. Next, we employ FedAvg [15], the standard federated averaging method. FedAvg updates the global model by performing weighted averaging of the locally trained models from all clients, thereby enabling knowledge sharing while minimizing data exchange.

Collaborative Fairness Baselines. We next consider a set of baselines specifically aimed at improving collaborative fairness. CFFL [13] focuses on distributing rewards proportionally by measuring each client's contribution differences. In practice, it evaluates client updates on a held- out validation set (or an equivalent performance

Table 5: Comparison of fairness and accuracy for traditional non-IID setting experiments.

(a) Maximum Client Accuracy  $(\%)$  

<table><tr><td rowspan="2">Method</td><td colspan="5">FashionMNIST</td><td colspan="5">CIFAR10</td></tr><tr><td>POW</td><td>CLA</td><td>DIR(1.0)</td><td>DIR(2.0)</td><td>DIR(3.0)</td><td>POW</td><td>CLA</td><td>DIR(1.0)</td><td>DIR(2.0)</td><td>DIR(3.0)</td></tr><tr><td>Standalone</td><td>84.10±0.22</td><td>79.00±0.87</td><td>81.92±0.19</td><td>83.22±0.36</td><td>84.64±0.29</td><td>59.06±0.23</td><td>32.61±0.22</td><td>44.19±0.84</td><td>45.19±0.29</td><td>47.40±0.89</td></tr><tr><td>FedAvg</td><td>87.57±0.23</td><td>81.04±1.22</td><td>85.68±0.77</td><td>86.16±1.14</td><td>86.80±0.30</td><td>59.87±0.95</td><td>50.84±0.14</td><td>49.82±1.63</td><td>49.60±0.76</td><td>48.16±0.97</td></tr><tr><td>CFFL</td><td>83.78±0.56</td><td>85.88±0.82</td><td>86.90±0.19</td><td>87.09±0.08</td><td>87.44±0.27</td><td>58.41±1.15</td><td>42.89±1.22</td><td>48.28±1.72</td><td>42.15±0.93</td><td>44.21±1.27</td></tr><tr><td>CGSV</td><td>82.68±0.97</td><td>82.72±0.99</td><td>81.39±1.24</td><td>84.92±0.83</td><td>86.75±1.46</td><td>53.24±0.86</td><td>43.98±1.81</td><td>47.92±1.33</td><td>48.92±1.24</td><td>46.21±0.41</td></tr><tr><td>FedAVE</td><td>85.99±0.82</td><td>80.26±0.64</td><td>85.29±0.73</td><td>85.60±0.44</td><td>85.27±0.22</td><td>59.11±0.54</td><td>45.16±0.91</td><td>50.11±1.93</td><td>51.24±1.14</td><td>42.14±0.33</td></tr><tr><td>FedsAC</td><td>87.83±0.07</td><td>85.28±0.66</td><td>87.53±0.49</td><td>87.15±0.45</td><td>87.81±0.14</td><td>58.24±0.11</td><td>47.92±0.65</td><td>52.14±0.13</td><td>50.51±1.33</td><td>49.92±0.14</td></tr><tr><td>FedAKD</td><td>87.93±0.25</td><td>86.03±0.48</td><td>87.57±0.13</td><td>87.27±0.38</td><td>87.95±0.11</td><td>60.03±0.38</td><td>50.62±0.72</td><td>53.32±0.24</td><td>53.12±0.77</td><td>50.09±0.15</td></tr></table>

(b) Collaborative Fairness (CF) Coefficient  

<table><tr><td rowspan="2">Method</td><td colspan="4">FashionMNIST</td><td colspan="5">CIFAR10</td><td></td></tr><tr><td>POW</td><td>CLA</td><td>DIR(1.0)</td><td>DIR(2.0)</td><td>DIR(3.0)</td><td>POW</td><td>CLA</td><td>DIR(1.0)</td><td>DIR(2.0)</td><td>DIR(3.0)</td></tr><tr><td>FedAvg</td><td>16.78±21.51</td><td>83.23±6.53</td><td>20.18±16.52</td><td>23.24±27.33</td><td>29.00±31.87</td><td>18.84±6.83</td><td>86.83±4.82</td><td>32.77±6.43</td><td>27.92±8.52</td><td>50.73±10.39</td></tr><tr><td>CFFL</td><td>89.43±1.52</td><td>91.85±2.39</td><td>88.09±3.43</td><td>87.29±4.21</td><td>76.23±10.32</td><td>83.52±4.28</td><td>70.62±3.62</td><td>66.28±4.15</td><td>70.51±8.31</td><td>71.35±2.51</td></tr><tr><td>CGSV</td><td>90.87±0.98</td><td>86.63±2.89</td><td>92.43±0.64</td><td>91.29±1.93</td><td>87.53±2.34</td><td>90.15±3.37</td><td>94.26±0.82</td><td>89.27±2.05</td><td>91.74±0.45</td><td>93.63±1.37</td></tr><tr><td>FedAVE</td><td>94.21±1.41</td><td>89.42±0.44</td><td>93.22±1.28</td><td>90.34±0.33</td><td>94.34±0.98</td><td>93.64±0.35</td><td>96.25±1.87</td><td>95.85±1.86</td><td>98.03±0.13</td><td>90.62±1.69</td></tr><tr><td>FedsAC</td><td>96.53±1.20</td><td>93.45±1.75</td><td>97.31±0.83</td><td>95.78±0.89</td><td>94.34±2.93</td><td>99.05±0.24</td><td>95.63±0.51</td><td>93.54±0.99</td><td>95.73±1.37</td><td>94.73±0.97</td></tr><tr><td>FedAKD</td><td>98.93±0.14</td><td>95.13±1.83</td><td>99.27±0.23</td><td>97.87±0.45</td><td>98.82±0.42</td><td>99.78±0.08</td><td>97.35±1.20</td><td>97.05±0.21</td><td>98.86±0.72</td><td>97.76±1.59</td></tr></table>

benchmark) before deciding how to compensate high- versus low- contribution clients. On the other hand, CGSV [23] relies on gradient- based importance metrics- specifically using cosine similarities of gradients- so it does not require a separate validation set. Each client's contribution is gauged by comparing its gradient direction against the aggregated global gradient. Additionally, FedAVE [19] incorporates explicit fairness by maintaining a global validation set at the server. It periodically tests each client's model update on this validation set to compute a "reputation" score, which then guides how the final global model is aggregated. FedSAC [20] similarly uses a global validation set for evaluating each client's local update. Clients receive a proportionate "reward gradient" based on their evaluated performance, ensuring that clients with higher impact on the validation set obtain a larger share of the global update.

Personalized FL (Covariate Shift) Baselines. Since our approach also falls under the umbrella of Personalized Federated Learning (FL), we include baselines originally designed to tackle feature- level (covariate) shifts, even though they do not explicitly target collaborative fairness. FedDC [1] tackles non- IID data by introducing a drift variable that aligns local models more closely with the global model. FedAS [25] alleviates intra- and inter- client inconsistencies through federated parameter alignment and client synchronization. pFedCK [30] clusters clients by update similarity and performs mutual knowledge distillation between interactive and personalized models to enhance robustness under data heterogeneity. Lastly, FedMPR [2] combines iterative magnitude pruning with regularization techniques to improve robustness under highly heterogeneous client data distributions.

By comparing these diverse baselines, we can comprehensively evaluate our proposed method from both collaborative fairness and feature- level drift perspectives.

# G IMPLEMENTATION DETAILS

# G.1 Common Hyperparameters of All Algorithms Used in Simulation

Number of clients (NUM_CLIENTS) is set to 10. This indicates how many total clients are simulated or trained independently in the scenario. Global rounds (NUM_GLOBAL_ROUNDS) is commonly set to 20. This is the total number of federated communication rounds. Local epochs (LOCAL_EPOCHS) is often set to 1. It denotes how many epochs each client trains on its local data per global round. Batch size (BATCH_SIZE) is often set to 32. Learning rates (learning_rates) are set to 0.001 for FashionMNIST and 0.005 for CIFAR10. These control the local step size for optimizing SGD.

# G.2 Algorithm-Specific Hyperparameters

Table 6 details each algorithm's unique or particularly important hyperparameters, along with their default values (or ranges) and a brief explanation.

# H THE EHR DATASET

We begin with a real- world healthcare dataset composed of 17 tables. To simplify preprocessing and retain the most relevant information, we keep only: Patient Demographic Table, Diagnosis Table, Procedure Table, Medication Drug Table, Lab Result Table, and Vital Sign Table.

Data Processing. We merge these tables by patient_ID, ensuring each patient record contains both static features (e.g., zipcode, sex) and a series of events (medical codes plus numerical attributes, and the patient's age at each event). Because multiple medical coding systems are used across the U.S. healthcare spectrum, we unify these codes into a single standardized terminology.

Next, a board- certified medical oncologist provides a set of pancreatic cancer diagnostic codes, which are used to extract data labels.

Table 6: Algorithm-Specific Hyperparameters (Condensed)  

<table><tr><td>Algorithm</td><td>Special Hyperparameters</td><td>Value</td></tr><tr><td rowspan="4">CFFL</td><td>1) THETA_U (grad upload)</td><td>(1) 0.5</td></tr><tr><td>2) CLIP_NORM (clip thr.)</td><td>(2) 5.0</td></tr><tr><td>3) C_TH (rep. thr.)</td><td>(3) 0.05</td></tr><tr><td>4) ALPHA (rep. update)</td><td>(4) 1.0</td></tr><tr><td rowspan="4">CGSV</td><td>1) ALPHA_F (mov. avg)</td><td>(1) 0.9</td></tr><tr><td>2) BETA (sim. scaling)</td><td>(2) 2.0</td></tr><tr><td>3) SPARSITY</td><td>(3) True</td></tr><tr><td>4) ALTRUISM</td><td>(4) 1.0</td></tr><tr><td rowspan="3">FedAVE</td><td>1) UPLOAD_FRAC</td><td>(1) 0.5</td></tr><tr><td>2) DOWNLOAD_FRAC_BASE</td><td>(2) 0.3</td></tr><tr><td>3) ALPHA / BETA</td><td>(3) 0.9 / 1.0</td></tr><tr><td>FedAvg</td><td>-</td><td>-</td></tr><tr><td rowspan="2">FedDC</td><td>1) ALPHA (penalty)</td><td>(1) 1.0</td></tr><tr><td>2) drift_vars</td><td>(2) init=0</td></tr><tr><td>FedMPR</td><td>PRUNE_PERCENT</td><td>0.1</td></tr><tr><td>FedProx</td><td>MU (prox. coeff.)</td><td>0.01</td></tr><tr><td rowspan="2">FedSAC</td><td>1) BETA (c_i mapping)</td><td>(1) 2.0</td></tr><tr><td>2) mid_round</td><td>(2) 15</td></tr><tr><td rowspan="4">SCAFFOLD</td><td>1) ηg (global LR)</td><td>(1) 0.005</td></tr><tr><td>2) ηl (local LR)</td><td>(2) 0.1</td></tr><tr><td>3) K (local steps)</td><td>(3) 1</td></tr><tr><td>4) c_global, c_local</td><td>(4) init=0</td></tr><tr><td rowspan="3">FedAKD</td><td>1) Distill α</td><td>(1) 1.0</td></tr><tr><td>2) Distill β</td><td>(2) 1.0</td></tr><tr><td>3) Temp T</td><td>(3) 1.0</td></tr></table>

Table 7: State Data Statistics  

<table><tr><td>State</td><td>Total</td><td>Pos.</td><td>Neg.</td><td>State</td><td>Total</td><td>Pos.</td><td>Neg.</td></tr><tr><td>AK</td><td>558</td><td>196</td><td>362</td><td>MT</td><td>636</td><td>221</td><td>415</td></tr><tr><td>AL</td><td>3,410</td><td>1,292</td><td>2,118</td><td>NC</td><td>7,263</td><td>2,222</td><td>5,041</td></tr><tr><td>AR</td><td>2,341</td><td>842</td><td>4,499</td><td>ND</td><td>605</td><td>179</td><td>426</td></tr><tr><td>AZ</td><td>5,521</td><td>2,347</td><td>3,174</td><td>NE</td><td>1,429</td><td>424</td><td>1,005</td></tr><tr><td>CA</td><td>20,040</td><td>7,116</td><td>12,924</td><td>NH</td><td>900</td><td>344</td><td>556</td></tr><tr><td>CO</td><td>4,164</td><td>1,362</td><td>2,802</td><td>NJ</td><td>7,347</td><td>3,762</td><td>3,585</td></tr><tr><td>CT</td><td>2,595</td><td>1,180</td><td>1,415</td><td>NM</td><td>1,203</td><td>416</td><td>787</td></tr><tr><td>DE</td><td>805</td><td>342</td><td>463</td><td>NV</td><td>1,994</td><td>792</td><td>1,202</td></tr><tr><td>FL</td><td>18,898</td><td>8,158</td><td>10,740</td><td>NY</td><td>18,268</td><td>8,134</td><td>10,134</td></tr><tr><td>GA</td><td>7,901</td><td>2,461</td><td>5,440</td><td>OH</td><td>11,634</td><td>4,339</td><td>7,295</td></tr><tr><td>HI</td><td>1,060</td><td>433</td><td>627</td><td>OK</td><td>2,575</td><td>865</td><td>1,710</td></tr><tr><td>IA</td><td>2,951</td><td>1,093</td><td>1,858</td><td>OR</td><td>3,637</td><td>1,322</td><td>2,315</td></tr><tr><td>ID</td><td>1,021</td><td>361</td><td>660</td><td>PA</td><td>11,817</td><td>4,658</td><td>7,159</td></tr><tr><td>IL</td><td>8,932</td><td>3,497</td><td>5,440</td><td>RI</td><td>423</td><td>158</td><td>265</td></tr><tr><td>IN</td><td>4,388</td><td>1,523</td><td>2,865</td><td>SC</td><td>3,386</td><td>1,194</td><td>2,192</td></tr><tr><td>KS</td><td>1,857</td><td>524</td><td>1,333</td><td>SD</td><td>651</td><td>266</td><td>385</td></tr><tr><td>KY</td><td>4,110</td><td>1,396</td><td>2,714</td><td>TN</td><td>5,671</td><td>2,038</td><td>3,633</td></tr><tr><td>LA</td><td>3,316</td><td>1,150</td><td>2,166</td><td>TX</td><td>15,785</td><td>5,152</td><td>1,0633</td></tr><tr><td>MA</td><td>3,492</td><td>1,453</td><td>2,039</td><td>UT</td><td>2,117</td><td>495</td><td>1,622</td></tr><tr><td>MD</td><td>5,145</td><td>1,959</td><td>3,186</td><td>VA</td><td>6,057</td><td>1,924</td><td>4,133</td></tr><tr><td>ME</td><td>1,183</td><td>473</td><td>710</td><td>VT</td><td>445</td><td>150</td><td>295</td></tr><tr><td>MI</td><td>9,744</td><td>3,629</td><td>6,115</td><td>WA</td><td>6,247</td><td>2,009</td><td>4,238</td></tr><tr><td>MN</td><td>3,504</td><td>1,371</td><td>2,133</td><td>WI</td><td>2,893</td><td>1,281</td><td>1,612</td></tr><tr><td>MO</td><td>4,341</td><td>1,572</td><td>2,769</td><td>WV</td><td>1,712</td><td>617</td><td>1,095</td></tr><tr><td>MS</td><td>2,246</td><td>710</td><td>1,536</td><td>WY</td><td>358</td><td>113</td><td>245</td></tr></table>

In ICD- 10, the codes for malignant neoplasm of the pancreas include:

C25 (Malignant neoplasm of pancreas, general), C25.0 (Malignant neoplasm of head of pancreas), C25.1 (Malignant neoplasm of body of pancreas), C25.2 (Malignant neoplasm of tail of pancreas), C25.3 (Malignant neoplasm of pancreatic duct), C25.4 (Malignant neoplasm of endocrine pancreas), C25.7 (Malignant neoplasm of other specified parts of pancreas), C25.8 (Malignant neoplasm of overlapping lesions of pancreas), C25.9 (Malignant neoplasm of pancreas, unspecified).

In ICD- 9, the corresponding codes are:

157 (Malignant neoplasm of pancreas, general), 157.0 (Malignant neoplasm of head of pancreas), 157.1 (Malignant neoplasm of body of pancreas), 157.2 (Malignant neoplasm of tail of pancreas), 157.3 (Malignant neoplasm of pancreatic duct), 157.4 (Malignant neoplasm of islets of Langerhans), 157.8 (Malignant neoplasm of other specified sites of pancreas), 157.9 (Malignant neoplasm of pancreas, unspecified).

For each patient, if any of these codes appear in the longitudinal record, we set the label  $y = 1$  ; furthermore, we remove any events that occur at or after the time of pancreatic cancer diagnosis to prevent data leakage. If none of the pancreatic cancer codes appear for a patient, we set that patient's label to  $y = 0$

After these preprocessing steps, we obtain 265,085 de- identified patient samples spanning 50 U.S. states.4 We randomly sample  $10\%$  (26,509) of this dataset as a global validation set. The remaining  $90\%$  of the data is returned to each state, where each state splits its local data into a local training set and a local test set (e.g. 7:2). The binary label  $y$  indicates whether the patient eventually develops pancreatic cancer. Table 7 summarizes, for each of the 50 states, the number of local samples, how many are used for training/testing, and the distribution of positive/negative labels. Globally, we observe 99,604 positive and 165,481 negative samples from local total data(train and test).

Model and Training Setup. To handle this longitudinal EHR data, we embed the event codes, concatenate them with numerical attributes (e.g. age, value), and feed the sequence into a two- layer bidirectional GRU, which captures both forward and backward dependencies. We then apply an attention mechanism over the GRU outputs, computing importance weights for each time step and aggregating them into a context vector. This vector is concatenated with the embedded static features (sex, postal_code), and a linear classifier predicts whether the patient will develop pancreatic cancer.

We adopt a focal loss [12] to mitigate label imbalance and train with an Adam optimizer (default PyTorch settings). The mini- batch size is set to  $B = 64$  GRU hidden dimension to 256, dropout probability to 0.3, and learning rate to  $1r = 0.00001$  .We embed sex and postal_code into vectors of dimensions 8 and 16, respectively, zero- padding time- series inputs (with sequence lengths tracked via pack_padded_sequence). Specifically, the two- layer GRU produces a  $2\times 256$  - dim vector per time step, mapped by the attention layer to a 256- dim energy vector and finally to scalar scores for

softmax weighting. Ultimately, we train and validate our model in this federated setting, treating each state as one client  $(K = 50)$ .