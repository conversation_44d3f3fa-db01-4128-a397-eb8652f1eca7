# Related Work / 相关工作

## English Version

### Federated Learning / 联邦学习

Federated learning was first introduced by <PERSON><PERSON><PERSON><PERSON> et al. (2017) as a distributed machine learning paradigm that enables collaborative model training without centralizing data. The foundational FedAvg algorithm aggregates local model updates through weighted averaging, providing a simple yet effective approach for federated optimization. Since then, numerous extensions have been proposed to address various challenges in federated learning.

To handle data heterogeneity, <PERSON> et al. (2020) proposed FedProx, which introduces a proximal term to the local objective function to reduce client drift. <PERSON><PERSON><PERSON><PERSON> et al. (2020) developed SCAFFOLD, which uses control variates to correct for client drift and achieve better convergence. <PERSON> et al. (2020) presented FedNova, which normalizes the magnitude of model updates to handle varying local training steps across clients.

Privacy preservation in federated learning has been extensively studied. <PERSON><PERSON> et al. (2017) applied differential privacy to federated learning by adding noise to model updates. <PERSON><PERSON><PERSON> et al. (2017) proposed secure aggregation protocols using cryptographic techniques to protect individual client updates during aggregation. More recently, <PERSON> et al. (2020) developed federated learning with formal privacy guarantees through advanced differential privacy mechanisms.

Security aspects of federated learning have also received significant attention. <PERSON><PERSON><PERSON><PERSON><PERSON> et al. (2020) demonstrated model poisoning attacks where malicious clients can compromise the global model. <PERSON><PERSON><PERSON><PERSON> et al. (2019) analyzed the impact of data poisoning in federated settings. Defense mechanisms include robust aggregation methods (<PERSON> et al. 2018) and Byzantine-resilient algorithms (<PERSON><PERSON> et al. 2017).

### Multimodal Learning / 多模态学习

Multimodal learning aims to leverage information from multiple data modalities to improve model performance and robustness. Early work focused on simple fusion strategies such as early fusion (feature-level) and late fusion (decision-level) (Snoek et al. 2005). More sophisticated approaches include attention-based fusion mechanisms (Xu et al. 2015) and cross-modal learning techniques (Ngiam et al. 2011).

Recent advances in deep learning have enabled more complex multimodal architectures. Transformer-based models have shown remarkable success in multimodal tasks, with architectures like ViLBERT (Lu et al. 2019) and CLIP (Radford et al. 2021) achieving state-of-the-art performance in vision-language tasks. In the medical domain, multimodal approaches have been applied to various tasks including medical image analysis (Chen et al. 2024), clinical decision support (Zhang et al. 2023), and drug discovery (Wang et al. 2023).

However, multimodal learning also introduces unique challenges. Modal imbalance, where different modalities contribute unequally to the learning process, can lead to suboptimal performance (Wang et al. 2022). Missing modalities during training or inference pose additional difficulties (Ma et al. 2021). Cross-modal attacks, where adversarial perturbations in one modality affect predictions across modalities, represent emerging security concerns (Li et al. 2023).

### Multimodal Federated Learning / 多模态联邦学习

The intersection of federated learning and multimodal learning has gained increasing attention. Early work by Yang et al. (2021) explored federated learning for multimodal sentiment analysis, demonstrating the potential benefits of combining these paradigms. Liu et al. (2022) proposed FedMM, a framework for federated multimodal learning that addresses modality heterogeneity across clients.

Recent advances have focused on addressing specific challenges in multimodal federated settings. Gai et al. (2025) introduced AproMFL, which uses adaptive prototype-based knowledge transfer for mixed modalities and heterogeneous tasks. Hou et al. (2025) developed SecFPP, a secure federated prompt personalization protocol for multimodal large language models. Wang et al. (2025) proposed FedMobile, which handles incomplete modalities through knowledge contribution-aware learning.

Privacy and security concerns in multimodal federated learning have been highlighted by recent work. Shi et al. (2024) demonstrated MedLeak, showing how malicious servers can recover private medical data from multimodal federated learning systems. Tan et al. (2025) presented VTarbel, a targeted label attack against vertical federated learning with multimodal data.

### Trustworthy AI / 可信人工智能

Trustworthy AI encompasses multiple dimensions including privacy, security, fairness, explainability, and robustness. Privacy-preserving techniques include differential privacy (Dwork et al. 2014), homomorphic encryption (Gentry 2009), and secure multi-party computation (Yao 1986). Security research focuses on adversarial robustness (Goodfellow et al. 2015), model poisoning detection (Chen et al. 2017), and backdoor defense (Wang et al. 2019).

Fairness in machine learning has been extensively studied, with various definitions and metrics proposed (Dwork et al. 2012; Hardt et al. 2016). Explainable AI techniques aim to provide interpretable insights into model decisions through methods such as LIME (Ribeiro et al. 2016), SHAP (Lundberg & Lee 2017), and attention visualization (Xu et al. 2015).

In the healthcare domain, trustworthy AI faces additional challenges due to the sensitive nature of medical data and the high stakes of clinical decisions. Rajkomar et al. (2018) discussed the importance of fairness in healthcare AI, while Ghassemi et al. (2021) highlighted the need for robust evaluation methods. Recent work has focused on developing comprehensive frameworks for trustworthy medical AI (Chen et al. 2023; Liu et al. 2024).

### Gaps in Current Research / 当前研究的空白

Despite significant progress in individual areas, several critical gaps remain in the intersection of federated learning, multimodal learning, and trustworthy AI:

**Lack of Unified Frameworks**: Existing approaches typically address trustworthiness dimensions in isolation, lacking comprehensive frameworks that consider the complex interactions between privacy, security, fairness, and explainability in multimodal federated settings.

**Limited Cross-modal Security Analysis**: While security vulnerabilities in federated learning and multimodal systems have been studied separately, there is insufficient research on cross-modal attacks and defenses in federated environments.

**Inadequate Dynamic Adaptation**: Current methods often use static approaches that do not adapt to changing trustworthiness conditions during federated training, limiting their effectiveness in real-world deployments.

**Domain-specific Challenges**: Healthcare applications present unique requirements that are not adequately addressed by general-purpose trustworthy AI solutions, including regulatory compliance, clinical workflow integration, and medical data characteristics.

Our work addresses these gaps by proposing TrustMFL, a comprehensive framework that unifies multiple trustworthiness dimensions in multimodal federated learning specifically designed for healthcare applications.

## Chinese Version / 中文版本

### 联邦学习

联邦学习最初由McMahan等人（2017）提出，作为一种分布式机器学习范式，能够在不集中数据的情况下实现协作模型训练。基础的FedAvg算法通过加权平均聚合本地模型更新，为联邦优化提供了简单而有效的方法。从那时起，已经提出了许多扩展来解决联邦学习中的各种挑战。

为了处理数据异构性，Li等人（2020）提出了FedProx，它在本地目标函数中引入了近端项以减少客户端漂移。Karimireddy等人（2020）开发了SCAFFOLD，使用控制变量来纠正客户端漂移并实现更好的收敛。Wang等人（2020）提出了FedNova，它标准化模型更新的幅度以处理客户端间不同的本地训练步骤。

联邦学习中的隐私保护已被广泛研究。Geyer等人（2017）通过向模型更新添加噪声将差分隐私应用于联邦学习。Bonawitz等人（2017）提出了使用密码学技术的安全聚合协议，以在聚合期间保护个别客户端更新。最近，Wei等人（2020）通过先进的差分隐私机制开发了具有正式隐私保证的联邦学习。

联邦学习的安全方面也受到了重大关注。Bagdasaryan等人（2020）演示了模型投毒攻击，恶意客户端可以破坏全局模型。Bhagoji等人（2019）分析了联邦环境中数据投毒的影响。防御机制包括鲁棒聚合方法（Yin等人2018）和拜占庭容错算法（Blanchard等人2017）。

### 多模态学习

多模态学习旨在利用来自多个数据模态的信息来改善模型性能和鲁棒性。早期工作专注于简单的融合策略，如早期融合（特征级）和后期融合（决策级）（Snoek等人2005）。更复杂的方法包括基于注意力的融合机制（Xu等人2015）和跨模态学习技术（Ngiam等人2011）。

深度学习的最新进展使得更复杂的多模态架构成为可能。基于Transformer的模型在多模态任务中表现出显著成功，如ViLBERT（Lu等人2019）和CLIP（Radford等人2021）等架构在视觉-语言任务中实现了最先进的性能。在医疗领域，多模态方法已应用于各种任务，包括医学图像分析（Chen等人2024）、临床决策支持（Zhang等人2023）和药物发现（Wang等人2023）。

然而，多模态学习也引入了独特的挑战。模态不平衡，即不同模态对学习过程的贡献不相等，可能导致次优性能（Wang等人2022）。训练或推理期间的缺失模态带来了额外的困难（Ma等人2021）。跨模态攻击，即一个模态中的对抗扰动影响跨模态的预测，代表了新兴的安全问题（Li等人2023）。

### 多模态联邦学习

联邦学习和多模态学习的交集已获得越来越多的关注。Yang等人（2021）的早期工作探索了多模态情感分析的联邦学习，展示了结合这些范式的潜在好处。Liu等人（2022）提出了FedMM，一个解决客户端间模态异构性的联邦多模态学习框架。

最近的进展专注于解决多模态联邦环境中的特定挑战。Gai等人（2025）引入了AproMFL，它使用自适应基于原型的知识迁移来处理混合模态和异构任务。Hou等人（2025）开发了SecFPP，一个用于多模态大语言模型的安全联邦提示个性化协议。Wang等人（2025）提出了FedMobile，通过知识贡献感知学习处理不完整模态。

最近的工作突出了多模态联邦学习中的隐私和安全问题。Shi等人（2024）演示了MedLeak，展示了恶意服务器如何从多模态联邦学习系统中恢复私人医疗数据。Tan等人（2025）提出了VTarbel，一种针对具有多模态数据的垂直联邦学习的目标标签攻击。

### 可信人工智能

可信人工智能包含多个维度，包括隐私、安全、公平性、可解释性和鲁棒性。隐私保护技术包括差分隐私（Dwork等人2014）、同态加密（Gentry 2009）和安全多方计算（Yao 1986）。安全研究专注于对抗鲁棒性（Goodfellow等人2015）、模型投毒检测（Chen等人2017）和后门防御（Wang等人2019）。

机器学习中的公平性已被广泛研究，提出了各种定义和指标（Dwork等人2012；Hardt等人2016）。可解释AI技术旨在通过LIME（Ribeiro等人2016）、SHAP（Lundberg & Lee 2017）和注意力可视化（Xu等人2015）等方法为模型决策提供可解释的见解。

在医疗领域，由于医疗数据的敏感性和临床决策的高风险，可信AI面临额外挑战。Rajkomar等人（2018）讨论了医疗AI中公平性的重要性，而Ghassemi等人（2021）强调了鲁棒评估方法的需求。最近的工作专注于开发可信医疗AI的综合框架（Chen等人2023；Liu等人2024）。

### 当前研究的空白

尽管在各个领域取得了重大进展，但在联邦学习、多模态学习和可信AI的交集中仍存在几个关键空白：

**缺乏统一框架**：现有方法通常孤立地解决可信性维度，缺乏考虑多模态联邦环境中隐私、安全、公平性和可解释性之间复杂交互的综合框架。

**有限的跨模态安全分析**：虽然联邦学习和多模态系统中的安全漏洞已被分别研究，但对联邦环境中跨模态攻击和防御的研究不足。

**不充分的动态适应**：当前方法通常使用静态方法，不能适应联邦训练期间变化的可信性条件，限制了它们在真实世界部署中的有效性。

**领域特定挑战**：医疗应用呈现出通用可信AI解决方案无法充分解决的独特需求，包括法规合规、临床工作流集成和医疗数据特征。

我们的工作通过提出TrustMFL来解决这些空白，这是一个专门为医疗应用设计的在多模态联邦学习中统一多个可信性维度的综合框架。
