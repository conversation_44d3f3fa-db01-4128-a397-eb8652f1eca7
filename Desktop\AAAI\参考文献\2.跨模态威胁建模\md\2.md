# A Client-level Assessment of Collaborative Backdoor Poisoning in Non-IID Federated Learning

\*Phung Lai University at Albany Albany, New York, USA <EMAIL>

\*<PERSON><PERSON><PERSON><PERSON> Liu Meta Inc. Menlo Park, California, USA <EMAIL>

NhatHai Phan New Jersey Institute of Technology Newark, New Jersey, USA <EMAIL>

<PERSON><PERSON> Qatar Computing Research Institute Doha, Qatar <EMAIL>

Abdallah Khreishah New Jersey Institute of Technology Newark, New Jersey, USA <EMAIL>

Xintao Wu University of Arkansas Fayetteville, Arkansas, USA <EMAIL>

Abstract- Federated learning (FL) enables collaborative model training using decentralized private data from multiple clients. While FL has shown robustness against poisoning attacks with basic defenses, our research reveals new vulnerabilities stemming from non- independent and identically distributed (non- IID) data among clients. These vulnerabilities pose a substantial risk of model poisoning in real- world FL scenarios.

To demonstrate such vulnerabilities, we develop a novel collaborative backdoor poisoning attack called COLLAPOIS. In this attack, we distribute a single pre- trained model infected with a Trojan to a group of compromised clients. These clients then work together to produce malicious gradients, causing the FL model to consistently converge towards a low- loss region centered around the Trojan- infected model. Consequently, the impact of the Trojan is amplified, especially when the benign clients have diverse local data distributions and scattered local gradients. COLLAPOIS stands out by achieving its goals while involving only a limited number of compromised clients, setting it apart from existing attacks. Also, COLLAPOIS effectively avoids noticeable shifts or degradation in the FL model's performance on legitimate data samples, allowing it to operate stealthily and evade detection by advanced robust FL algorithms.

Thorough theoretical analysis and experiments conducted on various benchmark datasets demonstrate the superiority of COLLAPOIS compared to state- of- the- art backdoor attacks. Notably, COLLAPOIS bypasses existing backdoor defenses, especially in scenarios where clients possess diverse data distributions. Moreover, the results show that COLLAPOIS remains effective even when involving a small number of compromised clients. Notably, clients whose local data is closely aligned with compromised clients experience higher risks of backdoor infections.

Index Terms- Federated Learning, Backdoor Attack, Non- IID.

# I. INTRODUCTION

Recent and emerging data privacy regulations [1] challenge machine learning (ML) applications that collect sensitive user data on centralized servers. Federated learning (FL) [2] addresses this by enabling collaborative model training without sharing raw data. However, FL often faces performance disparities due to diverse data distributions across clients [3].

To address this challenge, personalization in FL has gained considerable attention. Personalization allows each client's model to adjust to its own unique data distribution, and various methods have been proposed to achieve this [4], [5]. In contrast to conventional FL methods, personalized FL methods are more suitable for individual clients, especially in situations where there are significant variations in data distributions across clients. In real- world scenarios, clients often employ FL at different geographical locations or serve different user cohorts [6], [7]. This often results in significant variations in data distribution, making personalized FL a promising choice.

FL has been extensively studied to identify potential adversarial and Trojan vulnerabilities [8]- [11], given its significance and widespread usage. Despite this, recent research (drawing on various sources) [12] surprisingly shows that FL remains relatively resilient to previously reported poisoning attacks, even when utilizing low- cost robust training algorithms. However, commonly employed aggregate metrics in previous research, such as the average accuracy across all clients, do not sufficiently reflect the individualized impact of proposed attacks or defenses on each client. This is concerning because a high average accuracy may mask unacceptably low accuracy levels for certain clients, particularly if there is substantial variation in their individual accuracy, which may lead to an underestimation of FL vulnerabilities. Ensuring a well- balanced client- level accuracy is of utmost importance as clients actively participate in FL with the expectation of achieving good performance. Consequently, an attack that substantially impacts the performance of a small subset of clients can pose a significant threat to the entire FL system. The high diversity in data distribution among clients, which is a key feature of FL, becomes even more pronounced when coupled with personalization techniques. As a result, it becomes imperative to thoroughly assess the effects of attacks and defenses on individual clients. Concretely, we seek to answer the question: How many clients are impacted, which ones, and to what extent in terms of attack success rates, and what are the underlying reasons for these impacts?

Key Contributions. To close these gaps, we introduce a novel Trojan attack called CoLLAPoIs, with the goal of systematically elucidating the connection between the risk of backdoor poisoning and the degree of diversity present in the local data distributions of clients in the context of FL.

In contrast to existing backdoor attacks, CoLLAPoIs leverages the diverse local data distributions of benign clients and the resulting scattered gradients to steer the federated training model towards a pre- trained Trojaned model  $X$  .As a result, backdoors can be transferred to benign clients' local models through the Trojan- infected federated training model.

To achieve this, we adopt a coordinated approach involving a group of compromised clients. With this approach, we generate well- aligned malicious gradients, in stark contrast to the scattered gradients contributed by the benign clients. This compels the federated training model to converge within a tightly confined region surrounding the Trojaned model  $X$  effectively poisoning the federated training model.

Our extensive theoretical and empirical analysis show that CoLLAPoIs can significantly lower and bound the number of compromised clients required to successfully carry out backdoor poisoning as a function of the attack stealthiness and the degree of diversity in benign clients' local data distribution. We refer to stealthiness as the ability to prevent the server from approximating the Trojaned model  $X$  and identifying compromised clients. In fact, the greater the diversity in benign clients' local data, the fewer compromised clients are required and the more stealthy the successful poisoning becomes, and vice- versa. This establishes a new theoretical connection among these three key components in FL. The novelty of CoLLAPoIs also stems from its Simplicity, as it involves only minor adjustments to classical poisoning techniques in FL [13], [14]. These modifications do not necessitate any additional knowledge in the federated training process. Moreover, its Efficiency makes it cost- effective for compromised clients to compute malicious local gradients during a training round. Consequently, CoLLAPoIs becomes a practical method to expose backdoor risks in FL.

Through comprehensive assessments with client- level metrics, we show that CoLLAPoIs outperforms existing attacks and adeptly evades various robust federated training and defense algorithms, with and without personalization. Even with only a modest  $0.5\%$  of compromised clients, CoLLAPoIs can effectively create a backdoor for  $15\%$  of benign clients with an impressive success rate exceeding  $70\%$  under robust federated training on benchmark datasets.

# II. BACKGROUND AND RELATED WORK

# A. Federated Learning and Personalization

Federated Learning (FL). FL is a multi- round protocol between a server and  $N$  devices. At each round  $t$  the server sends the global model  $\theta^t$  to a random subset  $M^t$  which trains local models  $\theta_u^t$  on data  $D_{u}$  and returns them. The server aggregates these updates using a function  $\mathcal{G}$  to produce  $\theta^{t + 1} = \mathcal{G}(\theta_u^{t + 1}{}_{u\in M^t})$  . FedAvg is a commonly used aggregation method [15].

Non- IID Data in FL. One of the fundamental challenges that could impair performance and may introduce vulnerabilities in FL models is the non- independent and identically distributed (non- IID) clients' local data [15], [16]. Non- IID data is characterized by notable distinctions in the local data distributions of clients. In this study, we investigate label distribution skew as one prevalent form of non- IID data [15]. Similar to earlier research [17], we assume that the distribution of the number of data samples per class (label) within each client adheres to a symmetric Dirichlet distribution denoted as  $Dir(\alpha)$ , in which the concentration parameter  $\alpha$  controls the degree of non- IIDness. Values of  $\alpha$  above 1 favor dense and evenly distributed classes, while values below 1 favor diverse and sparse distributions with data concentrated in fewer classes. Throughout the remainder of this study, "diversity" refers to the non- IID level of clients' local data distribution.

Personalized FL. Personalized Federated Learning (PFL) approaches address non- IID challenges by adapting performance to individual clients' local data distributions [4], [18]. PFL approaches can be broadly categorized into four main research directions: (1) Regularization- based approaches apply penalties to local training to address data distribution drift and reduce discrepancies between local and global model weights [18], [19], (2) Knowledge distillation allows the server to combine clients' knowledge via a generator or consensus, enabling clients to use this knowledge as an inductive bias or to train local models using public and private data [4], [20], (3) Clustering- based frameworks assign clients to clusters and aggregate local models within each cluster [21], [22], and (4) Meta- Learning leverages the concept of meta- training and meta- testing. In meta- training, a flexible initial model is learned using methods like Model Agnostic Meta- Learning, enabling quick adaptation to various tasks. This model maps to the global model, which is then fine- tuned for specific tasks during meta- testing on the client's side.

In this study, we focus on regularization and knowledge distillation- based FL approaches, as they generalize to clustering and meta- learning methods. These methods allow FL models learned for client clusters to adapt to various tasks.

# B. Backdoor Attacks

This section reviews the threat model of FL poisoning attacks. We first introduce attack knowledge and attack capability to categorize the attacks discussed.

Attack Knowledge. Attack knowledge refers to an attacker's awareness of other clients' information. While- box knowledge means knowing other clients' updates, while blackbox one implies no such access, making it more practical.

Attack Capability. We classify attack capability into partial capability, where the attacker can only inject poisoned data into a subset of clients' training sets, and full capability, where the attacker can control the entire subset of clients (referred to as compromised clients and manipulate their training process at will  $\mathcal{C}$ ). The attacker with full capability can control compromised clients to coordinate poisoning attacks. Both attackers are practical in FL [12].

![](images/3f6c930b5c9ea3b74ca8ab0fcd2d5e5727df9ce57164b81e668409fdca2e302c.jpg)  
Fig. 1: DPOIS and MREPL attacks show modest changes, with  $0.1\%$  and  $1\%$  compromised clients across distribution levels.

Backdoor Attacks. This work focuses on backdoor attacks that misclassify specific inputs while maintaining model accuracy on legitimate data. Trojans have emerged as a prominent method for conducting backdoor attacks, as highlighted in previous studies [23], [24]. Trojans involve carefully embedding a specific pattern, such as a brand logo or additional pixels, into legitimate data samples to induce the desired misclassification. Recently, an image warping- based Trojan has been developed, which subtly distorts an image using geometric transformations [25]. This technique makes modifications imperceptible to human observers. Importantly, warping techniques enable Trojans to evade commonly used detection methods like Neural Cleanse [26], Fine- Pruning [27], and STRIP [28]. The attacker activates the backdoor during the inference phase by applying this Trojan trigger to legitimate data samples. In this study, we employ the warping- based Trojan technique [25].

In FL, backdoor attacks involve compromised clients controlled by an attacker to create malicious gradients before sending them to the server. The attacker can apply data poisoning (DPOIS) and model replacement (MREPL) approaches to generate malicious local gradients. In DPOIS [13], [14], compromised clients train on Trojaned datasets to generate malicious local gradients, causing the aggregated model to exhibit backdoor effects. In MREPL [9], adversaries create malicious local gradients to partially or entirely replace the server's aggregated model with a Trojaned model, even after a single training round [29]. However, MREPL often causes noticeable performance shifts [9], making detection easier by monitoring abrupt changes across training rounds.

DPOIS and MREPL were not designed to exploit non- IID data for more effective poisoning in FL. Current methods lack client- level risk insights, threatening FL integrity.

# C. Defenses against Backdoor Attacks in FL

Current defense mechanisms against backdoor poisoning attacks in FL can be classified into two categories: (1) Detection of Trojans during inference [26], [28], [30], [31] by identifying or decomposing poisoned samples to clean inputs and triggers. These approaches typically demand computing- intensive resources, given their high computational complexity. This expensive computation overhead hinders their applicability to clients with limited resources in federated learning; (2) Poisoned update detection [22] by checking difference between malicious and benign gradients using statistical tests; and (3) Resilient gradient aggregation to reduce the impact of malicious local gradients during the federated training process [32]- [34]. Unlike the first category, the other approaches are efficient and do not incur extra computational costs for clients in FL. Therefore, when discussing defense mechanisms against our attack, we refer to methods that can ensure a robust aggregation process in FL and PFL, effectively preventing backdoor Trojans from being transferred to benign clients.

Robust Federated Training. Table I (Supplementary) summarizes various robust federated training approaches. They include coordinate- wise median, geometric median,  $\alpha$ - trimmed mean, as well as their variants and combinations, as outlined in the literature [32]. Recently proposed approaches include weight- clipping and noise addition with certified bounds, ensemble models, differential privacy (DP) optimizers, and adaptive and robust learning rates (RLR) across clients and at the server [33], [34]. Despite differences, existing robust aggregation focuses on analyzing and manipulating the local gradients  $\Delta \theta_{i}^{t}$ , which share the global aggregated model  $\theta_{t}$  as the same root, i.e.,  $\forall i \in [N]: \Delta \theta_{i}^{t} = \theta_{i}^{t} - \theta^{t}$ . The fundamental assumption in these approaches is that the local gradients from compromised clients  $\{\Delta \theta_{c}^{t}\}_{c \in \mathcal{C}}$  and from legitimate clients  $\{\Delta \theta_{i}^{t}\}_{i \in N \setminus c}$  are different in terms of magnitude and direction.

# III. NUMBERS OF COMPROMISED CLIENTS AND NON-IID

Our first effort to draw the correlation between the number of compromised clients and non- iid data in backdoor poisoning attacks is launching DPOIS and MREPL attacks in the Sentiment dataset [35], which has 5,600 clients and 1 million samples. Data distribution across clients follows a symmetric Dirichlet distribution with concentration parameter  $\alpha \in [0.01, 100]$ . We conduct experiments with  $0.1\%$  and  $1\%$  compromised clients under varying non- IID levels, i.e.,  $\alpha \in [0.01, 100]$ . As shown in Fig. 1, there are modest changes observed between  $0.1\%$  and  $1\%$  compromised clients across different levels of data distribution, i.e.,  $\alpha \in [0.01, 100]$ , in existing DPOIS and MREPL attacks. This result highlights a gap in understanding the correlation between non- IID data distribution, attack stealthiness, and attack effectiveness, motivating our systematic study of backdoor risks in FL under non- IID data from theoretical and empirical angles.

# IV. COLLABORATIVE POISONING ATTACKS

# A. Threat Model

We consider black- box poisoning carried out by an attacker with full capability in FL as in Section II- B. Fig. 2 shows our threat model. The server is honest and strictly follows the federated training protocol and there is no collusion between the server and the attacker. This threat model is crucial for servers and service providers aiming to maximize client model utility, as poisoning can degrade performance, reducing incentives to harm service quality.

The attacker fully controls a set of compromised clients  $\mathcal{C}$  participating in the federated training. The literature considers this attacker as a practical threat when the complement set

![](images/90c31a77d63950e9367d66ce2991f6a58dc473e8ab6f576ca3ade9c2d2d9d602.jpg)  
Fig. 2: CollaPois framework. In each training round, compromised clients send malicious gradients (red-solid) to steer the FL model  $\theta$  toward a Trojaned model  $X$  sent by the attacker (red-dashed). Dashed and solid vectors indicate one-time and multiple training rounds, respectively.

size,  $|\mathcal{C}|$ , is small, e.g.,  $0.01 - 10\%$  of clients [12]. The attacker has access to an auxiliary dataset, referred to as  $D_{a}$ , which is composed of local datasets collected by the compromised clients, denoted as  $D_{a} = \cup_{c\in \mathcal{C}}D_{c}$ .  $D_{a}$  shares the same downstream task with benign clients.

The attacker's objective is to manipulate the federated training process by transmitting malicious local gradients through compromised clients to the server, producing backdoored local models that deviate from clean local models in benign clients. An optimal backdoored model behaves identically to a clean model for legitimate inputs but provides a prediction of the attacker's choosing when the input contains a backdoor trigger, such as a Trojan [25]. The attack is more effective as more benign clients are impacted by backdoors, increasing success rates without reducing model utility on legitimate inputs. It also becomes stealthier and more practical when fewer compromised clients are needed, making detection harder.

# B. Collaborative Poisoning (COLLAPOIS)

Existing backdoor attacks have not been designed to exploit scattered gradients that arise due to the non- IID characteristics of clients' data in FL. In contrast to existing attacks, COLLAPOIS investigates model integrity risks caused by scattered gradients and demonstrates how these scattered gradients can be harnessed for potent attack strategies.

The pseudo- code of COLLAPOIS is in Algorithm 1. First, the attacker poisons the auxiliary data  $D_{a}$  by embedding a backdoor trigger (Trojan) into the data samples and changing their labels to match the attacker's desired prediction. This manipulation results in a collection of perturbed data samples, denoted as  $D_{a}^{Troj}$ , which the attacker employs to train a Trojaned model  $X$  using a centralized approach (Line 3). The training process minimizes the following objective loss:

$$
X = \arg \min_{\theta_a}L(\theta_a,D_a\cup D_a^{Troj}), \tag{1}
$$

where  $\theta_{a}$  is the attacker's model used to train  $X$ , sharing the same structure as the global model  $\theta$ , as the attacker learns its structure through the compromised clients.

# Algorithm 1 Collaborative Poisoning Attack (COLLAPOIS)

1: Input: Number of compromised clients  $|\mathcal{C}|$ , number of benign clients  $|N| - |\mathcal{C}|$ , client sampling probability  $q$ , number of rounds  $T$ , number of local rounds  $K$ , server's learning rates  $\lambda$ , clients' learning rate  $\gamma$ , a random and dynamic learning rate  $\psi \sim \mathcal{U}[a,b]$ , and  $L_{i}(B)$  is the loss function  $L_{i}(\theta)$  on a mini- batch  $B$  2: Output:  $\theta$  3: Attacker trains a Trojaned model  $X = \arg \min_{\theta_{a}}L(\theta_{a},D_{a}\cup D_{a}^{Troj})$  where  $\theta_{a}$  has the same structure as the model  $\theta$  4: for  $t = 1,\ldots ,T$  do 5:  $S_{t}\gets$  Sample a set of users with a probability  $q$  6: for each legitimate client  $i\in S^t\backslash \mathcal{C}$  do 7: set  $\theta_t^t = \theta^t$  # where  $\theta^1$  is randomly initialized 8: for  $k = 1,\ldots ,K$  do 9: sample mini- batch  $B\subset D_{i}$  10:  $\theta_{i}^{k + 1} = \theta_{i}^{k} - \gamma \bigvee_{\theta_{i}^{k}}L_{i}(B)$  11:  $\triangle \theta_{i}^{t}\leftarrow \theta_{i}^{K} - \theta_{i}^{t}$  12: for each compromised client  $c\in S^t\cap \mathcal{C}$  do 13:  $\begin{array}{rl} & {\triangle \theta_{i}^{t}\leftarrow \left(\psi_{c}^{t}\sim \mathcal{U}[a,b]\right)\left[X - \theta^{t}\right]}\\ & {\theta^{t + 1}\leftarrow \theta^{t} - \lambda \left[\sum_{i\in S^{t}\backslash \mathcal{C}}\triangle \theta_{i}^{t} + \sum_{c\in S^{t}\cap \mathcal{C}}\triangle \theta_{c}^{t}\right] / |S^{t}|} \end{array}$  14:

In this study, the attacker uses WaNet [25], which is one of the state- of- the- art backdoor attacks, for generating the poisoned image data  $D_{a}^{Troj}$ . WaNet uses image warping- based triggers to create natural and unnoticeable backdoor modifications. Following [25], backdoor images are generated to train the Trojaned model  $X$  (samples in Fig. 14, Supplementary). For the text data used in the evaluation, we follow existing Trojan attack [36], which use a fixed term as the trigger.

Employing the Trojaned model  $X$  shared by the attacker, the compromised clients  $\mathcal{C}$  compute their malicious local gradients as  $\{\triangle \theta_{c}^{t} = X - \theta^{t}\}_{c\in \mathcal{C}}$  in each training round  $t$  (Lines 12 and 13). If a compromised client  $c$  is selected with probability  $q$  in training round  $t$ , it sends malicious local gradients  $\triangle \theta_{c}^{t}$  to the server upon receiving the latest model update  $\theta^t$ . The global model is then aggregated and updated as follows:

$$
\theta^{t + 1} = \theta^t -\lambda \Big(\sum_{i\in S^t\setminus \mathcal{C}}\triangle \theta_i^t +\sum_{c\in S^t\cap \mathcal{C}}\triangle \theta_c^t\Big) / |S_t|. \tag{2}
$$

The Trojaned surrogate loss minimized by the attacker and the compromised clients is as follows:

$$
\frac{1}{2} (\sum_{c\in \mathcal{C}}\| X - \theta^{*}\|_{2}^{2} + \sum_{i\in N\backslash \mathcal{C}}\| \theta_{i}^{*} - \theta^{*}\|_{2}^{2}), \tag{3}
$$

where  $\theta^{*}$  represents the optimal global FL model, and  $i\in N\backslash \mathcal{C}$  are the benign clients and their associated loss functions on their legitimate local datasets  $D_{i}$ :  $\theta_{i}^{*} = \arg \min_{\theta_{i}}L(\theta_{i},D_{i})$ . In practice,  $\theta_{i}^{*}$  serves as a personalized model for client  $i$ .

To increase stealthiness of our attack, we introduce a dynamic learning rate  $\psi_{c}^{t}$ , randomly sampled from a predetermined distribution, such as  $\mathcal{U}[a,b]$  ( $0< a< b\leq 1$ ). Before sending the malicious gradients to the server in each training round  $t$ , they are multiplied by the sampled dynamic learning rate  $\psi_{c}^{t}$ , as follow:

$$
\forall c\in \mathcal{C},t\in [T]:\triangle \theta_c^t = \psi_c^t [X - \theta^t ]. \tag{4}
$$

![](images/224499880ca52c75491806c65ec5b0ae79010fad34f0d129f8518ee2bd1a40bd.jpg)  
Fig. 3: Average angles among gradients from benign and compromised clients as a function of  $\alpha$  in the FEMNIST dataset. Model and data configuration are in Section V.

![](images/0843aab8981ce4c61e9ad4bf09b9c402ec8f0f679db3532c304bf7e8ca3613a0.jpg)  
Fig. 4: Approximation error for the lower bound of  $|\mathcal{C}|$  in Theorem 1 as a function of  $\alpha$  using FEMNIST dataset

Novelty and Advantages. The novelty and distinctive advantages of COLLAPOIS stem from its simplicity and efficiency. Its simplicity lies in the fact that the attacker only needs to train the Trojaned model  $X$  with minimal adjustments to the FL procedure compared to classical data poisoning methods [13], [14]. Additionally, these adjustments do not add any extra computational overhead, as compromised clients do not need to derive gradients from their local data in each training round. Importantly, the attacker does not require additional knowledge from benign clients or the server to implement this adjustment.

COLLAPOIS offers significant cost- effectiveness for compromised clients when computing malicious gradients using Eq. 4 compared to conventional data poisoning approaches, where local models are trained on poisoned datasets. It also benefits the attacker controlling all compromised clients.

Consequently, COLLAPOIS is a practical and feasible method for uncovering backdoor risks in FL, thanks to its simplicity, efficiency, and the aforementioned benefits.

The following analysis provides more insights into the novelty and key advantages of COLLAPOIS.

# C. Smaller and Bounded Numbers of Compromised Clients

Given scattered gradients from benign clients, the effectiveness of the malicious local gradients  $\triangle \theta_{c}^{t}$  in transferring backdoors to FL models is enhanced. This allows establishing a lower bound on the number of compromised clients needed for COLLAPOIS, reducing the attack's client requirements.

In Fig. 3, we present a visual representation to aid our comprehension of the scatter observed among the gradients of both benign and compromised clients. This scatter is illustrated by the angles formed among these gradients. The diversity in clients' local data distribution is expressed through smaller values of the concentration parameter  $\alpha$  in the Dirichlet distribution among clients' local data. As a result, angles between pairs of benign clients' local gradients become larger, indicating a more scattered distribution.

This observation can be easily understood, as the local models  $\{\theta_i^t\}_{i\in N\backslash \mathcal{C}}$  of benign clients are customized through training on their respective local datasets  $\{D_i\}_{i\in N\backslash \mathcal{C}}$ . When these datasets  $\{D_i\}_{i\in N\backslash \mathcal{C}}$  exhibit greater diversity, the resulting local models become more dispersed. Consequently, their corresponding local gradients  $[\triangle \theta_i^t = \theta_i^t - \theta^t ]_{i\in N\backslash \mathcal{C}}$  experience more scattering when compared to the same global model  $\theta^t$ . This, in turn, weakens the aggregation of benign gradients, denoted by  $\sum_{i\in S^t\cap \mathcal{C}}\triangle \theta_t^t$  in Eq. 2, in the face of the poisoned gradients  $\sum_{c\in S^t\cap c}\triangle \theta_c^t$ . This observation applies to various training algorithms, including FedAvg and FedDC (personalized federated training approaches).

In typical DPOIS attacks (as illustrated in Fig. 3b), the malicious local gradients  $\{\triangle \theta_c^t = \theta_c^t - \theta^t\}_{c\in \mathcal{C}}$  where  $\{\theta_c^t = \arg \min_{\theta^t}L(\theta^t,D_c\cup D_c^{T r o j})\}_{c\in \mathcal{C}}$  exhibit a similar level of scatter as benign gradients. This is because the local Trojaned models  $\{\theta_c^t\}_{c\in \mathcal{C}}$  heavily rely on diverse local data distributions, causing them to scatter in each training round. As local data diversity among compromised clients rises, the angles between malicious gradients increase (Fig. 3b), reducing DPOIS attack effectiveness and limiting insights into the impact of data distribution on attack stealthiness.

In this work, we leverage our observations about the gradient scatter to establish a novel correlation between data distribution and the effectiveness as well as the stealthiness of the attack. We utilize the scatter observed in benign gradients to manipulate the angles among malicious gradients (as illustrated in Fig. 3a). By consistently reinforcing the aggregation of these malicious gradients during training iterations, we exert a pulling force on the global model  $\theta^t$ , steering it towards the shared Trojaned model  $X$ . As a result, we are able to substantially reduce the number of compromised clients needed to successfully execute a backdoor poisoning attack and establish a minimum threshold for the same. We consider a poisoning attempt to be successful in a particular training round  $t$  if the updated global model  $\theta^{t + 1}$  moves closer to the Trojaned model  $X$ . In other words, malicious gradients dominate benign ones, causing the global model to align with their direction and trigger the backdoor attack.

In the worst case, when the aggregated benign gradients are oriented in the opposite direction to that of the aggregated compromised gradients, and the angle  $\beta_{i}$  between the gradients of an arbitrary benign client  $i$  and the aggregated malicious gradients of all compromised clients follows a normal distribution  $\mathcal{N}(\mu_{\alpha},\sigma^{2})$  (Fig. 3), we derive a lower bound on the

![](images/b63b8c09768371a630e115436af783fa3cbcb55167c6d5de0bbc82486e388893.jpg)  
Fig. 5: 3D plot of  $|\mathcal{C}| / |N|$  as a function of  $\mu_{\alpha}$  and  $\sigma$ .

minimum number of compromised clients  $|\mathcal{C}|$  needed for attack success in a single training round in the following theorem.

Theorem 1. The minimum number of compromised clients needed to carry out backdoor poisoning successfully in the worst- case scenario is given by the following formula:

$$
|\mathcal{C}|\geq \frac{2 - \sigma^2 - \mu_\alpha^2}{a + b + 2 - \sigma^2 - \mu_\alpha^2} |N|, \tag{5}
$$

where  $\beta_{i}$  is the angle between the gradients of an arbitrary benign client  $i$  and that of the aggregated malicious gradients of all the compromised clients. We assume that  $\beta_{i}$  follows a normal distribution, i.e.,  $\beta_{i}\sim \mathcal{N}(\mu_{\alpha},\sigma^{2})$

# All proofs of Theorems are in Supplementary.

Theorem 1 is obtained by approximating  $\sum_{c\in \mathcal{C}}\psi_c$  with  $|\mathcal{C}|$ $\frac{(a + b)}{2}$  and  $\sum_{i\in N\setminus \mathcal{C}}\beta_i^2$  with  $\begin{array}{r}\mathbb{E}(\sum_{i\in N\setminus \mathcal{C}}\beta_i^2) \end{array}$  . The error of the approximation can be bounded using concentration bounds, such as Hoeffding, with high confidence levels.

In practical scenarios, the attacker can make accurate estimations of the mean  $\mu_{\alpha}$  and variance  $\sigma$  of the angles by utilizing the datasets  $\{D_c\}_{c\in \mathcal{C}}$  collected by compromised clients. This enables the attacker to precisely approximate the lower bound of  $|\mathcal{C}|$  with a bounded error based on concentration bounds, such as Hoeffding bound. Fig. 4 presents this relative approximation error  $|\frac{|\mathcal{C}| - |\mathcal{C}|}{|\mathcal{C}|} |$  as a function of the concentration parameter  $\alpha$  where  $|\mathcal{C}|$  is the approximated lower bound of  $|\mathcal{C}|$  The higher the degree of diversity in benign clients' local data is, the larger the relative approximation error is. However, the relative approximation error is marginal across all the degrees of  $\alpha$  i.e.,  $2.23\%$  given  $\alpha = 0.01$  and  $0.57\%$  given  $\alpha = 100$  In addition, the mean of angles  $\mu_{\alpha}$  and its variance  $\sigma$  are quite consistent from initial training rounds and throughout the training process (Fig. 3); therefore, the attacker can estimate the lower bound  $|\mathcal{C}|$  in less than ten training rounds. This reduces poisoning delay in federated training. Importantly, our lower bound of  $|\mathcal{C}|$  remains practical, as the attacker follows the threat model without extra client information.

Fig. 5 shows the lower bound of  $\frac{|\mathcal{C}|}{|\mathcal{N}|}$  in a 3D surface as a function of  $\mu_{\alpha}$  and  $\sigma$ . From Theorem 1, a higher mean  $\mu_{\alpha}$  and variance  $\sigma$  (indicating more scattered gradients and greater local data diversity) reduce the number of compromised clients  $\mathcal{C}$  needed for successful execution of COLLAPoIs, leading to a higher backdoor success rate with more diverse local data.

To address this concern, COLLAPoIs introduces the concept of a shared Trojaned model  $X$  as a stable and optimized poisoned area. Leveraging the lower bound on the number of compromised clients, we demonstrate in the following theorem that the global FL model  $\theta$  converges to a small bounded region around  $X$ . This ensures that the impact of the backdoor attack is confined to a limited area.

Theorem 2. For a compromised client c participating in round  $t$  the  $l_{2}$  - norm distance between the global model  $\theta^t$  and the Trojaned model  $X$  is always bounded as follows:

$$
\| \theta^t -X\| _2\leq (1 / a - 1)\| \triangle \theta_c^{t'}\| _2 + \| \zeta \| _2, \tag{6}
$$

where  $\forall t:\psi_c^t\sim \mathcal{U}[a,b]$ $a< b$ $a,b\in (0,1],t'$  is the closest round the compromised client c participated in, and  $\zeta$  is  $a$  small error rate.

In Theorem 2, as the global model approaches convergence, indicated by  $t'$  and  $t$  approaching the number of rounds  $T$  the norm  $\| \xi \| _2$  becomes extremely small, and  $\| \triangle \theta_c^{t'}\| _2$  is bounded by a small constant  $\tau$ . This ensures that the global FL model  $\theta^T$  converges to a bounded and low- loss region surrounding the Trojaned model  $X$ . In other words,  $\| \theta^T - X\| _2$  is minimized to a negligible value. This provides a robust assurance of the success of our attack.

Theorem 2 shows that the  $l_{2}$  - norm distance between the global model  $\theta^t$  and the Trojaned model  $X$  , i.e.,  $\| \theta^t - X\| _2$  is bounded by  $\begin{array}{r}\left(\frac{1}{a} - 1\right)\| \triangle \bar{\theta}_c^{t'}\| _2 + \| \xi \| _2 \end{array}$  , where  $t^\prime$  is the closest round the compromised client  $c$  participated in, and  $\xi \in \mathcal{R}^m$  is a small error rate. When the FL model converges, e.g.,  $t^\prime ,t\approx$ $T$ $\| \xi \| _2$  become tiny and  $\| \triangle \theta_c^{\bar{\theta}_c}\| _2$  is bounded by a small constant  $\tau$  ensuring that the output of the FL model under COLLAPoIs given the compromised client  $\theta^T$  converges into a bounded and low loss area surrounding  $X$ $(\| \theta_c^T - X\| _2$  is tiny) to imitate the model convergence behavior of legitimate clients. Consequently, COLLAPoIs requires a small number of compromised clients to be highly effective. Also, COLLAPoIs is stealthy by avoiding degradation and shifts in model utility on legitimate data samples during the whole poisoning process.

Remark. Theorems 1 and 2 establish that COLLAPoIs maintains stealthiness through controlled perturbations, with minimal impact on the effectiveness of clean inputs. Simultaneously, it demonstrates high effectiveness even with a limited number of compromised clients, thanks to its well- coordinated malicious updates.

# D. Attack Stealthiness

In addition to their effectiveness, the malicious gradients  $\{\triangle \theta_c^t\}_{c\in \mathcal{C}}$  possess several key properties that contribute to their stealthiness, including the following:

(1) The Trojaned model  $X$  exhibits higher model utility on legitimate data samples compared to randomly initialized global FL and benign clients' local models, particularly during the early training rounds and when there is a greater diversity in the local data distribution of benign clients (indicated by smaller values of the concentration parameter  $\alpha$ ). The resulting models achieve superior model utility on legitimate data samples by utilizing malicious gradients to train both the global and clients' local models. Consequently, COLLAPoIs demonstrates greater stealthiness compared to MREPL and

![](images/737b8cdc81e0bdccc5594bc7c9e45cdafae4c319ff4f34ce617914d863897de2.jpg)  
Fig. 6: Attack Stealthiness: Angles between malicious/benign gradients and sampled gradients. Compromised clients with benign clients are blended and modestly different (using the FEMNIST dataset with  $\psi_c^t \sim \mathcal{U}[0.95, 0.99]$ ).

DPOIS attacks, as it avoids the degradation and shifts in model utility on legitimate data samples throughout the entire poisoning process. This characteristic makes the detection of COLLAPoIs highly challenging during the federated training.

(2) By ensuring that the random and dynamic learning rate  $\psi_c^t$  is exclusively known to the compromised client  $c$ , we can effectively prevent the server from tracking the Trojaned model  $X$  or detecting suspicious behavior patterns from the compromised client. In practice, the server can identify compromised clients with precision  $p$  and detect the presence of the Trojaned model  $X$ . The server's set of identified compromised clients consists of  $p \times |\mathcal{C}|$  compromised clients  $\bar{C}$  and  $(1 - p)(|N| - |\mathcal{C}|)$  benign clients  $\bar{L}$ . The estimated Trojaned model is  $X' = \sum_{c \in \bar{C} \cup \bar{L}} \theta_c^t ||\mathcal{C}|$ . The following theorem establishes a bound on the server's  $l_2$ -norm estimation error of the Trojaned model  $X$ , denoted as  $Error = \| X' - X\| _2$ .

Theorem 3. The server's estimation error of the Trojaned model  $X$  is bounded as follows:

$$
\Big\| \sum_{c\in \bar{C}}\frac{\triangle\theta_c^t}{p|\mathcal{C}|b}\Big\| _2\leq Error\leq \arg \operatorname *{max}_{L\subseteq N}\max_{s.t.|L| = |\mathcal{C}|}\Big\| \sum_{i\in L}\frac{\theta_i^t}{|L|} -X\Big\| _2. \tag{7}
$$

From Theorem 3, we observe that: (1) Lower detection precision  $p$  leads to a larger estimation error near the upper bound; (2) A smaller upper bound  $b$  of  $\psi_c^t$  increases the estimation error's lower bound; and (3) If the malicious gradient  $\triangle \theta_c^t$  is too small, we can uniformly upscale its  $l_2$ - norm to be a small constant, denoted  $\tau$ , to enlarge the lower bound of the estimation error without affecting the model utility or backdoor success rate. Fig. 7 shows this effect with  $p = 1$  across various numbers of compromised clients  $|\mathcal{C}|$ . After 1,000 rounds, the error stabilizes at a controlled lower bound  $(\tau = 2)$ , preventing accurate estimation of model  $X$ .

Remark. Practitioners can connect Theorems 1 and 3 to discover that: The more diverse clients' local data is (i.e., larger values of  $\mu_{\alpha}$  and  $\sigma$  resulting in a smaller number of compromised clients  $|\mathcal{C}|$  in Eq. 5), the more difficult for the server to approximate the Trojaned model  $X$  is; hence, the more stealthy the attack will be. This is because a smaller number of compromised clients  $|\mathcal{C}|$  induces a larger lower bound of the estimation error in Eq. 7.

While the server may not be efficient in directly estimating the Trojaned model  $X$ , it can attempt to distinguish compro

![](images/1452c8e82d090c030b9e617f4958af7a7dc302d5ad79db0efee78b535e20f77f.jpg)  
Fig. 7: Estimation error of COLLAPoIs.  $(p = 1$ , FEMNIST)

mised clients by analyzing the angles and magnitudes of the gradients submitted by each client [22].

To protect malicious gradients from being detectable, the attacker can marginally adjust the dynamic learning rate  $\psi_c^t$  to seamlessly blend each malicious gradient in the background of benign gradients. The wider the range of  $\psi_c^t \sim \mathcal{U}[a, b]$ , the more scattered malicious gradients in terms of angles and magnitude are, i.e., more randomness. The attacker can select a suitable range of  $\mathcal{U}[a, b]$  such that the average angle and its variance between each of the malicious gradients and a set of sampled gradients (which plays a role of data background) are similar to those of benign clients. In practice, the attacker can derive sampled gradients using clean data from compromised clients  $D_{cc \in \mathcal{C}}$  and the global model  $\theta^t$ . These clean gradients can then mimic those from benign clients, ensuring the threat model without accessing additional benign client information.

Fig. 6 shows that malicious and benign gradients have similar average angles and variance. To improve robustness, malicious gradients are clipped with a shared bound  $A$ , keeping their magnitude within the range of benign gradients.

Consequently, COLLAPoIs can conceal malicious gradients (angles, variance, magnitude) to bypass statistical and clustering defenses [22] without compromising attack performance, provided  $\psi_c^t \sim \mathcal{U}[a, b]$  and the clipping bound  $A$  are chosen such that they do not negatively impact federated training.

Remark. Our attack has several key advantages: (1) It requires only a small, bounded number of compromised clients to successfully manipulate the global model toward a tight region around the Trojaned model  $X$  (Theorems 1, 2), enabling effective backdoor transfer to local models. (2) It achieves higher attack success rates under diverse local data distributions, enhancing real- world applicability (Theorem 1). (3) It prevents accurate estimation of  $X$  or detection of malicious clients (Theorem 3), ensuring stealth. Overall, these advantages collectively contribute to the effectiveness, practicality, and stealthiness of our attack against FL systems.

# V. EXPERIMENTAL RESULTS

In this section, we seek to examine the connections among backdoor attacks, defense mechanisms, different local data distribution levels, and the performance of Federated Learning (FL). To achieve this, we focus on addressing four demanding inquiries: (1) How effective is COLLAPoIs as a poisoning technique in FL, compared to existing backdoor attacks? (2) How does COLLAPoIs perform with different levels of data diversity given different proportions of compromised clients?

(3) How to defend against COLLAPoIs, and what are the costs and limitations of such defenses? and 
(4) How many and which clients are affected, at which attack success rates, and why?

To answer these questions, we evaluate at the population level, considering the FL system as a whole, and at the client level to study the impact of the attack on each client.

Data and Model Configuration. We conduct experiments on Sentiment [35] and FEMNIST [37] datasets. We leverage the symmetrical Dirichlet distribution with different values of the concentration parameter  $\alpha \in [0.01,100]$  [18], where smaller  $\alpha$  indicates greater diversity. In Sentiment, we include 5, 600 clients with over 1 million samples. In FEMNIST, there are 3, 400 clients with 805, 263 samples. We use  $q = 1\%$  and  $\psi \sim \mathcal{U}[0.9,1]$ . Class 0 is designated as the target class. Data is split into  $70\%$  training,  $15\%$  testing, and  $15\%$  validation per client. Combined validation sets from all compromised clients form the auxiliary set to train the Trojaned model  $X$ . The attacker randomly compromises  $0.1\%$ ,  $0.5\%$ , and  $1\%$  of clients, treating these small fractions as a practical threat [12].

We adopt the model from [4] with a LeNet- based network for the local model and a fully connected network with linear heads for the global model. For the Sentiment dataset, we use the BERT tokenizer with a two- layer fully connected task head. The SGD optimizer is applied with a learning rate of 0.01 for the global model and 0.001 for local models.

Evaluation Approach. We evaluate COLLAPoIs via three approaches. We first compare COLLAPoIs with DPOIs, MREPL, and distributed backdoor attacks (DBA) [8], [9] in terms of benign accuracy (Benign AC) on legitimate data samples and backdoor success rate Attack SR) on Trojaned data samples without defense. Then, we investigate the effectiveness of adapted robust federated training algorithms under a variety of hyper- parameter settings against COLLAPoIs. Finally, we provide a performance summary of the stateof- the- art attacks and defenses to assess the landscape of backdoor risks in FL under diverse levels of clients' local data.

The average Benign AC and Attack SR across all the clients, using testing data, are defined as:

$$
\begin{array}{rl} & {\mathrm{B e n i g n~A C} = \frac{1}{|N|}\sum_{i\in N}\Big[\frac{1}{|D_i^{test}|}\sum_{x\in D_i^{test}}\mathbb{I}\big(f(x,\theta_i),y\big)\Big]}\\ & {\mathrm{A t t a c k~S R} = \frac{1}{|N|}\sum_{i\in N}\Big[\frac{1}{|D_i^{test}|}\sum_{x\in D_i^{test}}\mathbb{I}\big(f(x + \mathcal{T},\theta_i),y^{T r o j}\big)\Big],} \end{array}
$$

where  $x + \mathcal{T}$  is a Trojaned data sample,  $\mathbb{I}$  is the indicator function s.t.  $\mathbb{I}(y^{\prime},y) = 1$  if  $y^\prime = y$  ; otherwise  $\mathbb{I}(y^{\prime},y) = 0$  , and  $D_{i}^{test}$  and  $|D_i^{test}|$  are a test set and its number of samples.

We evaluate COLLAPoIs against state- of- the- art personalized FL algorithms FedDC [18] and MetaFed [38], as well as the widely used FedAvg, to assess the attack's generalizability. To analyze client- specific performance, stealthiness, and backdoor attack risks, we report Benign AC and Attack SR values for the top-  $\kappa \%$  affected benign clients  $i$  selected based on the highest sum of Benign AC and Attack SR, as follows:

$$
\mathrm{score}_i = \frac{\sum_{x\in D_i^{test}}\Big[\mathbb{I}\big(f(x,\theta_i),y\big) + \mathbb{I}\big(f(x + \mathcal{T},\theta_i),y^{T r o j}\big)\Big]}{|D_i^{test}|}. \tag{8}
$$

# Figures 14-25 are in the Supplementary

COLLAPOIs and Existing Attacks. Figs. 8 and 15 illustrate the Benign AC and Attack SR of COLLAPoIs and three baseline poisoning attacks as a function of the concentration parameter  $\alpha$  on the Sentiment and FEMNIST datasets across FL algorithms, where the attacker compromises  $1\%$  of clients.

The figures show that COLLAPoIs significantly outperforms DPOIs, MREPL, and DBA in Attack SR without a notable drop in Benign AC across datasets, FL algorithms, and  $\alpha$  values. In the Sentiment dataset, COLLAPoIs achieves an  $25.56\%$  increase in Attack SR with a slightly better Benign AC  $(1.94\%)$  compared to the best baseline, DPOIs on average  $(p$ - value 3.08e- 19). All statistical tests are 2- tail t- tests. Each experiment was run 5 times with small variance  $(0.01\% - 0.03\%)$ . In the FEMNIST dataset, Attack SR rises to  $91.25\%$  on average  $(p$ - value 7.78e- 167), while baseline attacks struggle under FedDC. This is due to FedDC's local personalization. When Trojans are poorly integrated with global and local models during training, local personalization can mitigate backdoor attacks. COLLAPoIs tackles this by aligning global and local models near the Trojaned model  $X$ . Unlike MREPL, DPOIs, and DBA, where no such region exists, local personalization struggles to pull the model away from this area.

Local Data Diversity and Attack SR. Figs. 8 and 15 show that as  $\alpha$  decreases (indicating more diverse local data), the average Attack SR increases. At  $\alpha = 0.01$ , COLLAPoIs achieves  $83.33\%$  Attack SR, dropping to  $80.00\%$  at  $\alpha = 1$  and  $79.89\%$  at  $\alpha = 100$ . This observation aligns with our theoretical analysis. A slight difference is observed with MetaFed, as Attack SR shows a minor increase with higher  $\alpha$  values. This result is because MetaFed creates personalized models via knowledge distillation by leveraging common knowledge from neighboring clients. In highly non- IID scenarios, these neighbors are sparse, limiting knowledge transfer and reducing the backdoor's ability to spread beyond compromised clients.

Furthermore, smaller  $\alpha$  values lead to greater variation in Attack SR across federated training algorithms and datasets due to more diverse clients' local data, resulting in dispersed distributions. Consequently, a subset of benign clients aligns closely with compromised clients and the Trojan model  $X$ , leading to higher Attack SR, while others are more isolated, showing lower rates. This causes a broader range of Attack SR values. In addition, weaker benign gradients at smaller  $\alpha$  values allow the backdoor to infect more benign clients, supported by both average Attack SR and theoretical analysis.

Bypassing Robust Federated Training. Since COLLAPoIs outperforms baseline attacks, we evaluate its effectiveness against robust federated training algorithms. We select four state- of- the- art (SOTA) methods: DP- optimizer (DP), Norm- Bound, Krum, and robust learning rate (RLR). These choices

![](images/378fce7e0349257ac23a45e645cb2c0a610e8faed9e48d265c0da7591dae4fc3.jpg)  
Fig. 8: FedAvg, FedDC, and MetaFed under attacks (1% compromised clients) in the Sentiment dataset.

encompass diverse design strategies, allowing a comprehensive evaluation of COLLAPOIS against SOTA defenses.

An effective backdoor defense should maintain a minimal drop in Benign AC while reducing Attack SR (lower is better), allowing efficient FL training while mitigating backdoor effects. However, Figs. 9 and 16 show the lack of such effective defense among the baseline robust federated training methods.

Standard defenses, i.e., Krum and RLR often lead to substantial drops in Benign AC, making them effective but impractical. Some defenses, such as DP and NormBound, leave FL models highly vulnerable, with Attack SRs as high as  $89.02\%$  and  $91.60\%$  respectively. Krum and RLR reduce Benign AC by  $24.93\%$  and  $61.53\%$  on average ( $p$ - value 8.07e- 9). Only MetaFed combined with DP or NormBound on FEMNIST shows promise, maintaining high Benign AC and lower Attack SR. However, even then, over  $60\%$  of benign clients are compromised across various  $\alpha$  levels (Fig. 16f).

Bypassing Defenses. COLLAPOIS can hide the malicious gradients (i.e., in terms of angles, variance, and magnitude) bypassing the SOTA statistical tests and clustering- based defenses [22] without performance loss. There is no significant difference between malicious and benign gradients using t- test for the average angle and mean, Levene's test [39] for the variances, Kolmogorow- Smirnow- Test [40] for the gradients' distributions, and only a tiny  $3.5\%$  chance that a malicious gradient is disregarded as an outlier based on the  $3\sigma$  rule [41].

Percentage of Compromised Clients. To identify when a defense becomes effective against COLLAPOIS, we reduce compromised clients from  $1\%$  to  $0.5\%$  and  $0.1\%$ , indicating very small numbers of compromised clients, 5 and 28 clients in the Sentiment dataset, and 4 and 7 clients in the FEMNIST dataset. Lower Attack SR with high average Benign AC across clients is expected (Figs. 17- 20), but this does not indicate effective defense. The top-  $25\%$  of infected benign clients show very high Attack SR (86.12% on average with  $0.5\%$  compromised clients) across datasets and robust federated training algorithms (Figs. 10 and 25). The Attack SR is even higher for top-  $1\%$  infected clients (Figs. 21 and 22). Also, we observe high Attack SR for top-  $50\%$  infected clients (Figs. 23 and 24). While generally effective with  $0.1\%$  compromised clients (74.65% Attack SR on average), defenses like FedAvg with DP or NormBound show promising results in the FEMNIST dataset (Fig. 25a), achieving a low  $4.55\%$  Attack SR with a  $23.83\%$  Benign AC drop. Hence, even a small fraction of compromised clients (0.1-  $0.5\%$ ) allows COLLAPOIS to compromise a significantly large portion (25%) of benign clients, with an average Attack SR over  $60.12\%$ . At  $1\%$  compromised clients, all benign clients are affected.

![](images/ad1dc2c70711ce3b8e0cd85a1ffeea4df49291e052192162231ea3c40c9ef43e.jpg)  
Fig. 9: COLLAPOIS (1% compromised clients) under defenses for the Sentiment dataset. (Krum and RLR are not applicable for MetaFed.)

Client- level Evaluation. Our results reveal that different clients exhibit varying levels of backdoor susceptibility, as indicated by the spectrum of Attack SR resulting from COLLAPOIS, raising a fundamental question: What underlies these discrepancies in backdoor risk among benign clients?

To answer the question, we examine the proximity be

![](images/f683c59ae64e2c6cc54043d6054eeaf264631d28517b5c85e7e54feaf11eee6d.jpg)  
Fig. 10: COLLAPoIs (under defenses) with  $0.1\%$  and  $0.5\%$  compromised clients for the Sentiment. (Top  $25\%$  Clients)

![](images/7aeb44b78c182243777324c6f8bfc50116ce693606f9f8875b0ae178b0ad9d79.jpg)  
Fig. 11: Benign AC and Attack SR for all clients in the FEMNIST dataset using FedAvg under DP defense.

Fig. 11: Benign AC and Attack SR for all clients in the FEMNIST dataset using FedAvg under DP defense.tween  $X$  and groups of benign clients at different backdoor risk levels. These sets include the  $1\%$  - cluster,  $25\%$  - cluster,  $50\%$  - cluster, and the remaining bottom-  $50\%$  - cluster of benign clients. The  $k\%$  - cluster consists of all benign clients having top-  $k\%$  scores (Eq. 8) while excluding clients in all preceding clusters. For instance,  $50\%$  - cluster includes top-  $50\%$  infected clients, but it excludes clients in  $25\%$  - cluster and  $1\%$  - cluster. Fig. 11 shows the distribution of these infected client groups. We compute the average cosine similarity of their cumulative label distributions to  $X$  to examine proximity, as follows:

$$
C S_{k} = \frac{1}{|N_{k}|}\sum_{i\in \mathcal{C}}C o s\big(\mathcal{P}_{C L}(D_{i}),\mathcal{P}_{C L}(D_{a})\big) \tag{9}
$$

where  $k$  is the  $k\%$  - cluster of infected clients,  $|N_{k}|$  is the number of clients in  $k$  , and  $C o s()$  is cosine similarity function.  $\mathcal{P}_{CL}(D_i)$  and  $\mathcal{P}_{CL}(D_a)$  are the local data's cumulative label distributions of client  $i$  and the auxiliary data  $D_{a}$  used to train  $X$  , where  $\mathcal{P}_{CL}(\cdot) = [N_j]_{j\in [1,L]}$  and  $N_{j}$  is the sum of numbers of data samples with labels from 1 to  $j$  (i.e.,  $\begin{array}{r}N_{j} = \sum_{q = 1}^{j}N_{q}) \end{array}$

Fig. 12 shows that benign clients with local data more aligned with the compromised clients' auxiliary data  $D_{a}$

![](images/5bddd06938ca2906389d426cac9f8d362d5e574c0b3327def2fc95ae6d2ff4f3.jpg)  
Fig. 12: Label distributions and Attack SR.

![](images/f60768ecdb8766429e44f045f641cd1752b8769c7d62e53838beec2abd915556.jpg)  
Fig. 13: Benign AC and Attack SR as a function of training rounds. (1% compromised clients,  $\alpha = 0.01$ , FEMNIST)

(higher cosine similarity) are more vulnerable (higher Attack SR). This is because their gradients align more closely with malicious gradients, making them more likely to be influenced by  $X$  and highly susceptible to the backdoor attack.

In the FEMNIST dataset, the  $1\%$  - cluster infected clients have the highest  $CS_{1}$  0.95 and average Attack SR  $(98.49\%)$  compared with  $CS_{25} = 0.91$  and  $CS_{50} = 0.90$  and  $67.11\%$  and  $58.21\%$  Attack SR of the  $25\%$  - cluster and  $50\%$  - cluster infected clients, respectively. The bottom-  $50\%$  - cluster infected clients have the lowest  $CS_{\mathrm{bottom - 50}}$  (0.85), and consequently the lowest Attack SR  $(31.60\%)$  . The Sentiment dataset exhibits a similar trend. However, the gap between the  $1\%$  - cluster infected clients with the  $25\%$  - cluster and the  $50\%$  - cluster is smaller. This is because the clustering of infected clients near auxiliary data  $D_{a}$  results in lower variability in cosine similarity and Attack SR across client groups (Fig. 12b). Similar trends are observed across datasets and FL mechanisms.

Stealthy and Longevity Attack. Fig. 13 shows Attack SR and Benign AC over training rounds. Unlike MREPL with sudden shifts (e.g., Benign AC raises from  $39.21\%$  to  $74.11\%$  in one round), COLLAPoIs maintains consistently higher and long- lasting Attack SR across many rounds, with only a negligible  $1\%$  drop after 40 rounds compared to MREPL's  $40\%$  decline. Importantly, COLLAPoIs converges significantly faster than DPOIs and DBA. This aligns with its two key properties: (1) it pulls FL models consistently toward  $X$  enhancing effectiveness, and (2) once near the Trojan- infected area, models are hard to reverse. This highlights COLLAPoIs as highly effective and stealthy compared to baseline attacks.

# VI. DISCUSSION

This section aims to contribute our insights into the issues at hand and potentially guide future research endeavors.

Attack Perspective. COLLAPOIs poisons specific clients using divergent data. To escalate this threat, we target highvalue clients only, minimizing detection. A "semi- ready" Trojaned model  $X$  activates after updates from these clients, using (1) auxiliary data to approximate client behavior or (2) aggregated updates over multiple rounds to detect clientspecific patterns, boosting both attack precision and stealth.

Defense Viewpoint. Evaluation shows current defenses in (personalized) FL are largely ineffective against highly divergent client data. Methods like DP and NormBound lack protection, while Krum and RLR harm model utility. Effective defenses for these challenges remain largely unexplored.

Our study shows that while benign client updates tend to cancel out, compromised clients can coordinate to propagate backdoors. Existing defenses like DP and NormBound fail to manage divergence, whereas Krum and RLR overly constrain it, harming benign performance. More balanced model update strategies offer promising alternatives.

# VII. CONCLUSION

VII. CONCLUSIONThis study proposes a novel backdoor attack called COLLAPOIs that exploits diverse data distribution among clients in Federated Learning (FL). Through theoretical analysis and extensive empirical experiments, we demonstrate the effectiveness, practicality, and stealthiness of COLLAPOIs. We show that even with a small number of compromised clients, COLLAPOIs can successfully converge the FL model around a pre-trained Trojaned model. It achieves higher backdoor attack success rates when clients exhibit greater data diversity, and it impairs the server's ability to detect suspicious behaviors. Furthermore, COLLAPOIs can bypass current backdoor defenses, particularly when clients possess diverse data distributions. The evaluation results highlight that a mere  $0.5\%$  of compromised clients can open a backdoor on  $15\%$  of benign clients with an impressive success rate exceeding  $70\%$  using state-of-the-art robust FL algorithms on benchmark datasets.

# ACKNOWLEDGMENTS

ACKNOWLEDGMENTSThis research was partially supported by the National Science Foundation (NSF) CNS- 1935928, and the Qatar National Research Fund (QNRF) ARG01- 0531- 230438.

# REFERENCES

[1] GDPR, "The european data protection regulation," 2018. [2] B. McMahan et al., "Communication- efficient learning of deep networks from decentralized data," in AISTATS, 2017. [3] H. Zhu et al., "Fl on non- id data: A survey," arXiv, 2021. [4] A. Shamsian, A. Navon, E. Fetaya, and G. Chechik, "Personalized federated learning using hypernetworks," in ICML, 2021. [5] A. Fallah et al., "PFL: A meta- learning approach," NeurIPS, 2020. [6] X. Jiang, H. Hu et al., "Fssys: Toward an open ecosystem for federated learning mobile apps," IEEE TMC, 2022. [7] X. Jiang et al., "Zone- based federated learning for mobile sensing data," in IEEE PerCom, 2023, pp. 141- 148. [8] C. Xie, K. Huang, P.- Y. Chen, and B. Li, "Dba: Distributed backdoor attacks against federated learning," in ICLR, 2020.

REFERENCES[1] GDPR, "The european data protection regulation," 2018. [2] B. McMahan et al., "Communication- efficient learning of deep networks from decentralized data," in AISTATS, 2017. [3] H. Zhu et al., "Fl on non- id data: A survey," arXiv, 2021. [4] A. Shamsian, A. Navon, E. Fetaya and G. Chechik, "Personalized federated learning using hypernetworks," in ICML, 2021. [5] A. Fallah et al., "PFL: A meta- learning approach," NeurIPS, 2020. [6] X. Jiang, H. Hu et al., "Fssys: Toward an open ecosystem for federated learning mobile apps," IEEE TMC, 2022. [8] C. Xie, K. Huang, P.- Y. Chen, and B. Li, "Dba: Distributed backdoor attacks against federated learning," in ICML, 2020. [9] E. Bagdasaryan et al., "How to backdoor fl," in AISTATS, 2020. [10] Z. Sun et al., "Can you really backdoor fl?" FL- NeurIPS WS, 2019. [11] Z. Zhang et al., "Neurotoxin: Durable backdoors in FL," in ICML, 2022. [12] V. Shejwalkar et al., "Back to the drawing board: A critical evaluation of poisoning attacks on production federated learning," in IEEE SP, 2022. [13] O. Suciu et al., "When does machine learning fail? generalized transferability for evasion and poisoning attacks," in USENIX Security, 2018. [14] B. Li, Y. Wang, A. Singh, and Y. Vorobeychik, "Data poisoning attacks on factorization- based collaborative filtering," NeurIPS, vol. 29, 2016. [15] P. Kairouz et al., "Advances and open problems in federated learning," Found. Trends Mach. Learn., 2021. [16] L. Collins, H. Hassani, A. Mokhtari, and S. Shakkottai, "Exploiting shared representations for personalized fl," in ICML, 2021. [17] S. Luo, D. Zhu, Z. Li, and C. Wu, "Ensemble federated adversarial training with non- iid data," arXiv, 2021. [18] L. Gao, H. Fu, L. Li, Y. Chen et al., "Feddc: Federated learning with non- iid data via local drift decoupling and correction," in CVPR, 2022. [19] S. Reddi et al., "Adaptive federated optimization," ICLR, 2021. [20] D. Li and J. Wang, "Fedmd: Heterogeneous fl via model distillation," NeurIPS Workshop on FL for Data Privacy and Confidentiality, 2019. [21] A. Ghosh, J. Chung, D. Yin, and K. Ramchandran, "An efficient framework for clustered federated learning," NeurIPS, 2020. [22] T. Kraub and A. Dmitrienko, "Mesas: Poisoning defense for federated learning resilient against attractive attackers," in ACM CCS'23, 2023. [23] T. Gu, B. Dolan- Gavitt, and S. Garg, "Badnets: Identifying vulnerabilities in the ml model supply chain," MLCS Workshop, 2017. [24] Y. Liu et al., "Trojaning attack on neural networks," 2017. [25] T. Anh and A. Tuan, "Wanet - imperceptible warping- based backdoor attack," in ICLR, 2021. [26] B. Wang, Y. Yao et al., "Neural cleanse: Identifying and mitigating backdoor attacks in neural networks," in SP, 2019. [27] K. Liu, B. Dolan- Gavitt, and S. Garg, "Fine- pruning: Defending against backdooring attacks on deep neural networks," in RAID, 2018. [28] Y. Gao, C. Xu, D. Wang, S. Chen, D. Ramchonghe, and S. Nepal, "Strip: A defence against trojan attacks on deep nns," in ACSAC, 2019. [29] M. Fang, X. Cao, J. Jia, and N. Gong, "Local model poisoning attacks to byzantine- robust federated learning," in USENIX, 2020, pp. 1605- 1622. [30] W. Ma, D. Wang, R. Sun, M. Xue et al., "The" matrix"resurrections: Robust backdoor detection via gram matrices," NDSS, 2023. [31] S. Cheng, G. Tao, Y. Liu, S. An, X. Xu, S. Feng et al., "Beagle: Forensics of deep learning backdoor attack for better defense," arXiv, 2023. [32] D. Yin, Y. Chen, R. Kannan, and P. Bartlett, "Byzantine- robust distributed learning: Towards optimal statistical rates," in ICML, 2018. [33] S. Hong, V. Chandrasekaran, Y. Kayat et al., "On the effectiveness of mitigating data poisoning attacks with gradient shaping," arXiv, 2020. [34] M. Ozdayi, M. Kantarcioglu, and Y. Gol, "Defending against backdoors in federated learning with robust learning rate," AAAI, 2021. [35] A. Go, R. Bhavyani, and L. Huang, "Twin sentiment classification using distant supervision," CS224N project report, Stanford, 2009. [36] F. Alsharadgah, A. Khreishah, M. Al- Ayyoub et al., "An adaptive black- box defense against trojan attacks on text data," in SNAMS, 2021. [37] S. Caldas, S. M. K. Duddu, P. Wu, T. Li, J. Konecny, H. B. McMahan et al., "Leaf: A benchmark for federated settings," NeurIPS, 2019. [38] Y. Chen, W. Lu, X. Qin, J. Wang, and X. Xie, "Metafed: Federated learning among federations with cyclic knowledge distillation for personalized healthcare," IEEE Trans. Neuror. Netw. Learn. Syst., 2023. [39] T.- S. Lim and W.- Y. Loh, "A comparison of tests of equality of variances," Computational Statistics and Data Analysis, 1996. [40] F. Massey, "The kolmogorov- smirnov test for goodness fit," JASA, 1951. [41] F. Pukelsheim, "The three sigma rule," The American Statistician, 1994. [42] P. Blanchard et al., "Machine learning with adversaries: Byzantine tolerant gradient descent," NeusIPS, 2017. [43] J. Bernstein et al., "signSGD with majority vote is communication efficient and fault tolerant," arXiv, 2018. [44] M. S. Ozdayi, M. Kantarcioglu et al., "Defending against backdoors in federated learning with robust learning rate," in AAAI, 2021. [45] T. Li, S. Hu, A. Beirami, and V. Smith, "Ditto: Fair and robust federated learning through personalization," in ICML, 2021, pp. 6357- 6368. [46] C. Xie, M. Chen, P.- Y. Chen, and B. Li, "Crfl: Certifiably robust federated learning against backdoor attacks," in ICML, 2021. [47] N. Wang et al., "Flare: defending fl against model poisoning attacks via latent space representations," in ACM ASJACCS, 2022. [48] H. McMahan, D. Ramage, K. Talwar, and L. Zhang, "Learning differentially private recurrent language models," ICLR, 2018.

# APPENDIX

# A. Robust Federated Training Summary Table

Table I summarizes various robust FL approaches.

# B. Proof of Theorem 1

Proof. Given the diverse data distribution among clients, their gradient updates vary in direction and magnitude. To ensure the attack's effectiveness, it is necessary for the aggregated model updates at each iteration  $t$  to align with the direction of the aggregated malicious gradient  $\sum_{i\in \mathcal{C}}\psi_{c}g_{\Delta_{c}}$  . To capture both the direction and magnitude, we project all gradients onto the direction of the aggregated malicious gradient. This leads to the following condition:

$$
\sum_{i\in \mathcal{C}}\psi_{c}g_{\Delta_{c}} + \sum_{i\in N\backslash \mathcal{C}}g_{\Delta_{i}}\geq 0, \tag{10}
$$

where  $g_{\Delta_i}$  is the projection of the gradient  $\Delta_{i}$  into the direction of the malicious aggregated gradient  $\Delta_c$  and  $\psi_c$  is the dynamic learning rate  $(\psi_c\sim \mathcal{U}[a,b])$

In worst- case scenarios where the benign gradients are oriented in the opposite direction to the aggregated malicious gradient, Eq. 10 can be reformulated as follows:

$$
(\sum_{c\in \mathcal{C}}\psi_c\cdot A_c)\vec{i} -\sum_{c\in \mathcal{N}\backslash \mathcal{C}}[\cos (\beta_i)\cdot A_i^b\vec{i} ]\geq 0, \tag{11}
$$

where  $\vec{i}$  is a unit vector representing the direction, and  $A_{c}$  and  $A_{i}^{b}$  are the magnitudes of the gradients from compromised and benign clients, respectively. To circumvent the gradient exploration and prevent the server from tracking the gradients to identify suspicious behavior patterns, we upper- bound the magnitude of the gradients by  $\tilde{A}$  (i.e.,  $\max A_c = \max A_i^b =$ $\tilde{A}$  ). Then, Eq. 11 becomes:

$$
(\sum_{c\in \mathcal{C}}\psi_c) - \sum_{i\in N\backslash \mathcal{C}}[\cos (\beta_i)]\geq 0. \tag{12}
$$

To calculate  $\begin{array}{r}\sum_{i\in N\backslash \mathcal{C}}[\cos (\beta_i)] \end{array}$  , by applying Maclaurin's theorem to the cosine function (as in Trigonometry), we have:  $\begin{array}{r}\cos (\beta_i) = 1 - \frac{\beta_i^2}{2!} +\frac{\beta_i^4}{4!} = \sum_{k = 0}^{\infty}(- 1)^k\frac{(\beta_i)^{2k}}{(2k)!} \end{array}$  Therefore, we can approximate the term  $\begin{array}{r}\sum_{i\in N\backslash \mathcal{C}}\cos (\beta_i) \end{array}$  with an error bounded by  $\mathcal{O}(\frac{\sum_{i\in N\backslash\mathcal{C}}(\beta_i)^4}{4!})$

$$
\sum_{i\in N\backslash \mathcal{C}}\cos (\beta_i)\approx (|N| - |\mathcal{C}|) - \frac{\sum_{i\in N\backslash\mathcal{C}}(\beta_i)^2}{2}. \tag{13}
$$

Then,Eq.12 becomes:

$$
(\sum_{c\in \mathcal{C}}\psi_c) - \Big((|N| - |\mathcal{C}|) - \frac{\sum_{i\in N\backslash\mathcal{C}}(\beta_i)^2}{2}\Big)\geq 0. \tag{14}
$$

Finding a closed- form solution for  $|\mathcal{C}|$  to satisfy the condition in Eq. 14 is challenging, mainly because it is not feasible to precisely quantify the summations, as they are datadependent. Therefore, we  $(I)$  approximate  $\sum_{c\in \mathcal{C}}\psi_c$  with  $|\mathcal{C}|$ $\frac{(a + b)}{2}$  (since the mean of  $\psi_c\sim \mathcal{U}[a,b]$  is  $\frac{a + b}{2}$  and (2) replace  $\begin{array}{r}\sum_{i\in N\backslash \mathcal{C}}\beta_i^2 \end{array}$  with its expectation, which is  $\begin{array}{r}\frac{\mathbb{E}}{\mathbb{E}} (\sum_{i\in N\backslash \mathcal{C}}\beta_i^2) \end{array}$  . As a result, we get,

$$
\begin{array}{rl} & {\mathbb{E}\Big(\sum_{i\in N\backslash \mathcal{C}}\beta_i^2\Big) = \mathbb{E}\Big(\sigma^2\frac{\sum_{i\in N\backslash\mathcal{C}}\beta_i^2}{\sigma^2}\Big)}\\ & {= \sigma^2\mathbb{E}\Big[\sum_{i\in N\backslash \mathcal{C}}\Big(\frac{(\beta_i - \mu_\alpha)^2 + 2\mu_\alpha\beta_i - \mu_\alpha^2}{\sigma^2}\Big)\Big]}\\ & {= \sigma^2\mathbb{E}\Big(\sum_{i\in N\backslash \mathcal{C}}(\frac{\beta_i - \mu_\alpha}{\sigma})^2\Big) + 2\sigma^2\mathbb{E}\Big(\frac{\mu_\alpha}{\sigma^2}\sum_{i\in N\backslash \mathcal{C}}\beta_i\Big) - \mathbb{E}\big((|N| - |\mathcal{C}|)\mu_\alpha^2\big)}\\ & {= \sigma^2 (|N| - |\mathcal{C}|) + 2\mu_\alpha (|N| - |\mathcal{C}|)\mu_\alpha -(|N| - |\mathcal{C}|)\mu_\alpha^2 = (|N| - |\mathcal{C}|)(\sigma^2 +\mu_\alpha^2).} \end{array}
$$

From Eqs.14 and 15,we have:

$$
\begin{array}{l}{|\mathcal{C}|.\frac{(a + b)}{2} -\Big(|N| - |\mathcal{C}| - \frac{(|N| - |\mathcal{C}|)(\sigma^2 + \mu_\alpha^2)}{2}\Big)\geq 0}\\ {\Leftrightarrow |\mathcal{C}|\geq \frac{2 - \sigma^2 - \mu_\alpha^2}{a + b + 2 - \sigma^2 - \mu_\alpha^2} |N|.} \end{array} \tag{15}
$$

Therefore, Theorem 1 holds.

# C. Proof of Theorem 2

Proof. At the round  $t^{\prime}$ $\triangle \theta_{c}^{t^{\prime}} = \psi_{c}^{t^{\prime}}[X - \theta^{t^{\prime}}]$  . This is equivalent to  $\begin{array}{r}X = \frac{\triangle\theta_c^{t'}}{\psi_c^{t'}} +\theta^{t'} \end{array}$  . In round  $t$  according to the findings of Theorem 1, the global model is expected to be a more severely poisoned model for the compromised client  $c\colon \theta^t =$ $\triangle \theta_{c}^{t^{\prime}} + \theta^{t^{\prime}} + \zeta$  . To quantify the distance between the global model  $\theta^t$  and the Trojaned model  $X$  we subtract  $X$  from  $\theta^t$  as follows:  $\begin{array}{r}\theta^t - X = (1 - \frac{1}{\psi_c^{tr}})\triangle \theta_c^{t'} + \zeta \end{array}$  . Hence, we can bound the  $l_{2}$  - norm of the distance  $\theta^t - X$  as follows:

$$
\| \theta^{t^{\prime}} - X\|_{2} = \| (1 - \frac{1}{\psi_{c}^{t^{\prime}}})\triangle \theta_{c}^{t^{\prime}} + \zeta \|_{2}\leq (\frac{1}{a} -1)\| \triangle \theta_{c}^{t^{\prime}}\|_{2} + \| \zeta \|_{2}
$$

Consequently, Theorem 2 holds.

# D. Proof of Theorem 3

Proof. We have that  $Error = \| X^{\prime} - X\|_{2}$

$$
= \| \sum_{c\in \mathcal{C}}\frac{\theta_c^t}{p|\mathcal{C}|} +\sum_{i\in \mathcal{L}}\frac{\theta_i^t}{(1 - p)(|N| - |\mathcal{C}|)} -X\| _2 = \| \sum_{c\in \mathcal{C}\cup \mathcal{L}}\frac{\theta_c^t}{|\mathcal{C}|} -X\| _2,
$$

in which  $\begin{array}{r}\| X^{\prime} - X\|_{2}\geq \| \sum_{c\in \mathcal{C}}\theta_{t}^{c} / (p|\mathcal{C}|) - X\|_{2} \end{array}$

$$
= \| \sum_{c\in \mathcal{C}}\frac{\triangle\theta_c^t}{p|\mathcal{C}|\psi_t^c}\| _2\geq \| \sum_{c\in \mathcal{C}}\frac{\triangle\theta_c^t}{p|\mathcal{C}|b}\| _2, \tag{17}
$$

$$
\begin{array}{r}\mathrm{and}\| \sum_{c\in \mathcal{C}\cup \mathcal{L}}\theta_c^t /|\mathcal{C}| - X\| _2\leq \arg \underset {L\subseteq N}{\max}\underset {|L| = |C|}{\max}\| \sum_{i\in \mathcal{L}}\theta_i^t /|L| - X\| _2. \end{array} \tag{14}
$$

From Eqs. 17 and 18, we have the following error bounds:

$$
\Big\| \sum_{c\in \mathcal{C}}\frac{\triangle\theta_c^t}{p|\mathcal{C}|b}\Big\| _2\leq Error\leq \arg \underset {L\subseteq N}{\max}\underset {s.t.}{\max}|L| = |C|\Big\| \sum_{i\in \mathcal{L}}\frac{\theta_i^t}{|L|} -X\| _2. \tag{19}
$$

As a result, Theorem 3 holds.

TABLE I: Robust federated training algorithms against backdoor attacks.  

<table><tr><td>Approach</td><td>Method</td><td>Description</td></tr><tr><td rowspan="6">Robust Aggregation</td><td>Krum / Multi-Krum [42]</td><td>Score each update based on its closeness to its neighbors; 
Take the average of top N updates as aggregated update</td></tr><tr><td>Median GD [32]</td><td>Use the element-wise median as aggregated update</td></tr><tr><td>Trim Mean GD [32]</td><td>Remove the top and bottom β percentage of collected updates; 
Use the element-wise mean as aggregated update</td></tr><tr><td>SigNSGD [43]</td><td>Adjust the server&#x27;s learning rate based on the agreement of client updates</td></tr><tr><td>Robust Learning Rate [44]</td><td>Count the updates in the same direction of aggregated update for each element; 
Flip the update in elements where the count is smaller than the threshold</td></tr><tr><td>Ditto [45]</td><td>Fine-tune the potentially corrupt global model on each client&#x27;s private data</td></tr><tr><td rowspan="3">Model Smoothness</td><td>Norm Bound [10]</td><td>Clip the gradients based on magnitude; Add Gaussian noise to the gradients</td></tr><tr><td>CRFL [46]</td><td>Clip model parameters to control model smoothness; Generate sample robustness certification</td></tr><tr><td>FLARE [47]</td><td>Estimate a trust score for each model update based on the differences between all pair of updates; Aggregate model updates weighted by the trust scores</td></tr><tr><td rowspan="2">Differential Privacy</td><td>DP-optimizer [33]</td><td>Clip the gradients collected from clients; Add Gaussian noise to the clipped gradients</td></tr><tr><td>User-level DP [48]</td><td>Add sufficient Gaussian noise to model updates for providing user-level DP</td></tr></table>

# E. Data and Model Configuration.

We conduct experiments on Sentiment [35] and FEMNIST [37] datasets. To control data distribution across clients in terms of classes and size of local training data, we leverage the symmetrical Dirichlet distribution with different values of the concentration parameter  $\alpha \in [0.01,100]$  as in [18]. In short, the value of  $\alpha$  is inversely proportional to the degree of diversity in data distribution. In Sentiment, we include 5,600 clients with over 1 million data samples. In FEMNIST, there are 3,400 clients with 805,263 samples. The client sampling rate  $q = 1\%$  and the dynamic learning rate  $\psi \sim \mathcal{U}[0.9,1]$ . We designate class 0 as the targeted class for the attacker, denoted as  $y^{T_{roj}}$ . We divide the data samples in each client into training (70%), testing (15%), and validation (15%) sets. The combined validation set from all compromised clients serves as the auxiliary set for training the Trojaned model  $X$ . In the following experiments, the attacker randomly compromised  $0.1\%$ ,  $0.5\%$ , and  $1\%$  of benign clients, treating these small percentages of compromised clients as a practical threat ([12]). This percentage is below the required number of compromised clients, as indicated in Theorem 1. Our empirical experiments show that COLLAPoIs maintains its effectiveness even when a smaller fraction of clients is compromised, thereby enhancing the overall efficacy, feasibility, and subtlety of our attack.

We adopt the model configuration described in [4] for all the datasets. Specifically, we use a LeNet- based network with two convolution and two fully connected layers for the local model, and a fully connected network with three hidden layers and multiple linear heads per target weight tensor. For the Sentiment dataset, we utilize the BERT model as the tokenizer and connect it with a two- layer fully connected network as the task head. We use SGD optimizer with the learning rate of 0.01 for the aggregated global model and 0.001 for the benign clients' local models.

# F. Visualization of WaNet [25] Triggers G. Supplemental Results

![](images/69de4e19cabc6b0d376c7950b9c76c01c1930ac11652405096e573b8eaa21cb0.jpg)  
Fig. 14: WaNet [25] in the FEMNIST dataset. Backdoor (right) and legitimate (left) samples are almost identical.

![](images/fb201ca73a10eb61b2b5fe76626f1261e4d23f943acba9b5dc5cd8c2b7e47b94.jpg)  
Fig. 15: FedAvg, FedDC, and MetaFed under attacks (1% compromised clients) in the FEMNIST dataset.

![](images/4f020a6651a2453efde6e5df9304708170f401927220b49b3457a08c88e346db.jpg)  
Fig. 16: COLLAPoIs (1% compromised clients) under defenses for the FEMNIST dataset. (Krum and RLR are not applicable for MetaFed.)  
Fig. 17: COLLAPoIs (0.1% compromised clients) under defenses for the Sentiment dataset in FedAvg, FedDC, and MetaFed.

![](images/2aa51590657f75046432740851c6c9db0d1e4efd9e430ea93fbb9be63bdf0c04.jpg)  
Fig. 18: COLLAPoIs  $(0.1\%)$  compromised clients) under defenses for the FEMNIST dataset in FedAvg, FedDC, and MetaFed.  Fig.19:COLLAPOIs  $0.5\%$  compromised clients) under defenses for the Sentiment dataset in FedAvg, FedDC, and MetaFed.

![](images/241863391b29d85f1928e8ee1ecb07a7cdfaa612594f9ade42feb759553ab20d.jpg)  
Fig. 20: COLLAPoIs  $(0.5\%)$  compromised clients) under defenses for the FEMNIST dataset in FedAvg, FedDC, and MetaFed.  Fig.21:COLLAPOIs  $0.5\%$  compromised clients) under defenses for the Sentiment dataset in FedAvg, FedDC, and MetaFed. (Top-  $1\%$  clients)

![](images/52b0ba2b6210425ca274ed282280cb964d41cfc00000f7739db9e1a985d64a35.jpg)  
Fig. 22: COLLAPoIs  $(0.5\%)$  compromised clients) under defenses for the FEMNIST dataset in FedAvg, FedDC, and MetaFed. (Top-  $1\%$  clients)  Fig.23:COLLAPOIs  $0.5\%$  compromised clients) under defenses for the Sentiment dataset in FedAvg, FedDC, and MetaFed. (Top-  $50\%$  clients)

![](images/6cabd23835fd7b2c3e3d60492201f98d5450c54ca852a8c5ee88addd58838c4a.jpg)  
Fig. 24: COLLAPoIs  $(0.5\%)$  compromised clients) under defenses for the FEMNIST dataset in FedAvg, FedDC, and MetaFed. (Top-  $50\%$  clients)  
Fig. 25: COLLAPoIs (under defenses) with  $0.1\%$  and  $0.5\%$  compromised clients for the FEMNIST dataset in FedAvg, FedDC, and MetaFed (Top  $25\%$  Clients). (In MetaFed, many clients have high Attack SR across values of  $\alpha$  and defenses. Top-1% infected clients have an average of Attack SR over  $99.5\%$  (Fig. 22,Appx. G))