# FedGA: A Fair Federated Learning Framework Based on the Gini Coefficient

Research

<NAME_EMAIL> Guangxi Key Laboratory of Trusted Software, Guilin University of Electronic Technology Guilin, Guangxi, China

# ABSTRACT

Fairness has emerged as one of the key challenges in federated learning. In horizontal federated settings, data heterogeneity often leads to substantial performance disparities across clients, raising concerns about equitable model behavior. To address this issue, we propose FedGA, a fairness- aware federated learning algorithm. We first employ the Gini coefficient to measure the performance disparity among clients. Based on this, we establish a relationship between the Gini coefficient  $G$  and the update scale of the global model  $U_{s}$ , and use this relationship to adaptively determine the timing of fairness intervention. Subsequently, we dynamically adjust the aggregation weights according to the system's real- time fairness status, enabling the global model to better incorporate information from clients with relatively poor performance. We conduct extensive experiments on the Office- Caltech- 10, CIFAR- 10, and Synthetic datasets. The results show that FedGA effectively improves fairness metrics such as variance and the Gini coefficient, while maintaining strong overall performance, demonstrating the effectiveness of our approach.

# CCS CONCEPTS

Computing methodologies  $\rightarrow$  Distributed algorithms.

# KEYWORDS

Federated Learning, Fairness, Data Heterogeneity, Gini Coefficient, Aggregation Weights

# ACM Reference Format:

ShanBin Liu. 2018. FedGA: A Fair Federated Learning Framework Based on the Gini Coefficient: Research. In Proceedings of Make sure to enter the correct conference title from your rights confirmation email (Conference acronym 'XX). ACM, New York, NY, USA, 14 pages. https://doi.org/XXXXXXX.XXXXXXXX

# 1 INTRODUCTION

The widespread use of connected devices generates vast distributed data crucial for training AI models. However, traditional centralized training raises privacy, ownership, and regulatory concerns, while single- device training suffers from limited data. These challenges motivated federated learning [27], which enables collaborative training without raw data transfer, preserving privacy while achieving comparable performance to centralized methods.

Yet federated learning faces fairness challenges due to heterogeneous client data distributions. Performance disparities across clients can undermine trust, participation, and model reliability- particularly critical in healthcare and finance where equitable performance is essential.

To address this, we propose FedGA, a fairness- aware federated learning algorithm that monitors Gini coefficient dynamics to determine optimal fairness intervention timing and adaptively adjusts optimization intensity based on client validation performance.

The main contributions of this work are as follows:

We investigate the relationship between the Gini coefficient and the global update scale during federated learning, and observe that they tend to decrease concurrently. Based on this observation, we propose a novel delayed fairness intervention strategy. We design an algorithm that dynamically adjusts aggregation weights based on client validation set performance. Additionally, we introduce a hyperparameter  $\lambda$  to control the degree of fairness intervention, enabling practitioners to flexibly balance fairness and accuracy according to specific requirements. We provide a theoretical guarantee that the aggregation weight of the best- performing client is always less than  $\frac{1}{n}$ , while the weight of the worst- performing client is always greater than  $\frac{1}{n}$ , where  $n$  denotes the number of participating clients in each communication round. We analyze the time complexity of the delayed fairness intervention strategy. Compared to FedGini, our method FedGA exhibits lower computational complexity when the number of clients is smaller than the number of model parameters. We theoretically establish the relationship between the Gini coefficient and average sum of accuracy differences among clients (denoted as AvgDiff) during the later stages of federated learning training, where the global model has largely stabilized. Specifically, we derive a first- order approximation showing that changes in Gini and mean accuracy jointly influence AvgDiff, thereby providing a formal link between fairness metrics and client- level performance consistency.

We conduct extensive experiments on two real- world datasets and one synthetic dataset. Specifically, feature shift and label shift are simulated on the Office- Caltech- 10 and CIFAR- 10 datasets to represent two distinct types of heterogeneity. The

experimental results validate the effectiveness of the proposed method in improving both fairness and performance.

# 2 BACKGROUND AND MOTIVATION

2 BACKGROUND AND MOTIVATIONIn this section, we first introduce the optimization objectives of federated learning in Section 2.1. Then, in Sections 2.2 and 2.3, we present the definitions and evaluation metrics of fairness in federated learning. Finally, in Section 2.4, we discuss our motivation- how to search for an appropriate timing for fairness intervention while keeping the computational overhead minimal.

# 2.1 Optimization Goals of Federated Learning

The federated learning process consists of three iterative steps: (1) server distributes the global model to selected clients; (2) clients train locally on private data and upload updated models; (3) server aggregates client models to form a new global model.These steps continue until convergence or a predefined number of rounds. The optimization objective is:

$$
\min_w f(w) = \sum_{k = 1}^m p_k F_k(w) \tag{1}
$$

$$
F_{k}(w) = \frac{1}{n_{k}}\sum_{j_{k = 1}}^{n_{k}}l_{j_{k}}(w) \tag{2}
$$

The formal description of this objective is as follows:

$$
\min_w f(w) = \left\langle w,w,k,P_k,F_k(w),n_k,j_k,l_{jk}(w)\right\rangle \tag{3}
$$

Where:

1.  $f(w)$  is the global optimization objective of federated learning. 
2.  $\mathcal{W}$  is the global model of federated learning. 
3.  $m$  is the total number of clients participating in this round of training. 
4.  $k$  is the index of the client. 
5.  $P_{k}$  is the aggregation weight of client  $k.P_{k}\geq 0$  and  $\sum_{k = 1}^{m}p_{k} =$  1.Typically,  $\begin{array}{r}p_k = \frac{n_k}{n} \end{array}$  or  $\begin{array}{r}p_k = \frac{1}{m} \end{array}$  , where  $n$  is the total size of the dataset owned by all devices participating in this round of federated learning. 
6.  $F_{k}$  is the local optimization objective of client  $k$  
7.  $n_k$  is the amount of data owned by client  $k$  
8.  $j_{k}$  is the index of a data sample. 
9.  $l_{jk}(w)$  is the loss function of the global model parameter  $\mathcal{W}$  in sample  $j_{k}$

# 2.2 Definition of Fairness In Federated Learning And Metrics For Measuring Fairness

Our definition of fairness follows Li et al. [20].For two models  $\mathbb{W}_1$  and  $\mathbb{W}_2$  , if the performance distribution  $\{\mathbf{a}_1^{w_1},\dots,\mathbf{a}_n^{w_1}\}$  of model  $\mathbb{W}_1$  is more uniform than the performance distribution  $\{\mathbf{a}_1^{w_2},\dots,\mathbf{a}_n^{w_2}\}$  of model  $\mathbb{W}_2$  , then model  $\mathbb{W}_1$  is considered to be fairer than  $\mathbb{W}_2$  Here,  $a_i^w$  denotes the performance of model  $\mathcal{W}$  on client  $i$  which can be either accuracy or loss. In this work, We adopt standard deviation and Gini coefficient as fairness metrics, where lower values indicate more uniform client performance distribution and thus fairer federated learning outcomes.

# 2.3 Gini Coefficient

The Gini coefficient [7], proposed by Corrado Gini based on the Lorenz curve, was originally designed to measure wealth inequality on a scale from 0 (perfect equality) to 1 (maximal inequality). In federated learning, it quantifies client performance imbalance: a value of O indicates identical performance across all clients (perfect fairness), while 1 represents extreme unfairness where only one client benefits from the global model. The formal definition of the Gini coefficient is given as follows:

$$
G = \frac{\sum_{i = 1}^{n}\sum_{j = 1}^{n}\left|x_{i} - x_{j}\right|}{2\left(\mathbf{n} - 1\right)\sum_{j = 1}^{n}x_{j}} \tag{4}
$$

where  $x_{i}$  denotes the accuracy of client  $i,$  and  $n$  denotes the total number of clients.

# 2.4 Motivation

Most existing fairness optimization algorithms initiate intervention from the early stages of federated learning, which may inadvertently undermine fairness[24]. To mitigate this, Li et al. [24] proposed FedGini, which adaptively determines the intervention timing by monitoring the global update scale  $U_{s}$  .While effective in avoiding premature intervention, this method incurs high computational complexity.

To address this limitation, we propose FedGA, a lightweight alternative that preserves adaptive fairness scheduling with significantly reduced overhead. As shown in Section 4.2, the time complexity of FedGini is  $O(p\times q\times n)$  ,where  $p\times q$  is the number of model parameters, and  $n$  is the number of clients participating in each round. This complexity increases with the size of the model and client population, which may present practical challenges in largescale deployments. With the emergence of large language models (LLMs) such as ChatGPT and Claude, which contain hundreds of millions of parameters, the computational burden becomes especially pronounced. By contrast, FedGA reduces the time complexity to  $O\left(n^{2}\right)$  , where  $n$  denotes the number of clients per round. This design offers improved scalability and training efficiency, making FedGA better suited for large- scale federated learning scenarios.

# 3 THE DESIGN OF FEDGA

3 THE DESIGN OF FEDGAFedGA comprises two main components: a delayed fairness intervention strategy and dynamic adjustment of aggregation weights.

# 3.1 Delayed Fairness Intervention Strategy Based on Gini Coefficient Aware

Geyer et al. [6] proposed two definitions: the update scale  $U_{s}$  and the sum over all parameter variances in the update matrix  $V_{c}$

Definition 1: The update scale  $U_{s}$  .Let  $\Delta w_{i,j}$  define the  $(i,j)th$  parameter in an update of the form  $\Delta w\in R^{p\times q}$  , at some communication round  $t$  . For the sake of clarity, we will drop specific indexing of communication rounds for now. The parameter  $(i,j)$  in  $\Delta W$  is computed as  $\begin{array}{r}\mu_{i,j} = \frac{1}{K}\sum_{k = 1}^{K}\Delta \mathbf{w}_{i,j}^{k} \end{array}$  where  $\Delta \mathbf{w}_{i,j}^{k}$  is the  $(i,j)th$  parameter in the update of  $\Delta \mathbf{w}^k$ $k$  is the index of the client participating in the current round of federated learning, and  $K$  is the number

of clients participating in the current round of federated learning. We then define the update scales as the sum over all parameter variances in the updated matrix  $\Delta W$ :

$$
U_{s} = \frac{1}{p\times q}\sum_{i = 0}^{p}\sum_{j = 0}^{q}\mu_{i,j}^{2} \tag{5}
$$

It represents the extent of change in the global model during one round of communication.

Definition 2: The variance of parameters  $(i,j)$  throughout all  $K$  clients is defined as:

$$
VAR\left[\Delta w_{i,j}\right] = \frac{1}{K}\sum_{k = 0}^{K}\left(\Delta w_{i,j}^{k} - \mu_{i,j}\right)^{2} \tag{6}
$$

Definition 3: We the define  $V_{c}$  as the sum over all parameter variances in the update matrix:

$$
V_{c} = \frac{1}{q\times p}\sum_{i = 0}^{q}\sum_{j = 0}^{p}VAR\left[\Delta w_{i,j}\right] \tag{7}
$$

Geyer et al. [6] mentioned that federated learning can be divided into two stages: the label fitting stage and the data fitting stage. During the label fitting phase, client updates are more similar, so the sum over all parameter variances in the update matrix  $V_{c}$  is relatively small, while the global model update scale  $U_{s}$  is relatively large because there are significant updates to the randomly initialized weights. During the data fitting phase,  $V_{c}$  gradually increases as each client optimizes towards its own dataset. At the same time,  $U_{s}$  gradually decreases as it approaches the local optimum of the global model, with accuracy converging and contributions partially offsetting each other to some extent.

Li et al. [25] used this conclusion to propose a delayed fairness intervention method, utilizing the trend of the global model update scale  $U_{s}$  to determine the intervention time. The specific method is as follows:

$$
\Delta U_s^t = \frac{1}{D}\sum_{i = t - D}^{t - 1}U_s^i -\frac{1}{D}\sum_{i = t - D - 1}^{t - 1}U_s^i < \eta \tag{8}
$$

Where  $U_{s}$  represents the global model update scale,  $D$  represents the size of the sliding window  $t$  represents the current update round, and  $\eta$  represents the threshold for determining whether to start the fairness intervention. It can be observed that FedGini requires computing the global model's update scale each round, which may introduce substantial computational overhead—especially in neural networks with a large number of parameters. This poses scalability challenges for training large language models under federated learning frameworks.

To mitigate this issue, we revisit the underlying relationship between  $U_{s}$  and the Gini coefficient  $G$ . As illustrated in Figure 1 using the FedAvg algorithm on the Synthetic_0_0 dataset, the trajectories of  $U_{s}$  and the Gini coefficient  $G$  exhibit highly similar trends during training. Both metrics decrease substantially and almost synchronously during the early stages of training, suggesting that the Gini coefficient  $G$  may serve as an efficient proxy for  $U_{s}$  in determining the timing for fairness intervention.

Building on this insight, we propose a lightweight alternative by replacing  $U_{s}$  with the Gini coefficient in the fairness trigger mechanism. The revised condition is given by:

![](images/cebfdcac51e54600aef7c2789f7ddaf22e8e5d83f125467f39b77f5b18d4f445.jpg)  
Figure 1: Relationship between Global Update Scale and Gini Coefficient.

$$
\Delta G = \frac{1}{D}\sum_{i = t - 2D}^{t - D}G^i -\frac{1}{D}\sum_{i = t - D}^{t - 1}G^i < \eta \tag{9}
$$

# 3.2 Dynamic Aggregation Weight Adjustment Algorithm Based on Accuracy

Under non- IID distributions, heterogeneous client data leads to discrepant local models. Standard aggregation favors high- quality data clients, marginalizing those with less representative data. To enhance fairness, we adapt aggregation weights based on the global model's performance on client validation sets. Our approach assigns higher weights to underperforming clients and lower weights to well- performing ones, with weight adjustments proportional to performance disparities. This mechanism ensures balanced representation of all clients' local models in the global model, particularly benefiting underrepresented data distributions.

The specific algorithm for dynamic weight adjustment is presented below:

$$
weight_{i} = 1 - a_{i} \tag{10}
$$

$$
weight_{i} = \frac{weight_{i}}{\sum_{i = 1}^{n}weight_{i}}\times \lambda \tag{11}
$$

$$
exp_{i} = e^{\frac{weight_{i}}{exp_{i}}} \tag{12}
$$

$$
weight_{i} = \frac{n}{\sum_{i = 1}^{n}exp_{i}} \tag{13}
$$

Where weighti is the aggregation weight of the ith device, and  $a_{i}$  is the validation accuracy of the ith device,  $\exp_{i}$  represents the weighti power of e. Equations (10) and (11) are designed to decrease the proportion of aggregation weights for better- performing clients and increase the proportion for worse- performing clients. A softmax normalization is then used to magnify the weights of lower- accuracy devices, allowing the global model to learn more from these devices

during aggregation, thereby encouraging the global model to learn more from underperforming clients and improving overall fairness. The hyperparameter  $\lambda$  controls the strength of fairness intervention: a larger  $\lambda$  results in more emphasis on fairness, with values  $\lambda > 1$  typically used in practice.

![](images/3a5d3f6cd1067b4bffe6036a6c689928d948d46f17b41cb3e12ecf149ad313e6.jpg)  
Figure 2: Overview of the FedGA Algorithm Workflow.

To provide an intuitive understanding of the proposed method, Figure 2 illustrates the overall workflow of FedGA. The algorithm first evaluates whether fairness intervention should be applied by monitoring the change in the Gini coefficient across communication rounds. If the change falls below a predefined threshold, fairness- aware aggregation is triggered, assigning higher weights to underperforming clients to improve fairness in the global model. Otherwise, standard aggregation is performed. Algorithm 1 presents the pseudocode of FedGA.

# 4 THEORETICAL ANALYSIS

In this section, we conduct a series of theoretical analyses. Section 4.1 analyzes the communication overhead of our method and compares it with that of FedAvg. Section 4.2 evaluates the time complexity of FedGA in identifying the optimal intervention timing, and compares it with FedGini. In Section 4.3, we prove that FedGA consistently assigns an aggregation weight greater than  $\frac{1}{n}$  to the worst- performing client and less than  $\frac{1}{n}$  to the best- performing client. Section 4.4 explores the relationship between the Gini coefficient and the mean of the total performance disparity among clients.

# 4.1 Communication Overhead Analysis

Compared to the Federated Averaging algorithm (FedAvg), FedGA introduces only a minimal communication overhead by requiring each client to upload the accuracy of the global model on its local validation set. Assuming this accuracy is represented using single- precision floating- point format, each value occupies  $4B$  of memory. Since this information is only transmitted from the client to the server and does not need to be returned, the additional communication overhead is limited to  $4B$  per client.

In contrast, the communication overheads for the AlexNet and

# Algorithm 1 Gini Coefficient-aware Fair Federated Learning (FedGA)

Input: Number of communication rounds  $T$ , number of local iterations  $E$ , initial aggregation weight  $p$  Output: Optimal global model  $W_{\mathrm{op}}$

1:  $t\gets 0$  2:while  $t\leq T - 1$  do 3: Client  $(W_{s})$  4: Evaluate the global model on validation set  $a_{k}\gets$ $W_{s}(D_{k})$  5: for  $e = 1$  to  $E$  do 6:  $W_{k}^{1} + \leftarrow W_{s} - \eta \nabla F(W_{s})$  7: end for 8: Return  $W_{t + 1}^{k}$  and  $a_{k}$  9: Server: 10: Randomly select  $m$  clients from  $M\colon S_t\subset M$  11: for each client  $k\in S_t$  in parallel do 12:  $W_{t + 1}^{k}\gets \mathrm{Client}(W_{s})$  13: end for 14: Server updates accuracy list  $A c c = [a_{1},\dots,a_{m}]$  15: Compute Gini coefficient:

$$
G = \frac{\sum_{i = 1}^{n}\sum_{j = 1}^{n}|x_i - x_j|}{2(n - 1)\sum_{j = 1}^{n}x_j}
$$

16: Compute  $\Delta G = \frac{1}{D}\sum_{i = t - 2D}^{t - D}G^i - \frac{1}{D}\sum_{i = t - D}^{t}G^i$  17: if  $\Delta G< \eta$  then 18:  $weight_{t} = 1 - a_{t}$  19:  $weight_{t}\gets \frac{weight_{t}}{\sum_{j = 1}^{n}weight_{t}}\times \lambda$  20:  $exp_{i}\gets e^{\frac{weight_{i}}{weight_{t}}}$  21:  $weight_{t}\gets \frac{exp_{i}}{\sum_{j = 1}^{n}exp_{j}}$  22: else 23:  $W^{t + 1}\gets \sum_{k\in S_t}w_k^a\cdot W_k^{t + 1},w_k^a = \frac{n_k}{n}$  24: end if 25: end while

ResNet- 18 models used in our experiments are  $2\times 49.5MB = 99MB$  and  $2\times 44.6MB = 89.2MB$ , respectively. Therefore, we conclude that FedGA introduces negligible communication overhead and does not impose a significant burden on federated learning systems.

# 4.2 Algorithm Complexity Analysis

To compare the time complexity between the delayed fair intervention method proposed by FedGA and that by FedGini. We provide the following analysis:

FedGini requires the calculation of the following two formulas:

$$
\begin{array}{c}{\mu_{i,j} = \frac{1}{K}\sum_{k = 1}^{K}\lambda w_{i,j}^{k}}\\ {U_{s} = \frac{1}{p\times q}\sum_{i = 0}^{p}\sum_{j = 0}^{q}\mu_{i,j}^{2}} \end{array} \tag{15}
$$

Formula 14 includes a summation operation, summing from  $k = 1$  to  $k = k$ , a total of  $k$  times. For each combination of  $i$  and  $j$ , the summation operation runs  $k$  times. The time required for each

summation operation is a constant time operation (i.e., calculating  $\Delta w_{i,j}^{k}$  and adding it to the sum). Therefore, the time complexity of the entire summation operation can be expressed as  $O(K)$ .

Formula 15 includes two nested summation operations. The outer summation runs from  $i = 0$  to  $i = p$  a total of  $p + 1$  times. The inner summation runs from  $j = 0$  to  $j = q$  a total of  $q + 1$  times. For each value of  $i$  the inner summation operation runs  $q + 1$  times. Thus, the total number of summation operations is  $(p + 1)\times (q + 1)$  times. Therefore, the time complexity of the entire summation operation can be expressed as  $O((q + 1)\times (p + 1))$  . Since constant factors can be ignored in Big O notation, the time complexity simplifies to  $O(q\times p)$

However, in formula 15, each calculation of  $\mu_{i,j}$  involves formula 14. Since the calculation of  $\mu_{i,j}$  requires  $O(K)$  time, the time for calculating each  $\mu_{i,j}$  in formula 15 will also be  $O(K)$ . Therefore, the time complexity of computing the entire formula (15) is  $O(q\times p)\times O(k) = O(q\times p\times k)$ . For ease of comparison with FedGA, the time complexity of FedGini is expressed as  $O(q\times p\times n)$ .

FedGA requires the calculation of the Gini coefficient, and the time complexity of this calculation is as follows:

The numerator includes two nested summation operations:  $\sum_{i = 1}^{n}\sum_{j = 1}^{n}\left|x_{i} - x_{j}\right|$  According to the properties of the exponential function with base The outer summation runs from  $i = 1$  to  $i = n$  a total of  $n$  times, and the inner summation runs from  $j = 1$  to  $j = n$  also a total of  $n$  times. Therefore, the total number of summation operations is  $n\times n = n^2$  times. Calculating  $\left|x_{i} - x_{j}\right|$  is a constant time operation (assuming the absolute value operation is  $O(1)$  ). Thus, the time complexity of the numerator is  $O(n^{2})$  .The denominator includes two operations:  $2(n - 1)\sum_{j = 1}^{n}x_{j}$  Calculating  $\sum_{j = 1}^{n}x_{j}$  requires summing  $n$  elements, with a time complexity of  $O(n)$  . Multiplying by 2 (n- 1) is a constant time operation,  $O(1)$  . Thus, the time complexity of the denominator is  $O(n)$

Since the time complexity of the denominator  $O(n)$  is lower than that of the numerator  $O(n^{2})$ , the overall time complexity is determined by the numerator. Therefore, the total time complexity is  $O(n^{2})$ .

In this context,  $k$  and  $n$  in the algorithm complexity represent the number of clients participating in federated learning training. When the number of neural network parameters  $q\times p$  is greater than  $n$ , the computational overhead of FedGA computational overhead is lower than that of FedGini [24].

# 4.3 Analysis of Aggregation Weights

Let the set of clients participating in the current round of federated learning training be  $N$ . For client  $i$ , after the computation of the dynamic aggregation adjustment algorithm, the weight is:

$$
Weight_{z} = \frac{e^{\frac{x_{z}}{\sum_{j = 1}^{n}x_{j}}\times\lambda}}{\sum_{i = 1}^{n}e^{\frac{x_{i}}{\sum_{j = 1}^{n}x_{j}}\times\lambda}} \tag{16}
$$

Where  $n$  is the number of clients, and  $x_{z} = 1 - \alpha_{z}$ . Dividing both the numerator and the denominator by the numerator, we get:

$$
\begin{array}{r}{Weight_{z} = \frac{1}{\sum_{i\neq z}e^{\frac{n}{\sum_{j = 1}^{n}x_{j}}\times\lambda}}}\\ {1 + \frac{\sum_{i\neq z}e^{\frac{n}{\sum_{j = 1}^{n}x_{j}}\times\lambda}}{\sum_{i = 1}^{n}e^{\frac{n}{\sum_{j = 1}^{n}x_{j}}\times\lambda}}} \end{array} \tag{17}
$$

According to the laws of exponents, we can derive:

$$
\begin{array}{r}{Weight_{z} = \frac{1}{\lambda\times(x_{i} - x_{z})}}\\ {1 + \sum_{i\neq z}^{n}e^{\frac{\sum_{j = 1}^{n}x_{j}}{\sum_{j = 1}^{n}x_{j}}}} \end{array} \tag{18}
$$

Assuming that client  $z$  is the best- performing client, then  $x_{z}$  is the smallest among all clients, and  $\lambda >0$ . For  $\forall i\in N\backslash \{z\}$ , we have:

$$
\lambda \times (x_{i} - x_{z}) > 0 \tag{19}
$$

Therefore:

$$
\frac{\lambda\times(x_i - x_z)}{\sum_{j = 1}^nx_j} >0 \tag{20}
$$

$e$ , we obtain:

$$
e^{\frac{\lambda\times(x_i - x_z)}{\sum_{j = 1}^nx_j}} > 1 \tag{21}
$$

Therefore:

$$
1 + \sum_{i\neq z}^{n}e^{\frac{\lambda\times(x_i - x_z)}{\sum_{j = 1}^{n}x_j}} > n \tag{22}
$$

Therefore:

$$
Weight_{z} = \frac{1}{\frac{\lambda\times(x_{i} - x_{z})}{\sum_{i\neq z}^{n}e^{-\frac{n}{\sum_{j = 1}^{n}x_{j}}}}}< \frac{1}{n} \tag{23}
$$

Assuming that client  $z$  is the worst- performing client, then  $x_{z}$  is the largest among all clients, and  $\lambda >0$ . For  $\forall i\in N\backslash \{z\}$ , we have:

$$
\lambda \times (x_{i} - x_{z})< 0 \tag{24}
$$

Therefore:

$$
\frac{\lambda\times(x_{i} - x_{z})}{\sum_{j = 1}^{n}x_{j}} < 0 \tag{25}
$$

According to the properties of the exponential function with base  $e$ , we obtain:

$$
\frac{\frac{\lambda\times(x_{i} - x_{z})}{n}}{e^{\frac{n}{\sum_{j = 1}^{n}x_{j}}}} < 1 \tag{26}
$$

Therefore:

$$
1 + \sum_{i\neq z}^{n}e^{\frac{\lambda\times(x_{i} - x_{z})}{\sum_{j = 1}^{n}x_{j}}} > n \tag{27}
$$

Therefore:

$$
Weight_{z} = \frac{1}{\frac{\lambda\times(x_{i} - x_{z})}{\sum_{j = 1}^{n}x_{j}}} >\frac{1}{n} \tag{28}
$$

# 4.4 Proof of the Relationship Between the Gini Coefficient and the Mean Accuracy Disparity Among Clients in Federated Learning

Definition 4: The average sum of accuracy differences among  $\sum_{i\neq j}\left|x_{i} - x_{j}\right|$  clients, denoted as  $\begin{array}{r}A\nu gDiff = \frac{n(n - 1)}{n(n - 1)} \end{array}$  , is introduced to characterize the consistency of client performance and the generalization ability of the global model under heterogeneous data distributions. Since:

$$
\sum_{i\neq j}\left|x_{i} - x_{j}\right| = \sum_{i = 1}^{n}\sum_{j = 1}^{n}\left|x_{i} - x_{j}\right| - \sum_{i}\left|x_{i} - x_{i}\right| = \sum_{i = 1}^{n}\sum_{j = 1}^{n}\left|x_{i} - x_{j}\right|_{\mathrm{lg}}\mathrm{rate~was~set~to~0.1~with~a~batch~size~of~64. In~both~cases,~the~} \tag{29}
$$

According to Equation 4, the Gini coefficient can be expressed as:

$$
G = \frac{\sum_{i = 1}^{n}\sum_{j = 1}^{n}\left|x_{i} - x_{j}\right|}{2(n - 1)\sum_{i = 1}^{n}x_{i}} = \frac{\sum_{i = 1}^{n}\sum_{j = 1}^{n}\left|x_{i} - x_{j}\right|}{2n(n - 1)\mu} = \frac{\sum_{i\neq j}\left|x_{i} - x_{j}\right|}{2n(n - 1)\mu} \tag{30}
$$

where  $\begin{array}{r}\mu = \frac{\sum_{i = 1}^{n}x_{i}}{n} \end{array}$  denotes the mean accuracy across all clients. Therefore,  $AvgDiff(\mu ,G) = 2\mu G$  . Taking the total differential of  $AvgDiff(\mu ,G)$  we have:

$$
dAvgDiff = \frac{\partialAvgDiff}{\partial\mu} d\mu +\frac{\partialAvgDiff}{\partial G} dG \tag{31}
$$

By computing the partial derivatives, it follows that:

$$
\frac{\partialAvgDiff}{\partial\mu} = 2G,\frac{\partialAvgDiff}{\partial G} = 2\mu \tag{32}
$$

Thus,  $dAvgDiff = 2(\mu dG + Gd\mu)$  .By performing a first- order Taylor expansion at the point  $(\mu ,G)$  we obtain:

$$
AvgDiff(\mu +\Delta \mu ,G + \Delta G)\approx AvgDiff(\mu ,G) + \frac{\partialAvgDiff}{\partial\mu}\Delta \mu +\frac{\partialAvgDiff}{\partial G}\Delta G\mathrm{he~best~performance~achieved~by~each}.
$$

Therefore,  $\Delta AvgDiff\approx 2G\Delta \mu +2\mu \Delta G = 2(\mu \Delta G + G\Delta \mu)$  The approximation error is of the order  $O\left(\Delta \mu \Delta G,\Delta \mu^{2},\Delta G^{2}\right)$  ,and the first- order expansion is valid when higher- order terms are negligible, such as in the late stage of training where fluctuations are small.

Assuming that in the late stage of training the mean accuracy  $\mu$  is  $70\%$  and the Gini coefficient  $G$  is 0.1, when  $\Delta G$  decreases by 0.01 and  $\Delta \mu$  increases by 0.01, we have:

$$
\Delta AvgDiff\approx 2\times (0.7\times -0.01 + 0.1\times 0.01) = -0.012 \tag{34}
$$

This indicates that the average pairwise accuracy difference among clients is reduced by approximately  $1.2\%$

# 5 EXPERIMENTS

In this section, we present the empirical results to demonstrate the effectiveness of the proposed FedGA algorithm. Section 5.1 details the datasets and experimental setup. Section 5.2 provides an analysis of the main experimental results. In Section 5.3, we investigate the impact of hyperparameters. Section 5.4 presents the results of the ablation studies. Finally, Section 5.5 compares the computational efficiency of FedGA with that of FedGini.

# 5.1 Datasets

We use two real- world datasets and one synthetic dataset: OfficeCaltech- 10 [8], CIFAR- 10 [16], and synthetic [19]. The Office- Caltech10 dataset simulates a feature heterogeneous scenario, while the CIFAR- 10 dataset simulates a label heterogeneous scenario.

Experimental Details: We conducted experiments on the Office10 dataset using two different network architectures: AlexNet and ResNet18, referred to as office10_alexnet and office10_resnet18, respectively. For the experiments with AlexNet, we set the learning rate to 0.01 and the batch size to 32, while for ResNet18, the learnStochastic Gradient Descent (SGD) optimizer was employed, with one local epoch per round and a total of 400 communication rounds. For the CIFAR- 10 dataset, we adopted the ResNet18 architecture [9], using the SGD optimizer with a learning rate of 0.1, a batch size of 64, one local epoch, and a total of 600 communication rounds. The data were partitioned using a Dirichlet distribution with a concentration parameter of 0.1, referred to as CIFAR- 01. On the synthetic dataset, We employ a simple linear classification model consisting of a single fully connected layer that maps the input feature vector to the output class logits. The learning objective was to optimize parameters  $W$  and  $b$  using the SGD optimizer with a learning rate of 0.01, a batch size of 32, one local epoch, and 200 communication rounds. For all experiments, we performed five independent runs with different random seeds and reported the mean and standard deviation of the results.

Baselines: We compare our method against the following representative baselines: FedAvg [27], AFL [28], FedProx [19], q- FedAvg [20], FedFa [12], Fedmgda+ [10], FedFV [32], and FedGini [24]. To ensure a fair comparison, we adopted the hyperparameter configurations summarized in Table 1 to validate all methods, and reported

Table 1: Hyperparameters of Baseline Methods  

<table><tr><td>Method</td><td>Parameters</td></tr><tr><td>AFL</td><td>ηλ∈{0.01, 0.1, 0.5}</td></tr><tr><td>q-FedAvg</td><td>q∈{0.1, 0.2, 1.0, 2.0, 5.0}</td></tr><tr><td>FedFa</td><td>β∈{0.0, 0.5, 1.0}</td></tr><tr><td>Fedmgda+</td><td>ε∈{0.01, 0.05, 0.1, 0.5, 1.0}</td></tr><tr><td>FedFV</td><td>α∈{0.1, 0.3, 0.5, 1.0}, τ∈{0, 1, 3, 10}</td></tr><tr><td>FedGini</td><td>ε∈{0, 0.5, 1}</td></tr></table>

# 5.2 Experimental Results Analysis

In this section, we present experimental results on three datasets and conduct a detailed analysis. For clarity and fairness, all figures and tables report the performance of each baseline under its bestperforming hyperparameter settings.

Figure 3 presents the average test accuracy of the bottom  $10\%$  of clients across various federated learning methods on the cifar01 dataset. This metric highlights how well different algorithms serve the most disadvantaged clients. FedGA achieves the highest bottom-  $10\%$  accuracy  $(38.85\pm 3.41\%)$  , outperforming all baselines. This indicates that FedGA provides support for clients in the

![](images/b7dd642c37ba5ea8f7fa243dedafdfadb3063a738f1f817e476a6707fb12a43d.jpg)  
Average Accuracy of Bottom  $10\%$  Clients on the cifar01 Dataset

worst- case regime, ensuring that even clients with adverse data conditions receive a model that performs reliably. Compared to Fe $\mathrm{dAvg}$ $(30.64\pm 2.22\%)$  and FedProx  $(32.58\pm 3.62\%)$  ,FedGA improves tail client accuracy by approximately 6- 8 percentage points, indicating a meaningful reduction in client- level disparity.

![](images/d394b5a97e7c0758fc5dbb763850aeb9bda86423fa2ac48d4642245ec4abd9bc.jpg)  
Figure 3: Average Test Accuracy of the Bottom  $10\%$  Clients on the CIFAR-01 Dataset. Figure 4: Fairness Gap Measured by Gini Relative to FedAvg on the CIFAR-01 Dataset.

Figure 4 shows Gini values for federated learning algorithms on the FedAvg baseline. Gini quantifies the Gini coefficient difference between methods and FedAvg; negative values indicate improved fairness. FedGA achieves the largest improvement (Gini  $= - 0.028$ ), representing substantial fairness enhancement while confirming its effectiveness in reducing performance gaps across heterogeneous federated settings. Other fairness- enhancing methods show smaller improvements: FedFV and FedGini achieve Gini  $= - 0.023$ , indicating moderate inequality reductions. Meanwhile, FedFa demonstrates minimal improvement (Gini  $= - 0.019$ ) and q- FedAvg (Gini  $= - 0.016$ ) shows slight fairness degradation, indicating less improvement relative to FedAvg.

Table 2 reports the standard deviation (Std) of client accuracies across various federated learning algorithms on the Cifar01 dataset. With a standard deviation of  $11.54\pm 1.48$  FedGA outperforms all baselines in minimizing inter- client performance variability.

Table 2: Comparison of Client Accuracy standard deviation Across Federated Learning Methods on the Cifar-01 dataset  

<table><tr><td>Method</td><td>Std</td></tr><tr><td>FedAvg</td><td>14.49±0.80</td></tr><tr><td>FedProx</td><td>13.65±1.65</td></tr><tr><td>q-FedAvg | q = 0.1</td><td>12.89±0.96</td></tr><tr><td>FedFa | β = 0</td><td>13.24±1.71</td></tr><tr><td>FedMgda+ | ε = 0.01</td><td>13.78±1.03</td></tr><tr><td>FedFV | α = 0.1, r = 10</td><td>12.17±1.48</td></tr><tr><td>FedGini | ε = 1.0</td><td>12.18±0.79</td></tr><tr><td>FedGA</td><td>11.54±1.48</td></tr></table>

Algorithms such as FedFV  $(12.17\pm 1.48)$ , FedFa  $(13.24\pm 1.71)$ , and q- FedAvg  $(12.89\pm 0.96)$  also reduce variability relative to conventional methods but still fall short of FedGA's level of uniformity.

![](images/e3e06b779dbeccc1ae40ff4555bd60cd830b58d794e621a5aa0a7fa8f393940e.jpg)  
Figure 5: Smoothed Gini Coefficient Trajectories and AUC-Based Fairness Comparison on the Office10_Alexnet Dataset.

Figure 5 shows smoothed Gini coefficient trajectories over communication rounds with corresponding Area Under Curve (AUC) values for federated learning algorithms on the office10 dataset. FedGA achieves the lowest AUC(31.98), indicating superior cumulative fairness compared to FedAvg(33.29), FedProx(37.13), FedGini(34.59), and FedFV(34.49). FedGA demonstrates notable Gini coefficient reduction during training, consistently reaching low levels  $(\approx 0.1)$  after 50 rounds, suggesting effective fairness enhancement and reduced client performance disparities. These results highlight FedGA's potential for addressing feature heterogeneity challenges in federated learning.

Figure 6 illustrates the smoothed Gini trajectories for various federated learning algorithms compared to FedAvg on the office10_alexnet dataset over 400 communication rounds. Gini measures the Gini coefficient difference between each method and FedAvg, with negative values indicating reduced client performance inequality. FedGA consistently maintains negative Gini values throughout most training rounds, demonstrating superior fairness performance. In contrast, other fairness- oriented methods (FedFV and FedGini) exhibit predominantly positive Gini values, suggesting ineffective fairness protection, potentially due to their lack of optimization for full client participation scenarios.

![](images/9746344179238ec2f89ec5fbb16e694a46168543a9ef07d3fc8e2e98d8466b3d.jpg)  
Figure 6: Smoothed Fairness Gap Trajectories (Gini) Relative to FedAvg on the Office10_Alexnet Dataset.

![](images/023534ce3791833dc0960a368f0d867b107880c83d864d0a81611c9848912ec6.jpg)  
Figure 7: Performance-Fairness Trade-off of Federated Learning Algorithms on the Office10 Dataset (ResNet18 network).

Figure 7 depicts the fairness- performance trade- off for different federated learning algorithms on the Office10 dataset using ResNet18. Each point represents a method's average test accuracy versus Gini coefficient. FedGA achieves the optimal balance with the highest accuracy  $(68.23\pm 0.28\%)$  and lowest Gini coefficient  $(0.06673\pm 0.01113)$ , effectively promoting both global utility and fairness. Fairness- aware baselines (FedGini:  $65.59\pm 1.78\%$ $0.09155\pm 0.02196$ ; FedFa:  $62.91\pm 4.37\%$ $0.09408\pm 0.03066$ ) show improved fairness compared to FedAvg but at the cost of reduced accuracy. These results demonstrate FedGA's ability to mitigate client- level performance disparities without sacrificing global performance, while other fairness- oriented methods face inherent trade- offs between these objectives.

Figure 8 displays the training and test accuracy trajectories over 400 communication rounds for federated learning methods on the Office10 dataset using ResNet18. FedGA exhibits rapid convergence and strong generalization, reaching over  $80\%$  training accuracy within 40 rounds and maintaining the highest test accuracy above $68\%$  throughout training. This performance demonstrates both optimization efficiency and robustness to overfitting under heterogeneous client distributions. In contrast, fairness- oriented methods (FedFa, FedFV, FedGini) achieve test accuracies between  $62 - 66\%$  showing slower convergence and weaker generalization compared to FedGA. These results highlight FedGA's effectiveness in handling feature heterogeneity while maintaining stable generalization performance.

![](images/59f13d195cf19ace4f9da36dcbe949a499d9e8cc349ff3f8e81194c767daa027.jpg)  
Figure 8: Convergence and Generalization Performance on the Office10 Dataset with ResNet18 network.

Table 3: Standard Deviation of Client Accuracy Across Methods on the Office10 Dataset with ResNet18 network  

<table><tr><td>Method</td><td>Std</td></tr><tr><td>FedAvg</td><td>10.42±2.72</td></tr><tr><td>AFL | ηλ = 0.01</td><td>11.25±2.37</td></tr><tr><td>FedProx</td><td>8.89±1.88</td></tr><tr><td>q-FedAvg | q = 0.5</td><td>6.80±1.83</td></tr><tr><td>FedFa | β = 0.5</td><td>8.34±2.48</td></tr><tr><td>FedMgda+ | ε = 0.1</td><td>10.51±1.48</td></tr><tr><td>FedFV | α = 0.3, τ = 0</td><td>10.89±2.17</td></tr><tr><td>FedGini | ε = 1.0</td><td>8.32±1.79</td></tr><tr><td>FedGA</td><td>6.44±0.83</td></tr></table>

Table 3 reports the standard deviation (Std) of client- level accuracy distributions for various federated learning algorithms on the Office10_Resnet18 dataset. Consistent with the Gini coefficient results shown in Figure 7, FedGA achieves the lowest accuracy standard deviation. The fact that FedGA outperforms all baselines on both fairness metrics demonstrates its capability in preserving fairness in federated learning systems under feature heterogeneous conditions.

Figure 9 presents a three- dimensional visualization comparing federated learning algorithms across test accuracy, Gini coefficient, and client accuracy standard deviation on the synthetic_0_0 dataset. FedGA achieves a favorable balance among these metrics: high accuracy  $(77.86\pm 2.24\%)$ , the lowest Gini coefficient  $(0.16601\pm 0.02285)$

![](images/701893fa0f15947bd682fa1ee959a71f97d70165da2327b5713196a5e9657cf1.jpg)  
Comparison of Fairness, Accuracy, and Stability among Methods on the synthetic_0_DatasetFigure 9: 3D Comparison of Performance, Fairness, and Stability on the Synthetic_0_Dataset.

and lowest standard deviation  $(23.38\pm 2.63)$ . Baseline methods (FedAvg and FedProx) exhibit lower accuracy, higher Gini coefficients, and higher standard deviations, indicating limited resilience to client heterogeneity. While fairness- oriented methods (FedGini and FedFV) show partial improvements in fairness metrics, they remain suboptimal compared to FedGA. The distinct positioning of FedGA in this 3D trade- off space demonstrates its effectiveness in reconciling the competing objectives of accuracy and fairness under heterogeneous conditions.

![](images/50a90ee28b3627df60e79c60425f003feb63a6a8412c3db7e4705d64479e40bc.jpg)  
Figure 10: 3D Comparison of Performance, Fairness, and Stability on the Synthetic_0_Dataset.

Figures 10 and 11 present the performance of federated learning algorithms on the synthetic_0_0 dataset over 200 rounds, showing mean test accuracy and Gini coefficient evolution, respectively. While FedFV initially converges faster, FedGA achieves the highest final accuracy after 100 rounds and demonstrates more consistent fairness improvement through sustained Gini coefficient reduction. Other fairness- aware methods (q- FedAvg, FedFa, FedGini) show moderate improvements but remain limited compared to FedGA.

![](images/3110b10dba0df7f6c03db615ce7db1efebfd40a469a0fa6f6b9a25bf3438c6af.jpg)  
Figure 11: 3D Comparison of Performance, Fairness, and Stability on the Synthetic_0_0 Dataset.

FedAvg and FedProx exhibit similar trajectories, reflecting their sensitivity to data heterogeneity.

Table 4: Average Test Accuracy of the Bottom  $10\%$  Clients Under Different FL Algorithms on the synthetic_0_0 dataset  

<table><tr><td>Method</td><td>Worst 10%</td></tr><tr><td>FedAvg</td><td>0.00±0.00</td></tr><tr><td>FedProx</td><td>0.00±0.00</td></tr><tr><td>q-FedAvg | q = 0.2</td><td>11.78±8.65</td></tr><tr><td>FedFα | β = 0</td><td>16.47±6.51</td></tr><tr><td>FedMgda+ | ε = 0.5</td><td>0.00±0.00</td></tr><tr><td>FedFV | α = 0.1, τ = 10</td><td>23.65±8.75</td></tr><tr><td>FedGini | ε = 1.0</td><td>9.90±8.34</td></tr><tr><td>FedGA</td><td>28.94±7.89</td></tr></table>

Table 4 reports the average test accuracy of the bottom  $10\%$  clients. FedGA achieves the highest bottom-  $10\%$  accuracy  $(28.94\pm 7.89)$ , demonstrating strong support for disadvantaged clients. While fairness- aware methods (FedFV:  $23.65\pm 8.75$ , FedFa:  $16.47\pm 6.51$ , q- FedAvg:  $11.78\pm 8.65$ ) outperform standard baselines, they remain less effective than FedGA. FedAvg, FedProx, and FedMGDA+ yield near- zero accuracy for bottom- performing clients, indicating their focus on global performance at the expense of equity. These results underscore FedGA's effectiveness in addressing the critical challenge of balancing global accuracy with tail fairness in heterogeneous federated learning.

Figure 12 shows violin plots of client- wise accuracy distributions for federated learning algorithms on the synthetic_05_05 dataset. FedGA produces a highly concentrated distribution centered near the upper performance range (mean:  $84.00\pm 1.85\%$ ), with its narrow shape and minimal tail mass indicating both high average accuracy and reduced inter- client variability. This demonstrates improved fairness as even disadvantaged clients achieve competitive performance. Standard baselines (FedAvg, FedProx) exhibit broader, bottom- heavy distributions reflecting larger performance gaps across clients. While fairness- oriented methods (FedGini, FedFV) narrow the lower tail and raise median accuracy, their distributions remain wider with lower overall averages

![](images/9f36703d98726771c2786d6f97f8cdeb79ba86982335fd46daa007ae0aa0c4f5.jpg)  
Figure 12: Client Accuracy Distribution Across Algorithms on the Synthetic_0.5_0.5 Dataset.

than FedGA, highlighting FedGA's effectiveness in balancing global performance with client equity.

![](images/505f587977b80ca5a55798024e18adb515259b9fbded3efdf62ab3d446a13c5b.jpg)  
Figure 13: Top-Bottom Client Accuracy Gap Across Algorithms on the Synthetic_05_05 Dataset.

Figure 13 depicts the mean accuracy gap between the top-  $10\%$  and bottom-  $10\%$  clients on the synthetic_05_05 dataset, directly measuring performance polarization. FedGA achieves the smallest gap  $(56.86\%)$ , indicating that performance improvements are evenly distributed across clients and effectively supporting both well- resourced and disadvantaged participants. Standard approaches (FedAvg, FedProx) exhibit maximum disparities  $(100\%)$ , reflecting their tendency to favor clients with more representative data. Fairness- oriented algorithms show moderate improvements: q- FedAvg  $(67.33\%)$ , FedFa  $(67.49\%)$ , FedFV  $(60.44\%)$ , and FedGini  $(68.22\%)$ , demonstrating partial success in reducing head- tail disparities but remaining less effective than FedGA in promoting equitable performance distribution. Table 5 presents a multi- metric evaluation on the Synthetic_05_05 dataset. FedGA achieves the highest mean accuracy  $(84.00 \pm 1.85\%)$  alongside optimal fairness metrics: lowest standard deviation  $(18.60 \pm 2.36)$ , Gini coefficient  $(0.11955 \pm 0.01837)$ , and best worst-  $10\%$  client accuracy  $(43.14 \pm 5.94)$ , demonstrating effective balance between utility and equity. Fairness- oriented methods (FedFV, FedGini) show improvements over standard baselines with reduced Gini values (0.12774, 0.15121) and better tail performance, yet remain inferior to FedGA. FedAvg and FedProx exhibit high Gini coefficients and near- zero worst- client accuracy, highlighting their limitations under heterogeneous conditions.

![](images/59d30eead730887b69fe99c65a917c35f374c4be5cdf763a75a0e2de6cca7594.jpg)  
Figure 14: Client Accuracy Distribution via KDE on the Synthetic_1_1 Dataset.

Figure 14 displays kernel density estimates of client test accuracies for federated learning algorithms on the synthetic_1_1 dataset. FedGA produces a sharply peaked, right- shifted distribution centered around  $85 - 90\%$  accuracy with minimal low- accuracy occurrences, reflecting uniformly high performance and low interclient variability. Conversely, FedAvg and FedProx show broader distributions with substantial density between  $40 - 70\%$  and pronounced left tails, indicating larger performance disparities. While fairness- enhancing methods (FedFV, FedGini, q- FedAvg, FedFa) shift distributions rightward compared to FedAvg, their curves remain wider and less concentrated than FedGA's, suggesting persistent variability despite fairness improvements.

Figure 15 illustrates a radar chart comparing federated learning algorithms on the synthetic_1_1 dataset across four normalized metrics: mean accuracy, worst  $19\%$  client accuracy, Gini coefficient, and accuracy standard deviation. FedGA exhibits the most balanced performance with the lowest Gini coefficient and highest worst-  $10\%$  accuracy, while maintaining competitive mean accuracy and standard deviation. This demonstrates effective fairness improvement without sacrificing overall utility. FedAvg and FedProx achieve moderate mean accuracy but show elevated Gini coefficients and standard deviations, indicating higher disparity. Fairness- oriented methods (q- FedAvg, FedFa, FedGini, FedFV) improve fairness metrics compared to FedAvg but display uneven radar profiles, suggesting trade- offs between different performance dimensions.

# 5.3 Hyperparameter Analysis

This section examines the impact of hyperparameter  $\lambda$  on fairness in federated learning. We evaluated  $\lambda$  values from 1 to 10 across datasets, with results shown in Figure 16. The parameter  $\lambda$  controls fairness intervention strength—larger values assign higher weights to poorly- performing clients, while  $\lambda = 0$  applies uniform weighting (distinct from FedAvg's data- size- based weighting).

Table 5: Comparison of accuracy and fairness of different methods on the synthetic_05_05 dataset  

<table><tr><td>Method</td><td>Mean acc</td><td>Std</td><td>Worst 10%</td><td>Best Gini</td></tr><tr><td>FedAvg</td><td>64.48±0.83</td><td>37.62±1.56</td><td>0.00±0.00</td><td>0.32495±0.01057</td></tr><tr><td>FedProx</td><td>64.90±0.60</td><td>37.64±1.50</td><td>0.00±0.00</td><td>0.32160±0.01017</td></tr><tr><td>q-FedAvg | q = 0.2</td><td>81.01±1.88</td><td>22.43±1.39</td><td>32.67±3.27</td><td>0.15008±0.01415</td></tr><tr><td>FedFa | β = 0</td><td>81.25±1.58</td><td>22.03±1.58</td><td>32.95±4.01</td><td>0.14704±0.01341</td></tr><tr><td>FedMgda+ | ε = 0.05</td><td>34.06±5.87</td><td>38.68±2.74</td><td>0.00±0.00</td><td>0.62998±0.05413</td></tr><tr><td>FedFV | α = 0.1, τ = 10</td><td>83.50±1.89</td><td>19.76±2.25</td><td>39.56±6.98</td><td>0.12774±0.01728</td></tr><tr><td>FedGini | ε = 1.0</td><td>80.95±2.10</td><td>22.74±2.13</td><td>31.78±4.53</td><td>0.15121±0.01827</td></tr><tr><td>FedGA</td><td>84.00±1.85</td><td>18.60±2.36</td><td>43.14±5.94</td><td>0.11955±0.01837</td></tr></table>

![](images/3a5344ff89204ea8b4ff10fc9856cdff020eb6053d18769f4d4ab8fde5f7b85b.jpg)  
Figure 15: Radar Chart Comparison of Federated Learning Algorithms on Fairness and Performance (Synthetic_1_1 Dataset).

As illustrated, most datasets exhibit a U- shaped pattern where the Gini coefficient initially decreases then increases with  $\lambda$ , indicating that moderate fairness intervention improves equity while excessive intervention may be counterproductive. Office10_ResNet18 shows a different pattern: after a brief increase at  $\lambda \approx 2$ , the Gini coefficient steadily declines, reaching its minimum at  $\lambda = 10$ . These dataset- dependent responses demonstrate that optimal  $\lambda$  selection is crucial for balancing fairness and utility, as simply increasing  $\lambda$  does not guarantee improved fairness and may compromise overall performance.

# 5.4 Ablation Experiment

To evaluate delayed fairness intervention in FedGA, we conducted ablation experiments comparing FedGA ablation (fairness intervention from round one) against original FedGA (fairness intervention after initial phase). Tables 6 and 7 present results across three datasets.

FedGA consistently outperforms FedGA ablation in fairness metrics—achieving lower Gini coefficients and reduced accuracy standard deviation—while maintaining comparable or superior overall accuracy. Improvements are particularly notable on Synthetic and Cifar01 datasets, where FedGA excels across all metrics. Additionally, FedGA yields higher worst-  $10\%$  client accuracy, demonstrating stronger protection for disadvantaged participants. These findings validate our design choice of deferring fairness intervention, enabling the model to establish stable optimization before enforcing fairness objectives, thereby enhancing both equity and global knowledge aggregation in heterogeneous settings.

# 5.5 Execution Time Analysis

To evaluate computational efficiency, we compared the runtime overhead of FedGA and FedGini's intervention timing algorithms, described in Section 3.1, on Office- 10 and CIFAR- 10 datasets. As shown in Table 8, FedGA achieves several orders of magnitude improvement in runtime efficiency over FedGini, demonstrating the practicality of its lightweight intervention mechanism for federated training of large- scale models.

# 6 RELATED WORK

Mohri et al. [28] proposed AFL, which can optimize for any potential target distribution derived from a mixture of client distributions. However, it is limited to scenarios with a relatively small number of participating devices. Li et al. [20] introduced q- FedAvg, which adjusts fairness via a parameter q, assigning greater weight to clients with higher loss. Hu et al. [10] developed FedMGDA+, which enhances the fairness of federated learning without compromising the performance of other devices defends against malicious clients. Tian et al. [30] proposed alpha- FedAvg, incorporating Jain's Index to measure the fairness of federated learning. This method explores the parameter alpha in  $\alpha$  — fairness to balance fairness and accuracy. Huang et al. [12] proposed FedFa, which sets aggregation weights based on training accuracy and participation frequency, and employs dual- momentum to mitigate forgetting. Wang et al. [32] attributed fairness issues to gradient conflicts and designed a method to alleviate them. Li et al. [24] proposed FedGini, which uses the Gini coefficient to measure the level of fairness in federated learning, and they introduced a gradient descent- based method to improve fairness among participants.

![](images/d4a946aad294e930c2bc15d8a78b6dd37877a417b0a7963548e298557031d2a4.jpg)  
Figure 16: The impact of hyperparameters on the results.

Table 7: Ablation experiment on the Cifar-01 and synthetic dataset  

<table><tr><td>Model</td><td>Method</td><td>Amazon</td><td>Caltech</td><td>DSLR</td><td>Webcam</td><td>Average</td><td>Std</td><td>Best Gini</td></tr><tr><td rowspan="2">Alexnet</td><td>FedGA ablation</td><td>78.96±0.71</td><td>67.11±1.46</td><td>73.12±1.24</td><td>88.14±1.52</td><td>76.83±0.82</td><td>8.05±0.61</td><td>0.07656±0.00572</td></tr><tr><td>FedGA</td><td>79.38±0.71</td><td>66.76±1.52</td><td>72.50±3.61</td><td>86.10±1.98</td><td>76.18±0.84</td><td>7.48±1.06</td><td>0.07209±0.01006</td></tr><tr><td rowspan="2">Resnet18</td><td>FedGA ablation</td><td>68.75±2.80</td><td>58.84±0.45</td><td>64.38±3.19</td><td>77.63±4.72</td><td>67.40±1.30</td><td>7.28±1.61</td><td>0.07547±0.01582</td></tr><tr><td>FedGA</td><td>70.52±1.70</td><td>58.40±0.60</td><td>68.75±2.80</td><td>75.25±2.54</td><td>68.23±0.28</td><td>6.44±0.83</td><td>0.06673±0.01113</td></tr></table>

Table 6: Ablation experiment on the office-10 dataset  

<table><tr><td>Dataset</td><td>Method</td><td>Average</td><td>Std</td><td>Worst 10%</td><td>Best Gini</td></tr><tr><td rowspan="2">Cifar-01</td><td>FedGA ablation</td><td>58.85±1.21</td><td>13.34±1.95</td><td>34.73±5.43</td><td>0.12865±0.01984</td></tr><tr><td>FedGA</td><td>59.80±1.12</td><td>11.54±1.48</td><td>38.85±3.41</td><td>0.10919±0.01456</td></tr><tr><td rowspan="2">Synthetic_0_0</td><td>FedGA ablation</td><td>77.48±2.27</td><td>23.96±2.58</td><td>27.26±6.75</td><td>0.17092±0.02336</td></tr><tr><td>FedGA</td><td>77.86±2.24</td><td>23.38±2.63</td><td>28.94±7.89</td><td>0.16601±0.02285</td></tr><tr><td rowspan="2">Synthetic_05_05</td><td>FedGA ablation</td><td>83.91±1.88</td><td>19.16±2.12</td><td>40.98±6.86</td><td>0.12281±0.01645</td></tr><tr><td>FedGA</td><td>84.00±1.85</td><td>18.60±2.36</td><td>43.14±5.94</td><td>0.11955±0.01837</td></tr><tr><td rowspan="2">Synthetic_1_1</td><td>FedGA ablation</td><td>84.81±1.64</td><td>18.43±2.11</td><td>44.62±7.99</td><td>0.11505±0.01194</td></tr><tr><td>FedGA</td><td>85.01±1.58</td><td>18.19±2.10</td><td>44.76±6.18</td><td>0.11295±0.01181</td></tr></table>

Table 8:Execution Time between FedGini and FedGA  

<table><tr><td>time/second(s)</td><td>Office10_Alexnet</td><td>Office10_Resnet18</td><td>Cifar01</td></tr><tr><td>FedGini</td><td>4.90 × 10-2 ± 1.32 × 10-4</td><td>1.98 × 10-1 ± 3.55 × 10-3</td><td>4.02 × 10-1 ± 5.83 × 10-3</td></tr><tr><td>FedGA</td><td>8.32 × 10-6 ± 2.64 × 10-7</td><td>6.25 × 10-6 ± 4.13 × 10-7</td><td>1.20 × 10-5 ± 1.02 × 10-6</td></tr></table>

# 7 CONCLUSION AND FUTURE WORK

We propose FedGA, a federated learning method that monitors Gini coefficient evolution to determine optimal fairness intervention timing and adjusts aggregation weights based on client validation accuracy. Extensive experiments demonstrate that FedGA outperforms existing methods by significantly reducing performance disparities while maintaining competitive accuracy. The delayed fairness intervention strategy proves particularly effective, allowing models to establish stable optimization trajectories before enforcing equity constraints. Future work will extend FedGA to incorporate additional fairness dimensions, including sensitive attribute protection and demographic subgroup equity, further advancing federated learning for socially responsible applications.

# REFERENCES

[1] Zheng Chai, Yujing Chen, Ali Anwar, Liang Zhao, Yue Cheng, and Huzefa Rangwala. 2021. FedAT: A high- performance and communication- efficient federated learning system with asynchronous tiers. In Proceedings of the international conference for high performance computing, networking, storage and analysis. 1–16.  [2] Yang Chen, Xiaoyan Sun, and Yaochu Jin. 2019. Communication- efficient federated deep learning with layerwise asynchronous model update and temporally weighted aggregation. IEEE transactions on neural networks and learning systems 31, 10 (2019), 4229–4238.  [3] Liam Collins, Hamed Hassani, Aryan Mokhtari, and Sanjay Shakkottai. 2021. Exploiting Shared Representations for Personalized Federated Learning. In Proceedings of the 38th International Conference on Machine Learning, ICML 2021, 18–24 July 2021, Virtual Event /Proceedings of Machine Learning Research, Vol. 139, Marina Mella and Tong Zhang (Eds.). PMLR, 2089–2099. http://proceedings.mil. press/v139/collins21a.html  [4] Wei Du, Depeng Xu, Xintao Wu, and Hangchang Tong. 2021. Fairness- aware agnostic federated learning. In Proceedings of the 2021 SIAM International Conference on Data Mining (SDM). SIAM, 181–189.  [5] Moming Duan, Duo Liu, Xianzhang Chen, Yujuan Tan, Jinting Ren, Lei Qiao, and Liang Liang. 2019. Astrabile Self- balancing federated learning for improving classification accuracy of mobile deep learning applications. In 2019 IEEE 37th international conference on computer design (ICCD). IEEE, 246–254.  [6] Robin C Geyer, Tassilo Klein, and Moin Nabi. 2017. Differentially private federated learning: A client level perspective. ArXiv preprint abs/1712.07557 (2017). https://arxiv.org/abs/1712.07557  [7] Corrado Gini. 1912. Variabilită e mutabilită: contributo allo studio delle distribuzioni e delle relazioni statistiche (J. Tapiro, di P. Cuppin).  [8] Boqing Gong, Yuan Shai, Fei Shia, and Kristen I. Grauman. 2012. Geodesic flow kernel for unsupervised domain adaptation. In 2012 IEEE Conference on Computer Vision and Pattern Recognition, Providence, RI, USA, June 16–21, 2012. IEEE Computer Society, 2066–2073. doi:10.109/VPR.2012.6247911  [9] Kaiming He, Xiangyu Zhang, Shaoping Ren, and Jian Sun. 2016. Deep Residual Learning for Image Recognition. In 2016 IEEE Conference on Computer Vision and Pattern Recognition, CVPR 2016, Las Vegas, NV, USA, June 27–30, 2016. IEEE Computer Society, 770–778. doi:10.1109/VCPR.2016.90  [10] Zeou Hu, Kiarash Shaloudegi, Guojun Zhang, and Yaoliang Yu. 2022. Federated learning meets multi- objective optimization. IEEE Transactions on Network Science and Engineering 9, 4 (2022), 2039–2051.  [11] Li Huang, Yifeng Yin, Zeng Yu, Shifa Zhang, Hao Deng, and Dianbo Liu. 2020. LoAdaBoost: Loss- based AdiBoost federated machine learning with reduced computational complexity on IID and non- IID intensive care data. Plos one 15, 4 (2020), e0230706.  [12] Wei Huang, Tianrui Li, Dexian Wang, Shengdong Du, Junbo Zhang, and Tianqiang Huang. 2022. Fairness and accuracy in horizontal federated learning. Information Sciences 589 (2022), 170–185.  [13] Yutao Huang, Lingyang Chu, Zirui Zhou, Lanjun Wang, Jiangchuan Liu, Jian Pei, and Yong Zhang. 2021. Personalized Cross- Silo Federated Learning on NonIID Data. In Thirty- Fifth AAAI Conference on Artificial Intelligence, AAAI 2021, Thirty- Third Conference on Innovative Applications of Artificial Intelligence, IAAI 2021, The Eleventh Symposium on Educational Advances in Artificial Intelligence, EAAI 2021, Virtual Event, February 2–9, 2021. AAAI Press, 7865–7873. https://ojs.aaai.org/index.php/AAAI/article/view/16960  [14] Sai Praneth Kush, and Ananda Tyen Kale, Mehray Mohri, Sashank J. Reddi, SeBastian U. Stich, and Andyl A Theetha Suresh. 2020. SCAFFOLD: Stochastic Controlled Averaging for Federated Learning. In Proceedings of the 37th International Conference on Machine Learning, ICML 2020, 13–18 July 2020, Virtual Event (Proceedings of Machine Learning Research, Vol. 119). PMLR, 5132–5143. http://proceedings.mlr.press/v119. arsimireddy20a.html  [15] Jakub Konecny, H Brendan McMahan, Daniel Ramage, and Peter Richtarik. 2016. Federated optimization: Distributed machine learning for on- device intelligence. arXiv preprint arXiv:1610.02522 (2016).  [16] Alex Krizhevsky, Geoffrey Hinton, et al. 2009. Learning multiple layers of features from tiny images. (2009).  [17] Alex Krizhevsky, Ilya Sutskever, and Geoffrey E. Hinton. 2012. ImageNet Classification with Deep Convolutional Neural Networks. In Advances in Neural Information Processing Systems 25: 26th Annual Conference on Neural Information Processing Systems 2012. Proceedings of a meeting held December 3–6, 2012, Lake Tahoe, Nevada, United States, Peter L. Bartlett, Fernando C. N. Pereira, Christopher J. C. Burges, Léon Bottou, and Kilian Q. Weinberger (Eds.). 1106–1114. https://proceedings.neurips.cc/paper/2012/hash/c99862d3b9d6b78c8436e924a68c45b- Abstract.html  [18] Jie Li, Yongli Ren, and Ke Deng. 2022. FairGAN: GANs- based Fairness- aware Learning for Recommendations with Implicit Feedback. In WWW '22: The ACM Web Conference 2022, Virtual Event, Lyon, France, April 25–29, 2022, Frédérique Laforest, Raphael Troncy, Elena Simperl, Deepak Agarwal, Aristides Gionis, Ivan Herman, and Lionel Médini (Eds.). ACM, 297–307. doi:10.1145/3485447.3511958

[19] Tian Li, Amit Kumar Sahu, Manzil Zaheer, Maziar Sanjabi, Ameet Talwalkar, and Virginia Smith. 2020. Federated Optimization in Heterogeneous Networks. In Proceedings of Machine Learning and Systems 2020, MLSys, Austin, TX, USA, March 2–4, 2020, Inderjit S. Dhillon, Dimitris S. Papailiopoulos, and Vivienne Sze (Eds.). mlsys.org. https://proceedings.mlsys.org/book/316. pdf  [20] Tian Li, Maziar Sanjabi, Ahmad Beirami, and Virginia Smith. 2020. Fair Resource Allocation in Federated Learning. In 8th International Conference on Learning Representations, ICLR 2020, Addis Ababa, Ethiopia, April 26–30, 2020. OpenReview.net. https://openreview.net/forum?id=ByexElsYDr  [21] Xiaoxiao Li, Meirui Jiang, Xiaofei Zhang, Michael Kamp, and Qi Dou. 2021. FedBN: Federated Learning on Non- IID Features via Local Batch Normalization. In 9th International Conference on Learning Representations, ICLR 2021, Virtual Event, Austria, May 3–7, 2021. OpenReview.net. https://openreview.net/forum?id=6XFQUln9QICC  [22] Xin- Chun Li, Yi- Chu Xu, Shaoming Song, Bingshuai Li, Yinchuan Li, Yunfeng Shao, and De- Chuan Zhan. 2022. Federated Learning with Position- Aware Neurons. In IEEE/CVF Conference on Computer Vision and Pattern Recognition, CVPR 2022, New Orleans, LA, USA, June 18–24, 2022. IEEE, 10072–10081. doi:10.1109/CVPR52688.2022.00984  [23] Xion- Chun Li and De- Chuan Zhan. 2021. FEDRS: Federated Learning with Restricted Softmax for Label Distribution Non- IID Data. In KDD '21: The 27th ACM SIGKDD Conference on Knowledge Discovery and Data Mining, Virtual Event, Singapore, August 14–18, 2021, Feida Zhu, Beng Chin Ooi, and Chunyan Miao (Eds.). ACM, 995–1005. doi:10.1145/3447548.3467294  [24] Xiaoli Li, Siran Zhao, Chuan Chen, and Zibin Zheng. 2023. Heterogeneity- aware fair federated learning. Information Sciences 619 (2023), 968–986.  [25] Xin- Chun Li, De- Chuan Zhan, Yunfeng Shao, Bingshuai Li, and Shaoming Song. 2021. Fedphp: Federated personalization with inherited private models. In Joint European conference on machine learning and knowledge discovery in databases. Springer, 587–602.  [26] Wei Liu, Li Chen, Yunfei Chen, and Deseni Zhang. 2020. Accelerating federated learning via momentum gradient descent. IEEE Transactions on Parallel and Distributed Systems 31, 8 (2020), 1754–1766.  [27] Brendan McMahan, Eider Moore, Daniel Ramage, Seth Hampson, and Blaise Agüera de Arcas. 2017. Communications- Efficient Learning of Deep Networks from Centralized Data. In Proceedings of the 20th International Conference on Artificial Intelligence and Statistics, AISTATS 2017, 20–22 April 2017, Fort Lauderdale, FL, USA (Proceedings of Machine Learning Research, Vol. 54), Aarti Singh (Mukheyari et al., 2017), PMLR, 1979–1989. http://proceedings.mml. p.research/17a.html  [28] Mehryar Mohri, Gary Sivek, and Ananda Tueretha Suresh. 2019. Agnostic Federated Learning. In Proceedings of the 36th International Conference on Machine Learning, ICML 2019, 9–15 June 2019, Long Beach, California, USA (Proceedings of Machine Learning Research, Vol. 97), Kamalika Chaudhuri and Ruslan Salakhutdinov (Eds.). PMLR, 4615–4625. http://proceedings.mlr.press/v97/mohri19a.html  [29] Virginia Smith, Cha- Kai Chiang, Maziar Sanjabi, and Ameet S. Talwalkar. 2017. Federated Multi- Task Learning. In Advances in Neural Information Processing Systems 30: Annual Conference on Neural Information Processing Systems 2017, December 4–9, 2017, Long Beach, CA, USA, Inabelle Guyon, Ulrike von Luxburg, Samy Bengio, Hanna M. Wallach, Rob Ferguson, S. V. N. Vishwanathan, and Roman Garnett (Eds.). 4424–4434. https://proceedings.neurips.cc/paper/2017/hash/6211808fa8991866b1a0c9d556c1d0f- Abstract.html  [30] Jialui Tian, Xixiang Lv, Renpeng Zou, Bin Zhang, and Yige Li. 2022. A Fair Resource Allocation Scheme in Federated Learning. Journal of Computer Research and Development 59, 6 (2022), 1240–1254. doi:10.7544/issn1000- 1239.20201081  [31] Jianyu Wang, Qinghua Liu, Hao Liang, Gauri Joshi, and H. Vincent Poor. 2020. Tackling the Objective Inconsistency Problem in Heterogeneous Federated Optimization. In Advances in Neural Information Processing Systems 33: Annual Conference on Neural Information Processing Systems 2020, NeurIPS 2020, December 6–12, 2020, virtual, Hugo Larochelle, Marc Aurelio Ranzato, Raia Hadsell, Maria- Florina Balcan, and Hsuan- Tien Lin (Eds.). https://proceedings.neurips.cc/paper/2020/hash/564127c03caab942e503ce6f810f54fd- Abstract.html  [32] Zheng Wang, Xiaoliang Fan, Jianzhong Qi, Chenglu Wen, Cheng Wang, and Rongshan Yu. 2021. Federated Learning with Fair Averaging. In Proceedings of the Thirtieth International Joint Conference on Artificial Intelligence, IJCAI 2021, Virtual Event / Montreal, Canada, 19–27 August 2021, Zhi- Hua Zhou (Ed.). ijcai.org, 1615–1623. doi:10.24963/IJCAI.2021/223  [33] Cong Xie, Sammi Koyejo, and Indranil Gupta. 2019. Asynchronous federated optimization. ArXiv preprint abs/1903.03934 (2019). https://arxiv.org/abs/1903.03934  [34] Wenyu Zhang, Xiumin Wang, Pan Zhou, Weiwei Wu, and Xinglin Zhang. 2021. Client selection for federated learning with non- iid data in mobile edge computing. IEEE Access 9 (2021), 24462–24474.  [35] Yue Zhao, Meng Li, Liangzhen Lai, Naveen Suda, Damon Civin, and Vikas Chandra. 2018. Federated learning with non- iid data. ArXiv preprint abs/1806.00582 (2018). https://arxiv.org/abs/1806.00582  [36] Tianfei Zhou and Ender Konukoglu. 2023. FedFA: Federated Feature Augmentation. In The Eleventh International Conference on Learning Representations, ICLR

pdf?id=U9yFP90jU0