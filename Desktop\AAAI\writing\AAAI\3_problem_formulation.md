# Problem Formulation

In this section, we first introduce the mathematical foundations of medical multimodal federated learning and then formally define our multi-dimensional trustworthiness optimization problem for TrustGuard framework.

在本节中，我们首先介绍医疗多模态联邦学习的数学基础，然后正式定义TrustGuard框架的多维度可信性优化问题。

**Medical Multimodal Federated Learning.** Given a set of medical institutions $\{I_i\}_{i=1}^N$, each possessing multimodal medical datasets $M_i = \{M_i^{(v)}, M_i^{(t)}, M_i^{(g)}, M_i^{(c)}, M_i^{(s)}\}$ representing imaging, textual, genomic, clinical, and sensor data respectively, along with corresponding medical labels $Y_i$. The goal is to collaboratively train a trustworthy medical AI model while preserving patient privacy and ensuring regulatory compliance.

**医疗多模态联邦学习。** 给定一组医疗机构$\{I_i\}_{i=1}^N$，每个机构拥有多模态医疗数据集$M_i = \{M_i^{(v)}, M_i^{(t)}, M_i^{(g)}, M_i^{(c)}, M_i^{(s)}\}$，分别表示影像、文本、基因组、临床和传感器数据，以及相应的医疗标签$Y_i$。目标是在保护患者隐私和确保法规合规的同时，协作训练可信的医疗AI模型。

**Multi-dimensional Trustworthiness Space.** We define a five-dimensional trustworthiness space $\mathcal{T} = \{T^{(p)}, T^{(s)}, T^{(r)}, T^{(f)}, T^{(e)}\}$ representing privacy, security, robustness, fairness, and explainability dimensions respectively. Each dimension $T^{(k)}$ is characterized by specific metrics and constraints that must be satisfied for trustworthy medical AI deployment.

**多维度可信性空间。** 我们定义一个五维可信性空间$\mathcal{T} = \{T^{(p)}, T^{(s)}, T^{(r)}, T^{(f)}, T^{(e)}\}$，分别表示隐私、安全、鲁棒性、公平性和可解释性维度。每个维度$T^{(k)}$由特定的指标和约束特征化，这些指标和约束必须满足可信医疗AI部署的要求。

**Cross-Modal Cascade Threat Modeling.** We model cross-modal cascade threats as a directed graph $\mathcal{G}_{CMCT} = (\mathcal{V}, \mathcal{E})$, where vertices $\mathcal{V}$ represent different medical modalities and edges $\mathcal{E}$ represent potential attack propagation paths. Each edge $(v_i, v_j) \in \mathcal{E}$ has an associated cascade probability $p_{ij}$ indicating the likelihood of threat propagation from modality $i$ to modality $j$.

**跨模态级联威胁建模。** 我们将跨模态级联威胁建模为有向图$\mathcal{G}_{CMCT} = (\mathcal{V}, \mathcal{E})$，其中顶点$\mathcal{V}$表示不同的医疗模态，边$\mathcal{E}$表示潜在的攻击传播路径。每条边$(v_i, v_j) \in \mathcal{E}$都有一个相关的级联概率$p_{ij}$，表示威胁从模态$i$传播到模态$j$的可能性。

**Adaptive Coupling Mechanism.** The core challenge is to design an adaptive coupling function $\phi: \mathcal{T} \times \mathcal{C} \rightarrow \mathbb{R}^{5 \times 5}$ that dynamically determines the coupling weights between trustworthiness dimensions based on the current medical context $\mathcal{C}$ and threat landscape. The coupling matrix $\Phi = \phi(\mathcal{T}, \mathcal{C})$ captures the interdependencies and trade-offs among different trustworthiness aspects.

**自适应耦合机制。** 核心挑战是设计一个自适应耦合函数$\phi: \mathcal{T} \times \mathcal{C} \rightarrow \mathbb{R}^{5 \times 5}$，根据当前医疗上下文$\mathcal{C}$和威胁环境动态确定可信性维度之间的耦合权重。耦合矩阵$\Phi = \phi(\mathcal{T}, \mathcal{C})$捕获不同可信性方面之间的相互依赖关系和权衡。

**Medical Federated Learning Formulation.** In medical multimodal federated learning, a set of medical datasets $\{M_i\}_{i\in N}$ are distributed across $N$ medical institutions, the goal is to find the optimal global trustworthiness parameter $w$ while satisfying multi-dimensional constraints:

**医疗联邦学习表述。** 在医疗多模态联邦学习中，一组医疗数据集$\{M_i\}_{i\in N}$分布在$N$个医疗机构中，目标是在满足多维度约束的同时找到最优的全局可信性参数$w$：

$$
\min_{w\in \mathbb{R}^d}F(w)\coloneqq \sum_{n = 1}^{N}\frac{|M_n|}{|M|} f_n(w) + \lambda \sum_{k=1}^{5} \mathcal{R}_k(T^{(k)}), \tag{2}
$$

where $F(\cdot)$ is a function that aggregates the local objectives with trustworthiness regularization, $f_{i}(\cdot)$ denotes the expected loss over the medical data distribution of institution $i$, and $\mathcal{R}_k(T^{(k)})$ represents the regularization term for trustworthiness dimension $k$.

其中$F(\cdot)$是聚合带有可信性正则化的本地目标的函数，$f_{i}(\cdot)$表示机构$i$的医疗数据分布上的期望损失，$\mathcal{R}_k(T^{(k)})$表示可信性维度$k$的正则化项。

**Predictive Adaptive Defense.** The PAD framework aims to proactively defend against cross-modal cascade threats by predicting potential attack chains and implementing preventive measures. Given the Medical CMAG $\mathcal{G}_{CMCT}$, the predictive defense problem can be formulated as finding the optimal defense strategy $\Psi^*$ that minimizes the expected cascade threat impact:

**预测性自适应防御。** PAD框架旨在通过预测潜在攻击链并实施预防措施来主动防御跨模态级联威胁。给定医疗CMAG $\mathcal{G}_{CMCT}$，预测性防御问题可以表述为找到最优防御策略$\Psi^*$，以最小化预期级联威胁影响：

$$
\Psi^* = \underset{\Psi \in \mathcal{S}}{\operatorname{argmin}} \mathbb{E}_{(v_i,v_j) \in \mathcal{E}} [p_{ij} \cdot \mathcal{I}(v_i, v_j, \Psi)], \tag{3}
$$

where $\mathcal{S}$ represents the space of possible defense strategies and $\mathcal{I}(v_i, v_j, \Psi)$ denotes the impact function measuring the consequence of threat propagation from modality $v_i$ to $v_j$ under defense strategy $\Psi$.

其中$\mathcal{S}$表示可能防御策略的空间，$\mathcal{I}(v_i, v_j, \Psi)$表示在防御策略$\Psi$下威胁从模态$v_i$传播到$v_j$的后果的影响函数。

**Multi-dimensional Trustworthiness Optimization.** The comprehensive TrustGuard optimization problem integrates adaptive coupling and predictive defense as a multi-objective optimization problem:

**多维度可信性优化。** 综合的TrustGuard优化问题将自适应耦合和预测性防御集成为多目标优化问题：

$$
\begin{array}{rl}
\max_{\Phi, \Psi, w} & \sum_{k=1}^{5} w_k(\Phi) \cdot T^{(k)}(\Phi, \Psi, w) \\
\text{subject to} & \mathcal{C}_{reg}(\Phi, \Psi) \leq \epsilon_{reg} \\
& \mathcal{C}_{safety}(\Phi, \Psi) \geq \delta_{safety} \\
& \mathcal{C}_{utility}(w) \geq \gamma_{utility} \\
& \Phi = \phi(\mathcal{T}, \mathcal{C}) \\
& \Psi \in \mathcal{S}
\end{array} \tag{4}
$$

where $w_k(\Phi)$ are adaptive weights determined by the coupling matrix, and $\epsilon_{reg}$, $\delta_{safety}$, $\gamma_{utility}$ are threshold parameters for regulatory compliance, patient safety, and model utility respectively.

其中$w_k(\Phi)$是由耦合矩阵确定的自适应权重，$\epsilon_{reg}$、$\delta_{safety}$、$\gamma_{utility}$分别是法规合规、患者安全和模型效用的阈值参数。

**Problem Complexity.** This optimization problem is inherently complex due to: (1) the non-convex nature of trustworthiness objectives, (2) the dynamic coupling relationships among dimensions, (3) the stochastic nature of cascade threats, and (4) the distributed medical federated learning constraints. Our TrustGuard framework addresses these challenges through the AMTC mechanism, PAD framework, and DCAO algorithm.

**问题复杂性。** 这个优化问题本质上是复杂的，原因包括：(1)可信性目标的非凸性质，(2)维度间的动态耦合关系，(3)级联威胁的随机性质，(4)分布式医疗联邦学习约束。我们的TrustGuard框架通过AMTC机制、PAD框架和DCAO算法来解决这些挑战。