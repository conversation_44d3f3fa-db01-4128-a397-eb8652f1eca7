# Introduction / 引言

## English Version

The rapid advancement of artificial intelligence in healthcare has demonstrated tremendous potential for improving patient outcomes and clinical decision-making. However, the deployment of AI systems in medical settings faces significant challenges related to data privacy, security, and trustworthiness. Healthcare data is inherently sensitive and subject to strict regulatory requirements such as HIPAA and GDPR, making traditional centralized machine learning approaches problematic for multi-institutional collaborations.

Federated learning (FL) has emerged as a promising solution that enables collaborative model training across multiple healthcare institutions without sharing raw patient data (<PERSON> et al. 2020; <PERSON><PERSON> et al. 2017). By keeping data localized and only sharing model updates, FL addresses fundamental privacy concerns while enabling the development of more robust and generalizable AI models through access to diverse datasets.

The integration of multimodal data—including medical images, electronic health records, genomic data, and sensor readings—further enhances the potential of healthcare AI systems. Multimodal approaches have shown superior performance in various medical tasks, from diagnosis to treatment planning (<PERSON> et al. 2024; <PERSON> et al. 2023). However, the combination of federated learning with multimodal data introduces unprecedented challenges in ensuring trustworthiness across multiple dimensions.

Recent studies have revealed significant vulnerabilities in both federated learning and multimodal AI systems. For instance, <PERSON> et al. (2025) demonstrated that medical multimodal large language models are vulnerable to cross-modality jailbreak attacks, achieving attack success rates exceeding 80%. Similarly, <PERSON> et al. (2024) showed that malicious federated learning servers can recover high-quality private medical data through crafted model updates. These findings highlight the urgent need for comprehensive trustworthiness guarantees in multimodal federated learning systems.

Current approaches to addressing trustworthiness in federated learning typically focus on individual aspects such as privacy preservation (Dwork et al. 2014), security against adversarial attacks (Goodfellow et al. 2015), or fairness in model outcomes (Dwork et al. 2012). However, these isolated solutions fail to address the complex interactions between different trustworthiness dimensions and the unique challenges posed by multimodal data integration.

The healthcare domain presents additional complexities that existing solutions inadequately address. Medical data exhibits high heterogeneity across institutions due to differences in patient populations, imaging protocols, and clinical practices. Furthermore, healthcare applications require not only high accuracy but also interpretability, fairness across demographic groups, and compliance with regulatory standards. The stakes are particularly high in medical settings, where model failures can directly impact patient safety and clinical outcomes.

To address these challenges, we propose TrustMFL, a comprehensive trustworthy multimodal federated learning framework specifically designed for healthcare applications. Our approach makes the following key contributions:

**Multi-dimensional Trustworthiness Framework**: We develop a unified evaluation system that simultaneously assesses privacy preservation, security robustness, fairness guarantee, and explainability, providing a holistic view of system trustworthiness.

**Cross-modal Threat Modeling**: We establish comprehensive threat models for multimodal federated learning environments and develop novel defense mechanisms that address attacks spanning multiple data modalities.

**Dynamic Trustworthiness Assessment**: We introduce adaptive algorithms that continuously monitor trustworthiness metrics and dynamically adjust client participation and aggregation strategies based on real-time assessments.

**Healthcare-specific Optimization**: We design domain-specific modules that address unique healthcare requirements including regulatory compliance, clinical workflow integration, and medical data characteristics.

Our extensive experimental evaluation on three medical multimodal datasets demonstrates the effectiveness of TrustMFL in achieving superior trustworthiness guarantees while maintaining competitive performance. The framework successfully defends against various attack scenarios and provides interpretable insights into model decisions, making it suitable for real-world healthcare deployments.

## Chinese Version / 中文版本

人工智能在医疗领域的快速发展已经展现出改善患者结果和临床决策的巨大潜力。然而，在医疗环境中部署AI系统面临着与数据隐私、安全性和可信性相关的重大挑战。医疗数据本质上是敏感的，受到HIPAA和GDPR等严格监管要求的约束，这使得传统的集中式机器学习方法在多机构合作中存在问题。

联邦学习（FL）作为一种有前景的解决方案已经兴起，它能够在不共享原始患者数据的情况下实现跨多个医疗机构的协作模型训练（Li et al. 2020; McMahan et al. 2017）。通过保持数据本地化并仅共享模型更新，FL解决了基本的隐私问题，同时通过访问多样化数据集实现了更鲁棒和可泛化的AI模型开发。

多模态数据的集成——包括医学图像、电子健康记录、基因组数据和传感器读数——进一步增强了医疗AI系统的潜力。多模态方法在从诊断到治疗规划的各种医疗任务中表现出优越的性能（Chen et al. 2024; Zhang et al. 2023）。然而，联邦学习与多模态数据的结合在确保多个维度的可信性方面引入了前所未有的挑战。

最近的研究揭示了联邦学习和多模态AI系统中的重大漏洞。例如，Huang et al.（2025）证明了医疗多模态大语言模型容易受到跨模态越狱攻击，攻击成功率超过80%。类似地，Shi et al.（2024）表明恶意联邦学习服务器可以通过精心设计的模型更新恢复高质量的私人医疗数据。这些发现突出了在多模态联邦学习系统中全面保证可信性的迫切需求。

当前解决联邦学习可信性的方法通常专注于个别方面，如隐私保护（Dwork et al. 2014）、对抗攻击的安全性（Goodfellow et al. 2015）或模型结果的公平性（Dwork et al. 2012）。然而，这些孤立的解决方案未能解决不同可信性维度之间的复杂交互以及多模态数据集成带来的独特挑战。

医疗领域呈现出现有解决方案无法充分解决的额外复杂性。由于患者群体、成像协议和临床实践的差异，医疗数据在机构间表现出高度异构性。此外，医疗应用不仅需要高准确性，还需要可解释性、跨人口统计群体的公平性以及符合监管标准。在医疗环境中风险特别高，模型失败可能直接影响患者安全和临床结果。

为了解决这些挑战，我们提出了TrustMFL，一个专门为医疗应用设计的综合可信多模态联邦学习框架。我们的方法做出了以下关键贡献：

**多维度可信性框架**：我们开发了一个统一的评估系统，同时评估隐私保护、安全鲁棒性、公平性保证和可解释性，提供系统可信性的整体视图。

**跨模态威胁建模**：我们为多模态联邦学习环境建立了全面的威胁模型，并开发了解决跨多个数据模态攻击的新颖防御机制。

**动态可信性评估**：我们引入了自适应算法，持续监控可信性指标，并基于实时评估动态调整客户端参与和聚合策略。

**医疗专用优化**：我们设计了领域特定模块，解决独特的医疗需求，包括法规合规、临床工作流集成和医疗数据特征。

我们在三个医疗多模态数据集上的广泛实验评估证明了TrustMFL在实现优越可信性保证的同时保持竞争性能的有效性。该框架成功防御了各种攻击场景，并为模型决策提供了可解释的见解，使其适合真实世界的医疗部署。

## Paper Organization / 论文组织

The remainder of this paper is organized as follows. Section 2 reviews related work in federated learning, multimodal learning, and trustworthy AI. Section 3 presents our threat model and problem formulation. Section 4 describes the TrustMFL framework architecture and key components. Section 5 details our experimental setup and evaluation results. Section 6 discusses implications and limitations. Section 7 concludes the paper and outlines future work.

本文的其余部分组织如下。第2节回顾了联邦学习、多模态学习和可信AI的相关工作。第3节提出了我们的威胁模型和问题表述。第4节描述了TrustMFL框架架构和关键组件。第5节详细说明了我们的实验设置和评估结果。第6节讨论了影响和局限性。第7节总结了论文并概述了未来工作。
