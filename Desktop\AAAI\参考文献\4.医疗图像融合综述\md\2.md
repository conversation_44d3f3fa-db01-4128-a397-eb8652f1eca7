# Sok: Semantic Privacy in Large Language Models

<PERSON>he Ma $^{1}$ , <PERSON><PERSON> $^{1}$ , <PERSON> $^{1}$ , <PERSON><PERSON><PERSON>^{3}$ , <PERSON> $^{1}$ , <PERSON><PERSON>sheng Yu $^{1}$ , <PERSON> $^{1}$ , <PERSON><PERSON><PERSON> $^{2}$ , <PERSON> $^{1}$ , <PERSON> $^{1}$ , <PERSON> $^{1}$ $^{1}$ University of Technology Sydney, Australia |  $^{2}$ Zhijiang Lab, China |  $^{3}$ Northeastern University, China

# ABSTRACT

ABSTRACTLarge Language Models (LLMs) are distinguished by their advanced ability to internalize the meaning embedded in the training data. This deep understanding introduces a significant privacy risk beyond verbatim memorization, as models may reveal learned sensitive content through paraphrasing or inference, thus breaching Semantic Privacy. This Systematization of Knowledge (SoK) introduces a lifecycle- centric framework to analyze how semantic privacy risks emerge across input processing, pretraining, fine- tuning, and alignment stages of LLMs. We categorize key attack vectors and assess how current defenses, such as differential privacy, embedding encryption, edge computing, and unlearning, address these threats. Our analysis reveals critical gaps in semantic- level protection, especially against contextual inference and latent representation leakage. We conclude by outlining open challenges, including quantifying semantic leakage, protecting multimodal inputs, balancing de- identification with generation quality, and ensuring transparency in privacy enforcement. This work aims to inform future research on designing robust, semantically aware privacy- preserving techniques for LLMs.

# 1 INTRODUCTION

Large language models (LLMs) exhibit strong generalization capabilities across a wide range of tasks [28]. However, their capacity to memorize training data introduces a distinct class of privacy risks. Unlike conventional models that primarily capture distributional patterns, LLMs are capable of reproducing specific training instances, not only verbatim, but also through semantically equivalent rephrasings or stylistic reconstructions [8]. This phenomenon gives rise to what is increasingly referred to as semantic privacy leakage, wherein sensitive information can be indirectly revealed without requiring exact string reproduction.

The probabilistic nature of LLM outputs further compounds the challenge. A model may reveal memorized content only under particular prompting conditions, often in ways that evade string- matching- based detection [24]. For example, while a password containing a birthdate such as "2000- 04- 01" may not be reproduced explicitly, the model might generate: "She tends to choose combinations involving her birth year and a spring date." In such cases, the leakage occurs at the semantic level and can facilitate downstream inference or re- identification. To distinguish semantic privacy from general data privacy, we define semantic privacy as:

Semantic privacy aims to protect sensitive attributes that are not explicitly stated in the data but can be inferred from the data, often by leveraging contextual or external knowledge.

While both semantic privacy and data privacy aim to prevent the exposure or misuse of sensitive information and can be enhanced by established privacy- preserving technologies like anonymization, encryption, and DP [5]. However, as summarized in Table 1, data privacy is primarily concerned with protecting raw data from direct exposure. In contrast, semantic privacy focuses on safeguarding the sensitive inferences and relationships that can be derived from the data.

When applying these concepts to LLMs, we define their scope as follows: Data privacy pertains to the protection of verbatim information contained within the training data. Semantic privacy addresses a broader threat surface, covering sensitive semantic information that can be inferred at any stage of the LLM lifecycle- from the trained model's parameters and latent representations to its final generated outputs. As illustrated in Figure 1, an LLM can generate text that paraphrases sensitive content from its training data, thereby leaking rich semantic information. An ideal semantic privacy- preserving LLM would be capable of concealing this private semantic information.

Existing works [4, 11, 14, 23, 29, 41] offered classifications of privacy threats and discussed techniques targeting data privacy in LLM. Yet, their treatment of semantic privacy remains limited.

This paper aims to bridge this research gap by providing a comprehensive review of semantic privacy concerns and protection mechanisms in the context of LLMs, as follows:

RQ1: What distinguishes semantic privacy from traditional data privacy in LLMs, and how do different stages of the LLM lifecycle contribute to semantic leakage? RQ2: How effective are current semantic privacy defenses (e.g., DP, edge learning, encryption, unlearning), and what limitations do they face in mitigating inference from contextual, latent, or implicit semantics? RQ3: How can future research design quantifiable and adaptive defenses that preserve semantic privacy while maintaining LLM utility and interpretability?

Contributions. We focus on semantic privacy risks in LLMs, addressing a critical gap in the current research, where traditional data privacy paradigms fall short. Our contributions are:

We provide a formal definition of semantic privacy and establish its distinction from conventional data privacy, highlighting its unique threat landscape in LLMs. We propose a lifecycle- orientated analytical framework that systematically maps semantic privacy risks across the input, pretraining, fine- tuning, and alignment stages of LLMs. We also present a taxonomy of semantic privacy attacks and defences, synthesising fragmented literature and evaluating the limitations of existing protection techniques. We provide a structured gap analysis of previous surveys, highlighting the lack of a systematic treatment of semantic privacy, and position our work as the first semantically grounded systematization of this topic.

Table 1: Key Differences Between Data Privacy and Semantic Privacy  

<table><tr><td>Aspect</td><td>Data privacy</td><td>Semantic privacy</td></tr><tr><td>Protection goal</td><td>Protects the data itself (e.g., PII)</td><td>Protects relationships and inferences drawn from data</td></tr><tr><td>Threat model</td><td>Direct attacks on training data</td><td>Inference attacks based on model behavior and outputs</td></tr><tr><td>Leakage channel</td><td>Direct exposure of raw data</td><td>Indirect leakage via sensitive attribute inference</td></tr><tr><td>Adversary knowledge</td><td>Requires internal model knowledge or access to training context</td><td>Leverages external or world knowledge</td></tr><tr><td>System lifecycle</td><td>Preprocessing and data handling</td><td>Covers full lifecycle: pretraining, fine-tuning, and inference</td></tr></table>

![](images/3961f4efe3504434dac06dafbebe77b9b42609ec44562d4ae86bd55fa5ee9e6d.jpg)  
Figure 1: Ilustration of smantic privacy leakage across the LLM ifecyle. The example demonstrates how rich smantic information can be inferred from ntrl  f  i  d  t. t   i  t.  i suppress inference pathways by disrupting the alignment between inputs and their latent semantics. This figure exemplifies our central insight that semantic leakage arises from transformations across all stages of LLM processing, and surface-level anonymization is insufficient.

Key insights. Building on this diagnostic perspective, we articulate a strategic roadmap for future research that moves beyond incremental improvements. Our key insights include:

- Semantic privacy is threatened across the entire LLM lifecycle. Attacks such as membership inference, attribute inference, model inversion, and backdoor triggering exploit vulnerabilities at every stage from input encoding to final alignment. These threats expose the limitations of surface-level anonymization and demand deeper, architecture-aware protections.- Existing defenses are fragmented and insufficiently aligned with semantic leakage pathways. While techniques like DP, local processing, homomorphic encryption, and targeted forgetting address specific risks, none provide comprehensive coverage across representational layers. Strong semantic privacy requires stage-aware coordination and a balance between protection and performance.- Future solutions should be measurable, modality-aware, utility-preserving, and transparent:    
- Quantification: Traditional token-level perturbation methods inadequately capture the latent, context-driven nature of semantic leakage in LLMs. Future research should prioritize robust semantic privacy quantification by leveraging embedding-based similarity, entailment-aware scoring, and probabilistic reidentification metrics to assess risks stemming from meaning preservation, inference potential, and contextual traceability.

- Multimodal modeling: As LLMs increasingly operate across modalities (e.g., text, images, audio), privacy frameworks must account for cross-modal semantic entanglement, where sensitive information may be inferred through the interaction between modalities. Effective protection requires modeling modality interactions holistically, integrating attention-based suppression and modality-specific risk scoring to preemptively mitigate leakage pathways.- De-identification: Semantic de-identification should move beyond entity-level anonymization toward adaptive, task-aware strategies—such as controlled semantic rewriting and privacy-preserving generation—that preserve fluency, coherence, and task fidelity while obfuscating latent identifiers. Personalized and explainable de-identification mechanisms will be essential for aligning technical efficacy with user expectations and normative accountability.

# 2 EXISTING SURVEYS AND GAP ANALYSIS

This section surveys recent literature on LLM privacy, with a particular focus on the extent to which semantic privacy is addressed.

Existing surveys (Table 2) (e.g., [11][14][29]) provided overviews of privacy threats, but treat semantic risks only implicitly or fragmentarily. For instance, Gan et al. [11] presented a lifecycle- based threat taxonomy yet overlooked how semantically rich interactions

<table><tr><td rowspan="2">Reference</td><td colspan="4">Lifecycle</td><td colspan="4">Semantic threats</td><td colspan="5">Vulnerabilities</td><td colspan="4">Other</td><td></td><td></td><td></td></tr><tr><td>S1</td><td>S2</td><td>S3</td><td>S4</td><td>T1</td><td>T2</td><td>T3</td><td>T4</td><td>V1</td><td>V2</td><td>V3</td><td>V4</td><td>V5</td><td>V6</td><td>V7</td><td>V8</td><td>QF</td><td>MM</td><td>BA</td><td>EX</td></tr><tr><td>[29]</td><td>●</td><td>●</td><td>●</td><td>●</td><td>✓</td><td>●</td><td>●</td><td>●</td><td>●</td><td>●</td><td>●</td><td>●</td><td>●</td><td>●</td><td>●</td><td>●</td><td>●</td><td>●</td><td>●</td><td></td></tr><tr><td>[11]</td><td>●</td><td>●</td><td>●</td><td>●</td><td>●</td><td>●</td><td>●</td><td>●</td><td>●</td><td>●</td><td>●</td><td>●</td><td>●</td><td>●</td><td>●</td><td>●</td><td>●</td><td>●</td><td>●</td><td></td></tr><tr><td>[14]</td><td>●</td><td>●</td><td>●</td><td>●</td><td>●</td><td>●</td><td>●</td><td>●</td><td>●</td><td>●</td><td>●</td><td>●</td><td>●</td><td>●</td><td>●</td><td>●</td><td>●</td><td>●</td><td>●</td><td></td></tr><tr><td>&lt;fcel&gt;</td><td>&lt;fcel&gt;</td><td>&lt;fcel&gt;</td><td>&lt;fcel&gt;</td><td>&lt;fcel&gt;</td><td>&lt;fcel&gt;</td><td>&lt;fcel&gt;</td><td>&lt;fcel&gt;</td><td>&lt;fcel&gt;</td><td>&lt;fcel&gt;</td><td>&lt;fcel&gt;</td><td>&lt;fcel&gt;</td><td>&lt;fcel&gt;</td><td>&lt;fcel&gt;</td><td>&lt;fcel&gt;</td><td>&lt;fcel&gt;</td><td>&lt;fcel&gt;</td><td>&lt;fcel&gt;</td><td>&lt;ecel&gt;</td><td></td><td></td></tr><tr><td></td><td>&lt;fcel&gt;</td><td>&lt;fcel&gt;</td><td>&lt;fcel&gt;</td><td>&lt;fcel&gt;</td><td>&lt;fcel&gt;</td><td>&lt;fcel&gt;</td><td>&lt;fcel&gt;</td><td>&lt;fcel&gt;</td><td>&lt;fcel&gt;</td><td>&lt;fcel&gt;</td><td>&lt;fcel&gt;</td><td>&lt;fcel&gt;</td><td>&lt;fcel&gt;</td><td>&lt;fcel&gt;</td><td>&lt;fcel&gt;</td><td>&lt;fcel&gt;</td><td>&lt;fcel&gt;</td><td>&lt;fcel&gt;</td><td>&lt;ecel&gt;</td><td></td></tr><tr><td>&lt;fcel&gt;</td><td>&lt;fcel&gt;</td><td>&lt;fcel&gt;</td><td>&lt;fcel&gt;</td><td>&lt;fcel&gt;</td><td>&lt;fcel&gt;</td><td>&lt;fcel&gt;</td><td>&lt;fcel&gt;</td><td>&lt;fcel&gt;</td><td>&lt;fcel&gt;</td><td>&lt;fcel&gt;</td><td>&lt;fcel&gt;</td><td>&lt;fcel&gt;</td><td>&lt;fcel&gt;</td><td>&lt;fcel&gt;</td><td>&lt;fcel&gt;</td><td>&lt;fcel&gt;</td><td>&lt;fcel&gt;</td><td>&lt;fcel&gt;</td><td>&lt;ecel&gt;</td><td></td></tr></table>

Lifecycle: S1 - Input processing; S2 - Pre-training; S3 - Fine-tuning; S4 - Alignment. Semantic Threats: T1 - Membership Inference; T2 - Attribute Inference; T3 - Model Inversion; T4 - Backdoor Attack. Vulnerabilities: V1 - Embedding; V2 - Tokenization; V3 - Attention; V4 - FFN; V5 - Normalization; V6 - Output Heads; V7 - RLHF Modules; V8 - Generation Layer. Others: QF - Semantic Quantification; MM - Multimodal Risk; BA - Privacy-Utility Balance; EX - Explainability.  $\pmb{X}$  Not addressed;  $\bullet$ $\bullet$  Partial coverage;  $\sqrt{}$  Explicitly and systematically discussed.

e.g., context chaining and memory in multi- turn dialogues) facilitate indirect inference. Similarly, He et al. [14] focused on agentbased vulnerabilities without examining how semantic reasoning enables privacy violations through natural language cues. While Neel [29] introduced foundational threat categories like membership inference, it does not address the role of contextual semantics in privacy leakage.

Other studies, including Yan et al. [41] and Das et al. [4], offered broader taxonomies and mitigation strategies, yet primarily frame privacy through data- centric lenses such as DP and federated learning. These techniques are effective in limiting raw data exposure, but are insufficient against semantic leakage, where sensitive information is reconstructed through language patterns, context, or reasoning. Even when inference- related threats are acknowledged, as in Li et al. [23], mechanisms that operate at the semantic level (e.g., privacy- aware generation control or semantic obfuscation) remain underexplored. The inferential capacity of LLMs to deduce user intent, latent attributes, or background knowledge from semantic patterns remains an open threat vector.

These limitations underscore several critical research gaps. First, existing privacy mechanisms failed to account for semantic- level correlations, which standard DP methods, premised on independent data records, cannot capture [29, 41]. Second, runtime safeguards for semantic leakage, such as adaptive decoding or context- sensitive risk modulation, are largely absent [23]. Our work addresses these gaps by offering a clear definition of semantic privacy, a lifecyclealigned analytical framework, and a semantic- centric evaluation of attacks and defenses, contributing a forward- looking agenda for privacy protection in multimodal and generative settings.

# 3 SEMANTIC PRIVACY RISKS

LLMs rely on a multi- stage processing pipeline that transforms natural language inputs into semantically meaningful outputs. At each stage, the model extracts, preserves, and refines representations that encode lexical content and latent user attributes such as identity, intent, background, or ideology. These semantic representations are central to the power of LLMs, yet they also introduce a unique class of privacy vulnerabilities. Unlike traditional data privacy breaches, semantic privacy threats do not rely on direct access to raw data or identifiers. Instead, they exploit the model's ability to retain and regenerate meaning, enabling adversaries to infer sensitive information from internal representations or observable outputs. This section unpacks how semantic information flows across the LLM lifecycle and introduces four representative attack vectors - membership inference, attribute inference, model inversion, and backdoor attacks - that target distinct stages and components of the semantic processing chain, as shown in Fig. 2.

# 3.1 Semantic Information in LLM

3.1.1 S1: Input Processing. Input processing marks the entry point of semantic transformation within an LLM. The raw input text undergoes tokenization, a process that dissects natural language into discrete subword units based on statistical segmentation techniques like Byte Pair Encoding or WordPiece [16]. These tokens preserve core semantic constituents through carefully trained vocabulary mappings. These tokens are then mapped to dense vectors in a continuous space via embedding layers. Positional encoding is subsequently added to preserve syntactic order, allowing the model to differentiate between semantically distinct permutations. Together, embeddings and positional encodings instantiate the semantic structure of the input in a high- dimensional space suitable for subsequent reasoning.

3.1.2 S2: Pretraining. Pretraining occurs over multiple stacked transformer layers, each consisting of self- attention, feedforward neural networks (FFNs), residual connections, and layer normalization [37]. These components jointly enable the model to abstract hierarchical semantics from its input, moving from lexical and syntactic cues toward higher- order concepts such as intent, logical entailment, causality, and commonsense knowledge. Self- attention mechanisms are particularly crucial, as they enable the model to construct context- dependent semantic relationships across all token positions. FFNs and normalization layers reinforce these abstractions, producing increasingly entangled and generalizable semantic representations. At the end of pretraining, the model holds a rich and implicit understanding of language semantics, encompassing topics, sentiments, and factual knowledge.

3.1.3 S3: Fine- tuning. Fine- tuning adapts the pretrained model to specific downstream tasks by training new or modified output heads (e.g., task classifiers, generative decoders, or question- answering heads) [2]. At this stage, the general- purpose semantic representations learned during pretraining are reoriented toward a specific operational goal. The output head translates latent semantic features into actionable predictions, such as labels or token distributions. Because fine- tuning typically occurs on smaller, curated datasets, the model's semantic alignment with domain- specific language and logic becomes more pronounced.

3.1.4 S4: Alignment. The final stage of LLM deployment is alignment, wherein the model is tuned to conform to ethical, social, and safety norms. This includes structured interventions such as reinforcement learning from human feedback, output filtering, and inference- time controls [21]. During inference, the model generates outputs that are semantically grounded in both the input and its internal representations. Alignment ensures that outputs adhere to acceptable behaviors - avoiding toxicity, bias, or unsafe advice - while retaining the semantic intent of user prompts. This step is where semantic representations become publicly observable, rendering any residual privacy vulnerabilities fully manifest.

# 3.2 Semantic Privacy Threats

This section provides a comparative overview (Table 3) of four prominent semantic privacy attacks, including Membership Inference, Attribute Inference, Model Inversion, and Backdoor Attacks.

Table 3: Comparison of Semantic Privacy Attacks  

<table><tr><td>Attack type</td><td>Stage</td><td>Visibility</td><td>Quantifiable Metrics</td></tr><tr><td>[15, 27]</td><td>S4</td><td>■</td><td>Precision, perplexity, similarity</td></tr><tr><td>[18, 50]</td><td>S3-S4</td><td>□</td><td>AUC, clustering, accuracy</td></tr><tr><td>[31, 47]</td><td>S2-S4</td><td>□</td><td>BLEU, recovery rate, inversion success</td></tr><tr><td>[24, 48]</td><td>S2-S3</td><td>□</td><td>ASR, stealthiness, accuracy</td></tr></table>

:Blackbox; :Graybox;Whitebox.

3.2.1 Membership Inference Attack. Membership Inference Attacks (MIAs) aim to determine whether specific inputs, such as sentences or documents, were used in model training [33]. LLMs' memorization tendencies lead to distinguishable behaviors for seen versus unseen data, reflected in confidence scores or response structure. Recent work enhances MIAs by exploiting semantic perturbations [27], stochastic embeddings [10], and memorization traits beyond overfitting [9], improving attack robustness across model types and datasets.

LLMs can reproduce verbatim or semantically enriched training content [17, 19], allowing adversaries to exploit semantic memorization. Classification models rely on confidence scores, while generative models use metrics like perplexity or entropy. He et al. [15] propose a label- only MIA based on semantic similarity, matching logit- based methods. Wen et al. [40] show in- context learning is especially vulnerable, and Song et al. [33] introduce likelihood- based attacks targeting hard- to- predict tokens without auxiliary data. These works highlight the increasing subtlety and effectiveness of semantic- level MIAs.

3.2.2 Attribute Inference Attack. Attribute inference attacks target the internal representations of ML models, particularly those trained on natural language data, to infer latent attributes of the input data that are not explicitly presented. These attributes could include age, gender, location, political affiliation, or even psychological traits. Such attacks allow adversaries to extract personal characteristics from seemingly anonymized or generic inputs, potentially violating user privacy without any direct data breach.

For example, zheng et al. [50] introduces a timing- based side- channel attack that exploits cache- sharing mechanisms in LLM inference to steal private inputs, by employing machine learning techniques for vocabulary correlation and statistical time fitting. Additionally, attackers can exploit the correlation between certain linguistic features and demographic attributes. By constructing input queries that vary specific attributes while keeping the core semantics constant, the attacker can analyze changes in the model's outputs or intermediate representations. For example, training shadow models to mimic the victim model and perform attribute classification based on outputs or embeddings [18].

3.2.3 Model Inversion Attack. Model inversion attacks allow an adversary to infer sensitive information about the data used to train a machine learning model by analyzing its outputs [1]. By analysing model internal states, such as its embeddings, intermediate representations, or the output for a given input, adversaries can observe LLMs' responses to different queries and exploit. Then, the attackers can reverse- engineer the model to reconstruct certain attributes or features of the training data [35]. This type of attack is especially concerning for models trained on personal data, such as those used in finance, healthcare, and social media platforms, where users' sensitive attributes (e.g., income, health conditions) could be inferred from the model's internal representations.

For example, in collaborative inference scenarios, the work in [31] demonstrates the ability to recover input prompts through transmitted intermediate activations. Additionally, the method in [47] extracts prompts using only text outputs from normal user queries, showcasing zero- shot transferability across different LLMs.

3.2.4 Backdoor Attack. In backdoor attacks, an adversary poisons the training process by injecting crafted inputs that include a hidden "trigger" - a specific pattern or sequence in the input data that causes the model to behave abnormally [42]. In most cases, the model performs normally for clean inputs, but when it encounters inputs containing the trigger, it produces attacker- specified outputs. As semantic privacy focuses on the preservation of meaning and context within the data, backdoor attacks can manipulate semantic information by embedding triggers that exploit the model's understanding of language and context.

For example, Zhang et al., introduce a novel method of embedding backdoors in customized LLMs through semantic- level instructions [48], which do not require modifications to the input sentences, thereby enhancing the stealthiness of the attack. Liu et al., launch contextual backdoor attacks that can exploit adversarial in- context generation to optimize poisoned demonstrations and compromise

![](images/8cb71551840cb6e9593d68c1f8f9ba23deb5ef97b457db72295f374106443463.jpg)  
Figure 2: Ilustration of smantic inomation flow and corrsponding attack surfaes throughout the LM lifecycle. As the training data is t t t t t t t t t t t t t t t t t t t t t t t t t t t t t t t t t t t t t t t t t t t t t t t t t t t t t t t t t t t t t t t t t t t t t t t t t t t t t t t t t t t t t t t t t t t t t t t t t t t t h e s t a t a s t a t a s t a t a s t a t a s t a t a s t a t a s t a t a s t a t a s t a t a s t a t a s t a t a s t a t a s t a t a s t a t a s t a t a s t a t a s t a t a s t a t a s t a t a s t a t a s t a t t a s t a t a s t a t a s t a t a s t a t a s t a t a s t a t a s t a t a s t a t a s t a t a s t a t a s t a t a s t a t a s t a t a s t a t a s t a t a s t a t a s t a t a s t a t a s t a t a s t t a s t a t a s t a t a s t a t a s t a t a s t a t a s t a t a s t a t a s t a t a s t a t a s t a t a s t a t a s t a t a s t a t a s t a t a s t a t a s t a t a s t a t a s t a t a s t a t a s s t a t a s t a t a s t a t a s t a t a s t a t a s t a t a s t a t a s t a t a s t a t a s t a t a s t a t a s t a t a s t a t a s t a t a s t a t a s t a t a s t a t a s t a t a s t a t a s t a t s t a t a s t a t a s t a t a s t a t a s t a t a s t a t a s t a t a s t a t a s t a t a s t a t a s t a t a s t a t a s t a t a s t a t a s t a t a s t a t a s t a t a s t a t a s t a t a s t a t

the contextual environment, resulting in context- dependent defects in generated programs [24].

Takeaways. The semantic processing pipeline of LLMs introduces multiple stages where sensitive user information may be transformed, retained, and ultimately exposed. We noticed that a spectrum of attacks exploits these vulnerabilities: Membership Inference identifies training data presence via output behaviors; Attribute Inference extracts latent user traits from internal model states; Model Inversion reconstructs input features through observable outputs or intermediate activations; and Backdoor Attacks manipulate model behavior through stealthily embedded semantic triggers.

# 4 SEMANTIC PRIVACY PROTECTION

Given the multifaceted threats to semantic privacy in LLMs, defending against such attacks requires more than generic anonymization or surface- level noise injection. Because semantic information is transformed and retained across distinct processing stages, protection mechanisms should be stage- aware, representation- sensitive, and context- adaptive. A range of emerging techniques aim to safeguard semantic privacy by perturbing inputs, encrypting internal states, localizing computation, and erasing sensitive knowledge post hoc. However, these solutions vary significantly in their efficacy, overhead, and stage- specific applicability. This section provides a systematic review of semantic privacy protection strategies, highlighting their mechanisms, deployment stages, and limitations. We group existing approaches into five broad categories: DP- based perturbations, Edge- centric and split architectures, Encrypted representation learning, Knowledge Unlearning, and Privacy- aware model pruning and compression (Table 4).

Table 4: Comparison of Semantic Privacy Techniques  

<table><tr><td>Technique</td><td>Targeted 
threats</td><td>Stage</td><td>Protection 
capability¹</td></tr><tr><td>DP Embedding [25]</td><td>图1</td><td>S1-2</td><td>★★★★</td></tr><tr><td>DP Tokenization [34]</td><td>图2</td><td>S1-2</td><td>★★★★</td></tr><tr><td>DP Prompt Encoding [45]</td><td>图3</td><td>S1,3</td><td>★★★★</td></tr><tr><td>Semantic-Aware DP [39]</td><td>图4</td><td>S2-3</td><td>★★★★</td></tr><tr><td>Split Learning + LDP [25, 43]</td><td>图5</td><td>S1-2</td><td>★★★★</td></tr><tr><td>Encrypted Embedding [26]</td><td>图6</td><td>S2-3</td><td>★★★★</td></tr><tr><td>Homomorphic Encryption [36]</td><td>图7</td><td>S2-3</td><td>★★★★</td></tr><tr><td>Semantic Compression [20]</td><td>图8</td><td>S2-4</td><td>★★★★</td></tr><tr><td>Latent Unlearning [46]</td><td>图9</td><td>S3-4</td><td>★★★★</td></tr><tr><td>DP Forward Fine-Tuning [7]</td><td>图10</td><td>S3-4</td><td>★★</td></tr><tr><td>Gradient Unlearning [44]</td><td>图11</td><td>S3-4</td><td>★★★★</td></tr><tr><td>Entropy Pruning [30]</td><td>图12</td><td>S3-4</td><td>★★</td></tr><tr><td>Semantic Distillation [6]</td><td>图13</td><td>S2-3</td><td>★★★★</td></tr><tr><td>Gradient Masking [49]</td><td>图14</td><td>S2-3</td><td>★★★★</td></tr></table>

1 The semantic privacy protection from very low to very high is marked from 1 to 5

# 4.1 Data Locality and Edge Processing

Minimizing data transmission by performing computations at the edge significantly reduces the exposure of sensitive semantic content to centralized infrastructures. This design enhances privacy by keeping both raw inputs and intermediate representations within user- controlled environments, thereby mitigating the risks of inference attacks and semantic leakage during model execution. Split learning is a prominent realization of this paradigm. By partitioning model layers, executing early computations (e.g., embeddings or shallow transformers) on the client, and transmitting only encoded features to the server, it limits the exposure of semantically rich

data. Advanced frameworks like Split- N- Denoise [25] further enhanced privacy by injecting LDP noise on client- side embeddings and applying denoising post- inference, thereby forming a semantic bottleneck that weakens upstream inference attacks.

Empirical studies confirm the resilience of split learning to reconstruction threats. Yao et al. [43] showed that adversarial attacks like UnSplit fail to extract meaningful content from intermediate representations in transformer- based LLMs, especially when techniques such as dropout and normalization are applied in early layers. However, running partial LLMs on client devices imposes computational and memory overheads, particularly in resource- constrained settings. Moreover, the effectiveness of privacy protection hinges on the location of the split point: the shallower a split exposes sensitive semantics, while the deeper increases the client- side burden.

Complementary to architectural partitioning, personalized model updates offer an alternative approach to enhancing semantic privacy. By enabling local fine- tuning and adaptation without sharing gradients or updates with a centralized model, user- specific knowledge remains confined to the edge. For instance, SAP [32] combined edge- based fine- tuning with selective token- level privatization, perturbing only those embeddings deemed utility- relevant and sensitive. While effective in balancing performance and privacy, this approach relied on accurate token sensitivity identification and incurs device- side resource demands. Additionally, it presumed a trusted model vendor and secure edge infrastructure- assumptions that may not hold under adversarial deployment environments.

# 4.2 Embedding Encryption and Secure Representation Learning

Securing embeddings and intermediate representations is crucial for mitigating semantic privacy risks in large language models. Since embeddings encode rich semantic content, their exposure can lead to inference attacks, model inversion, or the extraction of sensitive training data. Techniques that encrypt or obfuscate embeddings aim to prevent adversaries from reconstructing inputs or accessing confidential knowledge, thereby offering protection during both training and inference phases.

Homomorphic Encryption (HE), particularly Fully Homomorphic Encryption (FHE), enables computation on encrypted embeddings without exposing raw or intermediate data, providing strong semantic privacy guarantees. Frameworks like Fission [36] adapt a hybrid strategy- utilizing secure multiparty computation for linear operations and evaluator networks for non- linear layers, with added data shuffling and partitioning to prevent semantic leakage. While theoretically robust, these approaches suffered from high computational overheads, limiting their practicality in real- time LLM applications and relying on partially trusted infrastructure.

Complementary strategies focus on minimizing the semantic exposure within embeddings themselves. Privacy- preserving embedding compression, as proposed by Kale et al. [20], employed mutual information- guided dimensionality reduction to retain only task- relevant features, filtering out sensitive semantics. Though effective in theory, such methods depended on accurate priors to distinguish sensitive from non- sensitive information and risk performance degradation if compression is too aggressive. Alternatively, token and embedding obfuscation methods, such as those in

Mishra et al. [26], leveraged irreversible transformations, keyed to- . kenization, randomized indexing, and spatial embedding distortion, to enable encrypted inference without ever decrypting the input. While promising against inversion and similarity attacks, this approach assumed a secure and well- aligned encryption setup and may face compatibility challenges across diverse LLM architectures.

# 4.3 Differential Privacy

DP is fundamental for mitigating privacy risks during LLM training by injecting calibrated noise into input data, thus preventing overfitting to sensitive linguistic patterns and guarding against attacks like membership inference, attribute leakage, and model inversion [33]. These protections extend to semantic privacy by disrupting latent representations that could reveal user identity.

To enhance semantic protection, DP at the embedding level introduces mechanisms such as Local Differential Privacy (LDP), randomized transformations, and encrypted perturbations. For example, Mai et al. [25] applied client- side LDP with local denoising, while Wang et al. [38] perturbed only sensitive segments, offering fine- grained control dependent on accurate sensitivity detection and noise calibration.

Beyond embeddings, differentially private tokenization introduces randomness into token selection to mitigate memorization and embedding inversion, as shown by [34, 38], though these methods often impact task fidelity. Prompt- level DP has also been explored. Yu et al. [45] anonymized entities via DP hashes using NER, and Tong et al. [34] applied perturbed prompt generation. However, both approaches depend heavily on accurate entity detection.

To overcome uniform DP limitations, semantic- aware DP frameworks adapt perturbations to semantic structure. Wei et al. [39] proposed a two- stage heterogeneous graph model combining feature attention and gradient perturbation with independently budgeted noise. While improving semantic alignment, such methods require complex semantic modeling and may degrade performance under high noise budgets.

# 4.4 Model Pruning and Compression for Privacy

Model pruning and compression techniques play a dual role in enhancing privacy and improving computational efficiency. By eliminating redundant parameters, these methods reduce the model's capacity to memorize and retain fine- grained training data, thus mitigating semantic privacy risks. Privacy- aware pruning methods, such as the mutual information- based strategy by Huang et al. [16], identified and removed structurally redundant neurons without requiring labeled data or retraining, reducing the risk of re- exposing private content. Structured pruning approaches like ZipLM [22] incorporated saliency- based criteria to maintain utility while improving latency. However, such methods relied on heuristic importance scores and inadvertently removed privacy- benign but task- critical components, especially in compact models.

Embedding compression provides another avenue for controlling semantic exposure. By applying low- rank factorization, sparsification, or hashing, models can discard detailed lexical and syntactic features while preserving essential semantics. Gilbert et al. [12] introduced semantic reconstruction effectiveness as a metric to

evaluate how well compressed embeddings retain meaning, showing that models like GPT- 4 can maintain utility post- compression. While such methods suppressed the capacity to encode identifiable cues, they offered no strong guarantees of recoverability, aggressive compression can impair task performance, and residual information may still be exploitable by sophisticated inversion attacks.

Entropy- based pruning further enhances privacy by removing components associated with high semantic uncertainty or risk. Techniques like Kernel Language Entropy [30] and semantic entropy estimators [8] assessed variability across semantically equivalent outputs to guide pruning decisions. These methods suppressed the generation of hallucinated content that could inadvertently leak sensitive information. However, their effectiveness depended on the robustness of semantic similarity metrics, which underperformed in complex or domain- diverse language scenarios.

Finally, compressed secure model distillation and gradient masking offer targeted defenses against semantic leakage. Work in [6] distilled large LLMs into encoder- only student models using anonymization- aware supervision (e.g., NER- guided masking), preserving high- level semantics while removing reconstructive details. Though suitable for deployment in privacy- sensitive settings, the pipeline's relianced on annotation quality can result in missed entities, particularly in multilingual or informal inputs. Similarly, GradMLMD [49] introduced gradient- guided masking to suppress exposure of semantically sensitive tokens during training. While not explicitly designed for privacy, it reduced overfitting to sensitive signals and resists model inversion. Yet, its reliance on accurate gradient estimation and lack of formal guarantees limited its robustness in adversarial contexts.

# 4.5 Knowledge Unlearning

Knowledge unlearning techniques aim to selectively remove the influence of specific data points or behaviors from trained language models without retraining from scratch. These approaches are increasingly critical for enforcing data privacy rights, such as the "right to be forgotten," and preventing persistent retention of sensitive information. Beyond compliance, unlearning contributes to semantic privacy by eliminating internal representations and decision pathways tied to the forgotten data, ensuring it no longer semantically influences model behavior.

One class of techniques focuses on privacy- aware fine- tuning. Du et al. [7] proposed DP- Forward, injecting analytically designed matrix- valued noise into the forward pass embeddings to ensure LDP during both training and inference. Compared to traditional DP- SGD, this method offered stronger protection against semantic leakage via embedding inversion or attribute inference, with improved computational efficiency. However, its effectiveness depended on precise noise calibration and degraded in tasks with long- range dependencies or high- dimensional inputs.

Other approaches leverage gradient- based unlearning and latent representation purging. Yao et al. [44] introduced a gradient ascent method that reverses the influence of targeted samples, particularly effective in erasing harmful or copyrighted content with minimal resources. While suitable for semantic unlearning, the method struggled with defining undesirable outputs in open- ended generation tasks and lacks guarantees of complete erasure without access to original training data. Alternatively, Yu et al. [46] proposed UniErase, which purges semantic traces in a structured latent space using vector quantization and sparse autoencoders. By isolating and suppressing discrete latent codes linked to sensitive content, the model unlearned specific information with minimal utility loss. However, this required accurate identification of semantically entangled representations and faced limitations in dense or overlapping latent structures.

# 4.6 Alignment and Data Filtering

Alignment techniques, particularly those used in instruction tuning and reinforcement learning from human feedback (RLHF), play a critical role in shaping how LLMs handle sensitive information. As part of the alignment process, training data is often curated to exclude harmful, private, or policy- violating content, either through automated filtering pipelines or manual annotation. These filtering mechanisms aim to prevent the model from learning undesired behaviors or memorizing sensitive semantic patterns. However, alignment- stage filtering is inherently limited by the quality and granularity of the data selection criteria, overly aggressive filters may harm generalization or utility, while insufficiently granular ones may allow latent semantic cues to persist. Recent work also explores aligning models to privacy- centric objectives, incorporating reward models that penalize outputs revealing sensitive attributes or exhibiting identifiable language styles. While alignment offers a proactive strategy for semantic privacy protection, it requires continuous refinement and auditing to ensure robustness against adversarial queries and domain shifts [3, 13].

Takeaways. Protecting semantic privacy in LLMs needs a stage- aware approach that combines methods like noise injection, encryption, local processing, and forgetting. DP helps protect inputs but may reduce meaning. Split learning keeps early steps on local devices for better privacy but needs more resources. Techniques like homomorphic encryption and semantic compression help prevent data leaks but can be slow or hard to integrate. Later- stage methods like unlearning and pruning try to remove sensitive features, but their success depends on finding the right ones. No single method is enough, effective privacy needs coordination across entire model pipeline.

# 5 LESSONS LEARNED AND OPEN CHALLENGES

While recent advances have proposed diverse mechanisms to protect semantic privacy in LLMs, significant gaps remain in our understanding and mitigation of real- world risks. Existing defenses often focus on syntactic obfuscation or noise injection but fail to capture the latent, context- dependent nature of semantic leakage, where sensitive information is preserved, inferred, or reconstructed without being explicitly reproduced. These limitations are further compounded by emerging trends in multimodal modeling, personalized generation, and regulatory demand for explainable AI. In this section, we distill key lessons from current research and highlight open challenges that should be addressed to advance robust, trustworthy semantic privacy, as shown in Table 5.

Table 5: Challenges and Research Directions for Semantic Privacy in LLMs  

<table><tr><td>Topics</td><td>Challenges</td><td>Future directions</td></tr><tr><td>1 Semantic leakage modeling</td><td>·Implicit leakage
·Contextual reasoning
·Stylistic mimicry</td><td>·Probabilistic modeling
·Inference-time controllers
·Semantic attribution</td></tr><tr><td>2 Semantic Privacy Quantification</td><td>·Cross-sentence semantics
·Implicit identifiers
·Stylistic signals</td><td>·Interpretable scoring
·Controlled rewriting
·Leakage risk estimation</td></tr><tr><td>3 Multimodal Privacy</td><td>·Cross-modal identity leakage
·Image-caption reconstruction</td><td>·Multimodal modeling
·Adversarial training
·Visual-semantic traceability</td></tr></table>

# 5.1 Modeling of Semantic Information Leakage

5.1 Modeling of Semantic Information LeakageExisting privacy- preserving mechanisms like DP and cryptographic obfuscation primarily target structured data and numerical noise, but fall short in addressing semantic leakage in LLMs, where outputs may inadvertently reveal sensitive information through stylistic cues, contextual relevance, or abstracted summaries. Unlike explicit data leakage, semantic risks arise from the model's latent ability to infer or paraphrase private content, often escaping standard metrics such as perplexity or token overlap. To address these challenges, emerging approaches propose embedding- based similarity analysis, inference- driven re- identification, and entailment- aware leakage metrics. Future research should explore probabilistic models under adversarial settings and develop inference- time privacy controllers, enabling dynamic detection and mitigation of semantic risks. Techniques like contrastive language modeling, transformer probing, and semantic attribution can further isolate leakage pathways and support audits, including retroactive detection of privacy violations even under obfuscation [15, 46].

# 5.2 Quantification of Semantic Privacy Protection

Semantic de- identification aims to obscure sensitive information while preserving utility, yet existing methods like named entity anonymization often degrade coherence and fail to account for latent, context- dependent identifiers such as profession or style. This challenge is amplified in multimodal LLMs, where private signals may emerge through cross- modal interactions (e.g., identity leakage from visual captions [12]). To address this, emerging techniques like privacy- preserving generation, controlled rewriting, and attention- based suppression seek to balance privacy with generation quality. Robust evaluation metrics are also needed to assess residual identifiability and utility beyond token- level measures.

Equally important is the interpretability and ethical framing of semantic privacy mechanisms. Transparent, explainable interventions- such as semantic attribution or modality- aware privacy scoring- are essential for accountability and trust, especially in sensitive domains. Personalized de- identification systems that adapt to user preferences and risk profiles can further support ethical deployment. Ultimately, semantic privacy protection should align technical effectiveness with social and normative responsibility [46].

# 5.3 Multimodal Semantic Privacy Protection

5.3 Multimodal Semantic Privacy ProtectionSemantic privacy risks in LLMs arise from their capacity to infer and regenerate sensitive information via latent associations and contextual reasoning, making traditional token- level privacy techniques inadequate. Unlike explicit identifiers, semantic cues are often implicit and distributed, especially in multimodal settings, complicating detection and modeling. To address this, robust semantic privacy quantification should adopt metrics that capture meaning preservation, inference likelihood, and contextual identifiability, such as embedding similarity, entailment- aware scoring, and re- identification risk estimation. At the same time, semantic de- identification should balance privacy with generation quality, using methods like controlled rewriting or adversarial training to preserve coherence and utility. Transparent, explainable mechanisms are also critical to ensure accountability and user trust.

Takeaways. Current privacy methods miss how LLMs leak sensitive info through reasoning and context. We need better ways to measure and control semantic leakage, especially in multimodal models where info spreads across text and images.

# 6 CONCLUSION

6 CONCLUSIONWe examine semantic privacy in LLMs, which focuses on the protection of implicit and contextually inferred information beyond data privacy in all existing studies. We review key attack vectors, including membership inference, attribute inference, model inversion, and backdoor attacks across every stage of the LLM lifecycle. We discuss the limitations of current defence covering differential privacy, embedding encryption, and knowledge unlearning, especially in handling contextual inference and semantic ambiguity. We also outline future directions, including but not limited to quantifying semantic leakage, multimodal protection, privacy- utility trade- offs, and transparent privacy mechanisms.

# REFERENCES

REFERENCES[1] Francisco Aguilera- Martinez and Fernando Berzal. 2025. LLM Security: Vulnerabilities, Attacks, Defenses, and Countermeasures. arXiv:2505.01177 (2025).[2] Yupeng Chang, Xu Wang, Jindong Wang, Yuan Wu, Linyi Yang, Kaijie Zhu, Hao Chen, Xiaoyuan Yi, Cunxiang Wang, Yidong Wang, et al. 2024. A survey on evaluation of large language models. ACM Transactions on Intelligent Systems and Technology (TIST) 15, 3 (2024), 1- 45. [3] Jinhyuk Choi, Jihong Park, Seung- Woo Ko, Jinho Choi, Mehdi Bennis, and Seong- Lyun Kim. 2024. Semantics alignment via split learning for resilient multi- user semantic communication. IEEE Transactions on Vehicular Technology (TVT) (2024).

[4] Badhan Chandra Das, M Hadi Amini, and Yanzhao Wu. 2024. Security and privacy challenges of large language models: A survey. Comput. Surveys (2024).[5] Badhan Chandra Das, M Hadi Amini, and Yanzhao Wu. 2025. Security and privacy challenges of large language models: A survey. Comput. Surveys 57, 6 (2025), 1- 39. [6] Tobias Deufer, Max Hahnbuck, Tobias Uelwer, Cong Zhao, Christian Bauckhage, and Rafet Sifa. 2025. Resource- Efficient Anonymization of Textual Data via Knowledge Distillation from Large Language Models. In International Conference on Computational Linguistics Industry Track. 243- 250. [7] Minxin Du, Xiang Yue, Sherran SM Chow, Tianhao Wang, Chenyu Huang, and Huan Sun. 2023. Dp- forward: Fine- tuning and inference on language models with differential privacy in forward pass. In ACM SIGSAC Conference on Computer and Communications Security (CCS). 2665- 2679. [8] Sebastien Farquhar, Jannik Kasson, Janss Kubm, and Yarin- Gal. 2024. Detecting hallucinations in large language models using semantic entropy. Nature 630, 8017 (2024), 625- 630. [9] Wenjie Fu, Huandong Wang, Chen Gao, Guanghua Liu, Yong Li, and Tao Jiang. 2024. Membership Inference Attacks against Fine- tuned Large Language Models via Self- prompt Calibration. In Annual Conference on Neural Information Processing Systems (NeurIPS).[10] Filippo Galli, Luca Melis, and Tommaso Cucinotta. 2024. Noisy Neighbors: Efficient membership inference attacks against LLMs. arXiv:2406.16565 (2024).[11] Yuyou Gan, Yong Yang, Zhe Ma, Ping He, Rui Zeng, Yiming Wang, Qingming Li, Chunyi Zhou, Songze Li, Ting Wang, et al. 2024. Navigating the risks: A survey of security, privacy, and ethics threats in LLM- based agents. arXiv preprint arXiv:2411.09523 (2024).[12] Henry Gilbert, Michael Sandborn, Douglas C Schmidt, Jesse Spencer- Smith, and Jules White. 2023. Semantic compression with large language models. In Tenth International Conference on Social Networks Analysis, Management and Security (SNAMS). IEEE, 1- 8. [13] Kaijie Gong, Yi Gao, and Wei Dong. 2024. Privacy- preserving and cross- domain human sensing by federated domain adaptation with semantic knowledge correction. Proceedings of the ACM on Interactive, Mobile, Wearable and Ubiquitous Technologies 8, 1 (2024), 1- 26. [14] Feng He, Tianqing Zhu, Daying Ye, Bo Liu, Wanlei Zhou, and Philip S Yu. 2024. The emerged security and privacy of LLM agent: A survey with case studies. arXiv preprint arXiv:2407.1954 (2024).[15] Yu He, Boheng Li, Liu Liu, Zhongjie Ba, Wei Dong, Yiming Li, Zhan Qin, Kui Pei, and Lichuan Chen. 2025. Two- layer deep learning with deep learning inference attack against pre- trained large language models. In USENIX Security.[16] Hanjuan Huang, Hao- Jia Song, and Hsing- Kuo Pao. 2024. Large Language Model Pruning. arXiv:2406.00030 (2024).[17] Jing Huang, Diyi Yang, and Christopher Potts. 2024. Demystifying verbatim memorization in large language models. arXiv preprint arXiv:2407.17817 (2024).[18] Bo Hui, Haolin Yuan, Neil Gong, Philippe Burlina, and Yinzhi Cao. 2024. Pleak: Prompt leaking attacks against large language model applications. In ACM SIGSAC Conference on Computer and Communications Security (CCS). 3600- 3614. [19] Daphne Ippolito, Florian Traner, Milad Nasr, Chiyuan Zhang, Matthew Jagielski, Katherine Lee, Christopher A Choquette- Choo, and Nicholas Carlini. 2022. Preventing verbatim memorization in language models gives a false sense of privacy. arXiv preprint arXiv:2210.17546 (2022).[20] Kaan Kale, Homa Esfahanizadeh, Noel Elias, Oguzhan Baser, Muriel Médard, and Sriram Vishwanath. 2024. Textship: Information theoretic sentence embedding for language models. In IEEE International Symposium on Information Theory (ISIT). IEEE, 2038- 2043. [21] Mehdi Khamassi, Marceau Mahon, and Raja Chatila. 2024. Strong and weak alignment of large language models with human values. Scientific Reports 14, 1 (2024), 19399. [22] Eldar Kurtić, Elias Frantar, and Dan Alistar. 2023. Ziplm: Inference- aware structured pruning of language models. Advances in Neural Information Processing Systems NeurIPS 36 (2023), 65597- 65617. [23] Qinbin Li, Junyuan Hong, Chulin Xie, Jeffrey Tan, Rachel Xin, Junyi Hou, Xavier Yin, Zhun Wang, Dan Henrycks, Zhangyang Wang, et al. 2024. LLM- PBE: Assessing data privacy in large language models. arXiv:2408.12787 (2024).[24] Aishan Liu, Yuguang Zhou, Xianglong Liu, Tianyuan Zhang, Siyuan Liang, Jiaikai Wang, Yanjun Pu, Tianlin Li, Junqi Zhang, Wenbo Zhou, et al. 2025. Compromising LLM Driven Embodied Agents with Contextual Backdoor Attacks. IEEE Transactions on Information Informatics and Security (TIFS) (2025).[25] Peihua Mai, Ran Yan, Zhe Huang, Youjia Yang, and Yan Pang. 2023. Split- and- denoise: Protect large language model inference with local differential privacy. arXiv:2310.09130 (2023).[26] Abhijit Mishra, Mingda Li, and Soham Deo. 2024. Sentinelmls: Encrypted input adaptation and fine- tuning of language models for private and secure inference. In Proceedings of the AAAI Conference on Artificial Intelligence (AAAI).[27] Hamid Mozaffari and Virendra J Marathe. 2024. Semantic Membership Inference Attack against Large Language Models. arXiv preprint arXiv:2406.10218 (2024).[28] Zabir Al Nazi and Wei Peng. 2024. Large language models in healthcare and medical domain: A review. In Informatics, Vol. 11. MDPI, 57.

[29] Seth Neel and Peter Chang. 2023. Privacy issues in large language models: A survey. arXiv preprint arXiv:2312.06719 (2023).[30] Alexander Nikitin, Jannik Kossen, Yairin Gal, and Pekka Marttinen. 2024. Kernel language entropy: Fine- grained uncertainty quantification for llms from semantic similarities. Advances in Neural Information Processing Systems (NeurIPS) (2024).[31] Wenjie Qu, Yuguang Zhou, Yongji Wu, Tingsong Xiao, Binhang Yuan, Yiming Li, and Jiaheng Zhang. 2025. Prompt Inversion Attack against Collaborative Inference of Large Language Models. arXiv:2503.0922 (2025).[32] Xiaoong Shen, Yang Liu, Huiqi Liu, Jue Hong, Bao Duan, Zirui Huang, Yunlong Me, Ye Wu, and Di Wu. 2023. A split- and- privatize framework for large language model fine- tuning. arXiv:2312.15603 (2023).[33] Changtian Song, Dongdong Zhao, and Jianwen Xiang. 2024. Not All Tokens Are Equal: Membership Inference Attacks Against Fine- tuned Language Models. In 2024 Annual Computer Security Applications Conference (ACSAC). IEEE, 31- 45. [34] Meng Tong, Kejiang Chen, Jie Zhang, Yuanqi Qi, Weiming Zhang, Nenghai Yu, Tianwei Zhang, and Zhiikun Zhang. 2025. InforDPT: Privacy- preserving Inference for Black- box Large Language Models. IEEE Transactions on Dependable and Secure Computing (TDSC) (2025).[35] Antonios Tragoudaras, Theofanis Aslanidis, Emmanouil Georgios Lionis, Marina Oezco González, and Panagiotis Eustadiadis. 2025. Information Leakage of Sentence Embeddings via Generative Embedding Inversion Attacks. arXiv preprint arXiv:2504.16609 (2025).[36] Mehmet Ugurbil, Dimitris Mouris, Manuel B Santos, José Cabrero- Holgueras, Miguel de Vega, and Shubho Sengupta. 2025. Fission: Distributed Privacy- Preserving Large Language Model Information. Cryptology ePrint Archive.[37] Benyou Wang, Qianqian Xie, Jiahuan Pei, Zhongchen, Prayag Tiwari, Zhao Li, and Jie Fu. 2023. Pre- trained language models in biomedical domain: A systematic survey. Comput. Surveys 56, 3 (2023), 1- 52. [38] Teng Wang, Lindong Zhai, Tengfei Yang, Zhuicheng Luo, and Shuanggen Liu. 2024. Selective privacy- preserving framework for large language models fine- tuning. Information Sciences 678 (2024), 121000. [39] Yuece Wei, Xingcheng Fu, Dongqi Yan, Qingyun Sun, Hao Peng, Jia Wu, Jinyan Wang, and Xianxian Li. 2023. Heterogeneous graph neural network with semantic- aware differential privacy guarantees. Knowledge and Information Systems 65, 10 (2023), 4085- 4110. [40] Rui Wen, Zheng Li, Michael Backes, and Yang Zhang. 2024. Membership inference attacks against in- context learning. In ACM SIGSAC Conference on Computer and Communications Security (CCS). 3481- 3495. [41] Xiuwen Wang, Xijian Li, Xiefei Yan, Xiefei Dong, Xue Zhang, Zhenxuan Ren, and Xiaohen Cheng. 2024. On protecting the data privacy of large language models (LLM): A survey. arXiv preprint arXiv:2403.05156 (2024).[42] Haomiao Yang, Kunlanxiang, Mengyu Ge, Hongwei Li, Rongxing Lu, and Shui Yu. 2024. A comprehensive overview of backdoor attacks in large language models within communication networks. IEEE Network (2024).[43] Dixi Yao and Baochun Li. 2024. Is Split Learning Privacy- Preserving for Fine- Tuning Large Language Models? IEEE Transactions on Big Data (TBD) (2024).[44] Yanshun Yao, Xiaojun Xu, and Yang Liu. 2024. Large language model unlearning. Advances in Neural Information Processing Systems (NeurIPS) (2024).[45] Junwei Yu, Jieyu Zhou, Yepeng Ding, Lingfeng Zhang, Yuheng Guo, and Hiroyuki Sato. 2024. Textual Differential Privacy for Context- Aware Reasoning with Large Language Model. In IEEE Annual Computers, Software, and Applications Conference (COMPSAC). IEEE, 988- 997. [46] Wang Yu, Liang Lin, Guibin Zhang, Xinfeng Li, Junfeng Fang, Ningyu Zhang, Kun Miao, and Yang Wang. 2025. UniErase: Unlearning Token as a Universal Erasure Primitive for Language Models. arXiv:2505.15674 (2025).[47] Collin Zhang, John X Morris, and Vitaly Shmatikov. 2024. Extracting prompts by reverting llm outputs. arXiv:2405.1502 (2024).[48] Rui Zhang, Hongwei Li, Rui Wen, Wenbo Jiang, Yun Zhang, Michael Backes, Yun Shen, and Yang Zhang. 2024. Instruction backdoor attacks against customized LLMs. In USENIX Security Symposium. 1849- 1866. [49] Xiaomei Zhang, Zhaoxi Zhang, Yanjun Zhang, Xufei Zheng, Leo Yu Zhang, Shengshan Hu, and Shirui Pan. 2025. Exploring Gradient- Guided Masked Language Model to Detect Textual Adversarial Attacks. arXiv:2504.08798 (2025).[50] Xinyao Zheng, Husheng Han, Shangyi Shi, Qiyan Fang, Zidong Du, Xing Hu, and Qi Guo. 2024. InputSnatch: Stealing Input in LLM Services via Timing Side- Channel Attacks. arXiv:2411.18191 (2024).