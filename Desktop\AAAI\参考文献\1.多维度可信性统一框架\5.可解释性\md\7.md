# Prediction of Freezing of Gait in Parkinson's Disease using Explainable AI & Federated Deep Learning for Wearable Sensors

Biplov Paneru<sup>1</sup>,

<sup>1</sup>Department of Electronics and Communication, Pokhara University, Nepal

Abstract— This study leverages an Inertial Measurement Unit (IMU) dataset to develop explainable AI methods for the early detection and prediction of Freezing of Gait (FOG), a common symptom in Parkinson's disease. Machine learning models, including CatBoost, XGBoost, and Extra Trees classifiers, are employed to accurately categorize FOG episodes based on relevant clinical features. A Stacking Ensemble model achieves superior performance, surpassing a hybrid bidirectional GRU model and reaching nearly  $99\%$  classification accuracy. SHAP interpretability analysis reveals that time (seconds) is the most influential factor in distinguishing gait patterns. Additionally, the proposed FOG prediction framework incorporates federated learning, where models are trained locally on individual devices and aggregated on a central server using a federated averaging approach, utilizing a hybrid Conv1D + LSTM architecture for enhanced predictive capability.

Keywords—Freezing of Gait (FoG), IMU sensor, Federated Learning, Bi- GRU attention, Stacking Ensemble

# I. INTRODUCTION

Globally, Parkinson's disease (PD) / Freezing of Gaits is a major health concern that requires early detection for efficient management and intervention. Global crises including public health, climate change, and socioeconomic difficulties are being faced by the world today. In this regard, neurodegenerative illnesses like Parkinson's disease pose a substantial research challenge with broad ramifications. Globally, the prevalence of neurological disorders is rising, necessitating immediate attention and creative solutions. Parkinson's disease and other disorders are becoming more common as the population ages, creating difficult problems for healthcare systems and society at large. Multidisciplinary research initiatives that cross conventional boundaries and tackle complex problems are essential to comprehending and alleviating these crises. Healthcare costs are rapidly rising due to an aging population and a growing global population. Healthcare systems are undergoing a transition thanks to technologies like the Internet of Things, Edge of Things, and Cloud of Things, which enable health monitoring of individuals without the need for hospitalization [6]. In mid- and late- stage Parkinson's disease, freezing of gait (FOG), a severe gait abnormality that impairs movement and raises the risk of falls, is widespread. With the ultimate goal of preventing freezes or lessening their impact through gait monitoring and assistive technology, wearable sensors have been utilized to detect and forecast FOG [7]. Globally, Parkinson's disease (PD) is the second most common type of dementia. In recent years, wearable technology has proven helpful in both long- term surveillance and computer- aided diagnosis of Parkinson's disease. The key challenge is still how to accurately and efficiently use wearable technology to gauge the severity of Parkinson's disease [9]. Climate change and eating habits are just two of the numerous issues that healthy humans must deal with. To survive, the outcome needs to be conscious of the health condition.

Health support services encounter issues such as overdiagnosis, data hazards, preventive errors, inaccurate patient information, and delayed implementation [10].

![](images/33f9a69d3ee26ca4bb819e8f1ea21b8a14ce4b216ce32fef2fd5cd8a71e9242d.jpg)  
Figure 1: Wearable sensor technology

This study looks into the usefulness of machine learning (ML) algorithms in the classification of Parkinson's disease (PD) in response to this necessity. The research assesses the effectiveness of several algorithms, machine learning and deep learning method along with federated approach based on pertinent clinical data from frontiers publication [17]. This research not only addresses the urgent need for practical applications of ML in the field of Parkinson's disease diagnosis, but also advances predictive accuracy through the integration of traditional and deep learning algorithms.

# II. LITERATURE REVIEW

The application of machine learning (ML) techniques for the diagnosis and progression monitoring of Parkinson's Disease (PD) has received significant attention in recent years. Various studies have integrated supervised learning, deep learning, wearable technology, and signal processing to enhance diagnostic accuracy and real- time monitoring.

# 1. Supervised Machine Learning in PD Diagnosis

Alshammri et al. (2023) implemented a hybrid approach using GridSearchCV for hyperparameter tuning and SMOTE for handling class imbalance, achieving  $99\%$  F1- score with MLP and  $97\%$  with SVM, showing promise for early PD diagnosis through structured ML workflows [1]. Similarly, Nithya et al. (2021) applied a hybrid of SVM and Random Forest on MRI images aligned using image registration, highlighting enhanced specificity and diagnostic accuracy [3].

# 2. Ensemble and Deep Learning Approaches

Ezhilin Freeda et al. (2022) explored early prediction using speech- based biomarkers and ensemble classifiers such as XGBoost and Random Forest, which outperformed traditional decision trees [4]. Schmid et al. (2022) proposed a combination of Rotation Forest and Random Forest on voice data from the UCI dataset to classify severity levels [5].

Chaterjee et al. (2023) introduced the PDD- ET model, which significantly outperformed other models, including CNNs, LSTMs, GRUs, and DNNs across multiple metrics by margins ranging from  $16\%$  to  $45\%$ , illustrating the robustness of their ensemble approach [2].

# 3. Sensor-Based Monitoring and Deep Learning

Uddin et al. (2021) designed a sensor- based RNN system for activity prediction using wearable medical sensors like ECG, accelerometers, and gyroscopes. The model, deployed on edge devices, outperformed traditional methods in speed and accuracy [6]. Likewise, Mishra et al. (2022) used wearable sensors to collect data from 39 Friedreich's Ataxia (FRDA) patients and developed ML models using sensor- derived, biological, and demographic features to predict disease severity [8].

Yue et al. (2024) proposed a Latent Dirichlet Allocation (LDA)- based architecture to capture activity- derived latent features, achieving a classification accuracy of  $73.48\%$  across PD severity levels in free- living settings [9].

# 4. FOG Detection and Gait Analysis

Freezing of Gait (FOG) remains a significant challenge in PD management. Pardoel et al. (2019) reviewed 74 studies, finding that modern ML models with transfer learning and semi- supervised learning offer improved person- specific FOG detection, despite challenges like limited datasets [7].

Deep learning- based approaches such as DeepFoG by Bikias et al. (2021) used IMU sensor data and achieved up to  $90\%$  specificity in FOG detection via LOSO and 10- fold cross- validation [12]. Similarly, Borzi et al. (2022) implemented a multi- head CNN on inertial sensor data from 118 PD patients, predicting FOG onset 3.1 seconds earlier with high specificity ( $>88\%$ ) [13].

Salomon et al. (2022) conducted a global ML competition involving 1,379 teams to develop wearable- based FOG detection models. The winning solutions showed strong correlations to gold- standard datasets and uncovered previously unnoticed real- world FOG patterns [14].

Shi et al. (2023) applied Continuous Wavelet Transform and Bayesian optimization to train CNNs on IMU signals, achieving an impressive  $91.5\%$  F1 score in subject- independent FOG detection [15].

# 5. IoT and Intervention-Based Systems

Pradhan et al. (2021) integrated IoT- based data collection with a Boltzmann- trained AI feedback system, achieving a  $97.4\%$  prediction rate for early disease detection [10]. Furthermore, a recent study by Kim et al. (2024) introduced soft robotic garments to prevent FOG by enhancing hip flexion. This led to measurable improvements in walking distance ( $+55\%$ ) and gait quality in a six- month case study [16].

The review of the literature identifies a number of interesting strategies and methods in the field of machine learning- based Parkinson's disease prediction. It also highlights important research gaps that need to be looked into further. This proposed method applies the machine learning models on a new dataset from frontiers publication and utilizes explainable AI with

Federated Learning on a custom Conv 1D + LSTM model (hybridized CNN- LSTM) for purpose of finding a global federated model with help of three users data (subjects originally in publication).

# III. METHODOLOGY

The study's dataset [17] includes the IMU data collected through various subjects that are important for diagnosing Gait disease. To improve the quality of the input for the ML models, preprocessing steps include feature selection, and data cleaning. The 3 users data were combined to trained federated model with extra column added to table 1 having values represented as user 1, user 2 and user 3. The data from 3 subjects [17] were combined and downsampled to make data classes balance as much as possible. For training ML models on ML basic approach the data for a subject A was combined and data was nearly balanced and didn't required further sampling techniques. The performance of the model is then assessed by dividing the dataset into training and testing sets. Using cross- validation, the selected machine learning algorithms are put into practice and their hyperparameters are optimized. To capture intricate patterns, a deep neural network hybrid architecture is created and trained on a dataset. The example dataset is tabulated in table 1.

Table 1:Dataset example  

<table><tr><td>Time [s]</td><td>ACC 
ML [g]</td><td>ACC AP 
[g]</td><td>ACC SI 
[g]</td><td>GYR 
ML 
[deg/s]</td><td>GYR AP 
[deg/s]</td><td>GYR SI 
[deg/s]</td><td>Freezing event [flag]</td></tr><tr><td>1.359375</td><td>0.105983</td><td>-0.32385</td><td>0.838768</td><td>8.897047</td><td>-16.8301</td><td>33.93851</td><td>0</td></tr><tr><td>1.367188</td><td>0.142105</td><td>-0.18689</td><td>0.883557</td><td>9.206729</td><td>-19.46</td><td>34.4718</td><td>0</td></tr><tr><td>1.375</td><td>0.101335</td><td>-0.18438</td><td>0.924018</td><td>5.610897</td><td>-21.3965</td><td>29.59217</td><td>0</td></tr><tr><td>1.382813</td><td>0.050564</td><td>-0.16351</td><td>0.947961</td><td>0.715407</td><td>-22.4346</td><td>20.82936</td><td>1</td></tr><tr><td>1.390625</td><td>-0.07304</td><td>-0.21369</td><td>0.879053</td><td>-1.75361</td><td>-22.0239</td><td>17.14693</td><td>1</td></tr><tr><td>1.398438</td><td>-0.04873</td><td>-0.2424</td><td>0.838982</td><td>-5.22388</td><td>-25.6008</td><td>-6.74861</td><td>1</td></tr><tr><td>1.40625</td><td>0.047031</td><td>-0.37857</td><td>0.847576</td><td>-9.90098</td><td>-27.0137</td><td>-21.2456</td><td>1</td></tr></table>

![](images/fb3195421654b6acd90b69d7da9861e004bdffbc40f5a05d21bdf02c49af7ad1.jpg)  
Figure 1: Proposed federated learning approach

Federated learning (FL) for Freezing of Gait (FOG) prediction using Inertial Measurement Unit (IMU) data is a privacy- preserving, decentralized machine learning approach that enables multiple hospitals, clinics, or individual patients to collaboratively train a shared global model without sharing their raw sensor data. In this setup, each user (e.g., Parkinson's patient wearing an IMU- equipped wearable) collects local gait data such as acceleration, orientation, and gyroscope signals during their daily activities. Instead of sending this sensitive IMU data to a central server, each device trains the FOG prediction model locally. These local models learn to distinguish between normal walking patterns and FOG episodes based on the unique mobility profile of each user.

After training locally for a number of epochs, each device sends only the model updates—such as gradients or weight changes—to a central server. The server performs federated averaging, which aggregates these updates to create a new global model that reflects learning from all participants. This process is repeated over several communication rounds. The advantage of this approach is twofold: (1) it preserves the privacy of the users, since no raw IMU data ever leaves their devices, and (2) it creates a robust, generalized FOG prediction model that benefits from the variability in real- world patient data across different users and conditions.

i. User-level Isolation: The IMU dataset contains a user_id column. Data is grouped and processed per user, ensuring each local model is trained only on its owner's data — mimicking edge-device learning (e.g., wearables).

ii. Local Model Training: For each federated round, the script calls train_user_model() per user. This trains an LSTM-based model locally on each user's gait data without sharing raw data externally — capturing the FL essence.iii. Federated Averaging (FedAvg): After each local model is trained, their weights are collected and averaged proportionally (based on the user's sample size) using federated_average_weights(). This implements the central aggregation step of federated learning.iv. Global Model Update: The global model is updated with the averaged weights and evaluated on a held-out test set, representing how well the federated model generalizes across users.v. Communication Rounds: This cycle repeats for multiple communication rounds, steadily improving the global model by learning collaboratively from all users.

# A. Model selection

Along with various ML algorithms we utilized different deep learning algorithms too. Two state- of- the- art machine learning models, Long Short- Term Memory (LSTM) and MultiLayer Perceptron (MLP), were deliberately selected in order to achieve robust and accurate Parkinson's disease classification. Recurrent neural networks (RNNs) of the LSTM type are particularly good at identifying temporal dependencies in sequential data, which makes them a good choice for examining time- series elements in the clinical dataset. However, because MLPs are feedforward neural networks, they can handle a wide range of complex non- linear relationships in datasets with lots of features. The choice to combine both XGBoost and Catboost is based on the necessity of utilizing each class's advantages in managing complex patterns and temporal dynamics in clinical features.

# B. Dataset selection

The dataset collected from the Fronier's publication source consists up of the IMU sensor data regarding Freezing of Gait 2 conditions that are Gait, and No- Gait. The use of ML model in the micromlgen python library especially needs a lightweight model development. So, we are dependent to a balanced volume dataset for developing 3 different models for purpose of Gait Prediction using IMU sensors data. The dataset combined through excel worksheet from SUB 01_1, and SUB 2_05 [17]. The dataset volume content can be seen in figure 5.

![](images/c3f2a617d74001acc4d492a2e7196e0a83d9cb6e1753e24ba7abfab0f9ac9634.jpg)  
Figure 2: Data classes division

# C. Model's proposed

i. Extra Trees Classifier:

The Extra Trees (Extremely Randomized Trees) Classifier is an ensemble learning method that builds multiple decision trees during training. Unlike Random Forests, which use bootstrapped samples and search for optimal split points, Extra Trees chooses split thresholds at random for features. This randomness reduces variance and training time, making the model fast and robust to overfitting.

# ii. Bi-GRU with attention:

The model combines bidirectional gated recurrent units (Bi- GRU) with an attention mechanism to capture both temporal dependencies and feature importance across time steps in sequential data. The Bi- GRU processes the sequence in both forward and backward directions, enabling the model to understand context from both past and future states. The attention layer assigns weights to different time steps, that allow the model to target more on the most relevant parts of the input during the making of predictions. This architecture is particularly effective in time- series classification tasks such as human activity recognition or gait analysis, where temporal context is crucial.

iii. Catboost: CatBoost is a gradient boosting algorithm created by Yandex with the goal of effectively handling category data. Without the requirement for explicit encoding, like one-hot encoding, it automatically processes categorical features, which can speed up preprocessing and enhance model performance. High accuracy, speed, and the ability to avoid overfitting using a variety of regularization strategies are all attributes of CatBoost. Regression and classification tasks frequently use it, and it works well with datasets that contain categorical variables and intricate relationships.

iv. Stacking (Two Level Ensemble)

To implement this, a stacking ensemble model is used to enhance classification performance by combining multiple diverse base learners. Specifically, four lightweight base classifiers—Random Forest, Extra Trees, XGBoost, and CatBoost—act as level- 1 models. These models each learn different patterns in the data and generate predictions. Instead of using these predictions directly, a logistic regression model serves as the meta- learner (level- 2 model), which is trained on the outputs (probabilities or class predictions) of the base learners. This meta- model then, learns how to best combine the individual base model predictions, exploiting their strengths while compensating for their weaknesses. A 10- fold cross- validation strategy is applied internally during stacking to reduce overfitting and ensure robust performance. Overall, this architecture provides a more generalized and accurate model compared to any single classifier alone by leveraging model diversity and layered learning.

v. Nested-cross validation: Two levels of cross-validation are used in nested cross-validation, a model evaluation method that reduces overfitting and yields a more accurate estimate of model performance. The dataset is divided into training and testing subsets by the outer loop, and the test set is used to assess the model. The inner loop optimizes the model's hyperparameters by cross-validating the training data of each outer fold.

![](images/81e3ecea11ff958b1d81676cadafc582d87bea3d2e145b756022b35e18b01c6c.jpg)  
Fig 3: Proposed methodology

# Evaluation metrics for model

The accuracy, precision, recall and F1- score related metrics are given in equation 1, 2, 3 and equation 4 [18].

i. Accuracy: The ratio of correctly predicted instances to the total instances. Accuracy  $= \frac{TP + TN}{(FP + FN + TP + TP)}$  (1)

ii. Precision: The ratio of correctly predicted positive instances to all predicted positives.

$$
\begin{array}{r}\mathrm{Precision} = \frac{TP}{(FP + TP)} \end{array} \tag{2}
$$

iii. Recall: The ratio of correctly predicted positive instances to all actual positives.

$$
\begin{array}{r}\mathrm{Recall} = \frac{TP}{(FN + TP)} \end{array}
$$

iv. F1- Score: The harmonic mean of precision and recall, which provides balance for both metrics.

F1 Score=2 x Precision+Recall Precision+Recall

v. Federated Averaging is the process by which:

In this approach, each client (e.g., mobile device or remote node) trains a local model using its own data. If  $w_{k}$  is the model weights from client  $\mathbf{k}$  trained on  $n_k$  data samples, then the global model update is:

$$
\begin{array}{r}w_{t + 1} = \sum_{k = 1}^{k}\frac{n_k}{n} w_k \end{array} \tag{5}
$$

Where:

$w_{t + 1}$  is the new global model.  $n_k$  is the number of samples on client  $\mathbf{k}$  k is the total number of participating clients.

# RESULTS & DICUSSIONS

The resulting accuracies in the evaluation of machine learning models for the classification of Parkinson's disease using clinical data highlight intriguing subtleties between various algorithms. The testing accuracy on a Stacking Ensemble surpassed other models.

Table.2.Accuracies results  

<table><tr><td>Model</td><td>Training</td><td>Testing</td></tr><tr><td>Random Forest</td><td>96.71%</td><td>96.70%</td></tr><tr><td>Catboost</td><td>100%</td><td>96.24%</td></tr><tr><td>Extra Trees</td><td>97%</td><td>97%</td></tr><tr><td>B-GRU with attention</td><td>98%</td><td>97%</td></tr><tr><td>Proposed Stacking Ensemble</td><td>99.77%</td><td>99.63%</td></tr></table>

The proposed Stacking Ensemble model can be seen to outperform other models with  $99.77\%$  training accuracy and  $99.63\%$  testing accuracy. The confusion matrix plot as shown in figure 12. Shows 1320 as correctly predicted as class '0' mean 'No- Gait' and '1' is predicted 280 instances correctly out of 282 instances indicating Gait.

![](images/8c838ecb55ea0af358100dc9254bc762ad6c77eb93aaeb72d7f3bb99a6c40575.jpg)  
Fig 4: Confusion matrix plot for stack model

The model is further nested cross validated for performance measure. These metrics are helpful for evaluating these model performances and their usability with real- time sensors integrating for the purpose of detecting and diagnosing Freezing of Gait early. The nested cross validation for stacking ensemble shows  $96.42\%$  mean accuracy and low standard deviation of 0.0130 indicating how well model is performing on entire 10 folds on rigorous cross nested validation phenomenon. The table 3 shows each fold accuracies for that model.

Table 3: nested Cross validation result  

<table><tr><td>Fold</td><td>Accuracy</td></tr><tr><td>Fold 1</td><td>0.9689</td></tr><tr><td>Fold 2</td><td>0.9626</td></tr><tr><td>Fold 3</td><td>0.9577</td></tr><tr><td>Fold 4</td><td>0.9527</td></tr><tr><td>Fold 5</td><td>0.9689</td></tr><tr><td>Fold 6</td><td>0.9801</td></tr><tr><td>Fold 7</td><td>0.9663</td></tr><tr><td>Fold 8</td><td>0.9464</td></tr><tr><td>Fold 9</td><td>0.9900</td></tr><tr><td>Fold 10</td><td>0.9489</td></tr><tr><td>Mean</td><td>0.9642</td></tr><tr><td>Std</td><td>0.0130</td></tr></table>

![](images/67c71bd64f2c6ddf19dd55b66af575a517949113e384fb447e1739aa0a896933.jpg)  
Figure 5 (a): SHAP analysis bar plot

![](images/ebad237dcd01024cc316ad05c25bca8e045895e1480ebbffc6dd3380e6b7a679.jpg)  
Fig 5 (b): SHAP impact plot

The SHAP summary plot in figure 5(a) and 5(b) visualizes the impact of various features from Inertial Measurement Unit (IMU) data on a machine learning model's prediction of Freezing of Gait (FOG) events. This SHAP plot is a summary plot showing the impact of the top features on a model's output. Each point represents a single sample's SHAP value for a specific feature. The x- axis (SHAP value) indicates how much that feature pushes the prediction higher (positive values) or lower (negative values). The features are listed on the y- axis, sorted by their overall importance. The color of the points corresponds to the feature's value for each sample, where blue indicates low values and red indicates high values. In the plot, the feature "Time_s_" has the largest impact, with higher values pushing the model output strongly positive (to the right). Other features like "ACC SI_g_" and "ACC ML_g_" also influence the model but less strongly, with mixed positive and negative effects depending on their values. Features such as "GYR ML_deg/s_" "GYR SI_deg/s_" and others have smaller impacts.

Table 4: Performance comparison for models:  

<table><tr><td>Model</td><td>Class</td><td>Precision</td><td>Recall</td><td>F1-Score</td><td>Support</td></tr><tr><td>Bi-GRU + Attention</td><td>0</td><td>0.96</td><td>1.00</td><td>0.98</td><td>1324</td></tr><tr><td></td><td>1</td><td>1.00</td><td>0.81</td><td>0.90</td><td>282</td></tr><tr><td>Extra Trees</td><td>0</td><td>0.97</td><td>1.00</td><td>0.98</td><td>1324</td></tr><tr><td></td><td>1</td><td>1.00</td><td>0.84</td><td>0.91</td><td>282</td></tr><tr><td>CatBoost</td><td>0</td><td>0.96</td><td>1.00</td><td>0.98</td><td>1324</td></tr><tr><td></td><td>1</td><td>1.00</td><td>0.79</td><td>0.88</td><td>282</td></tr><tr><td>Random Forest</td><td>0</td><td>0.96</td><td>1.00</td><td>0.98</td><td>1324</td></tr><tr><td></td><td>1</td><td>1.00</td><td>0.81</td><td>0.90</td><td>282</td></tr></table>

With a total accuracy of  $99\%$ , Stacking Ensemble (SEC) also outperformed XGBoost and Extra Trees, which both attained approximately  $98\%$  accuracy. The model hyperparameters are tabulated in table 4, 5, 6, 7.

Table 5: Hyperparameters for model  

<table><tr><td>Model</td><td>Accuracy</td></tr><tr><td>Random Forest</td><td>&#x27;max_depth&#x27;: 2, &#x27;min_samples_split&#x27;: 2, &#x27;n_estimators&#x27;: 50</td></tr><tr><td>Catboost</td><td>&#x27;depth&#x27;: 3, &#x27;iterations&#x27;: 50, &#x27;12_leaf_reg&#x27;: 1, &#x27;learning_rate&#x27;: 0.01</td></tr></table>

<table><tr><td>Extra Trees</td><td>criterion: &#x27;entropy&#x27;, &#x27;min_samples_leaf&#x27;: 4, &#x27;n_estimators&#x27;: 10</td><td>&#x27;max_depth&#x27;: None, &#x27;min_samples_split&#x27;: 2,</td></tr></table>

Table 6: Bi-GRU with attention model hyperparameters  

<table><tr><td>Layer / Component</td><td>Hyperparameter</td><td>Value</td></tr><tr><td>Input</td><td>Input Shape</td><td>(1, n_features)</td></tr><tr><td>GRU Layer</td><td>Units</td><td>128</td></tr><tr><td></td><td>Return Sequences</td><td>False</td></tr><tr><td></td><td>Regularization (12)</td><td>0.01</td></tr><tr><td>Dropout (after GRU)</td><td>Dropout Rate</td><td>0.3</td></tr><tr><td>Dense Layer 1 (MLP)</td><td>Units</td><td>64</td></tr><tr><td></td><td>Activation</td><td>&#x27;relu&#x27;</td></tr><tr><td></td><td>Regularization (12)</td><td>0.01</td></tr><tr><td>Dropout (after MLP1)</td><td>Dropout Rate</td><td>0.2</td></tr><tr><td>Dense Layer 2 (MLP)</td><td>Units</td><td>32</td></tr><tr><td></td><td>Activation</td><td>&#x27;relu&#x27;</td></tr><tr><td></td><td>Regularization (12)</td><td>0.01</td></tr><tr><td>Output Layer</td><td>Units</td><td>1 (binary classification)</td></tr><tr><td></td><td>Activation</td><td>&#x27;sigmoid&#x27;</td></tr></table>

Table 7: Proposed Stacking Ensebmle approach  

<table><tr><td>Model</td><td>Hyperparameter</td><td>Value</td><td>Description</td></tr><tr><td>RandomForestClassifier</td><td>n_estimators</td><td>10</td><td>Number of trees</td></tr><tr><td></td><td>max_depth</td><td>3</td><td>Maximum tree depth</td></tr><tr><td></td><td>random_state</td><td>42</td><td>Random seed for reproducibility</td></tr><tr><td>ExtraTreesClassifier</td><td>n_estimators</td><td>10</td><td>Number of trees</td></tr><tr><td></td><td>max_depth</td><td>3</td><td>Maximum tree depth</td></tr><tr><td></td><td>random_state</td><td>42</td><td>Random seed for reproducibility</td></tr><tr><td>XGBClassifier</td><td>use_label_encoder</td><td>False</td><td>Disable label encoding</td></tr><tr><td></td><td>eval_metric</td><td>&#x27;logloss&#x27;</td><td>Evaluation metric</td></tr><tr><td></td><td>n_estimators</td><td>10</td><td>Number of boosting rounds</td></tr><tr><td></td><td>max_depth</td><td>3</td><td>Maximum tree depth</td></tr><tr><td></td><td>random_state</td><td>42</td><td>Random seed for reproducibility</td></tr><tr><td>CatBoostClassifier</td><td>iterations</td><td>10</td><td>Number of boosting iterations</td></tr><tr><td></td><td>depth</td><td>3</td><td>Maximum tree depth</td></tr><tr><td></td><td>verbose</td><td>0</td><td>Silent training output</td></tr><tr><td></td><td>random_state</td><td>42</td><td>Random seed for reproducibility</td></tr></table>

<table><tr><td>LogisticRegression</td><td>default</td><td>default</td><td>Meta-model parameters not set</td></tr><tr><td>StackingClassifier</td><td>estimators</td><td>List of above models</td><td>Base estimators</td></tr><tr><td></td><td>final_estimator</td><td>LogisticRegression</td><td>Meta-model</td></tr><tr><td></td><td>cv</td><td>10</td><td>Internal cross-validation folds</td></tr><tr><td></td><td>n_jobs</td><td>-1</td><td>Use all CPU cores</td></tr><tr><td></td><td>passthrough</td><td>False</td><td>Use base model predictions only</td></tr><tr><td></td><td>verbose</td><td>2</td><td>Verbosity level</td></tr><tr><td></td><td></td><td></td><td></td></tr></table>

# Federated Learning Result

With Federated learning- based model development the model performance is calculated user 1 user 2, and user 3. Their performance are source for final global model. Table 8- 12 are tabulated that shows the performance as well as metrics used, and the models configurations.

![](images/fa461b4ce7dba2c621c74a614862ad4cfb2325f664c539509a3c21e192c5f61f.jpg)  
Fig 6. Federated learning model graphs

Table 8:Global Model Performance  

<table><tr><td>Metric</td><td>Value</td></tr><tr><td>Accuracy</td><td>0.6917</td></tr><tr><td>Precision</td><td>0.6761</td></tr><tr><td>Recall</td><td>0.7351</td></tr><tr><td>F1 Score</td><td>0.7044</td></tr><tr><td>AUC Score</td><td>0.7446</td></tr></table>

Table 9: Model Configuration  

<table><tr><td>Parameter</td><td>Value</td></tr><tr><td>Units</td><td>64</td></tr></table>

<table><tr><td>Dropout Rate</td><td>0.3</td></tr><tr><td>Learning Rate</td><td>0.001</td></tr><tr><td>Batch Size</td><td>32</td></tr></table>

<table><tr><td>Parameter</td><td>Value</td></tr><tr><td>Communication Rounds</td><td>10</td></tr><tr><td>Participating Users</td><td>3</td></tr><tr><td>Min Samples per User</td><td>20</td></tr></table>

Table 11:User Performance Summary  

<table><tr><td>User</td><td>Accuracy</td><td>Precision</td><td>Recall</td><td>F1 Score</td><td>AUC</td><td>Epochs</td><td>Samples</td></tr><tr><td>1</td><td>0.932</td><td>0.949</td><td>0.913</td><td>0.930</td><td>0.975</td><td>32</td><td>1830</td></tr><tr><td>2</td><td>0.917</td><td>0.965</td><td>0.866</td><td>0.913</td><td>0.978</td><td>17</td><td>2542</td></tr><tr><td>3</td><td>0.871</td><td>0.811</td><td>0.968</td><td>0.883</td><td>0.943</td><td>36</td><td>3145</td></tr></table>

Table 10: Federated Setup  
Table 12: User wise comparison  

<table><tr><td>Metric</td><td>Value</td></tr><tr><td>Mean Accuracy</td><td>0.907</td></tr><tr><td>Std Dev Accuracy</td><td>0.026</td></tr><tr><td>Mean F1 Score</td><td>0.909</td></tr><tr><td>Std Dev F1 Score</td><td>0.020</td></tr><tr><td>Avg Epochs</td><td>28.3</td></tr></table>

Table 13: Detailed classification report  

<table><tr><td>Class</td><td>Precision</td><td>Recall</td><td>F1-Score</td><td>Support</td></tr><tr><td>0</td><td>0.71</td><td>0.65</td><td>0.68</td><td>941</td></tr><tr><td>1</td><td>0.68</td><td>0.74</td><td>0.70</td><td>940</td></tr><tr><td>Accuracy</td><td></td><td></td><td>0.69</td><td>1881</td></tr><tr><td>Macro Avg</td><td>0.69</td><td>0.69</td><td>0.69</td><td>1881</td></tr><tr><td>Weighted Avg</td><td>0.69</td><td>0.69</td><td>0.69</td><td>1881</td></tr></table>

![](images/733391165a9723dfc0773e6a7b27bf2dc1da4d53b544bc3a43032d7bd7f6071e.jpg)  
Fig 7: Model architecture

The results from the federated learning model show moderate to high performance, indicating that the architecture is well- suited for time series classification tasks, such as detecting Freezing of Gait (FOG) in Parkinson's patients using IMU sensor data. The models trainings graph is depicted in figure 6 and the architecture is given in figure 7. The global test accuracy is  $69.17\%$ , with an F1- score of  $70.44\%$ , suggesting that the model can reasonably balance between precision and recall. The AUC score of 0.7446 reflects a fair ability to distinguish between FOG and non- FOG events, though there is still room for improvement. The downsampling was effective to reduce users' wise data imbalances. While this performance is not perfect, it is significant considering the decentralized, privacy- preserving federated learning approach, where data is never centralized and training happens locally on user devices.

The per- user performance is notably higher, especially for Users 1 and 2, who achieved F1- scores of 0.930 and 0.913 respectively, and AUC scores nearing 0.98. This suggests that the model learns individual movement patterns well and adapts effectively within users. However, User 3, while still performing well (F1: 0.883, AUC: 0.943), had slightly lower precision, indicating a few more false positives. The variability across users could be due to differences in sensor placement, movement patterns, or data quantity. On average, the users trained for 28.3 epochs, showing stable convergence without overfitting.

This federated learning system ensures data privacy by keeping user data decentralized and training models locally on each user's device. It addresses class imbalance by balancing data per user, ensuring unbiased local learning. After local training, the system performs federated averaging, where model weights are aggregated based on each user's data size to form a stronger global model. This process is repeated over multiple communication rounds,

progressively improving performance. Comprehensive evaluation metrics and visualizations like accuracy trends, user performance distributions, and confusion matrices provide deep insight into the model's behavior and effectiveness.

# Limitations and Future works

While there has been a greater advantage by use of machine learning for gait prediction, yet there is doubt on integrating with sensors for real- time prediction as the sensor data must align to the model predictability. The data received from sensors varied with time, and so it was difficult to correctly predict and identify gait disease. In a real- world healthcare setting, this federated learning approach is highly applicable for remote monitoring of Parkinson's patients using wearable IMU sensors (e.g., accelerometers, gyroscopes). It allows the model to be trained across different patient devices without transmitting sensitive health data to a central server—ensuring patient privacy and data sovereignty. Clinically, in future application of model developed such can be utilized to trigger alerts when FOG is detected, enabling timely intervention (e.g., cueing systems or caregiver notification), and could be integrated into Internet of Medical Things (IoMT) platforms for continuous, at- home monitoring. While the global performance needs optimization for deployment, the strong per- user metrics indicate that personalized FOG detection systems trained federatively are both feasible and promising for Parkinson's disease management.

The result shows a great potential of aiding the victim in the case of emergencies related to the occurrence of freezing of gait by deploying model on Internet of Medical Things (IoMT) devise, or wearable sensors. With the trained model and IoMT platforms integration can enhance the possibility of AI- powered ecosystem development as well as live monitoring, providing aid on time to the victim. The use of AI for biomedical prototyping and predictions and early detections [12, 13] can have significant enhancements in reducing health and various other emergencies. By use of such technologies, easily many kinds of diseases, apart from Parkinson's, can be healed [1, 2]. The use of sensor technologies has shown great possibilities in similar fields like biomedical assistive technology development [12] and enhancements by use of AI- powered tools and techniques.

# IV. CONCLUSION

This study concludes by showing that classifying Gait using machine learning techniques is feasible. Although conventional machine learning algorithms perform admirably, deep neural networks are more adept at deciphering intricate relationships found in the data. With the Stacking Ensemble and federated learning Conv 1D + LSTM model, an excellent outcome was attained. The results highlight machine learning's potential to support early gait prediction and diagnosis. In order to increase predictive accuracy in actual clinical scenarios, future research could concentrate on growing datasets and improving model architectures.

In conclusion, the trade- offs between complexity and performance are reflected in the varying accuracy obtained across models. Even though models showed excellent accuracy, there is still a lot of potential for model and system improvement. Further highlighting the models'

advantages and shortcomings, the confusion matrix's nuanced insights open the door to future machine learning advancements in Parkinson's disease classification.

# Declaration of clinical and ethical standard practice:

Not applicable (No human subjects were used for experiments). Data from publication is adapted to train models

# Data availability statement:

Data is publicly available through frontiers publication [17].

# REFERENCES

[1] Alshammri R, Alharbi G, Alharbi E, Almubark I. Machine learning approaches to identify Parkinson's disease using voice signal features. Front Artif Intell. 2023;6:1084001. doi: 10.3389/frai.2023.1084001. PMID: 37056913; PMCID: PMC10086231.  [2] Chatterjee K, Kumar RP, Bandyopadhyay A, Swain S, Mallik S, Li A, Ray KP. PDDET: Parkinson's Disease Detection Using ML Ensemble Techniques and Customized Big Dataset. Information. 2023;14(9):502. doi: 10.3390/info14d90502.  [3] Nithya M, Lalitha V, Paveethra K, Kumari S. Early Detection of Parkinson's Disease using Machine Learning & Image Processing. 2022 International Conference on Computer Communication and Informatics (ICCCI), Coimbatore, India; 2022. p. 1- 4. doi: 10.1109/ICCCI54379.2022.9740961.  [4] S E, T C, R S VD. Prediction of Parkinson's disease using XGBoost. 2022 8th International Conference on Advanced Computing and Communication Systems (ICACCS), Coimbatore, India; 2022. p. 1769- 1772. doi: 10.1109/ICACCS54159.2022.9785227.  [5] Schmid M, Sheikhi S, Kheirabadi MT. An Efficient Rotation Forest- Based Ensemble Approach for Predicting Severity of Parkinson's Disease. J Healthcare Eng. 2022;5524852. doi: 10.1155/2022/5524852.  [6] Uddin MZ. A wearable sensor- based activity prediction system to facilitate edge computing in smart healthcare system. J Parallel Distrib Comput. 2018;123:46- 53. doi: 10.1016/j.jpdc.2018.08.010.  [7] Pardoel S, Kofman I, Nantel J, Lemaire ED. Wearable- Sensor- Based Detection and Prediction of Freezing of Gait in Parkinson's Disease: A Review. Sensors. 2019;19:5141. doi: 10.3390/s19235141.  [8] Mishra RK, Nunes AS, Enriquez A, et al. At- home wearable- based monitoring predicts clinical measures and biological biomarkers of disease severity in Friedreich's Ataxia. Commun Med. 2024;4:217. doi: 10.1038/s43856- 024- 00653- 1.  [9] Yue P, Li Z, Zhou M, Wang X, Yang P. Wearable- Sensor- Based Weakly Supervised Parkinson's Disease Assessment with Data Augmentation. Sensors. 2024;24:1196. doi: 10.3390/s24041196.  [10] Pradhan MR, Ateeq K, Mago B. Wearable Device Based on IoT in the Healthcare System for Disease Detection and Symptom Recognition. Int J Artif Intell Tools. 2021;30(6- 8):Article 2140011. doi: 10.1142/S021821302140011X.

[11] Habib Z, Mughal M, Khan M, Shabaz M. WiFOG: Integrating deep learning and hybrid feature selection for accurate freezing of gait detection. Alexandria Eng J. 2023;86:481- 493. doi: 10.1016/j.aej.2023.11.075. [12] Bikias T, Iakovakis D, Hadjidimitriou S, Charisis V, Hadjileontiadis LJ. DEEPFOG: An IMU- Based detection of freezing of GAIT episodes in Parkinson's disease patients via Deep Learning. Front Robot AI. 2021;8:537384. doi: 10.3389/frobt.2021.537384. [13] Borzi L, Sigcha L, Rodriguez- Martin D, Olmo G. Real- time detection of freezing of gait in Parkinson's disease using multi- head convolutional neural networks and a single inertial sensor. Artif Intell Med. 2022;135:102459. doi: 10.1016/j.artmed.2022.102459. [14] Salomon A, Gazit E, Ginis P, et al. A machine learning contest enhances automated freezing of gait detection and reveals time- of- day effects. Nat Commun. 2024;15:4853. doi: 10.1038/s41467- 024- 49027- 0. [15] Shi B, Tay A, Au WL, Tan DML, Chia NSY, Yen SC. Detection of Freezing of Gait Using Convolutional Neural Networks and Data From Lower Limb Motion Sensors. IEEE Trans Biomed Eng. 2022;69(7):2256- 2267. doi: 10.1109/TBME.2022.3140258. [16] Kim J, Porciuncula F, Yang HD, et al. Soft robotic apparel to avert freezing of gait in Parkinson's disease. Nat Med. 2024;30:177- 185. doi: 10.1038/s41591- 023- 02731- 8. [17] De Souza CR, Miao R, De Oliveira JA, et al. A public data set of videos, inertial measurement unit, and clinical scales of freezing of GAIT in individuals with Parkinson's disease during a Turning- In- Place task. Front Neurosci. 2022;16:832463. doi: 10.3389/fnins.2022.832463. [18] Powers, D. M. (2020). Evaluation: from precision, recall and F- measure to ROC, informedness, markedness and correlation. arXiv preprint arXiv:2010.16061.