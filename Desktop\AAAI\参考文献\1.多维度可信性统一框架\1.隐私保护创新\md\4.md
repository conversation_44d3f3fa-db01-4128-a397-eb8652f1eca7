# Enhancing Convergence, Privacy and Fairness for Wireless Personalized Federated Learning: Quantization-Assisted Min-Max Fair Scheduling

<PERSON>, <PERSON><PERSON>, Senior Member, IEEE, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, Graduate Student Member, IEEE, <PERSON>, Graduate Student Member, IEEE, <PERSON>, <PERSON><PERSON>, Senior Member, <PERSON>, and <PERSON>, Fellow, IEEE

Abstract—Personalized federated learning (PFL) offers a solution to balancing personalization and generalization by conducting federated learning (FL) to guide personalized learning (PL). Little attention has been given to wireless PFL (WPFL), where privacy concerns arise. Performance fairness of PL models is another challenge resulting from communication bottlenecks in WPFL. This paper exploits quantization errors to enhance the privacy of WPFL and proposes a novel quantization- assisted Gaussian differential privacy (DP) mechanism. We analyze the convergence upper bounds of individual PL models by considering the impact of the mechanism (i.e., quantization errors and Gaussian DP noises) and imperfect communication channels on the FL of WPFL. By minimizing the maximum of the bounds, we design an optimal transmission scheduling strategy that yields min- max fairness for WPFL with OFDMA interfaces. This is achieved by revealing the nested structure of this problem to decouple it into subproblems solved sequentially for the client selection, channel allocation, and power control, and for the learning rates and PL- FL weighting coefficients. Experiments validate our analysis and demonstrate that our approach substantially outperforms alternative scheduling strategies by  $87.08\%$ ,  $16.21\%$ , and  $38.37\%$  in accuracy, the maximum test loss of participating clients, and fairness (Jain's index), respectively.

Index Terms—Personalized federated learning, differential privacy, quantization, min- max fairness, scheduling.

# I. INTRODUCTION

Personalized federated learning (PFL) has been recently proposed to account for both generalization and personalization. It can strike a balance between personalized models and the global model, e.g., via a global- regularized multi- task framework [1]. Only several studies [2]- [5] have attempted to integrate PFL under a wireless setting, compared to significant efforts on wireless federated learning (FL). Moreover, existing works on PFL, e.g., [1], [6]- [15], have focused on the accuracy or performance distribution fairness under the assumption of ideal (wired) communication environments.

A challenge arising from wireless PFL (WPFL) is the communication bottleneck, especially when dynamic, noisy, and resource- constrained wireless channels are considered. The channels of geographically dispersed clients can differ significantly and change frequently. While more participating clients and, hence, larger overall training datasets are conducive to the convergence of WPFL, they could congest wireless resources and hinder FL model uploading [16]. While wireless FL (WFL) focuses primarily on optimizing the performance of the global model across all clients [17]- [20], WPFL emphasizes the performance of each individual personalized model. In WPFL, the challenge arises from how heterogeneous wireless communication conditions affect the performance of different personalized models. Therefore, designing WPFL scheduling and parameter adjustment methods to efficiently handle resource constraints and imperfect channels becomes more challenging compared to traditional WFL, which optimizes a single global model shared across all clients.

Another challenge is that WPFL is prone to privacy leakage due to the incorporation of FL under the wireless setting. Differential privacy (DP) [21] can be applied to protect the privacy of WPFL. While the majority of the existing studies, e.g., [22]- [27], have straight- forwardly added artificial noise to implement DP in FL/PFL, several pioneering studies e.g., [28]- [30], have explored privacy protection through quantization since quantization inherently brings errors and can help perturb FL local models. Stochastic quantization with random quantization lattices [28] or random mappings [29], [30] has been considered for mathematical tractability, which is unfortunately less compatible with practical wireless systems. Specifically, random mapping requires additional random mapping functions [29], [30]. For stochastic dithering quantization, synchronized random seeds for the generation of dithering and scaling factors are required during decoding [28]. These processes introduce complexities that can be difficult to manage within the real- time constraints of communication networks. Moreover, fairness is critical to WPFL and can be difficult to achieve. It is affected by scheduling strategies in wireless channels with limited bandwidths. Neither of [28] and [29] has given thought to fairness.

This paper presents a novel quantization- assisted Gaussian DP mechanism and transmission scheduling strategy for WPFL with orthogonal frequency division multiple access (OFDMA) interfaces, where quantization errors are exploited to enhance the privacy of WPFL while fairness is strengthened among personalized models through scheduling. Specifically, we analyze the convergence upper bound of WPFL in the presence of errors caused by quantization, DP, and imperfect communication channels. Based on the convergence upper bound, the transmission scheduling strategy is designed to achieve the min- max fairness of WPFL by jointly optimizing the client selection, channel allocation, power control, and the weighting coefficients between personalized learning (PL) and FL models, adapting to the channel conditions, as well as the privacy requirement of WPFL.

The contributions of this paper are summarized as follows:

- We propose the exploitation of quantization errors to enhance the privacy of WPFL and develop the new quantization-assisted Gaussian mechanism. We analyze the cumulative privacy loss of the mechanism.- A convergence upper bound of WPFL is derived, characterizing the impact of the new quantization-assisted Gaussian mechanism and the imperfect channel conditions on the convergence of WPFL.- While the impact of the PFL learning rates and PL-FL weighting coefficients on the PL model convergence is intricate, a new min-max problem is formulated to enhance the convergence of wireless PFL and maintain fairness by retaining the consistency of the convergence rates among the clients and minimizing the maximum convergence bias of all clients.- A new scheduling strategy is developed to solve the min-max problem by revealing the nested structure of the problem and decoupling the problem into two subproblems solved sequentially for the client selection, channel allocation, and power control, and for the learning rate and PL-FL weighting coefficients.

Extensive experiments validate our convergence analysis of the WPFL under the new quantization- assisted Gaussian mechanism. Three image classification tasks are performed using a deep neural network (DNN), multi- class linear regression (MLR), and convolutional neural network (CNN) on the Federated MNIST, Federated FMNIST, and Federated CIFAR10 datasets. Under the CNN model, our approach substantially outperforms its alternative scheduling schemes, i.e., round- robin, random selection, and non- adjustment, by at least  $87.08\%$ $16.21\%$  , and  $38.37\%$  in accuracy, the maximum test loss of participating clients, and fairness (measured by Jain's index), respectively. Under DNN and MLR models, while our approach slightly outperforms the alternatives in fairness, it is at least  $52.26\%$  and  $15.99\%$  better in accuracy and the maximum test loss, respectively.

The rest of this paper is structured as follows. Section II reviews the related works. Section III outlines the system and threat models. Section IV elaborates on the new quantization- assisted Gaussian mechanism and analyzes its privacy budget. In Section V, the convergence upper bound of WPFL is established under the new mechanism. In Section VI, we develop a min- max fair scheduling strategy to accelerate the convergence in a fair fashion. Experimental results are presented in Section VII. Conclusions are drawn in Section VIII.

Notation:  $\| \cdot \|$  denotes the  $L_{2}$ - norm of a vector or matrix;  $\| \cdot \|$  stands for cardinality;  $\nabla (\cdot)$  takes gradient;  $\circ$  takes the element- wise product of two vectors or matrices.

# II. RELATED WORK

# A. Personalization

PFL has been explored to combat statistical heterogeneity among participants through transfer learning (TL) [6], metalearning [7]- [9], and multitask learning (MTL) [10]- [15]. TL- based FL enhances personalization by diminishing domain discrepancy of the global and local models [31]. FedMD [6] is an FL structure grounded in TL and knowledge distillation (KD), enabling clients to formulate autonomous models utilizing their private data.

Meta- learning finds utility in enhancing the global model for rapid personalization. In [7], a variation of FedAvg, named Per- FedAvg, was introduced, leveraging the Model- Agnostic Meta- Learning (MAML). It acquired a proficient initial global model that is effective on a novel heterogeneous task and can be achieved through only a few gradient descent steps. You et al. [9] proposed Semi- Synchronous Personalized FederatedAveraging (PerFedS) based on MAML. In [8], a privacy budget allocation scheme based on Renyi DP composition theory was designed to address information leakage arising from two- stage gradient descent of meta- learning- based PFL.

MTL trains a model to simultaneously execute several related tasks. In [10], pFedMe employing Moreau envelopes as the regularized loss functions for clients was recommended to disentangle the optimization of personalized models from learning the global model. The global model aggregates the local models updated based on the personalized models. Each client's personalized model maintains a bounded distance from the global model. In [11], FedProx was formulated by incorporating a proximal term into the local subproblem. Contrast was delineated between the global and local models to ease the influence of local updates. In [15], a federated multi- task learning (FMTL) framework was developed, where the server broadcasts a set of global models aggregated based on the local models of different clusters of clients. Each client selects one of the global models for local model updating.

Huang et al. [12] integrated PFL with supplementary terms and employed a federated attentive message passing (FedAMP) strategy to mitigate the impact of diverse data. A protocol named APPLE [13] was proposed to improve the personalized model of each client based on the others' models. Clients obtain the personalized models locally by aggregating the core models of other clients downloaded from the server. The aggregation weights and the core models are locally learned from the personalized model by adding a proximal term to the local objectives. Instead of overwriting the old local model with the downloaded global model, FedALA [14] aggregates the downloaded global model and the old local model for local model initialization.

Some recent studies [2]- [5] have started integrating PFL in wireless networks. In [2], over- the- air clustered FL was designed to enable spectrum sharing across different clusters by employing a coordinated precoder design. In [3], usercentric aggregation was designed, where the server aggregates personalized models based on collaboration coefficients heuristically determined at each round. K- means clustering was applied to cluster users based on their similarity and serve each group of similar users with one personalized model. Ensemble FL [4] was proposed by integrating intra- cluster FL models via model ensemble. Clusters were formed to improve data distribution similarity and expected energy consumption using a coalition formation game solved by a Nash- stable algorithm. In [5], three- layer FL was adopted, where edge servers aggregate local updates in multiple clusters, and a cloud server implements global aggregation. Scheduling and bandwidth allocation were optimized to balance training loss minimization and round latency minimization.

However, these existing studies [2]- [15] have focused primarily on model accuracy. None has taken fairness among the PL models of different participants.

# B. Privacy

Privacy has been increasingly valued in FL studies [22]- [27] have explored ways to integrate privacy techniques into FL to provide a demonstrable assurance of safeguarding privacy. However, little to no consideration has been given to the personalization of learning models and their fairness under imperfect communications and privacy techniques. In [22], a DP- based framework was suggested to avert privacy leakage by introducing noise to offuscate the local model parameters. In [23], three local DP (LDP) techniques were devised to uphold the privacy of FL, and diminish communication overhead in crowd- sourcing scenarios. Liu et al. [26] proposed a transceiver protocol to maximize the convergence rate under privacy constraints in a MIMO- based DP FL system, where a server performs over- the- air model aggregation and parallel private information extraction from the uploaded local gradients with a DP mechanism.

In [25], DP noises were adaptively added to local model parameters to preserve user privacy during FL. The amplitude of DP noises was adjustable to preserve privacy and encourage convergence. FedDual [27] was designed to add DP noises locally and aggregate asynchronously via a gossip protocol. Noise- cutting was adopted to alleviate the impact of the DP noise on the global model. In [32], the Gaussian mechanism was considered in a mean- regularized MTL framework, and the accuracy was analyzed for single- round FL using a Bayesian framework. In [33], differentially private federated MTL was designed for human digital twin systems with computationally efficient blockchain- enabled validation.

Some studies [28]- [30] have utilized stochastic quantization in support of DP. In [28], devices utilize vector quantization based on random lattices to compress their noise- perturbed local models, achieving a predefined privacy level by adding noise and exploiting quantization errors. In [29], a secure and efficient FL framework was proposed by adding a stochastic quantization module at the client to quantize the local gradients for global aggregation. A new metric was designed to analyze the privacy and a trade- off between communication overhead, convergence rate, and privacy concerning the quantization interval. In [30], an FL algorithm preserving privacy and efficiency of communication (P2CEFL) was proposed, where a subtractive dithering approach was employed to reduce communication overhead under DP guarantee.

However, none of the above works [8], [22]- [30], [32], [34] have considered fairness among the participants in PFL.

# C. Fairness

Some existing studies, e.g., [1], [35], [36], have attempted to improve performance distribution fairness, i.e., by mitigating the variability in model accuracy among different clients. Yet, none has taken user privacy into account. In [35],  $q$ - FFL was proposed to achieve a more uniform accuracy distribution across clients. A parameter  $q$  was used to re- weight the aggregation loss by assigning bigger weights to clients undergoing more significant losses. In [36], FedMGDA+ was suggested to enhance model robustness while upholding fairness with positive intentions. A multi- objective problem was structured to diminish the loss functions across all clients, tackled by employing Pareto- steady resolutions to pinpoint a collective descent direction suitable for all chosen clients. Li et al. [1] designed a scalable federated MTL framework, Ditto, which learns personalized and global models in a global- regularized framework. Regularization was introduced to bring the personalized models in proximity to the optimal global model. The optimal weighting coefficient of Ditto was designed in terms of fairness and robustness. Unfortunately, these studies [1], [35], [36] have overlooked privacy risks or failed to address the influence of DP and imperfect communications on fairness.

# III. SYSTEM MODEL AND PROBLEM STATEMENT

In this section, we present the PFL system, channel model, threat model, and the preliminary of DP. The PFL system consists of a server and  $N$  clients.  $\mathcal{N}$  denotes the set of clients.  $\mathcal{D}_n$  denotes the local dataset at client  $n \in \mathcal{N}$ .  $\mathcal{D}$  collects all data samples.  $|\mathcal{D}| = \sum_{n = 1}^{N} |\mathcal{D}_n|$ . The PFL has both global and personalized objectives for FL and PL, respectively.

# A. PFL Model

At every communication round  $t$ , the server selects a subset of clients  $\mathcal{N}_t \subset \mathcal{N}$ , and quantizes and sends the latest global model to the clients. Upon the receipt of the noisy global model, i.e., through imperfect downlink channels, each client  $n$ ,  $\forall n \in \mathcal{N}_t$ , executes local FL training, and updates its FL local model. Each client  $n$ ,  $\forall n \in \mathcal{N}$ , executes PL training, and updates its PL model. After clipping, DP perturbation, and quantization, client  $n$  uploads its local models to the server. Based on the received local models from client  $n$ , the global model is obtained by aggregation at the server.

1) FL: As for FL, the global objective of FL is to learn an FL global model with the minimum global training loss, i.e.,

$$
\min_{\pmb{\omega}}F(\pmb {\omega}) = \sum_{n = 1}^{N}p_{n}F_{n}(\pmb {\omega}), \tag{1}
$$

where  $\omega \in \mathbb{R}^{|\omega |}$  is the model parameter with  $|\omega |$  elements;  $F(\cdot)$  is the global loss function, and  $F_{n}(\cdot)$  is the local loss function of client  $n\in \mathcal{N}$ $p_n\triangleq \frac{|\mathcal{D}_n|}{|\mathcal{D}|}$  is the aggregation coefficient for client  $n$ $\begin{array}{r}w_{n} = \sum_{n = 1}^{N}p_{n} = 1 \end{array}$  . For illustration convenience, we assume the size of each client's local dataset is the same, i.e.,  $\begin{array}{r}p_n = \frac{1}{N} \end{array}$

According to (1), on each communication round, an FL local model, denoted by  $\pmb {u}_n^t$  is trained at every selected client  $n$  followed by clipping, DP perturbation, and quantization, before the client uploads the FL local model to the server.

Clipping: The FL local models are clipped as

$$
\begin{array}{r}\pmb {u}_n^t = \pmb {u}_n^t\big / \max \left(1,\| \pmb {u}_n^t\| /C\right), \end{array} \tag{2}
$$

where  $C$  is the pre- determined clipping threshold ensuring that the local model parameter  $\| \pmb {u}_n^t\| \leq C$  [25].

DP perturbation: Let  $\pmb {u}_n^t$  denote the independent and identically distributed (i.i.d.) Gaussian noise added by client  $n$  to its local model  $\pmb {u}_n^t$  at the  $t$  - th communication round. Each element in  $\pmb {z}_n^t$  follows  $\mathbb{N}(0,\sigma_{\mathrm{DP}}^2)$

Definition 1  $((\epsilon ,\delta)\text{- DP})$  A privacy preserving mechanism  $\mathcal{M}:\mathcal{X}\rightarrow \mathcal{R}$  is  $(\epsilon ,\delta)$  - DP if, for any two adjacent datasets  $\mathcal{X}_0$ $\mathcal{X}_1\in \mathcal{X}$  and any subset of outputs  $S\subseteq \mathcal{R}$  it holds that

$$
\operatorname *{Pr}\left[\mathcal{M}\left(\mathcal{X}_0\right)\in \mathcal{S}\right]\leq \epsilon^t\operatorname *{Pr}\left[\mathcal{M}\left(\mathcal{X}_1\right)\in \mathcal{S}\right] + \delta , \tag{3}
$$

where  $\epsilon >0$  specifies the difference beyond which the outputs concerning  $\mathcal{X}_0$  and  $\mathcal{X}_1$  can be differentiated, and  $\delta \in [0,1]$  is the probability with which the ratio between the probabilities of  $\mathcal{X}_0$  and  $\mathcal{X}_1$  is no smaller then  $\epsilon^e$

Definition 2 (Max Divergence). The Max Divergence, also known as the  $\infty$  - th order of Renyi divergence, between two random variables  $Y$  and  $z$  taking values from the same sample space  $\mathcal{V}$  is defined as

$$
D_{\infty}(Y||Z) = \max_{y\in \mathcal{Y}}\left[\ln \frac{\operatorname*{Pr}[Y = y]}{\operatorname*{Pr}[Z = y]}\right]. \tag{4}
$$

The  $\delta$  - Approximate Max Divergence is defined as

$$
D_{\infty}^{\delta}(Y||Z) = \max_{y\in \mathcal{Y}}\left[\ln \frac{\operatorname*{Pr}[Y = y] - \delta}{\operatorname*{Pr}[Z = y]}\right]. \tag{5}
$$

The randomized mechanism  $\mathcal{M}:\mathcal{D}\rightarrow \mathcal{R}$  satisfies  $\epsilon \text{- DP}$  if  $D_{\infty}\left[\mathcal{M}\left(\mathcal{X}_0\right)\right]\left|\mathcal{M}\left(\mathcal{X}_1\right)\right|\leq \epsilon$  for all measurable sets  $\mathcal{V}\subseteq \mathcal{R}$  and any two adjacent datasets  $\mathcal{X}_0,\mathcal{X}_1\in \mathcal{D}$  Moreover,  $\mathcal{M}$  satisfies  $(\epsilon ,\delta)$  - DP if  $D_{\infty}^{\delta}\left[\mathcal{M}\left(\mathcal{X}_0\right)\right]\left|\mathcal{M}\left(\mathcal{X}_1\right)\right]\leq \epsilon$  [37].

Quantization: The clients quantize and transmit their FL local models to the server for FL global aggregation. The server quantizes and broadcasts the FL global model. Suppose that each element of the FL local and global models is quantized into  $R$  bits. We set the quantization range of the FL local models to  $[- C - 3\sigma_{\mathrm{DP}},C + 3\sigma_{\mathrm{DP}}]$  capturing  $99.7\%$  of the local models perturbed by the Gaussian mechanism. With no perturbation, the quantization range of the FL global models is  $[- C,C]$  . Then, the respective quantization intervals of the FL local and global models are given by

$$
\triangle_{\mathrm{L}} = \frac{2(C + 3\sigma_{\mathrm{DP}})}{2^{R} - 1};\triangle_{\mathrm{G}} = \frac{2C}{2^{R} - 1}. \tag{6}
$$

The respective maximum quantization errors of the FL local and global models are given by

$$
E_{\mathrm{L}}^{\mathrm{max}} = \frac{\triangle_{\mathrm{L}}}{2}\triangleq \beta_{\mathrm{L}}(C + 3\sigma_{\mathrm{DP}});E_{\mathrm{G}}^{\mathrm{max}} = \frac{\triangle_{\mathrm{L}}}{2}\triangleq \beta_{\mathrm{G}}C, \tag{7}
$$

where, for conciseness,  $\beta_{\mathrm{L}}\triangleq \frac{1}{2^{R} - 1}$  and  $\beta_{\mathrm{G}}\triangleq \frac{1}{2^{R} - 1}$

Let  $\mathcal{Q}(\cdot)$  denote the multi- dimensional quantization with every element rounded towards the closest quantization level. The clipped, perturbed, and quantized FL local model is

$$
\hat{\pmb{u}}_n^t = \mathcal{Q}(\pmb {u}_n^t +\pmb {z}_n^t), \tag{8}
$$

which is uploaded by client  $n$  in the  $t$  - th round.

2)  $PL$  A PL model  $\pi_{n}$  is trained locally at client  $n$  concerning the FL global model. The training of the FL global model and that of the PL models are synchronized on the basis of FL rounds. Client  $n$  updates its PL model  $\pi_{n}^{t}$  based on the FL global model  $\omega^t$  updated at the  $t$  -th round. For the sake of model generalization, we encourage the PL model to be close to the optimal FL global model, i.e.,

$$
\begin{array}{rl} & {\min_{\pmb {\pi}_n}f_n(\pmb {\pi}_n;\pmb {\omega}^*) = (1 - \frac{\lambda}{2})F_n(\pmb {\pi}_n) + \frac{\lambda}{2}\| \pmb {\pi}_n - \pmb {\omega}^*\| ^2,}\\ & {\quad \mathrm{s.t.}\pmb {\omega}^* = \underset {\pmb{\omega}}{\arg \min}\frac{1}{N}\sum_{n = 1}^{\infty}F_n(\pmb {\omega}),} \end{array} \tag{9b}
$$

where  $f_{n}(\cdot)$  is the loss function of the PL model at client  $n$ $\lambda \in [0,2]$  is a PL- FL weighting coefficient that controls the trade- off between the FL and PL models. When  $\lambda = 0$  PFL trains a PL model for each client based on its local datasets. When  $\lambda = 2$  there is no personalization.

# B. Communication Model

The quantized parameters of the FL local and global models are modulated into  $M_{\omega}$  - ary QAM symbols. For a model with  $|\omega |$  elements, the minimum uplink data rate (in bits/s) is

$$
r_{\min} = |\omega |R / \tau_{\max}, \tag{10}
$$

where  $\tau_{\mathrm{max}}$  is the maximum transmission delay (in seconds). 1) Channelization: The server connects  $N$  clients wirelessly over  $K$  orthogonal subchannels.  $\kappa = \{1,\ldots ,K\}$  denotes the set of subchannels. In each round, at most  $K$  clients are selected for local model uploading. Let  $\mathbf{p}^t =$ $\{P_1^t,\ldots ,P_N^t\} \in \mathbb{R}^N$  and  $\mathbf{c}^t = \{\mathbf{c}_1^t,\ldots ,\mathbf{c}_N^t\} \in \mathbb{R}^{N\times K}$  collect the transmit powers of all clients and their selected subchannels in the  $t$  - th round.  $P_{n}^{t} = 0$  if client  $n$  selects no subchannel in the round.  $\mathbf{c}_n^t = \{c_{n,1}^t,\ldots ,c_{n,K}^t\}$  collects the channel selection indicators, with  $c_{n,k}^{t} = 1$  if subchannel  $k$  is selected for client  $n$  , and  $c_{n,k}^{t} = 0$  , otherwise.

Suppose that client  $n$  uploads its clipped, perturbed and quantized local model,  $\hat{\pmb{u}}_n^t$  , through subchannel  $k$  in the  $t$  - th round. The uplink data rate is given by

$$
r_{n,k}^t = B\log_2(1 + \gamma_{n,k,\mathrm{L}}^t), \tag{11}
$$

where  $B$  is the bandwidth of each subchannel, and  $\gamma_{n,k,\mathrm{L}}^t$  is the receive signal- to- noise ratio (SNR) at the server from client

$n$  in subchannel  $k$  during the  $t$ - th round, as given by

$$
\gamma_{n,k,\mathrm{L}}^{t} = P_{n}^{t}\left|h_{n,k,\mathrm{L}}^{t}\right|^{2} / \sigma_{0}^{2}, \tag{12}
$$

where  $h_{n,k,\mathrm{L}}^t$  is the channel of client  $n$  in subchannel  $k$ , and  $\sigma_0^2$  is the variance of the additive white Gaussian noise (AWGN).

2) Received Models: Let  $e_{n,k,\mathrm{L}}^t$  denote the bit error rate (BER) of client  $n$  in subchannel  $k$  [38]:

$$
e_{n,k,\mathrm{L}}^t = \frac{2\sqrt{M_\omega - 2}}{\sqrt{M_\omega}\log\sqrt{M_\omega}} Q\left(\sqrt{\frac{3\gamma_{n,k,\mathrm{L}}^t\log_2M_\omega}{M_\omega - 1}}\right), \tag{13}
$$

where  $Q(x) = \frac{1}{\sqrt{2\pi}}\int_{x}^{\infty}e^{- \frac{n^2}{2}}dx$  is the  $Q$  function.

Then, for client  $n$ , the error probability of each element of its FL local model is given by

$$
\rho_{n,\mathrm{L}}^{t} = \sum_{k = 1}^{K}\zeta_{n,k}^{t}\left(1 - (1 - e_{n,k,\mathrm{L}}^{t})^{R}\right). \tag{14}
$$

Similarly, we obtain the error probability  $\rho_{n,\mathrm{G}}^t$  of each element of the FL global model received at client  $n$  in the  $t$ - th round.

In the  $t$ - th round, we use  $\mathbf{s}_n^t = \{s_{n,i}^t,\forall i = 1,\ldots ,|\omega |\} \in \mathbb{R}^{|\omega |}$  to denote the error indicator vector for the received local model  $\hat{\omega}_{n}^{t}$ $s_{n,i}^{t} = 0$  if the  $i$ - th element of  $\hat{\omega}_{n}^{t}$  is error- free; otherwise,  $s_{n,i}^{t} = 1$ . At the server, the received FL local model of client  $n$  and the aggregated FL global model are given by

$$
\begin{array}{rl} & {\hat{\omega}_{n}^{t} = \mathbf{s}_{n}^{t}\circ \hat{\mathbf{u}}_{n}^{t} + (1 - \mathbf{s}_{n}^{t})\circ \tilde{\mathbf{u}}_{n}^{t};}\\ & {\tilde{\omega}_{\mathrm{L}}^{t} = \frac{1}{|\mathcal{N}_{t}|}\sum_{n\in \mathcal{N}_{t}}\hat{\omega}_{n}^{t},} \end{array} \tag{15}
$$

where  $\hat{\mathbf{u}}_n^t$  is the erroneous version of  $\tilde{\mathbf{u}}_n^t$  resulting from imperfect, noisy wireless channels. Considering the errors caused by DP noise, quantization, and transmission in imperfect channels,  $\tilde{\mathbf{u}}_n^t$  and  $\hat{\mathbf{u}}_n^t$  are given by

$$
\hat{\pmb{u}}_n^t = \pmb {u}_n^t +\pmb {z}_n^t +\mathbf{E}_{\mathrm{L},\mathrm{L}}^t;\quad \hat{\pmb{u}}_n^t = \pmb {u}_n^t +\pmb{\zeta}_{n,\mathrm{L}}^t, \tag{17}
$$

where  $\mathbf{E}_{n,\mathrm{L}}^t = \{E_{n,i,\mathrm{L}}^t,i = 1,\ldots ,|\omega |\} \in \mathbb{R}^{|\omega |}$  is the quantization error vector of  $\mathbf{u}_n^t$ $|E_{n,i,\mathrm{L}}^t |\leq E_{\mathrm{L}}^{\mathrm{max}}$ $i = 1,\ldots ,|\omega |$ $\zeta_{n,\mathrm{L}}^{t} = \{s_{n,i,\mathrm{L}}^{t},i = 1,\ldots ,|\omega |\} \in \mathbb{R}^{|\omega |}$  is the error between  $\hat{\mathbf{u}}_n^t$  and  $\hat{\mathbf{u}}_n^t$  caused by DP noise, quantization, and transmission errors.  $|\zeta_{n,i,\mathrm{L}}^{t}|\leq |u_{n,i}^{t}| + C + \partial_{\mathrm{DP}}$ $i = 1,\ldots ,|\omega |$

In the  $(t + 1)$ - th round, at client  $n$ , the received FL global model is given by

$$
\hat{\omega}_{n,\mathrm{G}}^{t + 1} = \mathbf{s}_{n,\mathrm{G}}^{t + 1}\circ \hat{\omega}_{n,\mathrm{L}}^{t + 1} + (\mathbf{1}_{|\omega |} - \mathbf{s}_{n,\mathrm{G}}^{t + 1})\circ \tilde{\omega}_{\mathrm{G}}^{t}, \tag{18}
$$

where  $\mathbf{s}_{n,\mathrm{G}}^{t + 1} = \{s_{n,i,\mathrm{G}}^{t + 1},\forall i = 1,\ldots ,|\omega |\} \in \mathbb{R}^{|\omega |}$  is the error indicator vector for the transmission of the FL global model,  $s_{n,i,\mathrm{G}}^{t + 1} = 0$  if the  $i$ - th element of  $\mathbf{s}_{n,\mathrm{G}}^{t + 1}$  is received error- free; otherwise,  $s_{n,i,\mathrm{G}}^{t + 1} = 1$ . Moreover,  $\tilde{\omega}_{\mathrm{G}}^{t}$  is the global model after quantization. Let  $\hat{\omega}_{\mathrm{L}}^{t + 1}$  be the erroneous version of  $\tilde{\omega}_{\mathrm{G}}^{t}$ . Then,

$$
\begin{array}{r}\tilde{\omega}_{\mathrm{G}}^{t} = \tilde{\omega}_{\mathrm{L}}^{t} + \mathbf{E}_{\mathrm{G}}^{t};\quad \hat{\omega}_{n,\mathrm{L}}^{t + 1} = \tilde{\omega}_{\mathrm{L}}^{t} + \zeta_{n,\mathrm{G}}^{t + 1}, \end{array} \tag{19}
$$

where  $\mathbf{E}_{\mathrm{G}}^{t} = \{E_{i,\mathrm{G}}^{t},\forall i = 1,\ldots ,|\omega |\} \in \mathbb{R}^{|\omega |}$  is the quantization error vector of  $\tilde{\omega}_{\mathrm{L}}^{t}$  with  $|E_{i,\mathrm{G}}^{t}|\leq E_{\mathrm{G}}^{\mathrm{max}},\forall i$ $\zeta_{n,\mathrm{G}}^{t + 1} = \{s_{n,i,\mathrm{G}}^{t + 1},\forall i = 1,\ldots ,|\omega |\} \in \mathbb{R}^{|\omega |}$  is the error between  $\hat{\omega}_{\mathrm{L}}^{t + 1}$  and  $\hat{\omega}_{n,\mathrm{L}}^{t + 1}$  caused by downlink quantization and transmission errors, with  $|\zeta_{n,i,\mathrm{G}}^{t + 1}|\leq |\omega_{i,\mathrm{L}}^{t}| + C,\forall i$

3) Local Model Update: The FL local model and the PL model of client  $n$  are updated at the  $(t + 1)$ -th communication

round based on the received FL global model, as given by

$$
\begin{array}{r}\pmb {u}_n^{t + 1} = \hat{\omega}_{n,\mathrm{G}}^{t + 1} - \eta_{\mathrm{F},\mathrm{G}}^{t + 1}\nabla F_n\left(\hat{\omega}_{n,\mathrm{G}}^{t + 1}\right);\\ \tilde{\pmb{\pi}}_n^{t + 1} = \tilde{\pmb{\pi}}_n^t -\eta_{\mathrm{P},n}^{t + 1}\left[(1 - \frac{\lambda_n^{t + 1}}{2})\nabla F_n(\tilde{\pmb{\pi}}_n^{t + 1} + \lambda_n^{t + 1}\left(\tilde{\pmb{\pi}}_n^t -\hat{\omega}_{n,\mathrm{G}}^{t + 1}\right)\right]. \end{array} \tag{12b}
$$

where  $\eta_{\mathrm{F},n}^{t + 1}$  and  $\eta_{\mathrm{P},n}^{t + 1}$  are the FL local learning rate and the PL learning rate of client  $n$  at the  $(t + 1)$ - th round, respectively.

Define  $\pmb {u}_n^*\in \mathbb{R}^{|\omega |}$  as the optimal FL local model of client  $n$ , and  $\pi_n^*\in \mathbb{R}^{|\omega |}$  as the optimal PL model, i.e.,

$$
\begin{array}{r}\pmb {u}_n^* = \underset {\pmb {u}_n}{\arg \min}F_n(\pmb {u}_n);\quad \pi_n^* = \underset {\pi_n}{\arg \min}f_n(\pi_n;\omega^*). \end{array} \tag{21}
$$

# C. Threat Model and Problem Statement

The server may be honest- but- curious, and attempt to recover the training datasets of the clients or infer their private features based on the FL local models uploaded by the clients [39]. There may also be external attackers who intend to breach the privacy of the clients. Although the clients train their FL local models locally, the local models are shared with the server and can be analyzed to potentially compromise their privacy under inference attacks during learning [39] and model- inversion attacks during testing [40].

In this paper, we wish to exploit the inherent privacy- preserving capability of quantization to enhance the privacy of WPFL. We also wish to optimize the scheduling policy and power control for the transmissions of the local models to facilitate the convergence of WPFL with privacy enhancement while maintaining performance fairness between the PL models of the participating clients.

# IV. PRIVACY ANALYSIS OF WPFL

This section analyzes the impact of quantization on the privacy of WPFL, establishes the new quantization- assisted Gaussian mechanism, and analyzes its privacy budget. This starts with the following proposition.

Proposition 1 (Quantization- Assisted Gaussian Mechanism). For a local model  $\pmb {u}_n(\cdot)$  and its local dataset  $\mathcal{D}_n$ , the quantization- assisted Gaussian mechanism is defined as

$$
\mathcal{M}_{\mathrm{Q}}(\pmb{u}_{n}(\cdot),\mathcal{D}_{n}) = \mathcal{Q}(\pmb{u}_{n}(\mathcal{D}_{n}) + \pmb{z}_{n}), \tag{22}
$$

where  $\mathbf{z}_n$  is the Gaussian noise added by client  $n$  to its local models before quantization, and its elements follow  $\mathbb{N}(0,\sigma_{\mathrm{DP}}^2)$

By evaluating the probability distribution of  $\mathcal{M}_{\mathrm{Q}}$  for any quantization level and its Max Divergence, we establish the upper bound of the privacy loss (i.e.,  $\epsilon$  and  $\delta$ ) for the quantization- assisted Gaussian mechanism in Theorem 1.

Theorem 1. Given the privacy budget  $\epsilon_{\mathrm{Q}}$ , the quantization- assisted Gaussian mechanism  $\mathcal{M}_{\mathrm{Q}}$  (22) satisfies  $(\epsilon_{\mathrm{Q}},\delta_{\mathrm{Q}})$ - DP:

$$
\delta_{\mathrm{Q}} = T_{0}\cdot \max \left\{\psi -\psi_{1}e^{\frac{\epsilon_{\mathrm{Q}}}{\tau_{0}}},\psi^{\prime} - \psi_{1}^{\prime}e^{\frac{\epsilon_{\mathrm{Q}}}{\tau_{0}}}\right\} , \tag{23}
$$

where, for conciseness,

$$
\begin{array}{rl} & {\psi = (1 - q)\psi_{1} + q\left(1 - 2Q\left(\frac{E_{\mathrm{L}}^{\mathrm{max}}}{\sigma_{\mathrm{DP}}}\right)\right);}\\ & {\psi_{1} = Q\left(\frac{2C + 3\sigma_{\mathrm{DP}} - E_{\mathrm{L}}^{\mathrm{max}}}{\sigma_{\mathrm{DP}}}\right) - Q\left(\frac{2C + 3\sigma_{\mathrm{DP}} + E_{\mathrm{L}}^{\mathrm{max}}}{\sigma_{\mathrm{DP}}}\right);} \end{array} \tag{24b}
$$

![](images/d840247890c95c495682f34f6ee12739355c6cbbd2891d215c7446111832e314.jpg)  
Fig. 1. The timeline of WPFL in the  $t$ -th round.

$$
\begin{array}{r}\psi^{\prime} = (1 - q)\psi_{1} + qQ\left(\frac{3\sigma_{\mathrm{DP}} - E_{\mathrm{L}}^{\mathrm{max}}}{\sigma_{\mathrm{DP}}}\right);\\ \psi_{1}^{\prime} = Q\left(\frac{2C + 3\sigma_{\mathrm{DP}} - E_{\mathrm{L}}^{\mathrm{max}}}{\sigma_{\mathrm{DP}}}\right), \end{array} \tag{24d}
$$

where  $q$  is the mini- batch sampling rate, and  $T_{0}$  is the maximum number of rounds in which each client can upload its FL local model due to privacy concerns.

# Proof. See Appendix A.

When  $\delta_{\mathrm{Q}} = 0$ $\mathcal{M}_{\mathrm{Q}}$  satisfies  $T_{0}\max \{\ln \frac{\psi}{\psi_{1}},\ln \frac{\psi^{\prime}}{\psi_{1}^{\prime}}\} - \mathrm{DP},$  which can be readily proved by substituting  $\delta_{\mathrm{Q}} = 0$  in (23) and obtaining  $\epsilon_{\mathrm{Q}} = T_{0}\max \{\ln \frac{\psi}{\psi_{1}},\ln \frac{\psi^{\prime}}{\psi_{1}^{\prime}}\}$

By plugging (24a) and (24c) into (23), we have

$$
\begin{array}{r}\delta_{\mathrm{Q}} = T_{0}\left[\psi_{1}\left(1 - q - e^{\frac{\epsilon_{\mathrm{Q}}}{\sigma_{0}}}\right) + q\left(1 - 2Q\left(\frac{E_{\mathrm{L}}^{\mathrm{max}}}{\sigma_{\mathrm{DP}}}\right)\right)\right]\\ \alpha \delta_{\mathrm{Q}} = T_{0}\left[\psi_{1}^{\prime}\left(1 - q - \frac{\epsilon_{\mathrm{Q}}}{\sigma_{0}}\right) + qQ\left(\frac{3\sigma_{\mathrm{DP}} - E_{\mathrm{L}}^{\mathrm{max}}}{\sigma_{\mathrm{DP}}}\right)\right] \end{array}
$$

With (24b) and (24d),  $\delta_{\mathrm{Q}}$  decreases with the increase of  $\sigma_{\mathrm{DP}}$  because both  $1 - 2Q\left(\frac{\epsilon_{\mathrm{max}}}{\sigma_{\mathrm{DP}}}\right)$  and  $Q\left(\frac{\sigma_{\mathrm{DP}} - E_{\mathrm{L}}^{\mathrm{max}}}{\sigma_{\mathrm{DP}}}\right)$  decrease with the increase of  $\sigma_{\mathrm{DP}}$  (with  $\psi_{1}$  and  $\psi_{1}^{\prime \prime}$  close to zero). The privacy budget  $\epsilon_{\mathrm{Q}}$  the number of quantization bits  $R$  and the clipping threshold  $C$  are determined based on the specific requirements of the applications and the structure of models. For applications where model performance is critical (e.g., autonomous vehicles or industrial systems),  $R$  should be chosen for small communication overhead while maintaining an acceptable level of accuracy. The requirement of privacy can be relaxed. For applications where privacy protection is prioritized (e.g., healthcare or financial systems),  $\epsilon_{\mathrm{Q}}$  and  $\delta_{\mathrm{Q}}$  should be small.  $C$  can be chosen adaptively based on the model type to ensure accuracy. Given the maximum number of uploading rounds for each client  $T_{0}$ , the clipping threshold  $C$  and the number of quantization bits  $R$ ,  $\sigma_{\mathrm{DP}}$  can be obtained through a one- dimensional search to satisfy the required privacy budget (i.e.,  $\epsilon_{\mathrm{Q}}$  and  $\delta_{\mathrm{Q}}$ ).

# V. CONVERGENCE ANALYSIS OF WPFL

This section analyzes the convergence of WPFL. Specifically, we first establish the convergence upper bound of the FL global model with the consideration of DP (see Section V- A), followed by analyzing the per- round convergence of PL (see Section V- B). Finally, the overall convergence of PL is attained (see Section V- C). This starts with the following assumptions.

Assumption 1.  $\forall n\in \mathcal{N}$

# Algorithm 1 WPFL with Privacy Protection

Input:  $T_{0}$ $t = 1$ $\{t_{n} = 0\}_{n\in \mathcal{N}}$ $\lambda_n^0$ $\omega^0$ $\{\pi_n^0\}_{n\in \mathcal{N}}$ $N$ $\eta_{\mathrm{F},n}^{0}$ $\eta_{\mathrm{P},n}^{0}$ $\sigma_{\mathrm{DP}}$  Output:  $\omega^{T}$ $\{\pi_n^T\}_{n\in \mathcal{N}}$  1: while  $\{n|t_n\leq T_0,n\in \mathcal{N}\} \neq \emptyset$  do 2: for  $n\in \mathcal{N}_t$  do 3: // Local training process for the FL model 4: Receive the FL global model  $\hat{\omega}_{n,\mathrm{G}}^t$  5: Update the FL local model  $\mathbf{u}_n^t$  by (20a); 6: Clip the local model by (2); 7: Perturb, quantize, and upload the FL local models: 8:  $\hat{\mathbf{u}}_n^t = \mathbf{u}_n^t +\mathbf{z}_n^t +\mathbf{E}_{n,\mathrm{L}}^t$  9: // Local training process for the  $PL$  model 10: Update the PL model  $\pi_n^t$  by (20b); 11:  $t_n\gets t_n + 1$  12: end for 13: // FL model aggregation 14: Update the FL global model  $\hat{\omega}_{\mathrm{L}}^{t}$  based on the received local models  $\hat{\omega}_{n}^{t}(\forall n\in \mathcal{N}_{t})$ $\begin{array}{r}\hat{\omega}_{\mathrm{L}}^{t} = \frac{1}{|\mathcal{N}_{t}|}\sum_{n\in \mathcal{N}_{t}}\hat{\omega}_{n}^{t} \end{array}$  15: Quantize the FL global model:  $\hat{\omega}_{\mathrm{G}}^{t} = \hat{\omega}_{\mathrm{L}}^{t} + \hat{\mathbf{E}}_{\mathrm{G}}^{t}$  16:  $t\gets t + 1$

17: end while

The local loss function of each client  $n$  i.e.  $F_{n}(\cdot)$  is  $\mu$  - strongly convex [41] and  $L$  - smooth [42], i.e.,  $F(\omega) - F(\omega^{*})\leq \frac{1}{2\mu}\| \nabla F(\omega)\|^{2}$  and  $\| \nabla F(\omega) - \nabla F(\omega^{\prime})\| \leq$ $L\parallel \omega - \omega^{2}\parallel$  .Here,  $\mu$  and  $L$  are constants [22]; The expectation of stochastic gradients is uniformly bounded at each client  $n$  and each round  $t$  i.e.,  $\mathbb{E}\left[\| \nabla F_n(\omega^t)\| ^2\right]\leq G_0^2 [I]$  The  $L_{2}$  - norm between the optimal  $FL$  local models and the optimal  $FL$  global model is bounded, i.e.,  $\| \mathbf{u}_n^* -$ $\omega^{*}\| \leq M$  where  $M$  is a constant [1].

# A. Convergence of FL

The convergence upper bound of the FL global model with DP is established under Assumption 1, as follows.

Lemma 1. Given the  $FL$  learning rate  $\eta_{\mathrm{F},n}^{t}$  of client  $n$  under Assumption 1, the expected difference between the  $FL$  global model  $\hat{\omega}_{\mathrm{L}}^{t}$  aggregated at the server and the optimal  $FL$  global model  $\omega^{*}$  at the  $t$ - th round is upper- bounded by

$$
\begin{array}{rl} & {\mathbb{E}\left[\| \hat{\omega}_{\mathrm{L}}^{t} - \omega^{*}\|^{2}\right]\leq \left(1 + \frac{1}{\phi_{1}} +\frac{1}{\phi_{2}}\right)\left[\Theta_{\mathrm{L}}^{t} + |\omega |\left(\sigma_{\mathrm{DP}}^{2} + (E_{\mathrm{L}}^{\mathrm{max}})^{2}\right)\right]}\\ & {+\frac{1}{|\mathcal{N}_{t}|}\sum_{n\in \mathcal{N}_{t}}\left[(1 + \phi_{2}) + (1 + \phi_{1})L^{2}(\eta_{\mathrm{F},n}^{t})^{\frac{2}{2}}\mu \eta_{\mathrm{F},n}^{t}\right]\mathbb{E}\left[\| \hat{\omega}_{n,\mathrm{G}}^{t} - \omega^{*}\|^{2}\right],} \end{array} \tag{26}
$$

where  $\phi_{1} > 0$  and  $\phi_{2} > 0$  can be any positive constants, and  $\Theta_{\mathrm{L}}^{t} = \frac{2C^{2} + (2 - \beta_{\mathrm{L}}^{2})|\omega|(C + 3\sigma_{\mathrm{DP}})^{2} - |\omega|\sigma_{\mathrm{DP}}^{2}}{|N_{t}|}\sum_{n\in \mathcal{N}_{t}}\rho_{n,\mathrm{L}}^{t}.$

Proof. See Appendix B.

Theorem 2. Given the  $FL$  local learning rate  $\eta_{\mathrm{F},n}^{t}$  of client  $n$  under Assumption 1, the expected difference between the  $FL$  global model  $\hat{\omega}_{n,\mathrm{G}}^{t + 1}$  received by client  $n$  at the  $(t + 1)$ - th round and the optimal  $FL$  global model  $\omega^{*}$  is upper- bounded:

$$
\mathbb{E}\left[\| \hat{\omega}_{\mathrm{n,G}}^{t + 1} - \omega^{*}\|^{2}\right]\leq \frac{1}{|\mathcal{N}_{t}|}\sum_{n\in \mathcal{N}_{t}^{*}}\epsilon_{\mathrm{F},n}^{t}\mathbb{E}\left[\| \hat{\omega}_{\mathrm{n,G}}^{t} - \omega^{*}\|^{2}\right] + \Gamma_{t + 1} \tag{27a}
$$

$$
\begin{array}{r}\leq (\epsilon_{\mathrm{F}}^{\mathrm{max}})^{t + 1}\mathbb{E}\big[\| \hat{\omega}_{n,\mathrm{G}}^{0} - \omega \| ^2\big] + \frac{(\epsilon_{\mathrm{F}}^{\mathrm{max}})^{t + 1} - 1}{\epsilon_{\mathrm{F}}^{\mathrm{max}} - 1}\Gamma^{\mathrm{max}}, \end{array} \tag{27b}
$$

where, for brevity, we define

$$
\begin{array}{r l} & {\Gamma_{t + 1}\triangleq h_{1}(\rho_{n,\mathrm{G}}^{t + 1})\Theta_{\mathrm{L}}^{t + 1} + \Gamma_{0}\rho_{n,\mathrm{G}}^{t + 1} + \Gamma_{1},}\\ & {\epsilon_{\mathrm{F},n}^{t}\triangleq (1 + \phi_{1})\big((1 + \phi_{2}) + (1 + \phi_{1})L^{2}\big(\eta_{\mathrm{F},n}^{t}\big)^{2} - \mu \eta_{\mathrm{F},n}^{t}\big),}\\ & {h_{1}(\rho_{n,\mathrm{G}}^{t + 1})\triangleq 2\bigg(1 + \frac{1}{\phi_{1}}\bigg)(1 + \phi_{2})\rho_{n,\mathrm{G}}^{t + 1} + (1 + \phi_{1})\bigg(1 + \frac{1}{\phi_{1}} +\frac{1}{\phi_{2}}\bigg),}\\ & {\Gamma_{0}\triangleq \bigg(1 + \frac{1}{\phi_{1}}\bigg)\bigg(2\bigg(1 + \frac{1}{\phi_{2}}\bigg)C^{2} + 2|\omega |\left(1 + \phi_{2}\right)\cdot}\\ & {\qquad \bigg(\sigma_{\mathrm{DP}}^{2} + (E_{\mathrm{L}}^{\mathrm{max}})^{2}\bigg) + 2|\omega |\big(C^{2} - (E_{\mathrm{L}}^{\mathrm{max}})^{2}\big)\bigg),}\\ & {\Gamma_{1}\triangleq |\omega |\left(1 + \phi_{1}\right)\bigg(1 + \frac{1}{\phi_{1}} +\frac{1}{\phi_{2}}\bigg)\bigg(\sigma_{\mathrm{DP}}^{2} + (E_{\mathrm{L}}^{\mathrm{max}})^{2}\bigg)}\\ & {\qquad +2|\omega |\bigg(1 + \frac{1}{\phi_{1}}\bigg)(E_{\mathrm{G}}^{\mathrm{max}})^{2},} \end{array} \tag{28e}
$$

where  $\epsilon_{\mathrm{F}}^{\mathrm{max}}\in (0,1)$  and  $\Gamma^{\mathrm{max}}$  are the maxima of  $\epsilon_{\mathrm{F},n}^{t}$  and  $\Gamma_{t + 1}$  respectively;  $\phi_{1}$  and  $\phi_{2}$  are any positive constants.

# Proof. See Appendix C.

According to Theorem 2, the right- hand side (RHS) of (27a) increases with  $\rho_{n,\mathrm{G}}^{t + 1}\Theta_{\mathrm{L}}^{t}$ ,  $\sigma_{\mathrm{DP}}$ , and  $E_{\mathrm{L}}^{\mathrm{max}}$ . In other words, the DP noise, imperfect wireless channel, and quantization degrade the convergence of the FL global model.

# B. Per-Round Convergence of  $PL$

Next, we analyze the convergence of the PL models under Assumption 1, as follows.

Theorem 3. Given the PL learning rate  $\eta_{\mathrm{P},n}^{t + 1}$ , the FL local learning rate  $\eta_{\mathrm{F},n}^{t}$ , and the weighting coefficient  $\lambda_{n}^{t + 1}$  of client  $n$ , under Assumption 1, the expected difference between the PL model  $\tilde{\omega}_{n}^{t + 1}$  at the  $(t + 1)$ - th round and the optimal PL model  $\pi_{n}^{*}$  is upper- bounded by

$$
\mathbb{E}\left[\| \tilde{\omega}_n^{t + 1} - \overline{\omega}_n^*\| ^2\right]\leq \epsilon_{\mathrm{P},n}^{t + 1}\mathbb{E}\left[\| \tilde{\omega}_n^t -\overline{\omega}_n^*\| ^2\right] + \Phi_n^{t + 1}, \tag{29}
$$

where, for brevity, we define

$$
\begin{array}{rl} & {\epsilon_{\mathrm{P},n}^{t + 1}\triangleq 1 - \eta_{\mathrm{P},n}^{t + 1}\left((1 - \lambda_n^{t + 1} / 2)\mu +\lambda_n^{t + 1}\right) + (\eta_{\mathrm{P},n}^{t + 1})^2;}\\ & {\Psi_n^{t + 1}\triangleq \left((\eta_{\mathrm{P},n}^{t + 1})^2 +1\right)(\lambda_n^{t + 1})^2 +(\eta_{\mathrm{P},n}^{t + 1})^3 /\lambda_n^{t + 1};}\\ & {\Phi_n^{t + 1}\triangleq \left(1 + (\lambda_n^{t + 1})^3\right)(\eta_{\mathrm{P},n}^{t + 1})^2 G_n^{t + 1} + \Psi_n^{t + 1}\left(\Gamma_{t + 1}\right.}\\ & {\qquad \left. + \frac{(G_0^2 + M\mu)^2}{|\mathcal{N}_t|\mu^2}\sum_{n\in \mathcal{N}_t}\epsilon_{\mathrm{F},n}^t\right);}\\ & {G_n^{t + 1}\triangleq \left((1 - \lambda_n^{t + 1} / 2)G_0 + \lambda_n^{t + 1}(G_0 / \mu +M)\right)^2.} \end{array} \tag{30d}
$$

# Proof. See Appendix D.

It is revealed in Theorem 3 that the convergence of PL is degraded by the DP noise (i.e.,  $\sigma_{\mathrm{DP}}$ ), imperfect wireless channel (i.e.,  $\rho_{n,\mathrm{G}}^{t + 1}$  and  $\Theta_{\mathrm{L}}^{t}$ ), and quantization errors (i.e.,  $E_{\mathrm{L}}^{\mathrm{max}}$  and  $E_{\mathrm{G}}^{\mathrm{max}}$ ). Based on Theorems 1 and 3, the DP noise (i.e.,  $\sigma_{\mathrm{DP}}$ ), while ensuring the privacy budget, compromises the convergence of PL. The effect of the DP noise on the convergence of PL (i.e.,  $\Phi_{n}^{t + 1}$ ,  $\forall n\in \mathcal{N}$ ) depends on the uplink and downlink channel conditions of the selected clients (i.e.,

$\rho_{n,\mathrm{L}}^{t}$  and  $\rho_{n,\mathrm{G}}^{t + 1}$ ), and can vary among clients. This also degrades the min- max fairness of WPFL.

The impact of  $\lambda_{n}^{t + 1}$  and  $\eta_{\mathrm{P},n}^{t + 1}$  on the convergence of PL is intricate. In particular,  $\epsilon_{\mathrm{P},n}^{t + 1}$  decreases monotonically with  $\eta_{\mathrm{P},n}^{t + 1}\in (0,1)$  when  $\mu \geq 2$ , and first decreases and then increases when  $\mu < 2$ . Moreover,  $\epsilon_{\mathrm{P},n}^{t + 1}$  increases with  $\lambda_{n}^{t + 1}\in (0,2)$  when  $\mu >2$ , decreases when  $\mu < 2$ , and is unaffected by  $\lambda_{n}^{t + 1}$  when  $\mu = 2$ . Across the entire possible range of  $\mu$ ,  $\Psi_{n}^{t + 1}$  increases with  $\eta_{\mathrm{P},n}^{t + 1}$ , and first increases and then decreases with  $\lambda_{n}^{t + 1}$ .  $\Phi_{n}^{t + 1}$  increases with  $\eta_{\mathrm{P},n}^{t + 1}$ , but its dependence on  $\lambda_{n}^{t + 1}$  is more complex, subject to the values of  $M$ ,  $G_{0}$ ,  $\mu$ , and  $\eta_{\mathrm{L}}^{t}(\rho_{n,\mathrm{G}}^{t + 1})\Theta_{\mathrm{L}}^{t + 1} + \Gamma_{0}\rho_{n,\mathrm{G}}^{t + 1} + \Gamma_{1}$ , see (30c). Nevertheless, there is an opportunity to minimize the maximum of  $\Phi_{n}^{t + 1}$ ,  $\forall n\in \mathcal{N}$ , while keeping  $\epsilon_{\mathrm{P},n}^{t + 1}$  consistent across all clients by optimizing  $\mathbf{c}^{t}$ ,  $\mathbf{p}^{t}$ ,  $\eta_{\mathrm{F}}^{t}$ ,  $\eta_{\mathrm{P}}^{t + 1}$ , and  $\lambda^{t + 1}$ . This encourages the convergence of PL while maintaining performance fairness among the clients.

# C. Overall Convergence of  $PL$

Let  $T$  be the maximum number of communication rounds satisfying  $\{n|t_n\leq T_0,n\in \mathcal{N}\} \neq \emptyset$ . We analyze the overall convergence upper bound of PL under imperfect channels after  $T$  aggregation rounds, as follows.

Theorem 4. Under Assumption 1, the convergence upper bound of PL under imperfect channels after  $T$  aggregation rounds is given by

$$
\mathbb{E}\left[\left\| \tilde{\omega}_n^T -\overline{\omega}_n^*\right\| ^2\right]\leq (\epsilon_{\mathrm{P}}^{\mathrm{max}})^T\mathbb{E}\left[\left\| \overline{\omega}_n^0 -\overline{\omega}_n^*\right\| ^2\right] + \frac{(\epsilon_{\mathrm{P}}^{\mathrm{max}})^t - 1}{\epsilon_{\mathrm{P}}^{\mathrm{max}} - 1}\Phi^{\mathrm{max}}, \tag{31}
$$

where  $\epsilon_{\mathrm{P}}^{\mathrm{max}}\in (0,1)$  and  $\Phi^{\mathrm{max}}$  are the upper bounds of  $\epsilon_{\mathrm{P},n}^{t}$  and  $\Phi_{n}^{t}$ ,  $\forall n\in \mathcal{N}$ ,  $t = 0,1,\ldots ,T$ , respectively.

Proof. See Appendix E.

# VI. OPTIMAL CONFIGURATION AND SCHEDULING POLICY

To accelerate the convergence of WPFL in a fair fashion, this section minimizes the maximum per- round convergence upper bound of all PL models, as the channels and subsequently the device selections change randomly across rounds. This starts by formulating a min- max problem and converting it to a max- min problem (see Section VI- A). By revealing its nested structure, the max- min problem is solved first through client selection, channel allocation, and power control (see Section VI- B), followed by learning rate and weighting coefficient adjustment (see Section VI- C). The proposed algorithm and its complexity analysis are presented in Section VI- D.

# A. Problem Formulation

According to (29), the convergence upper bound is dominated by  $\Phi_{n}^{t + 1}$ , while the convergence rate is determined by  $\epsilon_{\mathrm{P},n}^{t + 1}$ . For this reason, we minimize  $\Phi_{n}^{t + 1}$ , while keeping  $\epsilon_{\mathrm{P},n}^{t + 1}$ ,  $\forall n\in \mathcal{N}$  consistent across the clients, as follows.

$$
\begin{array}{rl} & {\mathbf{P}:\underset {\mathbf{c}^{t},\mathbf{p}^{t},\eta_{\mathrm{F}}^{t},\eta_{\mathrm{P}}^{t + 1},\lambda^{t + 1}}{\min}\underset {n\in \mathcal{N}}{\max}\Phi_{n}^{t + 1}}\\ & {\mathbf{C}\mathbf{1}:\epsilon_{\mathrm{P},n}^{t + 1} = \epsilon_{\mathrm{P}}^{t + 1},\forall n\in \mathcal{N}.} \end{array} \tag{32b}
$$

$$
\begin{array}{rl} & {\mathbf{C2}:\sum_{k = 1}^{K}c_{n,k}^{t}\leq 1,\forall n\in \mathcal{N},}\\ & {\mathbf{C3}:\sum_{n = 1}^{N}c_{n,k}^{t}\leq 1,\forall k\in \mathcal{K},}\\ & {\mathbf{C4}:P_{n}^{t}\leq P_{n}^{\mathrm{th}},\forall n\in \mathcal{N},}\\ & {\mathbf{C5}:r_{n,k}^{t}\leq r_{\min},\forall k\in \mathcal{K},}\\ & {\mathbf{C6}:c_{n,k}^{t}\in \{0,1\} ,\forall n\in \mathcal{N},k\in \mathcal{K},}\\ & {\mathbf{C7}:\sum_{t = 1}^{t}\sum_{k = 1}^{K}c_{n,k}^{t^{\prime}}\leq T_{0},\forall n\in \mathcal{N},}\\ & {\mathbf{C8}:0< \lambda_{n}^{t + 1}< 2,\forall n\in \mathcal{N},}\\ & {\mathbf{C9}:0< \eta_{n,n}^{t + 1}< 1,\forall n\in \mathcal{N},}\\ & {\mathbf{C10}:0< \eta_{n,n}^{t} < 1,\forall n\in \mathcal{N},}\\ & {\mathbf{C11}:0< \epsilon_{n,n}^{t} < 1,\forall n\in \mathcal{N},} \end{array} \tag{32d}
$$

where  $\eta_{\mathrm{F}}^{t} = \{\eta_{\mathrm{F},1}^{t},\dots ,\eta_{\mathrm{F},N}^{t}\}$  collects the FL learning rates of the clients at the  $t$ - th round,  $\eta_{\mathrm{P}}^{t + 1} = \{\eta_{\mathrm{P},1}^{t + 1},\dots ,\eta_{\mathrm{P},N}^{t + 1}\}$  collects the PL learning rates of the clients at the  $(t + 1)$ - th round, and  $\lambda^{t + 1} = \{\lambda_{1}^{t + 1},\dots ,\lambda_{N}^{t + 1}\}$  collects the weighting coefficients of the clients at the  $(t + 1)$ - th round;  $\epsilon_{\mathrm{P}}^{t + 1}$  can differ between slots;  $P_{n}^{\mathrm{th}}$  is the maximum transmit power of client  $n$ .

Constraints C1 guarantee the consistent convergence rates among the clients. C2 and C3 specify that at most one subchannel is allocated to a client, and a subchannel is only allocated to one client. C4 indicates that the transmit power of client  $n$  is upper bound by  $P_{n}^{\mathrm{th}}$ . C5 indicates that the data rate of each client needs to exceed  $r_{\mathrm{min}}$ , to ensure that its transmission delay does not exceed the maximum transmission delay  $\tau_{\mathrm{max}}$ . C7 specifies that the number of rounds each client can participate in is no more than  $T_{0}$  to meet the privacy requirement. C6 and C8 - C11 are self- explanatory.

According to (28), (30), and  $\Theta_{\mathrm{L}}^{t} > 0$  in Lemma 1, the objectives  $\Phi_{n}^{t + 1}$ ,  $\forall n\in \mathcal{N}$ , increase monotonically with  $\Theta_{\mathrm{L}}^{t}$  which is consistent across all clients and only affected by the selected subchannels for the clients,  $\mathbf{c}^{t}$ , and the transmit powers of all clients,  $\mathbf{p}^{t}$ . The remaining terms in  $\Phi_{n}^{t + 1}$  are affected by the FL learning rate  $\eta_{\mathrm{F},n}^{t}$ , the PL learning rate  $\eta_{\mathrm{P},n}^{t}$  and the weighting coefficient  $\lambda_{n}^{t + 1}$ . In other words,  $\eta_{\mathrm{F},n}^{t}\in \eta_{\mathrm{F}}^{t}$ ,  $\eta_{\mathrm{P},n}^{t}\in \eta_{\mathrm{P}}^{t + 1}$  and  $\lambda_{n}^{t + 1}\in \lambda^{t + 1}$  are independent of each other, and have no impact on  $\Phi_{n'}^{t + 1}$ ,  $n'\neq n$ . This min- max problem can be converted into the following max- min problem:

$$
\begin{array}{rl}\mathbf{P1}: & \underset {n\in \mathcal{N}}{\max}\underset {\mathbf{c}^t,\mathbf{p}^t,\eta_{\mathbf{F}}^{t + 1},\mathbf{\lambda}_{n^{t + 1}}^t}{\min}\Phi_n^{t + 1},\quad \mathrm{s.t.}(32\mathbf{b}) - (32\mathbf{l}), \end{array} \tag{32b}
$$

which is a mixed integer program and is still challenging.

According to (28a) and (30c),  $\Theta_{\mathrm{L}}^{t}$  is consistent across all clients and depends only on client selection, channel allocation, and power control. Given  $\Theta_{\mathrm{L}}^{t}$ , for each client  $n$ , the second term on the RHS of (29),  $\Phi_{n}^{t}$ , only depends on the FL and PL learning rates  $\eta_{\mathrm{F},n}^{t}$  and  $\eta_{\mathrm{P},n}^{t + 1}$ , and the weighting coefficient  $\lambda_{n}^{t + 1}$ . Therefore, Problem P1 is a nested optimization problem and can be equivalently decoupled into two subproblems solved sequentially. The first subproblem minimizes  $\Theta_{\mathrm{L}}^{t}$  through client selection, channel allocation, and power control. Given  $\Theta_{\mathrm{L}}^{t}$ , the second subproblem maximizes the minimum of  $\Phi_{n}^{t + 1}$ , through the learning rate and weighting coefficient adjustment for optimizing  $\Phi_{n}^{t + 1}$  for any client  $n$ , followed by taking the maximum  $\Phi_{n}^{t + 1}$  among all clients.

# B. Client Selection, Channel Allocation, and Power Control

We first minimize  $\Phi_{n}^{t}$  per client  $n\in \mathcal{N}$  by minimizing  $\Theta_{\mathrm{L}}^{t}$ :

$$
\mathbf{P2}:\quad \min_{\mathbf{c}^t,\mathbf{p}^t}\Theta_{\mathrm{L}}^t,\quad \mathrm{s.t.}(32\mathbf{c}) - (32\mathbf{h}).
$$

Clearly, the data rate  $r_{n,k}^{t}$  increases monotonically with  $P_{n}^{t}\leq P_{n}^{\mathrm{th}}$ , while the element error probability  $\rho_{n,\mathrm{L}}^{t}$  decreases. Therefore, the optimal transmit power of client  $n\in \mathcal{N}_t$  is  $P_{n}^{t} = P_{n}^{\mathrm{th}}$ . Let  $\Upsilon_{n}^{t}$  denote the number of rounds that client  $n$  is selected for uploading before round  $t$ , and  $\mathcal{N}_t^{\mathrm{a}}$  collect the clients allowed to upload local models at round  $t$ , i.e.,  $\mathcal{N}_t^{\mathrm{a}} = \{n|n\in \mathcal{N},\Upsilon_n^t < T_0\}$ . Problem P2 can be rewritten as

$$
\begin{array}{rl}\mathbf{P3}: & \underset {\mathbf{c}^t}{\min}\sum_{n\in \mathcal{N}}\rho_{n,\mathrm{L}}^t,\\ \mathrm{s.t.} & \sum_{k = 1}^{K}c_{n,k}^t\leq 1,\forall n\in \mathcal{N}_t^{\mathrm{a}},\\ & \sum_{n = 1}^{N}c_{n,k}^t\leq 1,\forall k\in \mathcal{K},\\ & c_{n,k}^t\in \{0,1\} ,\forall n\in \mathcal{N}_t^{\mathrm{a}},k\in \mathcal{K},\\ & (32\mathrm{f}). \end{array} \tag{33a}
$$

Problem P3 can be interpreted as a maximum- weight matching problem in bipartite graphs, which can be optimally solved using the Kuhn- Munkres (KM) algorithm [43]. At round  $t$ , the minimum of  $\Theta_{\mathrm{L}}^{t}$ , denoted as  $\Theta_{\mathrm{L},\mathrm{min}}^{t}$ , is obtained with the optimal client selection, channel allocation, and power control.

# C. Learning Rate and Weighting Coefficient Adjustment

Given  $\Theta_{\mathrm{L},\mathrm{min}}^{t}$ ,  $\Phi_{n}^{t + 1}$  is rewritten as

$$
\Phi_{n}^{t + 1} = \left(1 + (\lambda_{n}^{t + 1})^{3}\right)(\eta_{\mathrm{P},n}^{t + 1})^{2}G_{n}^{t + 1} + \Psi_{n}^{t + 1}\left(\Gamma_{2}\rho_{n,\mathrm{G}}^{t + 1} + \Gamma_{3} + \frac{(G_{n}^{2} + M\mu)^{2}}{|\mathcal{N}_{t}|\mu^{2}}\sum_{n\in \mathcal{N}_{t}}\epsilon_{\mathrm{F},n}^{t}\right), \tag{34}
$$

where, for the brevity of notation,  $\Gamma_{2}$  and  $\Gamma_{3}$  are defined as

$$
\begin{array}{l}\Gamma_2\triangleq 2\left(1 + \frac{1}{\phi_1}\right)\left(1 + \phi_2\right)\Theta_{\mathrm{L,min}}^t +\Gamma_0;\\ \Gamma_3\triangleq \left(1 + \phi_1\right)\left(1 + \frac{1}{\phi_1} +\frac{1}{\phi_2}\right)\Theta_{\mathrm{L,min}}^t +\Gamma_1. \end{array} \tag{35b}
$$

According to (34),  $\Phi_{n}^{t + 1}$  increases with  $\sum_{n\in \mathcal{N}}\epsilon_{\mathrm{F},n}^{t}$ , where the latter depends only on  $\eta_{\mathrm{F},n}^{t}$ ,  $\forall n\in \mathcal{N}$  and is consistent among the clients. For any client  $n$ ,  $\Phi_{n}^{t + 1}$  is independent of  $\eta_{\mathrm{P},n}^{t + 1}\backslash \eta_{\mathrm{P},n}^{t + 1}$  and  $\lambda^{t + 1}\backslash \lambda_{n}^{t + 1}$ . The learning rates and weighting coefficient of client  $n$  can be optimized at the server by solving

$$
\begin{array}{rl} & {\mathbf{P4}:\underset {\lambda_n^{t + 1},\eta_{\mathrm{P},n}^{t + 1},\eta_{\mathrm{F}}^t}{\min}\Phi_n^{t + 1}}\\ & {\mathrm{s.t.}\quad \epsilon_{\mathrm{F},n}^{t + 1} = \epsilon_{\mathrm{F}}^{t + 1},}\\ & {0< \lambda_n^{t + 1}< 2,}\\ & {0< \eta_{\mathrm{P},n}^{t + 1}< 1,}\\ & {(32\mathrm{k}),(32\mathrm{l}).} \end{array} \tag{36b}
$$

Since (32k) and (32l) are independent of  $\eta_{\mathrm{P},n}^{t + 1}$  and  $\lambda_{n}^{t + 1}$  while  $\eta_{\mathrm{F}}^{t}$  impacts  $\Phi_{n}^{t + 1}$  through  $\sum_{n\in \mathcal{N}}\epsilon_{\mathrm{P},n}^{t}$ , we can solve Problem P4 in two steps. We first determine the optimal FL local learning rate  $\eta_{\mathrm{F},n}^{t}$ , followed by the PL learning rate  $\eta_{\mathrm{P},n}^{t + 1}$  and the weighting coefficient  $\lambda_{n}^{t + 1}$ .

1) FL Local Learning Rate: To minimize  $\Phi_n^{t + 1}$ , we optimize the FL local learning rates  $\eta_{\mathrm{F}}^{t}$  to minimize  $\sum_{n\in \mathcal{N}}\epsilon_{\mathrm{F},n}^{t + 1}$ :

$$
\begin{array}{r}\min_{\eta_{\mathrm{F}}^t}\sum_{n\in \mathcal{N}}\epsilon_{\mathrm{F},n}^t,\quad \mathrm{s.t.}(32\mathbf{k}),(32\mathbf{l}). \end{array}
$$

According to (28b), Problem P5 can be solved by setting  $\eta_{\mathrm{F},n}^{t} = \arg \min_{\eta_{\mathrm{F},n}^{t}\in (0,1)}\epsilon_{\mathrm{F},n}^{t}$ , which is consistent among the clients and rounds.  $\eta_{\mathrm{F},n}^{t} = \frac{\mu}{2(1 + \phi)^{2}}$ . Under properly designed  $\phi_{1}$ ,  $\phi_{2}$ , and  $\phi_{1}$ ,  $\min_{\eta_{\mathrm{F}}^{t}}\epsilon_{\mathrm{F},n}^{t}\in (0,1)$ .

2) PL learning Rate and Weighting Coefficient: Given  $\epsilon_{\mathrm{P}}^{t + 1}$  and  $\epsilon_{\mathrm{P}}^{t}$ ,  $\forall n\in \mathcal{N}$ , Problem P4 can be rewritten as

$$
\min_{\lambda_{n}^{t + 1}\in (0,2),\eta_{\mathrm{P},n}^{t + 1}\in (0,1)}\Phi_{n}^{t + 1},\quad \mathrm{s.t.}(36\mathbf{b}),(36\mathbf{c}),(36\mathbf{d}).
$$

Problem  $\mathbf{P6}$  is a non- convex problem because the equality constraint (36b) is not an affine function; i.e., the feasible set is not a convex set. Nevertheless, we can write  $\lambda_{n}^{t + 1} = \lambda_{n}^{t + 1}(\eta_{\mathrm{P},n}^{t + 1})$  and specify the convex/concave region of Problem P6 in  $\eta_{\mathrm{P},n}^{t + 1}$ . Specifically, according to (36b),

$$
\lambda_{n}^{t + 1} = \left(1 - \frac{\mu}{2}\right)^{-1}\left((1 - \epsilon_{\mathrm{P}}^{t + 1}) / \eta_{\mathrm{P},n}^{t + 1} + \eta_{\mathrm{P},n}^{t + 1} - \mu\right). \tag{37}
$$

Then, we can convert Problem P6 to an unconstrained problem about  $\eta_{\mathrm{P},n}^{t + 1}$  under the typical situation with  $\mu < 2$  (as empirically measured in our experiments described in Section VII).

We notice from (37) that, if  $0< \epsilon_{\mathrm{P}}^{t + 1}< 1 - \frac{\mu^2}{4}$ , then  $\lambda_{n}^{t + 1} > 0$ ; in other words, the regularization term on the RHS of (9) can never be suppressed, even when the FL model  $\omega_{\mathrm{C}}^{t}$  deviates dramatically from an individual PL model  $\tilde{\omega}_{n}^{t + 1}$ , penalizing personalization. For this reason, we design  $\epsilon_{\mathrm{P}}^{t + 1}\geq 1 - \frac{\mu^2}{4}$ , in which case  $\lambda_{n}^{t + 1} = \lambda_{n}^{t + 1}(\eta_{\mathrm{P},n}^{t + 1})$  intersects with  $\lambda_{n}^{t + 1} = 0$ , i.e., at  $(\eta_{2},0)$  and  $(\eta_{3},0)$ . By solving  $\lambda_{n}^{t + 1}(\eta_{\mathrm{P},n}^{t + 1}) = 0$ , the feasible set of  $\eta_{\mathrm{P},n}^{t + 1}$  can be written as  $\Omega_{0}^{t + 1}\cup \Omega_{1}^{t + 1}$ , with

$$
\begin{array}{rl} & {\Omega_{0}^{t + 1} = \{\eta_{\mathrm{P},n}^{t + 1}|\eta_{1}< \eta_{\mathrm{P},n}^{t + 1}< \eta_{2}\} ;}\\ & {\Omega_{1}^{t + 1} = \left\{ \begin{array}{ll}\{\eta_{\mathrm{P},n}^{t + 1}|\eta_{3}< \eta_{\mathrm{P},n}^{t + 1}< 1\} , & \mathrm{if} \epsilon_{\mathrm{P}}^{t + 1}\leq 2 - \mu ;\\ \emptyset , & \mathrm{if} \epsilon_{\mathrm{P}}^{t + 1} > 2 - \mu , \end{array} \right.} \end{array} \tag{38b}
$$

where  $\eta_{1} = 1 - \sqrt{\epsilon_{\mathrm{P}}^{t + 1}} < 1$  is the  $x$ - coordinate of the intersection of  $\lambda_{n}^{t + 1} = \lambda_{n}^{t + 1}(\eta_{\mathrm{P},n}^{t + 1})$  and  $\lambda = 2$ ,  $\eta_{2} = \frac{\mu - \sqrt{\mu^{2} - 4(1 - \epsilon_{\mathrm{P}}^{t + 1})}}{2}$ , and  $\eta_{3} = \frac{\mu + \sqrt{\mu^{2} - 4(1 - \epsilon_{\mathrm{P}}^{t + 1})}}{2}$ .

Now, constraints (36b), (36c), and (36d) are fulfilled in the feasible set specified by (38). Problem P6 can be rewritten as

$$
\mathbf{P7}:\min_{\eta_{\mathrm{P},n}^{t + 1}\in \Omega_{0}^{t + 1}\cup \Omega_{1}^{t + 1}}\Phi_{n}^{t + 1}, \tag{39}
$$

which is an unconstrained optimization problem about  $\eta_{\mathrm{P},n}^{t + 1}$  with a nonconvex feasible set.

Theorem 5. When  $\epsilon_{\mathrm{P}}^{t + 1}\in [1 - \frac{\mu^2}{4},2 - \mu ]$ ,  $\Phi_{n}^{t + 1}$  is convex in both  $\eta_{\mathrm{P},n}^{t + 1}\in \Omega_{0}^{t + 1}$  and  $\eta_{\mathrm{P},n}^{t + 1}\in \Omega_{1}^{t + 1}$ . When  $\epsilon_{\mathrm{P}}^{t + 1}\in (2 - \mu ,1)$  with  $\mu \in (1,2)$ ,  $\Phi_{n}^{t + 1}$  is convex in  $\eta_{\mathrm{P},n}^{t + 1}\in \Omega_{0}^{t + 1}$ .

Proof. See Appendix F.

By Theorem 5, Problem P7 is convex in  $\Omega_{0}^{t + 1}$  and  $\Omega_{1}^{t + 1}$  when they are non- empty, under  $\mu < 2$  and  $\epsilon_{\mathrm{P}}^{t + 1}\geq 1 - \frac{\mu^2}{4}$ . When  $\epsilon_{\mathrm{P}}^{t + 1}\in [1 - \frac{\mu^2}{4},2 - \mu ]$ , the optimal  $\eta_{\mathrm{P}}^{t + 1,*}$  can be obtained by comparing the respective solutions in  $\Omega_0^{t + 1}$  and  $\Omega_1^{t + 1}$ . When  $\epsilon_{\mathrm{P}}^{t + 1}\in (2 - \mu ,1)$  (i.e.,  $1< \mu < 2$ ), Problem P7 is convex. The solutions can be obtained using convex optimization tools. After minimizing  $\Phi_{n}^{t + 1}$  for every client  $n\in \mathcal{N}$ , the maximization problem (i.e.,  $\max_{n\in \mathcal{N}}\Phi_n^{t + 1}$ ) is solved by comparing the minimized  $\Phi_{n}^{t + 1}$  among all clients.

# D. Algorithm Description and Discussion

The overall algorithm is summarized in Algorithm 2. The complexity of the algorithm is dominated by the KM algorithm used to solve Problem P3 and the convex optimization used to solve Problem P7. The worst- case complexity of using the KM algorithm to solve Problem P3 is  $\mathcal{O}(|N|^3)$  [44], as  $N$  specifies the number of vertices in the bipartite graph.

According to Theorem 5, in the case where Problem P7 is convex in the feasible sets  $\Omega_{0}^{t + 1}$  and  $\Omega_{1}^{t + 1}$  or is convex in the feasible set  $\Omega_{0}^{t + 1}$ , the algorithm converges to the global optimum. The complexity of using a typical convex optimization solver, e.g., interior point method, to solve Problem P7 is  $\mathcal{O}(V_{1}^{4.5}\log (\frac{1}{\alpha}))$  [45], where  $V_{1}$  is the number of variables and  $\alpha$  is the convergence accuracy. Here,  $V_{1} = 1$ . The problem is solved for  $N$  clients in parallel. As a result, the overall complexity of Algorithm 2 is  $\mathcal{O}(N^{3} + N\log (\frac{1}{\alpha}))$ .

Under  $\epsilon_{\mathrm{P}}^{t + 1}\in [1 - \frac{\mu^2}{4},1)$ , we confirm the optimality of the solution to Problem P1. Due to the consistency of  $\Theta_{\mathrm{L}}^{t}$  across the clients and the independence of  $\Phi_{n}^{t}$  under given  $\Theta_{\mathrm{L}}^{t}$ ,  $\epsilon_{\mathrm{P}}^{t + 1}$ , and  $\eta_{\mathrm{P}}^{t}$ , Problem P1 is a nested problem and decoupled into two subproblems. The first subproblem, i.e., client selection, channel allocation, and power control, is optimally solved using the KM algorithm. The second subproblem, i.e., learning rate and weighting coefficient adjustment, is further divided between the FL local learning rate adjustment, and the PL learning rate and weighting coefficient adjustment (i.e., Problem P6). Under  $\epsilon_{\mathrm{P}}^{t + 1}\in [1 - \frac{\mu^2}{4},1)$ , Problem P6 is convex within the specified convex region of  $\eta_{\mathrm{P},n}^{t + 1}$ , optimally solved through convex optimization methods. The solution to Problem P1 is optimal, under the specified  $\eta_{\mathrm{F}}^{t}$ ,  $\eta_{\mathrm{P}}^{t + 1}$ , and  $\lambda^{t + 1}$ .

According to (27a) and (28a), the per- round convergence upper bound of the FL global model depends on  $\Gamma_{t + 1}$  and  $\sum_{n\in \mathcal{N}}\epsilon_{\mathrm{F},n}^{t}$ , which are minimized by solving Problems P2 and P5, respectively. In this sense, through client selection, channel allocation, power control, and FL local learning rate configuration, the per- round convergence upper bound and subsequently the overall convergence upper bound of the FL global model are minimized.

# VII. EXPERIMENTS AND RESULTS

Consider  $M = 20$  clients under the coverage of a BS with a coverage radius of  $100\mathrm{m}$ . The distances between the BS and the clients are uniformly randomly taken from  $[10,100]\mathrm{m}$ . We consider Rayleigh fading for both uplink and downlink channels. The total bandwidth is  $K\cdot B = 10\mathrm{MHz}$  with  $K = 10$  by default. Three network models are considered here:

- MLR: This classification method generalizes logistic regression to multiclass problems. It constructs a linear predictor function to predict the probability of an outcome based on an input observation.

# Algorithm 2 Proposed Transmission Scheduling Policy

Input:  $T_{0}$ $\{t_{n} = 0\}_{n\in \mathcal{N}}$ $r_{\mathrm{min}}$ $\{P_{n}^{\mathrm{th}}\}_{n\in \mathcal{N}}$ $\epsilon_{\mathrm{P}}$ $h_0^0$  Output:  $\mathbf{c}^t$ $\mathbf{p}^t$ $\eta_{\mathrm{F}}^{t}$ $\eta_{\mathrm{P}}^{t + 1}$ $\lambda^{t + 1}$ $\forall t$  1: while  $\{n\mid t_n\leq T_0,n\in \mathcal{N}\} \neq \emptyset$  do 2: //Client selection, channel allocation, power control 3: Let  $P_{n}^{t} = P_{n}^{\mathrm{th}}$ $\forall n\in \mathcal{N}$  4: Obtain the set of candidate clients  $\mathcal{N}_t^{\mathrm{a}}$  and  $\{\rho_{n,\mathrm{L}}^t\}_{n\in \mathcal{N}}$  according to (12)- (14); 5: Obtain the optimal client selection  $\mathcal{N}_t$  and channel allocation  $\mathbf{C}^t$  by solving Problem P3 using KM algorithm; 6:  $t_n\gets t_n + 1$ $\forall n\in \mathcal{N}_t$  7: // Learning rate and weighting coefficient adjustment 8: Set  $\eta_{\mathrm{F},n}^{t} = \frac{\mu}{2(1 + \phi_{\mathrm{I}})L^{2}}$  and  $\epsilon_{\mathrm{P}}^{t + 1} = \epsilon_{\mathrm{P}}$  9: parfor  $n\in \mathcal{N}$  do 10: Obtain the optimal PL learning rate  $\eta_{\mathrm{P},n}^{t + 1,*}$  by solving Problem P7; 11: Obtain the optimal weighting coefficient  $\lambda_{n}^{t + 1,*}$  based on (37) and  $\eta_{\mathrm{F},n}^{t + 1,*}$  12: end parfor 13:  $t\gets t + 1$  14:end while

TABLEI SIMULATION PARAMETER CONFIGURATION  

<table><tr><td>Parameter</td><td>Value</td></tr><tr><td>Total bandwidth</td><td>10 MHz</td></tr><tr><td>BS&#x27;s maximum transmit power</td><td>30 dBm</td></tr><tr><td>client&#x27;s maximum transmit power Pth</td><td>23 dBm</td></tr><tr><td>Noise spectral density</td><td>-169 dBm/Hz</td></tr><tr><td>Path loss at 1 m</td><td>-30 dB</td></tr><tr><td>Path loss exponent</td><td>2.8</td></tr><tr><td>Modulation order ML</td><td>256</td></tr><tr><td>Sampling rate q</td><td>0.01</td></tr></table>

DNN: This model consists of an input layer, a fully connected hidden layer (with 100 neurons), and an output layer. The rectified linear unit (ReLU) activation function is applied to the hidden layer. CNN: This model contains two convolutional layers with 32 and 64 convolutional filters per layer, and a pooling layer in- between to prevent over- fitting. Following the convolutional layers are two fully connected layers (with 1024 and 512 neurons for FMNIST, and 1600 and 512 neurons for CIFAR10). We use the ReLU in the convolutional and fully connected layers.

The default FL and PL learning rates are  $\eta_{\mathrm{F},n}^{t} = 0.01$  and  $\eta_{\mathrm{P},n}^{t} = 0.01$  , respectively. The default PL learning rates are used as the initial PL learning rates when  $t = 0$  and the default PL and FL learning rates are used for the compared scheduling policy (i.e., Non- Adjustment). The clipping threshold is  $C = 3$  7and 20 for MLR,DNN,and CNN, respectively. The default privacy budget, the maximum number of transmissions per client, and the weighting coefficient for each client are  $\epsilon_{\mathrm{Q}} = 1$ $T_{0} = 20$  , and  $\lambda_{n}^{t} = 0.5$  . By default,  $\delta_{\mathrm{Q}} = 0.001$  for DNN and MLR, and  $\delta_{\mathrm{Q}} = 0.005$  for CNN. The corresponding values of  $\sigma_{\mathrm{DP}}$  are given in Table II. The maximum transmission delays are  $\tau_{\mathrm{max}} = 0.01$  0.1,and  $0.6\mathrm{s}$

TABLE II THE STANDARD DEVIATION OF THE DP NOISE,  $\sigma_{\mathrm{DP}}$  

<table><tr><td></td><td>T0= 5</td><td>T0= 10</td><td>T0= 15</td><td>T0= 20</td><td>T0= 25</td><td>T0= 30</td></tr><tr><td>MLR</td><td>0.001</td><td>0.003</td><td>0.005</td><td>0.006</td><td>0.008</td><td>0.01</td></tr><tr><td>DNN</td><td>0.004</td><td>0.008</td><td>0.012</td><td>0.016</td><td>0.02</td><td>0.024</td></tr><tr><td>CNN</td><td>0.0025</td><td>0.0045</td><td>0.007</td><td>0.009</td><td>0.012</td><td>0.014</td></tr></table>

For MLR, DNN, and CNN, respectively. Given a dataset and an ML model,  $L$  and  $\mu$  can be obtained by empirically estimating the minimum and maximum of  $\frac{\|\nabla F(\omega) - \nabla F(\omega')\|}{\|\omega - \omega'\|}$  [46].  $L$  is the maximum,  $\mu$  is the minimum.1

We consider three widely used public datasets, i.e., MNIST, Fashion- MNIST (FMNIST), and CIFAR10. Cross- entropy loss is considered for the datasets.

The following benchmarks are considered for WPFL:

pFedMe [10]: The global FL model is updated in the same way as the typical FL. Learning from the global model, each personalized model is updated based on a regularized loss function using the Moreau envelope. APPLE [13]: Each client uploads to the server a core model learned from its personalized model and downloads the other clients' core models in each round. The personalized model is obtained by locally aggregating the core models with learnable weights. FedAMP [12]: The server has a set of personalized cloud models. Each client has a local personalized model. In each round, the server updates the personalized cloud models using an attention- inducing function of the uploaded local models and combination weights. Upon receiving the cloud model, each client locally updates its personalized model based on a regularized loss function. FedALA [14]: In every round of FedALA, each client adaptively initializes its local model by aggregating the downloaded global model and the old local model with learned aggregation weights before local training.

For a fair comparison, all these benchmarks are enhanced with the proposed DP mechanism and scheduling policy.

To assess the proposed quantization- assisted Gaussian mechanism, we consider the following DP implementations and baselines:

Gaussian mechanism [22]: The FL local model is protected by Gaussian noise satisfying  $(\epsilon_{\mathrm{Q}},\delta_{\mathrm{Q}})$  - DP. The contribution of quantization to privacy is overlooked. Moments accountant (MA)- based DP mechanism [21]: Much tighter estimates on the privacy loss can be obtained through the MA technique and bisection. The FL local model is protected by Gaussian noise with a smaller standard deviation than the Gaussian mechanism [22]. This mechanism also overlooks the contribution of quantization to privacy. DP with dithering quantization [30]: To guarantee  $(\epsilon_{\mathrm{Q}},\delta_{\mathrm{Q}})$  - DP, the quantization intervals are determined

TABLE III THE AVERAGE NUMBERS OF QUANTIZATION BITS  $(B_{\mathrm{q}})$  AND OVERHEAD BITS  $(B_{\mathrm{o}})$  PER PARAMETER UNDER DIFFERENT DP IMPLEMENTIONS. NOTE THAT A CLIENT TRANSMITS  $\min (16,B_{\mathrm{q}} + B_{\mathrm{o}})$  BITS PER PARAMETER SINCE IT WOULD BE MORE EFFICIENT TO TRANSMIT ALL 16 QUANTIZATION BITS PER PARAMETER WHEN  $B_{\mathrm{q}} + B_{\mathrm{o}}\geq 16$  ; E.G., THE GAUSSIAN MECHANISM UNDER MLR.  

<table><tr><td rowspan="2">Model</td><td rowspan="2">Dataset</td><td colspan="2">Proposed</td><td colspan="2">MA</td><td colspan="2">Gaussian</td><td colspan="2">Dithering</td><td colspan="2">Without DP</td></tr><tr><td>Bq</td><td>Bo</td><td>Bq</td><td>Bo</td><td>Bq</td><td>Bo</td><td>Bq</td><td>Bo</td><td>Bq</td><td>Bo</td></tr><tr><td>MLR</td><td>MNIST</td><td>6.81</td><td>2.61</td><td>8.98</td><td>4.00</td><td>14.32</td><td>3.93</td><td>8.01</td><td>1.34</td><td>4.70</td><td>1.63</td></tr><tr><td>DNN</td><td>MNIST</td><td>4.55</td><td>3.04</td><td>11.13</td><td>4.00</td><td>14.82</td><td>2.10</td><td>6.91</td><td>1.20</td><td>2.54</td><td>0.72</td></tr><tr><td>CNN</td><td>IMNIST</td><td>5.17</td><td>4.00</td><td>10.95</td><td>4.00</td><td>14.79</td><td>1.09</td><td>5.59</td><td>4.00</td><td>3.88</td><td>3.85</td></tr><tr><td>CNN</td><td>CIFAR10</td><td>4.93</td><td>4.00</td><td>11.34</td><td>4.00</td><td>14.84</td><td>0.89</td><td>5.20</td><td>4.00</td><td>3.55</td><td>3.92</td></tr></table>

by sampling a set of gamma random variables for the coordinates of all clients at each slot. Each client's FL local model is quantized by adding uniform noise. With shared random seeds across clients, the server estimates the local models by subtracting the uniform noise.

Perfect Gaussian [22]: The PFL is executed in an ideal environment with no quantization conducted and no communication error undergone. The FL local model is protected by Gaussian noise that satisfies  $(\epsilon_{\mathrm{Q}},\delta_{\mathrm{Q}})$  DP. No quantization noises and transmission errors are considered. Comparing the proposed mechanism with this baseline helps isolate the impact of quantization and imperfect communication on the proposed mechanism.

WPFL without DP: The standard WPFL is conducted with no privacy considered, where quantization is conducted for uploading the FL local models and downloading the FL global models, and imperfect communication channels are undergone. Comparing the proposed mechanism with this baseline helps isolate the impact of DP on the proposed mechanism.

To assess the proposed scheduling policy, the following scheduling policies are considered for comparison:

Round- Robin: The BS selects the clients for the available subchannels in a round- robin manner with no adjustment of the coefficients. Random Selection: The clients are selected randomly with random subchannel allocation and fixed coefficients within all training rounds. Non- Adjustment: The BS selects the clients in each round utilizing the KM algorithm with fixed coefficients within all training rounds.

1) Communication overhead: Under the proposed mechanism, the Gaussian mechanism [37], and MA [21], we employ a 16-bit quantizer  $R = 16$  to ensure fine quantization intervals. Note that the significant bits of most weight parameters remain unused. Only the effectively utilized bits, along with the sign bit, need to be transmitted for each parameter. Moreover, multiple consecutive parameters may have the same number of effective bits. An index list is also sent to specify the count of consecutive parameters sharing the same number of effective bits and the number.

Under Dithering [30], the number of quantization bits per parameter can vary across clients. The average numbers of quantization bits are evaluated to be 16.05, 16.05, 16.25, and 16.00, for MLR on the MNIST dataset, DNN on the MNIST dataset, CNN on the FMNIST dataset, and CNN on the CIFAR10 dataset, respectively. Likewise, only the effective bits and the sign bit are transmitted, along with an index list specifying the count of consecutive parameters sharing the same number of effective bits and the number.

![](images/0d01531d72765b0aaab7609993604691d9dd26a60b61c3ba29e57b56f8ba0f33.jpg)  
c) Accuracy vs.  $T$  CNN,CIFAR10) d) Accuracy vs.  $T$  CNN, FMNIST) Fig.2. PL model accuracy vs. the maximum number of rounds  $T_{0}$  under different allocation methods, where the default setting is considered:  $\epsilon_{\mathrm{Q}} = 1$ $\delta_{\mathrm{Q}} = 0.001$  for DNN and MLR, and  $\delta_{\mathrm{Q}} = 0.005$  for CNN.

Table III shows the average numbers of quantization bits  $(B_{\mathrm{q}})$  and overhead bits  $(B_{\mathrm{o}})$  per parameter under different DP mechanisms, datasets, and models. Clearly, the consideration of DP increases the number of bits to be transmitted, because the DP noise extends the range of the model parameters. Among the schemes considering DP, the proposed mechanism generally requires the smallest number of bits to be transmitted, as the privacy- enhancing capability of quantization is exploited to help reduce the DP noise in the mechanism.

2) Comparison with existing DP mechanisms: Fig.2 evaluates the impact of different DP mechanisms (i.e., Gaussian [37], MA [21], DP with Dithering [30], and the proposed quantization-assisted Gaussian mechanism) on the accuracy of WPFL in a noisy wireless environment. The accuracy of WPFL under the proposed DP protection first increases and then decreases as  $T_{0}$  grows. This is because the effect of the DP perturbation and the quantization and transmission errors accumulates, and  $\sigma_{\mathrm{DP}}$  increases with  $T_{0}$  based on Theorem 1, causing performance degradation when  $T_{0}$  is large.

![](images/224287cea4d35176b8af944e9fc27cdc29109d7e1d92a99faf8b5237892b7adb.jpg)  
(c) Accuracy vs.  $T_{0}$  (CNN,CIFAR10) (d) Accuracy vs.  $T_{0}$  (CNN,FMNIST)

In the scenarios with quantization and imperfect communication channels, the proposed mechanism achieves at least  $5.00\%$  better accuracy than the second- best (i.e., Dithering) and is only  $9.26\%$  worse than the standard WPFL without DP. This is because our mechanism utilizes the inherent privacy- preserving ability of quantization, reducing the intensity of the added noise compared to the Gaussian and MA mechanisms. Although privacy is guaranteed using uniform noise addition and dithering quantization in Dithering, the quantization intervals can vary over time and differ among clients, depending on gamma random variables sampled in each round. The bit lengths can be large for clients with tiny intervals, causing high transmission errors under Dithering.

The proposed mechanism is  $20.76\%$  worse in test accuracy than Perfect Gaussian. This difference arises because Perfect Gaussian operates in an ideal environment where there is no quantization conducted and no communication errors undergone, and FL and PL model training is influenced solely by Gaussian noise.

To maintain acceptable performance of PL models in practice, a training process can be terminated once the accuracy stops improving or starts to degrade. Additionally, cross- validation can be employed to select  $T_{0}$  by evaluating performance across multiple subsets of the data. Note that the optimal  $T_{0}$  can be different under specific networks, datasets, and DP mechanisms. Fig. 2 provides guidance on adjusting  $T_{0}$  in different experimental setups.

3) Comparison with alternative allocation schemes: Figs. 3 and 4 plot the accuracy, fairness (i.e., Jain's fairness index), and maximum test loss of all participating clients among the clients of privacy-preserving WPFL. In addition to imperfect channels, we consider the situation of the proposed allocation and configuration scheme running in an error-free channel (i.e.,  $\rho_{n,\mathrm{L}}^{t} = 0$ ), which provides the best possible results of the proposed allocation and configuration scheme.

![](images/52ffcdb1adba2e11dcacf72eb0537adb93bec36c6c3ccb498eca3d65293d8b74.jpg)  
Fig. 4. Comparison of fairness and the maximum test loss of all participating clients between the benchmarks.  $\epsilon_{\mathrm{Q}} = 1$ $\delta_{\mathrm{Q}} = 0.001$  for DNN and MLR, and  $\delta_{\mathrm{Q}} = 0.005$  for CNN.

As shown in Figs. 3, 4(b), 4(d), 4(f), and 4(h), the proposed scheduling policy outperforms the other benchmarks, in accuracy and maximum test loss of all participating clients. Particularly, it is better than the second- best (including round- robin, random selection, and non- adjustment) by  $87.08\%$  in accuracy and  $16.21\%$  in the maximum test loss of participating clients under the CNN model, and  $52.26\%$  in accuracy and  $15.99\%$  in the maximum test loss under the DNN and MLR models, respectively. The proposed scheduling and configuration policy differs marginally from the best possible results achieved under an error- free channel. This is because our device selection and adaptive coefficient adjustment take into account time- varying

![](images/7bff177638cf05007c3204f016b2b79c85345c7cacb13173015009b3a5e1da4a.jpg)  
Fig. 5. Comparison of accuracy vs. aggregation time  $t$  between the benchmarks.  $\epsilon_{\mathrm{Q}} = 1$ .  $\delta_{\mathrm{Q}} = 0.001$  for DNN and MLR, and  $\delta_{\mathrm{Q}} = 0.005$  for CNN.

errors from DP, quantization, and transmission, and minimize the maximum of the convergence upper bound among the clients, reducing the impact from DP and noisy channels. The accuracy first increases and then decreases as  $T_{0}$  grows, and the maximum test loss of all participating clients first decreases and then increases for WPFL under the proposed policy running in either noisy or error- free channels, due to the accumulated impact of the DP noise.

Figs. 4(a), 4(c), 4(e), and 4(g) examine the fairness of WPFL through Jain's fairness index  $\mathcal{I} = \frac{(\sum_{n = 1}^{N}x_{n})^{2}}{n\sum_{n = 1}^{N}x_{n}^{2}}$ , with  $x_{n}$  being the training loss of client  $n$ . Under the CNN model, our approach is better than the second- best (i.e., Round- Robin) by  $38.37\%$  in fairness (Jain's index). Compared to the benchmarks (i.e., Round- Robin, Random, and Non- Adjustment), the proposed configuration and scheduling policy is substantially fairer, thanks to its consideration of the min- max fairness.

4) Comparison with state-of-the-art PFL: Fig. 5 plots the training accuracy against the increasing number  $t$  of global aggregations under  $T_{0} = 30$ , under the proposed WPFL and the benchmarks (i.e., FedAMP, pFedMe, APPLE, and FedALA). The proposed WPFL achieves at least  $11.30\%$  better accuracy than the benchmarks, due to the self-adaptive configuration and scheduling policy proposed under imperfect and noisy channels. Figs. 6 and 7 compare the accuracy, fairness (i.e., Jain's fairness index), and the maximum test loss of all participating clients between the proposed privacy-preserving WPFL framework and the PFL benchmarks (i.e., pFedMe, APPLE, FedAMP, and FedALA), under different  $T_{0}$  values, datasets, and models (i.e., DNN and MLR on the MNIST dataset, and CNN on the FMNIST and CIFAR10 datasets). All schemes are protected through our DP mechanism with the consistent default  $\epsilon_{\mathrm{Q}}$  and  $\delta_{\mathrm{Q}}$  values. The PFL benchmarks do not utilize the proposed configuration policy, due to their different training procedures and loss functions.

As shown in Figs. 6, 7(b), 7(d), 7(f), and 7(h), our proposed

![](images/cad432166b5091a7b928d19d3a43df2ca84f707dcef97af7702704769e66f179.jpg)  
Fig. 6. Comparison of testing accuracy between the benchmarks.  $\epsilon_{\mathrm{Q}} = 1$ .  $\delta_{\mathrm{Q}} = 0.001$  for DNN and MLR, and  $\delta_{\mathrm{Q}} = 0.005$  for CNN.

WPFL outperforms the benchmarks in accuracy and maximum test loss of all participating clients. Particularly, it is better than the second- best (i.e., FedAMP) by  $10.43\%$  in accuracy and  $8.16\%$  in maximum test loss. Generally, the accuracy and maximum test loss of all participating clients improve first, then degrade as  $T_{0}$  grows, especially for the DNN models on the MNIST dataset and the CNN model on the FMNIST and CIFAR10 datasets. Although the trade- off between personalization and generalization is considered for all benchmarks, the performance of pFedMe is always worse than the other benchmarks. This is because the PL model is uploaded directly for global model aggregation in pFedMe, and the trade- off weighting coefficient is fixed across the whole training.

To capture the similarity among the PL models, in each round of FedAMP and APPLE, the PL cloud models for clients are obtained by aggregating the PL local models based on weights updated using attention- inducing function and stochastic gradient descent (SGD), respectively. Both FedAMP and APPLE require multiple models to upload and download, which increases resource contention, leading to increased transmission errors.

By downloading an FL global model instead of a group of models, FedALA reduces communication overhead. In each round, the PL model of each client is initialized by aggregating the FL global model and its local model with element- wise learnable weights to capture the desired information. However, updates based on the previous round's PL models may be ineffective due to time- varying bit errors in transmissions. The performances of FedAMP, APPLE, and FedALA deteriorate faster than that of the proposed WPFL, because these PFL benchmarks do not adaptively adjust their training process and settings in response to the time- varying errors, and they undergo a greater cumulative impact of DP noise, quantization, and transmission errors.

Figs. 7(a), 7(c), 7(e), and 7(g) gauge the fairness of the pro

![](images/65bb1ef9a098ac1b46b832f20fa2412064bdd8c73cad4b346bd0dc29afe1567c.jpg)  
Fig. 7. Comparison of fairness and maximum test loss of all participating clients between the benchmarks.  $\epsilon_{\mathrm{Q}} = 1$ .  $\delta_{\mathrm{Q}} = 0.001$  for DNN and MLR, and  $\delta_{\mathrm{Q}} = 0.005$  for CNN.

posed WPFL and its PFL benchmarks. The proposed WPFL outperforms pFedMe and FedALA in fairness, due to our optimization for performance fairness. Although the fairness of the proposed WPFL is worse than that of FedAMP and APPLE, the proposed WPFL can achieve dramatically better accuracy and maximum test loss of all participating clients while maintaining relatively satisfying fairness. By contrast, FedAMP and APPLE offer much poor accuracy and maximum test loss, rendering the achieved fairness less meaningful.

# VIII. CONCLUSION

In this paper, we proposed a new WPFL framework, where quantization errors were exploited in coupling with a Gaussian

DP mechanism to enhance the privacy of WPFL and min- max fairness was enforced to balance its convergence and fairness. Experiments validated our analysis and demonstrated that, under the CNN model, our approach substantially outperforms its alternative scheduling strategies (including round- robin, random selection, and non- adjustment) by  $87.08\%$  in accuracy,  $16.21\%$  in the maximum test loss of participating clients, and  $38.37\%$  in fairness. With the quantization- assisted Gaussian mechanism, WPFL is  $16.10\%$  better in accuracy than using only the Gaussian mechanisms (e.g., MA), validating the idea of exploiting quantization errors for privacy enhancement. Moreover, our approach dramatically surpasses the wireless deployment of the state- of- the- art PFL (e.g., FedAMP) by  $10.43\%$  in accuracy and  $8.16\%$  in maximum test loss.

# REFERENCES

[1] T. Li, S. Hu, A. Beirami et al., "Ditto: Fair and robust federated learning through personalization," in Proc. 38th Int. Conf. Mach. Learn., vol. 139, 2021, pp. 6357- 6368. [2] H. U. Sami and B. Guler, "Over- the- air clustered federated learning," IEEE Trans. Wirel. Commun., vol. 23, no. 7, pp. 7877- 7893, 2023. [3] M. Mestoukirdi, M. Zecchin, D. Geisbert, and Q. Li, "User- centric federated learning: Trading off wireless resources for personalization," IEEE trans. mach. learn. commun. netw., vol. 1, pp. 346- 359, 2023. [4] Z. Zhao, J. Wang, W. Hong, T. Q. Quek, Z. Ding, and M. Peng, "Ensemble federated learning with non- in- data in wireless networks," IEEE Trans. Wirel. Commun., vol. 23, no. 4, pp. 3557- 3571, 2024. [5] C. You, K. Guo, H. H. Yang, and T. Q. Quek, "Hierarchical personalized federated learning over massive mobile edge computing networks," IEEE Trans. Wirel. Commun., vol. 22, no. 11, pp. 8141- 8157, 2023. [6] D. Li and J. Wang, "FedMD: Heterogeneous federated learning via model distillation," in Proc. NeurIPS Workshop, 2019, pp. 1- 8. [7] A. Fallah, A. Mokhtari, and A. Ozdaglar, "Personalized federated learning with theoretical guarantees: A model- agnostic meta- learning approach," Proc. Adv. Neural Inf. Process. Syst. (NeurIPS), vol. 33, pp. 3557- 3568, 2020. [8] K. Wei, J. Li, C. Ma et al., "Personalized federated learning with differential privacy and convergence guarantee," IEEE Trans. Inf. Forensics Security, vol. 18, pp. 4488- 4503, 2023. [9] C. You, D. Feng, K. Guo et al., "Semi- synchronous personalized federated learning over mobile edge networks," IEEE Trans. Wirel. Commun., vol. 22, no. 4, pp. 2262- 2277, 2022. [10] C. T. Dinh, N. Tran, and J. Nguyen, "Personalized federated learning with moree envelopes," Proc. Adv. Neural Inf. Process. Syst. (NeurIPS), vol. 33, pp. 21394- 21405, 2020. [11] T. Li, A. K. Sahu, M. Zaheer et al., "Federated optimization in heterogeneous networks," Proc. 3rd Conf. Mach. Learn. Syst. (MLSys), vol. 2, pp. 429- 450, 2020. [12] Y. Huang, L. Chu, Z. Zhou et al., "Personalized cross- silo federated learning on non- IID data," in Proc. AAAI Conf. Artif. Intell., vol. 35, no. 9, 2021, pp. 7865- 7873. [13] J. Luo and S. Wu, "Adapt to adaptation: Learning personalization for cross- silo federated learning," in Proc. 31th Int. Joint Conf. Artif. Intell. (IJCAI), vol. 2022, 2022, pp. 2166- 2173. [14] J. Zhang, Y. Hua, H. Wang et al., "FedALA: Adaptive local aggregation for personalized federated learning," in Proc. AAAI Conf. Artif. Intell., vol. 37, no. 9, 2023, pp. 11237- 11244. [15] H. Zhang, M. Tao, Y. Shi et al., "Federated multi- task learning with non- stationary and heterogeneous data in wireless networks," IEEE Trans. Wirel. Commun., vol. 23, no. 4, pp. 2653- 2667, 2023. [16] Q. Cui, X. You, N. Wei et al., "Overview of AI and Communication for 6G Network: Fundamentals, Challenges, and Future Research Opportunities," Sci China Inf Sci, vol. 68, no. 7, p. 171301, 2025. [17] M. Chen, Z. Yang, W. Saad et al., "A joint learning and communications framework for federated learning over wireless networks," IEEE Trans. Wirel. Commun., vol. 20, no. 1, pp. 269- 283, 2020. [18] W. Ni, Y. Liu, Z. Yang et al., "Integrating over- the- air federated learning and non- orthogonal multiple access: What role can this play?" IEEE Trans. Wirel. Commun., vol. 21, no. 12, pp. 10083- 10099, 2022.

[19] W. Ni, Y. Liu, Y. C. Eklar et al., "Star- ris integrated nonorthogonal multiple access and over- the- air federated learning: Framework, analysis, and optimization," IEEE Internet Things J., vol. 9, no. 18, pp. 17 136- 17 156, 2022. [20] W. Ni, J. Zheng, and H. Tian, "Semi- federated learning for collaborative intelligence in massive ion networks," IEEE Internet Things J., vol. 10, no. 13, pp. 11 942- 11 943, 2023. [21] M. Abadi, A. Chu, I. Goodfellow et al., "Deep learning with differential privacy," in n Proc. ACM SIGSAC Conf. Comput. Commun. Secur. (CCS), 2016, pp. 308- 318. [22] K. Wei, J. Li, M. Ding et al., "Federated learning with differential privacy: Algorithms and performance analysis," IEEE Trans. Inf. Forensics Secur., vol. 15, pp. 3454- 3469, 2020. [23] Y. Zhao, J. Zhao, M. Yang et al., "Local differential privacy- based federated learning for internet of things," IEEE Internet Things J., vol. 8, no. 11, pp. 8836- 8835, 2020. [24] S. Truex, L. Liu, K.- H. Chow et al., "LDP- Fed: Federated learning with local differential privacy," in Proc. 3rd ACM Int. Workshop Edge Syst. Anal. Netw., 2020, pp. 61- 66. [25] X. Yuan, W. Ni, M. Ding et al., "Amplitude- varying perturbation for balancing privacy and utility in federated learning," IEEE Trans. Inf. Forensics Security, vol. 18, pp. 1884- 1897, 2023. [26] H. Liu, J. Yan, and Y.- J. A. Zhang, "Differentially private over- the- air federated learning over MIMO fading channels," IEEE Trans. Wirel. Commun., vol. 41, no. 11, pp. 3533- 3547, 2024. [27] Q. Chen, Z. Wang, H. Wang et al., "FedDual: Pair- wise gossip helps federated learning in large decentralized networks," IEEE Trans. Inf. Forensics Security, vol. 18, pp. 335- 350, 2022. [28] N. Lang, E. Sofer, T. Shaked et al., "Joint privacy enhancement and quantization in federated learning," IEEE Trans. Signal Process., vol. 71, pp. 295- 310, 2023. [29] X. Lyu, X. Hou, C. Ren, X. Ge, P. Yang, Q. Cui, and X. Tao, "Secure and efficient federated learning with provable performance guarantees via stochastic quantization," IEEE Trans. Inf. Forensics Secur., vol. 19, pp. 4070- 4085, 2024. [30] G. Wang, Q. Qi, R. Han et al., "P2cefl: Privacy- preserving and communication efficient federated learning with sparse gradient and dithering quantization," IEEE Trans. Mob. Comput., 2024. [31] A. Z. Tan, H. Yu, L. Cui et al., "Towards personalized federated learning," IEEE Trans. Neural Netw. Learn. Syst., vol. 34, no. 12, pp. 9587- 9603, 2022. [32] K. Liu, S. Hu, S. Z. Wu et al., "On privacy and personalization in cross- silo federated learning," Proc. Adv. Neural Inf. Process. Syst. (NeurIPS), vol. 35, pp. 5925- 5940, 2022. [33] S. D. Okegbile, J. Cai, H. Zheng et al., "Differentially private federated multi- task learning framework for enhancing human- to- virtual connectivity in human digital twin," IEEE J. Sel. Areas Commun., 2023. [34] R. Hu, Y. Guo, H. Li et al., "Personalized federated learning with differential privacy," IEEE Internet Things J., vol. 7, no. 10, pp. 9530- 9539, 2020. [35] T. Li, M. Sanjabi, A. Beirami et al., "Fair resource allocation in federated learning," in Proc. Int. Conf. Learn. Represent., 2020, pp. 1- 13. [36] Z. Hu, K. Shaloudegi, G. Zhang et al., "Federated learning meets multi- objective optimization," IEEE Trans. Netw. Sci. Eng., vol. 9, no. 4, pp. 2039- 2051, 2022. [37] C. Dwork and A. Roth, "The algorithmic foundations of differential privacy," Found. Trends Theor. Comput. Sci., vol. 9, no. 3- 4, pp. 211- 407, 2014. [38] K. Cho and D. Yoon, "On the general BER expression of one- and two- dimensional amplitude modulations," IEEE Trans. Commun., vol. 50, no. 7, pp. 1074- 1080, 2002. [39] M. Nasr, R. Shokri, and A. Houmansadr, "Comprehensive privacy analysis of deep learning: Passive and active white- box inference attacks against centralized and federated learning," in Proc. IEEE Symp. Secur. Privacy (SP), 2019, pp. 739- 753. [40] M. Fredrikson, S. Jha, and T. Ristenpart, "Model inversion attacks that exploit confidence information and basic countermeasures," in Proc. 22nd ACM SIGSAC Conf. Comput. Commun. Secur., 2015, pp. 1322- 1333. [41] H. Karimi, J. Nutini, and M. Schmidt, "Linear convergence of gradient and proximal- gradient methods under the polyak- lojasiewicz condition," in Proc. Joint Eur. Conf. Mach. Learn. Knowl. Discovery Databases. Springer, 2016, pp. 795- 811. [42] M. O'Searcoid, Metric spaces. Berlin, Germany: Springer, 2006. [43] H. W. Kuhn, "The Hungarian method for the assignment problem," Naval Res. Logist. Quart., vol. 2, no. 1- 2, pp. 83- 97, 1955.

[44] D. Jungnickel and D. Jungnickel, Graphs, networks and algorithms. Berlin, Germany: Springer, 2005, vol. 3. [45] C. Sun, W. Ni, and X. Wang, "Joint computation offloading and trajectory planning for uav- assisted edge computing," IEEE Trans. Wirel. Commun., vol. 20, no. 8, pp. 5343- 5358, 2021. [46] S. Wang, T. Tuor, T. Salonidis et al., "Adaptive federated learning in resource constrained edge computing systems," IEEE J. Sel. Areas Commun., vol. 37, no. 6, pp. 1205- 1221, June 2019.

# APPENDIX

# A. Proof of Theorem 1

We first focus on the DP mechanism in one communication round. According to [21], the privacy bound for multivariate noise is converted into a one- dimensional form by assuming that  $\mathcal{M}_{\mathrm{Q}}(\mathcal{X}_1^{\prime})$  and  $\mathcal{M}_{\mathrm{Q}}(\mathcal{X}_1)$  are identical except for the first element, without loss of generality. For conciseness,  $u_{n,1}(\bullet)$  is written as  $u(\bullet)$  . Under the quantization- assisted Gaussian mechanism  $\mathcal{M}_{\mathrm{Q}}$  , the Mark Divergence and the  $\delta$  - Approximate Max Divergence are given by

$$
\begin{array}{r}D_{\infty}\left[\mathcal{M}\left(\mathcal{X}_1^{\prime}\right)\left|\mathcal{M}\left(\mathcal{X}_1\right)\right.\right] = \underset {\chi \in \mathbb{L}}{\max}\ln \frac{p}{p_1};\\ D_{\infty}^{\delta}\left[\mathcal{M}\left(\mathcal{X}_1^{\prime}\right)\left|\mathcal{M}\left(\mathcal{X}_1\right)\right.\right] = \underset {\chi \in \mathbb{L}}{\max}\ln \frac{p - \delta}{p_1}, \end{array} \tag{40b}
$$

where  $\mathcal{Q}_{\mathrm{L}} = \{\chi_{1},\ldots ,\chi_{|\mathcal{Q}_{\mathrm{L}}|}\}$  collects the quantization levels; for conciseness,  $p$  and  $p_1$  are defined as

$$
\begin{array}{r}p = (1 - q)\operatorname *{Pr}[\mathcal{M}_{\mathrm{Q}}(\chi_1) = \chi ] + q\operatorname *{Pr}[\mathcal{M}_{\mathrm{Q}}(\chi_0) = \chi ];\\ p_1 = \operatorname *{Pr}[\mathcal{M}_{\mathrm{Q}}(\chi_1) = \chi ], \end{array} \tag{41b}
$$

where  $\chi_0 = x_n\cup \chi_1$  is the adjacent dataset of  $\chi_1,\forall d_n\in \mathcal{X}$ $x_{n}\notin \mathcal{X}_{1}$  .  $\chi \in \mathcal{Q}_{\mathrm{L}}$  is a quantization level. According to (22), when  $\chi \in \mathcal{Q}_{\mathrm{L}}\backslash \{\chi_1,\chi_{|\mathcal{Q}_L|}\}$  , the probabilities in (41a) and (41b) are given by

$$
\begin{array}{r}\operatorname *{Pr}[\mathcal{M}_Q(\chi_1) = \chi ] = Q(\frac{\chi - E_{\mathrm{max}} - u(\chi_1)}{\sigma_{\mathrm{max}}}) - Q(\frac{\chi + E_{\mathrm{max}} - u(\chi_1)}{\sigma_{\mathrm{DP}}});\\ \operatorname *{Pr}[\mathcal{M}_Q(\chi_0) = \chi ] = Q(\frac{\chi - E_{\mathrm{max}} - u(\chi_0)}{\sigma_{\mathrm{DP}}}) - Q(\frac{\chi + E_{\mathrm{max}} - u(\chi_0)}{\sigma_{\mathrm{DP}}}). \end{array} \tag{42b}
$$

When  $\chi \in \{\chi_1,\chi_{|\mathcal{Q}_L|}\}$  we have

$$
\begin{array}{r}\operatorname *{Pr}[\mathcal{M}_Q(\chi_1) = \chi ] = Q(\frac{\chi - E_{\mathrm{L}}^{\max} - u(\chi_1)}{\sigma_{\mathrm{DP}}});\\ \operatorname *{Pr}[\mathcal{M}_Q(\chi_0) = \chi ] = Q(\frac{\chi - E_{\mathrm{L}}^{\max} - u(\chi_0)}{\sigma_{\mathrm{DP}}}). \end{array} \tag{43b}
$$

Since each parameter of the model is bounded by the clipping threshold  $C$  ,i.e.,  $u(\bullet)\leq C$  ,when  $\chi \in \mathcal{Q}_{\mathrm{L}}\backslash \{\chi_{1},\chi_{|\mathcal{Q}_{\mathrm{L}}|}\}$

$$
\begin{array}{rl} & {\operatorname *{Pr}[\mathcal{M}_Q(\bullet) = \chi ]\geq Q(\frac{2C + 3\sigma_{\mathrm{DP}} - E_{\mathrm{L}}^{\max}}{\sigma_{\mathrm{DP}}}) - Q(\frac{2C + 3\sigma_{\mathrm{DP}} + E_{\mathrm{L}}^{\max}}{\sigma_{\mathrm{DP}}}),}\\ & {\operatorname *{Pr}[\mathcal{M}_Q(\bullet) = \chi ]\leq Q(\frac{-E_{\mathrm{L}}^{\max}}{\sigma_{\mathrm{DP}}}) - Q(\frac{E_{\mathrm{L}}^{\max}}{\sigma_{\mathrm{DP}}});} \end{array} \tag{44b}
$$

when  $\chi \in \{\chi_1,\chi_{|\mathcal{Q}_L|}\}$

$$
Q(\frac{2C + 3\sigma_{\mathrm{DP}} - E_{\mathrm{L}}^{\max}}{\sigma_{\mathrm{DP}}})\leq \operatorname *{Pr}[\mathcal{M}_{\mathrm{Q}}(\bullet) = \chi ]\leq Q(\frac{3\sigma_{\mathrm{DP}} - E_{\mathrm{L}}^{\max}}{\sigma_{\mathrm{DP}}}). \tag{45}
$$

By substituting (44) and (45) into (40a) and then plugging the results into (40), it follows that

$$
\begin{array}{r}D_{\infty}[\mathcal{M}(\mathcal{X}_1^{\prime})||\mathcal{M}(\mathcal{X}_1)]\leq \max \{\ln \frac{\psi}{\psi_1},\ln \frac{\psi^{\prime}}{\psi_1^{\prime}}\} ,\\ D_{\infty}^{\delta}[\mathcal{M}(\mathcal{X}_1^{\prime})||\mathcal{M}(\mathcal{X}_1)]\leq \max \{\ln \frac{\psi - \delta}{\psi_1},\ln \frac{\psi^{\prime} - \delta}{\psi_1^{\prime}}\} = \epsilon_{\mathrm{Q}},\\ \Rightarrow \delta_{\mathrm{Q}} = \max \{\psi -\psi_1e^{\epsilon_{\mathrm{Q}}},\psi^{\prime} - \psi_1^{\prime}e^{\epsilon_{\mathrm{Q}}}\} . \end{array} \tag{46b}
$$

By applying Composition Theorem [37, Thm 3.16] to (46), Theorem 1 readily follows.

# B. Proof of Lemma 1

By substituting (15) and (17) into (16), we have

$$
\mathbb{E}\left[\| \pmb {\omega}_\mathrm{L}^t -\pmb {\omega}^*\| ^2\right] = \mathbb{E}\left[\| \frac{1}{|\mathcal{N}_t|}\sum_{n\in \mathcal{N}_t}\pmb {u}_n^t +\Lambda_0 - \pmb {\omega}^*\| ^2\right] \tag{47a}
$$

$$
\begin{array}{rl} & {\leq \frac{1}{|\mathcal{N}_t|}\sum_{n\in \mathcal{N}_t}\mathbb{E}\Big[\big(\hat{\omega}_{n,\mathrm{G}}^t -\eta_{\mathrm{F},n}^t\nabla F_n(\hat{\omega}_{n,\mathrm{G}}^t)\big) - \omega^* +\Lambda_0\| ^2\Big]}\\ & {= \frac{1}{|\mathcal{N}_t|}\sum_{n\in \mathcal{N}_t}\big(2\eta_{\mathrm{F},n}^t\mathbb{E}\langle \nabla F(\hat{\omega}_{n,\mathrm{G}}^t),\omega^* -\hat{\omega}_{n,\mathrm{G}}^t\rangle}\\ & {+2\mathbb{E}\langle \Lambda_0, - \eta_{\mathrm{F},n}^t\nabla F(\hat{\omega}_{n,\mathrm{G}}^t)\rangle +2\mathbb{E}\langle \Lambda_0,\hat{\omega}_{n,\mathrm{G}}^t -\omega^*\rangle \big)}\\ & {\mathbb{E}\big[\| \Lambda_0\| ^2\big] + \mathbb{E}\Big[\| -\eta_{\mathrm{F},n}^t\nabla F(\hat{\omega}_{n,\mathrm{G}}^t)\| ^2\Big] + \mathbb{E}\Big[\| \hat{\omega}_{n,\mathrm{G}}^t -\omega^*\| ^2\Big]}\\ & {\leq \frac{1}{|\mathcal{N}_t|}\sum_{n\in \mathcal{N}_t}\Big((1 + \frac{1}{\phi_1} +\frac{1}{\phi_2})\mathbb{E}\big[\| \Lambda_0\| ^2\big] + (1 + \phi_1)\cdot}\\ & {\mathbb{E}\Big[\| -\eta_{\mathrm{F},n}^t\nabla F(\hat{\omega}_{n,\mathrm{G}}^t)\| ^2\Big] + (1 + \phi_2 - \mu \eta_{\mathrm{F},n}^t)\mathbb{E}\Big[\| \hat{\omega}_{n,\mathrm{G}}^t -\omega^*\| ^2\Big]}\\ & {\leq \frac{1}{|\mathcal{N}_t|}\sum_{n\in \mathcal{N}_t}\Big((1 + \frac{1}{\phi_1} +\frac{1}{\phi_2})\mathbb{E}\big[\| \mathbf{\Lambda}_0\| ^2\big] + (1 + \phi_2}\\ & {+(1 + \phi_1)L^2 (\eta_{\mathrm{F},n}^t)^2 -\mu \eta_{\mathrm{F},n}^t)\mathbb{E}\big[\| \hat{\omega}_{n,\mathrm{G}}^t -\omega^*\| ^2\big]\big),} \end{array} \tag{47b}
$$

where  $\Lambda_0 = \mathbf{s}_n^t\circ \zeta_{n,\mathrm{L}}^t +(\mathbf{1}_{|\omega |} - \mathbf{s}_{n,\mathrm{L}}^t)\circ (\mathbf{z}_n^t +\mathbf{E}_{n,\mathrm{L}}^t)$  . (47b) is obtained by substituting (20a) into (47a) and then utilizing the Cauchy- Schwarz inequality; (47d) is obtained by considering the  $\mu$  strong convexity of  $F(\cdot)$  and  $F(\omega^{*}) - F(\hat{\omega}_{n,\mathrm{G}}^{t})\leq 0$  for the first term of (47b), exploiting  $2\langle a,b\rangle \leq va^2 +\frac{1}{v} b^2$  with  $a = \Lambda_0$ $b = - \eta_{\mathrm{F},n}^{t}\nabla F(\hat{\omega}_{n,\mathrm{G}}^{t})$  , and  $v = \phi_{1} > 0$  for the second term, and exploiting  $2\langle a,b\rangle \leq va^2 +\frac{1}{v} b^2$  , with  $a = \Lambda_0$ $b = \hat{\omega}_{n,\mathrm{G}}^{t} - \omega^{*}$  , and  $v = \phi_{2} > 0$  for the third term; (47e) is obtained under the  $L$  - smoothness of  $F(\cdot)$  and

$$
\begin{array}{rl} & {\mathbb{E}[\| \Lambda_0\| ^2] = \mathbb{E}[\| \mathbf{s}_{n,i}^t\circ \zeta_{n,\mathrm{L}}^t\| ^2] + \mathbb{E}[\| \mathbf{1}_{|\omega |} - \mathbf{s}_{n}^t)\circ (\mathbf{z}_{n}^t +\mathbf{E}_{n,\mathrm{L}}^t)\| ^2]}\\ & {\qquad +2\mathbb{E}\left\langle \mathbf{s}_{n}^t\circ \zeta_{n,\mathrm{L}}^t,(1 - \mathbf{s}_{n}^t)\circ (\mathbf{z}_n^t +\mathbf{E}_{n,\mathrm{L}}^t)\right\rangle} \end{array} \tag{48}
$$

We further have

$$
\begin{array}{rl} & {\mathbb{E}\left[\| \mathbf{s}_n^t\circ \zeta_{n,\mathrm{L}}^t\| ^2\right] = \mathbb{E}\left[\sum_{i = 1}^{|\omega |}\left(s_{n,i}^t\zeta_{n,i,\mathrm{L}}^t\right)^2\right]}\\ & {\leq \sum_{i = 1}^{|\omega |}\mathbb{E}\left[\left(s_{n,i}^t\right)^2\right]\mathbb{E}\left[\left(|u_{n,i}^{t + 1} + C + 3\sigma_{\mathrm{DP}}\right)^2\right]}\\ & {\leq 2\rho_{n,\mathrm{L}}^t\mathbb{E}\left[\sum_{i = 1}^{|\omega |}\left(|u_{n,i}^{t + 1}|^2 +(C + 3\sigma_{\mathrm{DP}})^2\right)\right]}\\ & {\leq 2\rho_{n,\mathrm{L}}^t C^2 +2|\omega |\rho_{n,\mathrm{L}}^t (C + 3\sigma_{\mathrm{DP}})^2;} \end{array} \tag{49b}
$$

where (49b) is obtained by substituting  $\zeta_{n,i,\mathrm{L}}^t\leq |u_{n,i}^t |+$ $C + 3\sigma_{\mathrm{DP}}$  into (49a), followed by exploiting  $\begin{array}{rl}\mathbb{E}\left[\sum_{i = 1}^{|\omega |}a\right] = \end{array}$ $\textstyle \sum_{i = 1}^{|\omega |}\mathbb{E}\left[a\right]$  and  $\mathbb{E}\left[bc\right] = \mathbb{E}\left[b\right]\mathbb{E}\left[c\right]$  with  $a = (s_{n,i}^t)^2 (|u_{n,i}^t |+$ $C + 3\sigma_{\mathrm{DP}})^2$ $b = (s_{n,i}^t)^2$ $c = (|u_{n,i}^t | + C + 3\sigma_{\mathrm{DP}})^2$  and (49c) is due to  $\mathbb{E}[(s_{n,i}^t)^2 ] = \rho_{n,\mathrm{L}}^t$  and  $(a + b)^2\leq 2(a^2 +b^2)$  ; (49d) follows from  $\| \pmb {u}_n^t\| ^2\leq C^2$

$$
\begin{array}{rl} & {\mathbb{E}[\| \mathbf{\Phi}(\mathbf{1}_{|\omega |} - \mathbf{s}_n^t)\circ (\mathbf{z}_n^t +\mathbf{E}_{n,\mathrm{L}}^t)]^2}\\ & {\quad = (1 - \rho_{n,\mathrm{L}}^t)\sum_{i = 1}^{|\omega |}(\mathbb{E}\big[(s_{n,i}^t)^2] + \mathbb{E}\big[(E_{n,i,\mathrm{L}}^t)^2\big])}\\ & {\quad \leq |\omega |(1 - \rho_{n,\mathrm{L}}^t)(\sigma_{\mathrm{DP}}^2 +(E_{\mathrm{L}}^{\mathrm{max}})^2),} \end{array} \tag{50b}
$$

where (50b) is obtained by substituting  $\mathbb{E}\left[(1 - s_{n,i}^t)^2\right] = 1$ $\rho_{n,\mathrm{L}}^t$  and  $|E_{n,i,\mathrm{L}}^t |\leq E_{\mathrm{L}}^{\mathrm{max}}$  into (50a).

$$
\begin{array}{rl} & {\mathbb{E}\left\langle \mathbf{s}_n^t\circ \zeta_{n,\mathrm{L}}^t,(1 - \mathbf{s}_n^t)\circ (\mathbf{z}_n^t +\mathbf{E}_{n,\mathrm{L}}^t)\right\rangle}\\ & {\quad = \mathbb{E}\left[\sum_{i = 1}^{|\omega |}\left(s_{n,i,\mathrm{L}}^t\zeta_{n,i,\mathrm{L}}^t\left(1 - s_{n,i,\mathrm{L}}^t\right)(z_{n,i}^t +E_{n,i,\mathrm{L}}^t)\right)\right] = 0,} \end{array} \tag{51}
$$

which is due to the fact that  $s_{n,i,\mathrm{L}}^t = 0$  or  $s_{n,i,\mathrm{L}}^t = 1$

By plugging (49d), (50b), and (51) into (48), the upper bound of  $\begin{array}{r}\frac{1}{|\mathcal{N}_t|}\sum_{n\in \mathcal{N}_t}\mathbb{E}\left[\| \Lambda_0\| ^2\right] \end{array}$  can be rewritten as

$$
\begin{array}{rl} & {\frac{1}{|\mathcal{N}_t|}\sum_{n\in \mathcal{N}_t}\mathbb{E}\left[\| \Lambda_0\| ^2\right]\leq \frac{1}{|\mathcal{N}_t|}\sum_{n\in \mathcal{N}_t}\left(2\rho_{n,\mathrm{L}}^t C^2\right.}\\ & {\quad \quad \left. + 2|\omega |\rho_{n,\mathrm{L}}^t (C + 3\sigma_{\mathrm{DP}})^2 +|\omega |(1 - \rho_{n,\mathrm{L}}^t)\left(\sigma_{\mathrm{DP}}^2 +(E_{\mathrm{L}}^{\mathrm{max}})^2\right)\right)}\\ & {\quad = \Theta_{\mathrm{L}}^t +|\omega |\left(\sigma_{\mathrm{DP}}^2 +(E_{\mathrm{L}}^{\mathrm{max}})^2\right)} \end{array} \tag{52b}
$$

By plugging (52b) into (47e), Lemma 1 follows.

# C. Proof of Theorem 2

By plugging (19) into (18), we have

$$
\begin{array}{rl} & {\mathbb{E}\big[\| \hat{\omega}_{n,\mathrm{G}}^{t + 1} - \omega^{*}\|^{2}\big] = \mathbb{E}\big[\| \Lambda_{1} + \hat{\omega}_{\mathrm{L}}^{t} - \omega^{*}\|^{2}\big]}\\ & {\quad \leq (1 + \frac{1}{\phi_{1}})\mathbb{E}\big[\| \Lambda_{1}\|^{2}\big] + (1 + \phi_{1})\mathbb{E}\big[\| \hat{\omega}_{\mathrm{L}}^{t} - \omega^{*}\|^{2}\big]} \end{array} \tag{53b}
$$

where  $\Lambda_{1} = \mathbf{s}_{n,\mathrm{G}}^{t + 1}\circ \zeta_{n,\mathrm{G}}^{t + 1} + (1_{\omega |\mathrm{~ - ~}s_{n,\mathrm{G}}^{t + 1}}^{\mathrm{~ - ~}})\circ \mathbf{E}_{n,\mathrm{G}}^{t}$  is defined for brevity; (53b) is obtained by exploiting  $(a + b)^2 = a^2 +b^2 +$ $2\langle a,b\rangle \leq a^2 +b^2 +v^2 a^2 +\frac{1}{v^2} b^2$  with  $a = \Lambda_1$ $b = \tilde{\omega}_{\mathrm{L}}^{t} - \omega^{*}$  and  $v = \rho_{1}\neq 0$

Further, we establish the upper bound of  $\mathbb{E}[\| \Lambda_1\| ^2 ]$  as

$$
\begin{array}{rl} & {\mathbb{E}[\| \Lambda_1\| ^2] = \mathbb{E}[\sum_{k = 1}^{|\omega |}f_{n,k,\mathrm{G}}^{t + 1}C_{n,k,\mathrm{G}}^{t + 1} + (1 - s_{n,k,\mathrm{G}}^{t + 1})E_{n,k,\mathrm{G}}^t]^2}\\ & {\quad \leq \mathbb{E}[\sum_{k = 1}^{|\omega |}C_{n,k,\mathrm{G}}^{t + 1}(\hat{\omega}_{k,\mathrm{L}}^t\big| + C) + (1 - s_{n,k,\mathrm{G}}^{t + 1})E_{\mathrm{G}}^{\max})^2]}\\ & {\quad \leq 2\rho_{n,\mathrm{G}}^{t + 1}\mathbb{E}[\| \hat{\omega}_{\mathrm{L}}^t\| ^2] + 2|\omega |(\beta_{\mathrm{G}}^2 +(1 - \beta_{\mathrm{G}}^2)\rho_{n,\mathrm{G}}^{t + 1})C^2,} \end{array} \tag{54b}
$$

where (54b) is based on triangle inequality, and (54c) is due to the Cauchy- Schwarz inequality.

The upper bound of  $\mathbb{E}[\| \hat{\omega}_{\mathrm{L}}^{t}\| ^2 ]$  is established as

$$
\begin{array}{rl} & {\mathbb{E}\left[\| \hat{\omega}_{\mathrm{L}}^{t}\|^{2}\right] = \mathbb{E}\left[\| \frac{1}{|\mathcal{N}_{t}|}\sum_{n\in \mathcal{N}_{t}}\left(u_{n}^{t} + \Lambda_{0}\right)\|^{2}\right]}\\ & {\quad \leq \frac{1}{|\mathcal{N}_{t}|}\sum_{n\in \mathcal{N}_{t}}\left((1 + \frac{1}{\phi_{2}})\mathbb{E}\left[\| u_{n}^{t}\|^{2}\right] + (1 + \phi_{2})\mathbb{E}\left[\| \Lambda_{0}\|^{2}\right]\right)}\\ & {\quad \leq \left(1 + \frac{1}{\phi_{2}}\right)C^{2} + (1 + \phi_{2})\left(\mathbf{e}_{\mathrm{L}}^{t} + |\omega |\left(\sigma_{\mathrm{DP}}^{2} + (E_{\mathrm{L}}^{\max})^{2}\right)\right),} \end{array} \tag{55b}
$$

where (55b) is due to the Cauchy- Schwarz inequality and  $(a+$ $b)^2\leq a^2 +b^2 +v^2 a^2 +\frac{1}{v^2} b^2$  with  $a = \Lambda_0,b = \omega_t$  , and  $v = \phi_{2}\neq 0$  55c) is obtained by substituting (52b) into (55b) and exploiting  $\| \mathbf{\nabla}u_t^t\| \leq C$  . Substituting (55c) into (54c) yields

$$
\begin{array}{rl} & {\mathbb{E}\left[\| \Lambda_1\| ^2\right]\leq 2\rho_{n,\mathrm{G}}^{t + 1}\Big(\Big(1 + \frac{1}{\phi_2}\Big)\mathcal{C}^2 +(1 + \phi_2)\big(\Theta_{\mathrm{L}}^t +|\omega |\left(\sigma_{\mathrm{DP}}^t +(E_{\mathrm{L}}^{\max})^2\right)\Big)\Big)}\\ & {\qquad +2|\omega |\left(\beta_{\mathrm{G}}^2 +(1 - \beta_{\mathrm{G}}^2)\rho_{n,\mathrm{G}}^{t + 1}\right)C^2.} \end{array} \tag{56}
$$

Based on Lemma 1, we finally obtain (27a) by substituting (26) and (56) into (53b).According to (27a), we further have

$$
\begin{array}{rl} & {\mathbb{E}\left[\| \hat{\omega}_{n,\mathrm{G}}^{t + 1} - \omega^{*}\| ^2\right]\leq \epsilon_{\mathrm{F}}^{\max}\mathbb{E}\left[\| \hat{\omega}_{n,\mathrm{G}}^t -\omega^{*}\| ^2\right] + \Gamma^{\max}}\\ & {\quad \leq (\epsilon_{\mathrm{F}}^{\max})^{t + 1}\mathbb{E}\left[\| \hat{\omega}_{n,\mathrm{G}}^0 -\omega^{*}\| ^2\right] + \Gamma^{\max}\sum_{i = 0}^{t}(\epsilon_{\mathrm{F}}^{\max})^i.} \end{array} \tag{57b}
$$

By utilizing the geometric series, (27b) is obtained. With  $\epsilon_{\mathrm{F}}^{\max}\in (0,1)$  , the global  $\mathrm{FL}$  converges.

# D. Proof of Theorem 3

Let  $g_{n}(\pi_{n}^{t},\hat{\omega}_{n,\mathrm{G}}^{t + 1})$  denote the stochastic gradient of  $f_{n}(\pi_{n}^{t},\hat{\omega}_{n,\mathrm{G}}^{t + 1})$  . Then,

$$
g_{n}(\pi_{n}^{t},\hat{\omega}_{n,\mathrm{G}}^{t + 1}) = \left(1 - \frac{\lambda_{n}^{t + 1}}{2}\right)\nabla F_{n}(\pi_{n}^{t}) + \lambda_{n}^{t + 1}(\pi_{n}^{t} - \hat{\omega}_{n,\mathrm{G}}^{t + 1}). \tag{58}
$$

As per the PL model of client  $n$  at the  $(t + 1)$  - th model update, we have [1, Eq. (96)]

$$
\begin{array}{rl} & {\mathbb{E}\left[\| \hat{\omega}_{n}^{t + 1} - \pi_{n}^{*}\|^{2}\right] = \mathbb{E}\left[\| \hat{\omega}_{n}^{t} - \pi_{n}^{*}\|^{2}\right] + \left(\eta_{\mathrm{P},n}^{t + 1}\right)^{2}\times}\\ & {\mathbb{E}\left[\| g_{n}(\hat{\pi}_{n}^{t};\hat{\omega}_{n,\mathrm{G}}^{t + 1})\|^{2}\right] + 2\eta_{\mathrm{P},n}^{t + 1}\mathbb{E}\left\langle g_{n}(\hat{\pi}_{n}^{t};\hat{\omega}_{n,\mathrm{G}}^{t + 1}),\pi_{n}^{*} - \hat{\pi}_{n}^{t}\right\rangle .} \end{array} \tag{59}
$$

The third term on the RHS of (59) can be rewritten as

$$
\begin{array}{rl} & 2\eta_{\mathrm{P},n}^{t + 1}\mathbb{E}\Big\langle g_n(\hat{\pi}_n^t;\hat{\omega}_{n,\mathrm{G}}^{t + 1}),\pi_n^* -\hat{\pi}_n^t\Big\rangle \\ & = 2\eta_{\mathrm{P},n}^{t + 1}\mathbb{E}\Big\langle (1 - \frac{\lambda_n^{t + 1}}{2})\nabla F_n(\hat{\pi}_n^t)\Big\rangle \lambda_n^{t + 1}(\hat{\pi}_n^t -\hat{\omega}_{n,\mathrm{G}}^{t + 1}),\pi_n^* -\hat{\pi}_n^t\Big\rangle \end{array} \tag{60a}
$$

$$
\begin{array}{rl} & {\leq 2\eta_{\mathrm{P},n}^{t + 1}\Big(1 - \frac{\lambda_n^{t + 1}}{2}\Big)\mathbb{E}\left[F_n\left(\pi_n^*\colon \hat{\omega}_{n,\mathrm{G}}^{t + 1}\right) - F_n\left(\hat{\pi}_n^t;\hat{\omega}_{n,\mathrm{G}}^{t + 1}\right)\right]}\\ & {\quad -\eta_{\mathrm{P},n}^{t + 1}\Big(1 - \frac{\lambda_n^{t + 1}}{2}\Big)\mu E\left[\| \pi_n^* -\hat{\pi}_n^t\| ^2\right]}\\ & {\quad +2\eta_{\mathrm{P},n}^{t + 1}\mathbb{E}\left\langle \lambda_n^{t + 1}(\bar{\pi}_n^t -\hat{\omega}_{n,\mathrm{G}}^{t + 1}),\pi_n^* -\hat{\pi}_n^t\right\rangle}\\ & {= 2\eta_{\mathrm{P},n}^{t + 1}\mathbb{E}\left[f_n\left(\pi_n^*\colon \hat{\omega}_{n,\mathrm{G}}^{t + 1}\right) - f_n\left(\bar{\pi}_n^*\colon \hat{\omega}_{n,\mathrm{G}}^{t + 1}\right)\right]}\\ & {\quad -\eta_{\mathrm{P},n}^{t + 1}\Big(1 - \frac{\lambda_n^{t + 1}}{2})\mu +\lambda_n^{t + 1}\Big)\mathbb{E}\left[\| \pi_n^* -\bar{\pi}_n^t\| ^2\right],} \end{array} \tag{60b}
$$

where 60a) is based on 58)60b) is obtained by first considering the  $\mu$  strong convexity of  $F_{n}(\cdot)$  followed by substituting (9a) into (60b).By substituting (60c) into (59), we obtain the upper bound of  $\mathbb{E}\left[\| \tilde{\pi}_{n}^{t + 1} - \pi_{n}^{*}\|^{2}\right]$  as

$$
\begin{array}{rl} & {\mathbb{E}\left[\| \tilde{\pi}_{n}^{t + 1} - \pi_{n}^{*}\|^{2}\right]\leq \epsilon_{\mathrm{P},n}^{t + 1}\mathbb{E}\left[\| \tilde{\pi}_{n}^{t} - \pi_{n}^{*}\|^{2}\right] + (\eta_{\mathrm{P},n}^{t + 1})^{2}\mathbb{E}\left[\| g(\tilde{\pi}_{n}^{t};\hat{\omega}_{n,\mathrm{G}}^{t + 1})\|^{2}\right]}\\ & {\quad +2\eta_{\mathrm{P},n}^{t + 1}\mathbb{E}\left[f_{n}(\pi_{n}^{*};\hat{\omega}_{n,\mathrm{G}}^{t + 1}) - f_{n}(\bar{\pi}_{n}^{t};\hat{\omega}_{n,\mathrm{G}}^{t + 1})\right]}\\ & {\leq \epsilon_{\mathrm{P},n}^{t + 1}\mathbb{E}\left[\| \tilde{\pi}_{n}^{t} - \pi_{n}^{*}\|^{2}\right] + (\eta_{\mathrm{P},n}^{t + 1})^{2}\mathbb{E}\left[\| g(\tilde{\pi}_{n}^{t};\omega^{*})\|^{2}\right]}\\ & {\quad +(\eta_{\mathrm{P},n}^{t + 1})^{2}\mathbb{E}\left[\| \hat{\omega}_{n,\mathrm{G}}^{t + 1} - \omega^{*}\|^{2}\right]}\\ & {\quad +2(\eta_{\mathrm{P},n}^{t + 1})^{2}\lambda_{n}^{t + 1}\sqrt{\mathbb{E}\left[\|g(\bar{\pi}_{n}^{t};\omega^{*})\|^{2}\right]}\sqrt{\mathbb{E}\left[\|g_{n,\mathrm{G}}^{t + 1} - \omega^{*}\|^{2}\right]}}\\ & {\quad +2\eta_{\mathrm{P},n}^{t + 1}\lambda_{n}^{t + 1}\sqrt{\mathbb{E}\left[\|g(\bar{\pi}_{n}^{t} - \pi_{n}^{*})\|^{2}\right]}\mathbb{E}\left[\| \hat{\omega}_{n,\mathrm{G}}^{t + 1} - \omega^{*}\|^{2}\right]}\\ & {\leq \epsilon_{\mathrm{P},n}^{t + 1}\mathbb{E}\left[\| \tilde{\pi}_{n}^{t} - \pi_{n}^{*}\|^{2}\right] + \Psi_{n}^{t + 1}\mathbb{E}\left[\| \hat{\omega}_{n,\mathrm{G}}^{t + 1} - \omega^{*}\|^{2}\right]}\\ & {\quad +\left(1 + (\lambda_{n}^{t + 1})^{3}\right)(\eta_{\mathrm{P},n}^{t + 1})^{2}\mathbb{E}\left[\| g(\bar{\pi}_{n}^{t};\omega^{*})\|^{2}\right],} \end{array} \tag{61b}
$$

where (61b) and (61c) exploit Cauchy- Schwarz inequality.

We further establish the upper bounds for the squared distances between the PL model and the FL local model, and between the PL model and the optimal FL global model, and for the squared norm of the gradient of the PL model:

$$
\begin{array}{rl} & {\mathbb{E}\left[\| \tilde{\pi}_n^t -\pmb {u}_n^* |\mathbb{I}_2^2\right]\leq \frac{1}{\mu^2}\mathbb{E}\left[\| \nabla F_n(\tilde{\pi}_n^t)\| ^2\right]\leq \frac{G_0^2}{\mu^2};}\\ & {\mathbb{E}\left[\| \tilde{\pi}_n^t -\omega^* |\mathbb{I}_2^2\right] = \mathbb{E}\left[\| \tilde{\pi}_n^t -\pmb {u}_n^* +\tilde{\pi}_n^* -\omega^* |\mathbb{I}_2^2\right]}\\ & {\quad \leq \mathbb{E}\left[\| \tilde{\pi}_n^t -\pmb {u}_n^* |\mathbb{I}_2^2\right] + \mathbb{E}\left[\| \pmb {u}_n^* -\omega^* |\mathbb{I}_2^2\right] + \mathbb{E}\left[\| \tilde{\pi}_n^t -\pmb {u}_n^* |\mathbb{I}_2^2\right]}\\ & {\quad \leq \frac{G_0^2}{\mu^2} +M^2 +\frac{2MG_0}{\mu};}\\ & {\mathbb{E}\| g(\tilde{\pi}_n^t;\omega^*)\| ^2\} = \mathbb{E}\left[\| (1 - \frac{\lambda_{n}^{t + 1}}{2})\nabla F_n(\tilde{\pi}_n^t) + \lambda_{n}^{t + 1}(\tilde{\pi}_n^t -\omega^*)\| ^2\right]}\\ & {\quad \leq (1 - \frac{\lambda_{n}^{t + 1}}{2})^2 G_0^2 +(\lambda_{n}^{t + 1})^2 (\frac{G_0}{\mu} +M)^2 +2(1 - \frac{\lambda_{n}^{t + 1}}{2})G_0\lambda_{n}^{t + 1}(\frac{G_0}{\mu} +M)}\\ & {\quad \triangleq G_n^{t + 1},} \end{array} \tag{64}
$$

where (62) is due to the convexity of  $F_{n}(\cdot)$  and the assumption that  $\mathbb{E}\left[\| \nabla F_n(\omega^t)\| ^2\right]\leq G_0^2$  63) is based on the CauchySchwarz inequality and (62). Likewise, (64) is based on (63). Similarly, we have  $\mathbb{E}\left[\| \hat{\omega}_{n,\mathrm{G}}^{t} - \omega^{*}\|^{2}\right]\leq (\frac{G_{0}^{2}}{\mu} +M)^{2}$

By plugging (62)- (64) into (61c), it readily follows that

$$
\begin{array}{rl} & {\mathbb{E}\left[\| \bar{\pi}_n^{t + 1} - \pi_n^*\| ^2\right]\leq \epsilon_{\mathrm{P},n}^{t + 1}\mathbb{E}\left[\| \bar{\pi}_n^t -\pi_n^*\| ^2\right]}\\ & {\quad +(1 + (\lambda_n^{t + 1})^3)\left(\eta_{\mathrm{P},n}^{t + 1}\right)^2 G_n^{t + 1} + \Psi_n^{t + 1}\mathbb{E}\left[\| \hat{\omega}_{n,\mathrm{G}}^{t + 1} - \omega^*\| ^2\right],} \end{array} \tag{65}
$$

Based on Theorem 2 and (65), it follows that

$$
\begin{array}{rl} & {\mathbb{E}\left[\| \bar{\pi}_{n}^{t + 1} - \pi_{n}^{*}\|^{2}\right]\leq \epsilon_{\mathrm{P},n}^{t + 1}\mathbb{E}\left[\| \bar{\pi}_{n}^{t} - \pi_{n}^{*}\|^{2}\right]}\\ & {\quad +\left(1 + (\lambda_{n}^{t + 1})^{3}\right)(\eta_{\mathrm{P},n}^{t + 1})^{2}G_{n}^{t + 1} + \Psi_{n}^{t + 1}\left(\epsilon_{1}(\rho_{n,\mathrm{G}}^{t + 1})\Theta_{\mathrm{L}}^{t}\right.}\\ & {\quad \left. + \Gamma_{0}\rho_{n,\mathrm{G}}^{t + 1} + \Gamma_{1} + \frac{1}{|\mathcal{N}_{t}|}\sum_{n\in \mathcal{N}_{t}}\epsilon_{\mathrm{P},n}^{t}\mathbb{E}\left[\| \hat{\omega}_{n,\mathrm{G}}^{t} - \omega^{*}\|^{2}\right]\right),}\\ & {\quad = \epsilon_{\mathrm{P},n}^{t + 1}\mathbb{E}\left[\| \bar{\pi}_{n}^{t} - \pi_{n}^{*}\|^{2}\right] + \Phi_{n}^{t + 1}.} \end{array} \tag{66b}
$$

Then, Theorem 3 follows.

# E. Proof of Theorem 4

For the brevity of notation, we define

$$
B_{t + 1}\triangleq \big(1 + (\lambda_n^{t + 1})^3\big)(\eta_{\mathrm{P},n}^{t + 1})^2 G_n^{t + 1}. \tag{67}
$$

Let  $B^{\mathrm{max}}$  and  $\Psi^{\mathrm{max}}$  denote the maxima of  $B_{t + 1}$  and  $\Psi_{n}^{t + 1}$  respectively. Based on (37) and (14),  $B^{\mathrm{max}}$ $\Psi^{\mathrm{max}}$  , and  $\Gamma^{\mathrm{max}}$  exist and are unique since  $e_{n,k,\mathrm{L}}^t < 1,\forall n,t$  in (13). The maximum of  $\Phi_{n}^{t + 1}$  , denoted as  $\Phi^{\mathrm{max}}$  , exists and is unique; i.e.,  $\Phi^{\mathrm{max}} = B^{\mathrm{max}} + \Psi^{\mathrm{max}}(\Gamma^{\mathrm{max}} + (\frac{G_0^2}{\mu} +M)^2\epsilon_{\mathrm{F}}^{\mathrm{max}})$

By substituting (67) into (65) and (27a), it follows that

$$
\begin{array}{rl} & {\mathbb{E}\left[\| \vec{\pi}_{n}^{t + 1} - \pi_{n}^{*}\|^{2}\right]\leq \epsilon_{\mathrm{P}}^{\mathrm{max}}\mathbb{E}\left[\| \vec{\pi}_{n}^{t} - \pi_{n}^{*}\|^{2}\right] + \Phi^{\mathrm{max}}}\\ & {\quad \leq (\epsilon_{\mathrm{P}}^{\mathrm{max}})^{t + 1}\mathbb{E}\left[\| \pi_{n}^{0} - \pi_{n}^{*}\|^{2}\right] + \Phi^{\mathrm{max}}\sum_{i = 0}^{t}(\epsilon_{\mathrm{P}}^{\mathrm{max}})^{i}}\\ & {\quad = (\epsilon_{\mathrm{P}}^{\mathrm{max}})^{t + 1}\mathbb{E}\left[\| \pi_{n}^{0} - \pi_{n}^{*}\|^{2}\right] + (\epsilon_{\mathrm{P}}^{\mathrm{max}})^{t + 1 - 1}\Phi^{\mathrm{max}}.} \end{array} \tag{68b}
$$

According to (68c), with  $\epsilon_{\mathrm{P}}^{\mathrm{max}} < 1$ , WPFL under imperfect channels converges as  $t$  increases. After  $T$  aggregations, the convergence upper bound of the PL model is (31).

# F. Proof of Theorem 5

For conciseness,  $\eta_{\mathrm{P},n}^{t + 1}$  and  $\lambda_{n}^{t + 1}$  are written as  $\eta$  and  $\lambda$  respectively. Based on (34) and (37), the second derivative of  $\Phi_{n}^{t + 1}$  with respect to  $\eta$  is given by

$$
\begin{array}{rl} & {\frac{\partial^2\Phi_n^{t + 1}}{\partial\eta^2} = \frac{6a_0\lambda^2}{\eta^2}\left(MH_1 + (2a_0^3\frac{3}{4} b_0)^2 +\frac{7}{16} b_0^3\right)\left(\frac{2 - \mu}{2\mu} G_0\right)}\\ & {+2(1 + \lambda^3)H_2 + \frac{2\eta^6H_3 + 2\mu_0^3(b_0 + \eta(-\mu + \eta))^3H_4}{a_0\eta^4(b_0 - \mu\eta + \eta^2)^3} (\Gamma_2\rho_{n,\mathrm{G}}^{t + 1} + \Gamma_3),} \end{array}
$$

where  $a = (1 - \frac{t}{2})^{- 1}$ ,  $b = 1 - \epsilon_{\mathrm{P}}^{t + 1}$ , and  $Q = (\frac{t}{\mu} - \frac{1}{2})G_0 + M$  and  $H_i$ ,  $i = 1,2,3,4$ , are given by

$$
\begin{array}{rl} & H_{1} = 4\eta^{4} - 2\mu \eta^{3} - 3b_{0}\eta^{2} + b_{0}\mu \eta +b_{0}^{2};\\ & H_{2} = 6(a_{0}Q)^{2}\eta^{2} + (6G_{0}(a_{0}Q) - 6\mu (a_{0}Q)^{2})\eta \\ & \qquad +((2b_{0} + \mu^{2})(a_{0}Q)^{2} - 2(a_{0}Q)\mu G_{0} + G_{0}^{2});\\ & H_{3} = 6b_{0}^{2} + b_{0}\eta (-8\mu +3\eta) + \eta^{2}(3\mu^{2} - 3\mu \eta +\eta^{2});\\ & H_{4} = 3b_{0}^{2} + \eta^{4}(1 + \mu^{2} - 6\mu \eta +6\eta^{2}) + b_{0}(-2\mu \eta +2\eta^{4}). \end{array} \tag{69d}
$$

By analyzing the monotonicity of  $H_{i}$ $i = 1,2,3,4$  and comparing their minima with respect to  $\eta \in \Omega_{0}^{t + 1}\cup \Omega_{1}^{t + 1}$  it can be found that  $H_{1} - H_{4}$  are positive with  $\eta \in \Omega_{0}^{t + 1}\cup \Omega_{1}^{t + 1}$  Therefore, the second derivative of  $\Phi_n^t$  is positive in  $\eta \in$ $\Omega_0^{t + 1}\cup \Omega_1^{t + 1}$  . This concludes this proof.