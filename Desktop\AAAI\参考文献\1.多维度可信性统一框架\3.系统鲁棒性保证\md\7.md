# Learning Critically: Selective Self Distillation in Federated Learning on Non-IID Data

Yu<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON> <PERSON><PERSON>, and <PERSON>tract—Federated learning (FL) enables multiple clients to collaboratively train a global model while keeping local data decentralized. Data heterogeneity (non- IID) across clients has imposed significant challenges to FL, which makes local models re- optimize towards their own local optima and forget the global knowledge, resulting in performance degradation and convergence slowdown. Many existing works have attempted to address the non- IID issue by adding an extra global- model- based regularizing item to the local training but without an adaption scheme, which is not efficient enough to achieve high performance with deep learning models. In this paper, we propose a Selective Self- Distillation method for Federated learning (FedSSD), which imposes adaptive constraints on the local updates by self- distilling the global model's knowledge and selectively weighting it by evaluating the credibility at both the class and sample level. The convergence guarantee of FedSSD is theoretically analyzed and extensive experiments are conducted on three public benchmark datasets, which demonstrates that FedSSD achieves better generalization and robustness in fewer communication rounds, compared with other state- of- the- art FL methods.

Index Terms—Federated Learning, Knowledge Distillation, Non- identically Distributed, Deep Learning, Catastrophic Forgetting

# 1 INTRODUCTION

NowADAYs, privacy protection has attracted increasing regulations such as the General Data Protection Regulation (GDPR) [1]. The data collected by different devices or organizations cannot be gathered in a centralized server due to privacy concerns and unreliable network transmission, forming these distributed data consisting of multiple "data silos". Federated learning (FL) has been proposed to cope with the "data silos" dilemma, which enables clients to collaboratively train a generalized and robust model while keeping their local data decentralized. Most existing FL algorithms follow the procedure of FedAvg [2], as shown in Fig 1. In each communication round, the server sends the global model to the clients which participate in the federation. Then, these clients locally update the models using their private data and send the optimized models back to the server. Finally, the server aggregates the local models to update the global model. The above procedure is repeated until the global model converges.

One of the key challenges that distinguish Federated Learning from traditional distributed learning is the heterogeneity of data distribution across the clients, also known as non- independent and identically distributed (non- IID). The heterogeneous data not only makes the theoretical analysis difficult [3], [4], but also leads to performance degradation and convergence slowdown [5], [6]. Specifically, when each client trains a local model on biased data, it will be re- optimized towards the local optima and deviate from the global objective. This causes a drift in the client local updates, which is called "Client- drift" [7]. Moreover, the server averages the divergent local models, causing the updated global model to deviate from the global objective's optimum.

![](images/f71975e1af05c9fb09a6d1a86a628b33e9e92b9f37c9805bc8bae177e07688c1.jpg)  
Fig. 1: The framework of FedAvg.

To address such non- IID issue in FL, a wealth of literature has been proposed. FedProx [8] directly limited the local updates by adding a L2 regularization term to the local objective; MOON [9] proposed a model- contrastive loss to control the local updates, which utilized the similarity between the global model representation and the representations of local models; Making an analogy with Continual Learning (CL), FedCurv [10] added a penalty term to the local objective to prevent the important parameters of the global model from changing too much. However, these works regularize the local updates without adaptive adjustments to the performance of the global model, which

are not efficient enough to achieve good performance for deep learning models and on more heterogeneous data.

Based on the empirical observations that local models tend to overfit local data and hence forget the global knowledge. An intuitive strategy is to utilize the prediction of the global model to regularize local models, making local models preserve the knowledge that the local distributions cannot represent. However, unlike the teacher in traditional knowledge distillation, which is a high- capacity model capable of achieving high accuracy, the teacher (global model) in FL cannot extract feature representation well in each round and for each class, especially in the early communication rounds and for some hard classes. The credibility of the distilled knowledge varies with samples, classes and communications rounds.

In this paper, we propose Selective Self Distillation in FL (FedSSD), which alleviates knowledge forgetting issue by selectively self- distilling the knowledge of the global model into the local models. We regard local models as students to critically learn the feature representations from the global model (teacher), aiming at preserving local models' global knowledge while avoiding the misleading of the global model. More specifically, we measure the credibility of the distilled features from the global model in two levels: sample level and class level. The class level credibility is estimated by the global model's performance on the auxiliary dataset in the server and then sent to each selected client as a credibility matrix. The sample level credibility is estimated by the global model's probability for the true class on the local samples. During the local training, the local model selectively distills the global feature representation on local data in consideration of the two levels. Our extensive experiments demonstrate that FedSSD can accelerate the FL training process and improve the generalization ability by alleviating catastrophic forgetting, especially on extremely heterogeneous data and for deep learning models.

Our main contributions are summarized as follows:

We study local models' catastrophic forgetting of the global knowledge in federated learning, which causes performance degradation and convergence slowdown. We suggest that knowledge of the global model is of different credibility between samples and classes and a naive distillation strategy is not appropriate to the FL scenario. We propose a novel FL algorithm, FedSSD, to effectively mitigate the local update drift and catastrophic forgetting by selectively self- distilling the knowledge of the global model into local models. We analyze the advantages of FedSSD by performing experiments and related visualizations that show the superiority of FedSSD in terms of classification accuracy and convergence rate on several benchmark datasets.

Note that, this article is an extension of our earlier publication [11]. Our changes include method improvement and experiment enrichment. First, this article proposes a new technique for non- IID issue in FL. Our previous work [11] adaptively controls the degree of self- distillation according to the category of training samples. In addition to [11], we analyze several aspects that need to be considered in the weight control of self- distillation in Sec. 4.2. Different from [11], FedSSD applies the class- wise distillation weighting on the logits- channel level and also considers the distilling credibility on the sample level. With the new technique, we achieve a performance higher than our original submission. Second, we conduct more experiments and related analysis in Sec. 5. We provide additional comparison results on the cases of changing client sample ratios, changing number of local epochs and different datasets. Ablation study explores the key properties of FedSSD.

The rest of this paper is organized as follows: First, we review the related work in Section 2. Then, we introduce the motivation for our work in Section 3. In Section 4, we provide the detailed algorithm of our method FedSSD and mathematically analyze the convergence. In Section 5, extensive experimental results are reported and analyzed, before concluding in Section 6.

# 2 RELATED WORK

# 2.1 Federated Learning

Federated learning (FL) is first proposed by [2] to address privacy concerns as a distributed machine learning paradigm. A key challenge in federated learning is that the data are usually non- identically distributed (non- IID) across different clients in the real world. Recently, considerable studies have been proposed to address the non- IID problem, including improvements on the local training phase and server aggregation phase. Our work focus on the first one.

In the literature on improving the local training phase to tackle client drift, regularization terms are commonly used to impose constraints on updating the local model. FedProx [8] adding a proximal term in the local objective to limit the L2 distance of the local model and current global model. Although the proximal term pulls the local model backward closer to the global model and can slightly mitigate objective inconsistency, it slows down the convergence rate [12]. SCAFFOLD [7] introduces control variates to estimate the update direction of the local model and global model. Then, the difference between these two variates is used to correct the client drift. As each client needs to send the updated model and the updated control variates, the communication burden of SCAFFOLD is doubled compared with FedAvg. Recent work MOON [9] empirically showed that FedProx and SCAFFOLD fail to achieve good performance on image datasets with deep neural networks. To address these issues, MOON utilizes the similarities between feature representations of the local model and global model to perform contrastive learning in the model level. However, we find that not all the representations learned by the global model are useful for local training. FedCurv [10] is motivated by Continual Learning(CL), using Elastic Weight Consolidation [13] to alleviate the forgetting issue in FL. It can prevent the important parameters of the global model from changing too much by adding a penalty term to the local objective. However, it estimates parameter importance by the diagonal of the empirical Fisher Information Matrix in the clients and sends them to the server, which brings 2.5 times communication costs compared with FedAvg.

As for the studies on improving the server aggregation phase, some works have proposed a layer- wise aggregation

strategy to adapt to data heterogeneity, applying Bayesian nonparametric to match and average the parameters. For instance, instead of averaging the parameters weight- wise without considering the meaning of each parameter, PFNM [14] and FedMA [15] use the Beta- Bernoulli Process for matching parameters. Specifically, FedMA is an improved version of PFNM which extends the matching strategy from fully connected layers to CNNs and LSTMs. Another line of work tries to adjust the aggregation weights, IDA [16] calculates the inverse distance to re- weight aggregation and FedNova [12] use normalized local model updates when averaging. These methods are orthogonal to the above methodologies which improve in the local training phase and can be coupled with each other.

Recently, personalized federated learning has attracted significant interest from researchers [17], [18], [19], which tries to train personalized local models for each client. In this paper, we study conventional federated learning, with the goal of training a single generalized and robust model for all clients.

# 2.2 Knowledge Distillation

Knowledge Distillation (KD) is proposed to transfer knowledge from a large teacher model to a small student model [20], which is widely used for model compression [21], [22] as well as to reduce the generalization errors in teacher models (i.e., self- distillation) [23], [24]. The knowledge to be distilled is not only the soften softmax probability of the teacher [20], but also the hidden feature vector of the teacher's penultimate layer output [25], [26], [27]. Recently, some studies theoretically demonstrate the reasons for the superiority of KD, understanding how distillation is beneficial to the student network and when distillation helps [28], [29], [30]. Several studies investigated the effects of a noisy teacher. The idea of soften [31] and selective [32] distillation have been proposed to avoid the harmful distilling caused by the inaccuracy of the teacher model in class level and sample level, respectively. [33] and [34] do it in checkpoint level and model level. Different from prior work, we consider the credibility of knowledge more comprehensively by taking all these factors into account and further going to the class level. [35] theoretically and empirically investigate Mean Squared Error (MSE) loss between the student's logits and teacher's logits better than the Kullback- Leibler divergence (KL) loss between the softened softmax probability of student and teacher. Based on these findings, we proposed weighted MSE loss between the student's logits and teacher's logits in sample level and class level.

# 2.3 Knowledge Distillation in FL

Knowledge Distillation in federated learning has recently emerged as an effective approach to tracking data heterogeneity. Numerous related works study ensemble distillation, i.e. distilling the knowledge from the ensemble of teachers (local models) to a student (global model). In Federated distillation (FD) [36], [37], clients share the model output parameters (logits) as opposed to the model parameters (weights or gradients) to reduce the communication costs. Then, the averaged logits are used to regularize local training. FedMD [38] and Cronus [39] use public dataset to get the averaged logits per sample. FedDF [40] use an unlabeled dataset in the server to aggregate knowledge from all received local model. Furthermore, the above methods can deal with model heterogeneity and each client can design a unique model.

Instead of treating the ensembles of local models as teachers and transferring the knowledge into the global model, FedNTD and FEDGKD [41], [42] regard the global model as a teacher and self- distill the global model's prediction during the local training phase to preserve global knowledge. However, the above- mentioned methods do not consider the global model as a teacher in FL cannot extract feature representation well in each round and for each class. To this end, our previous method FedCAD [11] adaptively controls the degree of self- distillation according to the according to the category of training samples.

# 3 MOTIVATION

Under the non- IID data scenarios, each local data distribution will not be representative of the overall global data distribution. In the local training phase, the local model steps towards the local optima and tends to forget the global knowledge, which has been verified in [43]. Moreover, [9] verifies that the aggregated global model extracts better feature representations than the local models. In this paper, we further verify the catastrophic forgetting issue from another perspective and observe that the global model learns different representations for different classes by an experimental study.

# 3.1 Forgetting the Global Knowledge

We compare the status of FL learning with data distributed to clients of different heterogeneity levels to illustrate the existence of catastrophic forgetting. We choose two data partitioning strategies  $\# K = k$  and  $Dir(\delta = 0.5)$  (see Section 5.1 for the detailed settings) on CIFAR10 to simulate the non- IID data scenario. In each round  $t$ , we record the performance of the global model on the uniform test dataset  $Acc_G^t$  and the average test accuracy of the updated local models  $Acc_L^t$ . As shown in Fig. 2a, we empirically observe that the values of  $Acc_G^t$  are significantly larger than  $Acc_L^t$  (a dot in the figure means one round) in the most rounds, indicating that part of the global knowledge has been forgotten during the local training phase. On the contrary, when the values of  $Acc_G^t$  smaller than  $Acc_L^t$ , indicating the local models learn new knowledge from the local data while preserving the global knowledge. It is analogous to the stability- plasticity dilemma in continual learning, where the learning methods must strike a balance between retaining knowledge from previous tasks and learning new knowledge for the current task [44]. The forgetting issue causes the waste of learned knowledge, which reduces the learning efficiency and results in a large performance degradation. Furthermore, we show the difference  $Acc_G^t - Acc_L^t$  in Fig. 2b, noting that the difference increases as the heterogeneity level increases.

# 3.2 Global Model Mis-Prediction

Although the global model learns a better representation than the local model [9], we conjecture that the global

![](images/fbc6a10ed63ceb42a2f964f0cbff0623eecbef6e58d20ce50cc6ea5908f7c02a.jpg)  
Fig. 2: The catastrophic forgetting issue on non-IID CIFAR10. Here,  $Acc_{G}$  and  $Acc_{L}$  denote the global test accuracy and the average local test accuracy, respectively.

![](images/851650e405f3d99482fe148386710802ca873a6bc24357c638df83164aaaac5c.jpg)  
Fig. 3: The class-wise test accuracy of the global model and the local model on CIFAR10.

model perhaps cannot extract representation well at each round, on each class or sample. To verify our conjecture, we further visualize the class- wise test accuracy of the global model  $Acc_{G}^{t}$  and the local model of "client  $0^{\prime \prime}Acc_{0}^{t}$  (whose data distribution is shown in the first column of Fig. 6) in each round. Here, we denote  $Acc_{0}^{t}(k)$  as the class  $k$  's accuracy of the local model  $w_{0}^{t}$  in round  $t$ , and similarly  $Acc_{G}^{t}(k)$  denotes the accuracy of class  $k$  of the global model  $w^{t}$ . As shown in Fig. 3, the global model's reliability on different classes varies during the training processes. The performance of the global model in some classes is much worse than the local model, especially in the local majority class 5 and 9.

To summarize, when the data are non- IID across clients, the local models suffer from catastrophic forgetting of the knowledge of previous training data, due to the discrepancy between local data distribution and global data distribution. Furthermore, the global model cannot perform as well as the local model on locally owned classes.

# 3.3 Critically Learn from the Global Knowledge

Knowledge Distillation is one of the ways to keep the representations of previous data from drifting too much while learning new tasks. It is intuitive to guide the local training by the global knowledge, which is represented in the soften logits of the global model on local data. As discussed above, naively applying a constant weighted distilling loss is not a wise strategy. The reliability of the logits of the global model varies with training rounds, samples and classes. The impact of distilling should grow with the convergence of the global model. On the samples accurately predicted by the global model, the global knowledge tends to be more reliable. What's more, the global model's capability of extracting features of different classes may be different. Thus the logits of different class channels could be of different reliability. In this paper, we design a selective distilling scheme to take those factors into consideration when applying self- distilling in the local training phase. We visualize a randomly selected client's (client  $\mathrm{ID} = 8$  confusion matrix on non- IID  $Dir(\delta = 0.5)$  CIFAR10 to show the superiority of FedSSD. As shown in Fig. 4, after local training, selective distilling could not only preserves the global knowledge on the classes  $\{1,2,6,7\}$ , but also learns local knowledge on the classes  $\{0,3,4,5,8\}$ . Therefore, FedSSD avoids the waste of knowledge caused by forgetting global knowledge and improves the efficiency of FL.

![](images/51b033bce46f2a4d06ec8684ac28e3332491fd24a3470779770c10c27e2fdbae.jpg)  
Fig. 4: Examples of confusion matrices for training on local biased data on CIFAR10.

# 4 METHODOLOGY

Based on the above experimental results and analysis, we propose a simple yet effective federated learning method with Selective Self Distillation (FedSSD). Since the catastrophic forgetting of the global knowledge occurs during the local update, FedSSD minimizes the discrepancy between the global model's and the local model's classifiers while learning local knowledge. In the following, we start with the formulation of heterogeneous federated learning and knowledge distillation. Then, we describe our critical learning strategies and the complete learning procedure. At last, we give a theoretical analysis of our method.

# 4.1 Problem Formulation

We consider there exist  $N$  clients, which are all connected to a central server. Each client  $i$  has a private local dataset  $D_{i}$ , with no data sharing between clients. The goal of our work is to train a generalized model  $w$  to adapt to the local and global data distribution while keeping the generalization

![](images/2da1aa7279724a47384faa58e4bf5f3d0f73ef28486f32e05529efc383764ebd.jpg)  
Fig. 5: An overview of FedSSD in the heterogeneous setting.

ability. More formally, federated learning can be formulated as the following optimization problem:

$$
\min_{w}\mathcal{L}(w) = \sum_{i = 1}^{N}q_{i}\mathcal{L}_{i}(w),\quad q_{i} = |D_{i}| / \sum_{j = 1}^{N}|D_{j}| \tag{1}
$$

where the global objective function  $\mathcal{L}(w)$  is the weighted average of the local objectives  $\mathcal{L}_i(w)$  over  $N$  clients. The balancing weight  $q_{i}$  is typically set as proportional to the sizes of the local dataset  $|D_{i}|$ . In general, the local objective denotes the empirical risks on possibly heterogeneous data distribution  $D_{i}$ , i.e.  $\mathcal{L}_i(w) \coloneqq \mathbb{E}_{(x,y) \sim D_i} \left[ l_i(w_i, (x, y)) \right]$ , where  $l_i$  measures the sample wise loss between the prediction of the network parameterized by  $w_i$  and the ground truth label  $y$  when given the input image  $x$ .

# 4.2 Federated Selective Self distillation

Compared to the global model, local models tend to overfit local datasets and forget global knowledge. To counteract such forgetting and raise the efficiency of federated learning, distilling knowledge from the global model to local models [41], [42] and parameters regularization with the global model [8] are proposed. However, it is not proper to distill knowledge from the global model to local models using a constant coefficient for the distilling loss term. The reliability of the predictions of the global model grows with the convergence of the global model. Distilling may mislead local models and even decrease the efficiency of federated learning at an early stage when the global model performs poorly.

The global model's performance in extracting features of different classes may vary due to the imbalanced data distribution of the clients. Assume that the global model is aggregated by the clients trained on skewed datasets, thus it is well- trained to extract features of the majority classes of the previously picked clients' local data and performs well on them. The output logits of the global model may be precisely estimated in channels of these classes. However, the logits in the minority classes' channels are less creditable since the aggregated local models do not learn rich features about these classes.

What's more, the reliability of logits is also related to the specific training sample. In the example above, the logits on the majority class channels of the samples belonging to the majority classes are relatively more creditable on account of the accurate predictions of the global model on samples of those classes. However, it is not guaranteed that logits on these channels are still reliable for the samples of the minority classes. The global model may fail to analyze the features of the samples and predict inaccurate logits.

Traditional distillation with KL divergence [20], where the predictions of the global model on each class channels share the same scale of impact, is not proper for such circumstances. The global knowledge is represented in the output logits of the global model on local data. Logits could be regarded as absolute estimations for classes. Passing through a softmax layer, logits are converted to the predicted relative probability of the samples belonging to each class. To disentangle the predictions of the global model on different classes, we choose logits matching with L2 norm loss in distillation rather than the KL- divergence loss for aligning the probability distributions. We estimate the reliability of logits at each channel by the recall rate and the degree of inductive bias of the corresponding class on the auxiliary dataset. A logit of class channel  $k$  is more reliable when the global model could fully extract the features of class  $k$  and incorporate them into the probability estimation, which could be reflected in a higher recall rate of class  $k$ . What's more, the global model should have a less inductive bias for the channel  $k$ , which is reflected in a less rate of mistaking other classes for  $k$ . The reliability of logits on each sample is measured by the predicted probability for the true class. When the global model fails to extract and analyze features of a sample, the logits are generally imprecise and should be given less weight for distilling.

We next formulate this mathematically. In each communication round  $t$ , we evaluate the credibility matrix (also called as confusion matrix) of the global model on the auxiliary dataset  $D_V$  in the server and send it to the online clients in current round. We define the credibility matrix  $A^t \in \mathbb{R}^{K \times K}$  of the global model as follows:

$$
A^t = \mathcal{P}(w^t,D_V) \tag{2}
$$

where  $\mathcal{P}$  function denotes the performance of the global model with parameters  $w^{t}$  on the auxiliary data  $D_{V}$  and  $K$  is the number of classes. More specifically,  $A_{k_1,k_2}$  denotes the probability that the global model predicts the class  $k_{1}$  as the class  $k_{2}$ .

Suppose client  $i\in S^t$  initializes its local model  $w_{i}^{t}$  with the global model  $w_{g}^{t}$  and trains on the local data  $D_{i}$ . Then, it optimizes its local objective by running Stochastic Gradient Descent (SGD) for  $E$  local epochs to get the updated local model  $w_{i}^{t}$ . For every input  $t$ , we denote the output logits vector of the global model and the local model as  $z^{g} = f(w_{g}^{t},x)\in \mathbb{R}^{K}$  and  $z = f(w_{i}^{t},x)\in \mathbb{R}^{K}$ , respectively. Similar to [35], we directly computes the mean squared error (MSE) between the global model logits and the local model logits. The selective self- distillation loss is defined as follows:

$$
\mathcal{L}_{SSD,i} = \mathbb{E}(\| M\odot z^g -M\odot z\| _2^2) \tag{3}
$$

where  $M\in \mathbb{R}^{K}$  is the class- wise weights vector and  $\odot$  is element- wise multiplication. As discussed above,  $M$  is related to (1) sample level: the prediction performance of the global model on each sample and (2) class level: the reliability of the logits on each class channel. Suppose the sample  $x^{*}$  label is  $k_{2}$  and the  $k_{1:k_h}$  value of  $M(x)$  is determined as:

$$
\begin{array}{rl} & M(x)[k_1] = M_{max}\cdot [M_{class}[k_1]M_{sample}(x) - 0.1]^+\\ & \qquad M_{class}[k_1] = A_{k_1,k_1}(1 - \max_{k\neq k_1}A_{k,k_1})\\ & \qquad M_{sample}(x) = 1 - (1 - p^g (x)[k_2])^{0.5} \end{array} \tag{4}
$$

where  $\begin{array}{r}\big[*\big]^{+} = \frac{(\ast\ast\ast)}{2};A_{k_1,k_1} \end{array}$  is the recall rate on class  $k_{1};$ $A_{k,k_1}$  is the rate of mistaking class  $k$  for  $k_{1}$  and  $p^g (x)[k_2]$  is the global model's predicted probability for the true class  $k_{2}$ $M_{max}$  decides the upper bound of the distillation impact.  $M_{class}[k_1]$  measures the credibility of the global locit on channel  $k_{1}$  . Credible distillation on the channel  $k_{1}$  requires the sufficient feature extraction and less inference bias on class  $k_{1},$  which can be reflected in  $M_{class}[k_1]$  and  $(1 - \max_{k\neq k_1}A_{k,k_1})$  respectively.  $M_{sample}(x)$  measures the credibility of sample  $x$  by mapping the global probability on the ground truth label to  $[0,1]$  increasingly.

The classification loss  $\mathcal{L}_{CE}$  is the softmax cross- entropy loss between the local model probability and the one- hot true labels  $y$ , which is computed as follows:

$$
\mathcal{L}_{CE,i} = \frac{1}{|D_i|}\sum_{x\in D_i}\sum_{k = 1}^{K} - y_k\log [p_k(x)] \tag{5}
$$

Consequently, the overall loss is a combination of the cross- entropy loss  $\mathcal{L}_{CE}$  and the self- distillation loss  $\mathcal{L}_{SSD}$  as follows:

$$
\mathcal{L}_i = \mathcal{L}_{CE,i} + \mathcal{L}_{SSD,i} \tag{6}
$$

The framework of FedSSD is shown in Fig. 5 and the detailed algorithm is outlined in Algorithm 1. In the beginning, the server initializes the parameters of the global model  $w^{0}$  randomly. Then, it runs the outer loop in Algorithm 1 for  $T$  communication rounds. In each round  $t$ , the server evaluates the global model on the auxiliary data  $D_{V}$  to get the credibility matrix. Then, it samples  $C*N$  clients to form the sampled set of clients  $S_{t}$  for receiving updates. Next, Input:  $N$  clients' datasets  $\{D_{i}\}_{i = 1}^{N}$ , auxiliary dataset at the server  $D_{V}$ , total communication rounds  $T$ , clients sample ratio  $C$ , local epochs  $E$ , learning rate  $\eta$ , minibatch size  $b$ .

# Algorithm 1 FedSSD: Selective Self-Distillation in FL

# Output: The final global model  $w^{T}$

1: Initialize the global model  $w^{0}$  in the server 2: for  $t = 0,\dots,T - 1$  do 3:  $A^{t} = \mathcal{P}(w^{t},D_{V})$  Eq.(2) 4:  $S_{t}\gets$  Randomly sample a set of  $C\times N$  clients 5: for  $i\in S_t$  in parallel do 6:  $w_{i}^{t}\gets \mathbf{C}\mathbf{l}\mathbf{i}\mathbf{e}\mathbf{n}\mathbf{t}\mathbf{U}\mathbf{p}\mathbf{d}\mathbf{a}\mathbf{t}\mathbf{e}(i,w^{t},A^{t})$  7: end for 8:  $w^{t + 1}\gets \frac{1}{|D_{S_t}|}\sum_{i\in S_t}|D_i|w_i^t$  9: end for 10: return  $w^{T}$

# ClientUpdate:  $(i,w^{t},A^{t})$

1:  $w_{i}^{t}\gets w^{t}$  2: for epoch  $e = 1,2,\dots E$  do 3: for batch  $b = \{x,y\} \in D_i$  do 4:  $\begin{array}{rl} & {\mathcal{L}_{SSD,i}\gets \frac{1}{|D_i|}\sum ||M\odot z^g - M\odot z||_2^2}\\ & {\mathcal{L}_{CE,i}\gets \mathcal{C}rossEntropyLoss(f(x),y)}\\ & {\mathcal{L}_i\gets \mathcal{L}_{CE,i} + \mathcal{L}_{SSD,i}}\\ & {w_i^t\gets w_i^t - \eta \nabla \mathcal{L}(w_i^t,b)} \end{array}$  5: 6: 7: 8: end for 9: end for 10: return  $w_{i}^{t}$  to the server

the server sends the model parameters  $w^{t}$  and credibility matrix  $A^{t}$  to the clients in  $S_{t}$ . After receiving the parameters and the credibility of the global model, each client  $i\in S^{t}$  executes the inner for loop in Algorithm 1. First, the client  $i$  initializes its local model  $w_{i}^{t}$  with the received global model parameters  $w^{t}$ . Then, it updates the local model by minimizing its local objective function on its local dataset  $D_{i}$  and in the meanwhile minimizing the discrepancy between the global model's and student's logits. During the local training phase, the global model is frozen and local models update  $E$  epochs by the SGD algorithm. Once the server receives all updates from the clients in  $S^{t}$ , it aggregates model parameters by a weighted average of the number of samples.

# 4.3 Convergence Analysis

We give the convergence analysis in this section, stating the bounded dissimilarity, Lipschitz smooth assumptions about the local objective function and Lipschitz continuity assumption about logits function, which are similar to existing literature [4], [8], [12], [45]. We introduce the following notations before formally stating the convergence result.  $[N]$  denotes  $\{1,2,\dots,N\}$  for any positive integer  $N$ .  $x,f$  represent the input data and the logits function, respectively.  $\mathcal{L}$  denotes the global objective which is the weighted sum of local objectives  $\mathcal{L}_{i}$ .

# Assumption 1.

1 (Bounded dissimilarity) For each client and any parameter  $i\in [N]$ ,  $w\in \mathbb{R}^{W}$ , there exists a constant  $B > 0$  such that  $\mathbb{E}_{i}[||\nabla \mathcal{L}_{i}(w)||^{2}]\leq ||\nabla \mathcal{L}(w)||^{2}B^{2}$ .

If the local objective functions are identical to each other, then we have  $B^{2} = 1$ .

2 (L- Lipschitz smooth) For each local objective function  $\mathcal{L}_i$  is Lipschitz smooth, there exists a constant  $L_{F} > 0$  such that  $\| \nabla \mathcal{L}_i(w) - \nabla \mathcal{L}_i(w')\| \leq$ $L_{F}||w - w^{\prime}||$  . We further assume that  $\nabla^2\mathcal{L}_i(w)$  is lower bounded by a constant  $L_{m}$  multiplied with an identity matrix. 3 (L- Lipschitz continuity) For each local logits function  $f$  is Lipschitz continuity, there exists a constant  $L_{f}>$  O such that  $||f(w,x_i) - f(w',x_i)||< \sqrt{2\bar{L}_f} ||w - w'||$

If the local objective functions are identical to each other, then we have  $B^{2} = 1$ .2 (L- Lipschitz smooth) For each local objective function  $\mathcal{L}_i$  is Lipschitz smooth, there exists a constant  $L_{F} > 0$  such that  $\| \nabla \mathcal{L}_i(w) - \nabla \mathcal{L}_i(w')\| \leq$ $L_{F}||w - w^{\prime}||$ . We further assume that  $\nabla^2\mathcal{L}_i(w)$  is lower bounded by a constant  $L_{m}$  multiplied with an identity matrix.3 (L- Lipschitz continuity) For each local logits function  $f$  is Lipschitz continuity, there exists a constant  $L_{f} > 0$  such that  $\| f(w,x_i) - f(w',x_i)\| < \sqrt{2\bar{L}_f} ||w - w'||$ .

Lemma 1. Define  $\tilde{\mathcal{L}}$  as the following:

$$
\begin{array}{rl} & {\mathcal{L}(w,w^t)}\\ & {= \mathcal{L}_{CE}(w) + \frac{1}{2|D_i|}\sum_{j = 1}^{|D_i|}|M\odot (f(w,x_{i,j}) - f(w^t,x_{i,j}))||^2}\\ & {\leq \mathcal{L}_{CE}(w) + \frac{1}{2|D_i|}\sum_{j = 1}^{|D_i|}M_{max}^2 ||f(w,x_{i,j}) - f(w^t,x_{i,j})||^2}\\ & {\leq \mathcal{L}_{CE}(w) + \frac{1}{|D_i|}\sum_{j = 1}^{|D_i|}M_{max}^2 L_f^2 ||w - w^t||^2}\\ & {= \mathcal{L}_{CE}(w) + M_{max}^2 L_f^2 ||w - w^t||^2}\\ & {\eqqcolon \tilde{\mathcal{L}} (w,w^t)} \end{array}
$$

where the first inequality holds because  $M$  is upper bounded by  $M_{max}$  and the second inequality follows from Assumption 1.3. For any  $w_{i}^{t + 1}$  satisfies  $\tilde{\mathcal{L}} (w_i^{t + 1},w^t)\leq$ $\tilde{\mathcal{L}} (w^t,w^t)$  , then we get the following relationship:

$$
\mathcal{L}(w_i^{t + 1},w^t)\leq \tilde{\mathcal{L}} (w_i^{t + 1},w^t)\leq \tilde{\mathcal{L}} (w^t,w^t) = \mathcal{L}(w^t,w^t)
$$

which implies that a solution optimizing  $\tilde{\mathcal{L}}$  is also a solution optimizing  $\mathcal{L}$ .

Assumption 2. At round  $t$ , the optimized parameter  $w_{i}^{t + 1}$  satisfies that:

$$
||\nabla \mathcal{L}_{CE,i}(w_i^{t + 1}) + M_{max}L_f(w_i^{t + 1} - w^t)||\leq \eta ||\nabla \mathcal{L}_i(w^t)|
$$

where the learning rate  $\eta \in [0,1)$ .

The Assumption 2 is well- posed based on Lemma 1, which implies the intersection of the solution space for  $\tilde{\mathcal{L}}$  and  $\mathcal{L}$  are not empty.

Theorem 1. (Non- convex FedSSD convergence). Let Assumption 1 and Assumption 2 hold. Suppose in each round

$t$ , a set of  $|S^{t}| = S$  clients are chosen. If  $M_{max}$ ,  $S$  and  $\eta$  are chosen such that

$$
\begin{array}{rl} & {\rho = (\frac{1}{M_{max}L_f} -\frac{\eta B}{M_{max}L_f} -\frac{B(1 + \eta)\sqrt{2}}{(M_{max}L_f + L_m)\sqrt{S}}}\\ & {-\frac{L_FBB(1 + \eta)}{(M_{max}L_f + L_m)M_{max}L_f} -\frac{L_FB(1 + \eta)^2B^2}{2(M_{max}L_f + L_m)^2}}\\ & {-\frac{L_FBB^2(1 + \eta)^2}{(M_{max}L_f + L_m)^2S} (2\sqrt{2S} +2)) > 0} \end{array}
$$

# 2.  $M_{max}L_{f} + L_{m} > 0$

Then we have the following expected decrease in the global objective:

$$
\mathbb{E}_{S_t}[\mathcal{L}(w^{t + 1})]\leq \mathcal{L}(w^t) - \rho ||\nabla \mathcal{L}(w^t)||^2
$$

The theorem derives from the Theorem 4. in [8]. Thus, convergence can be guaranteed when there is a certain expected one- round decrease, which can be achieved by choosing appropriate  $M_{max}$  and  $\eta$ .

# 5 EXPERIMENTS

In this section, we extensively evaluate our method to demonstrate that critically learning from the global model accelerates the federated learning progress with alleviated knowledge forgetting in the local training phase. Our selective self- distillation strategy, FedSSD, outperforms other methods on non- IID datasets in terms of performance and convergence speed. We design the experiment like [9]. The implementation details and extended experimental results are provided as follows.

# 5.1 Datasets and Experimental Settings

Datasets. We evaluate our method on three image datasets: CIFAR10, CIFAR100 [46], TinyImageNet [47]. We choose two data partitioning strategies to simulate the non- IID data distribution, which is inspired by [48]. One strategy Quantity- based label imbalance randomly assigns  $k$  different labels to each client and we use  $\# K = k$  to denote it. The other strategy Distribution- based label imbalance allocates a proportion of the samples of each class to each client according to Dirichlet distribution. Specifically, we sample  $p_k \sim \text{Dir} (\delta)$  and allocate a proportion  $p_{k,i}$  of the instances of class  $k$  to client  $i$ , where  $\delta$  is the concentration parameter controlling the uniformity between clients and a bigger  $\delta$  indicates a more uniform distribution. With the above partitioning strategy, each client only owns a partial class set. An example of the data distributions among clients is shown in Figure 6. The auxiliary dataset at the server  $D_V$  is a small subset of samples of different classes, which can be acquired from the public data. In our experiments, we sample auxiliary data according to the strategy from [49], using only 64 samples for each class.

Models. For CIFAR10, we follow the same network architecture as FedAvg: two  $5 \times 5$  convolution layers (the first with 6 channels and the second with 16 channels, each followed by a ReLU activation and  $2 \times 2$  max pooling), two fully connected layers with ReLU activation (the first

![](images/fec75dfc613ec0b84c62ed9aead3998504dd68f4b373794a098db46040ef1dea.jpg)  
Fig. 6: Visualization of statistical heterogeneity among clients, where the x-axis indicates client IDs, the y-axis indicates class labels and each rectangle indicates the number of samples held by one client for each particular class.

![](images/d6bbce395958b09e2342e071a14791c6f90f0cbb457a3995c6d62277cec58b7f.jpg)  
Fig. 7: The learning curves on the benchmark datasets with default settings. The global test accuracy is the performance of the global model on the test dataset. The average local test accuracy is the average of the respective performance of the local models on the same test dataset.

with 120 units and the second with 84 units) and a final softmax output layer. For CIFAR100 and TinyImageNet, we use ResNet50 [50] instead.

Configurations. Unless otherwise mentioned, we run 100 rounds on CIFAR10/100 and 30 rounds on TinyImageNet, with 10 clients  $N = 10$  and all fixed clients are selected  $C = 1$  in each communication round to eliminate the effect of randomness brought by client sampling [2]. We use distribution- based strategy  $Dir(\delta = 0.5)$  to generate the non- IID data distribution by default. In the local training phase, we use the SGD optimizer with initial learning rate 0.01 and momentum 0.9. The local epoch is set to 10 and local batch size is set to 64 by default. We implement FedSSD and the baseline methods with PyTorch [51], and train our models on RTX 3090 GPU.

Baselines. We compare the performance of FedSSD against recent state- of- the- art (SOTA) FL methods towards solving the non- IID problem, including FedAvg, FedProx, FedCurv, MOON, FedNTD and the previous work FedCAD. The best weight of proximal term  $\mu$  in FedProx and the best upper bound  $M_{max}$  of FedSSD for CIFAR10, CIFAR100, TinyImageNet are 0.01, 0.001 and 0.001, respectively. The best weight  $\lambda$  in FedCurv for CIFAR10, CIFAR100, TinyImageNet are  $10^{- 3}$ ,  $10^{- 4}$  and  $10^{- 5}$ , respectively. We use an additional projection head (2- layer MLP with output dimension 256) to achieve the better performance of MOON and the best weight  $\mu$  of model- contrastive loss for CIFAR10, CIFAR100 and TinyImageNet are 5, 1 and 1, respectively.

# 5.2 Comparison with the State-Of-The-Arts

The top- 1 test accuracy with the above default settings is shown in Table 1 and the learning curves of the global model

![](images/9265687ed4684b5f2c83c8e9431801c180c7ce6a747d14e347105657f24d8cca.jpg)  
Fig. 8: The learning curves on CIFAR10 and CIFAR100 with different client sample ratios.

TABLE 1: The top-1 test accuracy (ACC) after training the target rounds and the number of communication rounds (T) to achieve the same accuracy as running FedAvg. The best and the second best values are highlighted.  

<table><tr><td rowspan="2">Method</td><td colspan="2">CIFAR10</td><td colspan="2">CIFAR100</td><td colspan="2">TinyImageNet</td></tr><tr><td>ACC ↑</td><td>T↓</td><td>ACC ↑</td><td>T↓</td><td>ACC ↑</td><td>T↓</td></tr><tr><td>FedAvg</td><td>70.49±0.23</td><td>100</td><td>65.73±0.31</td><td>100</td><td>22.78±0.28</td><td>30</td></tr><tr><td>FedProx</td><td>72.08±0.58</td><td>55</td><td>65.79±0.05</td><td>100</td><td>22.29±0.69</td><td>25</td></tr><tr><td>FedCurv</td><td>70.40±0.74</td><td>99</td><td>65.54±0.41</td><td></td><td>22.18±0.53</td><td>16</td></tr><tr><td>MOON</td><td>71.27±0.14</td><td>62</td><td>67.32±0.74</td><td>60</td><td>23.89±0.27</td><td>16</td></tr><tr><td>FedNTD</td><td>70.99±0.24</td><td>51</td><td>65.23±0.25</td><td>-</td><td>27.17±0.42</td><td>13</td></tr><tr><td>FedCAD</td><td>72.23±0.23</td><td>35</td><td>65.15±0.21</td><td>-</td><td>25.24±0.26</td><td>12</td></tr><tr><td>FedSSD</td><td>73.38±0.46</td><td>33</td><td>66.19±0.28</td><td>48</td><td>27.44±0.19</td><td>10</td></tr></table>

and local models are shown in Fig. 7. For CIFAR10, FedSSD outperformed all the baseline methods in both final model test accuracy and convergence speed (measured in communication rounds), indicating the superiority of FedSSD in dealing with non- IID data when a shallow neural network is used. A similar conclusion can be drawn when training a deeper network on TinyImageNet, showing the robustness of FedSSD on various datasets and model architectures. For CIFAR100, note that although the final global test accuracy of FedSSD is slightly worse than MOON, the average local test accuracy and convergence speed are higher. In the first row of Fig. 7, we record the test accuracy of the global model in each communication round. It is obvious that the speed of accuracy improvement in FedSSD is almost the same as FedAvg at the beginning, due to the low credibility of the global model and knowledge distillation loss is almost close to zero. Then, it achieves a better accuracy benefit from the selective knowledge distillation loss, after the global model gains credible knowledge.

Furthermore, we report the number of communication rounds to reach the same accuracy as running FedAvg for 100 rounds on CIFAR10/100 or 30 rounds on TinyImageNet in Table 1. The results show that FedSSD significantly outperformed all the baseline methods in the convergence speed (required fewer communication rounds to reach the target accuracy), demonstrating FedSSD accelerates the con vergence by alleviating the catastrophic forgetting issue. FedCurv and FedNTD also try to alleviate the forgetting issue, but their performance is not significantly better than ours.

TABLE 2: The top-1 test accuracy with different levels of data heterogeneity on CIFAR10.  

<table><tr><td>Method</td><td>#K=2</td><td>#K=5</td><td>δ=0.1</td><td>δ=0.5</td><td>δ=10</td></tr><tr><td>FedAvg</td><td>49.64±1.64</td><td>67.82±0.28</td><td>62.18±0.54</td><td>70.49±0.23</td><td>73.76±0.27</td></tr><tr><td>FedProx</td><td>50.31±1.34</td><td>67.39±0.14</td><td>62.61±0.21</td><td>72.08±0.58</td><td>73.29±0.48</td></tr><tr><td>FedCurv</td><td>50.37±1.54</td><td>68.15±0.46</td><td>62.09±0.54</td><td>70.40±0.74</td><td>73.49±0.68</td></tr><tr><td>MOON</td><td>45.15±0.75</td><td>67.41±0.13</td><td>62.41±0.25</td><td>71.27±0.14</td><td>73.37±0.11</td></tr><tr><td>FedNTD</td><td>49.16±1.92</td><td>68.17±0.31</td><td>62.59±0.25</td><td>70.99±0.24</td><td>73.82±0.22</td></tr><tr><td>FedCAD</td><td>50.75±1.19</td><td>68.86±0.37</td><td>62.74±0.31</td><td>72.23±0.23</td><td>74.10±0.55</td></tr><tr><td>FedSSD</td><td>53.37±1.52</td><td>69.33±0.47</td><td>62.84±0.38</td><td>73.38±0.46</td><td>74.40±0.15</td></tr></table>

# 5.3 Preservation of the Global Knowledge

To evaluate the effectiveness of our method in the local training phase, we also record the average test accuracy of the local models on a test dataset obeying the global data distribution. If local models preserve the global knowledge after fitting on the biased local data, the updated local model could be generalized well on the uniform test data distribution. As shown in the second row of Fig. 7, the improved speed of local test accuracy in FedSSD is almost the same as FedAvg at the beginning. Since the global model is far from convergence and the credibility of the global model is low. Then, FedSSD achieves better accuracy than FedAvg thanks to the distillation loss after about 10 rounds, which helps local models to preserve the global knowledge and mitigate the catastrophic forgetting during local training. The result demonstrates that the selective self- distillation mechanism makes local models more generalized on the global data distribution.

# 5.4 Sensitivity Analysis

Effects of data heterogeneity. To evaluate the robustness of our method under different data heterogeneity levels, we use quantity- based  $\# K = k$  and Dirichlet distribution- based  $Dir(\delta)$  strategies to partition CIFAR10 and CIFAR100.

A bigger  $k$  and  $\delta$  indicate a more uniform distribution. Results in Table 2 show that our method consistently outperforms other baselines across different heterogeneity levels, and the trend of which is more pronounced at higher data heterogeneity. Under the quantity- based data heterogeneity  $(\# K = 2,5)$ , the performance of MOON is worse than FedAvg, while FedSSD still outperforms others. Although our method outperforms FedCurv only  $1\%$  accuracy, the communication costs of FedCurv increase to 2 times compared with our method, due to our credibility matrix is simpler than the Fisher information. The experiments demonstrate the robustness and effectiveness of FedSSD under different heterogeneity levels.

Effects of clients participation ratio. To evaluate the scalability of our method, we design the experiments with a different numbers of participating clients at each communication round on CIFAR10 and CIFAR100. Specifically, we partition CIFAR10 and CIFAR100 training datasets into 100 clients and randomly sample 10, 20 clients to participate in the training during each round. The results are shown in Fig. 8, FedSSD stably outperforms other methods even with fewer participants in each round, which indicates FedSSD has strong robustness to different client participation degrees.

Effects of local update epochs. Aggregating local models at different frequencies may affect the learning performance, since less frequent communication will further enhance the drift in the local training phase. We conduct the experiments to study the effect of local epochs on the performance of the final global model and the results are shown in Fig. 9. Intuitively, a small  $E$  may increase the communication burden and a large  $E$  may result in a low convergence rate. When the local epoch  $E = 1$ , all methods have a relatively close test accuracy due to each client communicates with others frequently and there is no "Client drift". With more local epochs are used, the client local update drifts more and the discrepancy between the local model and global model becomes larger. Both FedAvg and MOON suffer from performance drops. Nevertheless, due to our methods selectively self- distills the global knowledge into local models, FedSSD significantly outperforms other methods and shows robustness to large drift caused by more local update epochs.

![](images/21ae593ddeb2d42f491ec340dbc3153147b63b4a76ccf0bc37413835aa4965d2.jpg)  
Fig. 9: Visualized the final global model's performance with different number of local epochs on CIFAR10.

TABLE 3: The top-1 test accuracy with different loss types on CIFAR10.  

<table><tr><td>Loss Type</td><td>α = 0.01</td><td>α = 0.1</td><td>α = 0.5</td></tr><tr><td>KL</td><td>71.27</td><td>71.30</td><td>70.61</td></tr><tr><td>MSE</td><td>72.66</td><td>71.44</td><td>69.51</td></tr><tr><td>SSD</td><td>73.47</td><td>71.57</td><td>71.38</td></tr></table>

Ablation Study. We further conduct an ablation study to investigate key properties of FedSSD. We replace our distillation loss with KL- divergence distillation loss and MSE distillation loss respectively. Both two losses are controlled by a constant coefficient rather than our adaptive weights. We uniformly denote the constant coefficient and the upper bound of selective distillation loss  $M_{max}$  as  $\alpha$ . As shown in Table 3, selective distillation achieves best performance under different values of  $\alpha$ .

# 6 CONCLUSION

In this work, we propose a novel federated learning algorithm with selective self- distillation (FedSSD), to overcome the forgetting issue in the local training phase. We observe that the global model learns a better representation than local models but is not reliable for distilling in every case. FedSSD critically distills the knowledge of the global model into local models by measuring the credibility of samples and class channels of logits, which helps local models to preserve the global knowledge and meanwhile learns the knowledge from the local data. The effectiveness of the proposed method has been comprehensively analyzed from both theoretical and experimental perspectives. FedSSD can be used for non- vision problems in the future because it does not require image inputs.

# ACKNOWLEDGMENTS

This work is supported by the National Key Research & Development Plan of China No.2021YFC2501202, National Science Foundation of China No.61972383 and No. 61902377, Beijing Municipal Science & Technology Commission No.Z211100002121171, Youth Innovation Promotion Association CAS, Science and Technology Service Network Initiative, Chinese Academy of Sciences No. KFJ- 515- QYZD- 2021- 11- 001

# REFERENCES

[1] P. Voigt and A. Von dem Bussche, "The eu general data protection regulation (gdpr)," A Practical Guide, 1st Ed., Cham: Springer International Publishing, vol. 10, no. 3152676, pp. 10- 5555, 2017. [2] B. McMahan, E. Moore, D. Ramage, S. Hampson, and B. A. y Arcas, "Communication- efficient learning of deep networks from decentralized data," in Artificial intelligence and statistics. PMLR, 2017, pp. 1273- 1282. [3] A. Khaled, K. Mishchenko, and P. Richtarik, "Tighter theory for local sgd on identical and heterogeneous data," in International Conference on Artificial Intelligence and Statistics. PMLR, 2020, pp. 4519- 4529.

[4] X. Li, K. Huang, W. Yang, S. Wang, and Z. Zhang, "On the convergence of fedavg on non- iid data," in 8th International Conference on Learning Representations (ICLR), 2020. [5] Y. Zhao, M. Li, L. Lai, N. Suda, D. Civin, and V. Chandra, "Federated learning with non- iid data," arXiv preprint arXiv:1806.00582, 2018. [6] T. Li, A. K. Sahu, A. Talwalkar, and V. Smith, "Federated learning: Challenges, methods, and future directions," IEEE Signal Processing Magazine, vol. 37, no. 3, pp. 50- 60, 2020. [7] S. P. Karimireddy, S. Kale, M. Mohri, S. Reddi, S. Stich, and A. T. Suresh, "Scaffold: Stochastic controlled averaging for federated learning," in International Conference on Machine Learning (ICML), PMLR, 2020, pp. 5132- 5143. [8] T. Li, A. K. Sahu, M. Zaheer, M. Sanjabi, A. Talwalkar, and V. Smith, "Federated optimization in heterogeneous networks," in Proceedings of Machine Learning and Systems (MLSys), vol. 2, 2020, pp. 429- 453. [9] Q. Li, B. He, and D. Song, "Model- contrastive federated learning," in Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition (CVPR), 2021, pp. 10713- 10722. [10] N. Shoham, T. Avidor, A. Keren, N. Israel, D. Benditkis, L. Moryosef, and I. Zeitak, "Overcoming forgetting in federated learning on non- iid data," arXiv preprint arXiv:1910.07796, 2019. [11] Y. He, Y. Chen, X. Yang, Y. Zhang, and B. Zeng, "Class- wise adaptive self distillation for heterogeneous federated learning," in International Workshop on Trustable, Verifiable and Auditable Federated Learning in Conjunction with AAAI 2022 (FL- AAAI- 22), 2022. [12] J. Wang, Q. Liu, H. Liang, G. Joshi, and H. V. Poor, "Tackling the objective inconsistency problem in heterogeneous federated optimization," Advances in neural information processing systems, vol. 33, pp. 7611- 7623, 2020. [13] J. Kirkpatrick, R. Pascanu, N. Rabinowitz, J. Veness, G. Desjardins, A. A. Rusu, K. Milan, J. Quan, T. Ramalho, A. Grabska- Barwinska et al., "Overcoming catastrophic forgetting in neural networks," Proceedings of the national academy of sciences, vol. 114, no. 13, pp. 3521- 3526, 2017. [14] M. Yurochkin, M. Agarwal, S. Ghosh, K. Greenewald, N. Hoang, and Y. Khazaeni, "Bayesian nonparametric federated learning of neural networks," in International Conference on Machine Learning, PMLR, 2019, pp. 7252- 7261. [15] H. Wang, M. Yurochkin, Y. Sun, D. S. Papailiopoulos, and Y. Khazaeni, "Federated learning with matched averaging," in 8th International Conference on Learning Representations (ICLR), 2020. [16] Y. Yeganeh, A. Farshad, N. Navab, and S. Albarqouni, "Inverse distance aggregation for federated learning with non- iid data," in Domain Adaptation and Representation Transfer, and Distributed and Collaborative Learning. Springer, 2020, pp. 150- 159. [17] Y. Deng, M. M. Kamani, and M. Mahdavi, "Adaptive personalized federated learning," arXiv preprint arXiv:2003.13461, 2020. [18] Y. Chen, W. Lu, J. Wang, and X. Qin, "Fedhealth 2: Weighted federated transfer learning via batch normalization for personalized healthcare," arXiv preprint arXiv:2106.01009, 2021. [19] Y. Huang, L. Chu, Z. Zhou, L. Wang, J. Liu, J. Pei, and Y. Zhang, "Personalized cross- silo federated learning on non- iid data," in Proceedings of the AAAI Conference on Artificial Intelligence, vol. 35, no. 9, 2021, pp. 7865- 7873. [20] G. Hinton, O. Vinyals, and J. Dean, "Distilling the knowledge in a neural network," arXiv preprint arXiv:1503.02531, 2015. [21] W. Wang, F. Wei, L. Dong, H. Bao, N. Yang, and M. Zhou, "Minilm: Deep self- attention distillation for task- agnostic compression of pre- trained transformers," Advances in Neural Information Processing Systems, vol. 33, pp. 5776- 5788, 2020. [22] S. Sun, Y. Cheng, Z. Gan, and J. Liu, "Patient knowledge distillation for bert model compression," in Proceedings of the 2019 Conference on Empirical Methods in Natural Language Processing and the 9th International Joint Conference on Natural Language Processing. Association for Computational Linguistics, 2019, pp. 4322- 4331. [23] L. Zhang, J. Song, A. Gao, J. Chen, C. Bao, and K. Ma, "Be your own teacher: Improve the performance of convolutional neural networks via self distillation," in Proceedings of the IEEE/CVF International Conference on Computer Vision (CVPR), 2019, pp. 3713- 3722. [24] S. Yun, J. Park, K. Lee, and J. Shin, "Regularizing class- wise predictions via self- knowledge distillation," in Proceedings of the IEEE/CVF conference on computer vision and pattern recognition (CVPR), 2020, pp. 13 876- 13 885.

[25] A. Romero, N. Ballas, S. E. Kahou, A. Chassang, C. Gatta, and Y. Bengio, "Fitnets: Hints for thin deep nets," in 3rd International Conference on Learning Representations (ICLR), 2015. [26] B. Heo, J. Kim, S. Yun, H. Park, N. Kwak, and J. Y. Choi, "A comprehensive overhaul of feature distillation," in Proceedings of the IEEE/CVF International Conference on Computer Vision (CVPR), 2019, pp. 1921- 1930. [27] J. Kim, S. Park, and N. Kwak, "Paraphrasing complex network: Network compression via factor transfer," Advances in neural information processing systems, vol. 31, 2018. [28] J. Tang, R. Shivanna, Z. Zhao, D. Lin, A. Singh, E. H. Chi, and S. Jain, "Understanding and improving knowledge distillation," arXiv preprint arXiv:2002.03532, 2020. [29] H. Zhou, L. Song, J. Chen, Y. Zhou, G. Wang, J. Yuan, and Q. Zhang, "Rethinking soft labels for knowledge distillation: A bias- variance tradeoff perspective," in 9th International Conference on Learning Representations (ICLR), 2021. [30] T. Dao, G. M. Kamath, V. Syrgkanis, and L. Mackey, "Knowledge distillation as semiparametric inference," in 9th International Conference on Learning Representations (ICLR), 2021. [31] M. Lukasik, S. Bhojanapalli, A. K. Menon, and S. Kumar, "Teacher's pet: understanding and mitigating biases in distillation," arXiv preprint arXiv:2106.10494, 2021. [32] F. Wang, J. Yan, F. Meng, and J. Zhou, "Selective knowledge distillation for neural machine translation," in Proceedings of the 59th Annual Meeting of the Association for Computational Linguistics and the 11th International Joint Conference on Natural Language Processing, 2021, pp. 6456- 6466. [33] H.- R. Wei, S. Huang, R. Wang, X. Dai, and J. Chen, "Online distilling from checkpoints for neural machine translation," in Proceedings of the 2019 Conference of the North American Chapter of the Association for Computational Linguistics: Human Language Technologies, 2019, pp. 1932- 1941. [34] X. Tan, Y. Ren, D. He, T. Qin, Z. Zhao, and T. Liu, "Multilingual neural machine translation with knowledge distillation," in 7th International Conference on Learning Representations (ICLR), 2019. [35] T. Kim, J. Oh, N. Kim, S. Cho, and S.- Y. Yun, "Comparing kullback- leibler divergence and mean squared error loss in knowledge distillation," arXiv preprint arXiv:2105.08919, 2021. [36] E. Jeong, S. Oh, H. Kim, J. Park, M. Bennis, and S.- L. Kim, "Communication- efficient on- device machine learning: Federated distillation and augmentation under non- iid private data," arXiv preprint arXiv:1811.11479, 2018. [37] H. Seo, J. Park, S. Oh, M. Bennis, and S.- L. Kim, "Federated knowledge distillation," arXiv preprint arXiv:2011.02367, 2020. [38] D. Li and J. Wang, "Fedmd: Heterogenous federated learning via model distillation," arXiv preprint arXiv:1910.03581, 2019. [39] H. Chang, V. Shejwalkar, R. Shokri, and A. Houmansadr, "Cronus: Robust and heterogeneous collaborative learning with black- box knowledge transfer," arXiv preprint arXiv:1912.11279, 2019. [40] T. Lin, L. Kong, S. U. Stich, and M. Jaggi, "Ensemble distillation for robust model fusion in federated learning," in Advances in Neural Information Processing Systems, 2020. [41] G. Lee, Y. Shin, M. Jeong, and S.- Y. Yun, "Preservation of the global knowledge by not- true self knowledge distillation in federated learning," arXiv preprint arXiv:2106.03097, 2021. [42] D. Yao, W. Pan, Y. Dai, Y. Wan, X. Ding, H. Jin, Z. Xu, and L. Sun, "Local- global knowledge distillation in heterogeneous federated learning with non- iid data," arXiv preprint arXiv:2107.00051, 2021. [43] C. Xu, Z. Hong, M. Huang, and T. Jiang, "Acceleration of federated learning with alleviated forgetting in local training," arXiv preprint arXiv:2203.02645, 2022. [44] M. Mermillod, A. Bugaiska, and P. Bonin, "The stability- plasticity dilemma: Investigating the continuum from catastrophic forgetting to age- limited learning effects," Frontiers in psychology, vol. 4, p. 504, 2013. [45] M. Jiang, Z. Wang, and Q. Dou, "Harmof: Harmonizing local and global drifts in federated learning on heterogeneous medical images," arXiv preprint arXiv:2112.10775, 2021. [46] A. Krizhevsky, G. Hinton et al., "Learning multiple layers of features from tiny images," Tech Report, 2009. [47] Y. Le and X. Yang, "Tiny imagenet visual recognition challenge," CS 231N, vol. 7, no. 7, p. 3, 2015. [48] Q. Li, Y. Diao, Q. Chen, and B. He, "Federated learning on non- iid data silos: An experimental study," arXiv preprint arXiv:2102.02079, 2021.

[49] L. Wang, S. Xu, X. Wang, and Q. Zhu, "Addressing class imbalance in federated learning," in Proceedings of the AAAI Conference on Artificial Intelligence, vol. 35, no. 11, 2021, pp. 10 165- 10 173. [50] K. He, X. Zhang, S. Ren, and J. Sun, "Deep residual learning for image recognition," in Proceedings of the IEEE conference on computer vision and pattern recognition (CVPR), 2016, pp. 770- 778. [51] A. Paszke, S. Gross, F. Massa, A. Lerer, J. Bradbury, G. Chanan, T. Killeen, Z. Lin, N. Gimelshein, L. Antiga et al., "Pytorch: An imperative style, high- performance deep learning library," Advances in neural information processing systems, vol. 32, pp. 8026- 8037, 2019.