# Corrected with the Latest Version: Make Robust Asynchronous Federated Learning Possible

1st <PERSON>yi Lu\* School of Software Engineering Xi'an Jiaotong University Xi'an, China <EMAIL>

2nd Yiding Sun School of Software Engineering Xi'an Jiaotong University Xi'an, China <EMAIL>

3rd Pengbo Li International School Beijing University of Posts and Telecommunications Beijing, China <EMAIL>

4th Zhichuan Yang School of Software Engineering Xi'an Jiaotong University Xi'an, China <EMAIL>

Abstract- As an emerging paradigm of federated learning, asynchronous federated learning offers significant speed advantages over traditional synchronous federated learning. Unlike synchronous federated learning, which requires waiting for all clients to complete updates before aggregation, asynchronous federated learning aggregates the models that have arrived in real- time, greatly improving training speed. However, this mechanism also introduces the issue of client model version inconsistency. When the differences between models of different versions during aggregation become too large, it may lead to conflicts, thereby reducing the model's accuracy. To address this issue, this paper proposes an asynchronous federated learning version correction algorithm based on knowledge distillation, named FedADT. FedADT applies knowledge distillation before aggregating gradients, using the latest global model to correct outdated information, thus effectively reducing the negative impact of outdated gradients on the training process. Additionally, FedADT introduces an adaptive weighting function that adjusts the knowledge distillation weight according to different stages of training, helps mitigate the misleading effects caused by the poorer performance of the global model in the early stages of training. This method significantly improves the overall performance of asynchronous federated learning without adding excessive computational overhead. We conducted experimental comparisons with several classical algorithms, and the results demonstrate that FedADT achieves significant improvements over other asynchronous methods and outperforms all methods in terms of convergence speed.

Index Terms- federated learning, asynchronous, knowledge distillation, adaptive weight

# 1. INTRODUCTION

Federated learning [1], [2], as a machine learning paradigm that combines privacy protection, is increasingly gaining widespread attention. It effectively addresses the issue of data privacy leakage by replacing data transmission with model transmission. Compared to traditional local training, it can significantly improve model accuracy in certain cases. The working principle of federated learning is that a central server coordinates clients to participate in training. In each round of training, the server selects a subset of clients and broadcasts the global model to them. Then, the clients use their local data for training. Once the clients finish training, the server aggregates the model parameters uploaded by all the clients to generate an updated global model.

However, the traditional synchronous federated learning method has a significant drawback: Each round of training must wait for all selected clients to complete their training, which can lead to severe delays, especially when there is a large performance gap between clients. Faster clients often have to wait for slower clients to finish training, a situation known as the "straggler problem". The root cause of the straggler problem is system and communication heterogeneity [3], [4], specifically the differences in computing power and bandwidth among clients [5], which lead to performance imbalances and exacerbate waiting times.

To address the straggler problem, asynchronous federated learning [6], [7] has been introduced. Unlike synchronous methods, asynchronous federated learning does not wait for all clients to complete training. Instead, it aggregates the gradients from any client as soon as they upload them. However, while asynchronous methods effectively mitigate the straggler problem, the lack of synchronization often affects the accuracy of the model. In asynchronous federated learning, since clients may be at different stages of training, the server may aggregate models from multiple versions during each update. This asynchronous updating process can introduce outdated information, which in turn impacts the training effectiveness [8].

We observe that the primary difference between asynchronous and synchronous methods lies in how updates are made. In synchronous federated learning, each update is based on the most recent model. The server patiently waits for all selected clients to complete training, and uses the latest global model to guide the server in generating high- value gradients with its own training data. In contrast, asynchronous federated learning lacks such patient guidance: The server performs multiple updates while the selected clients are

still training, resulting in almost abandoned waiting for the clients. Ultimately, the server hastily merges the outdated results after the client has completed training. We believe this strategy is unreasonable. Although asynchronous federated learning, by its very nature, cannot wait for clients to finish training, it still requires a "guide" to direct outdated information toward the latest version, thus reducing the impact of outdated information on accuracy.

In this paper, we propose a version correction algorithm for asynchronous federated learning, which utilizes the latest global model to guide and correct outdated information, aiming to improve both the accuracy and performance of asynchronous federated learning. The main contributions of this paper are as follows:

We propose FedADT, an asynchronous federated learning gradient correction algorithm based on knowledge distillation. After the client sends the training results, the server corrects outdated information using the globally updated model, thereby significantly improving the performance of asynchronous federated learning. We design an adaptive weight- based strategy for FedADT, enabling the client to be better guided by the globally updated model at different stages of training, thus avoiding the potential misguidance of the student model by the teacher model in the early stages of training. We conducted experimental comparisons with several existing methods on computer vision tasks. The experimental results demonstrate that the proposed method exhibits competitive performance across various evaluation metrics and shows significant advantages over other asynchronous methods.

# II. RELATED WORK

# A. Synchronous Federated Learning

Synchronous methods are the leading strategy in federated learning, where the server selects a subset of clients to participate in training during each communication round. The server aggregates the uploaded model data only after all selected clients have completed their training. These synchronous approaches offer notable advantages in terms of model accuracy. Among them, FedAvg [9] is the most traditional algorithm in synchronous federated learning, creating a new global model each communication round by averaging the models from all participating clients. In contrast, FedAdam [10] enhances FedAvg by incorporating a momentum mechanism to further improve optimization performance.

Another key benefit of synchronous federated learning is that clients consistently train using the latest global model, ensuring that the server receives the most current gradient information. As a result, synchronous algorithms can efficiently gather the latest client data and optimize the global model to address various complex challenges. For example, FedProx [11] introduces a regularization term during local client training to prevent model parameters from deviating too much from the global model. FedBN [12] reduces feature shifts caused by differences in data distributions through Batch Normalization. Additionally, MOON [13] utilizes the similarity between model representations to adjust the local training processes of individual clients.

# B. Asynchronous Federated Learning

Asynchronous Federated Learning was designed to overcome the issue of "stragglers", where the performance of faster clients is hindered by slower ones, thereby limiting overall efficiency [14]. In an entirely asynchronous framework, the global model is updated as soon as any client submits an update, meaning clients don't have to wait for others. This feature allows asynchronous methods to significantly accelerate training compared to synchronous ones, especially in scenarios with varying client latencies. However, one major drawback of asynchronous systems is the problem of stale updates, which can negatively impact both model accuracy and stability, particularly when working with non- i.i.d. data [15]. To address this challenge, several solutions have been proposed. For example, FedAsync [16] adjusts the contribution of updates based on their age, FedASMU [17] ensures clients always use the most current global model during training, and FedFa [18] utilizes a queue- based buffer that discards the oldest update when a new one arrives after the buffer is full.

Additionally, some research has proposed the use of buffers at the server to collect and aggregate client updates, a technique known as semi- asynchronous federated learning. In semi- asynchronous federated learning, gradient aggregation occurs when the buffer reaches its capacity. This approach reduces the frequency of global model updates, thereby mitigating the negative impact of stale updates. For instance, FedBuff [19] empties the buffer once it reaches capacity, while CA2FL [20] stores the latest updates from each client to better refine the global model. The buffer strategy in semi- asynchronous federated learning performs relatively well in high- latency environments. However, the introduction of buffers increases time consumption, making this solution still unsatisfactory [3], [21]. We aim to design a purely asynchronous approach that can achieve the same or even slightly higher accuracy compared to semi- asynchronous methods, while ensuring training speed, thus achieving a more robust asynchronous federated learning solution.

# C. Knowledge Distillation

The concept of knowledge distillation was introduced by Hinton et al. [22], where a large and complex network, referred to as the teacher, is used to generate soft labels. These labels then guide the training of a smaller, more efficient student network. While early approaches focused on transferring knowledge from a single teacher model, more recent techniques have explored the use of multiple teachers and various aggregation methods. These include gate learning strategies in supervised environments [23], [24] and methods that leverage sample similarity in unsupervised settings. Advances in data- free knowledge distillation [25] emphasize adversarial techniques that generate challenging samples that are difficult for both

the student and the teacher to learn from. In a similar vein, DeepInversion [26] exploits backpropagated gradients to create adversarial samples that induce disagreements between the teacher and the student. Furthermore, [27] approaches the task by fitting data distributions based on output similarities to craft a suitable transfer set.

In the context of federated learning based on distillation, there are approaches beyond the traditional parameter- based FL methods [9], [28]. Early efforts, such as [29], involved exchanges of both parameters and model outputs. More recent approaches [30]- [32] focus solely on transferring knowledge via local model outputs. However, these methods typically assume that the data distributions are similar across different devices, creating a reliance on prior knowledge of private data. Some newer methods [33], [34] relax this requirement to some extent, but they still necessitate careful selection of transfer data based on local task specifics and private data characteristics.

Although approaches like [35]- [37] allow knowledge transfer without requiring actual data, they often entail significant communication overhead due to the need for extensive model exchanges over multiple iterations. This makes them vulnerable to potential security threats and privacy risks.

# III. PROBLEM FORMULATION

This section explains the problem formulation and system model for federated learning. We consider a system comprising a central server and  $M$  heterogeneous devices (also referred to as edge devices), where each device has its own local private dataset. The goal is to collaboratively train a global model shared by all devices.

# A. Problem Definition

Assume there are  $M$  devices in the system, and the set of devices is denoted by  $\mathcal{M} = \{1,2,\ldots ,M\}$ . For any device  $i\in \mathcal{M}$ , its local dataset can be expressed as:

$$
D_{i} = \{(x_{i,d},y_{i,d})\mid d = 1,2,\ldots ,|D_{i}|\} , \tag{1}
$$

where  $x_{i,d}\in \mathbb{R}^s$  represents the  $d$ - th input sample,  $y_{i,d}\in \mathbb{R}$  represents the corresponding label, and  $|D_{i}|$  is the number of samples on device  $i$ . After aggregating the datasets of all devices in the system, we obtain the global dataset:

$$
D = \bigcup_{i\in \mathcal{M}}D_{i},\quad N = \sum_{i\in \mathcal{M}}|D_{i}|, \tag{2}
$$

where  $N$  represents the total number of samples across all devices.

Our goal is to collaboratively train a global model using the local datasets of all devices, without directly exchanging the original data. We define the global loss function as:

$$
J(w) = \frac{1}{N}\sum_{i\in \mathcal{M}}\sum_{(x_i,d,y_i,d)\in D_i}\ell (w;x_{i,d},y_{i,d}), \tag{3}
$$

where  $\ell (\cdot)$  represents the loss for a single sample. The local loss function on device  $i$  is defined as:

$$
J_{i}(w) = \frac{1}{|D_{i}|}\sum_{(x_{i,d},y_{i,d})\in D_{i}}\ell (w;x_{i,d},y_{i,d}). \tag{4}
$$

![](images/9573da7f06de0ada573d083490d548adc1920f1704afc50da592e738d6dd6ccc.jpg)  
Fig. 1: The comparison of the experimental data distribution and algorithm performance. In the left figure, different colors of points represent different categories, and the position of each point reflects the proportion of each category's data in the corresponding client. In the figure on the right, 'Efficiency' refers to the ratio of the time taken by the optimal algorithm to reach a target accuracy of  $35\%$  to the time taken by each algorithm to achieve the same level of accuracy.

# Algorithm 1 Client-Training

Input: server model  $w$ , client learning rate  $\eta$ , client steps  $Q$ , global timestamp  $t$

Output: client model  $w_{Q}$

1:  $y_{0}\leftarrow w$  2: for  $q = 1$  to  $Q$  do 3:  $y_{q}\leftarrow y_{q - 1} - \eta g_{q}(y_{q - 1})$  4: end for 5:  $w_{Q}\leftarrow y_{Q}$  6: Send  $(w_{Q},t)$  to server

Finally, the Federated Learning problem can be expressed as the following optimization problem:

$$
\min_w J(w) = \min_w\frac{1}{N}\sum_{i\in \mathcal{M}}|D_i|J_i(w). \tag{5}
$$

Synchronous federated learning requires waiting for all selected clients to complete their local training before the server performs model aggregation. In contrast, asynchronous federated learning overcomes this limitation by immediately aggregating the model whenever the server receives an update from any client, thereby generating an updated global model. The aggregation process in asynchronous federated learning can be expressed by the following formula:

$$
\mathbf{w}_i^{(g)} = \beta \mathbf{w}^{(l)} + (1 - \beta)\mathbf{w}_{i - 1}^{(g)}, \tag{6}
$$

here,  $\mathbf{w}^{(l)}$  represents the received client model,  $\mathbf{w}_i^{(g)}$  denotes the  $i$ - th version of the global model, and  $\beta$  depends on the value of the received model. Typically, a smaller  $\beta$  is assigned to gradients with significant staleness.

# B. Dilemma of Asynchronous Federated Learning

Although asynchronous federated learning has the advantage of faster training speed, it has to simultaneously deal with staleness and the heterogeneity of data distribution, which results in poor performance when handling uneven

![](images/9eaffecd60e023c32e19affd3124f3cbc120b92928399708224d5968cc5ca379.jpg)  
Fig. 2: FedADT Framework. Faster client models are almost always trained based on the latest global model, whereas slower client models can only be trained on outdated global models. Traditional methods typically aggregate these outdated models directly with the latest model, which can lead to conflicts between different model versions. In contrast, our approach leverages knowledge distillation to rapidly align outdated client models with the latest version, significantly reducing conflicts between different model versions.

data distributions. As a result, its training outcomes are often significantly worse than those of other federated learning algorithms. To simulate strong data distribution heterogeneity, we employed a Dirichlet distribution with  $\alpha = 0.2$  to evenly distribute the CIFAR- 10 dataset across 100 clients. Based on this, we selected two algorithms for each of the synchronous, semi- asynchronous, and asynchronous federated learning approaches and calculated their average performance, as shown in Fig. 1. In the case of uneven data distribution, asynchronous federated learning consistently performs worse in terms of accuracy and convergence compared to the other algorithms, leading to its frequent neglect.

# IV. METHOD

FedADT consists of two main components: The first component is knowledge distillation, which is based on the idea of using the latest version of the global model to correct outdated models uploaded by clients. By introducing this knowledge distillation mechanism, FedADT ensures that the client models are promptly aligned with the most recent state of the global model after each local update, thereby maintaining stability and consistency during training. The second component is the adaptive weight function, which dynamically adjusts the distillation weights at different training stages to prevent client models from being misled by the global model's poor performance in the early stages of training.

# A. Knowledge Distillation

In asynchronous federated learning, the models uploaded by different clients may not always correspond to the latest version of the global model. Such discrepancies in model Input: all clients  $c$ , client learning rate  $\eta$ , client SGD steps  $Q$ , value calculation function  $\beta$ , global timestamp  $t_g$

# Algorithm 2 FedADT

Output: a optimized model

1:  $t_g = 0$  2: repeat 3:  $c\gets$  sample clients 4: Run Client- Training  $(w^{t},\eta ,Q)$  on  $c$  5: if receive client update then 6:  $(w_{i},t_{i})\gets$  received from client  $i$  7:  $\tau_{i}\gets t_{g} - t_{i}$  8: if  $\tau >1$  then 9:  $w_{i}\gets K o w l e d g e D i s t i l l a t i o n(w_{g},w_{i})$  10: end if 11:  $\beta_{i}\gets \beta (\tau_{i})$  12:  $w_{g}\gets (1 - \beta)w_{g} + \beta w_{i}$  13:  $t_g\gets t_g + 1$  14: end if 15: until Convergence

versions can significantly impact the aggregation effectiveness and training performance of the global model. To address this issue, FedADT introduces a knowledge distillation mechanism. This mechanism utilizes the latest global model on the server to correct outdated models uploaded by clients, thereby reducing the interference caused by version differences during the training process and improving the overall system performance and convergence speed. For client- uploaded models that are not the latest version, we use the latest global model on

the server to perform version correction through knowledge distillation, thereby reducing the impact of model version differences on the performance of asynchronous federated learning. Before training begins, the server needs to prepare a small labeled dataset for knowledge distillation of client models. This dataset can be extracted from the server's existing data or collected from other sources. To ensure the fairness and accuracy of the testing results, this distillation dataset must be completely independent of the test set. Additionally, the loss function used in our knowledge distillation process is defined as follows:

$$
\mathcal{L}_{\mathrm{KD}} = \tilde{\alpha}\cdot \mathrm{KL}\left(\sigma \left(\frac{z_S}{T}\right)\| \delta \left(\frac{z_C}{T}\right)\right) + (1 - \tilde{\alpha})\left(-\log \sigma (z_C)\right), \tag{7}
$$

where  $\tilde{\omega}_{S}$  and  $\tilde{\omega}_{C}$  represent the logits of the server- side model and the client- side model, respectively.  $T$  is the temperature parameter for knowledge distillation, which is used to smooth the probability distribution.  $\sigma (\cdot)$  represents the Softmax function, defined as  $\begin{array}{r}\sigma (z_i) = \frac{\exp(z_i)}{\sum_j\exp(z_j)} \end{array}$ $\mathrm{KL}(P\| Q)$  represents the Kullback- Leibler divergence, which is used to measure the difference between two probability distributions  $P$  and  $Q$  defined as  $\begin{array}{r}\mathrm{KL}(P\| Q) = \sum_{i}P(i)\log \frac{P(i)}{Q(i)} \end{array}$

We recommend minimizing the size of the dataset used for knowledge distillation and the number of distillation rounds, as the global model continues to be updated during this process. It should be noted that in our experiments, we set the data ratio for knowledge distillation to  $0.5\%$  and limited the distillation process to a single round. Since knowledge distillation itself achieves knowledge transfer at a relatively fast speed, we can ensure that, with a smaller amount of data, the time spent on distillation is significantly shorter than the time required for local training on the client side. This helps avoid the negative impact of significant changes in the global model version during the distillation process on training.

# B. Adaptive Weight Function

Unlike traditional methods that rely on pre- trained teacher models, our knowledge distillation teacher model is a serverside model that is continuously updated during training. Therefore, using a fixed knowledge distillation weight  $\alpha$  throughout the process is not appropriate. To better adapt to the dynamic nature of training, the weight of knowledge distillation should be reduced during the early stages of client training to mitigate potential misleading effects from the global model. In the mid- to- late stages of training, the weight should gradually return to a normal level to fully utilize the corrective effect of knowledge distillation on outdated client models. Our weighting function is designed as follows:

$$
\tilde{\alpha} (t) = \tilde{\alpha}_{min} + (\tilde{\alpha}_{max} - \tilde{\alpha}_{min})\cdot \min \left(1,\frac{t}{T_g}\right). \tag{8}
$$

Among them,  $\alpha_{\mathrm{min}}$  and  $\alpha_{\mathrm{max}}$  represent the predefined minimum and maximum values of the knowledge distillation weight, respectively.  $T_{g}$  denotes the preset number of training rounds for weight adjustment, and  $t$  represents the current training round. Using this weighting function, we can dynamically adjust the weight during the first  $T_{g}$  training rounds, thereby accelerating the convergence of the model.

# V. EXPERIMENT

In this section, we conduct experiments using four different datasets and compare the proposed method with six other federated learning algorithms. The experimental results demonstrate that the proposed method outperforms other algorithms across various experimental settings, showing particularly significant advantages when compared with asynchronous federated learning algorithms. These findings indicate that our method has achieved a major breakthrough in the field of asynchronous federated learning.

# A. Experimental Setup

We implement our algorithm FedADT using FLGO [38] and PyTorch. FLGO is a good federated learning framework that supports asynchronous federated learning. Our experimental setup is equipped with an NVIDIA GeForce RTX 3090 GPU.

Settings. We simulate a scenario with 500 clients. Our concurrency rate is set to  $20\%$ . When the number of concurrent clients falls below this value, the server resamples 50 clients until the concurrency requirement is met, and each client's response time uniformly distributed between 0 and 5000. The entire training process lasted for 15 days of virtual time.

Datasets and Models. We conduct experiments on four representative datasets: MNIST [39], FMNIST [40], CIFAR10 [41], and CIFAR100 [41]. We extract  $0.5\%$  of the data from the clients' datasets as knowledge distillation data, which was independent of the server's test set. We use CNN models for experiments on these datasets. For data partitioning, we use a Dirichlet distribution to allocate data among all clients, with  $\alpha$  values set to 0.1, 0.5 and 1, simulating both ideal and non- iid scenarios.

Hyperparameters. In all our experiments, the learning rate is set to 0.01, with a decay rate of 0.9999, and the batch size was 32. For semi- asynchronous algorithms with a buffer, the buffer size is set to the optimal value of 10 as suggested in the literature, and the remaining parameters are set to the recommended values from the original paper. The knowledge distillation rounds of our method are set to 1,  $\beta$  is set to  $\frac{1}{\sqrt{\tau + 1}}$ , the distillation temperature  $T$  is set to 3,  $\tilde{\alpha}_{\mathrm{min}}$  is set to 0.2,  $\tilde{\alpha}_{\mathrm{max}}$  is set to 0.6, and  $T_{g}$  is set to 1000.

Baselines. To verify the performance of our method in asynchronous environments, we compare it with several classic and advanced federated learning algorithms across multiple datasets. The algorithms compared include FedAVG [9], FedAVGM [42], FedAsync [16], FedRa [18], FedBuff [19], and CA2FL [20]. Among these, FedAVG and FedAVGM represent synchronous methods, which require waiting for all clients to complete their training before performing model aggregation. Although this introduces delays, their accuracy remains higher due to the absence of staleness issues. FedAsync and FedFa, as representatives of asynchronous methods, immediately aggregate once client updates are received. However, their accuracy

TABLE I: Virtual time (seconds) required for different methods to achieve target accuracy.  

<table><tr><td rowspan="3">Methods</td><td colspan="3">MFNIST</td><td colspan="3">FMNIST</td><td colspan="3">CIFAR10</td><td colspan="3">CIFAR100</td></tr><tr><td colspan="3">Target Acc: 0.90</td><td colspan="3">Target Acc: 0.75</td><td colspan="3">Target Acc: 0.40</td><td colspan="3">Target Acc: 0.10</td></tr><tr><td>α = 0.1</td><td>α = 0.5</td><td>α = 1.0</td><td>α = 0.1</td><td>α = 0.5</td><td>α = 1.0</td><td>α = 0.1</td><td>α = 0.5</td><td>α = 1.0</td><td>α = 0.1</td><td>α = 0.5</td><td>α = 1.0</td></tr><tr><td>FedAVG</td><td>397458</td><td>250312</td><td>128982</td><td>104299</td><td>78213</td><td>59630</td><td>Fail</td><td>734126</td><td>521661</td><td>Fail</td><td>980473</td><td>918653</td></tr><tr><td>FedAsync</td><td>208658</td><td>95836</td><td>50883</td><td>42780</td><td>35831</td><td>18938</td><td>Fail</td><td>Fail</td><td>254897</td><td>Fail</td><td>Fail</td><td>Fail</td></tr><tr><td>FedBuff</td><td>65059</td><td>37194</td><td>23102</td><td>27350</td><td>15942</td><td>10394</td><td>349026</td><td>193215</td><td>148721</td><td>515938</td><td>322954</td><td>294161</td></tr><tr><td>CA2FL</td><td>62772</td><td>34749</td><td>25212</td><td>Fail</td><td>345237</td><td>179075</td><td>1145905</td><td>581034</td><td>211775</td><td>Fail</td><td>Fail</td><td>485448</td></tr><tr><td>FedFa</td><td>288541</td><td>195813</td><td>67087</td><td>53401</td><td>394106</td><td>28637</td><td>Fail</td><td>Fail</td><td>553746</td><td>Fail</td><td>Fail</td><td>Fail</td></tr><tr><td>FedAVGM</td><td>397458</td><td>22952</td><td>104299</td><td>124056</td><td>74398</td><td>59649</td><td>Fail</td><td>639136</td><td>516679</td><td>Fail</td><td>728312</td><td>893856</td></tr><tr><td>FedADT</td><td>44136</td><td>28493</td><td>20905</td><td>14391</td><td>11324</td><td>8269</td><td>251159</td><td>112764</td><td>89373</td><td>399009</td><td>239431</td><td>142346</td></tr></table>

TABLE II: Final accuracy of different algorithms under various settings after running for 15 days.  

<table><tr><td rowspan="2">Methods</td><td colspan="3">MFNIST</td><td colspan="3">FMNIST</td><td colspan="3">CIFAR10</td><td colspan="3">CIFAR100</td></tr><tr><td>α = 0.1</td><td>α = 0.5</td><td>α = 1.0</td><td>α = 0.1</td><td>α = 0.5</td><td>α = 1.0</td><td>α = 0.1</td><td>α = 0.5</td><td>α = 1.0</td><td>α =0.1</td><td>α = 0.5</td><td>α = 1.0</td></tr><tr><td>FedAVG</td><td>94.64</td><td>96.33</td><td>97.32</td><td>81.43</td><td>82.03</td><td>82.69</td><td>37.82</td><td>45.50</td><td>51.16</td><td>7.88</td><td>10.83</td><td>12.24</td></tr><tr><td>FedAsync</td><td>93.28</td><td>95.38</td><td>96.69</td><td>81.58</td><td>81.70</td><td>82.30</td><td>28.85</td><td>39.84</td><td>43.88</td><td>4.41</td><td>7.53</td><td>8.33</td></tr><tr><td>FedBuff</td><td>97.16</td><td>97.22</td><td>97.65</td><td>83.18</td><td>82.98</td><td>82.72</td><td>42.21</td><td>48.36</td><td>51.82</td><td>16.29</td><td>21.78</td><td>24.94</td></tr><tr><td>CA2FL</td><td>99.16</td><td>99.32</td><td>99.18</td><td>74.35</td><td>75.61</td><td>77.36</td><td>36.30</td><td>47.33</td><td>55.89</td><td>6.44</td><td>9.78</td><td>14.87</td></tr><tr><td>FedFa</td><td>92.41</td><td>94.23</td><td>95.90</td><td>81.32</td><td>81.98</td><td>82.09</td><td>23.67</td><td>33.18</td><td>41.97</td><td>4.65</td><td>5.92</td><td>7.58</td></tr><tr><td>FedAVGM</td><td>94.74</td><td>96.36</td><td>97.31</td><td>81.44</td><td>82.09</td><td>82.68</td><td>38.14</td><td>44.56</td><td>51.08</td><td>7.85</td><td>10.15</td><td>12.24</td></tr><tr><td>FedADT</td><td>98.49</td><td>98.73</td><td>98.79</td><td>82.37</td><td>83.09</td><td>83.11</td><td>48.55</td><td>51.23</td><td>53.73</td><td>11.93</td><td>17.52</td><td>19.59</td></tr></table>

TABLE III: Accuracy at round  $T_{g}$  

<table><tr><td rowspan="2">Strategy</td><td colspan="2">MFNIST</td><td colspan="2">FMNIST</td><td colspan="2">CIFAR10</td></tr><tr><td>α = 0.1</td><td>α = 0.5</td><td>α = 0.1</td><td>α = 0.5</td><td>α = 0.1</td><td>α = 0.5</td></tr><tr><td>Fixed ̃=0.2</td><td>62.18</td><td>65.92</td><td>72.21</td><td>72.27</td><td>14.32</td><td>16.24</td></tr><tr><td>Fixed ̃=0.6</td><td>84.59</td><td>88.06</td><td>76.74</td><td>77.49</td><td>23.82</td><td>23.82</td></tr><tr><td>Adaptive</td><td>87.98</td><td>91.04</td><td>78.59</td><td>79.33</td><td>28.92</td><td>30.46</td></tr></table>

is lower due to the impact of staleness. On the other hand, FedBuff and CA2FL, as semi- asynchronous methods, use buffering to balance speed and accuracy, achieving superior performance in environments with uneven delay distributions.

# B. Convergence Effectiveness

We evaluate the convergence rate of FedADT by comparing it with different baseline methods in terms of the virtual time required to reach the target accuracy, with the specific results shown in Table I. The primary goal of asynchronous algorithms is to minimize the time required to reach the target accuracy in order to leverage their speed advantage over synchronous methods, making convergence rate the most important evaluation metric for asynchronous approaches. Experimental results show that, under all experimental settings, our method consistently exhibits faster convergence than the other methods, highlighting its superiority.

# C. Performance Comparison

We conduct experiments on all baseline methods under the condition of fixed virtual runtime, and record the final accuracy achieved by each algorithm, as shown in Table II. The experimental results demonstrate that our algorithm achieved optimal or near- optimal accuracy across all experimental settings. Due to the excellent buffer configuration of semi- asynchronous federated learning, our algorithm's accuracy is not optimal in some settings. However, our algorithm significantly outperforms other asynchronous algorithms, as shown in Fig. 3, and achieving performance comparable to or even surpassing that of semi- asynchronous federated learning algorithms. We believe that we have made a significant advancement in asynchronous federated learning, making robust asynchronous learning possible.

![](images/d6e63aa4a43d736e33347c9eac8def3a33d899028adb628ab542041fea14f4d3.jpg)  
Fig. 3: Comparison with other pure asynchronous methods when  $\alpha = 0.1$

# D. Effectiveness of Adaptive Weight

In this section, we investigate the impact of the adaptive weighting function on our algorithm. We test the model's ac

TABLE IV: Performance comparison under same rounds.  

<table><tr><td>P</td><td>10%</td><td>20%</td><td>25%</td><td>30%</td></tr><tr><td>FedAsync</td><td>0.6103</td><td>0.5935</td><td>0.5713</td><td>0.5587</td></tr><tr><td>FedFa</td><td>0.5984</td><td>0.5839</td><td>0.5640</td><td>0.5509</td></tr><tr><td>FedADT</td><td>0.6832</td><td>0.6794</td><td>0.6713</td><td>0.6642</td></tr></table>

curacy at the  $T_{g}$  round under three conditions: the knowledge distillation weight fixed at 0.2, fixed at 0.6, and using the adaptive weight function. The results are shown in Table III. The experimental results demonstrate that our adaptive weighting function effectively facilitates a rapid warm- up of the model, thereby improving the convergence rate of the model.

# E.Impact of Concurrency Rate

E. Impact of Concurrency RateThe concurrency rate in asynchronous methods is defined as the minimum proportion of concurrent clients to the total number of clients. In our algorithm, when the number of concurrent clients falls below the specified concurrency rate, the server resamples clients until the required number of concurrent clients is reached. The concurrency rate for asynchronous algorithms is typically set between  $10\%$  and  $20\%$ . This is because a high concurrency rate increases the upper limit of staleness, which reduces the final model accuracy, while a low concurrency rate results in prolonged server waiting times, negatively impacting the convergence speed. We compared the accuracy of our method with other asynchronous algorithms under different sampling rates  $\mathcal{P}$ , after 10 times the number of  $T_{g}$  iterations. The results are shown in Table IV. Experimental results demonstrate that our algorithm exhibits greater robustness at higher concurrency rates, as knowledge distillation effectively mitigates the adverse effects of staleness on model training.

# VI. CONCLUSION

VI. CONCLUSIONIn this paper, we propose FedADT, a version correction method based on knowledge distillation, aimed at reducing the negative impact of version discrepancies in asynchronous federated learning. FedADT rapidly transfers the knowledge of the latest model to outdated client models through knowledge distillation, enabling the outdated models to be effectively guided toward the direction of the latest model, thereby reducing conflicts between different model versions. Additionally, by introducing an adaptive weight function, the performance of the method is further enhanced. Extensive experiments demonstrate that FedADT outperforms other asynchronous methods, with a faster convergence rate.

# VII. ACKNOWLEDGEMENT

This work is supported by the Fundamental Research Funds for Central Universities under grant No. xzy012024105.

# REFERENCES

REFERENCES[1] P. Kairouz, H. B. McMahan, B. Avent, A. Bellet, M. Bennis, A. N. Bhagoji, K. Bonawitz, Z. Charles, G. Cormode, R. Cummings, R. G. L. D'Oliveira, H. Eichner, S. E. Rouayheb, D. Evans, J. Gardner, Z. Garrett, A. Gascón, B. Ghazi, P. B. Gibbons, M. Gruteser, Z. Harchaoui, C. He, L. He, Z. Huo, B. Hutchinson, J. Hsu, M. Jaggi, T. Javidi, G. Joshi, M. Khodak, J. Konečný, A. Korolova, F. Koushanfar, S. Koyejo, T. Lepoint, Y. Liu, P. Mittal, M. Mohri, R. Nock, A. Özgür, R. Pagh, M. Raykova, H. Qi, D. Ramage, R. Raskar, D. Song, W. Song, S. U. Stich, Z. Sun, A. T. Suresh, F. Tramè, P. Vepakomma, J. Wang, L. Xiong, Z. Xu, Q. Yang, F. X. Yu, H. Yu, and S. Zhao, "Advances and open problems in federated learning," 2021. [Online]. Available: https://arxiv.org/abs/1912.04977[2] J. Liu, J. Huang, Y. Zhou, X. Li, S. Ji, H. Xiong, and D. Dou, "From distributed machine learning to federated learning: a survey," Knowledge and Information Systems, vol. 64, no. 4, p. 885- 917, Mar. 2022. [Online]. Available: http://dx.doi.org/10.1007/s10115- 022- 01664- x[3] W. Wu, L. He, W. Lin, R. Mao, C. Magle, and S. Jarvis, "Safa: A semi- asynchronous protocol for fast federated learning with low overhead," IEEE Transactions on Computers, vol. 70, no. 5, pp. 655- 668, 2021. [4] T. Che, Y. Zhou, Z. Zhang, L. Lyu, J. Liu, D. Yan, D. Dou, and J. Huan, "Fast federated machine unlearning with nonlinear functional theory," in Proceedings of the 40th International Conference on Machine Learning, ser. ICML'23. JMLR.org, 2023. [5] H. Li, K. Ota, and M. Dong, "Learning iot in edge: Deep learning for the internet of things with edge computing," IEEE Network, vol. 32, no. 1, pp. 96- 101, 2018. [6] C. Xu, Y. Qu, Y. Xiang, and L. Gao, "Asynchronous federated learning on heterogeneous devices: A survey," Computer Science Review, vol. 50, p. 100595, 2023. [Online]. Available: https://www.sciencedirect.com/science/article/pii/S157401372300062X[7] D. C. Nguyen, Q.- V. Pham, P. N. Pathirang, M. Ding, A. Seneviratne, Z. Lin, O. A. Dobre, and W.- J. Huang, "Federated learning for smart healthcare: A survey," 2021. [Online]. Available: https://arxiv.org/abs/2111.08834[8] N. Su and B. Li, "How asynchronous can federated learning be?" in 2022 IEEE/ACM 3th International Symposium on Quality of Service (WQoS), 2022, pp. 1- 11. [9] H. B. McMahan, E. Moore, D. Ramage, S. Hampson, and B. A. y Arcas, "Communication- efficient learning of deep networks from decentralized data," 2023. [Online]. Available: https://arxiv.org/abs/1602.05629[10] S. Reddi, Z. Charles, M. Zaheer, Z. Garrett, K. Rush, J. Konečný, S. Kumar, and H. B. McMahan, "Adaptive federated optimization," 2021. [Online]. Available: https://arxiv.org/abs/2003.00295[11] T. Li, A. K. Sahu, M. Zaheer, M. Sanjabi, A. Talwalkar, and V. Smith, "Federated optimization in heterogeneous networks," 2020. [Online]. Available: https://arxiv.org/abs/1812.06127[12] X. Li, M. Jiang, X. Zhang, M. Kamp, and Q. Dou, "Fedbn: Federated learning on non- iid features via local batch normalization," 2021. [Online]. Available: https://arxiv.org/abs/2102.07623[13] Q. Li, B. He, and D. Song, "Model- contrastive federated learning," 2021. [Online]. Available: https://arxiv.org/abs/2103.16257[14] C. Xu, Y. Qu, Y. Xiang, and L. Gao, "Asynchronous federated learning on heterogeneous devices: A survey," 2023. [Online]. Available: https://arxiv.org/abs/2109.04269[15] Q. Ma, Y. Xu, H. Xu, Z. Jiang, L. Huang, and H. Huang, "Fedsa: A semi- asynchronous federated learning mechanism in heterogeneous edge computing," IEEE Journal on Selected Areas in Communications, vol. 39, no. 12, pp. 3654- 3672, 2021. [16] C. Xie, S. Koyejo, and I. Gupta, "Asynchronous federated optimization," 2020. [Online]. Available: https://arxiv.org/abs/1903.03934[17] J. Liu, J. Jia, T. Che, C. Huo, J. Ren, Y. Zhou, H. Dai, and D. Dou, "Fedamu: Efficient asynchronous federated learning with dynamic staleness- aware model update," 2023. [Online]. Available: https://arxiv.org/abs/2312.05770[18] H. Xu, Z. Zhang, S. Di, B. Liu, K. A. Alharthi, and J. Cao, "Fedfa: a fully asynchronous training paradigm for federated learning," in Proceedings of the Thirty- Third International Joint Conference on Artificial Intelligence, ser. IJCAI '24, 2024. [Online]. Available: https://doi.org/10.24963/ijcai.2024/584[19] J. Nguyen, K. Malik, H. Zhan, A. Yousefpour, M. Rabbat, M. Malek, and D. Huba, "Federated learning with buffered asynchronous aggregation," 2022. [Online]. Available: https://arxiv.org/abs/2106.06639

[20] Y. Wang, Y. Cao, J. Wu, R. Chen, and J. Chen, "Tackling the data heterogeneity in asynchronous federated learning with cached update calibration," in The Twelfth International Conference on Learning Representations, 2024. [Online]. Available: https://openreview.net/forum?id=4ywmeb97I[21] R. Xie, C. Li, X. Zhou, and Z. Dong, "Asynchronous federated learning for real- time multiple licence plate recognition through semantic communication," in ICASSP 2023 - 2023 IEEE International Conference on Acoustics, Speech and Signal Processing (ICASSP), 2023, pp. 1- 5. [22] G. Hinton, O. Vinyals, and J. Dean, "Distilling the knowledge in a neural network," 2015. [Online]. Available: https://arxiv.org/abs/1503.02531[23] U. Asif, J. Tang, and S. Haner, "Ensemble knowledge distillation for learning improved and efficient networks," 2020. [Online]. Available: https://arxiv.org/abs/1909.08097[24] L. Xiang, G. Ding, and J. Han, "Learning from multiple experts: Self- paced knowledge distillation for long- tailed classification," 2020. [Online]. Available: https://arxiv.org/abs/2001.01536[25] Z. Zhu, J. Hong, and J. Zhou, "Data- free knowledge distillation for heterogeneous federated learning," 2021. [Online]. Available: https://arxiv.org/abs/2105.10056[26] H. Yin, P. Molchanov, Z. Li, J. M. Alvarez, A. Mallya, D. Hoiem, N. K. Jha, and J. Kautz, "Dreaming to distill: Data- free knowledge transfer via deep inversion," 2020. [Online]. Available: https://arxiv.org/abs/1912.08795[27] G. K. Nayak, K. R. Mopuri, V. Shaj, R. V. Babu, and A. Chakraborty, "Zero- shot knowledge distillation in deep networks," 2019. [Online]. Available: https://arxiv.org/abs/1905.08114[28] T. Li, J. Li, Z. Liu, and C. Zhang, "Few sample knowledge distillation for efficient network compression," 2020. [Online]. Available: https://arxiv.org/abs/1812.01839[29] E. Jeong, S. Oh, H. Kim, J. Park, M. Bennis, and S.- L. Kim, "Communication- efficient on- device machine learning: Federated distillation and augmentation under non- iid private data," 2023. [Online]. Available: https://arxiv.org/abs/1811.11479[30] Q. Li, B. He, and D. Song, "Practical one- shot federated learning for cross- silo setting," 2021. [Online]. Available: https://arxiv.org/abs/2010.01017[31] D. Li and J. Wang, "Fedued: Heterogeneous federated learning via model distillation," 2019. [Online]. Available: https://arxiv.org/abs/1910.03581[32] H. Chang, V. Shejwalkar, R. Shokri, and A. Houmansadr, "Cronus: Robust and heterogeneous collaborative learning with black- box knowledge transfer," 2019. [Online]. Available: https://arxiv.org/abs/1912.11279[33] T. Lin, L. Kong, S. U. Stich, and M. Jaggi, "Ensemble distillation for robust model fusion in federated learning," in Advances in Neural Information Processing Systems, H. Larochelle, M. Ranzato, R. Hadsell, M. Balcan, and H. Lin, Eds., vol. 33. Curran Associates, Inc., 2020, pp. 2351- 2363. [34] X. Gong, A. Sharma, S. Karanam, Z. Wu, T. Chen, D. Doermann, and A. Innanje, "Preserving privacy in federated learning with ensemble cross- domain knowledge distillation," Proceedings of the AAAI Conference on Artificial Intelligence, vol. 36, no. 11, pp. 11 891- 11 899, Jun. 2022. [Online]. Available: https://ojs.aaai.org/index.php/AAAI/article/view/21446[35] Z. Zhu, J. Hong, and J. Zhou, "Data- free knowledge distillation for heterogeneous federated learning," in Proceedings of the 38th International Conference on Machine Learning, ser. Proceedings of Machine Learning Research, M. Meila and T. Zhang, Eds., vol. 139. PMLR, 18- 24 Jul 2021, pp. 12 878- 12 889. [Online]. Available: https://proceedings.mlr.press/v139/zhu21b.html[36] L. Zhang, D. Wu, and X. Yuan, "Fedzkt: Zero- shot knowledge transfer towards resource- constrained federated learning with heterogeneous on- device models," in 2022 IEEE 42nd International Conference on Distributed Computing Systems (ICDCS), 2022, pp. 928- 938. [37] L. Zhang, L. Shen, L. Ding, D. Tao, and L.- Y. Duan, "Fine- tuning global model via data- free knowledge distillation for non- iid federated learning," 2023. [Online]. Available: https://arxiv.org/abs/2203.09249[38] Z. Wang, X. Fan, Z. Peng, X. Li, Z. Yang, M. Feng, Z. Yang, X. Liu, and C. Wang, "Flgo: A fully customizable federated learning platform," 2023. [Online]. Available: https://arxiv.org/abs/2306.12079[39] Y. LeCun, C. Cortes, and C. Burges, "Mnist handwritten digit database," ATT Labs [Online]. Available: http://yann.lecun.com/exdb/mnist, vol. 2, 2010.

[40] H. Xiao, K. Rasul, and R. Vollgraf. (2017) Fashion- mnist: a novel image dataset for benchmarking machine learning algorithms.[41] A. Krizhevsky, "Learning multiple layers of features from tiny images," University of Toronto, Tech. Rep., 2009. [42] T.- M. H. Hsu, H. Qi, and M. Brown, "Measuring the effects of non- identical data distribution for federated visual classification," 2019. [Online]. Available: https://arxiv.org/abs/1909.06335