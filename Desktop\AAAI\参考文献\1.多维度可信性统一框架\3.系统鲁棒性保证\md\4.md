# Enhancing Robustness to Missing Modalities through Clustered Federated Learning

<PERSON><PERSON>. <PERSON><PERSON>

# Abstract

In the era of big data, data mining has become indispensable for uncovering hidden patterns and insights from vast and complex datasets. The integration of multimodal data sources further enhances its potential. Multimodal Federated Learning (MFL) is a distributed approach that enhances the efficiency and quality of multimodal learning, ensuring collaborative work and privacy protection. However, missing modalities pose a significant challenge in MFL, often due to data quality issues or privacy policies across the clients. In this work, we present MMiC, a framework for Mitigating Modality incompleteness in MFL within the Clusters. MMiC replaces partial parameters within client models inside clusters to mitigate the impact of missing modalities. Furthermore, it leverages the Banzhaf Power Index to optimize client selection under these conditions. Finally, MMiC employs an innovative approach to dynamically control global aggregation by utilizing Markovitz Portfolio Optimization. Extensive experiments demonstrate that MMiC consistently outperforms existing federated learning architectures in both global and personalized performance on multimodal datasets with missing modalities, confirming the effectiveness of our proposed solution.

# 1. Introduction

Federated Learning (FL), introduced by <PERSON><PERSON><PERSON><PERSON> et al. (<PERSON><PERSON><PERSON><PERSON> et al., 2017), enables multiple participants to collaboratively train a unified model without sharing raw data, thus preserving privacy. This paradigm is particularly well- suited for applications such as medical image analysis, connected vehicles, blockchain and so on(<PERSON>ssis et al., 2020; Pokhrel & Choi, 2020; Lu et al., 2020). However, the absence of data sharing in FL introduces significant challenges, including model heterogeneity, communication overhead, and data distribution disparities.

Multimodal Federated Learning (MFL) extends the FL paradigm to multimodal tasks, which leverage the complementary nature of multiple data modalities (e.g., text, images, and audio) to achieve superior performance on large- scale datasets. However, a common challenge in MFL is modal incompleteness, where certain modalities may be missing for some clients due to hardware limitations or data collection constraints. This issue can significantly degrade model performance, especially in heterogeneous scenarios. Existing approaches to tackling missing modalities primarily concentrate on two main aspects: the Data Processing Aspect, which involves generating absent modalities or their representations from the available ones, and the Strategy Design Aspect, which entails devising flexible model architectures capable of adapting to a variable number of available modalities during training, such as attention- based models (Wu et al., 2024). While these methods have shown promise, they are often limited to specific models or datasets. For instance, Yu et al. (Yu et al., 2024) proposed a specialized LSTM- based model to handle incomplete modalities, but such solutions are not easily generalizable to other complex multimodal tasks because they rely heavily on the specific architecture. Moreover, the lack of a unified approach to address modality incompleteness across diverse tasks and datasets further restricts their applicability in broader real- world scenarios. In addition, the current MFL algorithm is limited to information exchange between clients and the server, lacking interaction between clients. For instance, PmcmFL (Bao et al., 2024) compensates for missing modality through server aggregation prototypes. However, due to constraints such as Non- IID data, the aggregated prototypes may not be suitable for all clients.

Clustered Federated Learning (CFL), a specialized form of FL, Personalization (Tan et al., 2023), groups clients into clusters based on models, parameters, or datasets. Collaborative training within each cluster reduces the impact of data distribution disparities between clients. Recent advancements in CFL, such as one- shot clustering methods like PACFL (Vahidian et al., 2023), leverage the decomposability of data to identify client clusters efficiently. Each cluster inherently contains clients with similarities in both data and model parameters. We can leverage this characteristic of federated clustering to improve the training scenarios involving missing modalities. This means that the model

parameters from clients within the same cluster that have no missing modalities can be used to fill in the parameters of clients with missing modalities. Due to the constraints of communication efficiency within clusters, we can shrink replaced parameters to only one layer most significantly affected by the missing modalities.

Current CFL algorithms predominantly employ random client selection (Ghosh et al., 2019; 2020; Ruan & JoeWong, 2022), which ensures fairness over multiple rounds of updates by granting each client an equal opportunity to participate in aggregation. However, we do not expect clients with severe missing modalities to enjoy the same level of aggregation rights. Therefore, by analyzing performance variations across client training rounds, we can determine whether a client is frequently affected by missing modalities. This allows us to adjust the probability of client selection accordingly.

Since the global model, due to the intermediate aggregation process involving the cluster- client hierarchy, cannot directly perceive changes in clients' missing modalities, we can employ certain functions or metrics to evaluate the training process within each cluster. This evaluation yields a specific value that can be reported to the server, enabling the server to determine the aggregation weights for the current round.

To achieve these goals, we propose MMiC framework that integrates our solutions for the three issues. In particular, we employ the rate of parameter variation to identify the parameters to be replaced. Moreover, the parameters from clients possessing complete modality data and belonging to the same cluster will be utilized to supplant those of clients with incomplete modality data. To enhance the current cluster client selection strategy in the presence of missing modalities, we incorporate the Banzhaf Power Index (BPI), a wellknown coalitional game theory, to assess the contribution of each client. This approach enables us to preferentially select the clients who are more contributable, to perform more efficient and effective aggregation processes. We further employ Markovitz Portfolio Optimization, a return- risk balancing asset optimization algorithm to assesses the training process on clusters in a nonlinear manner compared to the standard weighted averaging (Wang & Ji, 2024). The global model adjusts the weights of parameter updates based on the scores obtained from the clusters, thereby reducing the impact of missing modalities in local clients on the global model.

MMiC is model- independent allowing for integration with various multimodal base models. It is also modalityagnostic, enabling its application across diverse multimodal scenarios regardless of the specific modalities involved. Our experiments demonstrate that MMiC significantly outperforms traditional FL and MFL algorithms in terms of both accuracy and stability. In summary, the main contributions of our work are as follows:

- We propose a parameter substitution mechanism based on clustered federated learning, to mitigate the impact of missing modalities during training.- We enhance the random client selection strategy by integrating the Banzhaf Power Index to favor clients with more significant.- We are the first to apply the Markowitz Portfolio Optimization theory to evaluate the training on clusters by incorporating the evaluation results into the global aggregation process to mitigate the negative impact from missing modalities to the global server.

# 2. Related Works

# 2.1. Clustered Federated Learning (CFL)

Centralized federated learning trains a global model in a central server by aggregating gradients collected across numerous participating clients. The heterogeneity of clients in federated learning introduces challenges such as client drift, feature drift, and label drift, which significantly affect the performance of FL models. Personalized federated learning (PFL) has sprung up designed to better suit the individual characteristics of each client to address the heterogeneity. PFL strategies like Multi- Task Learning, Personalization Layers and Clustered Federated Learning (CFL) have attracted considerable attention in the literature (Smith et al., 2017; Tan et al., 2023; Mansour et al., 2020; Wu et al., 2020).

The fundamental concept of CFL is to group clients based on the similarity of their model parameters, data, or gradients. CFL emphasizes improving cooperation within these clusters to optimize the performance of cluster- specific models, while also enhancing overall global model performance. Ghosh et al. (Ghosh et al., 2019) first introduced the architecture for CFL, and recommended clustering based on the similarity of model parameters. This architecture has been refined through iterative clustering techniques such as the Iterative Federated Clustering Algorithm (IFCA) (Sattler et al., 2021). Fedsoff further advanced the iterative CFL by allowing a client to belong to multiple clusters, outperforming previous efforts. However, these methods incurred substantial resource consumption. One- shot clustering was then introduced by Dennis et al. (Dennis et al., 2021) for more efficient clustering. Their proposed method efficiently determined clustering attribution, identifying the uniqueness of client metrics within complex federated architectures. Vahidian et al. (Vahidian et al., 2023) proposed a one- shot CFL algorithm based on data subspace clustering. They measured the similarity between clients by decomposing

the dataset via Singular Value Decomposition (SVD). Our proposed MMiC framework is under the same setting as the one- shot CFL (Vahidian et al., 2023; Yang et al., 2024) and extends it to multimodal scenarios with missing modalities.

# 2.2. Multimodal Federated Learning (MFL)

Multimodal Federated Learning (MFL) extends standard federated learning to address multimodal datasets on clients and global server.

Challenges and Advances in MFL. FedCMR is the first to address multimodal tasks, though it encountered challenges such as modality discrepancies and missing data. Recent works have made strides in tackling these issues. For instance, Chen and Li (Chen & Li, 2022) proposed MFL- HGB, which adaptively computes optimal modality blending and local model weighting to mitigate overfitting. Similarly, Fed- MP employs adaptive aggregation of local model weights and a multimodal prototyping mechanism to enhance generalization to unseen classes (Zeng et al., 2024). These works collectively provide a foundation for understanding the challenges and solutions in MFL (Che et al., 2023).

MFL with Missing Modality. CACMRN (Xiong et al., 2023) is the first FL framework to propose solutions for missing modalities. They aim to achieve modality reconstruction using existing multi- modal data by training multiple functionally diverse models. Yu et al. (Yu et al., 2023) aim to address the issue of missing modalities in cross- modal retrieval. Clients, each with distinct modalities, learn the representation information of specific modalities and upload it to the server for contrastive learning. Fed- Multimodal (Feng et al., 2023) is a multimodal benchmark built upon the FL algorithm. The work presented a range of models and datasets and proposed a method to initialize the missing modal vector as zero. PmcmFL (Bao et al., 2024) applied the common prototype exchange method of federated learning against Non- IID to the missing modality, and they store prototypes of all modalities for all classes from each client on the server. When encountering missing modalities, it utilizes the corresponding class- specific prototypes to fill in the representations. Additionally, they applied the MIX- UP (Zhang et al., 2018) method in data augmentation into the additional training dataset. In contrast, our approach does not impose restrictions on the types of multimodal tasks, meaning that we do not require the use of specific models.

# 3. Preliminaries

Before jumping into the technical details of MMiC, we first introduce the fundamental concepts of CFL and the relevant concepts in the game theory and finance domain that have been adopted by MMiC. To assist the description, we list the notations used throughout the paper in Table 1.

Table 1. Main notations.  

<table><tr><td>Notation</td><td>Description</td></tr><tr><td>K</td><td>Number of clients</td></tr><tr><td>c</td><td>Client</td></tr><tr><td>M,m</td><td>Number, index of cluster</td></tr><tr><td>G,M</td><td>Server, cluster</td></tr><tr><td>T,t</td><td>Global Training rounds</td></tr><tr><td>E,e</td><td>Local training epochs</td></tr><tr><td>B,b</td><td>Batch number, b-th batch</td></tr><tr><td>I,i,J,j</td><td>Temporary index or sequence</td></tr><tr><td>O,θ</td><td>Model or parameters</td></tr><tr><td>l,L</td><td>Local loss, global Loss</td></tr><tr><td>η</td><td>Learning rate</td></tr><tr><td>g</td><td>Gradient</td></tr><tr><td>D</td><td>Dataset</td></tr><tr><td>R</td><td>Client joining rate each round</td></tr><tr><td>γt</td><td>Portfolio score in t-th round</td></tr><tr><td>a,α</td><td>Performance gap</td></tr><tr><td>σ</td><td>Risk of federated cluster</td></tr><tr><td>φ(*)</td><td>Banzhaf interaction index</td></tr><tr><td>wk</td><td>Weight of client k</td></tr><tr><td>P,P</td><td>Number of layers, the poverty layer</td></tr><tr><td>λ,ε,τ</td><td>Control constant</td></tr><tr><td>S</td><td>Subset</td></tr></table>

# 3.1. Clustered Federated Learning

Given a global server  $\mathcal{G}$  and  $K$  clients  $\{c_i\} ^K$  , let  $D_{k}$  over  $\chi *\mathcal{V}$  be the local dataset of the  $k$  - th client  $C_k$  ,with  $\mathcal{X}$  and  $\mathcal{V}$  represent the data inputs and labels,  $n_k = |\mathcal{D}_k|$  be the dataset size. Client weight is calculated as  $\begin{array}{r}w_{k} = \frac{n_{k}}{\sum_{k}^{K}n_{k}} \end{array}$ $C_k$  and  $\mathcal{G}$  possess the model  $\theta_{k}$  and  $\theta_{\mathcal{G}}$  respectively and the models are the same when initialized.

Learning Objective. During the FL training process, each cluster model, on the one hand, is regarded as a mesosphere agent that can optimize the training loss from clients within the cluster; and on the other hand, it serves as a local model to the global model, providing stronger generalization ability. In each round of federated training, on the client, it is to learn local model  $\theta_{k}$  by minimizing the local empirical loss:

$$
F_{i}(\theta_{k}) = \sum_{\xi \in \mathcal{D}_{k}}\frac{\ell(\theta_{k},\xi)}{n_{k}} \tag{1}
$$

where  $\ell (\cdot ,\cdot)$  is the pre- defined task- driven loss function and  $\xi$  represents sampled training data in a batch. The clusters are defined by partitioning  $K$  clients into  $M$  disjoint subsets. Different from traditional federated selection, clustered federated selection is refined to each cluster, and each round has client subset  $S_{m}$  selected in  $\mathcal{M}_m$ . The weight of cluster  $m$

is usually calculated as  $w_{m} = \frac{\sum_{k\in\mathcal{M}_{m}}n_{k}}{\sum_{1}^{M}\sum_{k\in\mathcal{M}_{m}}n_{k}}$ . Therefore, the global objective function can be expressed as:

$$
min\sum_{m = 1}^{M}\sum_{k\in \mathcal{M}_m}w_k\ell (\pmb {\theta}_k) \tag{2}
$$

Model Aggregation. A standard way to obtain a cluster model from clients' models in CFL is to adopt the Fe- dAvg (Li et al., 2020):

$$
\Theta_{m} = \sum_{k\in \mathcal{M}_{m}}w_{k}\pmb{\theta}_{k} \tag{3}
$$

There are various aggregation methods applied to aggregate the global model from clusters' models. FedAdagrad (Reddi et al., 2021b) exploits dynamic aggregation to combine the clients' parameter updates from the current round with the stored historical parameter updates:

$$
\begin{array}{l}{\Delta_{m,t} = \Theta_{m,t} - \Theta_{\mathcal{G},t - 1}}\\ {\Delta_t = \frac{1}{M}\sum_{m = 1}^{M}w_m\Delta_{m,t}}\\ {mtumn_t = \beta mtumn_{t - 1} + (1 - \beta)\Delta_t,\beta \in [0,1)}\\ {\pmb {v}_t = \pmb{v}_{t - 1} + \Delta_t^2}\\ {\pmb {\theta}_{\mathcal{G},t} = \pmb {\theta}_{\mathcal{G},t - 1} + \eta \frac{mtumn_t}{\sqrt{\pmb{v}_t} + \tau}} \end{array} \tag{4}
$$

where  $\Delta_{m,t}^{1}$  and  $\Delta_t$  is the difference between the current cluster model and the global model.  $mtumn_t$  is updated iteratively and collects the historical parameter updates, while  $\beta$  balances the historical parameter updates  $mtumn_{t - 1}$  and  $\Delta_t$ . A larger  $\beta$  indicates the retention of more historical parameter information.  $\pmb {v}_t$  is an accumulated squared change estimate used to measure changes in parameters and adjust the learning rate to improve the stability and convergence speed of the optimization process. MMiC relies on FedAdagrad and derives an extended version (Section 4.3).

# 3.2. Banzhaf Power Index

The Banzhaf Power Index (BPI) is a game theory metric used to quantify the power of an individual voter in a decision- making body where votes are not necessarily equal (Dubey & Shapley, 1979; Laruelle & Valenciano, 2001). The index is calculated by considering all possible coalitions (i.e., combinations of voters) that can be formed within a voting body and determining the importance of each voter in each coalition. A voter  $k$  is considered a core member if their presence in a coalition changes the outcome

denoted as  $A_S$  from a loss to a win:

$$
k i s c o r e?\left\{ \begin{array}{l l}{T r u e} & {\mathrm{if~}A_{S^{\prime}}i s a l o s s a n d A_{S}i s a w i n}\\ {F a l s e} & {\mathrm{otherwise}} \end{array} \right. \tag{5}
$$

where  $S^{\prime}$  is the set that excludes voter  $k$ :  $S^{\prime} = S \setminus \{k\}$ . BPI could be adopted in federated learning settings to help select the most contributable clients (i.e., a subset of the coalition) during the training process, making the client selection more effective.

# 3.3. Markowitz Portfolio Optimization

The Markowitz Portfolio Optimization model is an investment optimization model that aims to obtain an optimal investment portfolio with maximized returns and minimized risks (Miller, 1960). The model can assess the current investment process through Risk- Adjusted Return Coefficient RARC  $(\gamma)$ . The quadratic programming model of Markowitz Portfolio Optimization is formulated as follows:

$$
minimize\quad \gamma = \lambda \bar{\sigma} -(1 - \lambda)A \tag{6}
$$

indicating RARC is influenced by three variables: investment risk  $\bar{\sigma}$ , the investment return  $A$  and the risk tolerance factor  $\lambda$ .  $\lambda$  signifies the investor's risk tolerance within the range of [0,1]. When  $\lambda$  is set to 0.0, the maximum return value is achieved.  $A$  denotes the total portfolio return across all assets,  $\bar{\sigma}$  is calculated as follows:

$$
\begin{array}{l}\bar{\sigma} = \sum_{i = 1}^{K}\sum_{j = 1}^{K}w_{i}w_{j}\sigma_{ij}\\ \sigma_{ij} = \frac{1}{T - 1}\sum_{t = 1}^{T}(\alpha_{i,t} - \bar{\alpha}_{i,t - 1})(\alpha_{j,t} - \bar{\alpha}_{j,t - 1}) \end{array} \tag{7}
$$

where  $\sigma_{ij}$  is the covariance between assets  $i$  and  $j$ .  $w_{i}$  and  $w_{j}$  are the weights of assets  $i$  and  $j$ , respectively.  $K$  is the number of assets.  $\bar{\alpha}_{i,t - 1}$  denotes the historical average return of assets  $i$  across the preceding  $t - 1$  rounds.  $\sigma_{ij}$ , the risk between assets  $i$  and  $j$  is obtained by subtracting the corresponding average return from each round in the history of the asset. This formulation can be extended to federated learning scenarios, where  $\sigma_{ij}$  quantifies the risk or divergence of local models, and  $\alpha_{i,t - 1}$  reflect the historical performance of individual participants in the federated system.

# 4. Methodology

Figure 1 depicts the architecture of MMiC. The left part illustrates the overall architecture including the clients at the bottom, clusters in the middle, and the global server at the top.

Each cluster accommodates multiple clients, and each client hosts multimodal data with missing modalities locally. The

![](images/58f5d627809b3a003a259031f9cf6c94fa4323f028c51af0c573d4696e37411a.jpg)  
Figure 1. The MMiC architecture is divided into three layers: Server, Cluster, and Client. The right part illustrates the process of parameter substitution within a cluster. The MPO module primarily operates during the aggregation process from clusters to the global model, while the Banzhaf client selection is applied to the client selection within each cluster.

cluster model is derived from the federated average aggregation (Section 3.1). Within each cluster, MMiC handles the missing modality issue and the client selection task. First, a parameter layer substitution mechanism allows clients with missing modalities to obtain knowledge from clients with complete modality data within the same cluster (Section 4.1). Second, client selection is performed leveraging the Banzhaf Power Index, which identifies a set of core clients that contribute the most effectively to the cluster's model (Section 4.2).

For global aggregation, MMiC extends FedAdagrad by adopting the Markowitz Portfolio Optimization (Section 4.3). This method incorporates client training evaluation results from each cluster and reduces the impact of missing modalities by down- weighing the contribution from clusters that contain missing modalities.

# 4.1. Cluster-based Parameter Substitution

We are targeting the multimodal scenario when the dataset has both textual  $X^{text}$  and image  $X^{image}$  data, thus we form the input as a pair  $\{X_{i}^{text},X_{i}^{image}\}$  to the  $i$ - th data in  $\mathcal{D}$ . The missing modality randomly occurs resulting in data samples in the form of  $\{X_{i}^{null},X_{i}^{image}\}$  or  $\{X_{i}^{text},X_{i}^{null}\}^{2}$ .

To mitigate the consequences of missing modalities in each client, parameters that are less impacted by modality incompleteness are used to substitute those that are more significantly affected. We first propose a substitution strategy that can assist these clients in the training rounds. This strategy is inspired by the CFL setting, where a cluster model represents an aggregation of client models within the same cluster. The core idea is to substitute the parameters of client models with missing modality data with those of clients in the same cluster that have complete modality data. This approach involves two key tasks: (i) identifying the parameters to be substituted and (ii) ensuring the efficiency of the method.

Poverty Parameter Identification. In one round of client model training, we identify the layer that is the most affected by missing modalities as poverty layer. We use the parameter difference between the last batch  $b - 1$  and the current batch  $b$  to define the degree of this effect when the missing modality occurs in batch  $b$  and batch  $b - 1$  is with complete modality:

$$
\begin{array}{rl} & {\Delta \pmb{\theta}_{k,t}^{e,b}(p) = \mathrm{mean}\left(\left|\frac{\pmb{\theta}_{k,t}^{e,b}(p) - \pmb{\theta}_{k,t}^{e,b - 1}(p)}{\pmb{\theta}_{k,t}^{e,b - 1}(p)}\right|\right)}\\ & {\mathrm{where}\Delta \pmb{\theta}_{k,t}^{e,b}(p)\in \mathbb{R}} \end{array} \tag{8}
$$

where  $e$  is the epoch,  $t$  is the training round,  $k$  is the client.  $\Delta \theta_{k,t}^{e,b}(p)$  represents average parameters change rate of the  $p$ - th layer. As  $\Delta \theta_{k,t}^{e,b}$  is a set that contains all the layers change rate. We collect all the batches in the  $e$ - th epoch that contain complete modality data, while the preceding batch contains missing modality data, and form a batch set  $B*$ . Therefore, the relative change rate for the layer  $p$  in one epoch could be expressed as:

$$
\Delta \pmb{\theta}_{k,t}^{e}(p) = \frac{\sum_{b\in B^{*}}\Delta\pmb{\theta}_{k,t}^{e,b}(p)}{|\mathcal{B}^{*}|} \tag{9}
$$

After calculating the average rate of change for all layers in the model in one epoch, we identify the layer with the highest rate of change across all epochs. This layer would be considered as the poverty layer with index  $\mathcal{P}_{k,t}$ :

$$
\mathcal{P}_{k,t} = \arg \max_{p\in \{1,2,\ldots ,P\}}\Delta \pmb{\theta}_{k,t}(p) \tag{10}
$$

It is worth mentioning that we select parameters from only one layer to reduce communication consumption.

Poverty Parameter Substitution (PPS). After identifying the poverty parameter in  $c_{k}$ , we propose to replace these parameters with the parameters from other clients in the cluster that have complete modality data. Specifically, we compute a weighted average of the corresponding parameters of other clients and replace the poverty parameter in

$c_{k}$

$$
\begin{array}{c}{\theta_{k,t}(\mathcal{P}_{k,t}) = \sum_{j = 1}^{J}w_{j}\theta_{j,t}(\mathcal{P}_{k,t})}\\ {\mathrm{where}\mathcal{P}_{j,t} = \emptyset ,K + J = |\mathcal{M}_{m}|} \end{array} \tag{11}
$$

$c_{k}$  represents the poverty client and  $c_{j}$  is the client that trained with the complete modality round, and both  $c_{k}$  and  $c_{j}$  belong to the cluster  $\mathcal{M}_m$

Optimizers Could Approximate Parameter Difference. Saving parameters between two batches is resourceintensive and time- consuming. However, we can roughly deduce the rate of change by utilizing the gradient derivation function of the optimizer. Below, we provide the calculation process for parameter variations during the SGD and Adam optimization processes. For SGD,  $\eta$  is the learning rate and  $g_{k,b}$  is the gradient for  $\alpha_{k}$  in  $b$  batch.

$$
\Delta \theta_{k,t}^{e,b} = \frac{(\pmb{\theta}_{k,t}^{e,b - 1} - \eta_{k,t}\pmb{g}_{k,b}) - \pmb{\theta}_{k,t}^{e,b - 1}}{\pmb{\theta}_{k,t}^{e,b - 1}} = \frac{-\eta_{k,t}\pmb{g}_{k,b}}{\pmb{\theta}_{k,t}^{e,b} + \pmb{g}_{k,b}}
$$

Similarly, we can utilize the Adam optimizer to deduce the average rate of change, here momentum is the biascorrected moment estimates and  $\hat{\nu}$  is the second- moment estimate in Adam optimizer (Kingma & Ba, 2015):

$$
\Delta \theta_{k,t}^{e,b} = \frac{\eta_{k,t}abs(momentum_b)}{abs(\pmb{\theta}_{k,t}^{e,b})(\sqrt{\hat{\pmb{\nu}}_b} + \epsilon)} \tag{13}
$$

# 4.2.Banzhaf Client Selection in Clusters

The standard procedure for current CFL involves randomly selecting a subset of clients  $S_{m}$  from each cluster for training in each round, followed by a global aggregation. While the random selection is considered fair, there is a possibility that the cluster models aggregated by the selected clients may yield subpar results if some of the selected clients have a significant number of data instances with missing modalities. The negative impact will be transited to the global model through aggregation. Moreover, the negative impact will be enlarged over successive rounds of client training and cluster model aggregation, further degrading the performance of the global model.

Few CFL work addresses this issue and quantifies the impact of client selection for each cluster. We propose a novel method to fill the gap by leveraging Banzhaf Power Index (BPI) to select the core clients. It comprises two main steps. First, we identify core members within each subset selected in each round and tally the times of core members, denoted as core times. Then, by leveraging the historical selection times and core times, we devise a new client selection probability formula, which is applied in our subsequent client selection process. Banzhaf client selection does not require extra communication.

Core Members Identification. At training round  $t$  each cluster  $\mathcal{M}_m$  makes a random client selection. We can obtain the  $i$  - th client's local model performance  $a_{i,t}$  (i.e., accuracy, F1 score or RSum) of each client in the  $t$  - th round, and the performance  $a_{i,t - 1}$  in the  $(t - 1)$  - th round. We calculate the return  $\alpha_{i,t}$  of the client for the  $t$  - th round based on the difference between these two rounds of performances as follows:

$$
\alpha_{i,t} = a_{i,t} - a_{i,t - 1} \tag{14}
$$

where  $\alpha_{i,t}< 0$  indicates that the client is generating a negative return to the investment portfolio, conversely,  $\alpha_{i,t} > 0$  signifies a positive return. Then, the cluster's return is calculated as the sum of the client returns:

$$
A_{S_{m,t}} = \sum_{i = 0}^{|S_{m,t}|}w_{i}\alpha_{i,t} \tag{15}
$$

For each cluster  $S_{m,t}$  , we have its total return  $A_{S_{m,t}}$  by Equation (15). Correspondingly, we can calculate the total return for  $\mathcal{S}_{m,t}^{\prime}$  when excluding client  $c_{i},\mathcal{S}_{m,t}^{\prime} = \mathcal{S}_{m,t}n\{i\}$  Then, we use the return of cluster model to evaluate the core members:

$$
c_{i}iscoremember?\left\{ \begin{array}{ll}True & A_{S_{m,t}}\geq \alpha_{m}^{m}\mathrm{and}A_{\mathcal{C}_{m,t}^{\prime}}< \alpha_{m}^{m}\\ False & \mathrm{otherwise} \end{array} \right. \tag{13}
$$

The above equation describes the process of determining whether client  $c_{i}$  is a core member during the  $t$  - th round of client random selection. We utilize a counter  $\phi (*)$  that starts with O to tally the number of times each client has been identified as a core member:

$$
\phi (i) = \sum_{t = 1}^{T}\mathbb{I}(c_{i}isacoremember) \tag{17}
$$

where  $\mathbb{I}(\ast)$  is the indicator function that returns 1 if the condition  $\ast$  is satisfied, otherwise returns O.

Client Selection Probability. We could design a method to measure the probability of a client being selected in the current round based on its core times. Our goal is to ensure that a higher core time corresponds to a higher probability. However, this way will make clients with small core times unlikely to be selected later. To solve this problem, we set another counter  $\mathcal{T}(\ast)$  that records the number of times the client has been selected in its history, and we determine the final selection probability by balancing the historically selected times  $\mathcal{T}(i)$  and the core times  $\phi (i)$  ..

$$
prob(i) = \frac{e^{\frac{\tau\phi(i)}{\tau(i)}}}{\sum_{i\in\mathcal{M}_m}e^{\frac{\tau\phi(i)}{\tau(i)}}} \tag{18}
$$

For client  $i$  in cluster  $\mathcal{M}_m$ , its probability in the  $t$ - th round to be selected is  $prob(i)$ . We increase the disparity in probability allocation for each client through an exponential function, where  $\tau$  is a temperature constant. As  $\tau$  increases, the disparity in probability increases, and as  $\tau$  decreases, the disparity in probability decreases.

# 4.3. Markowitz Profolio Optimization for Global Aggregation

As introduced in Section 3.3, Markowitz Portfolio Optimization is a framework for constructing an investment portfolio that aims to maximize returns for a given level of risk. We are the first to integrate this theory into CFL by considering to mitigate the negative effects of local client models (e.g., those with missing modalities) on the global model. Specifically, we propose a mechanism where, in each communication round, the performance of the selected clients within a cluster  $\mathcal{M}_m$  is quantified through a non- linear scoring function. This score serves as a direct indicator of the cluster's performance during the round, allowing for adaptive control over the magnitude of the global model's updates. By leveraging this approach, we dynamically regulate the contribution of each cluster to the global model aggregation, thereby reducing the impact of underperforming or unreliable clients while maintaining the global model's robustness.

To achieve this, we require three core indicators to construct our "investment portfolio" for the cluster  $\mathcal{M}_m$ : the return (i.e., payoff) of each cluster  $(A_{m,t})$ , Equation (15); the risk associated with the cluster  $(\bar{\sigma}_{m,t})$ ; and the tolerance for return- risk ratio for each cluster  $(\lambda)$ .

Cluster Risk. According to the asset risk calculation formula Equation (7), we obtain the covariance as follows:

$$
\begin{array}{rl} & {\bar{\sigma}_{m,t} = \sum_{i = 1}^{|S_m|}\sum_{j = 1}^{|S_m|}w_iw_j\Bigg\{\frac{1}{T - 1}\sum_{t = 1}^{T}}\\ & {\qquad (\alpha_{i,t} - \bar{\alpha}_{i,t - 1})(\alpha_{j,t} - \bar{\alpha}_{j,t - 1})\Bigg\}} \end{array} \tag{19}
$$

where  $\bar{\alpha}_{i,t - 1}$  represents the historical average return for client  $i$  in round  $t - 1$ .  $w_{i}$  and  $w_{j}$  are weights assigned to the  $i$ - th and  $j$ - th clients respectively. Please refer to Section 3.1 for weights' calculation. Then the cluster risk is formulated as follows according to Equation (7):

$$
\gamma_{m,t} = \lambda \bar{\sigma}_{m,t} - (1 - \lambda)A_{m,t} \tag{20}
$$

where  $\lambda$  [0,1] is the risk tolerance constant. We defaultly set it as 0.5 to balance risk and return. Our goal is to minimize the sum of weighted cluster RARC (cluster weight calculation is introduced in Section 3.1):

$$
minimize\quad \gamma_{t} = \sum_{m = 1}^{M}w_{m}\gamma_{m,t} \tag{21}
$$

We analyzed how we achieved the time complexity of MPO in Appendix D.

Participate in Global Aggregation. A higher  $\gamma_t$  signifies that the clusters have undergone a relatively poor local client training process. In such scenarios, our objective is to diminish the impact of the current round's parameter updates on the global aggregation by down- weighting the cluster parameters associated with higher  $\gamma$ . To accomplish this, we introduce a dynamic  $\beta^*$  to replace the static  $\beta$  in FedAdagrad, resulting in  $\text{FedAdagrad}_{MPO}$ , which effectively captures the influence of  $\gamma_t$ :

$$
\beta^{*} = \beta +\frac{tanh(ReLU(\gamma_{t}))}{\tau} \tag{22}
$$

When  $\gamma_t > 0$ , then  $\beta^* > \beta$ , which helps retain more historical parameter updates in the global aggregation. Otherwise,  $\beta^* = \beta$ . Here  $\tau$  serves as the smoothness control constant for the range. We suggest setting the temperature to range [10,20], and it depends on the value set for  $\beta$ .

# 4.4. Variants of MMiC

We propose two MMiC variants by extending two existing methods that address modality incompleteness. Specifically, by integrating these methods with the three proposed modules in Section 4.1 - 4.3, we form the two MMiC variants.

MMiC (Modality Reconstruction). In this variant, we adopt the reconstruction idea from (Saharia et al., 2022) and incorporate two modality reconstruction models, Text- to- Image and Image- to- Text, at each cluster to reconstruct the missing modality using the data from the complete modality.

MMiC (GMC- Fusion). The other MMiC variant is based on the GMC- Fusion model (Poklukar et al., 2022). This variant necessitates the additional projecting of information from two modalities into the shared space, followed by computing the Geometric Multimodal Contrastive loss using all available modality representations and shared space representations from a batch. In this way, the missing modality information could be filled in the shared space.

# 5. Experiments

# 5.1. Datasets, Models and Metrics

Our evaluation of MMiC is based on two multimodal classification datasets and one multimodal retrieval dataset. Because each dataset has distinct characteristics, we followed (Feng et al., 2023; Jeni et al., 2013) in employing different base models and evaluation metrics for each dataset.

Table 2. Global and personalized performance compared with different algorithms. Bold with \* indicated the best performance, while bold black indicated excelent or stable top three performance. Since the PmcmFL algorithm is only for classification tasks, it can't be compared in Flick30k. MR stands for Modality Reconstruction and CF is GMC-Fusion.  

<table><tr><td rowspan="2">Global</td><td colspan="3">CrisisMMD (F1 score)</td><td colspan="3">UPMC-Food101 (Accuracy)</td><td colspan="3">Flickr30k (RSum score)</td></tr><tr><td>mm@0.2
mc@0.3</td><td>mm@0.2
mc@0.5</td><td>mm@0.5
mc@0.5</td><td>mm@0.2
mc@0.3</td><td>mm@0.2
mc@0.5</td><td>mm@0.5
mc@0.5</td><td>mm@0.2
mc@0.3</td><td>mm@0.2
mc@0.5</td><td>mm@0.5
mc@0.5</td></tr><tr><td>FedAvg [2017]</td><td>21.886 ± 2.87</td><td>20.531 ± 1.9</td><td>24.095 ± 0.178</td><td>88.053 ± 1.03</td><td>87.626 ± 1.34</td><td>88.283 ± 0.83</td><td>14.324 ± 1.68</td><td>4.704 ± 0.38</td><td>3.812 ± 0.16</td></tr><tr><td>FedOpt [2021]</td><td>29.320 ± 1.5</td><td>27.750 ± 2.04</td><td>26.292 ± 1.91</td><td>88.270 ± 0.53</td><td>89.010 ± 1.09</td><td>88.324 ± 0.85</td><td>16.138 ± 1.06</td><td>5.044 ± 0.61</td><td>12.576 ± 1.28</td></tr><tr><td>FedSoft [2022]</td><td>11.371 ± 0.02</td><td>8.180 ± 0.12</td><td>10.464 ± 0.01</td><td>87.471 ± 0.38</td><td>86.984 ± 0.79</td><td>81.461 ± 0.14</td><td>1.189 ± 0.01</td><td>1.297 ± 0.01</td><td>1.106 ± 0.02</td></tr><tr><td>PACFL [2023]</td><td>23.239 ± 3.35</td><td>20.274 ± 2.18</td><td>23.131 ± 3.029</td><td>88.152 ± 1.69</td><td>88.343 ± 1.31</td><td>88.439 ± 0.91</td><td>10.168 ± 1.14</td><td>8.013 ± 0.79</td><td>3.678 ± 0.25</td></tr><tr><td>CreamFL [2023]</td><td>22.766 ± 0.09</td><td>18.131 ± 1.0</td><td>16.958 ± 2.93</td><td>87.539 ± 0.26</td><td>87.102 ± 0.17</td><td>87.390 ± 0.88</td><td>13.965 ± 0.04</td><td>13.857 ± 0.06</td><td>11.839 ± 0.02</td></tr><tr><td>PmcmFL [2024]</td><td>24.610 ± 1.49</td><td>22.471 ± 0.44</td><td>22.946 ± 0.89</td><td>87.916 ± 0.90</td><td>85.159 ± 0.13</td><td>86.873 ± 0.52</td><td>-</td><td>-</td><td>-</td></tr><tr><td>MMiC</td><td>*29.462 ± 0.36</td><td>27.815 ± 1.44</td><td>*28.480 ± 1.86</td><td>88.777 ± 0.86</td><td>*89.436 ± 1.21</td><td>*89.428 ± 0.78</td><td>14.266 ± 1.20</td><td>8.322 ± 0.177</td><td>14.220 ± 1.28</td></tr><tr><td>MMiC (MR)</td><td>29.108 ± 0.86</td><td>*30.471 ± 0.77</td><td>28.354 ± 1.36</td><td>*88.997 ± 0.33</td><td>88.515 ± 2.31</td><td>89.044 ± 0.76</td><td>10.907 ± 1.21</td><td>10.152 ± 0.87</td><td>8.614 ± 0.84</td></tr><tr><td>MMiC (GF)</td><td>27.366 ± 2.22</td><td>26.229 ± 4.17</td><td>18.354 ± 2.06</td><td>88.465 ± 0.539</td><td>87.973 ± 1.26</td><td>89.056 ± 1.17</td><td>*18.333 ± 1.172</td><td>*25.318 ± 1.90</td><td>*22.707 ± 1.42</td></tr><tr><td>Personalized</td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td></tr><tr><td>FedAvg [2017]</td><td>11.133 ± 0.49</td><td>9.671 ± 0.368</td><td>13.454 ± 0.87</td><td>77.039 ± 2.69</td><td>76.080 ± 1.47</td><td>75.786 ± 1.76</td><td>34.625 ± 2.39</td><td>17.625 ± 0.76</td><td>17.907 ± 0.24</td></tr><tr><td>FedOpt [2021]</td><td>17.417 ± 1.24</td><td>18.825 ± 0.16</td><td>15.205 ± 1.07</td><td>76.073 ± 2.05</td><td>76.492 ± 1.40</td><td>76.304 ± 2.76</td><td>40.275 ± 2.22</td><td>33.569 ± 1.89</td><td>43.152 ± 1.43</td></tr><tr><td>FedSoft [2022]</td><td>10.814 ± 0.384</td><td>8.012 ± 0.57</td><td>8.180 ± 0.01</td><td>75.547 ± 0.94</td><td>75.108 ± 1.21</td><td>73.918 ± 1.41</td><td>7.812 ± 1.32</td><td>6.103 ± 0.36</td><td>3.695 ± 2.52</td></tr><tr><td>PACFL [2023]</td><td>13.837 ± 1.72</td><td>10.454 ± 0.56</td><td>12.985 ± 1.97</td><td>83.805 ± 1.76</td><td>82.915 ± 1.75</td><td>83.722 ± 1.96</td><td>18.348 ± 0.62</td><td>20.171 ± 3.49</td><td>16.266 ± 1.08</td></tr><tr><td>CreamFL [2023]</td><td>11.169 ± 2.37</td><td>11.853 ± 0.815</td><td>11.338 ± 0.647</td><td>78.610 ± 0.411</td><td>78.484 ± 4.85</td><td>77.008 ± 2.07</td><td>76.900 ± 3.88</td><td>79.461 ± 1.92</td><td>75.122 ± 3.01</td></tr><tr><td>PmcmFL [2024]</td><td>17.010 ± 0.32</td><td>14.662 ± 1.36</td><td>14.048 ± 0.06</td><td>83.031 ± 1.20</td><td>81.673 ± 1.59</td><td>79.987 ± 0.82</td><td>-</td><td>-</td><td>-</td></tr><tr><td>MMiC</td><td>*19.936 ± 0.91</td><td>18.911 ± 1.13</td><td>18.259 ± 1.62</td><td>83.576 ± 0.75</td><td>*84.022 ± 1.19</td><td>84.057 ± 1.73</td><td>90.726 ± 3.28</td><td>75.814 ± 3.35</td><td>78.979 ± 2.38</td></tr><tr><td>MMiC (MR)</td><td>19.492 ± 0.27</td><td>19.174 ± 1.32</td><td>18.101 ± 1.08</td><td>83.519 ± 2.31</td><td>83.055 ± 3.74</td><td>82.948 ± 3.59</td><td>41.824 ± 6.70</td><td>33.388 ± 4.52</td><td>31.489 ± 4.17</td></tr><tr><td>MMiC (GF)</td><td>19.625 ± 4.797</td><td>*22.796 ± 6.04</td><td>*18.450 ± 2.73</td><td>*84.715 ± 3.13</td><td>83.639 ± 2.10</td><td>*84.601 ± 4.69</td><td>*112.506 ± 5.10</td><td>*160.99 ± 6.082</td><td>*124.414 ± 2.86</td></tr></table>

We also provide learning configurations in Appendix A. CrisisMMD (Ofli et al., 2020). It is a multi- modal dataset for classification consisting of 18,126 samples annotated tweets and images. We brought in a lightweight multimodal model that used pre- trained embedding layers for input and concatenates shared representations (Gupta et al., 2024; Feng et al., 2023). Following (Feng et al., 2023), we employed the F1 (Goutte & Gaussier, 2005) score to evaluate the performance of CrisisMMD.

UPMC- Food101 (Gallo et al., 2020). This dataset was sourced from Food101, containing 101,000 image- text pairs across 101 food categories. We used CLIP with the official ViT version of the backbone pre- trained model (Radford et al., 2021). We froze the first 11 layers of attention while retaining the last layer of attention and the final output linear layer for parameter updates. We employed accuracy to evaluate its performance.

Flickr30k (Young et al., 2014). It is an image captioning dataset for retrieval, which is comprised of 31,783 images and the corresponding captions. We used SCAN (Stacked Cross Attention Network) (Lee et al., 2018) as the multimodal retrieval model. SCAN is a model designed for image- text matching. We relied on the RSum as the evaluation metric to measure the performance of the retrieval task (Faghri et al., 2018).

# 5.2. Benchmark Algorithms

We compared MMiC with selected representative works and state- of- the- art works.

FedAvg (McMahan et al., 2017) represented a basic federated architecture where multiple clients collaborated with a central server. FedOpt (Reddi et al., 2021a) proposed three specialized federated aggregation methods, building upon FedAvg. FedSoft (Ruan & Joe- Wong, 2022), as a soft clustering method, introduced a technique that permitted a client to belong to multiple clusters. PACFL (Vahidian et al., 2023), an advanced CFL methodology, employed Singular Value Decomposition (SVD) to deconstruct the dataset into its constituent subspaces, subsequently applying hierarchical clustering techniques to these subspaces for effective clustering. CreamFL (Yu et al., 2023), was a MFL algorithm that employed inter- model contrastive learning to address the issue of modality imbalance. PmcmFL (Bao et al., 2024), was another MFL for addressing missing modality. They utilized prototype aggregation to address the issue of missing modalities in classification tasks.

# 5.3. Settings

Cluster and Clients Settings. In this experiment, we employed two distance metrics for clustering: Locality Sensitive Hashing (LSH) sketch (Yang et al., 2024; Liu et al., 2023) and SVD decomposition (Vahidian et al., 2023). All experiments used the LSH sketch for clustering unless otherwise noted. Each cluster had a test set visible to all clients

within the cluster. Additionally, the global server provided accessibility to the test set for all clients. We simulated the Non- IID scenario by assigning a label probability table to each client utilizing the Dirichlet distribution (Teh, 2010) with  $\beta_{diri} = 0.5$ . For all the experiments, we set the number of clients to 50 so that ensured each client had at least 200 training data. Our client selection strategy comprised an initial phase of 100 rounds with random selection, followed by a subsequent phase of 200 rounds utilizing Banzhaf client selection (300 global rounds).

Missing Modality Settings. Our setting with missing modalities was governed by two variables: clients with missing modality (mc) and missing modality rate (mm), denoting the proportion of all clients selected to have missing modalities and the proportion of missing samples on these clients, respectively. Our main experiment (Section 5.4) offered three levels of modality incompleteness settings:  $(\mathrm{mm}@0.2, \mathrm{mc}@0.3)$ ,  $(\mathrm{mm}@0.2, \mathrm{mc}@0.5)$  and  $(\mathrm{mm}@0.5, \mathrm{mc}@0.5)$ . Each client has a fixed number of missing samples, determined by the mm rate. In each round, among the clients selected for aggregation, we use mc to decide whether the selected clients will activate missing data. Since the selected clients use a random training subset for training in each round, the maximum missing modality rate for a client in that round is  $\min \left(\frac{\text{missing samples}}{\text{subset samples}}, 1\right)$ .

# 5.4. Overall Performance

We evaluated MMiC based on global performance  $(gbp)$  and personalized performance  $(plp)$  and presented the results in Table 2. To do the evaluation, we made use of the global test dataset to evaluate the global model performance following (McMahan et al., 2017), and adopted the weighted average performance from client models to evaluate the  $plp$  following (Fallah et al., 2020).

MMiC and its two variants secure the top positions across all tasks and all missing modality scenarios. While MMiC (MR) excelled in classification tasks, being the only algorithm to achieve an F1 score exceeding 30 on CrisisMMD  $(\mathrm{mm}@0.2, \mathrm{mc}@0.5)$  at  $gbp$ , MMiC (GF) significantly outperformed all other algorithms in modality retrieval tasks across all three metrics. These results highlighted the superiority of MMiC in handling diverse missing modality scenarios.

FedOpt demonstrated strong  $gbp$  on the CrisisMMD dataset  $(\mathrm{mm}@0.2, \mathrm{mc}@0.3)$  and  $(\mathrm{mm}@0.2, \mathrm{mc}@0.5)$ . FedSoft's performance is medium across experiments with multiple missing modalities settings and datasets both in global and personalized evaluations. As a CFL algorithm, PACFL do not exhibit outstanding performance in  $gbp$ . In contrast, it demonstrated excellent performance in personalized evaluation. CreamFL is primarily an algorithm designed for retrieval tasks. The overall performance of CreamFL is relatively mediocre except for Flickr30k  $(\mathrm{mm}@0.2, \mathrm{mc}@0.5)$ , their method ranked second in this setting both in global and personalized evaluations. PmcmFL currently supports only classification tasks; however, its performance in classification tasks is somewhat limited.

![](images/ec6e32a8b1a507f97f18a25d9121dcada6ef9a545fc3f2f874bfebf401d2ee59.jpg)  
Figure 2. Ablation study under CrisisMMD  $(\mathrm{mm}@0.5 \mathrm{mc}@0.5)$ .

# 5.5. Ablation Study

We conducted ablation experiments of MMiC on CrisisMMD. The results are shown in Figure 2. A further ablation study on Food101 and Flickr30k is provided in Appendix B. BPI (b), MPO (p) and PPS (s) represent Banzhaf Power Selection, Markowitz Portfolio Optimization, and Poverty Parameter Substitution, respectively. The figures show that MMiC consistently achieves higher accuracy in the final rounds across multiple ablation tests. Specifically, both 'w/o PPS' (Figure 2(b)) and 'w/o BPI' (Figure 2(d)) saw a decrease of more than 5% compared with MMiC. The final performance of 'w/o MPO' (Figure 2(f)) is close to MMiC, yet it remained approximately 2% lower than MMiC. In addition, removing any of the two contributions leads to varying degrees of performance decline, with the resulting performance consistently falling between that of MMiC and the configuration with a single module removed (Figure

2(a), 2(c), 2(e)). We conclude that removing any module leads to performance degradation. We further proved the effectiveness of BPI in Appendix C.

# 5.6. Experiments on other modalities

We further experimented on two additional multimodal datasets with modalities other than image and text, aiming to show that MMiC is modality- agnostic. UCI- HAR (Reyes- Ortiz et al., 2013) and KU- HAR (Sikder & Nahid, 2021) both encompass smartphone sensor data utilized for the classification of diverse human activities. They include accelerometer and gyroscope readings gathered from a multitude of participants. We compared MMiC with FedOpt, which exhibited the good performance in Section 5.4, for this analysis. The experimental setup involved 30 clients running 150 global rounds, with all other parameters remaining consistent with those outlined in Section 5.3. The findings of this experiment are presented in Table 3. MMiC demonstrated a pronounced superiority over FedOpt, with enhancements of  $2\%$  and  $6\%$  in the KU- HAR and UCI- HAR respectively.

Table 3. Performance comparison of MMiC and FedOpt on UCIHAR and KU-HAR datasets.  

<table><tr><td>Dataset/Algorithm</td><td>MMiC (%)</td><td>FedOpt (%)</td></tr><tr><td>UCI-HAR</td><td>86.99</td><td>80.89</td></tr><tr><td>KU-HAR</td><td>75.83</td><td>73.24</td></tr></table>

# 6. Conclusions

In this work, we introduce MMiC, a model- independent and modality- agnostic framework designed to mitigate the impact of missing modalities in multimodal federated learning. By substituting parameters within clustered federated learning, adapting the Banzhaf Power Index for client selection, and extending Markowitz Portfolio Optimization for global aggregation, MMiC demonstrates improved performance over existing federated architectures in addressing missing modalities. Moving forward, we plan to further enhance the framework's robustness by substituting additional poverty parameters and exploring more efficient solutions.

# References

BaoG.ZhangQ.MiaoD.GongZ.HuL.LiuK. Liu, Y., and Shi, C. Multimodal federated learning with missing modality via prototype mask and contrast, 2024. URL https://arxiv.org/abs/2312.13508. Blelloch, G. E. Prefix sums and their applications. 1990. Che, L., Wang, J., Zhou, Y., and Ma, F. Multimodal federated learning:A survey.Sensors,23(15),2023. ISSN

1424- 8220. doi: 10.3390/s23156986. URL https://www.mdpi.com/1424- 8220/23/15/6986.

Chen, S. and Li, B. Towards optimal multi- modal federated learning on non- iid data with hierarchical gradient blending. In IEEE INFOCOM 2022 - IEEE Conference on Computer Communications, London, United Kingdom, May 2- 5, 2022, pp. 1469- 1478. IEEE, 2022.

Cho, Y. J., Wang, J., and Joshi, G. Client selection in federated learning: Convergence analysis and power- of- choice selection strategies. CoRR, abs/2016.01243, 2020.

Dennis, D. K., Li, T., and Smith, V. Heterogeneity for the win: One- shot federated clustering. In Meila, M. and Zhang, T. (eds.), Proceedings of the 38th International Conference on Machine Learning. ICML 2021, 18- 24 July 2021, Virtual Event, volume 139 of Proceedings of Machine Learning Research, pp. 2611- 2620. PMLR, 2021.

Dubey, P. and Shapley, L. S. Mathematical properties of the banzhaf power index. Math. Oper. Res., 4(2):99- 131, 1979.

Faghri, F., Fleet, D. J., Kiros, J. R., and Fidler, S. VSE++: improving visual- semantic embeddings with hard negatives. In Proceedings of the British Machine Vision Conference 2018, BMVC 2018, Newcastle, UK, September 3- 6, 2018, pp. 12. BMVA Press, 2018.

Fallah, A., Mokhtari, A., and Ozdaglar, A. Personalized federated learning with theoretical guarantees: A model- agnostic meta- learning approach. In Larochelle, H., Ranzato, M., Hadsell, R., Balcan, M., and Lin, H. (eds.), Proceedings of the Advances in Neural Information Processing Systems, NeurIPS 2020, volume 33, pp. 3557- 3568. Curran Associates, Inc., 2020. URL https://proceedings.neurips.cc/paper_files/paper/2020/file/24389bfe4fe2eba8bf9aa9203a44cdad- Paper.pdf.

Feng, T., Bose, D., Zhang, T., Hebbar, R., Ramakrishna, A., Gupta, R., Zhang, M., Avestimehi, S., and Narayanan, S. Fedmultimodal: A benchmark for multimodal federated learning. In Proceedings of the 29th ACM SIGKDD Conference on Knowledge Discovery and Data Mining, KDD 2023, Long Beach, CA, USA, August 6- 10, 2023, pp. 4035- 4045. ACM, 2023.

Gallo, I., Ria, G., Landro, N., and Grassa, R. L. Image and text fusion for UPMC food- 101 using BERT and cnns. In Proceedings of the 35th International Conference on Image and Vision Computing New Zealand, IVCNZ 2020, Wellington, New Zealand, November 25- 27, 2020, pp. 1- 6. IEEE, 2020.

Ghosh, A., Hong, J., Yin, D., and Ramchandran, K. Robust federated learning in a heterogeneous environment. CoRR, abs/1906.06629, 2019. URL http://arxiv.org/abs/1906.06629. Ghosh, A., Chung, J., Yin, D., and Ramchandran, K. An efficient framework for clustered federated learning. In Proceedings of Advances in Neural Information Processing Systems 33: Annual Conference on Neural Information Processing Systems 2020, NeurIPS 2020, December 6- 12, 2020, virtual, 2020. Goutte, C. and Gaussier, E. A probabilistic interpretation of precision, recall and  $F$  score, with implication for evaluation. In Losada, D. E. and Fernandez- Luna, J. M. (eds.), Proceedings of the 27th European Conference on IR Research, ECIR 2005, Santiago de Compostela, Spain, March 21- 23, 2005, Proceedings, volume 3408 of Lecture Notes in Computer Science, pp. 345- 359. Springer, 2005. Gupta, S., Saini, N., Kundu, S., and Das, D. Crisiskan: Knowledge- infused and explainable multimodal attention network for crisis event classification. In Proceedings of the 46th European Conference on Information Retrieval, ECIR 2024, Glasgow, UK, March 24- 28, 2024, Proceedings, Part II, volume 14009 of Lecture Notes in Computer Science, pp. 18- 33. Springer, 2024. Jeni, L. A., Cohn, J. F., and De La Torre, F. Facing imbalanced data- recommendations for the use of performance metrics. In Proceedings of the 2013 Humaine association conference on affective computing and intelligent interaction, pp. 245- 251. IEEE, 2013. Kaissis, G., Makowski, M. R., Rueckert, D., and Braren, R. Secure, privacy- preserving and federated machine learning in medical imaging. Nat. Mach. Intell., 2(6): 305- 311, 2020. Kingma, D. P. and Ba, J. Adam: A method for stochastic optimization. In Proceedings of the 3rd International Conference on Learning Representations, ICLR 2015, San Diego, CA, USA, May 7- 9, 2015, Conference Track Proceedings, 2015. Laruelle, A. and Valenciano, F. Shapley- shubik and banzhaf indices revisited. Math. Oper. Res., 26(1):89- 104, 2001. Lee, K., Chen, X., Hua, G., Hu, H., and He, X. Stacked cross attention for image- text matching. In Proceedings of the 15th European Conference on Computer Vision, ECCV 2018, volume 11208 of Lecture Notes in Computer Science, pp. 212- 228, Springer, 2018. Li, X., Huang, K., Yang, W., Wang, S., and Zhang, Z. On the convergence of fedavg on non- iid data. In Proceedings of the 8th International Conference on Learning Representations, ICLR 2020, Addis Ababa, Ethiopia,

April 26- 30, 2020. OpenReview.net, 2020. URL https://openreview.net/forum?id=HJxNAnVtDS.

Liu, Z., Xu, Z., Coleman, B., and Shrivastava, A. One- pass distribution sketch for measuring data heterogeneity in federated learning. In Proceedings of the Advances in Neural Information Processing Systems 36: Annual Conference on Neural Information Processing Systems 2023, NeurIPS 2023, New Orleans, LA, USA, December 10 - 16, 2023, 2023. Lu, Y., Huang, X., Dai, Y., Maharjan, S., and Zhang, Y. Blockchain and federated learning for privacy- preserved data sharing in industrial iot. IEEE Trans. Ind. Informatics, 16(6):4177- 4186, 2020. Mansour, Y., Mohri, M., Ro, J., and Suresh, A. T. Three approaches for personalization with applications to federated learning. CoRR, abs/2002.10619, 2020. McMahan, B., Moore, E., Ramage, D., Hampson, S., and y Arcas, B. A. Communication- efficient learning of deep networks from decentralized data. In Singh, A. and Zhu, X. J. (eds.), Proceedings of the 20th International Conference on Artificial Intelligence and Statistics, AISTATS 2017, 20- 22 April 2017, Fort Lauderdale, FL, USA, volume 54 of Proceedings of Machine Learning Research, pp. 1273- 1282. PMLR, 2017. Miller, M. H. Portfolio selection: efficient diversification of investments. The Journal of Business, 33(4): 391- 393, 1960. URL http://www.jstor.org/stable/2350928. Nishio, T. and Yonetani, R. Client selection for federated learning with heterogeneous resources in mobile edge. In Proceedings of 2019 IEEE International Conference on Communications, ICC 2019, Shanghai, China, May 20- 24, 2019, pp. 1- 7. IEEE, 2019. Offi, F., Alam, F., and Imran, M. Analysis of social media data using multimodal deep learning for disaster response. In Proceedings of the 17th International Conference on Information Systems for Crisis Response and Management, ISCRAM 2020, May 2020, pp. 802- 811. ISCRAM Digital Library, 2020. Pokhrel, S. R. and Choi, J. Federated learning with blockchain for autonomous vehicles: Analysis and design challenges. IEEE Trans. Commun., 68(8):4734- 4746, 2020. Poklukar, P., Vasco, M., Yin, H., Melo, F. S., Paiva, A., and Kragic, D. Geometric multimodal contrastive representation learning. In Proceedings of the International Conference on Machine Learning, ICML 2022, 17- 23 July 2022, Baltimore, Maryland, USA, volume 162 of

Proceedings of Machine Learning Research, pp. 17782- 17800. PMLR, 2022.

Radford, A., Kim, J. W., Hallacy, C., Ramesh, A., Goh, G., Agarwal, S., Sastry, G., Askell, A., Mishkin, P., Clark, J., Krueger, G., and Sutskever, I. Learning transferable visual models from natural language supervision. In Proceedings of the 38th International Conference on Machine Learning, ICML 2021, 18- 24 July 2021, Virtual Event, volume 139 of Proceedings of Machine Learning Research, pp. 8748- 8763. PMLR, 2021.

Reddi, S. J., Charles, Z., Zaheer, M., Garrett, Z., Rush, K., Konecný, J., Kumar, S., and McMahan, H. B. Adaptive federated optimization. In Proceedings of the 9th International Conference on Learning Representations, ICLR 2021, Virtual Event, Austria, May 3- 7, 2021. OpenReview.net, 2021a.

Reddi, S. J., Charles, Z., Zaheer, M., Garrett, Z., Rush, K., Konecný, J., Kumar, S., and McMahan, H. B. Adaptive federated optimization. In Proceedings of the 9th International Conference on Learning Representations, ICLR 2021, Virtual Event, Austria, May 3- 7, 2021, 2021b.

Reyes- Ortiz, J., Anguita, D., Ghio, A., Oneto, L., and Parra, X. Human Activity Recognition Using Smartphones. UCI Machine Learning Repository, 2013. DOI: https://doi.org/10.24432/C54S4K.

Ruan, Y. and Joe- Wong, C. Fedsoft: Soft clustered federated learning with proximal local updating. In Proceedings of the 36th AAAI Conference on Artificial Intelligence, AAAI 2022, Thirty- Fourth Conference on Innovative Applications of Artificial Intelligence, IAAI 2022, The Twelveth Symposium on Educational Advances in Artificial Intelligence, EAAI 2022 Virtual Event, February 22 - March 1, 2022, pp. 8124- 8131. AAAI Press, 2022.

Saharia, C., Chan, W., Saxena, S., Li, L., Whang, J., Denton, E. L., Ghasemipour, S. K. S., Lopes, R. G., Ayan, B. K., Salimans, T., Ho, J., Fleet, D. J., and Norouzi, M. Photorealistic text- to- image diffusion models with deep language understanding. In Proceedings of the Advances in Neural Information Processing Systems 35: Annual Conference on Neural Information Processing Systems 2022, NeurIPS 2022, New Orleans, LA, USA, November 28 - December 9, 2022, 2022.

Sattler, F., Müller, K., and Samek, W. Clustered federated learning: Model- agnostic distributed multitask optimization under privacy constraints. IEEE Trans. Neural Networks Learn. Syst., 32(8):3710- 3722, 2021.

Sikder, N. and Nahid, A.- A. Ku- har: An open dataset for heterogeneous human activity recognition. Pattern Recognition Letters, 146:46- 54, 2021.

Smith, V., Chiang, C., Sanjabi, M., and Talwalkar, A. Federated multi- task learning. In Proceedings of Advances in Neural Information Processing Systems 30: Annual Conference on Neural Information Processing Systems 2017, December 4- 9, 2017, Long Beach, CA, USA, pp. 4424- 4434, 2017.

Tan, A. Z., Yu, H., Cui, L., and Yang, Q. Towards personalized federated learning. IEEE Trans. Neural Networks Learn. Syst., 34(12):9587- 9603, 2023. URL https://doi.org/10.1109/TNNLS.2022.3160699.

Teh, Y. W. Dirichlet process. In Sammut, C. and Webb, G. I. (eds.), Encyclopedia of Machine Learning, pp. 280- 287. Springer, 2010.

Vahidian, S., Morafah, M., Wang, W., Kungurtsev, V., Chen, C., Shah, M., and Lin, B. Efficient distribution similarity identification in clustered federated learning via principal angles between client data subspaces. In Proceedings of the 37th AAAI Conference on Artificial Intelligence, AAAI 2023, Thirty- Fifth Conference on Innovative Applications of Artificial Intelligence, IAAI 2023, Thirteenth Symposium on Educational Advances in Artificial Intelligence, EAAI 2023, Washington, DC, USA, February 7- 14, 2023, pp. 10043- 10052. AAAI Press, 2023.

Wang, S. and Ji, M. A lightweight method for tackling unknown participation statistics in federated averaging. In Proceedings of The 12th International Conference on Learning Representations, ICLR 2024, Vienna, Austria, May 7- 11, 2024. OpenReview.net, 2024.

Wu, Q., He, K., and Chen, X. Personalized federated learning for intelligent iot applications: A cloud- edge based framework. IEEE Open J. Comput. Soc., 1:35- 44, 2020.

Wu, R., Wang, H., and Chen, H. A comprehensive survey on deep multimodal learning with missing modality. CoRR, abs/2409.07825, 2024. doi: 10.48550/ARXIV.2409.07825. URL https://doi.org/10.48550/arXiv.2409.07825.

Xiong, B., Yang, X., Song, Y., Wang, Y., and Xu, C. Client- adaptive cross- model reconstruction network for modality- incomplete multimodal federated learning. In Proceedings of the 31st ACM International Conference on Multimedia (MM), MM 2023, Ottawa, ON, Canada, 29 October 2023- 3 November 2023, pp. 1241- 1249. ACM, 2023.

Yang, L., Shakeri, A. S., Pu, L., Chen, W., and Shu, Y. Efficient clustered federated learning by locality sensitive hashing. In International Conference on Advanced Data Mining and Applications, pp. 177- 191. Springer, 2024.

Young, P., Lai, A., Hodosh, M., and Hockenmaier, J. From image descriptions to visual denotations: New similarity metrics for semantic inference over event descriptions. Trans. Assoc. Comput. Linguistics, 2:67- 78, 2014. Yu, Q., Liu, Y., Wang, Y., Xu, K., and Liu, J. Multimodal federated learning via contrastive representation ensemble. In Proceedings of the 11th International Conference on Learning Representations, ICLR 2023, Kigali, Rwanda, May 1- 5, 2023. OpenReview.net, 2023. Yu, S., Wang, J., Hussein, W., and Hung, P. C. K. Robust multimodal federated learning for incomplete modalities. Comput. Commun., 214:234- 243, 2024. URL https://doi.org/10.1016/j.comcom.2023.12.003. Zeng, H., Yue, Z., and Wang, D. Open- vocabulary federated learning with multimodal prototyping. In Proceedings of the 2024 Conference of the North American Chapter of the Association for Computational Linguistics: Human Language Technologies (Volume 1: Long Papers), NAACL 2024, Mexico City, Mexico, June 16- 21, 2024, pp. 5644- 5656. Association for Computational Linguistics, 2024. Zhang, H., Cisse, M., Dauphin, Y. N., and Lopez- Paz, D. mixup: Beyond empirical risk minimization. In 6th International Conference on Learning Representations, ICLR 2018, Vancouver, BC, Canada, April 30 - May 3, 2018, Conference Track Proceedings. OpenReview.net, 2018.

# A. Learning Settings

A. Learning SettingsIn all of our experiments, we set a global learning rate (0.1, 0.01, 0.05) in the three datasets (CrisisMMD, Food101, Flickr30k) respectively to global optimizers ( $FedAdgrad_{MPO}$ ,  $FedAdgrad$ ). Our Parameter difference calculation process is derived from Equation 13 and we used the Adam optimizer with a learning rate of 0.005 for the clients. These parameters were obtained through our simple tuning in the early stage of our experiment. The batch size for the CrisisMMD dataset was set to 15, and 7 for the UPMC-Food101 dataset, 20 for the Flickr30k dataset. All the local epoch was set to 3. The loss function on the client was cross-entropy, except for MMC (GMC-Fusion), where the loss consists of two parts: cross-entropy and contrastive loss between existing modalities (Poklukar et al., 2022).

# B. Ablation Study on Food101 and Flickr30k

B. Ablation Study on Food101 and Flickr30kWe conducted an ablation study on the Food101 and Flickr30k datasets under the settings of  $\mathrm{mm}@\mathrm{0.2}$  and  $\mathrm{mc}@\mathrm{0.5}$ . The modules are denoted as 'b', 'p', and 's' (BPI, MPO and PPS respectively following Section 5.5). The full MMiC framework demonstrated robust performance, achieving an accuracy exceeding  $90\%$  on Food101 and a score of 11 on Flickr30k under the specified conditions. In contrast, the exclusion of all modules lead to a significant decline in accuracy, reducing it to  $86\%$  on Food101 and 4.3 on Flickr30k. The removal of a single module generally yields superior performance compared to the exclusion of two modules, as presented in Table 4.

Table 4. Ablation study on Food101 and Flickr30k under  $(\mathrm{mm}\@ 0.2$ $\mathrm{mc}@\mathrm{0.5})$  

<table><tr><td>Configuration</td><td>Food101 (Accuracy)</td><td>Flickr30k (RSum)</td></tr><tr><td>w/o (b, p, s)</td><td>86.97</td><td>4.28</td></tr><tr><td>w/o s</td><td>88.54</td><td>6.99</td></tr><tr><td>w/o p</td><td>88.91</td><td>8.18</td></tr><tr><td>w/o b</td><td>87.26</td><td>8.10</td></tr><tr><td>only s</td><td>88.03</td><td>7.08</td></tr><tr><td>only p</td><td>87.49</td><td>4.84</td></tr><tr><td>only b</td><td>87.41</td><td>5.36</td></tr><tr><td>MMiC</td><td>90.12</td><td>11.02</td></tr></table>

# C. Effectiveness of Banzhaf Client Selection

C. Effectiveness of Banzhaf Client SelectionThe federated architecture had shown the feasibility of client selection, as demonstrated in (Nishio & Yonetani, 2019; Cho et al., 2020). The Banzhaf client selection strategy aimed to optimize global aggregation efficiency by minimizing communication rounds. Figure 3 illustrates the comparison of communication rounds and performance between the setting with Banzhaf client selection and random client selection across three levels of missing modality. We used Flickr30k dataset in this evaluation. It should be noted that randomly selected patterns required additional (12, 24, or 16) training rounds to surpass or approach the performance of the Banzhaf client selection.

# D. Time Complexity of MPO

Since the Banzhaf Client Selection do not incur an additional cost, the time complexity of MMiC compared to other works depended on the complexity of Markowitz Portfolio Optimization. The aggregation process of  $FedAdgrad_{MPO}$  involves the computation of RARC in each cluster, which is then uploaded to the server. The server utilizes the RARC of each cluster to dynamically adjust the beta parameter for aggregation. Since the computational complexity of beta is linear and negligible, the overall complexity of MPO is primarily dominated by the RARC computation. The time complexity of MPO is  $O(\frac{K^2}{M} \cdot T + K \cdot T)$ . Here we deduce:

We got average  $\frac{K}{M}$  clients for each cluster. Time complexity of  $\sigma_{ij}$  was  $O(T)$

Time complexity of risk  $\bar{\sigma}$  ..

$$
\bar{\sigma} = \sum_{i = 1}^{\frac{K}{M}}\sum_{j = 1}^{\frac{K}{M}}w_{i}w_{j}\sigma_{ij}.
$$

![](images/5dd8ac7422df9d78079f512739eccdcb5c0e583bbcc9e51fd7d5339489891a18.jpg)  
Figure 3. The number of additional training rounds needed for the Random Client Selection to achieve the same performance as the Banzhaf Client Selection (Flickr30k).

could be obtained from:

$$
O\left((\frac{K}{M})^2\right)\times O(T) = O\left((\frac{K}{M})^2\cdot T\right)
$$

In the covariance  $\sigma_{ij}$  section, we can employ the concept of prefix sums (Blelloch, 1990) to efficiently store historical data. The time complexity of constructing prefix sums was extra  $O(\frac{K^2}{M^2})$ . Hence this part could be improved to  $O(\frac{K^2}{M^2}) + O(\frac{K^2}{M^2})$ .

- Return  $A$  computation is to do the weighted sum for each cluster, and the time complexity of  $A$  is  $O(\frac{K}{M})$  to each cluster.

-  $M$  clusters will upload their RARC to the server and calculate the weighted sum:

$$
\left(O(\frac{K^2}{M^2} +\frac{K}{M}) + O(\frac{K^2}{M^2})\right)\times M = O(\frac{K^2}{M} +K) + O(\frac{K^2}{M})
$$

Meanwhile, MMiC has a total  $T$  global rounds. As prefix sums only build at once, we got:

$$
O(\frac{K^2}{M} +K)\times T + O(\frac{K^2}{M}) = O\left(\frac{K^2\cdot T}{M} +K\cdot T + \frac{K^2}{M}\right)
$$

$O(\frac{K^2}{M})$  was less than  $O(\frac{K^2\cdot T}{M})$  apparently. Therefore, the time complexity of MPO is  $O(\frac{K^2}{M}\cdot T + K\cdot T)$ .