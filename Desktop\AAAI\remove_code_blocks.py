#!/usr/bin/env python3
"""
<PERSON>ript to remove all code blocks from research_plan.md
"""

import re

def remove_code_blocks(file_path):
    """Remove all code blocks from the markdown file"""
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()

    # Split content into lines for line-by-line processing
    lines = content.split('\n')
    cleaned_lines = []
    in_code_block = False

    for line in lines:
        # Check if we're starting a code block
        if line.strip().startswith('```python') or line.strip().startswith('```'):
            in_code_block = True
            continue

        # Check if we're ending a code block
        if in_code_block and line.strip() == '```':
            in_code_block = False
            continue

        # If we're not in a code block, keep the line
        if not in_code_block:
            cleaned_lines.append(line)

    # Join lines back together
    cleaned_content = '\n'.join(cleaned_lines)

    # Clean up multiple empty lines
    cleaned_content = re.sub(r'\n\s*\n\s*\n+', '\n\n', cleaned_content)

    # Write back to file
    with open(file_path, 'w', encoding='utf-8') as f:
        f.write(cleaned_content)

    print(f"Code blocks removed from {file_path}")

if __name__ == "__main__":
    file_path = "writing/research_plan.md"
    remove_code_blocks(file_path)
