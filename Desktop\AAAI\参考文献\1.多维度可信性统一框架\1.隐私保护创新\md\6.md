# LAPA-based Dynamic Privacy Optimization for Wireless Federated Learning in Heterogeneous Environments

<PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, IEEE, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON>, Fellow, IEEE

Abstract—Federated Learning (FL) is a distributed machine learning paradigm based on protecting data privacy of devices, which however, can still be broken by gradient leakage attack via parameter inversion techniques. Differential privacy (DP) technology reduces the risk of private data leakage by adding artificial noise to the gradients, but detrimental to the FL utility at the same time, especially in the scenario where the data is Non- Independent Identically Distributed (Non- IID). Based on the impact of heterogeneous data on aggregation performance, this paper proposes a Lightweight Adaptive Privacy Allocation (LAPA) strategy, which assigns personalized privacy budgets to devices in each aggregation round without transmitting any additional information beyond gradients, ensuring both privacy protection and aggregation efficiency. Furthermore, the Deep Deterministic Policy Gradient (DDPG) algorithm is employed to optimize the transmission power, in order to determine the optimal timing at which the adaptively attenuated artificial noise aligns with the communication noise, enabling an effective balance between DP and system utility. Finally, a reliable aggregation strategy is designed by integrating communication quality and data distribution characteristics, which improves aggregation performance while preserving privacy. Experimental results demonstrate that the personalized noise allocation and dynamic optimization strategy based on LAPA proposed in this paper enhances convergence performance while satisfying the privacy requirements of FL.

Index Terms—Federated learning, wireless communication, differential privacy, heterogeneous environments.

# I. INTRODUCTION

T RADITONAL distributed machine learning requires col- lecting raw data from devices to a parameter server for centralized model training, which is a paradigm that offers high accuracy. However, in many privacy- sensitive scenarios such as speech recognition [1], [2], medical diagnosis

[3]- [5], and financial services [6], [7]- clients are often unwilling to share local private data with any entity, including the parameter server. Federated learning (FL) requires devices to train models locally and share only training parameters, providing privacy protection and thus has found broad application.

However, the training network and the resulting model information in FL are inherently vulnerable to attacks that compromise privacy, leading to serious privacy leakage risks such as Gradient Leakage Attacks (GLA) [8]- [12]. In such attacks, attackers construct synthetic data to approximate the original training data by minimizing the difference between virtual gradients and shared real gradients. Other common privacy attacks include membership inference attacks [13]- [15], class representative reconstruction attacks [16], [17], and property inference attacks [18]. The work by [19] demonstrated a model inversion attack that recovers images from a facial recognition system. Therefore, it is necessary to protect the privacy of each device's data in FL systems.

Differential privacy (DP) is an effective privacy protection technique and has been widely used in FL in recent years. The work in [20] introduced the basic theory of DP. According to this work, random noise is added to parameters to enable privacy protection in the system. Under the DP mechanism, designing a proper privacy allocation strategy is an important research topic, as it affects both the level of privacy protection for each device and the convergence performance of the FL system.

The work in [21] proposed a method that combines DP with secure multi- party computation to enhance the security of FL. In [22], the authors showed that under a given privacy level, the FL system can achieve good convergence performance when the number of participating devices is large enough. However, these works only consider security or privacy during the global model update phase, while the upload phase also needs to be addressed. In addition, they do not provide a theoretical explanation of the relationship between DP and FL convergence performance. The work in [23] considers both the upload phase and the global update phase, and mathematically explains the impact of DP on FL convergence. [24] further examines the impact of the number of global iterations on DP- FL. However, most existing works that apply DP to FL do not consider the Non- Independent Identically Distributed (Non- IID) data in real scenarios, which significantly degrades the aggregation performance of FL when noise is added, and

may even lead to divergence. X. You et al. [25] proposed a privacy- preserving aggregation scheme under time- varying data distribution. However, they did not consider that the privacy protection requirement may vary during the FL process, resulting in excessive redundant noise that reduces aggregation performance. The work in [26] proposed a dynamic privacy budget allocation strategy by quantifying the Risk of Privacy Leakage (RoPL), but the algorithm requires devices to upload local test results, increasing communication overhead. Moreover, it actively launches additional gradient leakage attacks to determine the required privacy budget level, which leads to low computational efficiency. The strategy in [27] improves computational efficiency, but offers limited customization for Non- IID data. In summary, there is still a lack of lightweight and customized privacy allocation strategies for FL systems under Non- IID scenarios.

In wireless FL with DP, communication quality is another important factor affecting learning performance. On the one hand, the impact of channel noise in practical communication environments introduces inherent interference to the transmitted model, which can partially function as the artificial noise in DP. In extreme cases, it may even allow the system to meet DP requirements directly. However, convergence performance under such high- noise conditions is usually poor. Under normal environmental noise, some studies attempt to balance wireless communication and privacy performance. The work in [28] proposed a DP- protected FL resource allocation scheme for multi- cell networks, which minimizes the total privacy leakage by jointly optimizing device association, transmission power, and DP noise power, which balances privacy and communication efficiency. The work in [29] proposed a privacy- preserving and communication- efficient FL (P2CEFL) algorithm based on sparse gradients and jittered quantization to reduce communication cost under DP guarantees. Most existing works that aim to balance communication performance and privacy focus on improving communication efficiency under privacy constraints, but they do not address the aggregation quality degradation caused by DP noise. To our knowledge, the only work that aims to improve aggregation performance under privacy constraints by enhancing communication quality is [30], which jointly optimizes DP noise levels and user transmission power to balance privacy protection and communication quality. However, this work does not consider the Non- IID data, and its method of balancing communication and privacy is limited, as it requires artificial noise to be added throughout the entire training process to meet DP, which overlooks the potential contribution of environmental noise. Therefore, under Non- IID conditions, an efficient privacy aggregation scheme is needed that adapts to FL iterations and takes both communication quality and data heterogeneity into account. On the other hand, under Non- IID conditions, jointly considering communication quality to improve aggregation performance is also important.

Inspired by the above works on privacy solutions, this paper proposes the Lightweight Adaptive Privacy Allocation (LAPA) strategy based on the dynamic impact of heterogeneous data on aggregation performance. The LAPA strategy is lightweight and requires no additional transmission beyond the local gra dients already needed by the FL algorithm, which helps reduce network communication load and improve computational efficiency. Then, we propose a dynamic noise control mechanism by fully utilizing the potential DP disturbance function of environmental noise. When the level of adaptively attenuated artificial DP noise aligns with the impact of communication noise, we stop adding extra artificial noise to the system, as the inherent environmental noise is already sufficient to meet the DP requirement. The optimal balance point between artificial noise and communication noise is determined by optimizing the transmission power, which achieves the best trade- off between DP protection and system utility. Finally, the Wasserstein distance is used to quantify the Non- IID degree of data distribution across devices, and a user selection mechanism based on devices' SNRs is designed to determine the aggregation weights, which further ensures aggregation performance while preserving privacy.

In summary, the main contributions of this paper are as follows:

This paper designs a new LAPA algorithm. To address the Non- IID nature of data distribution and the evolving RoPL during global updates, an efficient privacy allocation strategy is proposed based on global gradient updates and local gradient contributions. This paper fully utilizes the distribution characteristics of data and communication capabilities across devices to assign customized aggregation weights, maximizing system aggregation performance under privacy constraints. This paper designs a new dynamic noise control mechanism. Specifically, before the  $T_{th}$ - th global update, the proposed LAPA strategy is used to calculate the required artificial noise to satisfy DP; after the  $T_{th}$ - th update, the inherent environmental noise is used to meet the DP requirement. This paper analyzes the convergence upper bound of wireless FL under the proposed noise allocation mechanism. It provides both privacy guarantees and convergence guarantees of the proposed strategy, and further formulates a transmission power optimization problem based on the obtained convergence bound, determining the optimal switching time  $T_{th}$  via the Deep Deterministic Policy Gradient (DDPG) algorithm for noise allocation to maximize the aggregation utility of wireless FL.

The remainder of this paper is structured in the following manner: Section II describes the system model, including the learning model, the wireless communication model, and the privacy threat model. Section III describes the efficient privacy aggregation strategy based on proposed LAPA algorithm. Based on the LAPA algorithm, Section IV describes the proposed dynamic noise control optimization and provides a convergence analysis to formulate the optimization problem with respect to the transmit power. Section V describes the simulations. Section VI provides the conclusions.

Notation: Upper- and lower- case boldface letters denote matrices and vectors, respectively;  $\mathbb{R}^n$  denotes the  $n$ - dimensional real vector space;  $\mathbb{C}^{n_1 \times n_2}$  denotes the  $n_1 \times n_2$ - dimensional complex space;  $|\cdot |$  denotes modulus;  $|\cdot | \cdot ||$  denotes Euclidean

norm;  $\nabla (\cdot)$  and  $\nabla^2 (\cdot)$  take gradient and Laplacian, respectively;  $diag(\cdot)$  stands for a diagonal matrix;  $\mathbb{E}(\cdot)$  takes mathematical expectation;  $(\cdot)^T$  and  $(\cdot)^H$  stand for transpose and conjugate transpose, respectively.

# II. SYSTEM MODEL

We consider an FL system consisting of a BS equipped with  $N_{a}$  antennas, serving as the parameter server, and  $K$  single- antenna devices. The  $k$ - th device  $(k = 1,2,\ldots ,K)$  has its own local data set  $\mathcal{D}_k$ . Consider an FL algorithm with the input data vector  $\pmb{x}_{ks}\in \mathbb{R}^d$  and the corresponding output  $y_{ks}\in \mathbb{R}$  where  $s\in \{1,\dots ,|\mathcal{D}_k|^2\}$  is the index of a data sample. Let  $\pmb {w}_k$  denote the local model parameters trained on the  $k$ - th device.

# A. Learning Model

For edge devices, the goal of local training is to find the optimal model  $\pmb{w}^{*}$  that minimizes the training loss. Without loss of generality, the model parameter  $\pmb {w}\in \mathbb{R}^q$  (where  $q$  denotes the model size) has its local gradient with respect to dataset  $\mathcal{D}_k$  at the  $t$ - th communication round defined as

$$
\nabla F_{k}\left(\pmb{w}^{[t]}\right) = \frac{1}{|\mathcal{D}_{k}|}\sum_{\substack{(\pmb{x}_{ks},y_{ks})\in \mathcal{D}_{k}}}\nabla f_{k}\left(\pmb{x}_{ks},y_{ks};\pmb{w}^{[t]}\right), \tag{1}
$$

where  $f_{k}(\pmb{x}_{ks},y_{ks};\pmb {w})$  denotes the sample loss.

To minimize the global loss function, FL proceeds through multiple rounds of gradient/parameter transmission and iteration until convergence. In each communication round, the BS aggregates gradients received from  $K$  users as follows:

$$
\nabla F(\pmb{w}^{[t]}) = \sum_{k = 1}^{K}G_{k}\cdot \nabla F_{k}(\pmb{w}^{[t]}), \tag{2}
$$

where  $\sum_{k = 1}^{K}G_{k} = 1$  represents the aggregation weights. Typically, when using the FedAvg algorithm, the aggregation weight is set as  $G_{k} = |\mathcal{D}_{k}| / \sum_{k = 1}^{K}|\mathcal{D}_{k}|$ . However, under Non- IID conditions, this aggregation strategy clearly fails to achieve efficient convergence. Therefore,  $G_{k}$  needs to be redesigned based on practical scenarios, which will be detailed in Section III- C.

Finally, the global model is updated as

$$
\pmb{w}^{[t + 1]} = \pmb{w}^{[t]} - \lambda \sum_{k = 1}^{K}G_{k}\cdot \nabla F_{k}\left(\pmb{w}^{[t]}\right), \tag{3}
$$

where  $\lambda$  denotes the learning rate.

# B. Communication Model

The transmission of local gradients and global parameters between devices and the BS is carried out through a wireless communication system. The NOMA framework used in this paper allows multiple devices to transmit data simultaneously over a superimposed wireless channel, effectively alleviating communication congestion caused by multiple devices in FL.

A block fading channel model is assumed, where the channel coefficients remain constant throughout the training process. Let  $\pmb {h}_k\in \mathbb{C}^{N_a\times 1}$  denote the channel coefficient vector from the  $k$ - th device to the BS, with magnitude modeled as an independent random variable. It is assumed that perfect channel state information is available at both the BS and the devices. Therefore, at the  $t$ - th aggregation round, the signal received at the BS is given by:

$$
\pmb{y}^{[t]} = \sum_{k = 1}^{K}p_{k}\pmb{h}_{k}\pmb{s}_{k}^{[t]} + \pmb{n}_{0}, \tag{4}
$$

where  $p_k\in \mathbb{C}$  is the transmit power scalar of the  $k$ - th device,  $\pmb {s}_k$  is the gradient vector transmitted from the device to the BS, and  $\pmb {n}_0\in \mathbb{C}^{N_a\times q}$  is the Additive White Gaussian Noise (AWGN) with each element following a distribution  $CN(0,\sigma_{n_0}^2)$ , where  $\sigma_{n_0}^2$  denotes the environmental noise power.

The transmit power of the  $k$ - th device is constrained by:

$$
\mathbb{E}\left(|p_ks_k|^2\right) = |p_k|^2\leq P_0, \tag{5}
$$

where  $P_0 > 0$  is the upper bound of the maximum transmit power.

The Serial Interference Cancellation (SIC) technique based on least squares estimation is adopted to address transmission interference. Therefore, the received Signal- to- Interference- plus- Noise Ratio (SINR) of the  $k$ - th device is:

$$
\gamma_{k} = \frac{p_{k}^{2}|r_{k}h_{k}|^{2}}{\sum_{i = k + 1}^{K}p_{i}^{2}|r_{k}h_{i}|^{2}}\frac{1}{||r_{k}||^{2}\sigma_{n}^{2}}, \tag{6}
$$

where  $r_k$  is the linear receiver. To explicitly reflect the impact of environmental noise on the transmission system, we assume that the SIC technique can fully cancel inter- device interference and let  $\pmb {r}_k = \pmb {h}_k^H$ . As a result, the BS decodes the gradient signal from the  $k$ - th device as  $\hat{\pmb{s}}_k = \pmb {s}_k + \frac{\sigma_{n_0}}{p_k\|h_k\|}$ , where  $\frac{\sigma_{n_0}}{p_k\|h_k\|}$  denotes the influence of environmental noise on the transmission system.

# C. Threat Model

In the FL system, we assume that all participants other than the devices are honest- but- curious, especially the BS acting as the parameter server, which means that the attacker follows the training protocol honestly but is curious about the private data of target devices. The attacker always attempts to recover private data during the training or testing phase by reversing the shared model parameters between devices and the BS. Therefore, even though FL allows training and storing private data locally, there is still a RoPL, which is a common assumption in most studies analyzing potential privacy risks in FL. Specifically, this paper assumes that the attacker may possess a dataset  $\mathcal{D}_{attack}$  that overlaps with the data of some participating devices. The attacker then tries to use a subset  $\mathcal{D}_{attack}^{\prime}$  to simulate the local training of the  $k$ - th device, and infers private data of the device by exploiting the difference in model outputs between training and non- training samples. Hence, this model is realistic and reasonable.

# III. EFFICIENT PRIVACY AGGREGATION BASED ON LAPA

# A. Definition of DP

To ensure the privacy of algorithms on datasets, the DP mechanism provides a strong guarantee through the parameters

$\epsilon$  and  $\delta_{dp}$ . Suppose the dataset  $\mathcal{D}$  contains neighboring datasets  $d$  and  $d^{\prime}$ , which differ by only one sample.  $\epsilon > 0$  defines the distinguishability bound for all outputs of the mapping  $\mathcal{M}$  when applied to neighboring datasets  $d$  and  $d^{\prime}$ . A larger value of  $\epsilon$  indicates a higher distinguishability between the neighboring datasets, and thus a greater RoPL. The parameter  $\delta_{dp}$  is not part of the original DP definition, but in the extended version proposed by Dwork et al. [31], it allows relaxation of the strict  $\epsilon$ - DP requirement, making DP more flexible in practice.  $\delta_{dp}$  represents the probability that the output difference between two neighboring datasets is not bounded by  $e^{\epsilon}$  after applying the privacy mechanism, i.e., the probability of DP failure. The definition of  $(\epsilon , \delta_{dp})$ - DP is as follows.

Definition 1  $((\epsilon , \delta_{dp})$ - DP [32]: A randomized mechanism  $\mathcal{M}: \mathcal{D} \rightarrow \mathcal{R}$ , with domain  $\mathcal{D}$  and range  $\mathcal{R}$ , satisfies  $(\epsilon , \delta_{dp})$ - DP if for any two adjacent inputs  $d, d^{\prime} \in \mathcal{D}$  and any output  $S \in \mathcal{R}$ , the following holds:

$$
\operatorname *{Pr}[\mathcal{M}(d)\in \mathcal{S}]\leq e^{\epsilon}\operatorname *{Pr}[\mathcal{M}(d^{\prime})\in \mathcal{S}] + \delta_{dp}. \tag{7}
$$

This ensures that the output probabilities for neighboring datasets are similar within a bounded range, and the deviation is controlled by  $\delta_{dp}$ .

A common method to approximate a deterministic real- valued function  $\mathcal{M}: \mathcal{D} \rightarrow \mathcal{R}$  under the DP mechanism is to add Gaussian noise with zero mean and variance  $\sigma^{2} \mathbf{I}$  to each coordinate of the function output  $s(d)$ , where  $\mathbf{I}$  is the identity matrix of the same dimension as  $s(d)$ . This process is expressed as:

$$
\mathcal{M}(d) = s(d) + \mathcal{N}(0,\sigma^2\mathbf{I}). \tag{8}
$$

The  $l_{2}$ - norm- based sensitivity is defined as:

$$
\Delta s = \max_{d,d^{\prime}\in \mathcal{D}}\| s(d) - s(d^{\prime})\|_{2}, \tag{9}
$$

which provides an upper bound on the amount of perturbation that needs to be added to the output for privacy protection. Repeated application of this noise- adding DP mechanism can be implemented using the basic composition theorem, the advanced composition theorem, or their improvements, which enables devices to design personalized DP guarantees.

# B. LAPA Algorithm

To satisfy the DP requirement of wireless FL, this paper adopts the  $(\epsilon , \delta_{dp})$ - DP criterion to apply noise perturbation to the gradients to be transmitted. In learning systems with Non- IID data condition, applying a uniform level of artificial noise to all devices can meet the privacy requirement, but it severely degrades the convergence performance of the system due to the significant data distribution differences and additional noise disturbances. Therefore, it is necessary to design a personalized privacy allocation scheme. Fig. 1 illustrates the wireless FL framework based on LAPA proposed in this paper. In the  $t$ - th global aggregation round, heterogeneous devices train local model gradients based on their local data, compute customized artificial noise power according to the designed privacy budget allocation strategy, and add noise to each element of the gradient before transmitting it to the BS for aggregation. This process is repeated until the system converges. The following section introduces the privacy allocation algorithm in detail.

Section III- C of [33] points out that the RoPL changes with the progress of FL. Specifically, in the early stages of training, devices face a higher RoPL, while this risk decreases rapidly as the training process converges, following an approximately exponential trend. Therefore, the privacy budget in each FL iteration can be allocated according to the training progress. As a result, quantifying the progress of FL training is important.

The training progress of FL can be quantified by the change in the global gradient over communication rounds. Specifically, in the  $t$ - th communication round, the BS aggregates the locally perturbed gradients  $\tilde{\mathbf{g}}_{t}^{[t]} = \nabla F_{k}(\tilde{\mathbf{w}}^{[t]})$  to form the global perturbed gradient  $\tilde{\mathbf{g}}^{[t]} = \nabla F(\tilde{\mathbf{w}}^{[t]})$ , which is stored in a memory unit. Then, the "difference" in the global gradient relative to its historical state is used as an indicator to measure training progress (i.e., whether convergence is approaching), which helps quantify the RoPL at each communication round.

A variant of the Proportional- Integral- Derivative (PID) algorithm [34] is used to describe the dynamic changes of the global gradient, as it is highly sensitive to data and effectively guides the allocation of the privacy budget  $\epsilon^{[t]}$  for each communication round, even when the changes are small. Compared to the work in [33], the advantage of using the global gradient to reflect training progress in this paper is that no additional information needs to be transmitted—such as accuracy on a public validation set—other than the local gradients already required by FL, which significantly reduces communication overhead. The storage unit on the BS records the global gradient information of round  $t$  and its historical updates as  $\nabla F(\tilde{\mathbf{w}}) = \left[\nabla F(\tilde{\mathbf{w}}^{[1]}), \nabla F(\tilde{\mathbf{w}}^{[2]}), \ldots , \nabla F(\tilde{\mathbf{w}}^{[t - 1]})\right]$ . From the interval  $[t - m, t]$  in  $\nabla F(\tilde{\mathbf{w}})$ , the BS randomly selects  $m$  samples, where the index of the current sample is  $i_{m}$  and the adjacent sample is  $i_{m - 1}$ , to compute the PID- like error. The feedback error between the current sample and its adjacent sample in terms of the global gradient is given by:

$$
E^{(i_m)} = \left|\nabla F(\tilde{\pmb{w}}^{[i_m]}) - \nabla F(\tilde{\pmb{w}}^{[i_{m - 1}]})\right|. \tag{10}
$$

Then, the PID- like error of round  $t$  on the sampled subset of  $\nabla F(\tilde{\mathbf{w}})$  is calculated as:

$$
e^{[t]} = K_pE^{(i_m)} + K_s\left(\frac{1}{m}\sum_{i = n - m + 1}^m E^{(i_s)}\right), \tag{11}
$$

where  $K_{p}$  and  $K_{s}$  denote the proportional and integral coefficients, respectively.

Let the total privacy budget of the system be  $\epsilon^T$  ,which is first allocated to each communication round, resulting in  $\epsilon^{[t]}$  . We require that  $\epsilon^{[t]}$  be monotonically non- decreasing with respect to the RoLP, i.e., monotonically non- increasing with respect to the training progress, and that their total sum does not exceed the total privacy budget  $\epsilon^T$  . Let  $\begin{array}{r}\epsilon_c = \sum_{i = 1}^{t - 1}\epsilon^{[t]} \end{array}$  denote the privacy budget consumed up to the  $t$  - th round. The remaining privacy budget is allocated to each communication

![](images/a892dcf0166f3f68ceda34eed558ca7a05577ad23b914678862af07fb45e5cb9.jpg)  
Fig. 1: The workflow of proposed LAPA-based FL, involving only gradient exchange between devices and the BS, without incurring additional communication overhead, making it simpler and more efficient than the algorithm in [33].

round based on the nearly exponential trend of RoLP with respect to FL progress, as follows:

$$
\epsilon^{[t]} = \exp (-e^{[t]})\cdot \frac{\epsilon^T - \epsilon_c}{T - t + 1},\quad s.t.\sum_{t = 1}^{T}\epsilon^{[t]}\leq \epsilon^T. \tag{12}
$$

Next, to meet the personalized privacy requirements of devices with heterogeneous characteristics and to better balance FL aggregation performance and privacy utility, we further allocate  $\epsilon^{[t]}$  to each heterogeneous device. Data distribution heterogeneity leads to a deviation between local and global gradients, meaning that devices exhibit heterogeneity in their local gradient updates. Therefore, to achieve better learning performance under privacy constraints, we design a personalized privacy budget allocation scheme based on the data heterogeneity of devices. In the  $t$ - th round, the deviation of device  $k$ 's local gradient from the global gradient is expressed as:

$$
\theta_{k}^{[t]} = \arccos \left(\frac{\left\langle\nabla F(\tilde{\pmb{w}}^{[t]}),\nabla F_{k}(\tilde{\pmb{w}}^{[t]})\right\rangle}{\left\|\nabla F(\tilde{\pmb{w}}^{[t]})\right\|\cdot\left\|\nabla F_{k}(\tilde{\pmb{w}}^{[t]})\right\|}\right), \tag{13}
$$

where a smaller  $\theta_k^{[t]}$  indicates a smaller "difference" between the device's local gradient and the global gradient in the current round, meaning the device contributes more to the global update and should receive stronger privacy protection.

To suppress the instability of random angles, we smooth the angle between round  $t$  and round  $t - 1$  as follows:

$$
\tilde{\theta}_{k}^{[t]} = \left\{ \begin{array}{ll}\theta_{k}^{[t]}, & t = 1\\ \frac{t - 1}{t}\tilde{\theta}_{k}^{[t - 1]} + \frac{1}{t}\theta_{k}^{[t]}, & t > 1 \end{array} \right.. \tag{14}
$$

Then, the following mapping function is used to quantify the dynamic contribution of the  $k$ - th device in round  $t$ :

$$
f(\tilde{\theta}_k^{[t]}) = \beta \left(1 - \exp \left(-\exp \left(-\beta (\tilde{\theta}_k^{[t]} - 1)\right)\right)\right), \tag{15}
$$

where  $\beta$  is the decay factor. This mapping function is a monotonically non- decreasing function with respect to the smoothed angle. A smaller smoothed angle indicates that the device's local gradient is closer to the global gradient in the current round, representing a higher contribution. Therefore, this function can be used to measure the dynamic contribution of devices with respect to data distribution.

The learning system in this paper is under the Non- IID condition. Therefore, the dynamic contributions of devices are generally independent in the  $t$ - th round. Accordingly, the privacy budget  $\epsilon^{[t]}$  of round  $t$  can be personalized and allocated to each device based on the relative magnitude of their dynamic contributions, serving as guidance for the next update round:

$$
\epsilon_{k}^{[t + 1]} = \frac{f(\tilde{\theta}_{k}^{[t]})}{\sum_{k = 1}^{K}f(\tilde{\theta}_{k}^{[t]})}\cdot \epsilon^{[t]},\quad s.t.\quad \sum_{k = 1}^{K}\epsilon_{k}^{[t + 1]}\leq \epsilon^{[t]}. \tag{16}
$$

The advantages of the LAPA algorithm proposed in this paper are as follows: 1) The privacy budget allocation in [27]

is relatively coarse, simply increasing or decreasing the next round's privacy budget without capturing the heterogeneity among devices. In fact, it is not a truly personalized privacy budget allocation scheme. In contrast, our method achieves personalization based on the measurement of data heterogeneity; 2) The user- level privacy budget allocation algorithm in [33] is overly complex, requiring manual simulation of gradient leakage attacks in each round, which reduces computational efficiency. Our algorithm is much simpler and does not require manually simulating gradient leakage attacks.

In this paper, the parameter server is assumed to be semihonest. Therefore, to prevent eavesdropping by third parties and potential leakage by the server, we directly address data and privacy heterogeneity among devices [35]. In other words, we focus on privacy performance in the uplink phase. At the same time, this customized privacy protection mechanism helps achieve better model accuracy and privacy guarantees, as confirmed by simulation results.

To satisfy DP guarantees, each local gradient must be clipped before adding artificial noise. The clipping is performed under the  $l_{2}$  - norm as follows:

$$
\pmb {g}_k^{[t]} = \frac{\pmb{g}_k^{[t]}}{\max \left(1,\left\|\pmb{g}_k^{[t]}\right\| / C\right)}, \tag{17}
$$

where  $C$  is the clipping threshold. This ensures that when  $\left\| \pmb {g}_k^{[t]}\right\| \leq C$ $\pmb {g}_k^{[t]}$  is retained as is; otherwise, its norm is constrained within  $C$  . This helps reduce the impact of added noise on the gradient, mitigating its negative effect on training updates while still satisfying DP. Accordingly, the  $l_{2}$  - norm sensitivity of the local gradient can be expressed as:

$$
\begin{array}{rl} & {\Delta s = \underset {\mathcal{D}_k,\mathcal{D}_k^{\prime}}{\max}\left\| s^{\mathcal{D}_k} - s^{\mathcal{D}_k^{\prime}}\right\|}\\ & {\quad = \underset {\mathcal{D}_k,\mathcal{D}_k^{\prime}}{\max}\left\| \arg \min_{w_k}\left[\pmb {g}_k^{\mathcal{D}_k}\right] - \arg \min_{w_k}\left[\pmb {g}_k^{\mathcal{D}_k^{\prime}}\right]\right\|}\\ & {\quad = \frac{2\lambda C^{\prime}}{|\mathcal{D}_k|}.} \end{array} \tag{18}
$$

Finally, before uploading to the BS, noise is added as follows:

$$
\hat{\pmb{g}}_k^{[t]} = \pmb {g}_k^{[t]} + \pmb {\eta}_k^{[t]}, \tag{19}
$$

where  $\eta_{k}$  is an AWGN vector following a complex Gaussian distribution  $\mathcal{CN}(0,\sigma_k^2)$  . The noise scale  $\sigma_k^2$  satisfies  $\sigma_k^{[t]}\geq$ $\frac{c\Delta s_k}{\epsilon_k^2}$  . where the constant  $c\geq \sqrt{2\ln\left(\frac{1.25}{\epsilon_{dp}^2}\right)},\epsilon_k^{[t]}\in (0,1)$  Typically,  $\delta_{dp}$  represents the probability of events where the output difference between two adjacent datasets is not bounded by  $\epsilon_{k}^{[t]}$  after applying the privacy protection mechanism. Given any  $\delta_{dp}$  , a larger  $\epsilon_k^{[t]}$  leads to clearer distinguishability between neighboring datasets, resulting in a higher RoPL.

# C. Aggregation Weights with Heterogeneity

In this section, under the premise of ensuring system- level privacy protection, we propose an aggregation strategy that addresses both data and communication heterogeneity. The aggregation weights  $G_{k}$  are determined using the Wasserstein distance and a device selection mechanism based on SINR.

Compared with the traditional FedAvg method, which assigns weights solely based on data volume, our aggregation strategy achieves more efficient and accurate convergence under system heterogeneity.

The Wasserstein distance can be used to measure the similarity between two probability distributions. Compared with Kullback- Leibler (KL) divergence and Jensen- Shannon (JS) divergence, it offers better smoothness, as it can address the gradient vanishing problem. This is because, when two distributions do not overlap or have only slight overlap, the JS divergence remains constant (log2), while the KL divergence tends to infinity. In contrast, the Wasserstein distance can still reflect the distance between the two distributions under such conditions. Therefore, using the Wasserstein distance is beneficial for generating stable device aggregation weights in heterogeneous wireless FL systems.

The Wasserstein distance between two distributions can be computed using the probability mass functions (PMFs)  $pmf_{k}$  and  $pmf_{G}$  , and is defined as follows:

$$
W_{k}(pmf_{k},pmf_{G}) = \inf_{\Gamma \in \Pi (pmf_{k},pmf_{G})}\mathbb{E}_{(x,y)\sim \Gamma}[\| x - y\| ], \tag{20}
$$

where  $pmf_{k}$  represents the PMF of the label distribution in the local dataset of device  $k$ $pmf_{G}$  represents the PMF of the global dataset labels, and  $\Gamma$  denotes the set of all possible joint distributions. This formulation allows us to quantify the data heterogeneity of each device.

Based on the quantification of device data heterogeneity, we further compute the aggregation weight of device  $k$  when selected for training using the Softmax function:

$$
G_{k} = \frac{|\mathcal{D}_{k}|\cdot e^{1 / W_{k}}}{\sum_{k = 1}^{K}|\mathcal{D}_{k}|\cdot e^{1 / W_{k}}}. \tag{21}
$$

This provides a stable and smooth aggregation weight. At the same time, the weight assignment allows relatively homogeneous devices to contribute more to the global update, while relatively heterogeneous devices contribute less. This helps balance the influence of each device on the global model under data heterogeneity, thereby improving the convergence and overall performance of FL.

In addition to data heterogeneity, devices also exhibit significant differences in communication capabilities, which further affects the convergence efficiency of FL, as transmission errors can severely degrade the quality of global model updates. Therefore, device selection based on communication heterogeneity is critical. Accordingly, we introduce a communication- aware device selection mechanism based on the SINR, computed by  $\gamma_{k}$  to determine whether a device participates in training. The aggregation weight of a device is then updated as:

$$
G_{k} = \left\{ \begin{array}{ll}\frac{|\mathcal{D}_{k}|\cdot e^{1 / W_{k}}}{\sum_{k = 1}^{K}|\mathcal{D}_{k}|\cdot e^{1 / W_{k}}}, & \gamma_{k}\geq \gamma_{th}\\ 0, & \gamma_{k}< \gamma_{th}. \end{array} \right. \tag{22}
$$

If the threshold is set too high, the BS will receive only a small number of local models with little or no error, resulting in insufficient training. If the threshold is set too low, local models with large errors may be accepted and used in global

![](images/6ad43110570383ed6052561095bd06e8fdbf5a9cae0962eeca9cedad0992aa58.jpg)  
Fig. 2: dynamic noise control mechanism.

aggregation, leading to accumulated training errors. Both cases degrades the accuracy of FL.

# IV. DYNAMIC NOISE CONTROL OPTIMIZATION

# A. Dynamic Noise Control Mechanism

The aforementioned LAPA algorithm introduces artificial noise throughout the entire training process of FL to ensure compliance with DP requirements. However, continuously relying on artificial noise may introduce excessive and unnecessary noise into the system, reducing convergence performance. It is worth noting that the RoPL decreases as training progresses (as discussed in Section III- B). Meanwhile, the inherent channel noise in the communication environment, represented by  $\frac{\sigma n_0}{p_k\|h_k\|}$ , has the potential to satisfy DP constraints. Therefore, this section proposes a dynamic noise control mechanism that applies different noise injection strategies at different stages of training, aiming to optimize overall FL performance while ensuring privacy protection.

Specifically, in the early stage of training (i.e., before the  $T_{th}$ - th round of global aggregation), artificial noise is added according to the LAPA algorithm to ensure strict DP compliance and effectively suppress privacy leakage risks during the initial phase of system training. In the later stage of training (i.e., after the  $T_{th}$ - th round of global aggregation), the inherent environmental noise in the wireless channel is leveraged as a privacy- preserving mechanism to replace artificial noise. This reduces unnecessary noise interference while still ensuring that the system meets DP requirements. The proposed dynamic noise control mechanism is illustrated in Fig. 2.

The determination of  $T_{th}$  depends on the timing where the required level of artificial noise decreases with training progress and aligns with the interference level of the communication environment noise, i.e.,

$$
\sum_{k = 1}^{K}\frac{\sigma_{n_0}}{p_k\|h_k\|} = \sum_{k = 1}^{K}\frac{\Delta s_k\cdot\sqrt{2\ln(1.25 / \delta_{dp})}}{\epsilon_k^{[T_{th}]}}. \tag{23}
$$

This equation ensures that after the  $T_{th}$ - th round of global aggregation, relying solely on environmental noise is sufficient to meet DP constraints. This noise control mechanism not only adapts dynamically to changes in privacy protection needs, but also minimizes the convergence performance degradation caused by excessive noise, while maintaining DP compliance. As a result, it improves communication efficiency and the convergence stability of the global model in FL systems.

# B. Convergence Analysis and Problem Formulation

This section analyzes the convergence of FL based on the proposed LAPA algorithm and dynamic noise control mechanism. The proposed dynamic noise control mechanism leverages the combination of artificial noise and communication noise to satisfy DP constraints, where the artificial noise is generated by the LAPA algorithm and the communication interference arises from environmental noise. The transmission power of devices determines the interference intensity of communication noise, affecting the dynamic injection level of artificial noise and ultimately determining the timing where it can be reduced to match the communication interference. This process is critical to the convergence performance of FL. Therefore, it is necessary to analyze convergence to quantify the impact of transmission power on FL aggregation performance under the LAPA algorithm and the dynamic noise control mechanism.

To analyze the convergence of FL, we introduce assumptions A1- A4:

A1  $F_{k}$  is  $L$ - smooth, i.e.,  $\| F_k(\pmb {w}) - F_k(\pmb {w}')\| \leq L\| \pmb {w} - \pmb {w}'\|$ . According to this assumption and the triangle inequality,  $F(\pmb {w})$  is also  $L$ - smooth. Additionally, its gradient is Lipschitz continuous;

A2  $F_{k}$  is  $\mu$ - strongly convex, i.e.,  $F_{k}(\pmb{w}^{[t + 1]}) \geq F_{k}(\pmb{w}^{[t]}) + (\pmb{w}^{[t + 1]} - \pmb{w}^{[t]})^{\top} \nabla F(\pmb{w}^{[t]}) + \frac{\mu}{2} \| \pmb{w}^{[t + 1]} - \pmb{w}^{[t]}\|^{2}$ ;

A3  $F(\pmb {w})$  is second- order continuously differentiable. Based on this, together with A1 and A3, we have  $\mu I \preceq \nabla^2 F(\pmb {w}) \preceq LI$ ;

A4 ( $\delta$ - local dissimilarity) The local loss function  $F_{k}(\pmb{w}^{[t]})$  at  $\pmb{w}^{[t]}$  is  $\delta$ - locally dissimilar, i.e.,  $\mathbb{E}\left[\| \nabla F_k(\pmb{w}^{[t]})\| ^2\right] \leq \delta^2 \| \nabla F(\pmb{w}^{[t]})\| ^2$ , for  $k = 1, \ldots , K$ , where  $\mathbb{E}$  denotes the weighted aggregation over participating devices, and the larger the  $\delta \geq 1$ , the more heterogeneous the data, indicating that local updates (i.e., local gradients) are more divergent. When the data distribution is IID,  $\delta$  approaches 1. Thus,  $\delta$  characterizes the degree of Non- IID- ness.

In the considered FL system, image classification is taken as an example task. Accordingly, the cross- entropy function is chosen as the loss function, which is strongly convex and satisfies the aforementioned assumptions A1- A4. The following theorem provides the convergence upper bound of FL under the specified LAPA algorithm and dynamic noise control mechanism.

Theorem 1: Given the optimal global model  $\pmb{w}^{*}$  under ideal channel conditions, the local dissimilarity index  $\delta$ , the aggregation weights  $G_{k}$  computed based on the proposed aggregation strategy, the learning rate  $\lambda$ , and the transmission power allocation  $\pmb{p}$ , the convergence upper bound of FL is given by:

$$
\begin{array}{rl} & {\mathbb{E}\left[F(\pmb{w}^{[t + 1]}) - F(\pmb{w}^{*})\right]}\\ & {\leq A^T\mathbb{E}\left[F(\pmb{w}^{[0]}) - F(\pmb{w}^{*})\right]}\\ & {\quad +\frac{L\lambda^2}{2}\cdot \frac{1 - A^T}{1 - A}\sum_{k = 1}^{K}\frac{G_k^2\sigma_{n_0}^2}{\|h_k\|^{2}p_k^2}}\\ & {\quad +\frac{L\lambda^2}{2}\sum_{m = 0}^{T_{th}}A^m\left[\sum_{k = 1}^{K}\frac{G_k^2\Delta s_k^2\cdot 2\ln(1.25 / \delta_{dp})}{(c^{[t]})^2}\right].} \end{array} \tag{24}
$$

where  $A = 1 + \mu L\lambda^2\delta^2\sum_{k = 1}^{K}G_k^2 - 2\lambda \mu$ , and  $T$  is the total number of aggregation rounds.Proof: See Appendix I.

Proof: See Appendix I.

According to Theorem 1, when  $A< 1$  the upper bound  $\mathbb{E}\left[F(\pmb{w}^{[t + 1]}) - F(\pmb{w}^{*})\right]$  converges at a rate of  $A$  . Therefore,  $A$  can be regarded as an indicator of the FL convergence rate. To ensure convergence of FL, i.e., to satisfy  $A< 1$  the following condition should be held:

$$
A = 1 + \mu L\lambda^2\delta^2\sum_{k = 1}^{K}G_k^2 -2\lambda \mu < 1. \tag{25}
$$

Based on assumption A3, we have  $\mu < L$  or  $\mu /L< 1)$  .To satisfy  $A< 1$  , the following inequality must be satisfied:

$$
\mu L\lambda^2\delta^2\sum_{k = 1}^{K}G_k^2 < 2\lambda \mu , \tag{26}
$$

which leads to:

$$
\lambda < \frac{2}{L\delta^2\sum_{k = 1}^{K}G_k^2}. \tag{27}
$$

When the above condition is met, the FL algorithm is guaranteed to converge. The learning rate  $\lambda$  needs to be inversely proportional to the degree of data heterogeneity (measured by  $\delta$  ). In other words, the more imbalanced the data distribution, the smaller the learning rate  $\lambda$  required to ensure convergence.

From the convergence upper bound, it can be observed that the system error mainly consists of artificial noise introduced before the  $T_{th}$  - th aggregation round and the communication noise interference that persists throughout the entire training process. The level of artificial noise required to meet the DP constraint gradually decreases with the increase in training rounds, while the intensity of communication noise is determined by the transmission power of each device and remains constant during global aggregation.

Higher transmission power reduces the impact of communication noise, which benefits the accuracy of model transmission. However, when the system operates under high transmission power, although the transmission quality is improved, additional artificial noise must be introduced to satisfy the DP constraint. In this case, the convergence error is mainly dominated by the artificial noise. Moreover, the inherent noise in the communication environment fails to effectively contribute to DP satisfaction and instead adds up with the artificial noise, further worsening the error, which is detrimental to system accuracy. Therefore, high transmission power leads to unnecessary resource consumption and degrades system performance, making it a suboptimal strategy.

Reducing transmission power increases the level of communication noise interference. When transmitting the model with excessively low transmission power, although the interference from communication noise may be sufficient to meet DP constraints- requiring little or even no additional artificial noise- the fixed transmission power cannot adapt to the decreasing RoLP. This results in consistently high communication interference, which is also detrimental to global updates. Therefore, the transmission power should not be too low, in order to avoid excessive communication errors that impair system convergence.

In summary, to enhance the convergence performance of FL, it is crucial to fully exploit communication noise as a potential resource for meeting privacy requirements. Specifically, transmission power should neither be too high, which would unnecessarily increase artificial noise, nor too low, which would result in excessive communication errors. Accordingly, we formulate the optimization problem, as shown in (28).

Under the privacy constraint, this problem aims to optimize the transmission power allocation of devices to identify the optimal timing  $T_{th}$  at which the required artificial noise level decreases to match the interference level of the communication environment noise. This allows effective control of total system error, thereby achieving the best trade- off between privacy protection and communication performance.

# C. Optimization using DDPG algorithm

A key challenge in the above optimization problem lies in the fact that the total number of rounds requiring artificial noise,  $T_{th}$  , dynamically changes with the transmission power allocation decisions. In other words, each optimization attempt of power allocation influences the available privacy budget (i.e., the outcome of LAPA) as well as the convergence upper bound, making the scenario highly coupled and dynamically evolving. Moreover,  $T_{th}$  is mathematically difficult to express in a closed form as a function of transmission power  $\pmb{p}$  Therefore, traditional static or one- shot offline optimization methods are not effective in handling such strongly coupled and dynamic optimization problems. In contrast, reinforcement learning (RL) is capable of interacting with the environment over multiple rounds, progressively learning an optimal decision policy by repeatedly exploring different power allocation strategies, which enables a more flexible and efficient trade- off among multiple objectives, such as convergence upper bound and privacy noise cost.

Since power allocation is a continuous- variable problem, traditional Deep Q- Network (DQN) algorithms are not wellsuited for direct application, as they require discretization of the action space, which leads to accuracy loss and reduced optimization efficiency. To address this, we adopt the Deep Deterministic Policy Gradient (DDPG) algorithm. Through its Actor- Critic structure, DDPG can directly output power allocation decisions in a continuous action space, and employs batch training and target networks to ensure convergence. This enables the system to gradually reach an optimal state that significantly improves convergence performance while also meeting privacy requirements.

We map the  $\mathcal{P}1$  problem into a reinforcement learning process, with the following key elements defined:

1) State. At time step  $n$  the state vector is defined as:

$$
\pmb {s}_n = \left[\pmb{p}^{(n - 1)},T_{th}^{(n)},\pmb{L}^{(n - 1)}\right], \tag{29}
$$

where the elements of the state vector include the power allocation from the previous optimization round, the noiseswitching time  $T_{th}^{(n)}$  determined by the current power allocation, and the objective function value from the previous round.

$$
\begin{array}{rl}\mathcal{P}1: & \min_{\pmb{p}}\frac{1 - A^T}{1 - A}\sum_{k = 1}^{K}\frac{G_k^2\sigma_{n_0}^2}{\|\pmb{h}_k\|^2p_k^2} +\sum_{m = 0}^{T_{th}}A^m\cdot \left(\sum_{k = 1}^{K}\frac{2G_k^2\Delta s_k^2\ln(\frac{1.25}{\delta_{dp}})}{(\epsilon_k^{[m]})^2}\right),\\ & \qquad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad (28)\\ & \qquad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad \quad (28) \end{array} \tag{28}
$$

These elements characterize the system's environment before the current decision.

(2) Action. Since DDPG operates in a continuous action space, the action in the  $n$ -th round is defined as a new power allocation vector:

$$
\pmb {a}_n = \{p_k^{(n)}\} ,\quad k = 1,2,\ldots ,K, \tag{30}
$$

Each action component is a continuous power value, and projection is applied to ensure it satisfies the power constraint  $P_{min} \leq \sum_{k = 1}^{K} |p_k|^2 \leq P_{max}$ .

(3) Reward. The reward is defined as the negative value of the objective function, aiming to unify the learning goal of reward maximization with the objective of minimizing the optimization target:

$$
\pmb {r}_n = -\mathcal{L}\left(\{p_k^{(n)}\}\right). \tag{31}
$$

With this design, the agent receives a higher reward when the power allocation results in a lower objective value.

With the DDPG algorithm, we employ an Actor network  $\mu (\pmb {s} \mid \pmb{\theta}^{\mu})$  and a Critic network  $Q(\pmb {s}, a \mid \pmb{\theta}^Q)$  to perform power allocation decision- making and evaluation. Corresponding target networks  $\mu '$  and  $Q'$  are used for soft updates of the policy and experience replay. Specifically, at each optimization iteration  $n$ , given the current state  $\pmb {s}_n$ , the Actor network outputs the power allocation policy as follows:

$$
\pmb {a}_n = \mu (\pmb {s}_n\mid \pmb {\theta}^\mu) + \mathcal{N}_n, \tag{32}
$$

where  $\mathcal{N}_n$  denotes the exploration noise (e.g., Gaussian noise) added to encourage the agent to explore new power configurations. Based on the power allocation vector  $\pmb {a}_n$  and the DP- based FL environment defined in this paper, the environment executes one round of FL according to equation (28b) and the dynamic noise control mechanism, determines the noise- switching time  $T_{th}^{(n)}$ , and computes the convergence upper bound  $\mathcal{L}^{(n)}$ , which yields the immediate reward  $\pmb {r}_n = - \mathcal{L}^{(n)}$ . The state is then updated to  $\pmb{s}_{n + 1}$ , and the transition tuple  $(\pmb {s}_n, \pmb {a}_n, \pmb {r}_n, \pmb{s}_{n + 1})$  is stored in the experience replay buffer.

A batch of  $N$  data samples  $\{(\pmb {s}_n, \pmb {a}_n, \pmb {r}_n, \pmb{s}_{n + 1})\}$  is randomly sampled from the experience replay buffer. The target value for each sample is computed using the target networks:

$$
y_{i} = \pmb{r}_{i} + \Upsilon Q^{\prime}\left(\pmb{s}_{i}^{\prime},\mu^{\prime}(\pmb{s}_{i}^{\prime})\mid \pmb{\theta}^{Q^{\prime}}\right), \tag{33}
$$

where  $\Upsilon$  is the discount factor. The Critic network parameters  $\pmb{\theta}^Q$  are then updated by performing gradient descent to minimize the mean squared error loss:

$$
\mathcal{L}(\theta^{Q}) = \frac{1}{N}\sum_{i = 1}^{N}\left(y_{i} - Q(\pmb{s}_{i},\pmb{a}_{i}\mid \pmb{\theta}^{Q})\right)^{2}. \tag{34}
$$

The Actor network is updated using the deterministic policy gradient to output better actions that maximize the Q- values estimated by the Critic network:

$$
\begin{array}{l}\nabla_{\pmb {\theta}^{\mu}}J\approx \frac{1}{N}\\ \displaystyle \sum_{i = 1}^{N}\left[\nabla_{a}Q(\pmb {s},\pmb {a}\mid \pmb{\theta}^{Q})\bigg|_{\pmb {s} = \pmb {s}_{i},\pmb {a} = \mu (\pmb {s}_{i},\pmb{\mu}^{\prime \prime})}\nabla_{\pmb {\theta}^{\mu}}\mu (\pmb {s}_{i}\mid \pmb{\theta}^{\mu})\right]. \end{array} \tag{35}
$$

Finally, soft updates are used to smoothly update the target network parameters:

$$
\pmb{\theta}^{Q^{\prime}}\leftarrow \tau \pmb{\theta}^{Q} + (1 - \tau)\pmb{\theta}^{Q^{\prime}},\quad \pmb{\theta}^{\mu^{\prime}}\leftarrow \tau \pmb{\theta}^{\mu} + (1 - \tau)\pmb{\theta}^{\mu^{\prime}}. \tag{36}
$$

where  $\tau \ll 1$ , typically set to 0.001.

The above steps are repeated until the maximum number of training episodes is reached. Through this process, this section achieves an effective solution to the continuous power allocation optimization problem using the DDPG algorithm.

# D. Complexity Analysis

The computational complexity of the DDPG algorithm adopted in this paper mainly arises from two aspects: neural network and environment simulation. Specifically, in each iteration, the algorithm performs a forward pass from state to action with a complexity of  $\mathcal{O}(W)$ , where  $W$  denotes the total number of neural network parameters. The environment simulation involves determining the noise- switching time  $T_{th}$  under a given power allocation and executing the corresponding FL process and DP mechanism, with a complexity denoted by  $\mathcal{O}(\alpha \cdot T_{th})$ . In addition, each training step samples a batch of  $N$  data points from the replay buffer to update the parameters of the Critic and Actor networks, with a complexity of approximately  $\mathcal{O}(N \cdot W)$ . Therefore, the overall complexity per iteration is:

$$
\mathcal{O}(W + N\cdot W + \alpha \cdot T_{th}), \tag{37}
$$

Considering that the algorithm typically runs for  $E \cdot T_{RL}$  iterations (i.e.,  $E$  episodes with  $T_{RL}$  steps per episode), the total complexity is:

$$
\mathcal{O}\left(E\cdot T_{RL}\cdot \left(W + N\cdot W + \alpha \cdot T_{th}\right)\right). \tag{38}
$$

![](images/ec6a9dfdca8bae07a1eab288d10a9b96aa916218f5fb63eb98b4b8f24b041abf.jpg)  
Fig. 3: Device distribution in the simulation environment.

# V. SIMULATION RESULTS

# A. Simulation Configuration

We adopt a Cartesian coordinate system to describe the spatial relationships among devices in the simulation, as shown in Fig. 3. The BS, acting as the parameter server, is located at  $(- 50, 0, 10)$  m. A total of  $K = 15$  devices are distributed across two regions. Specifically, 7 devices are randomly selected to reside in Region I, defined as  $\{(x, y, 0): - 10 \leq x \leq 0, - 5 \leq y \leq 5\}$  m, while the remaining 8 devices are placed in Region II, defined as  $\{(x, y, 0): 10 \leq x \leq 20, - 5 \leq y \leq 5\}$  m. Different Non- IID data partitions significantly affect the experimental results. In this experiment, lower label diversity reduces the randomness of the data, making the training process more stable and efficient. We employ stochastic gradient descent (SGD) for local model training, with a batch size of 0.1. The learning rate is set to  $\lambda = 0.01$  for the MNIST dataset and  $\lambda = 0.008$  for the Fashion- MNIST dataset.

The path loss in the wireless transmission system is given by:

$$
PL_{DB} = G_{BS}G_{D}\left(\frac{c}{4\pi f_{c}d_{DB}}\right)^{P}, \tag{39}
$$

where the number of antennas at the BS is  $N_{a} = 15$  the antenna gain at the BS is  $G_{BS} = 5$  dBi, and the device antenna gain is  $G_{D} = 0$  dBi. The carrier frequency is  $f_{c} = 915$  MHz, the path loss exponent is  $P = 3.76$  and  $d_{\mathrm{DB}}$  denotes the distance between the device and the BS. The speed of light is denoted by  $c$  and the noise power is set to  $10^{- 3}$  W.

We use a CNN network to train and test the MNIST and Fashion- MNIST datasets, which consists of two  $5 \times 5$  convolutional layers (each followed by a  $2 \times 2$  max pooling layer), followed by a batch normalization layer, a fully connected layer with 50 units, a ReLU activation layer, and a softmax output layer. The cross- entropy function is used as the loss function during training.

Devices are distributed in different regions, and the data samples are clearly imbalanced across devices. Due to channel heterogeneity (mainly caused by varying distances) and data heterogeneity, the straggler problem may become more severe. Under such conditions, the proposed LAPA- based dynamic noise control strategy and customized aggregation weights are particularly important for improving learning performance under privacy constraints. This will be verified through the following simulation experiments.

# B. Non-IID Aggregation with Device Selection Mechanism

We evaluate the effectiveness and superiority of the personalized aggregation strategy proposed in Section III- C under heterogeneous scenarios using accuracy on both datasets. The simulation is conducted under practical communication conditions. To assess the impact of different SINR thresholds used in the device selection mechanism on convergence and accuracy, we convert the SINR threshold in (22) into an SER threshold  $\epsilon_{SER}$ . The following Configurations are defined for simulation:

Configuration 1: The aggregation strategy proposed in this paper, without device selection;

Configuration 2: The proposed aggregation strategy with device selection based on SER performance, using different filtering thresholds:  $10^{- 1}$ ,  $10^{- 2}$ , and  $10^{- 3}$ .

In addition, we compare the following approaches: (1) the traditional Federated Averaging aggregation strategy (FedAvg), referred to as Benchmark 1; and (2) a widely used aggregation strategy designed for heterogeneous data distributions, referred to as Benchmark 2, namely the Benefit Evaluation- based Dynamic Aggregation (BEDA) algorithm [36], which constructs aggregation weights based on the angle between local and global gradients. Although this method can quantify contribution in real time, it suffers from instability when data distributions are highly imbalanced, as the local gradient variability directly affects the reliability of contribution estimation. The simulation results are shown in Fig. 4.

First, it can be observed that under Non- IID data distribution, both the proposed aggregation strategy based on Wasserstein distance (Configuration 1) and the angle- based aggregation strategy in Benchmark 2 significantly improve FL accuracy compared to the traditional FedAvg algorithm. Specifically, on the MNIST dataset, Configuration 1 improves accuracy by  $2.470\%$  over Benchmark 1, while Benchmark 2 improves it by  $2.073\%$ . On the Fashion- MNIST dataset, Configuration 1 improves by  $1.413\%$  and Benchmark 2 by  $1.333\%$ . This indicates that under Non- IID data distribution, it is necessary to design customized aggregation weights tailored to the data distribution of different devices to mitigate the update bias caused by data imbalance.

Second, under practical communication conditions, the proposed Wasserstein distance- based aggregation strategy outperforms the widely used Benchmark 2 in terms of aggregation performance. Specifically, Configuration 1 achieves

![](images/43ede3a09b3f45c0aa49085eb1565a364a85f3c5f95e56f110098b60c021208a.jpg)  
Fig. 5: FL performance of different privacy allocation strategies under various artificial noise levels.

improvements of  $0.397\%$  MNIST) and  $0.800\%$  (FashionMNIST) over Benchmark 2. Furthermore, by incorporating a device selection mechanism based on transmission quality to exclude devices with severe transmission errors, accuracy can be further improved. When the SER threshold is set to  $\epsilon_{SER} = 10^{- 1}$ , the performance is optimal, with improvements of  $2.367\%$  (MNIST) and  $1.137\%$  (Fashion- MNIST) over Benchmark 2. This is because devices with severe transmission errors are harmful to the FL update. However, setting a more stringent selection threshold that excludes more devices with even slight errors leads to reduced accuracy, since in Non- IID FL, having more valid data is beneficial to model updates.

In summary, under Non- IID conditions, it is important to assign customized and stable aggregation weights based on the data distribution of devices. Moreover, in noisy environments, devices with Non- IID data should provide sufficient and accurate aggregated data for the learning system. Therefore, the proposed aggregation strategy based on Wasserstein distance with device selection capability demonstrates clear advantages.

![](images/193ed4b5d0ea12474261cab71f2d3f52a81f17c24ee432493f0ff1581d85d12f.jpg)  
Fig. 4: Comparison of different aggregation strategies in a heterogeneous wireless communication environment.

# C. Superiority of the LAPA Algorithm

This simulation is designed to conduct a horizontal comparison of the performance of different privacy budget allocation strategies applied in the FL system. The simulation is carried out under ideal communication conditions. The Configurations are defined as follows:

Configuration 3: The LAPA algorithm proposed in this paper;

Configuration 4: No privacy allocation mechanism is applied in the FL process.

In addition, we compare with the traditional DP algorithm based on uniform artificial noise [37], referred to as Benchmark 3, and an advanced existing method, the Adaptive Rayleigh Budgeting (ARB) algorithm [27], referred to as Benchmark 4. The ARB algorithm assigns personalized privacy budgets to devices based on global gradient updates, but does not take into account the local update bias caused by Non- IID data.

We first compare the FL performance of each scheme under different initial noise levels on the MNIST dataset, as shown in Fig. 5. The vertical axis represents the final convergence accuracy of FL when applying different privacy allocation strategies, while the horizontal axis indicates the initial level of artificial noise for each strategy (unit:  $W$ ).

As shown in the figure, omitting a privacy strategy in FL maintains the most stable and optimal aggregation performance. However, such a strategy does not provide the FL system with any resistance against differential attacks, and thus only serves as a performance reference for other DP- based strategies. The three types of DP strategies exhibit different performance fluctuations under varying levels of artificial noise. This indicates that under the same privacy level, different DP strategies result in varying degrees of degradation in learning performance. Under Non- IID condition, both

![](images/91474078bd6ee6d8efb4084edcb484db8cca54174f3416e93aa2f0ad7783558b.jpg)  
Fig. 6: FL convergence performance of different privacy allocation strategies under varying degrees of data heterogeneity.

the ARB and LAPA strategies demonstrate clear advantages in aggregation performance compared to the traditional DP strategy. Notably, the LAPA algorithm achieves convergence performance nearly equivalent to that of FL without any DP noise, suggesting that it can provide personalized privacy budget allocation based on heterogeneous data distributions while still meeting DP requirements, preserving FL convergence performance to the greatest extent.

Next, we assign different data distributions to devices and observe the FL convergence performance under varying degrees of data heterogeneity for each privacy strategy, as shown in Fig. 6.

As shown in the figure, regardless of the degree of data heterogeneity, the LAPA algorithm proposed in this paper consistently outperforms Benchmark 3 and Benchmark 4. Additionally, we observe an interesting phenomenon: when the learning rate is fixed, as the proportion of IID devices increases, the performance of the LAPA- based strategy gradually approaches that of the non- DP strategy. When all device data distributions are IID, the convergence rate under LAPA even slightly exceeds that of the non- DP case (although with continued training, the final accuracy remains slightly lower than the non- DP case). This phenomenon arises because, under well data conditions, the system typically requires a larger learning rate. However, when a smaller learning rate is used for stability, the LAPA algorithm introduces just enough artificial noise, which in turn helps alleviate underfitting. As a result, the introduced artificial noise not only minimizes the impact on model convergence while ensuring privacy, but also mitigates underfitting in certain ideal scenarios (thanks to the controlled noise intensity). Furthermore, from a reverse perspective, as the proportion of IID devices decreases, the data- adaptive privacy mechanism demonstrates stronger adaptability, showing good robustness against variations in data heterogeneity.

# D. Dynamic Noise Control Optimization Based on the LAPA Algorithm

As shown in the simulation results of the previous section, under ideal communication conditions and Non- IID data distribution, both the proposed LAPA algorithm and the advanced ARB algorithm can improve FL performance under privacy protection compared to the traditional DP algorithm, with the LAPA algorithm outperforming ARB. In this simulation, we evaluate the superiority of the LAPA- based dynamic noise control optimization strategy (denoted as Configuration 5) under realistic communication noise conditions. The simulation results are presented in Fig. 7.

As shown in Fig. 7, on the MNIST dataset, Configuration 5 achieves a  $1.465\%$  improvement compared to its average power setting, and a  $5.872\%$  improvement compared to the average power setting of Benchmark 4. On the Fashion- MNIST, the improvements are  $0.910\%$  and  $2.705\%$ , respectively. This demonstrates that, under realistic noise conditions, optimizing transmission power to achieve an optimal balance between artificial and channel noise can effectively leverage environmental noise interference, maximizing system utility while ensuring DP guarantees.

# VI. CONCLUSION

This paper addresses the problem of privacy protection and aggregation performance optimization in FL under Non- IID data condition by proposing a lightweight adaptive privacy allocation (LAPA) strategy. This strategy dynamically adjusts the privacy budget based on gradient contribution without introducing additional communication overhead, and utilizes the inherent noise in the communication environment to optimize the timing of artificial noise injection, aiming to balance privacy protection and system utility. In addition, based on data distribution characteristics and channel quality, an efficient device selection and aggregation weight assignment mechanism is designed, which improves convergence performance while ensuring privacy. Theoretical analysis and simulation results validate the effectiveness of the proposed method.

![](images/c22649de882c83e3cb58cf4998a06b658f289b40d76e5f9418b86c6e78852b5e.jpg)  
Fig. 7: Superiority of the proposed dynamic noise control optimization strategy based on the LAPA algorithm

# VII. APPENDIX I

In this paper, the total noise introduced into the FL system is denoted as  $N_{k}^{t}$ , which includes the artificial noise added before the  $T_{th}$ - th iteration, as well as the channel noise that persists throughout the process. Thus, we have:

$$
\begin{array}{rl} & {\pmb{w}^{[t + 1]} - \pmb{w}^{[t]}}\\ & {= -\lambda \cdot \sum_{k = 1}^{K}G_{k}\left[\nabla F_{k}(\pmb{w}^{[t]}) + N_{k}^{t}\right]}\\ & {= -\lambda \cdot \left[\sum_{k = 1}^{K}G_{k}\nabla F_{k}(\pmb{w}^{[t]}) + \sum_{k = 1}^{K}G_{k}N_{k}^{t}\right].} \end{array} \tag{40}
$$

Therefore,

$$
\begin{array}{rl} & {\mathbb{E}\left[\left\| \pmb{w}^{[t + 1]} - \pmb{w}^{[t]}\right\| ^2\right]}\\ & {= \lambda^2\cdot \mathbb{E}\left[\left\| \sum_{k = 1}^{K}G_k\nabla F_k(\pmb{w}^{[t]}) + \sum_{k = 1}^{K}G_kN_k^t\right\| ^2\right].} \end{array} \tag{41}
$$

Since we assume that the artificial noise added before the  $T_{th}$ - th iteration follows a zero- mean Gaussian distribution, we have:

$$
\begin{array}{rl} & {\mathbb{E}\left[\left\| \pmb{w}^{[t + 1]} - \pmb{w}^{[t]}\right\| ^2\right]}\\ & {= \lambda^2\left\{\mathbb{E}\left[\left\| \sum_{k = 1}^{K}G_k\nabla F_k(\pmb{w}^{[t]})\right\| ^2\right] + \mathbb{E}\left[\left\| \sum_{k = 1}^{K}G_kN_k^t\right\| ^2\right]\right\} .} \end{array} \tag{42}
$$

By performing a Taylor expansion of  $F(\pmb{w}^{[t + 1]})$  and taking the expectation, we obtain:

$$
\begin{array}{rl} & {\mathbb{E}[F(\pmb{w}^{[t + 1]})]}\\ & {\leq \mathbb{E}[F(\pmb{w}^{[t]})] - \lambda \left\| \nabla F(\pmb{w}^{[t]})\right\| ^2}\\ & {\quad +\frac{L\lambda^2}{2}\left\{\mathbb{E}\left[\left\| \sum_{k = 1}^{K}G_k\nabla F_k(\pmb{w}^{[t]})\right\| ^2\right] + \mathbb{E}\left[\left\| \sum_{k = 1}^{K}G_kN_k^t\right\| ^2\right]\right\}}\\ & {\leq \mathbb{E}[F(\pmb{w}^{[t]})] - \lambda \left\| \nabla F(\pmb{w}^{[t]})\right\| ^2}\\ & {\quad +\frac{L\lambda^2}{2}\sum_{k = 1}^{K}G_k^2\mathbb{E}\left[\left\| \nabla F_k(\pmb{w}^{[t]})\right\| ^2\right] + \frac{L\lambda^2}{2}\mathbb{E}\left[\left\| \sum_{k = 1}^{K}G_kN_k^t\right\| ^2\right],} \end{array} \tag{43}
$$

where the first inequality follows from the Taylor expansion, and the second inequality follows from the Cauchy- Schwarz inequality.

From assumption A4, we have:

$$
\frac{L}{2}\lambda^{2}\sum_{k = 1}^{K}G_{k}^{2}\mathbb{E}\left[\left\| \nabla F_{k}(\pmb{w}^{[t]})\right\|^{2}\right]\leq \frac{L}{2}\lambda^{2}\delta^{2}\sum_{k = 1}^{K}G_{k}^{2}\left\| \nabla F(\pmb{w}^{[t]})\right\|^{2}, \tag{44}
$$

therefore,

$$
\begin{array}{rl} & {\mathbb{E}[F(\pmb{w}^{[t + 1]})]}\\ & {\leq \mathbb{E}[F(\pmb{w}^{[t]})] - \lambda \left\| \nabla F(\pmb{w}^{[t]})\right\| ^2}\\ & {\quad +\frac{K}{2}\lambda^2\delta^2\sum_{k = 1}^{K}G_k^2\left\| \nabla F(\pmb{w}^{[t]})\right\| ^2 +\frac{L}{2}\lambda^2\cdot \mathbb{E}\left[\left\| \sum_{k = 1}^{K}G_kN_k^t\right\| ^2\right]}\\ & {= \mathbb{E}[F(\pmb{w}^{[t]})] + \left(\frac{L\lambda^2\delta^2}{2}\sum_{k = 1}^{K}G_k^2 -\lambda\right)\left\| \nabla F(\pmb{w}^{[t]})\right\| ^2}\\ & {\quad +\frac{L}{2}\lambda^2\cdot \mathbb{E}\left[\left\| \sum_{k = 1}^{K}G_kN_k^t\right\| ^2\right].} \end{array} \tag{45}
$$

From assumptions A1 and A3, we have  $\left\| \nabla F(\pmb{w}^{[t]})\right\| ^2 \geq 2\mu [F(\pmb{w}^{[t]}) - F(\pmb{w}^*)]$ , thus:

$$
\begin{array}{rl} & {\mathbb{E}[F(\pmb{w}^{[t + 1]}) - F(\pmb{w}^{*})]}\\ & {\leq \mathbb{E}[F(\pmb{w}^{[t]}) - F(\pmb{w}^{*})]}\\ & {\quad +\left(\frac{L\lambda^{2}\delta^{2}}{2}\sum_{k = 1}^{K}G_{k}^{2} - \lambda\right)(2\mu [F(\pmb{w}^{[t]}) - F(\pmb{w}^{*})])}\\ & {\quad +\frac{L}{2}\lambda^{2}\cdot \mathbb{E}\left[\left\| \sum_{k = 1}^{K}G_{k}N_{k}^{t}\right\|^{2}\right]}\\ & {= \mathbb{E}[F(\pmb{w}^{[t]}) - F(\pmb{w}^{*})]\left(1 + \mu L\lambda^{2}\delta^{2}\sum_{k = 1}^{K}G_{k}^{2} - 2\lambda \mu\right)}\\ & {\quad +\frac{L}{2}\lambda^{2}\cdot \mathbb{E}\left[\left\| \sum_{k = 1}^{K}G_{k}N_{k}^{t}\right\|^{2}\right],} \end{array} \tag{46}
$$

where  $\begin{array}{rlr}{A}&{=}&{1+}\end{array}$ $\begin{array}{rlr}{\mu L\lambda^2\delta^2\sum_{k = 1}^{K}G_k^2 - 2\lambda \mu} \end{array}$  and  $\mathbb{E}\left[\left\| \sum_{k = 1}^{K}G_kN_k^t\right\| ^2\right]$  can be divided into two parts:

(1) When  $t \leq T_{th}$ : the noise introduced by the system includes two components: artificial noise calculated based on the proposed LAPA strategy and inherent channel noise. Thus,

$$
\begin{array}{rl} & {\mathbb{E}\left[\left\| \sum_{k = 1}^{K}G_{k}N_{k}^{t}\right\|^{2}\right]}\\ & {= \sum_{k = 1}^{K}G_{k}^{2}\left(\frac{\Delta s_{k}^{2}\cdot 2\ln(1.25 / \delta_{dp})}{(\epsilon_{k}^{[t]})^{2}} +\frac{\sigma_{n_{0}}^{2}}{\|\pmb{h}_{k}\|^{2}p_{k}^{2}}\right).} \end{array} \tag{47}
$$

At this point, if the FL system runs for  $T_{th}$  rounds, we obtain:

$$
\begin{array}{rl} & {\mathbb{E}[F(\pmb{w}^{[t + 1]}) - F(\pmb{w}^{*})]}\\ & {\quad \leq A^{T_{th}}\mathbb{E}[F(\pmb{w}^{[0]}) - F(\pmb{w}^{*})] + \frac{1 - A^{T_{th}}}{1 - A}}\\ & {\quad \quad \cdot \frac{L\lambda^{2}}{2}\sum_{k = 1}^{K}G_{k}^{2}\left(\frac{\Delta s_{k}^{2}\cdot 2\ln(1.25 / \delta_{dp})}{(\epsilon_{k}^{[t]})^{2}} +\frac{\sigma_{n_{0}}^{2}}{\|\pmb{h}_{k}\|^{2}p_{k}^{2}}\right).} \end{aligned} \tag{48}
$$

(2) When  $t > T_{th}$ : the system stops adding artificial noise, so only channel noise remains, and:

$$
\mathbb{E}\left[\left\| \sum_{k = 1}^{K}G_{k}N_{k}^{t}\right\|^{2}\right] = \sum_{k = 1}^{K}G_{k}^{2}\cdot \frac{\sigma_{n_{0}}^{2}}{\|\pmb{h}_{k}\|^{2}p_{k}^{2}}. \tag{49}
$$

At this point, letting the FL system run for the remaining rounds gives:

$$
\begin{array}{rl} & {\mathbb{E}[F(\pmb{w}^{[t + 1]}) - F(\pmb{w}^{*})]}\\ & {\quad \leq A^{T - T_{th}}\mathbb{E}[F(\pmb{w}^{[0]}) - F(\pmb{w}^{*})]}\\ & {\quad \quad +\frac{1 - A^{T - T_{th}}}{1 - A}\cdot \frac{L\lambda^{2}}{2}\sum_{k = 1}^{K}G_{k}^{2}\cdot \frac{\sigma_{n_{0}}^{2}}{\|\pmb{h}_{k}\|^{2}p_{k}^{2}}.} \end{array} \tag{50}
$$

In summary, when the FL process runs for  $T$  rounds, we have:

$$
\begin{array}{rl} & {\mathbb{E}[F(\pmb{w}^{[t + 1]}) - F(\pmb{w}^{*})]}\\ & {\leq A^{T}\mathbb{E}[F(\pmb{w}^{[0]}) - F(\pmb{w}^{*})] + \frac{L\lambda^{2}}{2}\cdot \frac{1 - A^{T}}{1 - A}\sum_{k = 1}^{K}G_{k}^{2}\cdot \frac{\sigma_{n_{0}}^{2}}{\|\pmb{h}_{k}\|^{2}p_{k}^{2}}}\\ & {\quad +\frac{L\lambda^{2}}{2}\sum_{m = 0}^{T_{th}}A^{m}\left[\sum_{k = 1}^{K}\frac{G_{k}^{2}\Delta s_{k}^{2}\cdot 2\ln(1.25 / \delta_{dp})}{(\epsilon_{k}^{[t]})^{2}}\right].} \end{array} \tag{51}
$$

Proof completed.

# REFERENCES

[1] F. Granqvist, M. Seigel, R. Van Dalen, A. Cahill, S. Shum, and M. Paulik, "Improving on- device speaker verification using federated learning with privacy," ArXiv preprint at arXiv:2008.02651, 2020. [2] A. Hard, K. Partridge, C. Nguyen, N. Subrahmanya, A. Shah, P. Zhu, I. L. Moreno, and R. Mathews, "Training keyword spotting models on non- iid data with federated learning," ArXiv preprint at arXiv:2005.10406, 2020. [3] T. S. Brisimi, R. Chen, T. Mela, A. Olshevsky, I. C. Paschalidis, and W. Shi, "Federated learning of predictive models from federated electronic health records," International Journal of Medical Informatics, vol. 112, pp. 59- 67, 2018. [4] O. Choudhury, Y. Park, T. Salonidis, A. Gkoulalas- Divanis, I. Sylla, and A. k Das, "Predicting adverse drug reactions on distributed health data using federated learning," in Proceedings of AMIA Annual Symposium, vol. 2019, p. 313, 2020. [5] A. Sadilek, L. Liu, D. Nguyen, M. Kamruzaman, S. Serghiou, B. Rader, A. Ingerman, S. Mellem, P. Kairouz, E. O. Nsoesie et al., "Privacy- first health research with federated learning," NPJ Digital Medicine, vol. 4, no. 1, p. 132, 2021. [6] G. Long, Y. Tan, J. Jiang, and C. Zhang, "Federated learning for open banking," in Federated Learning: Privacy and Incentive, pp. 240- 254, 2020. [7] W. Yang, Y. Zhang, K. Ye, L. Li, and C.- Z. Xu, "Ftd: A federated learning based method for credit card fraud detection," in Proceedings of Big Data- BigData 2019: 8th International Congress, pp. 18- 32. Springer, 2019. [8] L. Zhu, Z. Liu, and S. Han, "Deep leakage from gradients," Advances in Neural Information Processing Systems, vol. 32, 2019. [9] B. Zhao, K. R. Mopuri, and H. Bilen, "idlg: Improved deep leakage from gradients," ArXiv preprint at arXiv:2001.02610, 2020. [10] H. Yin, A. Mallya, A. Vahdat, J. M. Alvarez, J. Kautz, and P. Molchanov, "See through gradients: Image batch recovery via gradinversion," in Proceedings of IEEE/CVF Conference on Computer Vision and Pattern Recognition (CVPR), pp. 16337- 16346, 2021. [11] Z. Wang, M. Song, Z. Zhang, Y. Song, Q. Wang, and H. Qi, "Beyond inferning class representatives: User- level privacy leakage from federated learning," in Proceedings of IEEE Conference on Computer Communications (ICCC), pp. 2512- 2520. IEEE, 2019. [12] J. Geipsing, H. Bauermeister, H. Drige, and M. Moeller, "Inverting gradients- how easy is it to break privacy in federated learning?" Advances in Neural Information Processing Systems, vol. 33, pp. 16937- 16947, 2020. [13] L. Song and P. Mittal, "Systematic evaluation of privacy risks of machine learning models," in Proceedings of USENIX Security Symposium (USENIX Security 21), pp. 2615- 2632, 2021. [14] A. Salem, Y. Zhang, M. Humbert, P. Berarrang, M. Fritz, and M. Backes, "MI- leaks: Model and data independent membership inference attacks and defenses on machine learning models," ArXiv preprint at arXiv:1806.01246, 2018. [15] B. Hui, Y. Yang, H. Yuan, P. Burlina, N. Z. Gong, and Y. Cao, "Practical blind membership inference attack via differential comparisons," ArXiv preprint at arXiv:2101.01341, 2021. [16] C. Song, T. Ristenpart, and V. Shmatikov, "Machine learning models that remember too much," in Proceedings of ACM SIGSAC Conference on Computer and Communications Security, pp. 587- 601, 2017. [17] B. Hitaj, G. Ateniese, and F. Perez- Cruz, "Deep models under the gan: information leakage from collaborative deep learning," in Proceedings of ACM SIGSAC Conference on Computer and Communications Security, pp. 603- 618, 2017.

[18] K. Ganju, Q. Wang, W. Yang, C. A. Gunter, and N. Borisov, "Property inference attacks on fully connected neural networks using permutation invariant representations," in Proceedings of ACM SIGSAC Conference on Computer and Communications Security, pp. 619- 633, 2018. [19] M. Fredrikson, S. Jha, and T. Ristenpart, "Model inversion attacks that exploit confidence information and basic countermeasures," in Proceedings of ACM SIGSAC Conference on Computer and Communications Security, pp. 1322- 1333, 2015. [20] C. Dwork, A. Roth et al., "The algorithmic foundations of differential privacy," Foundations and Trends in Theoretical Computer Science, vol. 9, no. 3- 4, pp. 211- 407, 2014. [21] S. Truex, N. Baracaldo, A. Anwar, T. Steinke, H. Ludwig, R. Zhang, and Y. Zhou, "A hybrid approach to privacy- preserving federated learning," in Proceedings of ACM Workshop on Artificial Intelligence and Security, pp. 1- 11, 2019. [22] R. C. Geyer, T. Klein, and M. Nabi, "Differentially private federated learning: A client level perspective," ArXiv preprint at arXiv:1712.07557, 2017. [23] K. Wei, J. Li, M. Ding, C. Ma, H. H. Yang, F. Farokhi, S. Jin, T. Q. Quek, and H. V. Poor, "Federated learning with differential privacy: Algorithms and performance analysis," IEEE Transactions on Information Forensics and Security, vol. 15, pp. 3454- 3469, 2020. [24] H. Zhou, G. Yang, H. Dai, and G. Liu, "Pff: Privacy- preserving federated learning framework for edge computing," IEEE Transactions on Information Forensics and Security, vol. 17, pp. 1905- 1918, 2022. [25] X. You, X. Liu, N. Jiang, J. Cai, and Z. Ying, "Reschedule gradients: Temporal non- iid resilient federated learning," IEEE Internet of Things Journal, vol. 10, no. 1, pp. 747- 762, 2022. [26] P. Sun, G. Liao, X. Chen, and J. Huang, "A socially optimal data marketplace with differentially private federated learning," IEEE/ACM Transactions on Networking, 2024. [27] T. Zhang, A. Song, X. Dong, Y. Shen, and J. Ma, "Privacy- preserving asynchronous grouped federated learning for iot," IEEE Internet of Things Journal, vol. 9, no. 7, pp. 5511- 5523, 2021. [28] L. Lin, Z. Zhao, Z. Yang, and Z. Zhang, "Joint communication and learning design of differential privacy for federated learning over multicell networks," in Proceedings of IEEE International Conference on Communications Workshops (ICC Workshops), pp. 770- 775. IEEE, 2023. [29] G. Wang, Q. Qi, R. Han, L. Bai, and J. Choi, "P2ceff: Privacy- preserving and communication efficient federated learning with sparse gradient and dithering quantization," IEEE Transactions on Mobile Computing, 2024. [30] D. Q. Nguyen and T. Kim, "Time- varying noise perturbation and power control for differential- privacy- preserving wireless federated learning," in Proceedings of Asilomav Conference on Signals, Systems, and Computers, pp. 656- 660. IEEE, 2023. [31] C. Dwork, K. Kenthapadi, F. McSherry, I. Mironov, and M. Naor, "Our data, ourselves: Privacy via distributed noise generation," in Proceedings of Advances in cryptology- EUROCRYPT 2006: 24th Annual International Conference on the Theory and Applications of Cryptographic Techniques, pp. 486- 503, Springer, 2006. [32] S. Wang, L. Huang, Y. Nie, X. Zhang, P. Wang, H. Xu, and W. Yang, "Local differential private data aggregation for discrete distribution estimation," IEEE Transactions on Parallel and Distributed Systems, vol. 30, no. 9, pp. 2046- 2059, 2019. [33] J. Hu, Z. Wang, Y. Shen, B. Lin, P. Sun, X. Pang, J. Liu, and K. Ren, "Shield against gradient leakage attacks: Adaptive privacy- preserving federated learning," IEEE/ACM Transactions on Networking, vol. 32, no. 2, pp. 1407- 1422, 2023. [34] Q. Wang, Y. Zhang, X. Lu, Z. Wang, Z. Qin, and K. Ren, "Rescuedp: Real- time spatio- temporal crowd- sourced data publishing with differential privacy," in Proceedings of IEEE International Conference on Computer Communications (ICCC), pp. 1- 9. IEEE, 2016. [35] T. Li, A. K. Sahu, M. Zaheer, M. Sanjabi, A. Talwalkar, and V. Smith, "Federated optimization in heterogeneous networks," Machine Learning and Systems, vol. 2, pp. 429- 450, 2020. [36] H. Wu and P. Wang, "Fast- convergent federated learning with adaptive weighting," IEEE Transactions on Cognitive Communications and Networking, vol. 7, no. 4, pp. 1078- 1088, 2021. [37] M. Abadi, A. Chu, I. Goodfellow, H. B. McMahan, I. Mironov, K. Talwar, and L. Zhang, "Deep learning with differential privacy," in Proceedings of ACM SIGSAC Conference on Computer and Communications Security, pp. 308- 318, 2016.