# DIFFERENTIALLY PRIVATE FEDERATED LEARNING WITH TIME-ADAPTIVE PRIVACY SPENDING

<PERSON><PERSON><PERSON> $^{1,\ast}$  <PERSON><PERSON><PERSON> $^{2}$ , <PERSON> $^{2}$ , <PERSON> Draper $^{1}$ , & <PERSON><PERSON><PERSON> $^{2}$ $^{1}$  Department of Electrical and Computer Engineering, University of Toronto $^{2}$  CISPA Helmholtz Center for Information Security

# ABSTRACT

Federated learning (FL) with differential privacy (DP) provides a framework for collaborative machine learning, enabling clients to train a shared model while adhering to strict privacy constraints. The framework allows each client to have an individual privacy guarantee, e.g., by adding different amounts of noise to each client's model updates. One underlying assumption is that all clients spend their privacy budgets uniformly over time (learning rounds). However, it has been shown in the literature that learning in early rounds typically focuses on more coarse- grained features that can be learned at lower signal- to- noise ratios while later rounds learn fine- grained features that benefit from higher signal- to- noise ratios. Building on this intuition, we propose a time- adaptive DP- FL framework that expends the privacy budget non- uniformly across both time and clients. Our framework enables each client to save privacy budget in early rounds so as to be able to spend more in later rounds when additional accuracy is beneficial in learning more fine- grained features. We theoretically prove utility improvements in the case that clients with stricter privacy budgets spend budgets unevenly across rounds, compared to clients with more relaxed budgets, who have sufficient budgets to distribute their spend more evenly. Our practical experiments on standard benchmark datasets support our theoretical results and show that, in practice, our algorithms improve the privacy- utility trade- offs compared to baseline schemes.

# 1 INTRODUCTION

With machine learning (ML) relying increasingly on users' sensitive data, the development of utilitydriven frameworks that also adhere to users' individual constraints, including privacy preferences, has become a priority. When federated learning (FL) (McMahan et al., 2017) was first introduced, it was perceived as a privacy- preserving distributed learning framework that allows users (also called clients) to keep their data local and solely exchanging model updates with the server who can aggregate the updates and apply them to the global model for training.

However, it has been shown that data can be leaked through the model gradients (Zhu et al., 2019; Geiping et al., 2020; Boenisch et al., 2023). Hence, FL was extended to incorporate formal privacy guarantees (McMahan et al., 2017; Geyer et al., 2017; Wei et al., 2020; Hu et al., 2023; Ramaswamy et al., 2020) via the mathematical framework of differential privacy (DP) (Dwork, 2006). In DP- FL frameworks, one common approach is to protect the entire dataset of each client ("client- level DP") by clipping local model updates and adding noise before releasing them in each training round (Truex et al., 2019; 2020). This ensures that an adversary who has access to the aggregated perturbed updates of a subset of clients cannot confidently infer whether or not any particular client has participated in the given training round. However, although the DP- FL framework ensures privacy, it degrades model utility by introducing errors into the model updates. Thus, careful calibration of the perturbations is necessary to balance privacy and utility.

There are several extensions of the DP- FL framework in the literature that aim to improve privacyutility tradeoffs by reducing the effect of perturbation while adhering to the privacy budgets of

clients. Typically, these prior works (Pichapati et al., 2019; Yang et al., 2021; Shen et al., 2023; Yang et al., 2023; McMahan et al., 2017) consider the inherent heterogeneity in FL—both in data and privacy—to make perturbation more efficient. Yet, they either assume that clients' privacy budgets should be exhausted uniformly over time (Bolenisch et al., 2024), or rely on strong assumptions regarding access to public data (Li et al., 2022) or negligible privacy loss when adjusting privacy parameters in a time- adaptive manner based on data (Pichapati et al., 2019). As we will see, judiciously expending the budget non- uniformly over time and in a privacy- preserved manner can yield an improved privacy- utility tradeoff. Hence, a gap exists in the literature.

In this work, we reduce this gap by proposing a novel DP- FL framework with a data- independent time- adaptive privacy spending method. In our framework, clients can spend their privacy budget non- uniformly across time (training rounds). This means that clients intentionally allocate less of their privacy budgets in the early rounds to save them for later rounds. We term these rounds as "saving". Clients then transit to "spending" rounds, wherein they uniformly allocate their remaining budget across spending rounds. The decisions about when each client transits from saving to spending and how much they save in each round are made solely based on clients' privacy budgets, and not their local data. Therefore, we can schedule spending before the start of training, making it free of privacy loss. We account for each client's privacy spending in each round, formulating privacy bounds as a function of clients' decisions and budgets.

Two observations that motivate the potential of our framework to improve the privacy- utility tradeoff are as follows. First, by preserving the privacy budget in early rounds and incrementally spending later, we are able to adjust the signal- to- noise (SNR) ratio to be uneven across training rounds, with noise shifting from later rounds to earlier ones. This enables coarse- grained features, which are typically learned in the early rounds and are more tolerant to noise, still to be learned effectively. Furthermore, the fine- grained features, typically learned in later rounds (Dziedzic et al., 2019; Raghu et al., 2017; Shwartz- Ziv & Tishby, 2017), can be learned in a beneficial higher- SNR setting. Secondly, in practical scenarios, we note that clients are likely to have different privacy budgets. We show theoretically that clients with stricter privacy budgets benefit from expending their privacy budgets more unevenly than those with relaxed (larger) budgets. Intuitively, this allows less- privacy- sensitive clients, who have often sufficient budgets, to contribute to the learning both of coarse- grained features in early rounds and of fine- grained features in later rounds. On the other hand, more- privacy- constrained clients can preserve their budgets and helpfully contribute more to the learning of fine- grained features.

In summary, we make the following contributions:

- As part of our framework design (detailed in Sec. 3), we introduce a novel privacy spending method, namely "spend-as-you-go", where clients spend their privacy budgets incrementally over time, instead of spending the privacy budget uniformly across time, as in traditional DP-FL approaches.- Our theoretical analysis (detailed in Sec. 4) provides privacy accounting for the incremental spending pattern in our method. Additionally, we show theoretically, that if clients that use stricter privacy parameters, such as lower clipping norms, save a larger portion of their privacy budget during saving rounds, and can spend more in spending rounds, then, in expectation, we can reduce the clipping bias (Das et al., 2023).- Based on these theoretical insights, in Sec. 5 we experimentally benchmark our framework against the baselines and show that the global test accuracy achieved by our method surpasses that of the baselines for the FMNIST (Xiao et al., 2017), MNIST (Deng, 2012), Adult Income (Becker & Kohavi, 1996), and CIFAR10 datasets (Krizhevsky et al., 2009).

# 2 BACKGROUND AND RELATED WORK

Federated Learning. We consider a typical FL system with  $N$  clients and a central server. Each client  $n\in [N]$  has its own data distribution  $P_{n}$  on  $\mathcal{X}\times \mathcal{Y}$  where  $\mathcal{X}\subseteq \mathbb{R}^u$  denotes the feature space and  $\mathcal{V}\subseteq \mathbb{R}$  denotes the label space. Let  $\mathcal{L}_n:\mathbb{R}^v\times \mathcal{X}\times \mathcal{Y}\to \mathbb{R}$  denote client  $n$  's loss function which maps a model parameter  $\theta \in \mathbb{R}^v$  and a data sample  $(\mathbf{x},y)\in \mathcal{X}\times \mathcal{Y}$  to a cost. Each client  $n$  is assumed to have access to a dataset  $\mathcal{D}_n$  which consists of  $|\mathcal{D}_n|$  data points sampled from  $P_{n}$  . Defining  $\begin{array}{r}\bar{\mathcal{L}}_n(\theta)\coloneqq \frac{1}{|\mathcal{D}_n|}\sum_{(\mathbf{x},y)\in \mathcal{D}_n}\mathcal{L}_n(\theta ;(\mathbf{x},y)) \end{array}$  and  $\begin{array}{r}\bar{\mathcal{L}} (\theta)\coloneqq \frac{1}{N}\sum_{n = 1}^{N}\bar{\mathcal{L}}_n(\theta) \end{array}$  , the

optimization problem in FL is  $\min_{\theta} \bar{\mathcal{L}} (\theta)$ . The Federated Averaging (FedAvg) (McMahan et al., 2017) algorithm solves this problem by having clients run local stochastic gradient descent (SGD) and send updates to the server, which averages them to update the global model. This cycle repeats until convergence or for a specified number of communication rounds. As the baseline for our proposed approach (detailed in Sec.3), we consider FedAvg, presented as Alg. 3 in App.A.1. This algorithm, unconstrained by privacy limitations, represents the ideal utility case.

Differential Privacy. In ML, the mathematical framework of  $(\epsilon , \delta)$ - DP (Dwork et al., 2014), ensures that two models trained on neighboring datasets, i.e., datasets that differ in one data point, differ only slightly in their outputs. This can be formalized as Def. 1 in App. A.6.2. In  $(\epsilon , \delta)$ - DP, a smaller privacy budget  $\epsilon \in \mathbb{R}_{+}$  enforces a stronger privacy guarantee. The  $\delta \in [0, 1]$  quantifies the probability of violating the privacy guarantee and thereby has usually a small value. We discuss  $(\alpha , \epsilon)$ - Rényi- DP (Mironov, 2017) as an alternative to relax DP guarantees and get smoother composition in Alg. A.6.2. In this paper, we adopt RDP, while as it is shown by (Mironov, 2017), it can be converted to DP when needed (cf. Lem. 3 in App. A.6.2).

DP- FL. We use the notion of client- level DP- FL which protects the entire client's dataset (McMahan et al., 2017; Geyer et al., 2017; Hu et al., 2023). To implement client- level DP in FL, we can rely on the DP- FedAvg (McMahan et al., 2017; Hu et al., 2023) algorithm that first clips clients' updates according to a sensitivity  $c$  and then adds Gaussian noise according to  $\mathcal{N} \left(0, c^2 \sigma^2 \mathbb{I}\right)$ . DP- FedAvg also implements privacy amplification by sub- sampling (Beimel et al., 2014), where clients are sampled independently at random with probability  $q$ . As shown by Mironov et al. (2019), the Sampled Gaussian Mechanism (SGM) tightens the RDP  $\epsilon$  by a quadratic scaling factor  $q^2$  (cf. Lem. 4 in App. A.6.2). We also use the notion of distributed DP (DDP) (Truex et al., 2019) which combines the advantages of centralized (Ramaswamy et al., 2020) and local DP (Truex et al., 2020). In DDP, clients clip their updates and locally add a small amount of noise, distributed according to  $\mathcal{N} \left(0, c^2 \sigma^2 / N \mathbb{I}\right)$  (Truex et al., 2019). By adding local noise, clients' privacy is partially protected against the server, and sufficient noise,  $\mathcal{N} \left(0, c^2 \sigma^2 \mathbb{I}\right)$ , is guaranteed when the server aggregates all  $N$  noisy updates. We consider the DP- FedAvg algorithm that is implemented with client- level and DDP (presented as Alg. 4 in App.A.1) as a baseline for our approach, detailed in Sec.3.

Personalized DP. DP- FedAvg and most of its variants apply worst- case privacy guarantees to ensure privacy for the most constrained clients. This leads to over- perturbation and reduced utility for clients with more relaxed privacy constraints. Some recent literature addresses this by exploring personalized, or individualized, DP (Yang et al., 2021; Shen et al., 2023; Malekmohammadi et al., 2024; Boenisch et al., 2024). For example, Boenisch et al. (2024) introduce individualized DP (IDP) and apply it to the DP- SGD algorithm, a collaboration- free variant of DP- FedAvg. The integration of IDP into a client- level DP- FL framework is natural. IDP enables individualized privacy budgets across clients and fine- tunes client- specific privacy parameters, through the use of different clip norms and/or sampling rates. We name this integrated algorithm IDP- FedAvg and formally present it as Alg. 5 in App.A.1. We note that IDP- FedAvg and our proposed approach, detailed in Sec.3, complement each other. In this paper, we consider IDP- FedAvg as another baseline for our approach.

Adaptive DP. Another approach to improve the utility performance of DP learning is parameter grouping (Yang et al., 2023; McMahan et al., 2017) which clusters the ML parameters with similar clipping norms and applies a non- uniform clipping across clusters. Similar to the IDP approach, our approach and parameter grouping are complementary. However, integrating our approach into parameter grouping optimally, given the increased parameter choices across clusters, requires a study on cluster- based parameter selection which is beyond the scope of this paper. Another approach used for utility improvement is adaptive clipping (Pichapati et al., 2019; Andrew et al., 2021; Li et al., 2022) which aims to optimize the clipping norms during training and thereby reduce the noise effect. Some of these papers either rely on a strong assumption of accessing public data (Li et al., 2022), or the strong assumption of minimal privacy loss occurs during parameter optimization (Pichapati et al., 2019). Our proposed approach is an alternative that selects privacy parameters non- uniformly over time. Compared to the above adaptive clipping methods, our approach eliminates the need for public data, and as parameter selection is done prior to training, ensures zero privacy loss. Another adaptive clipping method that is not limited to prior assumptions and is more relevant to ours is the one proposed by Andrew et al. (2021), and presented as Alg. 6 and detailed in App.A.1. While

targeting the same goal of improving the privacy- utility tradeoff as ours, the method (Andrew et al., 2021) maintains fixed privacy spending over time. Note that adaptive clipping is orthogonal to our approach and could be combined with ours for potential performance gain. However, such integration requires careful privacy analysis, which is beyond the scope of this paper and left for future work. In App. A.8, we benchmark our approach against the method (Andrew et al., 2021), showing that our approach achieves a better privacy- utility tradeoff.

# 3 PROPOSED FRAMEWORK

We introduce a novel time- adaptive DP- FL framework to solve the FL optimization problem with high utility and under privacy constraints that are not meant to be spent uniformly over time. We first discuss our threat space and privacy- related hyperparameters (summarized in Table 1).

Threat Space. We assume that each client aims to prevent data leakage to any other client who may be honest but curious (HbC). Specifically, HbC clients follow the FL protocol honesty but may attempt to infer sensitive information of the victim client from the shared model updates. We assume the server is trusted but rather than offering zero protection, we make clients perturb their model updates before sharing with the server to preserve privacy, though at a lower level. This is because, after the server aggregates the perturbed model updates, the total perturbation increases, providing stronger privacy protection against other clients than the server. For enhanced protection against the server, one can use secure aggregation (Bonawitz et al., 2016) which ensures that the server only learns an aggregated function (typically the sum) of the clients' local updates, without learning individual updates. However, the design or implementation of secure aggregation schemes is beyond the scope of this paper, and we mainly focus on preserving privacy against HbC clients.

Privacy Hyperparameters. In spend- as- you- go method, which we will detail as part of our framework design in Sec. 3.1, each client saves a specific fraction of their budget in certain rounds, then incrementally spends the saved portion over time. We realize savings by using client- specific sampling rates, denoted as  $q_{n} \in [0,1]$  for client  $n$ , during "saving" rounds and by using a uniform, higher, sampling rate, denoted as  $q \in [0,1]$ , during "spending" rounds. If  $q_{n} < q$ , client  $n$  is less likely to be sampled during the saving rounds. According to privacy amplification by subsampling (Beimel et al., 2014), and as detailed in Sec. 4.1, a fraction of the clients' privacy budget will thereby be saved for later rounds. Another hyperparameter is each client's designated round for transitioning from the saving to the non- saving (spend) mode. For each  $n \in [N]$ , we use  $T_{n}$  to denote the first round in which client  $n$  is in the spending mode. Intuitively, if  $T_{n}$  is aligned with the client  $n$  transitioning from the coarse- grained training of early rounds to the fine- grained feature training of later rounds, the client's saved budget during early rounds enables the client to spend more in later rounds when additional accuracy is beneficial in learning more fine- grained features. By setting  $q_{n} = q$  or  $T_{n} = 1$  for every  $n \in [N]$ , each client's privacy spending becomes uniform over time, resembling the traditional non- time- adaptive spending approaches. We show the hyperparameters,  $q_{n}$  and  $T_{n}$ , which are specific to our framework, in the first two rows of Table 1. Other hyperparameters, which are common in much of the DP- FL literature, are summarized in rows 3 to 10 of Table 1. The privacy- related parameters, consistent across clients and fixed over time, include the global clip norm  $c$ , the sampling rate  $q$ , the DP parameter  $\delta$ , and the RDP- related order  $\alpha$ . The configuration of these hyperparameters to avoid additional privacy loss is covered partly in simulations (Sec. 5 and App. A.8) and partly in theory (Sec. 4). Later, in Sec. 6, we discuss potential future directions for broader hyperparameter tuning.

# 3.1 THE SPEND-AS-YOU-GO METHOD

We consider an FL setting where every client  $n\in [N]$  is given a privacy budget  $\epsilon_{n}$  derived from their individual privacy preferences. We assume all clients exhaust their privacy budget after  $T$  global rounds. We propose the spend- as- you- go method, which is executed before training begins, and obtains the following privacy parameters used during execution: the local noise multipliers  $\sigma_{n}^{t}$  the sampling rates  $q_{n}^{t}$  , the privacy budget remaining  $\epsilon_{n}^{t}$  (with an initial value of  $\epsilon_{n}^{0} = \epsilon_{n})$  , and the local clip norms  $c_{n}^{t}$  .Each  $q_{n}^{t}$  is chosen as either  $q$  or  $q_{n}$  , depending on whether client  $n$  is in spending or saving mode. From now on, we denote  $q_{n}^{t}$  as the sampling rate, and to distinguish

between  $q$  and  $q_{n}$  , we respectively refer to them as the spending- based sampling rate and the savingbased sampling rate. To denote whether clients are in saving or spending mode at each round, we use  $M_{n}^{t}$  , a binary variable that is set to O if round  $t$  is a saving round for client  $n$  and 1 otherwise. I.e.,  $M_{n}^{t} = 0$  if  $t< T_n$  and  $M_{n}^{t} = 1$  if  $t\geq T_n$  Algorithm 1 presents the pseudocode for the spend- as- you- go method. Regardless of the saving or sampling mode, in each round, we first find the value  $\sigma_{n}^{t}$  required to obtain the remaining privacy budget  $\epsilon_{n}^{t}$  using the GetNoise function (Line 4 of  $\mathrm{Alg.1}$  ). This function takes as inputs  $\epsilon_{n}^{t},\delta ,q_{n}$  and the number of remaining rounds  $T - t$  During the spending rounds of client  $n$  (i.e., when  $M_{n}^{t} = 1)$  , the client is sampled according to  $q_{n}^{t} = q$  . During the saving rounds (when  $M_{n}^{t} = 0$  ), the client sets  $q_{n}^{t} = q_{n}$  . As shown in lines 6- 8, we calculate the privacy spent and  $\epsilon_{n}^{t}$  at the end of each round  $t\in [T]$  and for every client  $n\in [N]$  . Using Compute- rdp function, we calculate the privacy spent given  $q_{n}^{t},\sigma_{n}^{t}$  , and  $\alpha$  . We then use get- privacy- spent to convert the RDP privacy spent into the DP privacy spent, given the RDP privacy spent,  $\alpha$  , and  $\delta$  . We finally follow the same procedure as in Boenisch et al. (2024) to compute local clip norms  $c_{n}^{t}$  such that their average across  $n\in [N]$  equals the hyperparameter  $c$  To achieve this, we calculate  $c_{n}^{t} = c\sigma^{t} / \sigma_{n}^{t}$  (Line 12), where  $\begin{array}{r}\sigma^{t} = N\left(\sum_{n\in [N]}1 / \sigma_{n}^{t}\right)^{- 1} \end{array}$  (Line 10).

Table 1: Hyperparameters Summary  

<table><tr><td>Tn</td><td>Saving-to-spending transition round</td></tr><tr><td>qn</td><td>Saving-based sampling rate of Client n</td></tr><tr><td>T</td><td>Number of rounds</td></tr><tr><td>L</td><td>Number of local iterations</td></tr><tr><td>B</td><td>Batch size</td></tr><tr><td>λ</td><td>Learning rate</td></tr><tr><td>α</td><td>Reynolds in RDP</td></tr><tr><td>δ</td><td>Probability of violating in DP</td></tr><tr><td>c</td><td>Average clipping norm</td></tr><tr><td>q</td><td>Global (spending-based) sampling rate</td></tr></table>

# Algorithm 1 The spend-as-you-go Method in Our Time-adaptive DP-FL Framework

<table><tr><td colspan="2">Inputs: No. clients N, No. global rounds T, local privacy budgets
εn, sampling rate q, average clip norm c, modes Mnt, ε = {0, 1},
sampling rates for saving mode qn, Prob. of violating δ.</td></tr><tr><td>Def Set PrivacyParams</td><td>(c, q, {qn, cn, Mnt}n∈[NT], T, δ)</td></tr><tr><td>1: Initialize ε0 = εn and ε0
2: for each global round t ∈ [T] do
3: for each client n ∈ [N] do
4: 
σt = GetNoise(εt-1, δ, q, T - t)
5: 
If Nt = 0, then qt = qn, Else qt = q.
6: 
εrdp,n = Compute rdp(qn, σt, α)
7: 
εt
rdp,n = εt-1
rdp,n + εt
rdp,n</td><td></td></tr><tr><td>8: 
εn = εn - get-privacy-spent(α, εrdp,n, δ)</td><td></td></tr><tr><td>9: 
end for</td><td></td></tr><tr><td>10: 
Compute αt←(1/∑n∈[NT]σt)−1</td><td></td></tr><tr><td>11: 
for each client n ∈ [NT] do</td><td></td></tr><tr><td>12: 
Set local clip norm ct = cgt
13: 
end for</td><td></td></tr><tr><td>14: 
qt = 1/N∑n=1qt</td><td></td></tr><tr><td>15: 
end for</td><td></td></tr><tr><td>16: 
Return {qt, σt, {qt, cn, σt}n∈[NT]}t∈[NT]</td><td></td></tr></table>

# Algorithm 2 Iterative Training in Our Time-adaptive DP-FL Framework

Inputs: No. clients  $N$  No.global rounds  $T$  No. local iterations  $L$  local privacy budgets  $\epsilon_{n}$  sampling rate  $q$  average clip norm  $c$  modes  $M_{n}^{t}\in \{0,1\}$  sampling rates for saving mode  $q_{n}$  , loss functions  $\mathcal{L}_n$  datasets  $\mathcal{D}_n$  , learning rate  $\lambda$  batch size  $B$  Prob. of violating  $\delta$

1:  $\{q^{t},\sigma^{t},\{q_{n}^{t},c_{n}^{t},\sigma_{n}^{t}\}_{n\in [N]}\} _{t\in [T]}$ $= \text{SetPrivacyParams}\left(c,q,\{q_n,c_n,M_n^t\}_{n\in [N]},T,\delta\right)$  2: Initialize global model  $\theta^0$  3: for each global round  $t\in [T]$  do 4:  $c^{t}\leftarrow$  Sample clients with probability  $\{q_{n}^{t}\}$  5: for each client  $n\in [N]$  in parallel do 6:  $\bar{\Delta}\theta_{n}^{t} = \mathrm{ClientUpdate}(t,n,\theta^{t - 1},c^{t},\sigma_{n}^{t},\sigma_{n}^{t})$  7: end for 8:  $\begin{array}{r}\bar{\Delta}\theta^{t} = \sum_{n\in \mathcal{E}^{t}}\bar{\Delta}\theta_{n}^{t} + \sum_{n\in [N]}\mathcal{C}^{t}\mathcal{N}\left(0,\frac{c^{2}(\sigma^{t})^{2}}{N}\mathbb{I}\right) \end{array}$  9: Update  $\begin{array}{r}\theta^{t} = \theta^{t - 1} + \frac{\bar{\Delta}\theta^{t}}{q^{t}N} \end{array}$  10: end for Def ClientUpdate  $(t,n,\theta_{n}^{t - 1},c_{n}^{t},\sigma_{n}^{t})$  1: Initialize local model  $\theta_{n}^{t} = \theta^{t - 1}$  2: for local iteration  $\ell \left[l\right]$  do 3:  $\{B_i\}_{i = 1}^{l = n} / B\gets \mathrm{Split}D_n$  to size  $B$  batches 4: for each batch  $B_{i}$  do 5:  $\begin{array}{r}\theta_{n}^{t,l} = \theta_{n}^{t,l - 1} - \frac{\lambda\sum_{(\mathbf{x},\mathbf{y})\in B_{i}}\nabla_{L}n(\theta_{n}^{t,l - 1};(\mathbf{x},\mathbf{y}))}{B} \end{array}$  6: end for 7: end for 8: Compute  $\Delta \theta_{n}^{t} = \theta_{n}^{t,L} - \theta_{n}^{t,0}$  9: Clip  $\bar{\Delta}\theta_{n}^{t} = \Delta \theta_{n}^{t}\min \left(1,\frac{c_{n}^{t}}{\|\Delta\theta_{n}^{t}\|_{2}}\right)$  10: Add noise  $\bar{\Delta}\theta_{n}^{t} = \bar{\Delta}\theta_{n}^{t} + \mathcal{N}\left(0,\frac{(\bar{c}_{n}^{t})^{2}(\sigma_{n}^{t})^{2}}{N}\right)$  11: Return  $\bar{\Delta}\theta_{n}^{t}$

# 3.2 THE ITERATIVE TRAINING MODULE

After setting parameters through the spend- as- you- go method, our DP- FL framework operates the iterative training module, with the pseudocode presented in Algorithm 2. We note that although the primary contribution of our DP- FL framework lies in the spend- as- you- go method, the iterative

training module also differs from the baseline due to the use of time- adaptive privacy parameters and has to be carefully designed. This module spans  $T$  communication rounds. Within each round, clients conduct  $L$  iterations of local training. For iteration  $l \in [L]$  within round  $t \in [T]$ , let  $\theta_{n}^{t,l}$  denote the local model of client  $n$ . In round  $t \in [T]$ ,  $\mathcal{C}^t$  denotes the set of workers contributing to local training. This set is randomly chosen using Poisson sampling (Line 4). Each client  $n \in [N]$  in round  $t \in [T]$  has an independent probability  $q_{n}^{t} \in [0,1]$  of being selected for  $\mathcal{C}^t$ . In expectation  $q^{t}N$  clients, where  $q^{t} = \frac{1}{N} \sum_{n \in [N]} q_{n}^{t}$ , are sampled in round  $t$ .

Client update: In round  $t$  each client  $n\in [N]$  initializes  $\theta_{n}^{t,0} = \theta^{t - 1}$  (Line 1 of ClientUpdate). It then performs  $L$  iterations of local training, using a gradientbased technique, such as mini- batch SCD. In each iteration  $l\in [L]$  the client first splits  $\mathcal{D}_n$  into size  $B$  batches (Line 3 of ClientUpdate). For each batch  $B_{i}$  the nth client updates  $\begin{array}{r}\theta_{n}^{t,l} = \theta_{n}^{t,l - 1} - \frac{\tilde{B}}{\tilde{A}}\sum_{(\mathbf{x},y)\in B_i}\nabla \mathcal{L}_n\left(\theta_n^{t,l - 1};(\mathbf{x},y)\right) \end{array}$  , where  $\lambda$  is the learning rate and  $\begin{array}{r}\frac{1}{B}\sum_{(\mathbf{x},y)\in B_i}\nabla \mathcal{L}_n\left(\theta_n^{t,l - 1};(\mathbf{x},y)\right) \end{array}$  estimates the gradient  $\nabla \tilde{\mathcal{L}}_n(\theta_n^{t,l - 1})$  (Line 5 of ClientUpdate). Once local training finishes, the client computes the resulting model update  $\Delta \theta_{n}^{t} = \theta_{n}^{t,l} - \theta_{n}^{t}$  (Line 8 of ClientUpdate). The first phase of integrating the DP mechanism in the iterative training module is to assign the client the task of clipping  $\Delta \theta_{n}^{t}$  as shown in (Line 9 of ClientUpdate), and detailed as  $\tilde{\Delta \theta}_{n}^{t} = \Delta \theta_{n}^{t}\min \left(1,\frac{c_{n}^{t}}{\|\theta_{n}^{t}\|_{2}}\right)$  where  $c_{n}^{t}$  is the clip norm. The client then perturbs further its clipped model update by injecting random noise (Line 10 of ClientUpdate). The noise is selected independently for each client  $n\in \mathcal{C}^t$  in round  $t$  Algebraically, given noise  $z_{n}^{t}\sim \mathcal{N}\left(0,\frac{(c_{n}^{t}\sigma_{n}^{t})^{2}}{N}\mathbb{I}\right),\tilde{\Delta}\theta_{n}^{t}\gets \tilde{\Delta}\theta_{n}^{t} + z_{n}^{t}$

Server aggregation: As shown in lines 6 and 8, the server aggregates  $\tilde{\Delta \theta}_{n}^{t}$  for all  $n\in \mathcal{C}^t$  , and computes the sum  $\begin{array}{r}\Delta \theta^{t} = \sum_{n\in \mathcal{C}^{t}}\tilde{\Delta}\theta_{n}^{t} \end{array}$  . For those clients not being selected, i.e.,  $n\in [N]\backslash \mathcal{C}^t$  , the server compensates by injecting additional noise  $\begin{array}{r}\tilde{\Delta\theta}^{t}\gets \tilde{\Delta\theta}^{t} + \sum_{n\in [N]\backslash \mathcal{C}^{t}}\mathcal{N}\left(0,c^{2}(\sigma^{t})^{2} / N\mathbb{I}\right) \end{array}$  . Assuming  $c\sigma^{t} = c_{n}^{t}\sigma_{n}^{t}$  for all  $n\in [N]$  , because of this compensation, the total noise power  $\mathcal{N}\left(0,c^{2}(\sigma^{t})^{2}\mathbb{I}\right)$  that is injected to the global model update is not a function of the sampling. The round ends with the server computing  $\begin{array}{r}\theta^{t} = \theta^{t - 1} + \frac{\tilde{\Delta\theta}^{t}}{q^{t}N} \end{array}$  (Line 9).

# 4 THEORETICAL ANALYSIS

For the DP- FL framework (described in Sec. 3), we now develop the theory that underlies our privacy accounting (Sec. 4.1), and optimize the sampling rates to improve utility (Sec. 4.2).

# 4.1 PRIVACY ACCOUNTING

In this section, we calculate the RDP bounds for each client  $n\in [N]$  at round  $t\in [T]$  . The results will support the general idea of save- to- spend in our framework. Since we use RDP to perform privacy accounting, we convert each client's privacy budgets  $\{\epsilon_{n}\}_{n\in [N]}$  into the RDP equivalent, denoted  $\{\epsilon_{\mathrm{rdp},n}\}_{n\in [N]}$  , at a fixed order  $\alpha$  . For client  $n$  , we use  $\epsilon_{\mathrm{rdp},n}^{t}$  and  $\epsilon_{\mathrm{rdp - left},n}^{t}$  to denote, respectively, the RDP privacy spent in round  $t$  and the "go- forward" RDP privacy budget remaining for round  $t$  onwards.

The privacy accounting for the spend- as- you- go method first involves calculating each  $\epsilon_{\mathrm{rdp},n}^{t}$  (cf. line 6 of Alg. 1). Next, the total RDP privacy spent during the first  $t$  rounds is calculated as  $\sum_{\tau = 1}^{t - 1}\epsilon_{\mathrm{rdp},n}^{\tau}$  (Line 7 of Alg. 1), with budget remaining  $\epsilon_{\mathrm{rdp - left},n}^{t} = \epsilon_{\mathrm{rdp},n} - \sum_{\tau = 1}^{t - 1}\epsilon_{\mathrm{rdp},n}^{\tau}$ . Recalling from Sec. 3.1, client  $n$  selects the noise multiplier  $\sigma_{n}^{t}$  assuming that  $\epsilon_{\mathrm{rdp - left},n}^{t}$  will be spent uniformly across the remaining  $T - t + 1$  rounds subject to using sampling rate  $q$ . As shown by Mironov et al. (2019), under this uniform assumption  $\sigma_{n}^{t}$  should satisfy  $\epsilon_{T - t + 1}^{t}$  and  $\epsilon_{T - t + 1}^{t}$ . When  $t < T_{n}$ , the sampling rate  $\sigma_{n}$  reduces the RDP expenditure to  $\epsilon_{\mathrm{rdp},n}^{t} = \frac{\epsilon_{\mathrm{rdp - left},n}^{t}(q_{n})^{2}}{(T - t + 1)(q)^{2}}$ . When  $t \geq T_{n}$ , the full allocated budget is used in round  $t$ . The RDP privacy spend  $\epsilon_{\mathrm{rdp},n}^{t}$  can be computed recursively as

$$
\epsilon_{\mathrm{rdp},n}^{t} = \left(\frac{\epsilon_{\mathrm{rdp},n} - \sum_{\tau = 1}^{t - 1}\epsilon_{\mathrm{rdp},n}^{\tau}}{T - t + 1}\right)\left(\mathbf{1}_{t< T_{n}}\left(\frac{q_{n}^{t}}{q}\right)^{2} + \mathbf{1}_{t\geq T_{n}}\right). \tag{1}
$$

Lemma 1 solves the recursive formula (1) for the RDP spent. Theorem 1 shows the RDP spent is non- decreasing over time. The proofs of Lem. 1 and Thm. 1 are respectively provided in App. A.2 and A.3.

Lemma 1. Given any  $n\in [N],t\in [T]$  and  $T_{n}\in [T]$  we have

$$
\epsilon_{rdp,n}^{t} = \left\{ \begin{array}{ll}\frac{\epsilon_{rdp,n}}{T - t + 1}\left(\frac{q_n}{q}\right)^2\prod_{i = 1}^{t - 1}\left(1 - \frac{1}{T - t + 1 + i}\left(\frac{q_n}{q}\right)^2\right) & if t< T_n\\ \frac{\epsilon_{rdp,n}}{T - T_n + 1}\prod_{i = 1}^{T_n - 1}\left(1 - \frac{1}{T - T_n + 1 + i}\left(\frac{q_n}{q}\right)^2\right) & ow \end{array} \right.. \tag{2}
$$

Theorem 1. For  $(n,t,T_n)\in [N]\times [T]\times [T],\epsilon_{rdp,n}^t\geq \epsilon_{rdp,n}^{t - 1}if t\leq T_n,$  and  $\epsilon_{rdp,n}^{t} = \epsilon_{rdp,n}^{t - 1}ift > T_{n}$  Remark 1. The non- decreasing result of Thm. 1 indicates that during saving rounds  $(M_n^t = 0)$  clients spend at least as much of their privacy budget in each round as in the previous round. In other words, saving decreases over time. During spending rounds  $(M_n^t = 1)$  clients expend privacy budget at a constant rate. If budget savings are accumulated than  $\epsilon_{rdp,n}^{T_n} > \epsilon_{rdp,n}^{T_n - 1}$  and clients will have access to a larger budget to spend.

# 4.2 OPTIMAL PERMUTATION OF SAVING-BASED SAMPLING RATES

We now optimize the selection of sampling rates. We start from a pre- defined set of  $N$  sampling rates. We then choose a permutation that assigns each of the  $N$  rates to a distinct client. The permutation is selected to minimize the per- round difference between the utility achieved when DP perturbation is not applied (which generally will maximize utility), and the utility achieved when our DP- FL framework is used. In the first case, the server aggregates the unperturbed local updates  $\Delta \theta_{n}^{t}$  (i.e., no clipping or noise addition) for all clients  $n\in [N]$  (i.e., no sub- sampled), and updates the global model as  $\theta^{t} = \theta^{t - 1} + \Delta \theta^{t}$  where

$$
\Delta \theta^{t} = \frac{1}{N}\sum_{n = 1}^{N}\Delta \theta_{n}^{t}. \tag{3}
$$

In our DP- FL framework, local updates undergo the DP mechanism detailed in Sec. 3. Factoring into the (modified) global update  $\tilde{\Delta}\theta^{t}$  the clipping norms  $c_{n}^{t}$ , the additive noise multipliers  $\sigma_{n}^{t}$ , and the sampling rates  $q_{n}^{t}$ , we get

$$
\tilde{\Delta}\theta^{t} = \frac{1}{Nq^{t}}\sum_{n = 1}^{N}\left(\mathsf{b}_{n}^{t}\left(\mathrm{Clip}\left(\Delta \theta_{n}^{t},c_{n}^{t}\right) + \mathbf{z}_{n}^{t}\right) + (1 - \mathsf{b}_{n}^{t})\tilde{z}_{n}^{t}\right), \tag{4}
$$

where  $\begin{array}{r}q^{t} = \frac{1}{N}\sum_{n = 1}^{N}q_{n}^{t},z_{n}^{t},\tilde{z}_{n}^{t}\sim \mathcal{N}\left(0,\frac{\left(\sigma_{n}^{t}c_{n}^{t}\right)^{2}}{N}\right) \end{array}$  are identically and independently distributed (IID), and  $\mathsf{b}_n^t\sim \mathsf{Bern}(q_n^t)$  are also IID. The optimization problem is to choose the  $\{\sigma_{n}^{t}\}_{n\in [N]}$  so that  $\tilde{\Delta}\theta^{t}$  closely approximates an unbiased estimate of  $\Delta \theta^{t}$  . We define the difference between the respective model updates as

$$
\mathrm{Error}^t \coloneqq \Delta \theta^t -\tilde{\Delta}\theta^t. \tag{5}
$$

Error has four sources of randomness:

(i) Local dataset randomness: the randomness of local datasets  $\mathcal{D}_n$ , which are sampled from distributions  $P_{n}$ . This randomness is reflected in the local updates  $\Delta \theta_{n}^{t}$ . (ii) Client sampling randomness: the sampling of clients, represented by the use of random variables  $\mathsf{b}_n^t$ . These determine whether a client  $n$  contributes to the round  $t$ 's local training. (iii) Noise addition randomness: the addition of the Gaussian noises  $\mathbf{z}_n^t$  and  $\tilde{\mathbf{z}}_n^t$ . (iv) Privacy budget assignment randomness: the matching of clients with different datasets  $\mathcal{D}_n$  and data distributions  $P_{n}$  to different privacy budgets  $\epsilon_{n}$ . Here,  $\{\epsilon_{n}\}_{n\in [N]}$  are considered as a random permutation of a predefined set of privacy budgets  $\{\epsilon_{n}\}_{n\in [N]}$ .

To optimize the sampling rates, we first develop two upper bounds on the bias term  $\left\| \mathbb{E}\left(\mathrm{Error}^t\right)\right\|$ . Both bounds build on the clipping bias lemma (Das et al., 2023), cf., Lem. 2 in App. A.6.1. Our theorems extend the results of that lemma to the situation where clients have individualized privacy budgets and use time- varying clip norms  $c_{n}^{t}$  and sampling rates  $q_{n}^{t}$ .

Our first theorem, Thm. 2, bounds the expected bias with respect to (w.r.t.) three sources of randomness: (i), (ii), and (iii). We denote this expectation as  $\left\| \mathbb{E}_{(i),(ii),(iii)}\left(\mathrm{Error}^{t}\right)\right\|$ . The proof of Thm. 2 is given in App. A.4.

Theorem 2. Taking the expectation w.r.t. (i), (ii), and (iii), and for any  $\rho >1$  we have

$$
\left\| \mathbb{E}_{(i),(ii),(iii)}\left(\boldsymbol {E}\boldsymbol {r}\boldsymbol {r}^{t}\right)\right\| \leq \frac{1}{N}\left\| \sum_{n = 1}^{N}\left(1 - \frac{q_{n}^{t}}{q^{t}}\right)\mathbb{E}_{(i)}\left(\Delta \theta_{n}^{t}\right)\right\| +\frac{1}{N}\sum_{n = 1}^{N}\frac{q_{n}^{t}}{q^{t}}\frac{\mathbb{E}_{(i)}\left(\left\| \Delta \theta_{n}^{t}\right\|^{\rho}\right)}{(c_{n}^{t})^{\rho - 1}}. \tag{6}
$$

To minimize this bias term in a reasoned fashion we select the  $q_{n}^{t}$  to minimize the upper bound. The upper bound in (6) contains terms that couple  $q_{n}^{t}$  with the pure local updates  $\Delta \theta_{n}^{t}$ . The latter are not accessible to the server who is responsible for sampling clients. If clients were to select their own  $q_{n}^{t}$  based on their local updates, this could lead to privacy leakage and would require additional privacy protection. The coupling between  $q_{n}^{t}$  and  $\Delta \theta_{n}^{t}$  can be removed from the first term of the upper bound in (6) under certain conditions. For example, if all clients are sampled at the same rate  $(q_{n}^{t} = q^{t})$  or if  $\mathbb{E}_{(i)}(\Delta \theta_{n}^{t})$  is equal across all  $n\in [N]$ , the first term becomes zero. In such cases, the upper bound reduces to the second term, which still couples  $q_{n}^{t}$  with  $\Delta \theta_{n}^{t}$  and the clip norms  $c_{n}^{t}$ .

In contrast to Thm. 2, in Thm. 3 we take the expectation w.r.t. the additional source of randomness, (iv). This results in a bound that depends solely on clipping norms, which makes optimizing the sampling rates  $q_{n}^{t}$ , easier to accomplish. As we now bound the expected bias w.r.t. all four sources of randomness - (i), (ii), (iii), and (iv) - we denote the expectation as  $\left\| \mathbb{E}_{(i),(ii),(iii),(iv)}\left(\mathrm{Error}^{t}\right)\right\|$ . The proof of Thm. 3 is given in App. A.5.

Theorem 3. Taking the expectation w.r.t. (i), (ii), (iii), and (iv), and for any  $\rho >1$  we have:

$$
\left\| \mathbb{E}_{(i),(ii),(iii),(iv)}\left(\boldsymbol {E}\boldsymbol {r}\boldsymbol {r}^{t}\right)\right\| \leq \frac{1}{N^{2}}\left(\sum_{n = 1}^{N}\mathbb{E}_{(i)}\left(\left\| \Delta \theta_{n}^{t}\right\|^{\rho}\right)\sum_{n = 1}^{N}\left(\frac{q_{n}^{t}}{q^{t}}\frac{1}{(c_{n}^{t})^{\rho - 1}}\right)\right.. \tag{7}
$$

The main step in the proofs of Thm. 3 builds on the common assumption in FL that the sampling from  $P_{n}$  and of  $\epsilon_{n}$  is independent. This assumption is made without significant loss of generality as privacy budgets are often assigned based on clients' personal preferences and policy requirements. In contrast, the data distribution is influenced by external factors such as geographical locations. Such decoupling of privacy budgets and data distributions simplifies the proof of Thm. 3. It avoids the need to model potential correlations between the client's data and privacy preferences. These are often unknown or irrelevant in practice.

As per Thm. 3, to minimize  $\left\| \mathbb{E}_{(i),(ii),(iii),(iv)}\left(\mathrm{Error}^{t}\right)\right\|$  w.r.t. the sampling rates  $q_{n}^{t}$ , we solve the following optimization problem:

$$
\begin{array}{rl}\min_{\{\Pi_t\}_{t = 1}^T} & \sum_{n = 1}^N\frac{q_n^t}{q_n^t}\frac{1}{(c_n^t)^{\rho - 1}}\\ \mathrm{s.t.} & q_n^t = \mathfrak{q}_{\Pi_t^{-1}(n)},n\in [N] \end{array} \tag{8}
$$

where the  $\{\Pi_t\}$  are a set of (bijective) permutation maps  $\Pi_t:[N]\to [N]$ . Each permutation maps each element of the index set  $[N]$  to a distinct element of  $[N]$ . We apply the permutation to the indices of the fixed set of sampling rates  $\{q_{1},\ldots ,q_{N}\}$  to get  $\{q_{1}^{t},\ldots ,q_{N}^{t}\}$ . The set  $\{q_{1},\ldots ,q_{N}\}$  is a hyperparameter in our problem, which is constrained by the condition  $q_{n}\leq q$  for every  $n\in [N]$ . The optimized choice of the set of sampling rates  $\{q_{1},\ldots ,q_{N}\}$  is reserved for future work. Per (8), the optimal choice for  $\{q_{1}^{t},\ldots ,q_{N}^{t}\}$  is to assign clients with smaller clip norms  $c_{n}^{t}$  (which will contribute highly perturbed model updates) to have lower sampling rates  $q_{n}^{t}$ , and vice versa. The intuition is that by matching the  $q_{n}^{t}$  to the  $c_{n}^{t}$ , we prevent clients who contribute a highly perturbed model update from deteriorating other clients' performance during saving rounds. This reduces clipping bias and allows these clients to preserve more of their privacy budget compared, with the subsequent benefit of enabling them to contribute more in future rounds.

Datasets. To empirically evaluate the performance of our framework against baselines, we consider widely used datasets. For the Fashion MNIST (FMNIST) and MNIST, we use a convolutional neural network (CNN) architecture from (McMahan et al., 2017). For the Adult Income dataset, we use multi- layer perception from (Zhang et al., 2020). For the CIFAR10 dataset, we use a CNN architecture from He et al. (2016). We partition datasets across 100 clients in a non- IID manner using the Dirichlet distribution with a default parameter 0.1 (Zhang et al., 2023).

Privacy Settings. To reduce the number of choices for clients' privacy budgets, and motivated by society wherein individuals often share similar privacy preferences (Alaggan et al., 2015; Boenisch et al., 2024), in our experiments we divide clients into three groups. The groups are respectively assigned budgets of  $\epsilon_{\mathrm{group},1}$ ,  $\epsilon_{\mathrm{group},2}$ , or  $\epsilon_{\mathrm{group},3}$ . We randomly allocate  $34\%$  of clients to belong to Group 1,  $43\%$  to Group 2, and  $23\%$  to Group 3. In other words, Client  $n$  in Group  $m$ , shares  $\epsilon_{n} = \epsilon_{\mathrm{group},m}$ . To account for privacy consumption, we use the Opacus library (Yousefpour et al., 2021). We consider DP parameter  $\delta = 10^{- 5}$  and choose an extended version of the default RDP parameter  $\alpha$  from the RDPAccountant function in Opacus. We apply per- layer clipping (McMahan et al., 2017) to restrict the influence of individual layers by constraining their norms.

Baselines. As discussed in Sec. 2, we consider several baselines. The first is the non- private Fe- dAvg (McMahan et al., 2017) which represents our upper bound on utility without any privacy constraints. The second is DP- FedAvg (McMahan et al., 2017) where we use a uniform privacy budget, chosen according to the smallest epsilon value of any of the clients. This ensures that no client's privacy budget is exceeded. The fourth is IDP- FedAvg which is the IDP- integration (Boenisch et al., 2024) of DP- FedAvg. The fourth is adaptive clipping (Andrew et al., 2021). To have a fair comparison with the baselines, we evaluate two variants of our framework. The first variant assumes every client's budget is constrained by the smallest value in the group budget tuple  $(\epsilon_{\mathrm{group},1},\epsilon_{\mathrm{group},2},\epsilon_{\mathrm{group},3})$ . The second variant incorporates different privacy groups. Further details on the choice of hyperparameters in our experiments are given in Table 4 in Appendix A.7.

![](images/79bbb22884b20a8237c972d543371324f12657380bfc8e46bae9d77167ab79ee.jpg)  
Figure 1: Our framework improves accuracy in later rounds compared to the baseline. We plot the global test accuracy vs. rounds for (a) the FMNIST dataset, and (b) the MNIST dataset. In (a),  $(\epsilon_{\mathrm{group},1},\epsilon_{\mathrm{group},2},\epsilon_{\mathrm{group},3}) = (10,20,30)$ , and in (b) it equals (10, 15, 20).

Table 2: Global test accuracy for FedAvg without DP constraints, DP-FedAvg with  $\epsilon_{n} = 10$  ,IDPFedAvg with non-uniform privacy budgets, our framework with  $\epsilon_{n} = 10$  , and our framework with non-uniform budgets. For the FMNIST and Adult Income datasets, the non-uniform privacy budgets  $\epsilon_{n}$  are  $(\epsilon_{\mathrm{group},1},\epsilon_{\mathrm{group},2},\epsilon_{\mathrm{group},3}) = (10,20,30)$  , and for MNIST, they are (10, 15, 20).  

<table><tr><td>DATASET</td><td>FedAvg 
(non-DP)</td><td>DP-FedAvg 
εn= 10</td><td>IDP-FedAvg 
Non-uniform</td><td>Ours 
εn= 10</td><td>Ours 
Non-uniform</td></tr><tr><td>FMNIST</td><td>72.95</td><td>64.8</td><td>65.45</td><td>67.90</td><td>70.57</td></tr><tr><td>MNIST</td><td>90.23</td><td>76.79</td><td>76.94</td><td>80.2</td><td>83.83</td></tr><tr><td>Adult Income</td><td>78.93</td><td>60.12</td><td>70.93</td><td>72.14</td><td>77.53</td></tr></table>

![](images/77bfb25f9c579249bceede127426faeaa5e80bc2a5ab715e1869d8b2e2d6513d.jpg)  
Figure 2: While both adhere to privacy budgets, our framework follows spend-as-you-go, whereas IDP-FedAvg uses uniform privacy spending. The blue solid curves correspond to clients' privacy spending in our framework, while the red dashed curves show IDP-FedAvg. The curves of clients with budgets of 30, 20, and 10 are marked with rectangles, circles, and squares, respectively.

Experimental Results. As shown in Table 2 and Fig. 1, our framework yields improvements in the resulting global model's accuracy by spending privacy budget non- uniformly across training rounds. Comparing Columns 4 and 6 of Table 2, our framework with non- uniform privacy budgets improves global test accuracy over IDP- FedAve by  $7.8\%$ ,  $8.9\%$ , and  $9.3\%$  on FMNIST, MNIST, and Adult Income. In the case of using a uniform budget of  $\epsilon_{n} = 10$  across all clients  $n$ , our framework achieves respective improvements of  $4.7\%$ ,  $4.4\%$ , and  $19.9\%$  compared to DP- FedAvg, as shown in Columns 3 and 5. We also observe that, our time- adaptive DP- FL scheme comes closest to the ideal- case performance of FedAvg (column 2) without privacy constraints. Figure 1 plots global test accuracy vs. global rounds for our framework with non- uniform privacy budgets and IDP- FedAvg, on the FMNIST and MNIST datasets. This figure shows that while our framework conserves privacy in early rounds, it allocates more budget in later rounds, eventually catching up to and surpassing IDP- FedAvg by about  $8\%$  on FMNIST and  $6\%$  on MNIST in the final round.

In Fig. 2 we present the privacy budget spent by clients from different budget groups (10, 20, 30) across rounds. This figure shows that, while IDP- FedAvg enforces uniform privacy consumption over time, in our framework, clients follow spend- as- you- go, saving budgets in the first half of training, and spending more in later rounds. Our experimental results demonstrate that our time- adaptive approach boosts the utility of the trained model while adhering to privacy constraints.

In Appendix A.8, we present extended experimental results, including benchmarks on the CIFAR10 dataset (Table 11), comparisons with adaptive clipping (Table 7), and evaluations across privacy- related hyperparameters (Tables 6, 9, and 10, and Figure 5), as well as other parameters (Tables 5 and 8 and Figure 4).

# 6 DISCUSSIONS AND FUTURE WORK

We now discuss some limitations of our work that represent interesting directions for future work. Our spend- as- you- go method reduces reliance on determining when clients should transition from saving to spending by allowing them to gradually spend their saved budgets over time, rather than waiting until a specific round to start spending. While our experiments indicate that transitioning from saving to spending midway through training generally yields good results, tuning the hyper- parameters involved in estimating the transitioning round may improve utility. However, such hyperparameter tuning can lead to additional privacy loss (Papernot & Steinke, 2021) that would need to be accounted for. For future work, we believe that our time- adaptive DP- FL framework should be closely integrated with a form of privacy- preserving hyperparameter tuning to identify the best rounds in which to transition from savings to spending.

Furthermore, as we demonstrated theoretically and validated experimentally, adapting saving- related hyperparameters to clients' specific privacy budgets can enhance utility. To eliminate the risk of privacy leakage from this adaptation, we provide theoretical optimizations that rely solely on clients' privacy- related constraints, independent of their data. Future research can explore data- and- privacy joint measures to quantify clients' contributions with controlled privacy leakage and adapt client- specific savings decisions accordingly.

# ACKNOWLEDGMENTS

ACKNOWLEDGMENTSWe would like to acknowledge our sponsors. This work was supported in part by a Discovery Research Grant from the Natural Sciences and Engineering Research Council of Canada (NSERC), by an NSERC Alexander Graham Bell Canada Graduate Scholarship- Doctoral (CGS D3), by a DiDi graduate award, and by the Mitacs Globalink research award.

# REFERENCES

REFERENCESMohammad Alaggan, Sébastien Gambs, and Anne- Marie Kermarrec. Heterogeneous differential privacy. *ArXiv preprint:1504.06998*, 2015. Galen Andrew, Om Thakkar, Brendan McMahan, and Swaroop Ramaswamy. Differentially private learning with adaptive clipping. *Advances in Neural Information Processing Systems*, 34:17455–17466, 2021. Barry Becker and Ronny Kohavi. Adult dataset. UCI Machine Learning Repository, 1996. DOI: https://doi.org/10.24432/C5XW20. Amos Beimel, Hai Brenner, Shiva Prasad Kasiviswanathan, and Kobbi Nissim. Bounds on the sample complexity for private learning and private data release. *Machine Learning*, 94:401–437, 2014. Franziska Boenisch, Adam Dziedzic, Roei Schuster, Ali Shahin Shamsabadi, Ilia Shumailov, and Nicolas Papernot. When the curious abandon honesty: Federated learning is not private. In *IEEE European Symposium on Security and Privacy (EuroS&P)*, pp. 175–199. IEEE, 2023. Franziska Boenisch, Christopher Mühl, Adam Dziedzic, Roy Rinberg, and Nicolas Papernot. Have it your way: Individualized privacy assignment for dp- sgd. *Advances in Neural Information Processing Systems*, 36, 2024. Keith Bonawitz, Vladimir Ivanov, Ben Kreuter, Antonio Marcedone, H Brendan McMahan, Sarvar Patel, Daniel Ramage, Aaron Segal, and Karr Seth. Practical secure aggregation for federated learning on user- held data. *ArXiv preprint:1611.04482*, 2016. Rudrajit Das, Satyen Kale, Zheng Xu, Tong Zhang, and Sujay Sanghavi. Beyond uniform lipschitz condition in differentially private optimization. In *International Conference on Machine Learning*, pp. 7066–7101. PMLR, 2023. Li Deng. The mnist database of handwritten digit images for machine learning research. *IEEE Signal Processing Magazine*, 29(6):141–142, 2012. Cynthia Dwork. Differential privacy. In *International Colloquium on Automata, Languages, and Programming*, pp. 1–12. Springer, 2006. Cynthia Dwork, Aaron Roth, et al. The algorithmic foundations of differential privacy. *Foundations and Trends® in Theoretical Computer Science*, 9(3–4):211–407, 2014. Adam Dziedzic, John Paparrizos, Sanjay Krishnan, Aaron Elmore, and Michael Franklin. Bandlimited training and inference for convolutional neural networks. In *International Conference on Machine Learning*, pp. 1745–1754. PMLR, 2019. Jonas Geiping, Hartmut Bauermeister, Hannah Dröge, and Michael Moeller. Inverting gradients- how easy is it to break privacy in federated learning? *Advances in Neural Information Processing Systems*, 33:16937–16947, 2020. Robin C Geyer, Tassilo Klein, and Moin Nabi. Differentially private federated learning: A client level perspective. *ArXiv preprint:1712.07557*, 2017. Kaiming He, Xiangyu Zhang, Shaoqing Ren, and Jian Sun. Deep residual learning for image recognition. In *Proceedings of the IEEE Conference on Computer Vision and Pattern Recognition*, pp. 770–778, 2016.

Rui Hu, Yuanxiong Guo, and Yanmin Gong. Federated learning with sparsified model perturbation: Improving accuracy under client- level differential privacy. IEEE Transactions on Mobile Computing, 2023. Alex Krizhevsky, Geoffrey Hinton, et al. Learning multiple layers of features from tiny images. 2009. Tian Li, Manzil Zaheer, Sashank Reddi, and Virginia Smith. Private adaptive optimization with side information. In International Conference on Machine Learning, pp. 13086- 13105. PMLR, 2022. Ilya Loshchilov and Frank Hutter. Sgdr: Stochastic gradient descent with warm restarts. 08 2016. doi: 10.48550/arXiv.1608.03983. Saber Malekmohammadi, Yaoliang Yu, and Yang Cao. Noise- aware algorithm for heterogeneous differentially private federated learning. ArXiv preprint:2406.03519, 2024. Brendan McMahan, Eider Moore, Daniel Ramage, Seth Hampson, and Blaise Aguera y Arcas. Communication- efficient learning of deep networks from decentralized data. In Artificial Intelligence and Statistics, pp. 1273- 1282, 2017. H Brendan McMahan, Daniel Ramage, Kunal Talwar, and Li Zhang. Learning differentially private recurrent language models. ArXiv preprint:1710.06963, 2017. Ilya Mironov. Renyi differential privacy. In Computer Security Foundations Symp. (CSF), pp. 263- 275. IEEE, 2017. Ilya Mironov, Kunal Talwar, and Li Zhang. Renyi differential privacy of the sampled gaussian mechanism. ArXiv preprint:1908.10530, 2019. Nicolas Papernot and Thomas Steinke. Hyperparameter tuning with renyi differential privacy. ArXiv preprint:2110.03620, 2021. Venkatadheeraj Pichapati, Ananda Theertha Suresh, Felix X Yu, Sashank J Reddi, and Sanjiv Kumar. Adaclip: Adaptive clipping for private sgd. ArXiv preprint:1908.07643, 2019. Maithra Raghu, Justin Gilmer, Jason Yosinski, and Jascha Sohl- Dickstein. Svcca: Singular vector canonical correlation analysis for deep learning dynamics and interpretability. Advances in Neural Information Processing Systems, 30, 2017. Swaroop Ramaswamy, Om Thakkar, Rajiv Mathews, Galen Andrew, H Brendan McMahan, and Francois Beaufays. Training production language models without memorizing user data. ArXiv preprint:2009.10031, 2020. Xiaoying Shen, Hang Jiang, Yange Chen, Baocang Wang, and Le Gao. Pldp- fl: Federated learning with personalized local differential privacy. Entropy, 25(3):485, 2023. Ravid Shwartz- Ziv and Naftali Tishby. Opening the black box of deep neural networks via information. ArXiv preprint:1703.00810, 2017. Stacey Truex, Nathalie Baracaldo, Ali Anwar, Thomas Steinke, Heiko Ludwig, Rui Zhang, and Yi Zhou. A hybrid approach to privacy- preserving federated learning. In Proceedings of the ACM Workshop on Artificial Intelligence and Security, pp. 1- 11, 2019. Stacey Truex, Ling Liu, Ka- Ho Chow, Mehmet Emre Gursoy, and Wenqi Wei. Ldp- fed: Federated learning with local differential privacy. In Proceedings of the ACM International Workshop on Edge Systems, Analytics and Networking, pp. 61- 66, 2020. Kang Wei, Jun Li, Ming Ding, Chuan Ma, Howard H Yang, Farhad Farokhi, Shi Jin, Tony QS Quek, and H Vincent Poor. Federated learning with differential privacy: Algorithms and performance analysis. IEEE Transactions on Information Forensics and Security, 15:3454- 3469, 2020. Han Xiao, Kashif Rasul, and Roland Vollgraf. Fashion- mnist: a novel image dataset for benchmarking machine learning algorithms. ArXiv preprint:1708.07747, 2017.

Ge Yang, Shaowei Wang, and Haijie Wang. Federated learning with personalized local differential privacy. In IEEE International Conference on Computer and Communication Systems (ICCCS), pp. 484- 489. IEEE, 2021. Xiyuan Yang, Wenke Huang, and Mang Ye. Dynamic personalized federated learning with adaptive differential privacy. Advances in Neural Information Processing Systems, 36:72181- 72192, 2023. Ashkan Yousefpour, Igor Shilov, Alexandre Sablayrolles, Davide Testuggine, Karthik Prasad, Mani Malek, John Nguyen, Sayan Ghosh, Akash Bharadwaj, Jessica Zhao, Graham Cormode, and Ilya Mironov. Opacus: User- friendly differential privacy library in PyTorch. ArXiv preprint:2109.12298, 2021. Daniel Yue Zhang, Ziyi Kou, and Dong Wang. Fairfl: A fair federated learning approach to reducing demographic bias in privacy- sensitive classification models. In 2020 IEEE International Conference on Big Data (Big Data), pp. 1051- 1060, 2020. doi: 10.1109/BigData50022.2020.9378043. Jianqing Zhang, Yang Hua, Hao Wang, Tao Song, Zhengui Xue, Ruhui Ma, and Haibing Guan. Fedala: Adaptive local aggregation for personalized federated learning. In Proceedings of the AAAI Conference on Artificial Intelligence, volume 37, pp. 11237- 11244, 2023. Ligeng Zhu, Zhijian Liu, and Song Han. Deep leakage from gradients. Advances in Neural Information Processing Systems, 32, 2019.

# A APPENDIX

# A.1 SUMMARY OF NOTATIONS AND BENCHMARKING SCHEMES

We summarize the important notations (including the hyperparameters shown in Table 1) in Table 3. We formally depict the baselines: FedAvg (McMahan et al., 2017) as Alg. 3, DP- FedAvg as Alg. 4, IDP- FedAvg as Alg. 5, and adaptive clipping (Andrew et al., 2021) as Alg. 6. Next, we provide further details to explain the adaptive clipping baseline in comparison with our proposed approach.

Table 3: A Summary of Notation and Hyperparameters  

<table><tr><td>N</td><td>No. of Clients</td><td>Dn</td><td>Dataset of Client n</td></tr><tr><td>Ct</td><td>Client set in R. t</td><td>Bi</td><td>Batch n</td></tr><tr><td>T</td><td>No. of rounds</td><td>B</td><td>Batch size</td></tr><tr><td>L</td><td>No. of local iterations</td><td>εn</td><td>DP privacy budget of Client n</td></tr><tr><td>θt</td><td>Global model at R. t</td><td>εtdrp,n</td><td>RDP privacy spent of Client n in R. t</td></tr><tr><td>Δθt</td><td>Global model update at R. t</td><td>εtdrp-left,n</td><td>RDP budget RE. of Client n for R. t onwards</td></tr><tr><td>θt,l</td><td>Model of Client n at R. t, I. l</td><td>εtdrp,n</td><td>RDP privacy spend of Client n up to R. t + 1</td></tr><tr><td>Δθt,n</td><td>Model update of Client n at R. t</td><td>εn</td><td>DP budget RE. of Client n for R. t onwards</td></tr><tr><td>Δθt,n</td><td>Perturbed update of Client n at R. t</td><td>σt</td><td>Global noise multiplier in R. t</td></tr><tr><td>Errort</td><td>Δθt- Δθt</td><td>σt,n</td><td>Noise multiplier of Client n in R. t</td></tr><tr><td>λ</td><td>Learning rate</td><td>c</td><td>Average clipping norm</td></tr><tr><td>α</td><td>Renyi order in RDP</td><td>ctn</td><td>Clipping norm of Client n in R. t</td></tr><tr><td>δ</td><td>Probability of violating in DP</td><td>q</td><td>Spending-based sampling rate</td></tr><tr><td>Tn</td><td>Saving-to-spending transition R.</td><td>qn</td><td>Saving-based sampling rate of Client n</td></tr><tr><td>Mt</td><td>Saving-or-spending mode</td><td>qt</td><td>Sampling rate of Client n in R. t</td></tr><tr><td>Ln</td><td>Loss function of Client n</td><td>q</td><td>Average sampling rate in R. t</td></tr></table>

1 Table's abbreviations: No. for Number, RE. for Remaining, I. for Iteration, and R. for Round".

# Algorithm 3 Federated Averaging (Fe dAvg) (McMahan et al., 2017)

Inputs: No. clients  $N$  No. global rounds  $T$  No. local iterations  $L$  loss functions  $\Delta \theta_{n}$  local datasets  $\mathcal{D}_n$  learning rate  $\lambda$  batch size  $B$

1: Initialize global model  $\theta^0$  2: for each global round  $t\in [T]$  do 3:  $C^t\gets$  Sample clients in probability  $q$  4: for each client  $n\in C^t$  in parallel do 5:  $\Delta \theta_{n}^{t} = \text{ClientUpdate} (t,n,\theta^{t - 1})$  6: end for 7: Aggregate  $\begin{array}{r}\Delta \theta^t = \sum_{n\in C^t}\bar{\Delta}\theta_n^t \end{array}$  8: Update  $\begin{array}{r}\theta^t = \theta^{t - 1} + \frac{\Delta\theta_t^t}{qN} \end{array}$  9: end for

Def ClientUpdate  $(t,n,\theta^t)$

1: Initialize local model  $\theta_{n}^{t,0} = \theta^{t}$  2: for local iteration  $l\in [l]$  do 3:  $\{\mathcal{B}_i\}_{i = 1}^{[D_n] / B}\gets \mathrm{Split}\mathcal{D}_n$  to size  $B$  batches 4: for each batch  $\mathcal{B}_i$  do 5:  $\begin{array}{r}\theta_n^{t,0} = \theta_n^{t,l - 1} - \frac{\lambda\sum_{(\mathbf{x},\mathbf{y})\in\mathcal{B}_i}\nabla\mathcal{L}_n(\theta_n^{t,l - 1};(\mathbf{x},\mathbf{y}))}{B} \end{array}$  6: end for 7: end for 8: Return  $\Delta \theta_{n}^{t} = \theta_{n}^{t,L} - \theta_{n}^{t,0}$

# Algorithm 4 Differential Private Federated Averaging (DP-FedAvg) (McMahan et al., 2017)

Inputs: No. clients  $N$  No. global rounds  $T$  No. local iterations  $L$  noise multiplier  $\sigma$  clip norm c, sampling rate  $q$  loss functions  $\mathcal{L}_n$  local datasets  $\mathcal{D}_n$  learning rate  $\lambda$  batch size  $B$

1: Initialize global model  $\theta^0$  2: for each global round  $t\in [T]$  do 3:  $C^t\gets$  Sample clients with probability  $q$  4: for each client  $n\in C^t$  in parallel do 5:  $\begin{array}{r}\Delta \theta_n^t = \text{ClientUpdate} (t,n,\theta^{t - 1},q) \end{array}$  6: end for 7: Aggregate  $\begin{array}{r}\bar{\Delta\theta}^t = \sum_{n\in C^t}\bar{\Delta\theta}_n^t \end{array}$  8: Add noise  $\begin{array}{r}\bar{\Delta\theta}^t\gets \bar{\Delta\theta}^t +\mathcal{N}(0,c^2\sigma^2\mathbb{I}) \end{array}$  9: Update  $\begin{array}{r}\theta^t = \theta^{t - 1} + \frac{\bar{\Delta\theta}^t}{qN} \end{array}$  10: end for

Def ClientUpdate  $(t,n,\theta^t,c)$

1: Initialize local model  $\theta_{n}^{t,0} = \theta^{t}$  2: for local iteration  $l\in [l]$  do 3:  $\{\mathcal{B}_i\}_{i = 1}^{|\mathcal{D}_n| / B}\gets \mathrm{Split}\mathcal{D}_n$  to size  $B$  batches 4: for each batch  $\mathcal{B}_i$  do 5:  $\begin{array}{r}\theta_n^{t,l} = \theta_n^{t,l - 1} - \frac{\lambda\sum_{(\mathbf{x},\mathbf{y})\in\mathcal{B}_i}\nabla\mathcal{L}_n(\theta_n^{t,l - 1};(\mathbf{x},\mathbf{y}))}{B} \end{array}$  6: end for 7: end for 8: Compute  $\Delta \theta_{n}^{t} = \theta_{n}^{t,L} - \theta_{n}^{t,0}$  9: Clip  $\begin{array}{r}\Delta \theta_n^t = \Delta \theta_n^t\min \left(1,\frac{c}{\|\Delta\theta_n^t\|_2}\right) \end{array}$  10: Return  $\Delta \theta_{n}^{t}$

The Adapting Clipping Baseline. In this paper, we consider the adaptive clipping method (Andrew et al., 2021) as a baseline for our time- adaptive DP- FL approach. In the extended simulations (cf. App. A.8), we benchmark that method against our approach. The method is formally presented as Alg. 6. As shown in Line 13, the server dynamically adjusts the clipping norm based on a specified quantile  $\gamma$  of the distribution of clients' updates. The goal of this method is to minimize the difference between the clipping norm and the quantile in the distribution, aiming to achieve the same objective as ours: improving the privacy- utility tradeoff. In contrast to our time- adaptive approach, which is independent of the client's data and can be done prior to training, the method (Andrew et al., 2021) introduces privacy risks during the quantile approximation. To mitigate these risks, and as is shown in Line 2 of the SetClipping function in Alg. 6, the method (Andrew et al., 2021) incorporates a supplementary DP mechanism that allocates part of the privacy budget to preserve privacy during quantile estimation. However, this results in a lower remaining privacy budget, requiring a larger noise multiplier  $\sigma$ , as computed in Line 2 of SetSigma in Alg.6, in comparison to our approach.

# Algorithm 5 Individualized DP-FedAvg (IDPFedAvg), a natural integration of IDP (Boenisch et al., 2024) to FL

Inputs: No. clients  $N$  No. global rounds  $T$  No. local iterations  $L$  local privacy budgets  $\epsilon_{n}$  average clip norm  $c$  sampling rate  $q$  loss functions  $\mathcal{L}_n$  local datasets  $\mathcal{D}_n$  learning rate  $\lambda$  batch size  $B$  probability of violating  $\epsilon$

1:  $\sigma ,\{\epsilon_{n}\}_{n\in [N]} = \text{SetPrivacyParams}\left(c,q,\{\epsilon_{n}\}_{n\in [N]},T,\delta\right)$  2: Initialize global model  $\theta^0$  3: for each global round  $t\in [T]$  do 4:  $\alpha_{t}^{t}$  calculate policies with probability  $q$  5: for each client  $n\in \mathcal{C}^t$  in parallel do 6:  $\Delta \theta_{n}^{t} = \text{ClientUpdate}\left(t,n,\theta^{t - 1},c_{n}\right)$  7: end for 8: Aggregate  $\begin{array}{r}\bar{\Delta}\theta^{t} = \sum_{n\in \mathcal{C}^{t}}\bar{\Delta}\theta_{n}^{t} \end{array}$  9: Add noise  $\begin{array}{r}\bar{\Delta}\theta^{t}\leftarrow \bar{\Delta}\theta^{t} + \mathcal{N}(0,c^{2}\sigma^{2}\mathbb{I}) \end{array}$  10: Update  $\begin{array}{r}\theta^{t} = \theta^{t - 1} + \frac{\bar{\Delta}\theta^{t}}{qN} \end{array}$

11: end for

# Def ClientUpdate  $\left(t,n,\theta^t,c_n\right)$

1: Initialize local model  $\theta_{n}^{0,0} = \theta^{t}$  2: for local iteration  $l\in [l]$  do 3:  $\{\mathcal{B}_i\}_{i = 1}^{|\mathcal{D}_n| / B}\leftarrow \mathrm{Split}\mathcal{D}_n$  to size  $B$  batches 4: for each batch  $\mathcal{E}_i$  do 5:  $\theta_{n}^{t,l} = \theta_{n}^{t,l - 1} - \frac{\lambda\sum_{(x,y)\in B_{i}}\nabla\mathcal{L}_{n}\nabla\mathcal{L}_{n}^{t,l - 1};(\mathbf{x},y)}{B}$  6: end for 7: end for 8: Compute  $\Delta \theta_{n}^{t} = \theta_{n}^{t,L} - \theta_{n}^{t,0}$  9: Clip  $\bar{\Delta}\theta_{n}^{t} = \Delta \theta_{n}^{t}\min \left(1,\frac{c_{n}}{\|\bar{\Delta}\theta_{n}^{t}\|_{2}}\right)$  10: Return  $\bar{\Delta}\theta_{n}^{t}$

# Def SetPrivacyParams  $(c,q,\{\epsilon_n\}_{n\in [N]},T,\delta)$

1: for each client  $n\in [N]$  do 2: Set local noise multiplier  $\sigma_{n} = \text{GetNoise} (\epsilon_{n},\delta ,q,T)$  3: end for 4: Compute  $\begin{array}{r}\sigma_{n}\leftarrow \left(\frac{1}{N}\sum_{n\in [N]}\frac{1}{\sigma_{n}}\right)^{- 1} \end{array}$  5: for each client  $n\in [N]$  do 6: Set local clip norm  $\begin{array}{r}c_{n} = \frac{\sigma\sigma}{\sigma_{n}} \end{array}$  7: end for 8: Return  $\sigma ,\{\epsilon_{n}\}_{n\in [N]}$

# Algorithm 6 DP-FedAvg-M with Adaptive Clipping (Andrew et al., 2021)

Inputs: No. clients  $N$  No. global rounds  $T$  No. local iterations  $L$  noise multiplier  $\sigma$  clip norm  $c$  sampling rate  $q$  loss functions  $\{\mathcal{L}_n\}_{n\in [N]}$  local datasets  $\{\mathcal{D}_n\}_{n\in [N]}$  client side learning rate  $\lambda$  server side learning rate  $\lambda_{\mathrm{b}}$  client learning rate  $\lambda_{\mathrm{b}}$ $\gamma$  quantile, batch size  $B$  probability of violating  $\delta$

1:  $\sigma = \text{SetSigma}\left(q,\epsilon ,T,\delta ,\sigma_{\mathrm{b}}\right)$  2: Initialize global model  $\theta^0$  3: for each global round  $t\in [T]$  do 4: for each sample  $n\in \mathcal{C}^t$  in uniformly. 5:  $\mathcal{L}^t$  for each client  $q\in \mathcal{E}^t$  parallel do 6:  $\left(b_{n}^{t},\Delta \theta_{n}^{t}\right) = \text{ClientUpdate}\left(t,n,\theta^{t - 1},c^{t}\right)$  7: end for 8: Aggregate  $\begin{array}{r}\bar{\Delta}\theta^{t} = \sum_{n\in \mathcal{C}^{t}}\bar{\Delta}\theta_{n}^{t} \end{array}$  9: Add noise  $\begin{array}{r}\bar{\Delta}\theta^{t}\leftarrow \bar{\Delta}\theta^{t} + \mathcal{\Lambda}(0,(c^{t})^{2}\sigma^{2}\mathbb{I}) \end{array}$  10: Average  $\begin{array}{r}\bar{\Delta}\theta^{t}\leftarrow \frac{1}{qN}\bar{\Delta}\theta^{t} \end{array}$  11: Compute  $\begin{array}{r}\bar{\Delta}\theta^{t}\leftarrow \bar{\Delta}\theta^{t - 1} + (1 - \bar{\Delta}_{\mathrm{b}})\bar{\Delta}\theta^{t} \end{array}$  12: Update  $\begin{array}{r}\theta^{t} = \theta^{t - 1} + \lambda_{\mathrm{b}}\lambda_{\mathrm{b}}\sigma_{t} \end{array}$  13:  $\begin{array}{r}c^{t + 1} = \text{SetClippig}\left(\{\bar{b}_{n}^{t}\}_{n\in \mathcal{C}^{t}},\sigma_{\mathfrak{b}},\theta_{\mathfrak{b}},\gamma ,\lambda_{\mathfrak{b}},c^{t}\right) \end{array}$

14: end for

# Def ClientUpdate  $\left(t,n,\theta^{t},c^{t}\right)$

1: Initialize local model  $\theta_{n}^{t,0} = \theta^{t}$  2: for local iteration  $l\in [l]$  do 3:  $\{\mathcal{B}_i\}_{i = 1}^{|\mathcal{D}_n| / B}\leftarrow \mathrm{Split}\mathcal{D}_n$  to size  $B$  batches 4: for each batch  $B_{i}$  do 5:  $\theta_{n}^{t,l} = \theta_{n}^{t,l - 1} - \frac{\lambda\sum_{(\mathbf{x},\mathbf{y})\in B_{i}}\nabla\mathcal{L}_{n}\left(\theta_{n}^{t,l - 1};(\mathbf{x},\mathbf{y})\right)}{B}$  6: end for 7: end for 8: Compute  $\Delta \theta_{n}^{t} = \theta_{n}^{t,L} - \theta_{n}^{t,0}$  9: Compute  $b = \mathbb{I}\| \Delta \theta_{n}^{t}\|_{\mathbb{N}}\leq c^{t}$  10: Clip  $\bar{\Delta}\theta_{n}^{t} = \Delta \theta_{n}^{t}\min \left(1,\frac{c_{n}}{\|\bar{\Delta}\theta_{n}^{t}\|_{2}}\right)$  11: Return  $b,\bar{\Delta}\theta_{n}^{t}$

Def SetSigma  $(q,\epsilon ,T,\delta ,\sigma_{\mathrm{b}})$  1:  $\bar{\sigma} = \text{GetNoise} (\epsilon ,\delta ,q,T)$  2:  $\sigma = \left(\frac{1}{\sigma^{2}} - \frac{1}{(2\sigma_{\mathrm{b}})^{2}}\right)^{- 1 / 2}$  3: Return  $\sigma$

# Def SetClippig  $\left(\{b_{n}^{t}\}_{n\in \mathcal{C}^{t}},\sigma_{\mathrm{b}},q,\gamma ,\lambda_{\mathrm{b}},c^{t}\right)$

1: Aggregate  $\begin{array}{r}\bar{b}^{t} = \sum_{n\in \mathcal{C}^{t}}b_{n}^{t} \end{array}$  2: Add noise  $\begin{array}{r}\bar{b}^{t}\leftarrow \bar{b}^{t} + \mathcal{N}(0,\sigma_{\mathrm{b}}^{2}\mathbb{I}) \end{array}$  3: Average  $\begin{array}{r}\bar{b}^{t}\leftarrow \frac{1}{qN}\bar{b}^{t} \end{array}$  4: Update  $c^{t + 1} = c^{t}\exp \left(- \lambda_{\mathrm{b}}(\bar{b}^{t} - \gamma)\right)$  5: Return  $c^{t + 1}$

# A.2 PROOF OF LEMMA 1

We use induction to solve the recursive formula (1). According to (1), when  $t = 1< T_n$ $\epsilon_{\mathrm{rdp},n}^{1} =$ $\frac{\epsilon_{\mathrm{rdp},n}(q_n)^2}{T(q)^2}$  and when  $t = 2< T_n$  client  $n$  spends  $\begin{array}{r}\epsilon_{\mathrm{rdp},n}^{2} = \frac{\epsilon_{\mathrm{rdp},n} - \epsilon_{\mathrm{rdp},n}^{1}}{T - 1}\left(\frac{q_n}{q}\right)^2 \end{array}$  By substituting  $\epsilon_{\mathrm{rdp},n}^{1}$  in  $\epsilon_{\mathrm{rdp},n}^{2}$  we obtain  $\begin{array}{r}\epsilon_{\mathrm{rdp},n}^{2} = \frac{\epsilon_{\mathrm{rdp},n}}{T - 1}\left(1 - \frac{1}{T}\left(\frac{q_n}{q}\right)^2\right)\left(\frac{q_n}{q}\right)^2 \end{array}$  . We now assume  $\epsilon_{\mathrm{rdp},n}^{t - 1}$  satisfies in (2) for every  $2\leq t< T$  . If  $t< T_n$  , by substituting  $\epsilon_{\mathrm{rdp},n}^{t - 1}$  in (1), we obtain

$$
\begin{array}{r l} & {\epsilon_{\mathrm{rdp},n}^{t} = \left(\frac{\epsilon_{\mathrm{rdp},n} - \sum_{\tau = 1}^{t - 1}\epsilon_{\mathrm{rdp},n}^{\tau}}{T - t + 1}\right)\left(\frac{q_{n}}{q}\right)^{2} = \left(\frac{\epsilon_{\mathrm{rdp},n}^{t - 1}(T - t + 2)\left(\frac{q}{q_{n}}\right)^{2} - \epsilon_{\mathrm{rdp},n}^{t - 1}}{T - t + 1}\right)\left(\frac{q_{n}}{q}\right)^{2}}\\ & {\qquad = \epsilon_{\mathrm{rdp},n}^{t - 1}\frac{\left(T - t + 2 - \left(\frac{q_{n}}{q}\right)^{2}\right)}{T - t + 1}}\\ & {\qquad = \frac{\epsilon_{\mathrm{rdp},n}}{T - t + 2}\left(\frac{q_{n}}{q}\right)^{2}\left(\prod_{i = 1}^{t - 2}\left(1 - \frac{1}{T - t + 2 + i}\left(\frac{q_{n}}{q}\right)^{2}\right)\right)\frac{\left(T - t + 2 - \left(\frac{q_{n}}{q}\right)^{2}\right)}{T - t + 1}}\\ & {\qquad = \frac{\epsilon_{\mathrm{rdp},n}}{T - t + 1}\left(\frac{q_{n}}{q}\right)^{2}\prod_{i = 1}^{t - 1}\left(1 - \frac{1}{T - t + 1 + i}\left(\frac{q_{n}}{q}\right)^{2}\right).} \end{array} \tag{12}
$$

If  $t = T_n$  by substituting  $\epsilon_{\mathrm{rdp},n}^{t - 1}$  in (1), we obtain

$$
\begin{array}{r l} & {\epsilon_{\mathrm{rdp},n}^{T_{n}} = \left(\frac{\epsilon_{\mathrm{rdp},n} - \sum_{\tau = 1}^{T_{n} - 1}\epsilon_{\mathrm{rdp},n}^{\tau}}{T - T_{n} + 1}\right) = \left(\frac{\epsilon_{\mathrm{rdp},n}^{T_{n} - 1}(T - T_{n} + 2)\left(\frac{q}{q_{n}}\right)^{2} - \epsilon_{\mathrm{rdp},n}^{T_{n} - 1}}{T - T_{n} + 1}\right)}\\ & {\qquad = \epsilon_{\mathrm{rdp},n}^{T_{n} - 1}\frac{\left(T - T_{n} + 2 - \left(\frac{q_{n}}{q}\right)^{2}\right)}{T - T_{n} + 1}\left(\frac{q}{q_{n}}\right)^{2}}\\ & {\qquad = \frac{\epsilon_{\mathrm{rdp},n}}{T - T_{n} + 2}\left(\prod_{i = 1}^{T_{n} - 2}\left(1 - \frac{1}{T - T_{n} + 2 + i}\left(\frac{q_{n}}{q}\right)^{2}\right)\right)\frac{\left(T - T_{n} + 2 - \left(\frac{q_{n}}{q}\right)^{2}\right)}{T - T_{n} + 1}}\\ & {\qquad = \frac{\epsilon_{\mathrm{rdp},n}}{T - T_{n} + 1}\prod_{i = 1}^{T_{n} - 1}\left(1 - \frac{1}{T - T_{n} + 1 + i}\left(\frac{q_{n}}{q}\right)^{2}\right).} \end{array} \tag{16}
$$

If  $t > T_n$  by substituting  $\epsilon_{\mathrm{rdp},n}^{t - 1}$  in (1), we obtain

$$
\begin{array}{r l} & {\epsilon_{\mathrm{rdp},n}^{t} = \left(\frac{\epsilon_{\mathrm{rdp},n} - \sum_{\tau = 1}^{t - 1}\epsilon_{\mathrm{rdp},n}^{\tau}}{T - t + 1}\right) = \left(\frac{\epsilon_{\mathrm{rdp},n}^{t - 1}(T - t + 2) - \epsilon_{\mathrm{rdp},n}^{t - 1}}{T - t + 1}\right)}\\ & {\qquad = \epsilon_{\mathrm{rdp},n}^{t - 1} = \frac{\epsilon_{\mathrm{rdp},n}}{T - T_{n} + 1}\prod_{i = 1}^{T_{n} - 1}\left(1 - \frac{1}{T - T_{n} + 1 + i}\left(\frac{q_{n}}{q}\right)^{2}\right).} \end{array} \tag{18}
$$

We use the explicit solutions of the recursive formula (1), presented in Lem. 1, to prove this theorem. When  $t < T_n$ ,

$$
\begin{array}{rl} & {\epsilon_{\mathrm{rd},n}^{t - 1} - \epsilon_{\mathrm{rd},n}^{t - 1} = \frac{\epsilon_{\mathrm{rd},n}}{T - t + 1}\left(\frac{q_n}{q}\right)^2\prod_{i = 1}^{t - 1}\left(1 - \frac{1}{T - t + 1 + i}\left(\frac{q_n}{q}\right)^2\right)}\\ & {\qquad -\frac{\epsilon_{\mathrm{rd},n}}{T - t + 2}\left(\frac{q_n}{q}\right)^2\prod_{i = 1}^{t - 2}\left(1 - \frac{1}{T - t + 2 + i}\left(\frac{q_n}{q}\right)^2\right)}\\ & {\qquad = \epsilon_{\mathrm{rd},n}\left(\frac{q_n}{q}\right)^2\left(\prod_{i = 1}^{t - 2}\left(1 - \frac{1}{T - t + 2 + i}\left(\frac{q_n}{q}\right)^2\right)\right)}\\ & {\qquad \times \left(\frac{1}{T - t + 1}\left(1 - \frac{1}{T - t + 2}\left(\frac{q_n}{q}\right)^2\right) - \frac{1}{T - t + 2}\right)}\\ & {\qquad = \epsilon_{\mathrm{rd},n}\left(\frac{q_n}{q}\right)^2\left(\prod_{i = 1}^{t - 2}\left(1 - \frac{1}{T - t + 2 + i}\left(\frac{q_n}{q}\right)^2\right)\right)\frac{1 - \left(\frac{q_n}{q}\right)^2}{(T - t + 1)(T - t + 2)}.} \end{array} \tag{19}
$$

The right- hand side of (23) is larger than equal to zero because  $q_n \leq q$ . Therefore, in this case  $\epsilon_{\mathrm{rd},n}^t \geq \epsilon_{\mathrm{rd},n}^{t - 1}$ . When  $t = T_n$ ,

$$
\begin{array}{rl} & {\epsilon_{\mathrm{rd},n}^{T_n} - \epsilon_{\mathrm{rd},n}^{T_n - 1} = \frac{\epsilon_{\mathrm{rd},n}}{T - T_n + 1}\prod_{i = 1}^{T_n - 1}\left(1 - \frac{1}{T - T_n + 1 + i}\left(\frac{q_n}{q}\right)^2\right)}\\ & {\qquad -\frac{\epsilon_{\mathrm{rd},n}}{T - T_n + 2}\left(\frac{q_n}{q}\right)^2\prod_{i = 1}^{T - 2}\left(1 - \frac{1}{T - T_n + 2 + i}\left(\frac{q_n}{q}\right)^2\right)}\\ & {\qquad = \epsilon_{\mathrm{rd},n}\left(\prod_{i = 1}^{T_n - 2}\left(1 - \frac{1}{T - T_n + 2 + i}\left(\frac{q_n}{q}\right)^2\right)\right)}\\ & {\qquad \times \left(\frac{1}{T - t + 1}\left(1 - \frac{1}{T - T_n + 2}\left(\frac{q_n}{q}\right)^2\right) - \frac{\left(\frac{q_n}{q}\right)^2}{T - T_n + 2}\right)}\\ & {\qquad = \epsilon_{\mathrm{rd},n}\left(\prod_{i = 1}^{T_n - 2}\left(1 - \frac{1}{T - T_n + 2 + i}\left(\frac{q_n}{q}\right)^2\right)\right)\frac{1 - \left(\frac{q_n}{q}\right)^2}{(T - T_n + 1)}.} \end{array} \tag{28}
$$

The right- hand side of (28) is again larger than equal to zero because. Therefore, in this case we also have  $\epsilon_{\mathrm{rd},n}^{T_n} \geq \epsilon_{\mathrm{rd},n}^{T_n - 1}$ . Lem. 1 also shows  $\epsilon_{\mathrm{rd},n}^t = \epsilon_{\mathrm{rd},n}^{t - 1}$  when  $t > T_n$ .

If the expectation is taken w.r.t. (i) the randomness of local datasets, (ii) the sampling of clients, and (iii) the randomness of injected Gaussian noise, then the bias is simplified as follows:

$$
\begin{array}{rl}&{|\mathbb{E}_{(i),(ii),(iii)}(\mathbb{E}_{(i),(ii),(iii)}(\mathbb{E}_{(i),(ii),(iii)}(\Delta\theta_{n}^{t}-\frac{1}{q^{t}}(\mathbb{C}_{n}^{t}(\mathbb{C}_{n}^{t}(\Delta\theta_{n}^{t},c_{n}^{t})+\mathbb{C}_{n}^{t})+(1-\mathbb{C}_{n}^{t})\mathbb{Z}_{n}^{t}))|}\\&{=\frac{1}{N}|\sum_{n=1}^{N}\mathbb{E}_{(i),(ii),(iii)}(\Delta\theta_{n}^{t}-\frac{1}{q^{t}}\mathbb{C}_{n}^{t}(\mathbb{C}_{n}^{t}(\mathbb{C}_{n}^{t}(\Delta\theta_{n}^{t},c_{n}^{t})+\mathbb{C}_{n}^{t})+(1-\mathbb{C}_{n}^{t})\mathbb{Z}_{n}^{t}))|}\\&{=\frac{1}{\sqrt{N}}|\sum_{n=1}^{N}\mathbb{E}_{(i),(ii)}(\Delta\theta_{n}^{t}-\frac{1}{q^{t}}\mathbb{C}_{n}^{t}(\mathbb{C}_{n}^{t}(\mathbb{C}_{n}^{t}(\Delta\theta_{n}^{t},c_{n}^{t})))|}\\&{=\frac{1}{N}|\sum_{n=1}^{N}\mathbb{E}_{(i)}(\Delta\theta_{n}^{t}-\frac{q_{n}^{t}}{q^{t}}(\mathbb{C}_{n}^{t}(\mathbb{C}_{n}^{t}(\Delta\theta_{n}^{t},c_{n}^{t})))|}\\&{=\frac{1}{N}|\sum_{n=1}^{N}\mathbb{E}_{(i)}(\Delta\theta_{n}^{t}(\frac{q^{t}}{q^{t}}-\frac{q_{n}^{t}}{q^{t}}+\frac{q_{n}^{t}}{q^{t}})-\frac{q_{n}^{t}}{q^{t}}(\mathbb{C}_{n}^{t}(\mathbb{C}_{n}^{t}(\Delta\theta_{n}^{t},c_{n}^{t})))|}\\&{=\frac{1}{N}|\sum_{n=1}^{N}((\frac{q^{t}}{q^{t}}-\frac{q_{n}^{t}}{q^{t}})\mathbb{E}_{(i)}(\Delta\theta_{n}^{t})+\frac{q_{n}^{t}}{q^{t}}\mathbb{E}_{(i)}(\Delta\theta_{n}^{t}-(\mathbb{C}_{n}^{t}(\mathbb{C}_{n}^{t}(\Delta\theta_{n}^{t},c_{n}^{t}))))|}\\&{\leq\frac{1}{N}|\sum_{n=1}^{N}(\frac{q^{t}}{q^{t}}-\frac{q_{n}^{t}}{q^{t}})\mathbb{E}_{(i)}(\Delta\theta_{n}^{t})|+\frac{1}{N}\sum_{n=1}^{N}|\frac{q_{n}^{t}}{q^{t}}\mathbb{E}_{(i)}(\Delta\theta_{n}^{t}-(\mathbb{C}_{n}^{t}(\mathbb{C}_{n}^{t}(\Delta\theta_{n}^{t},c_{n}^{t})))|}\\&{\leq\frac{1}{N}|\sum_{n=1}^{N}(\frac{q^{t}}{q^{t}}-\frac{q_{n}^{t}}{q^{t}})\mathbb{E}_{(i)}(\Delta\theta_{n}^{t})|+\frac{1}{N}\sum_{n\geq 1}^{N}\frac{q_{n}^{t}}{q^{t}}\frac{\mathbb{E}_{(i)}(\|\Delta\theta_{n}^{t}\|^{p})}{(c_{n}^{t})^{p-1}}.}\end{array} \tag{34}
$$

The equality (29) is due to  $\mathbb{E}_{(iii)}\left(z_n^t\right) = \mathbb{E}_{(iii)}\left(\tilde{z}_n^t\right) = 0$ . The equality (30) is due to  $\mathbb{E}_{(ii)}\left(\mathbf{b}_n^t\right) = q_n^t$ . The inequality (33) is due to triangle inequality. The inequality (34) is due to the clipping bias lemma (Das et al., 2023), given any  $\rho > 1$ .

# A.5 PROOF OF THEOREM 3

If the expectation is taken w.r.t. (i) the randomness of local datasets and (ii) the sampling of clients, (iii) the randomness of injected Gaussian noise, and (iv) privacy budget assignment randomness, then the bias is simplified as follows:

$$
\begin{array}{rl}&{|\mathbb{E}_{(i),(ii),(iii),(iv)}(\mathbb{E}_{(i),(ii),(iv)}(\mathbb{E}_{(i),(ii),(iv)}(\Delta\theta_{n}^{t}-\frac{1}{q^{t}}(\mathbb{E}_{(i),(ii),(iv)}(\Delta\theta_{n}^{t}(\mathbb{E}_{(i),(ii),(iv)}(\Delta\theta_{n}^{t}(\mathbb{E}_{(i),(ii),(iv)}(\Delta\theta_{n}^{t}(\mathbb{E}_{(i),(ii),(iv)}(\Delta\theta_{n}^{t}(\mathbb{E}_{(i),(ii),(iv)}(\Delta\theta_{n}^{t}-\frac{1}{q^{t}}(\mathbb{E}_{(i),(ii),(iv)}(\Delta\theta_{n}^{t}(\mathbb{E}_{(i),(ii),(iv)}(\Delta\theta_{n}^{t}(\mathbb{E}_{(i),(ii),(iv)}(\Delta\theta_{n}^{t}(\mathbb{E}_{(i),(ii),(iv)}(\Delta\theta_{n}^{t}(\mathbb{E}_{(i),(ii),(iv)}(\Delta\theta_{n}^{t}(\mathbb{E}_{(i),(ii),(iv)}(\Delta\theta_{n}^{t}(\mathbb{E}_{(i),(ii),(iv)})))))))}\\&{=\frac{1}{N}|\sum_{n=1}^{N}\mathbb{E}_{(i),(ii),(iv)}(\Delta\theta_{n}^{t}-\frac{1}{q^{t}}\mathbb{E}_{(i),(ii),(iv)}(\Delta\theta_{n}^{t}(\mathbb{E}_{(i),(ii),(iv)}(\Delta\theta_{n}^{t}(\mathbb{E}_{(i),(ii),(iv)}(\Delta\theta_{n}^{t}(\mathbb{E}_{(i),(ii),(iv)}(\Delta\theta_{n}^{t}+(\mathbb{E}_{(i),(ii),(iv)}(\Delta\theta_{n}^{t}(\mathbb{E}_{(i),(ii),(iv)}(\Delta\theta_{n}^{t}(\mathbb{E}_{(i),(ii),(iv)}(\Delta\theta_{n}^{t}(\mathbb{E}_{(i),(ii),(iv)}(\Delta\theta_{n}^{t}+\mathbb{E}_{(i),(ii),(iv)}(\Delta\theta_{n}^{t}(\mathbb{E}_{(i),(ii),(iv)}(\Delta\theta_{n}^{t}(\mathbb{E}_{(i),(ii),(iv)}(\Delta\theta_{n}^{t}(\mathbb{E}_{(i),(ii),(iv)}(\Delta\theta_{n}^{t}+(\mathbb{E}_{(i),(ii),(iv)}(\Delta\theta_{n}^{t}(\mathbb{E}_{(i),(ii),(iv)}(\Delta\theta_{n}^{t}(\mathbb{E}_{(i),(ii),(iv)}(\Delta\theta_{n}^{t}(\mathbb{E}_{(i),(ii),(iv)}(\Delta\theta_{n}^{t}))))))))}\\&{=\frac{1}{N}|\sum_{n=1}^{N}\mathbb{E}_{(i),(ii),(iv)}(\Delta\theta_{n}^{t}-\frac{1}{q^{t}}\mathbb{E}_{(i),(ii),(iv)}(\Delta\theta_{n}^{t}(\mathbb{e}_{(i),(ii),(iv)}(\Delta\theta_{n}^{t}(\mathbb{E}_{(i),(ii),(iv)}(\Delta\theta_{n}^{t}(\mathbb{E}_{(i),(ii),(iv)}(\Delta\theta_{n}^{t}(\mathbb{E}_{(i),(ii),(iv)}(\Delta\theta_{n}^{t}(\mathbb{e}_{(i),(ii),(iv)}(\Delta\theta_{n}^{t}(\mathbb{E}_{(i),(ii),(iv)}(\Delta\theta_{n}^{t}(\mathbb{E}_{(i),(ii),(iv)}(\Delta\theta_{n}^{t}+\mathbb{E}_{(i),(ii),(iv)}(\Delta\theta_{n}^{t}(\mathbb{e}_{(i),(ii),(iv)}(\Delta\theta_{n}^{t}(\mathbb{E}_{(i),(ii),(iv)}(\Delta\theta_{n}^{t}(\mathbb{E}_{(i),(ii),(iv)}(\Delta\theta_{n}^{t}+(\mathbb{E}_{(i),(ii),(iv)}(\Delta\theta_{n}^{t}(\mathbb{e}_{(i),(ii),(iv)}(\Delta\theta_{n}^{t}(\mathbb{E}_{(i),(ii),(iv)}(\Delta\theta_{n}^{t}(\mathbb{E}_{(i),(ii),(iv)}(\Delta\theta_{n}^{t}+(\mathbb{E}_{(i),(ii),(iv)}(\Delta\theta_{n}^{t}(\mathbb{\Delta}\theta_{(i),(ii),(iv)}(\Delta\theta_{n}^{t}(\mathbb{E}_{(i),(ii),(iv)}(\Delta\theta_{n}^{t}(\mathbb{E}_{(i),(ii),(iv)}(\Delta\theta_{n}^{t}(\mathbb{E}_{(i),(ii),(iv)}(\Delta\theta_{n}^{t}(\mathbb{E_{(i),(ii),(iv)}(\Delta\theta_{n}^{t}(\mathbb{E_{(i),(ii),(iv)}(\Delta\theta_{n}^{t}(\mathbb{E_{(i),(ii),(iv)}(\Delta\theta_{n}^{t}(\mathbb{E_{(i),(ii),(iv)}(\Delta\theta_{n}^{t}(\mathbb{E}_{(i),(ii),(iv)}(\Delta\theta_{n}^{t}(\mathbb{E_{(i),(ii),(iv)}(\Delta\theta_{n}^{t}(\mathbb{E_{(i),(ii),(iv)}(\Delta\theta_{n}^{t}(\mathbb{E}_{(i),(ii),(iv)}(\Delta\theta_{n}^{t}(\mathbb{E}_{(i),(ii),(iv)}(\Delta\theta_{n}^{t}(\mathbb{E_{(i),(ii),(iv)}(\Delta\theta_{n}^{t}(\mathbb{E}_{(i),(ii),(iv)}(\Delta\theta_{n}^{t}(\mathbb{E_{(i),(ii),(iv)}(\Delta\theta_{n}^{t}(\mathbb{E}_{(i),(ii),(iv)}(\Delta\theta_{n}^{t}(\mathbb{E}_{(i),(ii),(iv)}(\Delta\theta_{n}^{t}(\mathbb{E}_{(i),(ii),(iv)}(\Delta\theta_{n}^{t}(\mathbb{e_{(i),(ii),(iv)}(\Delta\theta_{n}^{t}(\mathbb{E_{(i),(ii),(iv)}(\Delta\theta_{n}^{t}(\mathbb{E_{(i),(ii),(iv)}(\Delta\theta_{n}^{t}(\mathbb{E_{(i),(ii),(iv)}(\Delta\theta_{n}^{t}(\mathbb{e_{(i),(ii),(iv)}(\Delta\theta_{n}^{t}(\mathbb{E_{(i),(ii),(iv)}(\Delta\theta_{n}^{t}(\mathbb{E_{(i),(ii),(iv)}(\Delta\theta_{n}^{t}(\mathbb{E}_{(i),(ii),(iv)}(\Delta\theta_{n}^{t}(\mathbb{e_{(i),(ii),(iv)}(\Delta\theta_{n}^{t}(\mathbb{E_{(i),(ii),(iv)}(\Delta\theta_{n}^{t}(\mathbb{E}_{(i),(ii),(iv)}(\Delta\theta_{n}^{t}(\mathbb{E_{(i),(ii),(iv)}(\Delta\theta_{n}^{t}(\mathbb{e_{(i),(ii),(iv)}(\Delta\theta_{n}^{t}(\mathbb{E_{(i),(ii),(iv)}(\Delta\theta_{n}^{t}(\mathbb{E}_{(i),(ii),(iv)}(\Delta\theta_{n}^{t}(\mathbb{E}_{(i),(ii),(iv)}(\Delta\theta_{n}^{t}(\mathbb{e_{(i),(ii),(iv)}(\Delta\theta_{n}^{t}(\mathbb{E}_{(i),(ii),(iv)}(\Delta\theta_{n}^{t}(\mathbb{E_{(i),(ii),(iv)}(\Delta\theta_{n}^{t}(\mathbb{E_{(i),(ii),(iv)}(\Delta\theta_{n}^{t}(\mathbb{e_{(i),(ii),(iv)}(\Delta\theta_{n}^{t}(\mathbb{E}_{(i),(ii),(iv)}(\Delta\theta_{n}^{t}(\mathbb{E_{(i),(ii),(iv)}(\Delta\theta_{n}^{t}(\mathbb{E}_{(i),(ii),(iv)}(\Delta\theta_{n}^{t}(\mathbb{e_{(i),(ii),(iv)}(\Delta\theta_{n}^{t}(\mathbb{E}_{(i),(ii),(iv)}(\Delta\theta_{n}^{t}(\mathbb{E}_{(i),(ii),(iv)}(\Delta\theta_{n}^{t}(\mathbb{E_{(i),(ii),(iv)}(\Delta\theta_{n}^{t}(\mathbb{e_{(i),(ii),(iv)}(\Delta\theta_{n}^{t}(\mathbb{E}_{(i),(ii),(iv)}(\Delta\theta_{n}^{t}(\mathbb{E}_{(i),(ii),(iv)}(\Delta\theta_{n}^{t}(\mathbb{E}_{(i),(ii),(iv)}(\Delta\theta_{n}^{t}(\mathbb{B_{(i),(ii),(iv)}(\Delta\theta_{n}^{t}(\mathbb{E}_{(i),(ii),(iv)}(\Delta\theta_{n}^{t}(\mathbb{E_{(i),(ii),(iv)}(\Delta\theta_{n}^{t}(\mathbb{E_{(i),(ii),(iv)}(\Delta\theta_{n}^{t}(\mathbb{B_{(i),(ii),(iv)}(\Delta\theta_{n}^{t}(\mathbb{E_{(i),(ii),(iv)}(\Delta\theta_{n}^{t}(\mathbb{E_{(i),(ii),(iv)}(\Delta\theta_{n}^{t}(\mathbb{E_{(i),(ii),(iv)}(\Delta\theta_{n}^{t}(\mathbb{B_{(i),(ii),(iv)}(\Delta\theta_{n}^{t}(\mathbb{E}_{(i),(ii),(iv)}(\Delta\theta_{n}^{t}(\mathbb{E_{(i),(ii),(iv)}(\Delta\theta_{n}^{t}(\mathbb{E}_{(i),(ii),(iv)}(\Delta\theta_{n}^{t}(\mathbb{B_{(i),(ii),(iv)}(\Delta\theta_{n}^{t}(\mathbb{E_{(i),(ii),(iv)}(\Delta\theta_{n}^{t}(\mathbb{E_{(i),(ii),(iv)}(\Delta\theta_{n}^{t}(\mathbb{E}_{(i),(ii),(iv)}(\Delta\theta_{n}^{t}(\mathbb{B_{(i),(ii),(iv)}(\Delta\theta_{n}^{t}(\mathbb{E}_{(i),(ii),(iv)}(\Delta\theta_{n}^{t}(\mathbb{E}_{(i),(ii),(iv)}(\Delta\theta_{n}^{t}(\mathbb{E_{(i),(ii),(iv)}(\Delta\theta_{n}^{t}(\mathbb{B_{(i),(ii),(iv)}(\Delta\theta_{n}^{t}(\mathbb{E_{(i),(ii),(iv)}(\Delta\theta_{n}^{t}(\mathbb{E}_{(i),(ii),(iv)}(\Delta\theta_{n}^{t}(\mathbb{E_{(i),(ii),(iv)}(\Delta\theta_{n}^{t}(\mathbb{B_{(i),(ii),(iv)}(\Delta\theta_{n}^{t}(\mathbb{E}_{(i),(ii),(iv)}(\Delta\theta_{n}^{t}(\mathbb{E}_{(i),(ii),(iv)}(\Delta\theta_{n}^{t}(\mathbb{E}_{(i),(ii),(iv)}(\Delta\theta_{n}^{t}(\mathbb{C}_{n}^{t}(\mathbb{B_{(i),(ii),(iv)}(\Delta\theta_{n}^{t}(\mathbb{E}_{(i),(ii),(iv)}(\Delta\theta_{n}^{t}(\mathbb{E}_{(i),(ii),(iv)}(\Delta\theta_{n}^{t}(\mathbb{E}_{(i),(ii),(iv)}(\Delta\theta_
$$

The equality (35) is due to  $\mathbb{E}_{(iii)}\left(\mathbf{z}_n^t\right) = \mathbb{E}_{(iii)}\left(\hat{\mathbf{z}}_n^t\right) = 0$ . The equality (36) is due to  $\mathbb{E}_{(ii)}\left(\mathbf{b}_n^t\right) = q_n^t$ . The inequality (40) is due to triangle inequality. The inequality (42) is due to the clipping bias lemma (Das et al., 2023) given any  $\rho > 1$ .

We next further simplify the first and second terms on the right- hand side of (42).

The first term equals zero:

$$
\begin{array}{rl} & {\mathbb{E}_{(iv)}\left(\left(\frac{q^t}{q^t} -\frac{q_n^t}{q^t}\right)\mathbb{E}_{(i)}\left(\Delta \theta_n^t\right)\right)}\\ & {= \mathbb{E}_{(i)}\left(\left\| \Delta \theta_n^t\right\|^{\rho}\right)\mathbb{E}_{(iv)}\left(1 - \frac{q_n^t}{q^t}\right)}\\ & {= \mathbb{E}_{(i)}\left(\left\| \Delta \theta_n^t\right\|^{\rho}\right)\left(\sum_{i\in [N]}\operatorname *{Pr}\left(\epsilon_n = \hat{\epsilon}_i\right)\mathbb{E}_{(iv)}\left(1 - \frac{q_n^t}{q^t}\bigg|_{\epsilon_n = \hat{\epsilon}_i}\right)\right)}\\ & {= \mathbb{E}_{(i)}\left(\left\| \Delta \theta_n^t\right\|^{\rho}\right)\left(\frac{1}{N}\sum_{i\in [N]}\left(1 - \frac{\hat{q}_i^t}{q^t}\right)\right)}\\ & {= \mathbb{E}_{(i)}\left(\left\| \Delta \theta_n^t\right\|^{\rho}\right)\left(\frac{1}{N}\sum_{n\in [N]}\left(1 - \frac{q_n^t}{q^t}\right)\right)}\\ & {= \mathbb{E}_{(i)}\left(\left\| \Delta \theta_n^t\right\|^{\rho}\right)\left(\frac{1}{N}\sum_{n\in [N]}\left(1 - \frac{q_n^t}{q^t}\right)\right)}\\ & {= \mathbb{E}_{(i)}\left(\| \Delta \theta_n^t\|^{\rho}\right)\left(\frac{1}{N}\left(N - \frac{q_n^tN}{q^t}\right)\right) = 0.} \end{array} \tag{44}
$$

Equality (43) is due to the independency of randomness between (i) and (iv). Equality (45) is because of our assumption that the sampling from  $P_{n}$  and of  $\epsilon_{n}$  is independent. Equality (47) is because  $\sum_{n\in [N]}^{N} q_{n}^{t} = q^{t}$ .

The second term on the right- hand side of (42) can be further simplified into:

$$
\begin{array}{rl} & {\mathbb{E}_{(iv)}\left(\frac{q_t^t}{q^t}\mathbb{E}_{(i)}\left(\left\| \Delta \theta_n^t\right\|^{\rho}\right)\right)}\\ & {= \mathbb{E}_{(i)}\left(\left\| \Delta \theta_n^t\right\|^{\rho}\right)\mathbb{E}_{(iv)}\left(\frac{q_t^t}{q^t}\frac{1}{(c_n^t)^{\rho - 1}}\right)}\\ & {= \mathbb{E}_{(i)}\left(\left\| \Delta \theta_n^t\right\|^{\rho}\right)\left(\sum_{i\in [N]}\operatorname *{Pr}\left(\epsilon_n = \hat{\epsilon}_i\right)\mathbb{E}_{(iv)}\left(\frac{q_t^t}{q^t}\frac{1}{(c_n^t)^{\rho - 1}}\bigg|_{\epsilon_n = \hat{\epsilon}_i}\right)\right)}\\ & {= \mathbb{E}_{(i)}\left(\left\| \Delta \theta_n^t\right\|^{\rho}\right)\left(\frac{1}{N}\sum_{i\in [N]}\frac{\hat{q}_i^t}{q^t}\frac{1}{(c_n^t)^{\rho - 1}}\right)}\\ & {= \mathbb{E}_{(i)}\left(\left\| \Delta \theta_n^t\right\|^{\rho}\right)\left(\frac{1}{N}\sum_{n\in [N]}\frac{q_n^t}{q^t}\frac{1}{(c_n^t)^{\rho - 1}}\right).} \end{array} \tag{51}
$$

Equality (48) is due to the independency of randomness between (i) and (iv). Equality (49) is because of our assumption that the sampling from  $P_{n}$  and of  $\epsilon_{n}$  is independent. Combining (42), (47), and (51), Thm. 3 is proved.

# A.6 EXTENDED BACKGROUND

# A.6.1 SOME USEFUL LEMMAS FROM PRIOR WORKS

Lemma 2. [Clipping bias (Das et al., 2023) Suppose  $\phi (\xi)$  (where  $\xi$  denotes the source of randomness) is an unbiased estimator of  $\phi$ , i.e.,  $\mathbb{E}_{\xi}(\phi (\xi)) = \phi$ . Let  $b(\xi)$  denote the clipping bias of Clip  $(\phi (\xi), c)$ , i.e.,  $b(c) = \| \phi - \mathbb{E}_{\xi}(\operatorname {Clip}(\phi (\xi), c))\|$ . Then for any  $\rho > 1$ ,

$$
b(c)\leq \frac{\mathbb{E}_{\xi}(\|\phi(\xi)\|^{\rho})}{c^{\rho - 1}}. \tag{52}
$$

# A.6.2 DIFFERENTIAL PRIVACY

Definition 1  $((\epsilon ,\delta)$  - DP Dwork et al. (2014). The randomized algorithm  $\mathcal{A}:\chi \rightarrow \mathcal{R}$  with domain  $\chi$  and range  $\mathcal{R}$  satisfies  $(\epsilon ,\delta)$  - DP iff for any two neighboring inputs  $\mathcal{D},\mathcal{D}^{\prime}\in \chi$  that differ by at most one record, and any measurable subset of outputs  $S\subseteq \mathcal{R}$

$$
Pr(\mathcal{A}(\mathcal{D})\in \mathcal{S})\leq \epsilon^{\delta}Pr(\mathcal{A}(\mathcal{D}^{\prime})\in \mathcal{S}) + \delta . \tag{53}
$$

In (53), the privacy budget  $\epsilon \in \mathbb{R}_{+}$  controls the extent to which the output distributions induced by two neighboring inputs may differ. The  $\delta \in [0,1]$  quantifies the probability of violating the privacy guarantee. Allowing a larger  $\delta \in [0,1]$  improves utility at the cost of a more relaxed (weaker) privacy guarantee. One way to relax the DP guarantee is to use  $(\alpha ,\epsilon)$  - Renyi DP (RDP) (Mironov, 2017). The  $\alpha >1$  is the order of Renyi divergence between distributions  $P\coloneqq \operatorname *{Pr}(\mathcal{A}(\mathcal{D}))$  and  $P^{\prime}\coloneqq \operatorname *{Pr}\left(\mathcal{A}(\mathcal{D}^{\prime})\right)$  , defined as

$$
R_{\alpha}(P\| P^{\prime})\coloneqq \frac{1}{1 - \alpha}\log \mathbb{E}_{\mathbf{x}\sim P^{\prime}}\left(\frac{P}{P^{\prime}}\right)^{\alpha}. \tag{54}
$$

While the Renyi divergence can be defined for  $\alpha < 1$  , including negative orders, the RDP definition Mironov (2017) is based on  $\alpha \geq 1$  and is outlined as follows.

Definition 2 (Renyi DP (RDP) Mironov (2017). The randomized algorithm  $\mathcal{A}:\chi \rightarrow \mathcal{R}$  with domain  $\chi$  and range  $\mathcal{R}$  is  $(\alpha ,\epsilon)$  - RDP iff for any neighboring inputs  $\mathcal{D},\mathcal{D}^{\prime}\in \chi ,$  we have

$$
R_{\alpha}\left(Pr\left(\mathcal{A}(\mathcal{D})\right)\| Pr\left(\mathcal{A}(\mathcal{D}^{\prime})\right)\right)\leq \epsilon . \tag{55}
$$

When accounting for total privacy consumption over an iterative algorithm, RDP offers a smoother composition property than DP. RDP allows the privacy budget to accumulate linearly with the number of training rounds (Mironov, 2017). This simplifies the tracking and management of privacy budgets over time. We next recall a lemma from (Mironov, 2017). Lemma 3 shows how RDP can be converted to DP when needed.

Lemma 3. If  $\mathcal{A}$  is an  $(\alpha ,\epsilon_{rdp})$  - RDP algorithm, it also satisfies  $(\epsilon ,\delta)$  - DP for any  $0< \delta < 1$  , where

$$
\epsilon = \epsilon_{rdp} + \log \frac{\alpha - 1}{\alpha} -\frac{\log\delta + \log\alpha}{\alpha - 1}. \tag{56}
$$

To implement privacy guarantees, we use the sampled Gaussian mechanism (SGM) Mironov et al. (2019), formally defined as follows.

Definition 3 (SGM Mironov et al. (2019). Consider the algorithm  $\mathcal{A}$  which maps a subset  $\mathcal{D}\subseteq \chi$  to  $\mathbb{R}^w$  and has  $\ell_2$  - sensitivity c. The sampled Gaussian mechanism parameterized by the sampling rate  $q\in [0,1],c,$  and noise multiplier  $\sigma >0$  is defined as

$$
\mathcal{G}_{\sigma ,c,q}(\mathcal{D})\coloneqq \mathcal{A}(\{x\mid x\in \mathcal{D}is sampled with Probability q\}) + \mathcal{N}(0,c^2\sigma^2\mathbb{I}_w), \tag{57}
$$

where each element of  $\mathcal{D}$  is (Poisson) sampled independently at random with probability  $q$  and  $\mathcal{N}(0,c^2\sigma^2\mathbb{I}_w)$  is spherical  $w$  - dimensional Gaussian noise with per- coordinate variance  $c^2\sigma^2$

Lemma 4 (Mironov et al. (2019). The SGM  $\mathcal{G}_{\sigma ,c,q}$  with  $c = 1$  guarantees  $(\alpha ,\epsilon)$  - RDP, where  $\epsilon \leq \frac{2\alpha q^2}{\sigma^2}$

# A.7 EXTENDED EXPERIMENTAL SETUP

We conduct our experiments in Python 3.11 using Pytorch leveraging the  $4\times \mathrm{L424}$  GB GPU. Below, we provide additional details on the experimental setups used in Sec. 5 to analyze how our time- adaptive DP- FL framework enhances the privacy- utility tradeoff and in Appendix A.8 which extends experiments for further analysis.

Details on Datasets. For our experiments, we use FMNIST, MNIST, Adult Income, and CIFAR10 datasets. Both FMNIST and MNIST datasets have a training set of 60,000 and a test set of 10,000  $28\times 28$  images, associated with 10 labels. The Adult Income dataset consists of 48,842 samples with 14 features and is split into a training set of 32,561 samples and a test set of 16,281 samples.

The CIFAR10 dataset consists of  $60,00032 \times 32$  color images in 10 classes, with 6000 images per class. There are 50,000 training images and 10,000 test images.

Simulation Parameters. Throughout our simulations, we use SGD optimizer and momentum equal to 0.9. We also use a CosineAnnealing learning rate scheduler from (Loshchilov & Hutter, 2016) for faster convergence. In Sec.5, we fix the spending- based sample rate (during spend mode) to  $q = 0.9$  and the average clipping norm to  $c = 250$ . We consider the transition from saving round to spending round occurs in the middle of training. I.e., given the total number of rounds  $T = 25$ , we set  $T_{\mathrm{group},1} = T_{\mathrm{group},2}$ ,  $T_{\mathrm{group},3} = 13$ . The obtained results are averaged over three runs. In Table 4 we summarize other hyperparameters, including learning rate  $(\lambda)$ , number of clients  $(N)$ , batch size  $(B)$ , number of local epochs  $(L)$ , and the saving- based sampling rates of clients from privacy groups 1, 2, and 3  $(q_{\mathrm{group},1}, q_{\mathrm{group},2}, q_{\mathrm{group},3})$ .

Table 4: Parameters for different datasets, used in Table 2 and Figure 2. We set  $T = 25$ $T_{\mathrm{group,1}} =$ $T_{\mathrm{group},2}$ $T_{\mathrm{group},3} = 13$ $q = 0.9$  ,and  $c = 250$  

<table><tr><td>Dataset</td><td>(epsilon,1,epsilon,2,epsilon,3)</td><td>λ</td><td>N</td><td>B</td><td>L</td><td>(qgroup,1,epsilon,2,epsilon,3)</td></tr><tr><td>FMNIST</td><td>(10, 20, 30)</td><td>0.001</td><td>100</td><td>125</td><td>30</td><td>(0.5, 0.6, 0.7)</td></tr><tr><td>MNIST</td><td>(10, 15, 20)</td><td>0.001</td><td>100</td><td>125</td><td>30</td><td>(0.5, 0.6, 0.7)</td></tr><tr><td>Adult Income</td><td>(10, 20, 30)</td><td>0.01</td><td>80</td><td>32</td><td>5</td><td>(0.6, 0.7, 0.8)</td></tr></table>

![](images/0b55a0a301d7353fc2f33c7ad379fe0a37d5dcf56d7a4d8bfd18bead9d8b1353.jpg)  
Figure 3: Average Training loss of clients in our time-adaptive DP-FL scheme plotted versus the IDP-FedAvg baseline with FMNIST dataset in training rounds  $T = 25$ . We set  $(\epsilon_{\mathrm{group},1}, \epsilon_{\mathrm{group},2}, \epsilon_{\mathrm{group},3}) = (10, 20, 30)$  in our scheme and IDP-FedAvg.

# A.8 EXTENDED EXPERIMENTAL RESULTS

Impact of Training Rounds on Model Convergence. We extend experiments to more training rounds—  $T \in \{25, 50, 100\}$ . For example, in Figure 4, we set  $T = 50$ , and plot the global test accuracy vs. communication rounds. It is evident from Figure 4 that for our time- adaptive DP- FL framework, as the number of training rounds increases, the upward trend in the accuracy starts slowing down. However, increasing the number of communication rounds does not always improve accuracy. This is because, with more rounds, the privacy budget is distributed across more rounds, resulting in a lower budget per round. Consequently, the increased effect of perturbation can degrade the privacy- utility tradeoff. This is demonstrated in our FMNIST and MNIST experiments, as shown in Table 5, in which we report the final- round test accuracy across different schemes. As shown in the third column of Table 5, when training rounds increase from 25 to 50 and from 50 to 100, FedAvg (the non- DP baseline scheme) consistently demonstrates an upward trend in both MNIST and FMNIST experiments. However, our scheme (fifth column of Table 5) and IDP- FedAvg (fourth column), which operate under limited group privacy budgets  $(\epsilon_{\mathrm{group},1}, \epsilon_{\mathrm{group},2}, \epsilon_{\mathrm{group},3}) = (10, 20, 30)$ , do not exhibit the same consistent improvement. They exhibit an upward trend from 25 to 50 rounds but not consistently from 50 to 100 rounds. Notably, the best performance amongst the DP experiments of Table 5) is achieved by our scheme, reaching  $75.63\%$  after  $T = 100$  rounds for the FMNIST dataset, and  $90.78\%$  at  $T = 50$  rounds for the MNIST dataset.

Impact of Different Privacy Budgets on Model Utility. We present additional experimental results to evaluate the impact of stricter privacy budgets  $(\epsilon_{\mathrm{group},1}, \epsilon_{\mathrm{group},2}, \epsilon_{\mathrm{group},3}) = (2, 5, 10)$  and  $(5, 5, 5)$

![](images/ef8459a722209004f03a51d98be5843e1abd2db3e6bbca2421366cfa7bdb63b0.jpg)  
Figure 4: Global test accuracy for increasing number of communication rounds. In this figure, we use the FMNIST dataset,  $N = 100$  clients,  $L = 30$  local iterations,  $(\epsilon_{\mathrm{group},1}, \epsilon_{\mathrm{group},2}, \epsilon_{\mathrm{group},3}) = (20, 20, 20), c = 250$ , and  $q = 0.8$ .

Table 5: Benchmarking our time-adaptive DP-FL scheme against the baselines in terms of global test accuracy and across varying datasets and number of training rounds (T). We set  $(\epsilon_{\mathrm{group},1},\epsilon_{\mathrm{group},2},\epsilon_{\mathrm{group},3}) = (10,20,30)$  in our scheme and IDP-FedAvg.  

<table><tr><td>Dataset</td><td>T</td><td>FedAvg 
(non-DP)</td><td>IDP-FedAvg</td><td>Ours</td></tr><tr><td rowspan="3">FMNIST</td><td>25</td><td>72.95</td><td>62.57</td><td>66.55</td></tr><tr><td>50</td><td>76.00</td><td>71.80</td><td>75.51</td></tr><tr><td>100</td><td>80.14</td><td>71.29</td><td>75.63</td></tr><tr><td rowspan="3">MNIST</td><td>25</td><td>90.23</td><td>64.53</td><td>74.69</td></tr><tr><td>50</td><td>93.42</td><td>89.57</td><td>90.78</td></tr><tr><td>100</td><td>95.91</td><td>87.00</td><td>90.15</td></tr></table>

![](images/31b662dc8ed2fdc536860f659beda1ff013360808e292dbeaf9135009cad8cca.jpg)  
Figure 5: Test accuracy for our time-adaptive DP-FL framework vs. IDP-FedAvg, using stricter privacy budgets  $(\epsilon_{\mathrm{group},1}, \epsilon_{\mathrm{group},2}, \epsilon_{\mathrm{group},3}) = (2, 5, 10)$ . In this figure, we use  $N = 100$  clients,  $T = 50$  global rounds,  $L = 30$  local iterations,  $c = 250$ , and  $q = 0.8$ .

on model utility (test accuracy). The results are presented in Figure 5 and Tables 6 and 7. As expected, we observe that lower privacy budgets hamper utility. In particular, in Table 6, we benchmark our scheme against the IDP- FedAvg baseline using two sets of non- uniform privacy budgets,  $(10, 20, 30)$  and  $(2, 5, 10)$ , evaluated across two datasets. Our findings suggest that the time- adaptive DP- FL framework yields considerably higher utility than IDP- FedAvg, also under stringent privacy constraints. Similarly, Table 7 focuses on uniform privacy budgets and further confirms that even with a reduction in privacy budgets from  $(10, 10, 10)$  to  $(5, 5, 5)$ , our scheme consistently outperforms the corresponding baselines.

Additional Baseline. We benchmark our scheme against the adaptive clipping method Andrew et al. (2021), with pseudocode provided in Algorithm 6. We present results in the third and fifth columns of Table 7. This baseline is designed for uniform privacy budgets and is parameterized by the server- side learning rate  $\lambda_{s}$  and momentum parameter  $\beta_{s}$ , which are not privacy- specific. To ensure a fair comparison with our scheme and other baselines in our paper, we set these parameters to  $\lambda_{s} = 1.0$

Table 6: Benchmarking our time-adaptive DP-FL scheme against the baselines in terms of the finalround test accuracy and across varying privacy budgets  $(\epsilon_{\mathrm{group},1},\epsilon_{\mathrm{group},2},\epsilon_{\mathrm{group},3})$  . We set  $T = 25$  and  $L = 30$  for  $\epsilon = \{10,20,30\}$  and  $T = 25$  and  $L = 50$  for  $\epsilon = \{2,5,10\}$ $(q_{\mathrm{group},1},q_{\mathrm{group},2},q_{\mathrm{group},3}) =$  (0.3, 0.5, 0.7).  

<table><tr><td>Dataset</td><td>Privacy Budgets</td><td>IDP-FedAvg Non-uniform</td><td>Ours Non-uniform</td></tr><tr><td>FMNIST</td><td>(10, 20, 30)</td><td>62.57</td><td>66.55</td></tr><tr><td>FMNIST</td><td>(2, 5, 10)</td><td>60.99</td><td>65.75</td></tr><tr><td>MNIST</td><td>(10, 20, 30)</td><td>64.53</td><td>77.38</td></tr><tr><td>MNIST</td><td>(2, 5, 10)</td><td>63.35</td><td>66.50</td></tr></table>

Table 7: Benchmarking our time-adaptive DP-FL scheme against the baselines in terms of the finalround test accuracy and across varying uniform privacy budgets  $\epsilon_{\mathrm{group},1} = \epsilon_{\mathrm{group},2} = \epsilon_{\mathrm{group},3}$  . We set  $T = 25$  and  $L = 30$  

<table><tr><td>Dataset</td><td>Privacy Budgets</td><td>Adaptive Clipping (βs, λs) = (0, 1.0)</td><td>DP-FedAvg</td><td>Adaptive Clipping optimal (βs, λs)</td><td>Ours</td></tr><tr><td>FMNIST</td><td>(10, 10, 10)</td><td>60.23</td><td>64.8</td><td>67.64</td><td>67.90</td></tr><tr><td>FMNIST</td><td>(5, 5, 5)</td><td>52.39</td><td>51.06</td><td>52.39</td><td>60.79</td></tr><tr><td>MNIST</td><td>(10, 10, 10)</td><td>65.59</td><td>76.79</td><td>78.04</td><td>80.2</td></tr><tr><td>MNIST</td><td>(5, 5, 5)</td><td>55.48</td><td>61.45</td><td>55.48</td><td>69.07</td></tr></table>

and  $\beta_{s} = 0.0$  . In column 3, we use these default values, while in column 5, we select the optimal values from a set of possible choices. As shown in the table, our scheme consistently outperforms adaptive clipping, even when the baseline's parameters are optimally tuned.

Effect of Number of Clients on Model Utility. We experiment with different numbers of clients $N\in \{30,60,75\}$  for the MNIST dataset to validate the applicability of our time- adaptive DPFL framework across various scenarios. Additionally, we also perform experiments to analyze if our framework outperforms the baselines, in terms of the utility of the trained model. Our results in Table 8 indicate that for all the different numbers of clients that we consider, our framework remarkably surpasses the utility of the baseline.

Table 8: Comparison of model utility on a varying number of clients and comparison of model utility for time-adaptive DP-FL with baselines for a varying number of clients  

<table><tr><td>Number of clients</td><td>SETUP Privacy Budgets</td><td>IDP-FedAvg Non-uniform</td><td>Ours Non-uniform</td></tr><tr><td>30</td><td>(10, 20, 30)</td><td>72.35</td><td>73.34</td></tr><tr><td>60</td><td>(10, 20, 30)</td><td>78.69</td><td>83.83</td></tr><tr><td>75</td><td>(10, 20, 30)</td><td>70.93</td><td>77.53</td></tr></table>

The Choice of Hyperparameters. We evaluate our DP- FL framework with different choices of hyperparameters- different saving- based sampling rates  $(q_{\mathrm{group},1},q_{\mathrm{group},2},q_{\mathrm{group},3})$  and different saving- to- spending transition rounds  $(T_{\mathrm{group},1},T_{\mathrm{group},2},T_{\mathrm{group},3})$  . The final- round test accuracies for different choices of  $(q_{\mathrm{group},1},q_{\mathrm{group},2},q_{\mathrm{group},3})$  , and across both MNIST and FMNIST datasets, are presented in Table 9. In this table, in Column 3 we set these rates as (0.5,0.6,0.7), in Column 4 as  $(0.3,0.5,0.7)$  , and in Column 5 as  $(0.6,0.6,0.6)$  .As shown in the table, our scheme, which uses lower- sampling rates during saving- e.g., for all  $i\in [3]$ $q_{\mathrm{group},1}$  is smaller than  $q = 0.9$  in this table- - outperforms the IDP- FedAvg baseline (Column 6) that uses a uniform sampling rate  $q$  over time. This table also shows that our method is relatively robust against the clients' choice of saving- based sampling rates, consistently achieving performance between that of IDP- FedAvg and the ideal case of FedAvg without DP (Column 2).

Table 9: Evaluating the impact of saving-based sampling rates of different privacy groups,  $(q_{\mathrm{group},1},q_{\mathrm{group},2},q_{\mathrm{group},3})$  , on our time-adaptive DP-FL scheme in comparison with the baseline. We set  $(\epsilon_{\mathrm{group},1},\epsilon_{\mathrm{group},2},\epsilon_{\mathrm{group},3}) = (10,20,30)$ $T = 25,L = 30$  and  $N = 100$ $T_{\mathrm{group},1} = T_{\mathrm{group},2} =$ $T_{\mathrm{group},3} = 13$ $q = 0.9$ $c = 250$ $\lambda = 0.001$  , and  $B = 125$  

<table><tr><td>DATASET</td><td>FedAvg non-DP</td><td>Ours (0.5, 0.6, 0.7)</td><td>Ours (0.5, 0.5, 0.7)</td><td>Ours (0.6, 0.6, 0.6)</td><td>IDP-FedAvg</td></tr><tr><td>MNIST</td><td>90.23</td><td>72.72</td><td>77.39</td><td>71.6</td><td>64.53</td></tr><tr><td>FMNIST</td><td>72.95</td><td>70.57</td><td>66.55</td><td>67.75</td><td>62.57</td></tr></table>

The final- round test accuracies for different choices of saving- to- spending transition rounds  $(T_{\mathrm{group},1},T_{\mathrm{group},2},T_{\mathrm{group},3})$  , for both MNIST and FMNIST datasets, are presented in Table 10. We set the total number of rounds as  $T = 25$  . In this table, in Column 3 we set the transition rounds as (7,7,7), in Column 4 as (7, 13, 19), in Column 5 as (19, 13, 7), and in Column 6 as (19, 19, 19). As shown in the table, our scheme, which transitions from saving to spend mode sometime between the first and final round- i.e., for all  $i\in [3]$ $1< T_{\mathrm{group},1}< 25$  outperforms the IDP- FedAvg baseline (Column 7) which can be viewed as a special case of ours with transition rounds set to  $(1,1,1)$  . This table shows the robustness of our method to the client's choice of transition rounds, showing less than a  $2\%$  variation in accuracy across different choices while consistently achieving performance between that of IDP- FedAvg and the ideal- case of FedAvg without DP (Column 2).

Table 10: Evaluating the impact of saving-to-spending transition rounds of different privacy groups,  $(T_{\mathrm{group},1},T_{\mathrm{group},2},T_{\mathrm{group},3})$  , on our time-adaptive DP-FL scheme in comparison with the baseline. We set  $(\epsilon_{\mathrm{group},1},\epsilon_{\mathrm{group},2},\epsilon_{\mathrm{group},3}) = (10,20,30)$ $T = 25,L = 30$ $N = 100$ $(q_{\mathrm{group},1},q_{\mathrm{group},2},q_{\mathrm{group},3}) = (0.3,0.5,0.7)$ $q = 0.9$ $c = 250$ $\lambda = 0.001$  , and  $B = 125$  

<table><tr><td>DATASET</td><td>FedAvg non-DP</td><td>Ours (7, 7, 7)</td><td>Ours (7, 13, 19)</td><td>Ours (19, 13, 7)</td><td>Ours (19, 19, 19)</td><td>IDP-FedAvg</td></tr><tr><td>MNIST</td><td>90.23</td><td>74.38</td><td>74.69</td><td>72.24</td><td>73.88</td><td>64.53</td></tr><tr><td>FMNIST</td><td>72.95</td><td>66.72</td><td>65.29</td><td>65.34</td><td>67.5</td><td>62.57</td></tr></table>

Experiments on The CIFAR10 Dataset. We run experiments on the CIFAR10 dataset. The final- round test accuracies of our time- adaptive DP- FL framework in comparison with the FedAvg (non- DP) and IDP- FedAvg baselines are presented in Table 11. The results suggest that our proposed approach surpasses IDP- FedAvg, by lowering the gap to the ideal case of FedAvg by about  $9\%$ . We note that the test accuracies reported for all schemes in this table are relatively lower than those we reported earlier in this paper for the MNIST, FMNIST, and Adult Income datasets. We hypothesize that this happens due to the increased complexity of the CIFAR10 dataset, particularly when distributed in a non- iid manner in an FL setting with  $N = 100$  clients.

Table 11: Benchmarking our time-adaptive DP-FL framework against the baselines using the CIFAR10 dataset. We set  $(\epsilon_{\mathrm{group},1},\epsilon_{\mathrm{group},2},\epsilon_{\mathrm{group},3}) = (100,50,25)$ $T = 50,L = 30$ $N = 100$ $(q_{\mathrm{group},1},q_{\mathrm{group},2},q_{\mathrm{group},3}) = (0.5,0.5,0.5)$ $q = 0.9$ $c = 250$ $\lambda = 0.001$  , and  $B = 125$  

<table><tr><td>DATASET</td><td>FedAvg</td><td>IDP-FedAvg</td><td>Ours</td></tr><tr><td>CIFAR10</td><td>44.42</td><td>34.97</td><td>35.41</td></tr></table>