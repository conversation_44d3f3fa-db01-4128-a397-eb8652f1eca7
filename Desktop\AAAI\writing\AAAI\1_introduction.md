# Introduction

Medical multimodal federated learning has emerged as a transformative paradigm for collaborative healthcare AI development, enabling multiple medical institutions to jointly train sophisticated models while preserving patient privacy (<PERSON> et al. 2024; <PERSON> et al. 2023). This approach addresses critical challenges in healthcare, including the shortage of medical professionals, unequal distribution of medical resources, and the need for comprehensive analysis of diverse medical data modalities—ranging from medical imaging and electronic health records to genomics and clinical notes.

医疗多模态联邦学习已成为协作医疗AI开发的变革性范式，使多个医疗机构能够在保护患者隐私的同时联合训练复杂模型。这种方法解决了医疗保健中的关键挑战，包括医疗专业人员短缺、医疗资源分配不均，以及对从医学影像、电子健康记录到基因组学和临床笔记等多样化医疗数据模态进行综合分析的需求。

However, the deployment of medical multimodal federated learning systems faces unprecedented trustworthiness challenges that extend far beyond traditional privacy concerns. Recent studies reveal alarming vulnerabilities in medical AI systems, with cross-modal attacks achieving success rates exceeding 80% (<PERSON> et al. 2024), while diagnostic errors affecting 10-15% of clinical decisions highlight the critical need for robust and reliable medical AI systems (<PERSON><PERSON><PERSON> 2013; <PERSON><PERSON> and <PERSON> 2008). The interconnected nature of medical modalities creates complex attack surfaces where compromising one modality can cascade across others, potentially leading to life-threatening consequences in clinical decision-making.

然而，医疗多模态联邦学习系统的部署面临着前所未有的可信性挑战，这些挑战远远超出了传统的隐私关注。最近的研究揭示了医疗AI系统中令人担忧的漏洞，跨模态攻击的成功率超过80%，而影响10-15%临床决策的诊断错误突出了对鲁棒可靠医疗AI系统的迫切需求。医疗模态的互连特性创造了复杂的攻击面，其中一个模态的妥协可能级联到其他模态，在临床决策中可能导致危及生命的后果。

Current approaches to medical federated learning predominantly focus on single-dimension optimization, treating trustworthiness aspects such as privacy preservation, security enhancement, fairness improvement, robustness assurance, and explainability in isolation. For instance, FedAA (Zhang et al. 2024) concentrates on differential privacy mechanisms, while SADBA (Liu et al. 2024) addresses security vulnerabilities through backdoor attack mitigation. However, these methods fail to recognize the complex interdependencies among trustworthiness dimensions. Strong differential privacy can significantly impair attack detection capabilities, while robust aggregation methods may introduce fairness biases, creating a fundamental tension that existing approaches cannot adequately address.

当前的医疗联邦学习方法主要关注单维度优化，孤立地处理隐私保护、安全增强、公平性改进、鲁棒性保证和可解释性等可信性方面。例如，FedAA专注于差分隐私机制，而SADBA通过后门攻击缓解来解决安全漏洞。然而，这些方法未能识别可信性维度之间的复杂相互依赖关系。强差分隐私可能显著损害攻击检测能力，而鲁棒聚合方法可能引入公平性偏差，创造了现有方法无法充分解决的根本张力。

Moreover, the emergence of sophisticated cross-modal cascade threats (CMCT) poses unprecedented challenges to medical multimodal systems. Unlike traditional single-modality attacks, CMCT exploits the interconnected nature of medical data modalities, where compromising one modality (e.g., medical imaging) can progressively affect others (e.g., clinical text, genomic data), creating cascading failures that are difficult to detect and mitigate. Recent research demonstrates that medical multimodal systems are particularly vulnerable to such attacks, with mismatched malicious attacks achieving 10-20% higher success rates compared to single-modal approaches (Huang et al. 2024).

此外，复杂跨模态级联威胁(CMCT)的出现对医疗多模态系统构成了前所未有的挑战。与传统的单模态攻击不同，CMCT利用医疗数据模态的互连特性，其中一个模态（如医学影像）的妥协可能逐步影响其他模态（如临床文本、基因组数据），创造难以检测和缓解的级联故障。最近的研究表明，医疗多模态系统特别容易受到此类攻击，不匹配恶意攻击比单模态方法的成功率高10-20%。

Addressing these multifaceted challenges requires a paradigm shift from reactive, single-dimension optimization to proactive, multi-dimensional trustworthiness frameworks. Three critical research gaps must be addressed: (1) **Lack of Unified Multi-dimensional Framework**: Current methods treat trustworthiness dimensions independently, failing to model their complex interdependencies and potential conflicts. (2) **Absence of Predictive Defense Mechanisms**: Existing security approaches are predominantly reactive, responding to threats after they occur rather than predicting and preventing cross-modal cascade attacks. (3) **Insufficient Adaptive Coupling**: Traditional optimization methods lack sophisticated mechanisms to dynamically balance multiple trustworthiness objectives in response to changing medical environments and regulatory requirements.

解决这些多方面的挑战需要从反应性、单维度优化向主动性、多维度可信性框架的范式转变。必须解决三个关键研究空白：(1) **缺乏统一的多维度框架**：当前方法独立处理可信性维度，未能建模其复杂的相互依赖关系和潜在冲突。(2) **缺乏预测性防御机制**：现有安全方法主要是反应性的，在威胁发生后响应，而不是预测和防止跨模态级联攻击。(3) **自适应耦合不足**：传统优化方法缺乏复杂机制来动态平衡多个可信性目标以响应变化的医疗环境和法规要求。

To address these fundamental challenges, we introduce **TrustGuard**, a comprehensive multi-dimensional trustworthiness framework specifically designed for medical multimodal federated learning. TrustGuard represents a paradigm shift from traditional single-dimension optimization to unified multi-dimensional trustworthiness management through three core innovations: the **Adaptive Multi-dimensional Trustworthiness Coupling (AMTC)** mechanism, the **Predictive Adaptive Defense (PAD)** framework, and the **Dynamic Coupling-aware Optimization (DCAO)** algorithm.

为了解决这些根本挑战，我们引入了**TrustGuard**，这是一个专门为医疗多模态联邦学习设计的综合多维度可信性框架。TrustGuard代表了从传统单维度优化向统一多维度可信性管理的范式转变，通过三个核心创新实现：**自适应多维度可信性耦合(AMTC)机制**、**预测性自适应防御(PAD)框架**和**动态耦合感知优化(DCAO)算法**。

The AMTC mechanism addresses the first challenge by mathematically modeling the dynamic coupling relationships among five trustworthiness dimensions (privacy, security, robustness, fairness, explainability), enabling adaptive balance based on real-time medical scenarios and regulatory requirements. The PAD framework tackles cross-modal cascade threats through predictive threat modeling and proactive defense strategies, utilizing our novel Medical Cross-modal Attack Graph (Medical CMAG) to anticipate and mitigate potential attack chains before they fully manifest. The DCAO algorithm provides the optimization foundation for dynamically adjusting coupling weights and defense strategies based on evolving threat landscapes and changing medical environments.

AMTC机制通过数学建模五个可信性维度（隐私、安全、鲁棒性、公平性、可解释性）之间的动态耦合关系来解决第一个挑战，基于实时医疗场景和法规要求实现自适应平衡。PAD框架通过预测性威胁建模和主动防御策略来应对跨模态级联威胁，利用我们新颖的医疗跨模态攻击图(Medical CMAG)在潜在攻击链完全显现之前预测和缓解它们。DCAO算法为基于不断演变的威胁环境和变化的医疗环境动态调整耦合权重和防御策略提供了优化基础。

**Main Contributions.** Our primary contributions can be summarized as follows:

**主要贡献。** 我们的主要贡献可以总结如下：

• **Theoretical Innovation**: We establish the first unified mathematical framework for multi-dimensional trustworthiness optimization in medical federated learning, introducing novel coupling theory that models complex interdependencies among trustworthiness dimensions and provides theoretical guarantees for adaptive optimization.

• **Methodological Breakthrough**: We propose the AMTC mechanism that dynamically balances competing trustworthiness objectives through adaptive coupling weights, the PAD framework that provides predictive defense against cross-modal cascade threats, and the DCAO algorithm that ensures convergence and stability in dynamic medical environments.

• **Empirical Validation**: Extensive experiments on medical datasets (MIMIC-III, eICU-CRD, PhysioNet-2019) and our constructed Med-CM-Syn benchmark demonstrate that TrustGuard achieves 23.7% improvement in multi-dimensional trustworthiness scores while maintaining 94.2% model utility, significantly outperforming state-of-the-art baselines.

• **Practical Impact**: TrustGuard addresses real-world medical AI deployment challenges by providing HIPAA/GDPR-compliant solutions, regulatory-aware optimization strategies, and comprehensive threat modeling capabilities that are essential for clinical applications.

• **理论创新**：我们建立了医疗联邦学习中多维度可信性优化的首个统一数学框架，引入了建模可信性维度间复杂相互依赖关系的新颖耦合理论，并为自适应优化提供理论保证。

• **方法突破**：我们提出了通过自适应耦合权重动态平衡竞争可信性目标的AMTC机制、提供跨模态级联威胁预测性防御的PAD框架，以及确保动态医疗环境中收敛性和稳定性的DCAO算法。

• **实证验证**：在医疗数据集（MIMIC-III、eICU-CRD、PhysioNet-2019）和我们构建的Med-CM-Syn基准上的广泛实验表明，TrustGuard在保持94.2%模型效用的同时，多维度可信性分数提高了23.7%，显著优于最先进的基线方法。

• **实际影响**：TrustGuard通过提供符合HIPAA/GDPR的解决方案、法规感知优化策略和对临床应用至关重要的综合威胁建模能力，解决了现实世界医疗AI部署挑战。