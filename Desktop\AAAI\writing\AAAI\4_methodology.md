# Methodology

In this section, we introduce the TrustGuard framework, which establishes a multi-dimensional trustworthiness optimization system for medical multimodal federated learning through adaptive coupling theory and cross-modal threat defense. The TrustGuard architecture is illustrated in Figure 1. We design an Adaptive Multi-dimensional Trustworthiness Coupling (AMTC) mechanism that dynamically balances privacy, security, robustness, fairness, and explainability dimensions based on medical scenarios and regulatory requirements. To address cross-modal cascade threats, we deploy a Predictive Adaptive Defense (PAD) framework on client devices, using threat prediction techniques to proactively identify and mitigate potential attack chains. A dynamic coupling-aware optimization strategy accelerates trustworthiness evaluation across modalities, enabling clients to adaptively adjust defense resources and maintain optimal trustworthiness configurations.

在本节中，我们介绍了TrustGuard框架，该框架通过自适应耦合理论和跨模态威胁防御为医疗多模态联邦学习建立了多维度可信性优化系统。TrustGuard架构如图1所示。我们设计了自适应多维度可信性耦合（AMTC）机制，根据医疗场景和法规要求动态平衡隐私性、安全性、鲁棒性、公平性和可解释性维度。为了应对跨模态级联威胁，我们在客户端设备上部署了预测性自适应防御（PAD）框架，使用威胁预测技术主动识别和缓解潜在的攻击链。动态耦合感知优化策略加速了跨模态的可信性评估，使客户端能够自适应调整防御资源并维持最优的可信性配置。

## Adaptive Multi-dimensional Trustworthiness Coupling Optimization

As discussed before, to comprehensively address the task of multi-dimensional trustworthiness optimization and ensure the adaptability of the generated coupling strategies for each medical federated scenario, we design a performance-driven AMTC optimization strategy. First, we provide an introduction to the TrustGuard coupling mechanisms.

如前所述，为了全面解决多维度可信性优化任务，并确保为每个医疗联邦场景生成的耦合策略的适应性，我们设计了性能驱动的AMTC优化策略。首先，我们介绍TrustGuard耦合机制。

Coupling Task. Our objective is to construct a personalized trustworthiness architecture for highly heterogeneous medical data. Specifically, we leverage adaptive coupling theory to rapidly search for the optimal initial trustworthiness configurations from the coupling space for each client. To tailor the AMTC to different personalized medical federated scenarios, the coupling task prompt, denoted as $\kappa$, primarily focuses on three parameters: the number of clients $(N)$, the medical domain knowledge $(\pi)$ and specific dataset $(D)$. To bridge the gap between trustworthiness dimensions and medical domains, we have selected several domain-specific knowledge $(\pi)$ of medical datasets (such as patient privacy sensitivity, regulatory compliance requirements, etc.) to describe the medical context. For example, the privacy sensitivity level can assist in designing the coupling weights of privacy-security dimensions. Although we set the number of dimensions to 5, AMTC can choose coupling strategies such as Identity or ZeroCouple layers to reduce actual coupling complexity and avoid over-coupling of trustworthiness models. When the regulatory requirements are too strict, it can guide the AMTC in selecting trustworthiness models with adaptive techniques, such as dynamic privacy budgeting. Mathematically, this is expressed as $\kappa (N,D,\pi)$.

耦合任务。我们的目标是为高度异构的医疗数据构建个性化的可信性架构。具体而言，我们利用自适应耦合理论为每个客户端从耦合空间中快速搜索最优的初始可信性配置。为了使AMTC适应不同的个性化医疗联邦场景，耦合任务提示（记为$\kappa$）主要关注三个参数：客户端数量$(N)$、医疗领域知识$(\pi)$和特定数据集$(D)$。为了弥合可信性维度与医疗领域之间的差距，我们选择了医疗数据集的几个领域特定知识$(\pi)$（如患者隐私敏感性、法规合规要求等）来描述医疗上下文。例如，隐私敏感性级别可以帮助设计隐私-安全维度的耦合权重。虽然我们将维度数量设置为5，但AMTC可以选择Identity或ZeroCouple层等耦合策略来降低实际耦合复杂性并避免可信性模型的过度耦合。当法规要求过于严格时，它可以指导AMTC选择具有自适应技术的可信性模型，如动态隐私预算。数学上，这表示为$\kappa (N,D,\pi)$。

Coupling Space. The coupling space $\Omega$ in AMTC encompasses a variety of candidate coupling operations and interconnections between trustworthiness dimensions. Specifically, the architecture of personalized medical multimodal trustworthiness for each client is structured into three distinct coupling blocks.

耦合空间。AMTC中的耦合空间$\Omega$包含各种候选耦合操作以及可信性维度之间的互连。具体而言，每个客户端的个性化医疗多模态可信性架构被构造为三个不同的耦合块。

- Privacy-Security Coupling Block. It projects privacy constraints into security-aware embedding space and typically consists of differential privacy layers, such as adaptive noise injection mechanisms.- Robustness-Fairness Coupling Block. Aimed at extracting high-level trustworthiness embeddings, it integrates multiple coupling operations. The repertoire of candidate operations includes nine widely utilized methods: Dynamic Weighting, Adaptive Balancing, Cross-modal Alignment, Fairness Regularization, Robustness Enhancement, Gradient Clipping, alongside specialized layers such as the Identity-coupling layer, ZeroCouple layer (which outputs zero coupling during forward propagation), and the Bypass layer (which maintains original dimension values).- Explainability Integration Block. It transforms the coupled trustworthiness embeddings derived from the middle block into the final trustworthiness predictions.

- 隐私-安全耦合块。它将隐私约束投影到安全感知的嵌入空间中，通常由差分隐私层组成，如自适应噪声注入机制。- 鲁棒性-公平性耦合块。旨在提取高级可信性嵌入，它集成了多种耦合操作。候选操作库包括九种广泛使用的方法：动态加权、自适应平衡、跨模态对齐、公平性正则化、鲁棒性增强、梯度裁剪，以及专门的层，如身份耦合层、零耦合层（在前向传播期间输出零耦合）和旁路层（保持原始维度值）。- 可解释性集成块。它将来自中间块的耦合可信性嵌入转换为最终的可信性预测。

Furthermore, the framework incorporates non-linear coupling functions to enable learning from complex trustworthiness patterns and relationships. Each block offers five candidate coupling functions: Sigmoid, Tanh, ReLU, Linear, and ELU, enhancing the model's flexibility and capability to capture nonlinear trustworthiness dynamics.

此外，该框架结合了非线性耦合函数，以便从复杂的可信性模式和关系中学习。每个块提供五种候选耦合函数：Sigmoid、Tanh、ReLU、Linear和ELU，增强了模型的灵活性和捕获非线性可信性动态的能力。

Coupling strategy. The coupling strategy $\psi (T)$ for optimizing trustworthiness architectures via AMTC consists of two distinct phases. During the Exploration Stage $(T< 2)$, the framework broadly explores the entire coupling space, randomly sampling coupling operations and configurations to avoid premature convergence on locally optimal trustworthiness solutions. As the process transitions to the Exploitation Stage $(T\geq 2)$, the strategy shifts to a more targeted exploration. Here, the AMTC refines coupling parameters, focusing on promising combinations based on empirical trustworthiness data from earlier trials. This phase employs a self-reflective optimization approach, where the framework iteratively queries new coupling operation lists informed by historical trustworthiness performance, moving from random selection to a more structured

耦合策略。通过AMTC优化可信性架构的耦合策略$\psi (T)$包含两个不同的阶段。在探索阶段$(T< 2)$期间，框架广泛探索整个耦合空间，随机采样耦合操作和配置，以避免过早收敛到局部最优的可信性解决方案。当过程转换到利用阶段$(T\geq 2)$时，策略转向更有针对性的探索。在这里，AMTC细化耦合参数，基于早期试验的经验可信性数据专注于有前景的组合。这个阶段采用自反思优化方法，框架根据历史可信性性能迭代查询新的耦合操作列表，从随机选择转向更结构化的

![](images/87ba4d36201f0ecd7534263b691ede9e66177fc3054b2519918b12ef084193d3.jpg)
Figure 1: The overall framework of PFGNAS,  $\leq$  and  $\leq$  are task-specific configurations and calculated input statistics. The framework optimal the parameters of GNAS and supernet through four communication processes. (1) These generated architectures are sent to clients. (2) Each client acquires a personalized model by pruning the supernet and begins to train the model in a federated way. Therefore, they send the  $\mathrm{Info}_i$  (e.g., gradient and weight) to the server. (3) The server aggregates client information and sends the updated  $\mathrm{Info_{Agg}}$  to each client. (4) After several epochs, the architecture reaches the convergence state and sends the performance of the final test to the server.

and effective exploration.

Historical trustworthiness performance. AMTC should consider the historical trustworthiness performance $\mathcal{P}$ of all clients to swiftly search for the optimal coupling configuration. We aim to recommend a global trustworthiness architecture that performs well across multiple medical modalities, so our optimization goal is to maximize the local and global federated trustworthiness validation performance:

历史可信性性能。AMTC应该考虑所有客户端的历史可信性性能$\mathcal{P}$，以快速搜索最优的耦合配置。我们旨在推荐一个在多个医疗模态上表现良好的全局可信性架构，因此我们的优化目标是最大化本地和全局联邦可信性验证性能：

$$
\begin{array}{l}\mathcal{P}(T (w^{*})) = \sum_{f_{m}}f_{m}(T (w^{*});D),\\ \displaystyle \mathcal{P}(T_{i}(w_{i}^{*})) = \sum_{f_{m}}f_{m}(T_{i}(w_{i}^{*});D_{i}), \end{array} \tag{4}
$$

where $\mathcal{P}(T (w^{*}))$ and $\mathcal{P}(T_{i}(w_{i}^{*}))$ denote the local and global federated trustworthiness validation performance respectively, $D$ and $D_{i}$ denote the valid dataset of the server and client $i$ respectively. Here, we provide metrics $f_{m}$ for validation, namely the Trustworthiness Coupling Degree (TCD) and Multi-dimensional Trustworthiness Score (MTS).

其中$\mathcal{P}(T (w^{*}))$和$\mathcal{P}(T_{i}(w_{i}^{*}))$分别表示本地和全局联邦可信性验证性能，$D$和$D_{i}$分别表示服务器和客户端$i$的有效数据集。在这里，我们提供验证指标$f_{m}$，即可信性耦合度(TCD)和多维度可信性分数(MTS)。

In summary, TrustGuard utilizes those components to enable the self-adaptive coupling ability of AMTC, which accelerates the efficiency of trustworthiness architectures' search for the initial local model. Mathematically, we define the best trustworthiness coupling configuration of clients at $T$-th iteration as:

总之，TrustGuard利用这些组件来实现AMTC的自适应耦合能力，这加速了可信性架构搜索初始本地模型的效率。数学上，我们将客户端在第$T$次迭代时的最佳可信性耦合配置定义为：

$$
\begin{array}{l}T_T^* = AMTC(\mathcal{K}(N,D,\pi);\Omega ;\psi (T);\mathcal{P}),\\ \displaystyle P_T = \sum_{i = 0}^N P_{T - 1}(T_i(w_i^*)) + P_{T - 1}(T (w^*)) \end{array} \tag{5}
$$

where $P_{T - 1}(T_i(w_i^*))$ denotes historical trustworthiness performance of local model, and $P_{T - 1}(T (w^{*}))$ denote historical trustworthiness performance of global model. The optimal global trustworthiness architecture, denoted as $T_T^*$, is determined by the adaptive coupling mechanism via specific coupling strategies and is dynamically updated based on trustworthiness performance metrics $\mathcal{P}$.

其中$P_{T - 1}(T_i(w_i^*))$表示本地模型的历史可信性性能，$P_{T - 1}(T (w^{*}))$表示全局模型的历史可信性性能。最优全局可信性架构，记为$T_T^*$，由自适应耦合机制通过特定的耦合策略确定，并基于可信性性能指标$\mathcal{P}$动态更新。

# Predictive Adaptive Defense Strategy

In this section, we employ a PAD framework optimization strategy to address cross-modal cascade threats caused by medical data heterogeneity. This strategy enables all clients to deploy the defense framework in FL scenarios collaboratively.

在本节中，我们采用PAD框架优化策略来解决由医疗数据异构性引起的跨模态级联威胁。该策略使所有客户端能够在FL场景中协作部署防御框架。

Here, we optimize the defense architecture $\Phi$ and the parameters $w$ of threat-prediction network in an interleaved manner. First, the server undertakes a systematic exploration of a set of personalized federated defense architectures and recommends different combinations of different threat detection operations. For each defense architecture $\Phi = \{\Phi_i\}_{i = 0}^N$ the server splits it into $N$ models and distributes them to the corresponding clients. Subsequently, to facilitate the acquisition of a personalized initial defense architecture $\Phi_{i}$ while simultaneously preserving the extensibility of the threat prediction framework $D$ we implement a threat-aware pruning method on defense framework $D$ The pruning strategy is achieved by utilizing the parameter $\beta$ which represents the weights of the pruned defense framework $D$ In this configuration, the weights of selected threat detectors are assigned a value of 1, while those of unselected detectors are set to infinitesimally small values. Consequently, the initial parameters of a medical trustworthiness model $\Phi_{i}$ for any client can be expressed mathematically as:

在这里，我们以交错的方式优化防御架构$\Phi$和威胁预测网络的参数$w$。首先，服务器系统性地探索一组个性化联邦防御架构，并推荐不同威胁检测操作的不同组合。对于每个防御架构$\Phi = \{\Phi_i\}_{i = 0}^N$，服务器将其分割为$N$个模型并分发给相应的客户端。随后，为了促进获得个性化的初始防御架构$\Phi_{i}$，同时保持威胁预测框架$D$的可扩展性，我们在防御框架$D$上实施威胁感知剪枝方法。剪枝策略通过利用参数$\beta$来实现，该参数表示剪枝后防御框架$D$的权重。在此配置中，选定威胁检测器的权重被赋值为1，而未选定检测器的权重被设置为无穷小值。因此，任何客户端的医疗可信性模型$\Phi_{i}$的初始参数可以数学表达为：

$$
\Phi_{i}(\theta_{i}) = D(w)\odot \beta , \tag{6}
$$

where $\odot$ denotes the element-wise multiplication of the defense framework weights with the threat-aware pruning mask $\beta$. In particular, to reduce the complexity of the threat detection model, in our defense framework, we save the parameters of all threat detection operations in architectures and collect them in $\theta_{i}$, which is also known as the local defense weight. This strategy ensures that each client starts with a defense model that is not only tailored to its specific medical threat landscape but is also scalable within the broader architectural framework of $D$.

其中$\odot$表示防御框架权重与威胁感知剪枝掩码$\beta$的逐元素乘法。特别地，为了降低威胁检测模型的复杂性，在我们的防御框架中，我们保存架构中所有威胁检测操作的参数并将它们收集在$\theta_{i}$中，这也被称为本地防御权重。该策略确保每个客户端都以一个不仅针对其特定医疗威胁环境量身定制，而且在$D$的更广泛架构框架内可扩展的防御模型开始。

Next, we aim to update the defense model's parameters by minimizing the federated threat detection loss of the initial model $\Phi_{i}$ at round $T$ Let $\Phi_{i}(\theta_{i})$ denotes the defense model $\Phi_{i}$ of client $i$ parametrized by $\theta_{i}$. The optimal $\theta_{i}^{*}$ can be calculated as:

接下来，我们旨在通过最小化第$T$轮初始模型$\Phi_{i}$的联邦威胁检测损失来更新防御模型的参数。设$\Phi_{i}(\theta_{i})$表示由$\theta_{i}$参数化的客户端$i$的防御模型$\Phi_{i}$。最优$\theta_{i}^{*}$可以计算为：

$$
\theta_{i}^{*}\leftarrow \min_{\theta_{i}}\mathcal{L}(\Phi_{i}(\theta_{i};M_{i});\mathbf{T}_{i}), \tag{7}
$$

where $L$ denotes the threat detection loss function between predicted threat labels $\Phi_{i}(\theta_{i};M_{i})$ and true threat labels $\mathbf{T}_i$

其中$L$表示预测威胁标签$\Phi_{i}(\theta_{i};M_{i})$和真实威胁标签$\mathbf{T}_i$之间的威胁检测损失函数

Then, the object is to learn the global parameter $w$ according to the $\theta_{i}$ from all clients. Specifically, let $D(w)$ denote the defense framework $D$ parametrized by $w$. Suppose we apply FedAvg to jointly guide clients in training the defense framework under FL scenarios. According to the Eq. (2), the optimal global weight $w^{*}$ can be denoted as:

然后，目标是根据来自所有客户端的$\theta_{i}$学习全局参数$w$。具体地，设$D(w)$表示由$w$参数化的防御框架$D$。假设我们应用FedAvg在FL场景下联合指导客户端训练防御框架。根据公式(2)，最优全局权重$w^{*}$可以表示为：

$$
w^{*}\coloneqq \sum_{i = 1}^{N}\frac{|M_{i}|}{|M|}\theta_{i}^{*}. \tag{8}
$$

Applying chain rule to the approximate local threat detection loss function $\mathcal{L}(\Phi_i(\theta_i;M_i);\mathbf{T}_i)$ the gradient of $\theta_{i}$ yields:

将链式法则应用于近似本地威胁检测损失函数$\mathcal{L}(\Phi_i(\theta_i;M_i);\mathbf{T}_i)$，$\theta_{i}$的梯度为：

$$
\begin{array}{rl} & {\frac{\partial\mathcal{L}}{\partial\theta_i} = \frac{\partial\mathcal{L}}{\partial w}\cdot \frac{\partial w}{\partial\theta_i},}\\ & {\quad = \frac{\partial\mathcal{L}(\Phi_i(\theta_i;M_i);\mathbf{T}_i)}{\partial\theta_i}\cdot \frac{|M_i|}{|M|},}\\ & {\quad = \frac{\partial\mathcal{L}(\Phi_i(D(w)\odot\beta;M_i);\mathbf{T}_i)}{\partial\theta_i}\cdot \frac{|M_i|}{|M|},} \end{array} \tag{9}
$$

where $|M_{i}|$ denotes the number of medical modalities of dataset $M_{i}$

其中$|M_{i}|$表示数据集$M_{i}$的医疗模态数量

Then, we perform several local optimization steps on the personal medical data. According to the gradient descent algorithm, the gradient of the local defense model is

然后，我们在个人医疗数据上执行几个本地优化步骤。根据梯度下降算法，本地防御模型的梯度为

$$
\theta_{i}\leftarrow \theta_{i} - \eta \left(\nabla_{\theta_{i}}\mathcal{L}((D(w)\odot \beta ;M_{i});\mathbf{T}_{i})\cdot \frac{|M_{i}|}{|M|}\right), \tag{10}
$$

where $\eta$ denotes the learning rate. Therefore, the gradient of global personalized defense framework $\Phi_{i}(w)$ can be updated as:

其中$\eta$表示学习率。因此，全局个性化防御框架$\Phi_{i}(w)$的梯度可以更新为：

$$
w^{*}\leftarrow w - \eta \left(\nabla_{w}\mathcal{L}(D(w;M);t)\right). \tag{11}
$$

After several epochs, the server sends the current optimal global parameters $w^{*}$ to all clients for iterative updates. Until the defense model approaches convergence, we compute the final performance (i.e., Threat Detection Accuracy and Cascade Success Rate) of the current PAD architecture.

经过几个epoch后，服务器将当前最优全局参数$w^{*}$发送给所有客户端进行迭代更新。直到防御模型接近收敛，我们计算当前PAD架构的最终性能（即威胁检测准确率和级联成功率）。

$$
\mathcal{P}(\Phi_i(w^*)) = \{TDA_{\Phi_i(w^*)},CSR_{\Phi_i(w^*)}\} . \tag{12}
$$

In particular, TrustGuard is compatible with currently prevalent federated learning methods, such as FedAvg (McMahan et al. 2017), FedProx (Li et al. 2020). In the defense architecture optimization stage, to generate new defense architectures with improved threat detection performance, the server provides the performance feedback $\mathcal{P}$ to the AMTC by incorporating the performance of both historical and current federated defense architectures as part of the coupling strategy.

特别地，TrustGuard与当前流行的联邦学习方法兼容，如FedAvg (McMahan et al. 2017)、FedProx (Li et al. 2020)。在防御架构优化阶段，为了生成具有改进威胁检测性能的新防御架构，服务器通过将历史和当前联邦防御架构的性能作为耦合策略的一部分，向AMTC提供性能反馈$\mathcal{P}$。